
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882" xmlns="http://www.w3.org/TR/REC-html40"><head><meta http-equiv=Content-Type  content="text/html; charset=utf-8" ><meta name=ProgId  content=Word.Document ><meta name=Generator  content="Microsoft Word 14" ><meta name=Originator  content="Microsoft Word 14" ><link rel=File-List  href="MG免责声明-20250314（踏尘改3.15）.files/filelist.xml" ><title></title><!--[if gte mso 9]><xml><o:DocumentProperties><o:Author>Jackson</o:Author><o:LastAuthor>Jackson</o:LastAuthor><o:Revision>1</o:Revision><o:Pages>2</o:Pages><o:Characters>1265</o:Characters><o:Lines>10</o:Lines><o:Paragraphs>2</o:Paragraphs></o:DocumentProperties><o:CustomDocumentProperties><o:KSOProductBuildVer dt:dt="string" >1033-6.14.0.8924</o:KSOProductBuildVer><o:ICV dt:dt="string" >1F003B356E801CBBF071D667ACF4D1C8_43</o:ICV></o:CustomDocumentProperties></xml><![endif]--><!--[if gte mso 9]><xml><o:OfficeDocumentSettings></o:OfficeDocumentSettings></xml><![endif]--><!--[if gte mso 9]><xml><w:WordDocument><w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel><w:DisplayHorizontalDrawingGridEvery>0</w:DisplayHorizontalDrawingGridEvery><w:DisplayVerticalDrawingGridEvery>2</w:DisplayVerticalDrawingGridEvery><w:DocumentKind>DocumentNotSpecified</w:DocumentKind><w:DrawingGridVerticalSpacing>7.8 磅</w:DrawingGridVerticalSpacing><w:View>Web</w:View><w:Compatibility><w:AdjustLineHeightInTable/><w:DontGrowAutofit/><w:DoNotExpandShiftReturn/></w:Compatibility><w:Zoom>0</w:Zoom></w:WordDocument></xml><![endif]--><!--[if gte mso 9]><xml><w:LatentStyles DefLockedState="false"  DefUnhideWhenUsed="true"  DefSemiHidden="true"  DefQFormat="false"  DefPriority="99"  LatentStyleCount="260" >
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Normal" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="heading 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="heading 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="heading 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="heading 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="heading 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="heading 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="heading 7" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="heading 8" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="heading 9" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 7" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 8" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 9" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 7" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 8" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 9" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Normal Indent" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="footnote text" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="annotation text" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="header" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="footer" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index heading" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="caption" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="table of figures" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="envelope address" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="envelope return" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="footnote reference" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="annotation reference" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="line number" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="page number" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="endnote reference" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="endnote text" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="table of authorities" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="macro" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toa heading" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Bullet" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Number" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Bullet 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Bullet 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Bullet 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Bullet 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Number 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Number 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Number 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Number 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Title" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Closing" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Signature" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="1"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Default Paragraph Font" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text Indent" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Continue" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Continue 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Continue 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Continue 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Continue 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Message Header" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Subtitle" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Salutation" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Date" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text First Indent" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text First Indent 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Note Heading" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text Indent 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text Indent 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Block Text" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Hyperlink" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="FollowedHyperlink" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Strong" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Emphasis" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Document Map" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Plain Text" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="E-mail Signature" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Normal (Web)" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Acronym" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Address" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Cite" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Code" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Definition" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Keyboard" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Preformatted" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Sample" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Typewriter" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Variable" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Normal Table" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="annotation subject" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="No List" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="1 / a / i" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="1 / 1.1 / 1.1.1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Article / Section" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Simple 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Simple 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Simple 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Classic 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Classic 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Classic 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Classic 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Colorful 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Colorful 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Colorful 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Columns 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Columns 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Columns 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Columns 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Columns 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 7" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 8" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 7" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 8" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table 3D effects 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table 3D effects 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table 3D effects 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Contemporary" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Elegant" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Professional" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Subtle 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Subtle 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Web 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Web 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Web 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Balloon Text" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Theme" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Placeholder Text" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="No Spacing" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light Shading Accent 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light List Accent 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light Grid Accent 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Shading 1 Accent 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Shading 2 Accent 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium List 1 Accent 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Paragraph" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Quote" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Intense Quote" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium List 2 Accent 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 1 Accent 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 2 Accent 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 3 Accent 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Dark List Accent 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful Shading Accent 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful List Accent 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful Grid Accent 1" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light Shading Accent 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light List Accent 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light Grid Accent 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Shading 1 Accent 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Shading 2 Accent 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium List 1 Accent 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium List 2 Accent 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 1 Accent 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 2 Accent 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 3 Accent 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Dark List Accent 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful Shading Accent 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful List Accent 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful Grid Accent 2" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light Shading Accent 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light List Accent 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light Grid Accent 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Shading 1 Accent 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Shading 2 Accent 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium List 1 Accent 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium List 2 Accent 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 1 Accent 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 2 Accent 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 3 Accent 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Dark List Accent 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful Shading Accent 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful List Accent 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful Grid Accent 3" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light Shading Accent 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light List Accent 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light Grid Accent 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Shading 1 Accent 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Shading 2 Accent 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium List 1 Accent 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium List 2 Accent 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 1 Accent 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 2 Accent 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 3 Accent 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Dark List Accent 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful Shading Accent 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful List Accent 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful Grid Accent 4" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light Shading Accent 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light List Accent 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light Grid Accent 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Shading 1 Accent 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Shading 2 Accent 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium List 1 Accent 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium List 2 Accent 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 1 Accent 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 2 Accent 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 3 Accent 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Dark List Accent 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful Shading Accent 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful List Accent 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful Grid Accent 5" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light Shading Accent 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light List Accent 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Light Grid Accent 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Shading 1 Accent 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Shading 2 Accent 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium List 1 Accent 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium List 2 Accent 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 1 Accent 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 2 Accent 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Medium Grid 3 Accent 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Dark List Accent 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful Shading Accent 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful List Accent 6" ></w:LsdException>
  <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Colorful Grid Accent 6" ></w:LsdException>
</w:LatentStyles></xml><![endif]--><style>
    @font-face{
        font-family:"Times New Roman";
    }

    @font-face{
        font-family:"宋体";
    }

    @font-face{
        font-family:"Wingdings";
    }

    @font-face{
        font-family:"DengXian";
    }

    @font-face{
        font-family:"Calibri";
    }

    @list l0:level1{
        mso-level-start-at:0;
        mso-level-number-format:bullet;
        mso-level-suffix:tab;
        mso-level-text:"•";
        mso-level-tab-stop:0.0000pt;
        mso-level-number-position:left;
        margin-left:42.0000pt;text-indent:-21.0000pt;font-family:DengXian;}

    @list l0:level2{
        mso-level-number-format:bullet;
        mso-level-suffix:tab;
        mso-level-text:"";
        mso-level-tab-stop:0.0000pt;
        mso-level-number-position:left;
        margin-left:63.0000pt;text-indent:-21.0000pt;font-family:Wingdings;}

    @list l0:level3{
        mso-level-number-format:bullet;
        mso-level-suffix:tab;
        mso-level-text:"";
        mso-level-tab-stop:0.0000pt;
        mso-level-number-position:left;
        margin-left:84.0000pt;text-indent:-21.0000pt;font-family:Wingdings;}

    @list l0:level4{
        mso-level-number-format:bullet;
        mso-level-suffix:tab;
        mso-level-text:"";
        mso-level-tab-stop:0.0000pt;
        mso-level-number-position:left;
        margin-left:105.0000pt;text-indent:-21.0000pt;font-family:Wingdings;}

    @list l0:level5{
        mso-level-number-format:bullet;
        mso-level-suffix:tab;
        mso-level-text:"";
        mso-level-tab-stop:0.0000pt;
        mso-level-number-position:left;
        margin-left:126.0000pt;text-indent:-21.0000pt;font-family:Wingdings;}

    @list l0:level6{
        mso-level-number-format:bullet;
        mso-level-suffix:tab;
        mso-level-text:"";
        mso-level-tab-stop:0.0000pt;
        mso-level-number-position:left;
        margin-left:147.0000pt;text-indent:-21.0000pt;font-family:Wingdings;}

    @list l0:level7{
        mso-level-number-format:bullet;
        mso-level-suffix:tab;
        mso-level-text:"";
        mso-level-tab-stop:0.0000pt;
        mso-level-number-position:left;
        margin-left:168.0000pt;text-indent:-21.0000pt;font-family:Wingdings;}

    @list l0:level8{
        mso-level-number-format:bullet;
        mso-level-suffix:tab;
        mso-level-text:"";
        mso-level-tab-stop:0.0000pt;
        mso-level-number-position:left;
        margin-left:189.0000pt;text-indent:-21.0000pt;font-family:Wingdings;}

    @list l0:level9{
        mso-level-number-format:bullet;
        mso-level-suffix:tab;
        mso-level-text:"";
        mso-level-tab-stop:0.0000pt;
        mso-level-number-position:left;
        margin-left:210.0000pt;text-indent:-21.0000pt;font-family:Wingdings;}

    p.MsoNormal{
        mso-style-name:Normal;
        mso-style-parent:"";
        margin-top:0.0000pt;
        margin-right:0.0000pt;
        margin-bottom:0.0000pt;
        margin-left:0.0000pt;
        text-align:left;
        font-family:Calibri;
        mso-fareast-font-family:宋体;
        mso-bidi-font-family:'Times New Roman';
        font-size:12.0000pt;
    }

    h1{
        mso-style-name:"Heading 1";
        mso-style-next:Normal;
        margin-top:5.0000pt;
        margin-bottom:5.0000pt;
        mso-margin-top-alt:auto;
        mso-margin-bottom-alt:auto;
        text-align:left;
        font-family:宋体;
        font-weight:bold;
        font-size:24.0000pt;
        mso-font-kerning:22.0000pt;
    }

    h2{
        mso-style-name:"Heading 2";
        mso-style-next:Normal;
        margin-top:5.0000pt;
        margin-bottom:5.0000pt;
        mso-margin-top-alt:auto;
        mso-margin-bottom-alt:auto;
        text-align:left;
        font-family:宋体;
        font-weight:bold;
        font-size:18.0000pt;
    }

    h3{
        mso-style-name:"Heading 3";
        mso-style-next:Normal;
        margin-top:5.0000pt;
        margin-bottom:5.0000pt;
        mso-margin-top-alt:auto;
        mso-margin-bottom-alt:auto;
        text-align:left;
        font-family:宋体;
        font-weight:bold;
        font-size:13.5000pt;
    }

    h4{
        mso-style-name:"Heading 4";
        mso-style-next:Normal;
        margin-top:5.0000pt;
        margin-bottom:5.0000pt;
        mso-margin-top-alt:auto;
        mso-margin-bottom-alt:auto;
        text-align:left;
        font-family:宋体;
        font-weight:bold;
        font-size:12.0000pt;
    }

    h5{
        mso-style-name:"Heading 5";
        mso-style-next:Normal;
        margin-top:5.0000pt;
        margin-bottom:5.0000pt;
        mso-margin-top-alt:auto;
        mso-margin-bottom-alt:auto;
        text-align:left;
        font-family:宋体;
        font-weight:bold;
        font-size:10.0000pt;
    }

    h6{
        mso-style-name:"Heading 6";
        mso-style-next:Normal;
        margin-top:5.0000pt;
        margin-bottom:5.0000pt;
        mso-margin-top-alt:auto;
        mso-margin-bottom-alt:auto;
        text-align:left;
        font-family:宋体;
        font-weight:bold;
        font-size:7.5000pt;
    }

    span.10{
        font-family:'Times New Roman';
    }

    span.15{
        font-family:'Times New Roman';
    }

    span.16{
        font-family:'Times New Roman';
    }

    span.17{
        font-family:Calibri;
        mso-fareast-font-family:宋体;
        mso-bidi-font-family:'Times New Roman';
        font-weight:bold;
    }

    span.18{
        font-family:Calibri;
        mso-fareast-font-family:宋体;
        mso-bidi-font-family:'Times New Roman';
        font-size:9.0000pt;
    }

    span.19{
        font-family:Calibri;
        mso-fareast-font-family:宋体;
        mso-bidi-font-family:'Times New Roman';
        font-size:9.0000pt;
    }

    span.20{
        font-family:'Times New Roman';
        font-size:10.5000pt;
    }

    span.21{
        font-family:Calibri;
        mso-fareast-font-family:宋体;
        mso-bidi-font-family:'Times New Roman';
    }

    p.MsoCommentText{
        mso-style-name:"Comment Text";
        margin-top:0.0000pt;
        margin-right:0.0000pt;
        margin-bottom:0.0000pt;
        margin-left:0.0000pt;
        text-align:left;
        font-family:Calibri;
        mso-fareast-font-family:宋体;
        mso-bidi-font-family:'Times New Roman';
    }

    p.MsoCommentSubject{
        mso-style-name:"Comment Subject";
        mso-style-parent:"Comment Text";
        mso-style-next:"Comment Text";
        margin-top:0.0000pt;
        margin-right:0.0000pt;
        margin-bottom:0.0000pt;
        margin-left:0.0000pt;
        text-align:left;
        font-family:Calibri;
        mso-fareast-font-family:宋体;
        mso-bidi-font-family:'Times New Roman';
        font-weight:bold;
    }

    p.23{
        mso-style-name:"p Char Char";
        margin-top:5.0000pt;
        margin-right:0.0000pt;
        margin-bottom:5.0000pt;
        margin-left:0.0000pt;
        mso-margin-top-alt:auto;
        mso-margin-bottom-alt:auto;
        text-align:left;
        font-family:'Times New Roman';
        mso-fareast-font-family:宋体;
        font-size:12.0000pt;
    }

    p.pre{
        mso-style-name:"HTML Preformatted";
        margin-top:0.0000pt;
        margin-right:0.0000pt;
        margin-bottom:0.0000pt;
        margin-left:0.0000pt;
        text-align:left;
        font-family:宋体;
        font-size:12.0000pt;
    }

    p.MsoHeader{
        mso-style-name:Header;
        margin-top:0.0000pt;
        margin-right:0.0000pt;
        margin-bottom:0.0000pt;
        margin-left:0.0000pt;
        layout-grid-mode:char;
        text-align:center;
        font-family:Calibri;
        mso-fareast-font-family:宋体;
        mso-bidi-font-family:'Times New Roman';
        font-size:9.0000pt;
    }

    p.MsoFooter{
        mso-style-name:Footer;
        margin-top:0.0000pt;
        margin-right:0.0000pt;
        margin-bottom:0.0000pt;
        margin-left:0.0000pt;
        layout-grid-mode:char;
        text-align:left;
        font-family:Calibri;
        mso-fareast-font-family:宋体;
        mso-bidi-font-family:'Times New Roman';
        font-size:9.0000pt;
    }

    p.27{
        mso-style-name:"p Char";
        margin-top:5.0000pt;
        margin-right:0.0000pt;
        margin-bottom:5.0000pt;
        margin-left:0.0000pt;
        mso-margin-top-alt:auto;
        mso-margin-bottom-alt:auto;
        text-align:left;
        font-family:'Times New Roman';
        mso-fareast-font-family:宋体;
        font-size:12.0000pt;
    }

    p.p{
        mso-style-name:"Normal \(Web\)";
        margin-top:5.0000pt;
        margin-bottom:5.0000pt;
        mso-margin-top-alt:auto;
        mso-margin-bottom-alt:auto;
        text-align:left;
        font-family:'Times New Roman';
        mso-fareast-font-family:宋体;
        font-size:12.0000pt;
    }

    p.30{
        mso-style-name:"pre Char";
        margin:0pt;
        margin-bottom:.0001pt;
        text-align:left;
        font-family:宋体;
        font-size:12.0000pt;
    }

    span.msoIns{
        mso-style-type:export-only;
        mso-style-name:"";
        text-decoration:underline;
        text-underline:single;
        color:blue;
    }

    span.msoDel{
        mso-style-type:export-only;
        mso-style-name:"";
        text-decoration:line-through;
        color:red;
    }

    table.MsoNormalTable{
        mso-style-name:"Table Normal";
        mso-style-parent:"";
        mso-style-noshow:yes;
        mso-tstyle-rowband-size:0;
        mso-tstyle-colband-size:0;
        mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
        mso-para-margin:0pt;
        mso-para-margin-bottom:.0001pt;
        mso-pagination:widow-orphan;
        font-family:'Times New Roman';
        font-size:10.0000pt;
        mso-ansi-language:#0400;
        mso-fareast-language:#0400;
        mso-bidi-language:#0400;
    }
    @page{mso-page-border-surround-header:no;
        mso-page-border-surround-footer:no;}@page Section0{
        margin-top:72.0000pt;
        margin-bottom:72.0000pt;
        margin-left:90.0000pt;
        margin-right:90.0000pt;
        size:595.3000pt 841.9000pt;
        layout-grid:18.0000pt;
        mso-header-margin:36.0000pt;
        mso-footer-margin:36.0000pt;
    }
    div.Section0{page:Section0;}</style></head><body style="tab-interval:36pt;" ><!--StartFragment--><div class="Section0"  style="layout-grid:18.0000pt;" ><p class=p  align=center  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:center;line-height:150%;" ><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:14.0000pt;
mso-font-kerning:0.0000pt;" ><font face="DengXian" >免责声明</font></span></b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:14.0000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" >&nbsp;</span></b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" >&nbsp;</span></b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face="DengXian" >尊敬的</font>Muse Gate会员/用户（以下或称“您”）：</span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" >&nbsp;</span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face="DengXian" >欢迎您使用由杭州霖润智能科技有限公司（以下简称</font><font face="DengXian" >“霖润智能”）开发的MuseGate AI模特软件（以下简称“Muse Gate”）！为了保障您的合法权益，同时明确使用本软件的相关责任范围，特此声明如下内容。</font></span><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" ><font face="DengXian" >请您在使用本软件之前，仔细阅读并理解以下内容：</font></span></b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" >&nbsp;</span></b><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" ><o:p></o:p></span></b></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" >1.上传内容的合法性</span></b><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" ><o:p></o:p></span></b></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" >1.1用户（含子账号，下同）通过Muse Gate上传的任何图片、人脸信息、素材、数据、视频等内容（以下简称“上传内容”）均通过合法途径获得</span><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
font-weight:bold;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face="DengXian" >，</font></span></b><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" ><font face="DengXian" >不侵犯任何第三方的著作权、商标权或其他合法权益</font></span></b><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
font-weight:bold;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face="DengXian" >，</font></span></b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face="DengXian" >且已取得被采集人（如有）的书面明确授权，授权范围包括但不限于采集、存储、处理及</font>Muse Gate约定用途。</span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" >1.2用户不得上传侵犯他人肖像权、隐私权、著作权等合法权益的内容，不得包含暴力、色情、歧视等违法信息。用户上传未成年人生物信息需有监护人书面同意。</span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(255,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" >&nbsp;</span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" >2. Muse Gate责任限制</span></b><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" ><o:p></o:p></span></b></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" >2.1Muse Gate</span><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" ><font face="DengXian" >不对用户上传内容的合法性、真实性、完整性承担责任</font></span></b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face="DengXian" >。若因用户上传内容引发的任何侵权纠纷、行政处罚等，均由用户自行承担全部责任。</font></span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" >2.2Muse Gate有权对违规内容进行</span><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" ><font face="DengXian" >删除、屏蔽或终止服务</font></span></b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face="DengXian" >，但此义务不构成对内容的事先审查责任。用户理解并接受技术手段的局限性，平台不承诺实时监控所有内容。</font></span><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" >&nbsp;</span></b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" >2.3Muse Gate基于先进的人工智能技术为您生成图片。然而，生成的图片可能在某些情境下存在意外误差、偏差或不符合特定标准的情况。用户应对生成图片的适用性进行独立判断，并承担因使用这些图片而可能产生的任何后果。</span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" >&nbsp;</span></b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" >3. 数据安全须知</span></b><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" ><o:p></o:p></span></b></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" >3.1我们将按照《个人信息保护法》《数据安全法》等法律法规及《Muse Gate隐私政策》采取加密、去标识化等安全措施保护用户的隐私，但用户理解互联网技术存在固有风险（如黑客攻击），不排除发生数据泄露的可能性。用户应审慎考虑个人隐私信息的上传，避免包含敏感或可识别个人信息的图片上传至Muse Gate。</span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" >&nbsp;</span></b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" >4.知识产权及授权</span></b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" >4.1 用户上传即视为授予本公司全球性、非独占、免费许可，允许基于服务目的的必要使用（包括但不限于算法训练、系统优化）。</span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" >4.2 本公司保留对Muse Gate界面、代码、数据库等的全部知识产权，未经书面授权禁止反向工程、数据抓取等行为。</span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" >&nbsp;</span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" >5. 免责范围</span></b><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" ><o:p></o:p></span></b></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" >5.1霖润智能及其关联公司、员工、合作伙伴，对于因使用本软件或依赖由本软件生成的图片所导致的任何直接、间接、偶然、特殊或惩罚性的损害，包括但不限于利润损失、数据丢失、信誉损害等，不承担任何法律责任，除非这些损害是由于我们的故意或重大过失造成的。</span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
font-size:10.5000pt;mso-font-kerning:0.0000pt;" >5.2用户因以下情形导致的损失，霖润智能不承担责任：</span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(255,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:42.0000pt;text-indent:-21.0000pt;
mso-pagination:widow-orphan;text-align:justify;text-justify:inter-ideograph;
line-height:150%;mso-list:l0 level1 lfo1;" ><![if !supportLists]><span style="font-family:DengXian;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><span style='mso-list:Ignore;' >&#8226;<span>&nbsp;</span></span></span><![endif]><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face="DengXian" >第三方滥用上传内容引发的纠纷</font></span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:42.0000pt;text-indent:-21.0000pt;
mso-pagination:widow-orphan;text-align:justify;text-justify:inter-ideograph;
line-height:150%;mso-list:l0 level1 lfo1;" ><![if !supportLists]><span style="font-family:DengXian;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><span style='mso-list:Ignore;' >&#8226;<span>&nbsp;</span></span></span><![endif]><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face="DengXian" >不可抗力、政策变更导致的服务的调整或终止</font></span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:42.0000pt;text-indent:-21.0000pt;
mso-pagination:widow-orphan;text-align:justify;text-justify:inter-ideograph;
line-height:150%;mso-list:l0 level1 lfo1;" ><![if !supportLists]><span style="font-family:DengXian;font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><span style='mso-list:Ignore;' >&#8226;<span>&nbsp;</span></span></span><![endif]><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face="DengXian" >用户误操作或未遵循安全提示造成的后果</font></span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" >&nbsp;</span></b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" >6. 修改与更新 </span></b><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" ><o:p></o:p></span></b></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face="DengXian" >我们保留在任何时候修改本免责声明的权利，任何修改后的条款将在公布于</font>Muse Gate时立即生效。您继续使用Muse Gate即视为接受修改后的条款。</span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" >&nbsp;</span></b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" >7. 法律适用与争议解决</span></b><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" ><o:p></o:p></span></b></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face="DengXian" >本免责声明及您与</font>Muse Gate之间的关系受中华人民共和国法律管辖（不含冲突法原则）。任何因本软件或本声明引起的争议，应首先通过友好协商解决；协商不成时，任一方可提交至霖润智能所在地有管辖权的人民法院诉讼解决。</span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" >&nbsp;</span></b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" >8. 联系我们</span></b><b><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-weight:bold;font-size:10.5000pt;
mso-font-kerning:0.0000pt;" ><o:p></o:p></span></b></p><p class=p  align=justify  style="margin-right:0.0000pt;margin-left:0.0000pt;mso-pagination:widow-orphan;
text-align:justify;text-justify:inter-ideograph;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><font face="DengXian" >您通过访问或使用</font>Muse Gate及其服务，视为您已充分阅读、理解并同意以上所有条款。如您有任何疑问或建议，欢迎通过官方客服渠道联系我们。</span><span style="mso-spacerun:'yes';font-family:DengXian;line-height:150%;
color:rgb(0,0,0);font-size:10.5000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="mso-pagination:widow-orphan;line-height:150%;" ><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:宋体;
mso-bidi-font-family:'Times New Roman';font-size:12.0000pt;mso-font-kerning:0.0000pt;" >&nbsp;</span><span style="mso-spacerun:'yes';font-family:Calibri;mso-fareast-font-family:宋体;
mso-bidi-font-family:'Times New Roman';font-size:12.0000pt;mso-font-kerning:0.0000pt;" ><o:p></o:p></span></p></div><!--EndFragment--></body></html>