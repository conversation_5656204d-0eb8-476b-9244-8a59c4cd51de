#!/bin/bash

# 定义日志函数
log() {
    local timestamp=$(date +%Y-%m-%d_%H:%M:%S)
    echo "[$timestamp] $1"
}

# 定义错误处理函数
error_exit() {
    log "$1"
    exit 1
}

log "[MacOs打包脚本]aigc-platform-ui项目 需要和 aigc-platform项目 在同一目录下!!!"
log "【Begin】开始执行构建和部署脚本..."

log "【Step1】执行 npm run build:prod..."
npm run build:prod
if [ $? -ne 0 ]; then
    error_exit "【Error】npm run build:prod 失败！"
fi
log "npm run build:prod 完成！"

log "【Step2】删除旧的静态资源文件..."
rm -rf ../aigc-platform/aigc-platform-web/src/main/resources/static/*
if [ $? -ne 0 ]; then
    error_exit "【Error】删除旧的静态资源文件失败！"
fi
log "删除成功！"


log "【Step3】复制新的构建文件到静态资源目录..."
cp -r ./dist/* ../aigc-platform/aigc-platform-web/src/main/resources/static/
if [ $? -ne 0 ]; then
    error_exit "【Error】复制新的构建文件失败！"
fi
log "复制完成！"

# 5. 脚本完成
log "【Success】脚本执行完成！"