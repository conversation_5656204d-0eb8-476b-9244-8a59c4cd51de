@echo off
chcp 65001 >nul
setlocal EnableDelayedExpansion

goto main

:errorPause
echo.
echo [错误] 发生错误！%*
echo.
echo 按任意键退出...
pause >nul
exit /b 1

:main

:: 2. 执行生产构建
echo.
echo [Windows打包脚本]aigc-platform-ui项目 需要和 aigc-platform项目 在同一目录下!!!
echo [步骤1/3] 正在执行 npm run build:prod...
call npm run build:prod
if %ERRORLEVEL% neq 0 (
    call :errorPause "npm run build:prod 失败！"
)
echo [成功] npm run build:prod 完成！

:: 3. 删除旧静态资源文件
echo.
echo [步骤2/3] 正在删除旧的静态资源文件...
set "targetStaticPath=%~dp0..\aigc-platform\aigc-platform-web\src\main\resources\static"
if exist "%targetStaticPath%\*" (
    del /s /q "%targetStaticPath%\*"
    if %ERRORLEVEL% neq 0 (
        call :errorPause "删除旧的静态资源文件失败！"
    )
) else (
    echo [注意] 旧的静态资源目录为空或不存在，无需删除。
)
echo [成功] 删除完成！

:: 4. 复制新的构建文件
echo.
echo [步骤3/3] 正在复制新的构建文件到静态资源目录...
set "distPath=%~dp0dist"
if not exist "%distPath%" (
    call :errorPause "构建输出目录不存在！请检查 npm run build:prod 是否正确执行。"
)
xcopy /s /e /i "%distPath%\*" "%targetStaticPath%\"
if %ERRORLEVEL% neq 0 (
    call :errorPause "复制新的构建文件失败！"
)
echo [成功] 复制完成！

:: 5. 显示完成信息
echo.
echo 所有任务完成！
exit /b 0
