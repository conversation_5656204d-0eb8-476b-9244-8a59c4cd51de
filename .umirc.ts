import { defineConfig } from '@umijs/max';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  define: {
    'process.env.UMI_ENV': process.env.UMI_ENV,
  },
  history: { type: 'hash' },
  layout: {
    logo: './icon/logo-brand.png',
    title: ' ',
    collapsed: false,
  },
  npmClient: 'pnpm',
  proxy: {
    '/service': {
      target: process.env.API_BASE_URL || 'http://localhost:8080',
      changeOrigin: true,
      pathRewrite: { '^/service': '' },
    },
  },
  // 此时将指向 `/favicon.png` ，确保你的项目含有 `public/favicon.png`
  favicons: ['/logo.png'],
  hash: true, // 启用文件哈希
  chainWebpack(config) {
    config.module
      .rule('woff')
      .test(/.(woff|eot|woff2|ttf)$/)
      .use('file-loader')
      .loader('file-loader');
  },
});
