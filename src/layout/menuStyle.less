@import "@/app";

.ant-layout-sider .ant-menu-item, .ant-layout-sider .ant-menu-submenu-title {
  height: auto !important; /* 设置菜单项的高度 */
  width: @menu-width; /* 增加菜单项的宽度，确保可以显示完整的文字 */
  margin-inline: 0 !important;
}

html body .ant-layout-sider .ant-menu-inline .ant-menu-item {
  padding: 0 !important;
}

.ant-layout-sider-children ul {

}

.ant-menu-title-content {
  height: 100% !important;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  span {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}

.ant-layout-sider-children {
  padding: 0 !important;
  padding-inline: 0 !important;
}

.ant-menu-item-icon {
  font-size: 42px; /* 调整图标大小 */
}

.cloth-sub-menus {
  width: 60px;
  height: calc(210px - 16px * 2);
  border-radius: 8px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 8px 0px;
  gap: 8px;

  /* 中性色/N1-背景色 */
  background: #F5F6F9;

  z-index: 1;
}

.cloth-sub-menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50px;
  width: 100%;
  font-size: 12px;
  line-height: 12px;
  border-radius: 8px;

  .menu-item-icon {
    font-size: 42px;
  }

  div {
    margin-top: 4px;
  }
}

.custom-menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 66px;
  height: auto;
  width: 100%;
  font-size: 12px;
  line-height: 12px;

  div {
    margin-top: 4px;
  }
}

.custom-menu-item:hover {
  background: #D9E1FF;
}

.custom-menu-item-selected {
  background: #D9E1FF;
}

.icon-arrow {
  font-size: 16px;
  color: #727375;
  margin: 8px 0;
  border-radius: 0 !important;
  background: transparent !important;
}

.custom-menu-item {
  border-radius: 8px;
}
.cloth-sub-menu-item {
  border-radius: 8px;
}

.cloth-sub-menu-item:hover {
  background: #D9E1FF;
}

.cloth-sub-menu-item-selected {
  background: #D9E1FF;
}

.step-menu-container {
  width: 68px;
  height: 32px;
  transform: rotate(0deg);
  border-radius: 8px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0px 8px;

  box-sizing: border-box;
  /* 描边色 */
  border: 0.67px solid #D8D8D8;
}

.divider-line {
  width: 24px;
  height: 0;
  opacity: 1;
  border-top: 1px solid #E1E3EB;

  margin: 16px 0;
}

.hot-icon {
  position: absolute;
  right: 4px;
  top: 0;
  width: 36px;
  height: 18px;
  opacity: 1;
}

.to-be-customer {
  width: 120px;
  height: 32px;
  transform: rotate(0deg);
  border-radius: 227px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 16px;
  gap: 4px;

  background: linear-gradient(90deg, #F9EED3 0%, #E4CDF3 38%, #E9DCFF 100%);

  cursor: pointer;
}

.diamond-container {
  width: 24px; /* 设置圆形div的直径 */
  height: 24px;
  background-color: white; /* 设置div背景色为白色 */
  border-radius: 50%; /* 设置圆角，使div变成圆形 */
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden; /* 隐藏溢出部分 */

  img {
    width: 16px;
    height: auto;
  }
}

.topup-title {
  height: 20px;
  opacity: 1;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0px;
  color: #814516;
}

.already-customer {
  height: 32px;
  transform: rotate(0deg);
  border-radius: 227px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0px 12px;
  gap: 4px;
  border: 1px solid #D8D8D8;
  z-index: 1;

  cursor: pointer;
}

.pt-sep {
  width: 10px;
  height: 0px;
  transform: rotate(-90deg);
  opacity: 1;
  border-top: 1px solid #E1E3EB;
  z-index: 1;
}


.topup-center-popover {
  .ant-popover-inner {
    padding: 0;
    border-radius: 16px;
    box-shadow: 8px 6px 10px 0px #E9EAEB;
  }

  .popover-content {
    display: flex;
    flex-direction: column;
    width: 384px;
    padding: 16px;
    border-radius: 16px;
    opacity: 1;
    background: #FFFFFF;
    box-shadow: 8px 6px 10px 0px #E9EAEB;
  }

  .popover-info-row {
    width: 352px;
    height: 72px;
    opacity: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 0px;
    gap: 16px;
  }

  .popover-item {
    opacity: 1;
    display: flex;
    flex-direction: column;
    padding: 0px;
    gap: 8px;
    z-index: 0;
  }

  .popover-item-sep {
    width: 24px;
    height: 0px;
    transform: rotate(90deg);
    opacity: 1;
    border-top: 1px solid #E1E3EB;
    z-index: 1;
  }

  .popover-value {
    font-family: Alibaba Sans;
    font-size: 32px;
    font-weight: bold;
    line-height: 40px;
    letter-spacing: 0px;
    font-variation-settings: "opsz" auto;
    color: #1A1B1D;
    z-index: 1;
  }

  .popover-actions {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    margin-top: 22px;
    gap: 10px;
  }

  .popover-topup-records {
    border-radius: 227px;
    opacity: 1;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0px;
    border: 1px solid #D8D8D8;
  }
}

.menu-item-icon {
  font-size: 42px !important;
  border-radius: 8px;
  //color: #366EF4;
}

.menu-item-icon-sub {
  font-size: 32px !important;
}

.menu-item-icon-selected {
  background: #D9E1FF;
}
