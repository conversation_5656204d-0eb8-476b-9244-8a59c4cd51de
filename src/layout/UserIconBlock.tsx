import IconFont from '@/components/IconFont';
import { USER_INFO } from '@/constants';
import { logout } from '@/services/LoginController';
import { UserVO } from '@/services/UserController';
import { getUserInfo } from '@/utils/utils';
import { Dropdown, MenuProps } from 'antd';
import { useNavigate } from 'react-router-dom';

const UserIconBlock = ({}) => {
  const userInfo: UserVO | null = getUserInfo();
  const navigate = useNavigate();

  const userInfoMenu = {
    key: 'user-info',
    label: (
      <a
        onClick={() => {
          navigate('/user-info');
        }}
      >
        客户信息
      </a>
    ),
  };

  const membersMenu = {
    key: 'members',
    label: (
      <a
        onClick={() => {
          navigate('/members');
        }}
      >
        团队管理
      </a>
    ),
  };

  const userStatsMenu = {
    key: 'statsUserOperate',
    label: (
      <a
        onClick={() => {
          navigate('/stats-user-operate');
        }}
      >
        数据统计
      </a>
    ),
  };

  const logoutMenu = {
    key: 'logout',
    label: (
      <a
        onClick={() => {
          logout().then(() => {
            localStorage.clear();
            sessionStorage.clear();
            navigate('../');
            window.location.reload();
          });
        }}
      >
        退出登录
      </a>
    ),
  };

  const loginMenu = {
    key: 'login',
    label: (
      <a
        onClick={() => {
          logout().then(() => {
            localStorage.removeItem(USER_INFO);
            sessionStorage.clear();
            navigate('/login');
            window.location.reload();
          });
        }}
      >
        登录
      </a>
    ),
  };

  //只有商户和平台运营账号露出‘用户信息’菜单
  let items: MenuProps['items'] = [];
  if (userInfo && !['ADMIN', 'REVIEWER'].includes(userInfo.roleType)) {
    items = [userInfoMenu];

    // 目前仅对运营开放
    if (['OPERATOR'].includes(userInfo.roleType)) {
      items = [...items, userStatsMenu];
    }

    // 主账号显示团队管理
    if (userInfo.userType === 'MASTER') {
      items = [...items, membersMenu];
    }
  }

  items = [...items, userInfo ? logoutMenu : loginMenu];

  return <Dropdown menu={{ items }}>
    <IconFont type={'icon-icon_touxiang'}
              style={{ fontSize: 32, cursor: 'pointer', display: 'flex', alignItems: 'center' }} />
  </Dropdown>;
};

export default UserIconBlock;