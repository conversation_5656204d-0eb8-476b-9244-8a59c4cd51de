import { EVENT_TOGGLE_FLOW_STEPS, IMAGE_POINT, IMAGE_POINT_CHANGE_EVENT, IS_TRIAL_ACCOUNT, HIDE_TOPUP_MODAL } from '@/constants';
import { Button, Flex, Popover, Space } from 'antd';
import React, { useEffect, useState } from 'react';
import './menuStyle.less';
import { useLocation, useNavigate } from 'react-router-dom';
import { queryImagePoint } from '@/services/PointController';
import IconFont from '@/components/IconFont';
import {
  isTrialAccount,
  queryUserProfileByKey,
  RoleTypesItems,
  setUserProfileByKey,
  UserVO,
} from '@/services/UserController';
import topupIcon from '@/assets/icon/充值/icon_充值.png';
import TopupModal from '@/pages/Topup/Topup';
import { getUserInfo, maskPhoneNumber } from '@/utils/utils';
import { checkIfShowTopup } from '@/services/OrderInfoController';

const needShowFlowPath = ['/creation', '/upload', '/models'];

export default () => {
  const userInfo: UserVO | null = getUserInfo();

  //muse 点
  const [imagePoint, setImagePoint] = useState(0);

  //赠送的（通用）图片数量
  const [gavedImgCount, setGavedImgCount] = useState(0);

  const [showFlowSteps, setShowFlowSteps] = useState<boolean>(true);

  const navigate = useNavigate();
  const location = useLocation();

  const [showTopupModal, setShowTopupModal] = useState(false);
  const [isTrial, setIsTrial] = useState(true);

  const [shouldHideTopupModal, setShouldHideTopupModal] = useState(false);

  useEffect(() => {
    if (userInfo && (userInfo.roleType === 'MERCHANT' || userInfo.roleType === 'OPERATOR')) {
      queryImagePoint().then((res) => {
        if (res) {
          setImagePoint(Number(res.imagePoint));
          setGavedImgCount(Number(res.givePoint || 0));
          localStorage.setItem(IMAGE_POINT, res.imagePoint.toString());
        }
      });

      queryUserProfileByKey('showFlowSteps').then(res => {
        setShowFlowSteps(!(res && res.profileVal === 'N'));
      });
    }

    isTrialAccount().then(res => {
      if (res != null) {
        setIsTrial(res);
        sessionStorage.setItem(IS_TRIAL_ACCOUNT, res ? 'Y' : 'N');
      }
    });

    //渠道商特殊隐藏规则
    checkIfShowTopup().then(can => {
      if (can !== null){
        setShouldHideTopupModal(!can);
        sessionStorage.setItem(HIDE_TOPUP_MODAL, can ? 'N' : 'Y');
      }
    })

  }, []);

  useEffect(() => {
    const handleImagePointChange = () => {
      setImagePoint(Number(localStorage.getItem(IMAGE_POINT)));
    };

    window.addEventListener(IMAGE_POINT_CHANGE_EVENT, handleImagePointChange);

    const handleToggleFlowSteps = (event) => {
      setShowFlowSteps(event.detail.showFlowSteps);
    };

    window.addEventListener(EVENT_TOGGLE_FLOW_STEPS, handleToggleFlowSteps);

    return () => {
      window.removeEventListener(IMAGE_POINT_CHANGE_EVENT, handleImagePointChange);
      window.removeEventListener(EVENT_TOGGLE_FLOW_STEPS, handleToggleFlowSteps);
    };
  });


  useEffect(() => {
    const point = localStorage.getItem(IMAGE_POINT);
    if (point) {
      setImagePoint(Number(point));
    }
  }, [localStorage.getItem(IMAGE_POINT)]);

  function onCloseTopup() {
    setShowTopupModal(false);
  }

  function onTopupSuccess() {
    setShowTopupModal(false);
    window.location.reload();
  }

  const PopoverContent = () => (
    <div className="popover-content">
      <div className="popover-info-row">
        <div className="popover-item">
          <div style={{ display: 'flex' }}>
            <IconFont type={'icon-icon_mousidian'} style={{ fontSize: 24 }} />
            <div>缪斯点数量</div>
          </div>
          <div className="popover-value">{imagePoint}</div>
        </div>
        <div className={'popover-item-sep'}></div>
        <div className="popover-item">
          <div style={{ display: 'flex' }}>
            <IconFont type={'icon-icon_tupian'} style={{ fontSize: 24 }} />
            <span>赠送创作图数量</span>
          </div>
          <div className="popover-value">{gavedImgCount}</div>
        </div>
      </div>
      <div className="popover-actions">
        <Button type="default" style={{ borderRadius: 227, display: 'none' }}>充值记录</Button>
      </div>
    </div>
  );

  function getRoleTypeName(record: UserVO) {
    switch (record.roleType) {
      case 'MERCHANT':
        return '商家' + (record.userType === 'MASTER' ? '(主)' : '(子)');
      case 'DISTRIBUTOR':
        return '销售/渠道商' + (`(${record.customRoleName})`);
      default:
        return RoleTypesItems.find(item=> item.key === record.roleType)?.label;
    }
  }

  if (!userInfo) {
    navigate('/login');
    window.location.reload();
    return;
  }

  if (userInfo) {
    return (
      <Flex align={'center'} justify={'flex-start'} gap={8}>
        {!showFlowSteps && needShowFlowPath.includes(location.pathname) && (
          <div className={'step-menu-container'} onClick={() => {
            setShowFlowSteps(true);
            const customEvent = new CustomEvent(EVENT_TOGGLE_FLOW_STEPS, {
              detail: { showFlowSteps: true },
            });
            window.dispatchEvent(customEvent);

            setUserProfileByKey({
              key: 'showFlowSteps',
              value: 'Y',
            });

          }}>
            <div className={'font-pf text14 color-n6'}>步骤</div>
            <IconFont type={'icon-zhankai'} style={{ fontSize: 16 }} />
          </div>
        )
        }

        {/*充值中心 ｜ 点数与图片数*/}
        {userInfo.roleType !== 'ADMIN' && userInfo.roleType !== 'REVIEWER' && (
          <>
            {!isTrial && !shouldHideTopupModal &&
              <div className={'to-be-customer'} onClick={() => setShowTopupModal(true)}>
                <div className={'diamond-container'}>
                  <img src={topupIcon} alt="充值中心" />
                </div>
                <div className={'topup-title'}>充值中心</div>
              </div>
            }

            <Popover
              content={<PopoverContent />}
              trigger="hover"
              overlayClassName="topup-center-popover"
              placement="bottomLeft"
            >
              <div className="already-customer">
                <IconFont type={'icon-icon_mousidian'} style={{ fontSize: 24 }} />
                <span>{imagePoint}</span>
                <div className="pt-sep"></div>
                <IconFont type={'icon-icon_tupian'} style={{ fontSize: 24 }} />
                <span>{gavedImgCount}张</span>
              </div>
            </Popover>
          </>
        )}

        {showTopupModal && !isTrial &&
          <TopupModal visible={showTopupModal} onClose={onCloseTopup} onPaySuccess={onTopupSuccess} />}

        <Flex align={'center'} gap={6}>
          <Flex vertical gap={6} style={{ textAlign: 'right' }}>
            <div style={{ height: 12 }}>{maskPhoneNumber(userInfo.mobile)}</div>
            <div>
              {userInfo.nickName}&nbsp;{userInfo && (userInfo.roleType === 'ADMIN' || userInfo.roleType === 'OPERATOR' || userInfo.roleType === 'REVIEWER') &&
              <span style={{ fontSize: 10 }}>{getRoleTypeName(userInfo)}</span>
            }
            </div>

          </Flex>

        </Flex>

      </Flex>
    );
  }
}
