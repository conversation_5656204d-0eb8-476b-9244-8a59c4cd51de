
.invoice-warning-row {
  margin-top: 16px;
  width: 100%;
  height: 38px;
  border-radius: 6px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 16px;
  gap: 8px;
  align-self: stretch;
  background: #D9E1FF;
  z-index: 1;
}

.invoice-warning-text {
  margin-left: 8px;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: 0em;
  color: #0052D9;
  z-index: 1;
}

.invoice-dlg {
  .ant-form-item-label {
    width: 116px;
    text-align: left;
  }
  .ant-form-item-control {
    width: 392px;
  }
}

.invoice-confirm-btn {
  width: 158px;
  height: 38px;
  border-radius: 8px;
  opacity: 1;
  gap: 8px;
}

.invoice-confirm-btn:disabled {
  color: #FFFFFF;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.6)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%);
}

.invoice-confirm-btn:enabled {
  background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.invoice-confirm-btn:not(:disabled):hover, .invoice-confirm-btn:not(:disabled):focus {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.invoice-confirm-btn:not(:disabled):active {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.invoice-form{
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* Text/colorText */
  color: rgba(0, 0, 0, 0.88);
}