import React, { useEffect, useRef, useState } from 'react';
import { Flex, Progress } from 'antd';
import './MultiStepProgress.less';

interface MultiStepProgressProps {
  steps: Array<number>;
  subStep?: number;
  resize?: number;
}

const MultiStepProgress: React.FC<MultiStepProgressProps> = ({ steps, subStep, resize = null }) => {
  const divRef = useRef<HTMLDivElement>(null);
  const [width, setWidth] = useState<number>(0);
  const [gapWidth, setGapWidth] = useState<number>(0);

  const updateWidth = () => {
    if (divRef.current) {
      let width = divRef.current.offsetWidth;
      console.log('updateWidth', width);
      setWidth(width);

      const gap = (steps.length - 1) * 8;
      setGapWidth(steps.length === 1 ? width : width - gap - 12);
    }
  };

  useEffect(() => {
    updateWidth();
  }, [resize]);

  useEffect(() => {
    updateWidth(); // Set initial width
    window.addEventListener('resize', updateWidth);

    // Cleanup event listener on component unmount
    return () => {
      window.removeEventListener('resize', updateWidth);
    };
  }, []);

  return (
    <Flex vertical style={{ width: '100%' }} gap={4} ref={divRef}>
      <div className="multi-step-progress" style={{ marginTop: -8 }}>
        {steps.map((percent, index) => (
          <div key={index}
               className={'step' + (index === 0 ? ' step-start' : (index === steps.length - 1 ? ' step-end' : ''))}>
            <Progress percent={percent} showInfo={false} format={() => `${index + 1}`} strokeLinecap="butt"
                      size={[(gapWidth / steps.length), 14]}
                      status={percent === 100 ? 'normal' : 'active'} />
            {steps.length > 1 &&
              <div className={'step-label font-pf text12 weight color-a0' + (percent > 50 ? ' color-w' : '')}>
                {index + 1}
              </div>
            }
          </div>
        ))}
      </div>
      {(subStep || subStep === 0) && steps.length > 1 &&
        <Progress percent={subStep} showInfo={false} size={[width, 6]} status={'active'}
                  style={{ marginTop: -12, marginBottom: -4 }} strokeColor={{ from: '#0060FF', to: '#9478EA' }} />
      }
    </Flex>
  );
};

export default MultiStepProgress;