import { Button, ColorPicker, Flex, Form, Input, message, Modal, Tabs, TabsProps, Tag, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import { PlusOutlined, RedoOutlined } from '@ant-design/icons';
import Popconfirm from 'antd/lib/popconfirm';
import IconFont from '@/components/IconFont';
import {
  createPromptDict,
  deletePromptDict,
  PromptDictVO,
  queryAllPromptDict,
  updatePromptDict,
} from '@/services/PromptDictController';

interface PromptDictBlockProps {
  defaultDictType?: string | undefined | null;
}

interface DictChildItem {
  title: string;
  tags: string[];
}

const dictItems: TabsProps['items'] = [
  { key: 'CLOTH_COLLOCATION-system', label: '系统搭配' },
  { key: 'CLOTH_COLLOCATION-custom', label: '用户搭配' },
  { key: 'GARMENT_TYPE-system', label: '服装款式' },
  { key: 'IMAGE_TAGS-system', label: '图片打标' },
  { key: 'FACE_TYPES-system', label: '模特标签' },
  { key: 'CLOTH_COLOR-system', label: '服装颜色' },
];

const DictItemsChildren: Record<string, DictChildItem[]> = {
  'CLOTH_COLLOCATION-system': [
    {title: '上衣搭配', tags: ['tops']},
    {title: '下装搭配', tags: ['bottoms']},
    {title: '鞋子搭配', tags: ['shoe']},
    {title: '其他配饰', tags: ['others']},
    {title: '道具', tags: ['props']},
  ],
  'CLOTH_COLLOCATION-custom': [
    {title: '上衣搭配', tags: ['tops']},
    {title: '下装搭配', tags: ['bottoms']},
    {title: '鞋子搭配', tags: ['shoe']},
    {title: '其他配饰', tags: ['others']},
    {title: '道具', tags: ['props']},
  ],
  'GARMENT_TYPE-system': [
    {title: '服装类型', tags: ['system']},
  ],
  'IMAGE_TAGS-system': [
    {title: 'BadCase', tags: ['system', 'badCase']},
    {title: 'GoodCase', tags: ['system', 'goodCase']},
    {title: '人工介入', tags: ['system', 'manualReplacement']},
    {title: '低质量图库', tags: ['system', 'lowQuality']},
    {title: '是否需要同步至服务器', tags: ['system', ' isSyncToServer']},
  ],
  'FACE_TYPES-system': [
    {title: '女模风格', tags: ['system', 'style', 'female']},
    {title: '男模风格', tags: ['system', 'style', 'male']},
    {title: '国别标签', tags: ['system', 'nation']},
    {title: '特殊标签', tags: ['system', 'special']},
  ],
  'CLOTH_COLOR-system': [
    {title: '服装颜色', tags: ['color']},
  ],
}

const PromptDictBlock: React.FC<PromptDictBlockProps> = ({defaultDictType = 'CLOTH_COLLOCATION-system'}) => {
  const [dict, setDict] = useState<Array<PromptDictVO>>([]);
  const [dictType, setDictType] = useState(defaultDictType);
  const [delConfirm, setDelConfirm] = useState<number | null>(null);
  const [addDict, setAddDict] = useState<any>(null);
  const [updDict, setUpdDict] = useState<PromptDictVO | null>(null);

  const [dictForm] = Form.useForm();

  useEffect(() => {
    fetchDictData();
  }, []);

  function fetchDictData() {
    queryAllPromptDict().then(res => {
      if (res) {
        setDict(res.sort((a, b) => a.word > b.word ? 1 : -1));
        setDictType(dictType);
      }
    });
  }

  const handleAddDict = (values) => {
    setAddDict(values);
    setUpdDict(null);
    dictForm.resetFields();
  };

  const handleUpdDict = (item) => {
    dictForm.resetFields();
    setAddDict(null);
    dictForm.setFieldsValue({ ...item });

    setUpdDict(item);
  };

  const commitDel = (id) => {
    deletePromptDict(id).then(res => {
      if (res) {
        message.success('删除成功');
        fetchDictData();
        setDelConfirm(null);
      }
    });
  };

  const handleCommit = (values) => {
    setAddDict(null);
    let method = addDict ? createPromptDict : updatePromptDict;

    const payload = { ...values };
    if (addDict) {
      payload.tags = addDict.tags;
      payload.type = addDict.type;
    }

    method(payload).then(res => {
      if (res) {
        message.success('保存成功');

        cancelDict();
        fetchDictData();
      }
    });
  };

  const cancelDict = () => {
    setAddDict(null);
    setUpdDict(null);
  };



  const getItemType = (title: string) => {
    return title.split('-')[0];
  };
  const getItemTag = (title: string) => {
    return title.split('-')[1];
  };

  const buildTooltip = (item: PromptDictVO) => {
    return <Flex vertical gap={4}>
      <div>
        {item.memo ? 'prompt：' : ''}{item.prompt}
      </div>
      {item.memo && item.type === 'CLOTH_COLOR' &&
        <div>颜色：{item.memo}</div>
      }
      {item.memo && item.type !== 'CLOTH_COLOR' &&
        <div>备注：{item.memo}</div>
      }
      {/*<div>修改时间：{item.modifyTime}</div>*/}
    </Flex>;
  };


  const tagStyle = { height: 30, display: 'flex', alignItems: 'center' };
  const PromptDictItem = ({ item }) => {
    return <Popconfirm
      key={item.id}
      title={`确定要删除${item.word}吗?`}
      open={delConfirm === item.id}
      onConfirm={() => commitDel(item.id)}
      onCancel={() => setDelConfirm(null)}
    >
      <Tooltip title={() => buildTooltip(item)}>
        <Tag closable={true} style={tagStyle} onClose={(e) => {
          setDelConfirm(item.id);
          e.preventDefault();
        }} color={'processing'}>
          {item.type === 'CLOTH_COLOR' && item.memo && (
            <span 
              style={{ 
                width: 14, 
                height: 14, 
                display: 'inline-block', 
                backgroundColor: item.memo,
                marginRight: 4,
                verticalAlign: 'middle',
                borderRadius: '2px'
              }} 
            />
          )}
          {item.word}
          <IconFont type={'icon-bianji'} style={{ fontSize: 16 }} onClick={() => handleUpdDict(item)} />
        </Tag>
      </Tooltip>
    </Popconfirm>;
  };

  const ClothCollocationConfig = ({ title, tags, dictType }) => {
    const type = getItemType(dictType);
    tags = [getItemTag(dictType), ...tags];
    return <Flex vertical gap={12}>
      <div className={'text16 weight'}>{title}</div>
      <Flex justify={'flex-start'} gap={8} wrap={'wrap'}>
        {dict.filter(item => item.type === type && tags.every(t => item.tags.includes(t))).map(item =>
          <PromptDictItem item={item} key={item.id} />,
        )}

        <Tag className={'pointer'} style={tagStyle} icon={<PlusOutlined />}
             onClick={() => handleAddDict({ type, tags })}>新增</Tag>
      </Flex>
    </Flex>;
  };

  return <>
    <Tabs defaultActiveKey={dictType} items={dictItems} onChange={(key) => setDictType(key)} size={'middle'}
          indicator={{ size: 102 }} style={{ justifyContent: 'center' }} type={'card'} />

    <Flex vertical gap={24}>
      <Flex gap={8} align={'center'}>
        <Tooltip title={'刷新页面数据'}>
          <Button icon={<RedoOutlined />} onClick={() => {
            fetchDictData();
            message.success('刷新成功');
          }} />
        </Tooltip>

        {dictType === 'CLOTH_COLLOCATION-system' &&
          <div className={'color-error weight'}>添加的字段将被展示在创作的服装搭配中</div>
        }
        {dictType === 'CLOTH_COLLOCATION-custom' &&
          <div className={'color-1a weight'}>商家创作时输入的搭配词</div>
        }
      </Flex>

      {DictItemsChildren[dictType] && DictItemsChildren[dictType].map(item =>
        <ClothCollocationConfig key={item.title} title={item.title} tags={item.tags} dictType={dictType} />
      )}

    </Flex>

    {(addDict || updDict) &&
      <Modal open={true} maskClosable={false} centered onCancel={cancelDict} closeIcon={null}
             onOk={() => dictForm.submit()}>
        <Form form={dictForm} onFinish={handleCommit} labelCol={{ span: 4 }} wrapperCol={{ span: 22 }}>
          <Form.Item name="id" rules={[{ required: false }]} hidden>
            <Input disabled />
          </Form.Item>
          <Form.Item name="type" rules={[{ required: false }]} hidden>
            <Input disabled />
          </Form.Item>
          <Form.Item name="tags" rules={[{ required: false }]} hidden>
            <Input disabled />
          </Form.Item>
          <Form.Item name="word" label="名词" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="prompt" label="prompt" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          {dictType.startsWith('CLOTH_COLOR') ? (
            <Form.Item 
              name="memo" 
              label="颜色"
              rules={[
                { required: true, message: '请选择颜色', pattern: /^#([A-Fa-f0-9]{3}){1,2}$/ },
              ]}
              getValueFromEvent={(color) => {
                // 使用 ColorPicker 的 toHexString 方法获取十六进制颜色值
                if (color && typeof color === 'object' && color.toHexString) {
                  return color.toHexString();
                }
                return '#FFFFFF'; // 默认白色
              }}
              initialValue="#FFFFFF"
            >
              <ColorPicker
                format="hex"
                showText
                disabledAlpha
              />
            </Form.Item>
          ) : (
            <Form.Item name="memo" label="备注">
              <Input.TextArea />
            </Form.Item>
          )}
        </Form>
      </Modal>
    }
  </>;
};

export default PromptDictBlock;