import { updateElementExperimental } from '@/services/ElementController';
import { updateModelExperimental } from '@/services/MaterialModelController';
import { message } from 'antd';
import './styles/ExperimentalLabel.less';

interface ExperimentalProps {
  id: number;
  extInfo?: { experimental?: boolean } | Map<string, string> | undefined;
}

export const ExperimentalMark = () => {
  return (
    <div className="experimental-label-wrapper">
      <div className="experimental-label">
        <span>实验</span>
      </div>
    </div>
  );
};

/**
 * 元素实验状态管理 Hook
 *
 * @returns 用于设置元素实验状态的函数
 */
export const useExperimental = () => {
  const ExperimentalLabel = ({ id, extInfo }: ExperimentalProps) => {
    // Handle different types of extInfo
    const isExperimental = extInfo instanceof Map 
      ? extInfo.get('experimental') === 'true'
      : !!extInfo?.experimental;
      
    return isExperimental ? <ExperimentalMark /> : null;
  };
  const getExperimentalLabel = ({ id, extInfo }: ExperimentalProps) => {
    // Handle different types of extInfo
    const isExperimental = extInfo instanceof Map 
      ? extInfo.get('experimental') === 'true'
      : !!extInfo?.experimental;
      
    return isExperimental ? '取消实验' : '设为实验';
  };

  /**
   * 设置元素的实验状态
   *
   * @param item 元素配置对象
   * @param onSuccess 成功回调函数，用于刷新数据等操作
   * @returns void
   */
  const setExperimental = async (
    { id, extInfo, type }: ExperimentalProps & {type: 'Element' | 'MaterialModel'},
    onSuccess?: () => void,
  ) => {
    // Handle different types of extInfo
    const isExperimental = extInfo instanceof Map 
      ? extInfo.get('experimental') === 'true'
      : !!extInfo?.experimental;
    const updateFunc =
      type === 'Element' ? updateElementExperimental : updateModelExperimental;
    updateFunc(id, !isExperimental).then((res) => {
      if (res) {
        message.success(isExperimental ? '已取消实验状态' : '已设为实验状态');
        onSuccess?.();
      }
    });
  };

  return {
    getExperimentalLabel,
    setExperimental,
    ExperimentalLabel,
  };
};
