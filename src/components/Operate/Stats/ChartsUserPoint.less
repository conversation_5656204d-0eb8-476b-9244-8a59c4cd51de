.charts-point-container {
  width: 100%;
  height: 100%;
  min-height: 250px;
}

.charts-point-metrics-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.charts-point-metric-card {
  flex: 1;
  min-width: 150px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.charts-point-metric-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.charts-point-chart-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Different colors for metrics */
.charts-point-metric-point {
  color: #1890ff;
}

.charts-point-metric-give-point {
  color: #52c41a;
}

.charts-point-metric-exp-point {
  color: #fa8c16;
}

.charts-point-metric-recharge {
  color: #722ed1;
}

.charts-point-metric-ratio {
  color: #f5222d;
}

.charts-point-loading {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
} 