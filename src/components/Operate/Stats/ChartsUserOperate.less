.charts-operate-container {
  width: 100%;
  height: 100%;
  min-height: 250px;
}

.charts-operate-metrics-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.charts-operate-metric-card {
  flex: 1;
  min-width: 150px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.charts-operate-metric-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.charts-operate-chart-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Different colors for metrics */
.charts-operate-metric-create {
  color: #1890ff;
}

.charts-operate-metric-download {
  color: #52c41a;
}

.charts-operate-loading {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
} 