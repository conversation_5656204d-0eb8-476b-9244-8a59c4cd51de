import React, { useEffect, useRef, useState, useCallback } from 'react';
import * as echarts from 'echarts';
import { StatsClothesInfoVO } from '@/services/DataController';
import './ChartsClothesInfo.less';

// Chart colors - 马卡龙甜点配色方案
const CHART_COLORS = {
  vipClothesCount: '#fbb8c5',       // 粉红色马卡龙
  autoTrainCount: '#9eddb2',        // 薄荷绿马卡龙
  manualDeliveryCount: '#afb4da',   // 蓝莓色马卡龙
  autoTrainAndDeliveryCount: '#f8cca1', // 杏仁色马卡龙
  retryMattingCount: '#daeca2',     // 开心果色马卡龙
  updatePromptCount: '#f4af9f',     // 覆盆子色马卡龙
  copyCount: '#e0d6f5'              // 淡紫色马卡龙
};

interface ChartsClothesInfoProps {
  data: StatsClothesInfoVO[];
  chartType: 'bar' | 'line' | 'pie';
  statsType: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'TOTAL';
  height?: number | string;
  dateRange?: [string, string];
}

const ChartsClothesInfo: React.FC<ChartsClothesInfoProps> = ({ 
  data, 
  chartType, 
  statsType, 
  height = 400,
  dateRange 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const [chartInstance, setChartInstance] = useState<echarts.ECharts | null>(null);
  const [chartReady, setChartReady] = useState(false);

  // Initialize chart instance
  useEffect(() => {
    let instance: echarts.ECharts | null = null;

    // Initialize chart using requestAnimationFrame to ensure DOM is ready
    const initChart = () => {
      if (chartRef.current) {
        try {
          // Dispose existing instance if any
          if (chartInstance) {
            chartInstance.dispose();
          }

          // Initialize new instance
          instance = echarts.init(chartRef.current);
          setChartInstance(instance);
          setChartReady(true);
        } catch (err) {
          console.error("Failed to initialize chart:", err);
        }
      }
    };

    // Use requestAnimationFrame to ensure DOM rendering is complete
    requestAnimationFrame(initChart);

    // Handle window resize
    const handleResize = () => {
      console.log('Resizing chart...');
      if (chartInstance) {
        try {
          chartInstance.resize();
        } catch (err) {
          console.error("Error resizing chart:", err);
        }
      }
    };

    window.addEventListener('resize', handleResize);

    // Cleanup on component unmount
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance) {
        try {
          chartInstance.dispose();
          setChartInstance(null);
          setChartReady(false);
        } catch (err) {
          console.error("Error disposing chart:", err);
        }
      }
    };
  }, []);

  // Format chart date based on stats type
  const formatChartDate = (value: string, tabType: string) => {
    if (!value) return '';
    
    if (tabType === 'DAILY') {
      return value;
    } else if (tabType === 'WEEKLY') {
      // Weekly format: "YYYY-WW week"
      const parts = value.split('-');
      if (parts.length >= 3) {
        return `${parts[0]}-${parts[1]}-${parts[2].substring(0, 2)}周`;
      }
      return value;
    } else if (tabType === 'MONTHLY') {
      return value.substring(0, 7);
    } else {
      return value;
    }
  };

  // Render chart based on data
  const renderChart = useCallback(() => {
    if (!chartInstance || !data.length) return;

    // 使用传入的数据顺序，不再自行排序
    const workingData = [...data];

    // 如果指定了日期范围，只显示范围内的数据
    let filteredData = workingData;
    if (dateRange && dateRange[0] && dateRange[1]) {
      filteredData = workingData.filter(item => {
        if (!item.statsDate) return false;
        return item.statsDate >= dateRange[0] && item.statsDate <= dateRange[1];
      });
    }

    const dates = filteredData.map(item => item.statsDate || '').filter(Boolean);
    const vipClothesCountData = filteredData.map(item => item.vipClothesCount || 0);
    const autoTrainCountData = filteredData.map(item => item.autoTrainCount || 0);
    const manualDeliveryCountData = filteredData.map(item => item.manualDeliveryCount || 0);
    const autoTrainAndDeliveryCountData = filteredData.map(item => item.autoTrainAndDeliveryCount || 0);
    const retryMattingCountData = filteredData.map(item => item.retryMattingCount || 0);
    const updatePromptCountData = filteredData.map(item => item.updatePromptCount || 0);
    const copyCountData = filteredData.map(item => item.copyCount || 0);

    // Define base options
    const baseOption: any = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params: any) {
          let html = `${params[0].name}<br/>`;
          params.forEach((item: any) => {
            const suffix = item.seriesName === '更新提示词' ? '次' : '套';
            html += `${item.marker}${item.seriesName}: ${item.value} ${suffix}<br/>`;
          });
          return html;
        }
      },
      legend: {
        data: ['VIP用户上传', '自动训练', '人工交付', '自动训练+交付', '二次抠图', '更新提示词', '克隆服装'],
        bottom: 0
      }
    };

    let option;

    // Configure chart based on chart type
    if (chartType === 'pie') {
      // Pie chart configuration
      const allData = [
        { name: 'VIP用户上传', value: vipClothesCountData.reduce((a, b) => a + b, 0), itemStyle: { color: CHART_COLORS.vipClothesCount } },
        { name: '自动训练', value: autoTrainCountData.reduce((a, b) => a + b, 0), itemStyle: { color: CHART_COLORS.autoTrainCount } },
        { name: '人工交付', value: manualDeliveryCountData.reduce((a, b) => a + b, 0), itemStyle: { color: CHART_COLORS.manualDeliveryCount } },
        { name: '自动训练+交付', value: autoTrainAndDeliveryCountData.reduce((a, b) => a + b, 0), itemStyle: { color: CHART_COLORS.autoTrainAndDeliveryCount } },
        { name: '二次抠图', value: retryMattingCountData.reduce((a, b) => a + b, 0), itemStyle: { color: CHART_COLORS.retryMattingCount } },
        { name: '更新提示词', value: updatePromptCountData.reduce((a, b) => a + b, 0), itemStyle: { color: CHART_COLORS.updatePromptCount } },
        { name: '克隆服装', value: copyCountData.reduce((a, b) => a + b, 0), itemStyle: { color: CHART_COLORS.copyCount } }
      ];

      option = {
        ...baseOption,
        series: [
          {
            name: '服装数量',
            type: 'pie',
            radius: '65%',
            center: ['50%', '50%'],
            data: allData,
            label: {
              formatter: '{b}: {c} ({d}%)'
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
    } else {
      // Bar or line chart configuration
      option = {
        ...baseOption,
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: dates,
            axisLabel: {
              rotate: 45,
              formatter: (value: string) => formatChartDate(value, statsType)
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '数量',
            axisLabel: {
              formatter: '{value}'
            }
          }
        ],
        series: [
          {
            name: 'VIP用户上传',
            type: chartType,
            data: vipClothesCountData,
            itemStyle: {
              color: CHART_COLORS.vipClothesCount
            }
          },
          {
            name: '自动训练',
            type: chartType,
            data: autoTrainCountData,
            itemStyle: {
              color: CHART_COLORS.autoTrainCount
            }
          },
          {
            name: '人工交付',
            type: chartType,
            data: manualDeliveryCountData,
            itemStyle: {
              color: CHART_COLORS.manualDeliveryCount
            }
          },
          {
            name: '自动训练+交付',
            type: chartType,
            data: autoTrainAndDeliveryCountData,
            itemStyle: {
              color: CHART_COLORS.autoTrainAndDeliveryCount
            }
          },
          {
            name: '二次抠图',
            type: chartType,
            data: retryMattingCountData,
            itemStyle: {
              color: CHART_COLORS.retryMattingCount
            }
          },
          {
            name: '更新提示词',
            type: chartType,
            data: updatePromptCountData,
            itemStyle: {
              color: CHART_COLORS.updatePromptCount
            }
          },
          {
            name: '克隆服装',
            type: chartType,
            data: copyCountData,
            itemStyle: {
              color: CHART_COLORS.copyCount
            }
          }
        ]
      };
    }

    chartInstance.setOption(option, true);
  }, [data, chartType, statsType, chartInstance, dateRange]);

  // Update chart when data or chart type changes
  useEffect(() => {
    if (data.length > 0 && chartReady && chartInstance) {
      renderChart();
    }
  }, [data, chartType, chartReady, chartInstance, renderChart]);

  return (
    <div 
      ref={chartRef}
      className="charts-clothes-container"
      style={{ height: height }}
    />
  );
};

export default ChartsClothesInfo; 