import React, { useEffect, useRef, useState, useCallback } from 'react';
import * as echarts from 'echarts';
import { StatsUserOperateVO } from '@/services/DataController';
import './ChartsUserOperate.less';

// Chart colors - 马卡龙甜点配色方案
const CHART_COLORS = {
  createCount: '#fbb8c5',    // 粉红色马卡龙
  downloadCount: '#9eddb2',  // 薄荷绿马卡龙
};

interface ChartsUserOperateProps {
  data: StatsUserOperateVO[];
  chartType: 'bar' | 'line' | 'pie';
  statsType: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'TOTAL';
  height?: number | string;
}

const ChartsUserOperate: React.FC<ChartsUserOperateProps> = ({ 
  data, 
  chartType, 
  statsType, 
  height = 400 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const [chartInstance, setChartInstance] = useState<echarts.ECharts | null>(null);
  const [chartReady, setChartReady] = useState(false);

  // Initialize chart instance
  useEffect(() => {
    let instance: echarts.ECharts | null = null;

    // Initialize chart using requestAnimationFrame to ensure DOM is ready
    const initChart = () => {
      if (chartRef.current) {
        try {
          // Dispose existing instance if any
          if (chartInstance) {
            chartInstance.dispose();
          }

          // Initialize new instance
          instance = echarts.init(chartRef.current);
          setChartInstance(instance);
          setChartReady(true);
        } catch (err) {
          console.error("Failed to initialize chart:", err);
        }
      }
    };

    // Use requestAnimationFrame to ensure DOM rendering is complete
    requestAnimationFrame(initChart);

    // Handle window resize
    const handleResize = () => {
      console.log('Resizing chart...');
      if (chartInstance) {
        try {
          chartInstance.resize();
        } catch (err) {
          console.error("Error resizing chart:", err);
        }
      }
    };

    window.addEventListener('resize', handleResize);

    // Cleanup on component unmount
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance) {
        try {
          chartInstance.dispose();
          setChartInstance(null);
          setChartReady(false);
        } catch (err) {
          console.error("Error disposing chart:", err);
        }
      }
    };
  }, []);

  // Format chart date based on stats type
  const formatChartDate = (value: string, tabType: string) => {
    if (tabType === 'DAILY') {
      return value;
    } else if (tabType === 'WEEKLY') {
      // Weekly format: "YYYY-WW week"
      const year = value.substring(0, 4);
      const week = value.substring(5);
      return `${year}-${week}周`;
    } else if (tabType === 'MONTHLY') {
      return value.substring(0, 7);
    } else {
      return value;
    }
  };

  // Render chart based on data
  const renderChart = useCallback(() => {
    if (!chartInstance || !data.length) return;

    // Reverse data to display in chronological order (left to right)
    const reversedData = [...data].reverse();

    const dates = reversedData.map(item => item.statsDate || '').filter(Boolean);
    const createCountData = reversedData.map(item => item.createCount || 0);
    const downloadCountData = reversedData.map(item => item.downloadCount || 0);

    // Define base options
    const baseOption: any = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params: any) {
          let html = `${params[0].name}<br/>`;
          params.forEach((item: any) => {
            html += `${item.marker}${item.seriesName}: ${item.value}<br/>`;
          });
          return html;
        }
      },
      legend: {
        data: ['用户出图量', '图片下载量'],
        bottom: 0
      }
    };

    let option;

    // Configure chart based on chart type
    if (chartType === 'pie') {
      // Pie chart configuration
      const allData = [
        { name: '用户出图量', value: createCountData.reduce((a, b) => a + b, 0), itemStyle: { color: CHART_COLORS.createCount } },
        { name: '图片下载量', value: downloadCountData.reduce((a, b) => a + b, 0), itemStyle: { color: CHART_COLORS.downloadCount } }
      ];

      option = {
        ...baseOption,
        series: [
          {
            name: '操作数量',
            type: 'pie',
            radius: '65%',
            center: ['50%', '50%'],
            data: allData,
            label: {
              formatter: '{b}: {c} ({d}%)'
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
    } else {
      // Bar or line chart configuration
      option = {
        ...baseOption,
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: dates,
            axisLabel: {
              rotate: 45,
              formatter: (value: string) => formatChartDate(value, statsType)
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '操作数量',
            axisLabel: {
              formatter: '{value}'
            }
          }
        ],
        series: [
          {
            name: '用户出图量',
            type: chartType,
            data: createCountData,
            itemStyle: {
              color: CHART_COLORS.createCount
            }
          },
          {
            name: '图片下载量',
            type: chartType,
            data: downloadCountData,
            itemStyle: {
              color: CHART_COLORS.downloadCount
            }
          }
        ]
      };
    }

    chartInstance.setOption(option, true);
  }, [data, chartType, statsType, chartInstance]);

  // Update chart when data or chart type changes
  useEffect(() => {
    if (data.length > 0 && chartReady && chartInstance) {
      renderChart();
    }
  }, [data, chartType, chartReady, chartInstance, renderChart]);

  return (
    <div 
      ref={chartRef}
      className="charts-operate-container"
      style={{ height: height }}
    />
  );
};

export default ChartsUserOperate; 