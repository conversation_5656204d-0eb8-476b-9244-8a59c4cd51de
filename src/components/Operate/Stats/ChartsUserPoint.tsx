import React, { useEffect, useRef, useState, useCallback } from 'react';
import * as echarts from 'echarts';
import { StatsUserPointVO } from '@/services/DataController';
import './ChartsUserPoint.less';

// Chart colors - 马卡龙甜点配色方案
const CHART_COLORS = {
  pointConsumed: '#fbb8c5',    // 粉红色马卡龙 (加深 was #F6C3CD)
  givePointConsumed: '#9eddb2', // 薄荷绿马卡龙 (加深 was #B4E1C2)
  expPointConsumed: '#afb4da',  // 蓝莓色马卡龙 (加深 was #C3C7E5)
  modelPointConsumed: '#f8cca1', // 杏仁色马卡龙 (加深 was #F5D2AF)
  rechargeAmount: '#daeca2',    // 开心果色马卡龙 (加深 was #DBE8B3)
  consumptionRate: '#f4af9f'    // 覆盆子色马卡龙 (加深 was #F4C3B8)
};

interface ChartsUserPointProps {
  data: StatsUserPointVO[];
  chartType: 'bar' | 'line' | 'pie';
  statsType: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'TOTAL';
  height?: number | string;
}

const ChartsUserPoint: React.FC<ChartsUserPointProps> = ({ 
  data, 
  chartType, 
  statsType, 
  height = 400 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const [chartInstance, setChartInstance] = useState<echarts.ECharts | null>(null);
  const [chartReady, setChartReady] = useState(false);

  // Initialize chart instance
  useEffect(() => {
    let instance: echarts.ECharts | null = null;

    // Initialize chart using requestAnimationFrame to ensure DOM is ready
    const initChart = () => {
      if (chartRef.current) {
        try {
          // Dispose existing instance if any
          if (chartInstance) {
            chartInstance.dispose();
          }

          // Initialize new instance
          instance = echarts.init(chartRef.current);
          setChartInstance(instance);
          setChartReady(true);
        } catch (err) {
          console.error("Failed to initialize chart:", err);
        }
      }
    };

    // Use requestAnimationFrame to ensure DOM rendering is complete
    requestAnimationFrame(initChart);

    // Handle window resize
    const handleResize = () => {
      console.log('Resizing chart...');
      if (chartInstance) {
        try {
          chartInstance.resize();
        } catch (err) {
          console.error("Error resizing chart:", err);
        }
      }
    };

    window.addEventListener('resize', handleResize);

    // Cleanup on component unmount
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance) {
        try {
          chartInstance.dispose();
          setChartInstance(null);
          setChartReady(false);
        } catch (err) {
          console.error("Error disposing chart:", err);
        }
      }
    };
  }, []);

  // Format chart date based on stats type
  const formatChartDate = (value: string, tabType: string) => {
    if (tabType === 'DAILY') {
      return value;
    } else if (tabType === 'WEEKLY') {
      // Weekly format: "YYYY-WW week"
      const year = value.substring(0, 4);
      const week = value.substring(5);
      return `${year}-${week}周`;
    } else if (tabType === 'MONTHLY') {
      return value.substring(0, 7);
    } else {
      return value;
    }
  };

  // Render chart based on data
  const renderChart = useCallback(() => {
    if (!chartInstance || !data.length) return;

    // Reverse data to display in chronological order (left to right)
    const reversedData = [...data].reverse();

    const dates = reversedData.map(item => item.statsDate || '').filter(Boolean);
    const pointConsumedData = reversedData.map(item => item.pointConsumed || 0);
    const givePointConsumedData = reversedData.map(item => item.givePointConsumed || 0);
    const modelPointConsumedData = reversedData.map(item => item.modelPointConsumed || 0);
    const rechargeAmountData = reversedData.map(item => item.rechargeAmount || 0);

    // Calculate consumption rate
    const consumptionRateData = reversedData.map(item => {
      if (!item.rechargeAmount || item.rechargeAmount === 0) return 0;
      return ((item.pointConsumed || 0) / item.rechargeAmount) * 100;
    });

    // Define base options
    const baseOption: any = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params: any) {
          let html = `${params[0].name}<br/>`;
          params.forEach((item: any) => {
            if (item.seriesName === '消耗率') {
              html += `${item.marker}${item.seriesName}: ${item.value.toFixed(2)}%<br/>`;
            } else if (item.seriesName === '充值金额') {
              html += `${item.marker}${item.seriesName}: ¥${item.value.toFixed(2)}<br/>`;
            } else {
              html += `${item.marker}${item.seriesName}: ${item.value}<br/>`;
            }
          });
          return html;
        }
      },
      legend: {
        data: ['缪斯点消耗', '赠送点消耗', '套内点消耗', '充值金额', '消耗率'],
        bottom: 0
      }
    };

    let option;

    // Configure chart based on chart type
    if (chartType === 'pie') {
      // Pie chart configuration
      const allData = [
        { name: '缪斯点消耗', value: pointConsumedData.reduce((a, b) => a + b, 0), itemStyle: { color: CHART_COLORS.pointConsumed } },
        { name: '赠送点消耗', value: givePointConsumedData.reduce((a, b) => a + b, 0), itemStyle: { color: CHART_COLORS.givePointConsumed } },
        { name: '套内点消耗', value: modelPointConsumedData.reduce((a, b) => a + b, 0), itemStyle: { color: CHART_COLORS.modelPointConsumed } }
      ];

      option = {
        ...baseOption,
        series: [
          {
            name: '消耗点数',
            type: 'pie',
            radius: '65%',
            center: ['50%', '50%'],
            data: allData,
            label: {
              formatter: '{b}: {c} ({d}%)'
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
    } else {
      // Bar or line chart configuration
      option = {
        ...baseOption,
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: dates,
            axisLabel: {
              rotate: 45,
              formatter: (value: string) => formatChartDate(value, statsType)
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '点数/金额',
            axisLabel: {
              formatter: '{value}'
            }
          },
          {
            type: 'value',
            name: '消耗率(%)',
            axisLabel: {
              formatter: '{value}%'
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '缪斯点消耗',
            type: chartType,
            data: pointConsumedData,
            itemStyle: {
              color: CHART_COLORS.pointConsumed
            }
          },
          {
            name: '赠送点消耗',
            type: chartType,
            data: givePointConsumedData,
            itemStyle: {
              color: CHART_COLORS.givePointConsumed
            }
          },
          {
            name: '套内点消耗',
            type: chartType,
            data: modelPointConsumedData,
            itemStyle: {
              color: CHART_COLORS.modelPointConsumed
            }
          },
          {
            name: '充值金额',
            type: chartType,
            data: rechargeAmountData,
            itemStyle: {
              color: CHART_COLORS.rechargeAmount
            }
          },
          {
            name: '消耗率',
            type: 'line',
            yAxisIndex: 1,
            data: consumptionRateData,
            itemStyle: {
              color: CHART_COLORS.consumptionRate
            },
            lineStyle: {
              width: 2,
              type: 'dashed'
            },
            symbol: 'circle',
            symbolSize: 8
          }
        ]
      };
    }

    chartInstance.setOption(option, true);
  }, [data, chartType, statsType, chartInstance]);

  // Update chart when data or chart type changes
  useEffect(() => {
    if (data.length > 0 && chartReady && chartInstance) {
      renderChart();
    }
  }, [data, chartType, chartReady, chartInstance, renderChart]);

  return (
    <div 
      ref={chartRef}
      className="charts-point-container"
      style={{ height: height }}
    />
  );
};

export default ChartsUserPoint; 