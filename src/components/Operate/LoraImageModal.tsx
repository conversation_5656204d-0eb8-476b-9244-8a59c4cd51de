import React, { FC, useEffect, useState } from 'react';
import { queryCreativesByPage } from '@/services/CreativeController';
import { getUserInfo } from '@/utils/utils';
import ImgPreview from '@/components/ImgPreview';
import { Flex, Modal, Pagination } from 'antd';

interface LoraImageModalProps {
  modelId: number;
  queryAll?: boolean;
  onClose: () => void;
}

const LoraImageModal: FC<LoraImageModalProps> = ({ modelId, queryAll = false, onClose }) => {
  const userInfo = getUserInfo();
  const [allImages, setAllImages] = useState<string[]>([]);
  const [allImagesExtMap, setAllImagesExtMap] = useState<any>({});

  const [showImage, setShowImage] = useState('');
  const [showImageList, setShowImageList] = useState<Array<string>>([]);

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const queryRoleType = queryAll ? null : (userInfo?.roleType === 'DISTRIBUTOR' ? 'DISTRIBUTOR' : 'OPERATOR');

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    fetchData();
  }, [page, pageSize, modelId])

  const fetchData = () => {
    queryCreativesByPage({
      modelId: modelId, queryRoleType, status: 'FINISHED',
      pageNum: page, pageSize,
    }).then((res) => {
      if (res && res.list) {
        let images = Array<string>();
        const imageExtMap = {};
        res.list.forEach((item, index) => {
          if (item.resultImages) {
            const filter = item.resultImages.filter(e => e != null);
            images.push(...filter);
            filter.forEach(e => {
              imageExtMap[e] = (item.faceName ? item.faceName : '') + '/' + (item.sceneName ? item.sceneName : '');
            });
          }
        });
        images = [...new Set(images)];
        setAllImages(images);
        setAllImagesExtMap(imageExtMap);
      }
      setTotal(res?.totalCount || 0)
    });
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setPage(page);
    if (pageSize) {
      setPageSize(pageSize);
    }
  };

  const ImageCard = ({ image, width }) => {
    return (<>
      <div>
        <img src={image} width={width} alt={''} onClick={() => {
          setShowImage(image);
          setShowImageList(allImages);
        }} style={{ cursor: 'pointer' }} />
      </div>
    </>);
  };

  return <>

    <Modal open={true} onCancel={onClose} onOk={onClose}
           width={'calc(100vw * 0.96 + 10px)'} centered>

      <Flex vertical justify={'space-between'} style={{ height: 'calc(100vh - 100px)' }}>
          <Flex gap={8} wrap style={{ maxHeight: 'calc(100vh - 100px - 32px)', overflowY: 'auto' }}>
            {allImages.length > 0 && allImages.map((image, index) => (
              <Flex vertical key={index} align={'center'} gap={4}
                    style={{ border: '1px dashed #e8e8e8', borderRadius: 8, padding: 4, position: 'relative',height: 'auto' }}>
                <ImageCard image={image} width={120} />
              </Flex>
            ))}
          </Flex>

        <Pagination
          current={page}
          pageSize={pageSize}
          total={total}
          onChange={handlePageChange}
          showTotal={(total) => `共 ${total} 个批次`}
          showSizeChanger // 允许用户更改每页显示条数
          pageSizeOptions={[10, 20, 30]}
          showQuickJumper // 允许用户快速跳转到某一页
          style={{ marginTop: '16px', textAlign: 'center' }}
        />
      </Flex>
    </Modal>

    {showImage &&
      <ImgPreview
        previewVisible={!!showImage}
        handleCancelPreview={() => {
          setShowImage('');
          setShowImageList([]);
        }}
        previewImage={showImage}
        needSwitch={!!showImageList}
        previewIdx={showImageList.findIndex(item => item === showImage)}
        previewImgs={showImageList}
        showTools={true}
        modelId={modelId}
        imagesExtMap={allImagesExtMap}
        showBatchInfo={true}
      />
    }
  </>;
};

export default LoraImageModal;