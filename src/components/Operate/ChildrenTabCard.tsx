import { Card, Dropdown, Form, MenuProps, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import { CardTabListType } from 'antd/es/card';
import { deepCopy, isNumber, pathObjectToObject } from '@/utils/utils';
import { AGE_RANGE_LIST } from '@/components/AgeRangeSelector';

interface ChildrenTabCardProps {
  name?: string;
  tagsName?: string;
  needCopyValue?: boolean;
  opItemId: number | null | undefined;
  children: Array<any> | null;
  withNumber?: boolean;
  allTabList: Array<CardTabListType>;
  formItems: (name: number, restField: any) => React.ReactNode;
  onChangeChildren: (children: Array<any> | null) => void;
  form: any;
}

export const processChildrenByName = (extInfoMap: any, name: string, clothTypes: Array<any>, title) => {
  if (!extInfoMap[name] || extInfoMap[name].length < 1) {
    message.error('请至少填写一个' + title);
    return;
  }

  console.log('before', extInfoMap[name]);

  const newChildren = [];
  extInfoMap[name].forEach(child =>
    // @ts-ignore
    newChildren.push(pathObjectToObject(child)),
  );

  extInfoMap[name] = newChildren;

  if (extInfoMap.type && typeof extInfoMap.type !== 'string') {
    //如果是场景lora，如果性别有变更，则替换子场景
    if (extInfoMap.configKey === 'SCENE' && extInfoMap.extInfo['isLora'] === 'Y' && !['Male', 'Female'].every(e => extInfoMap.type.includes(e))) {
      let maleOrFemale = extInfoMap.type.includes('Male') ? 'Male' : 'Female';
      let other = extInfoMap.type.includes('Male') ? 'Female' : 'Male';

      extInfoMap[name].forEach(child => {
        //去掉type列表中value=other的值
        child.type = child.type.filter(e => e !== other);
        child.type.push(maleOrFemale);
      });
    }

    const otherTypes = extInfoMap.type.filter(e => !clothTypes.some(item => item.key.split(',').includes(e)));
    // 从children中获取type，并过滤掉AGE_RANGE_LIST中的类型
    const childrenTypes = new Set(extInfoMap[name].flatMap(child => 
      child.type.filter(type => !AGE_RANGE_LIST.includes(type))
    ));
    extInfoMap.type = [...otherTypes, ...childrenTypes];

    // extInfoMap.type 进行去重
    extInfoMap.type = [...new Set(extInfoMap.type)];
  }

  console.log('after', extInfoMap[name]);
};

export const  processChildren = (extInfoMap: any, clothTypes: Array<any>, title) => {
  processChildrenByName(extInfoMap, 'children', clothTypes, title);
};

const ChildrenTabCard: React.FC<ChildrenTabCardProps> = ({
                                                           name = 'children',
                                                           tagsName = 'type',
                                                           needCopyValue = false,
                                                           opItemId,
                                                           children,
                                                           withNumber = false,
                                                           allTabList,
                                                           formItems,
                                                           onChangeChildren,
                                                           form,
                                                         }) => {
  const [opType, setOpType] = useState<string | null>(null);

  const editOpClothType = (key: React.MouseEvent | React.KeyboardEvent | string, action: 'add' | 'remove') => {
    if (action !== 'remove') {
      return;
    }

    if (typeof key !== 'string') {
      return;
    }

    const filter = form.getFieldValue(name) ? form.getFieldValue(name).filter(e => e[tagsName] && !key.split(',').every(item => e[tagsName].includes(item))) : [];
    console.log('remove', filter, form.getFieldValue(name));
    onChangeChildren(filter);

    setOpType(null);
  };

  const newOpenClothType: MenuProps['onClick'] = ({ key }) => {
    let newData = [];
    if (form.getFieldValue(name) !== null && form.getFieldValue(name) !== undefined) {
      newData = deepCopy(form.getFieldValue(name));
    }

    let newChild;
    if (needCopyValue && newData.length > 0) {
      //@ts-ignore
      newChild = { ...newData[0], [tagsName]: key.split(',') };
    } else {
      newChild = { [tagsName]: key.split(',') };
    }

    // @ts-ignore
    newData.push(newChild);
    onChangeChildren(newData);

    setOpType(key);
  };

  const changeOpClothType = (value: string) => {
    if (opType === value) {
      return;
    }
    setOpType(value);
  };

  const getHasTabList = () => {
    if (!form.getFieldValue(name) || form.getFieldValue(name).length < 1) {
      return [];
    }
    const hasTabList: Array<CardTabListType> = [];
    form.getFieldValue(name).forEach(child => {
      let find = allTabList.find(e => isMatch(e.key, child[tagsName]));
      if (find) {
        if (withNumber) {
          const idx = child[tagsName].find(e => isNumber(e));
          const label = idx ? find.label + idx : find.label;
          const key = idx ? find.key + ',' + idx : find.key;
          find = { key, label };
        }
        hasTabList.push(find);
      }
    });
    return hasTabList;
  };
  const getNewTabList = () => {
    return allTabList.filter(e => !form.getFieldValue(name) || !form.getFieldValue(name).some(t => isMatch(e.key, t[tagsName])));
  };

  const isMatch = (key: string, types: string[]) => {
    if (!key || !types) return false;
    if (key.indexOf(',') === -1) {
      return types.includes(key);
    }

    const split = key.split(',');
    return split.every(item => types.includes(item));
  };

  useEffect(() => {
    setOpType(null);
  }, [opItemId, children]);

  return (
    <Form.List name={name}>
      {fields => (
        <Card className={'width-100'} onTabChange={changeOpClothType} hoverable
              tabList={getHasTabList()} key={form.getFieldValue(name)}
              activeTabKey={opType ? opType : getHasTabList().length > 0 ? getHasTabList()[0].key : undefined}
              tabProps={{
                type: 'editable-card', onEdit: editOpClothType,
                addIcon: (
                  <Dropdown menu={{ items: getNewTabList(), onClick: newOpenClothType }}>
                    <div style={{ width: 36 }}><PlusOutlined /></div>
                  </Dropdown>),
              }}>

          {fields.filter(f => form.getFieldValue(name) && opType ? opType && isMatch(opType, form.getFieldValue(name)[f.name][tagsName]) : f.name === 0)
            .map(({ key, name, ...restField }) => (
              <React.Fragment key={key}>
                {formItems(name, restField)}
              </React.Fragment>
            ))}

        </Card>
      )}
    </Form.List>
  );
};

export default ChildrenTabCard;