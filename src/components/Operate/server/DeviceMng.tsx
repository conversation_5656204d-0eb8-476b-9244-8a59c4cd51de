import React, { FC, useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Drawer, Flex, message, Popconfirm, Tooltip } from 'antd';
import { modifyDeviceInfo, queryDeviceInfo } from '@/services/SystemController';
import { MachineInfo, MachinePort, MachineRoom } from '@/services/ServerController';
import './DeviceMng.less';
import {CloseCircleOutlined, CloudServerOutlined, HomeOutlined} from '@ant-design/icons';
import { KeyValueEditModal } from '@/components/Common/CommonComponent';
import { deepCopy } from '@/utils/utils';

interface DeviceMngProps {
  onChange?: () => void;
}

const DeviceMng: FC<DeviceMngProps> = ({ onChange }) => {
  const [showAllDevice, setShowAllDevice] = useState(false);
  const [machineRooms, setMachineRooms] = useState<Array<MachineRoom>>([]);

  const reloadDevice = () => {
    queryDeviceInfo().then(res => {
      setMachineRooms(res || []);
    });
  };

  const handleChangeRoom: (keyValues: Array<{ key, value }>, room: MachineRoom) => void = (keyValues, room) => {
    const copy = deepCopy(machineRooms);
    copy.forEach(e => {
      if (e.id === room.id) {
        keyValues.forEach(({ key, value }) => {
          e[key] = value;
        });
      }
    });
    commitUpdate(copy, '修改');
  };

  const handelDelRoom = (id: string) => {
    let copy = deepCopy(machineRooms);
    copy = copy.filter(e => e.id !== id);
    commitUpdate(copy, '删除');
  };

  const handleChangeMachine: (keyValues: Array<{
    key, value
  }>, machine: MachineInfo) => void = (keyValues, machine) => {
    const copy = deepCopy(machineRooms);
    copy.forEach(e => {
      e.machines.forEach(f => {
        if (f.id === machine.id) {
          keyValues.forEach(({ key, value }) => {
            f[key] = value;
          });
        }
      });
    });
    commitUpdate(copy, '修改');
  };

  const handelDelMachine = (id: string) => {
    const copy = deepCopy(machineRooms);
    copy.forEach(e => {
      e.machines = e.machines.filter(f => f.id !== id);
    });
    commitUpdate(copy, '删除');
  };

  const handleChangePort: (keyValues: Array<{ key, value }>, port: MachinePort) => void = (keyValues, port) => {

    const copy = deepCopy(machineRooms);
    copy.forEach(e => {
      e.machines.forEach(f => {
        f.ports.forEach(p => {
          if (p.id === port.id) {
            keyValues.forEach(({ key, value }) => {
              p[key] = Number(value);
            });
          }
        });
      });
    });
    commitUpdate(copy, '修改');
  };

  const handelDelPort = (id: string) => {
    const copy = deepCopy(machineRooms);
    copy.forEach(e => {
      e.machines.forEach(f => {
        f.ports = f.ports.filter(p => p.id !== id);
      });
    });
    commitUpdate(copy, '删除');
  };

  const handleAddRoom = (keyValues) => {
    const copy = deepCopy(machineRooms);
    copy.push({
      name: keyValues[0].value,
      machines: [],
    });
    commitUpdate(copy, '新增');
  };

  const handleAddMachine = (keyValues, id) => {
    const copy = deepCopy(machineRooms);
    copy.forEach(e => {
      if (e.id === id) {
        e.machines.push({
          name: keyValues[0].value,
          publicAddress: keyValues[1].value,
          internalAddress: keyValues[2].value,
          ports: [],
        });
      }
    });
    commitUpdate(copy, '新增');
  };

  const handleAddPort = (keyValues, id) => {

    const copy = deepCopy(machineRooms);
    copy.forEach(e => {
      e.machines.forEach(f => {
        console.log('addPort', id, f.id);
        if (f.id === id) {
          f.ports.push({
            port: Number(keyValues[0].value),
            status: 'ENABLE',
          });
        }
      });
    });
    commitUpdate(copy, '新增');
  };

  const commitUpdate = (copy, title) => {
    setMachineRooms(copy);
    modifyDeviceInfo(copy).then(res => {
      if (res) {
        message.success(`${title}成功`);
        reloadDevice();
        if (onChange) {
          onChange();
        }
      }
    });
  };

  useEffect(() => {
    reloadDevice();
  }, []);

  const buildMachineRoomTitle = (e) => (
    <Flex gap={4} align={'center'}>
      <HomeOutlined style={{ fontSize: 18 }} />
      <Tooltip title={e.id}>
        {e.name}
      </Tooltip>
      <KeyValueEditModal keyValues={[{ key: 'name', value: e.name, label: '机房名称' }]} title={'机房'}
                         onChange={(keyValues) => handleChangeRoom(keyValues, e)} />

      <Tooltip title={'删除机房'}>
        <Popconfirm title={'确认删除机房'} onConfirm={() => handelDelRoom(e.id)}>
          <CloseCircleOutlined style={{ fontSize: 14 }} className={'pointer'} />
        </Popconfirm>
      </Tooltip>
    </Flex>
  );

  const AddRoomTitle = () =>
    <KeyValueEditModal keyValues={[{ key: 'name', value: '', label: '机房名称' }]} onChange={handleAddRoom}
                       title={'机房'}
                       isAdd={true} />;

  const AddMachineTitle = ({ id }) =>
    <KeyValueEditModal keyValues={[{ key: 'name', value: '', label: '机器名称' },
      { key: 'publicAddress', value: '', label: '公网域名' },
      { key: 'internalAddress', value: '', label: '内网地址' }]}
                       onChange={keyValues => handleAddMachine(keyValues, id)}
                       title={'机器'} isAdd={true} />;

  const AddPortTitle = ({ id }) =>
    <KeyValueEditModal keyValues={[{ key: 'port', value: '', label: '端口号' }]}
                       onChange={keyValues => handleAddPort(keyValues, id)}
                       title={'端口号'} isAdd={true} />;

  return <>
    <Button className="models-image-card-button"
            onClick={() => setShowAllDevice(true)}>配置设备信息</Button>

    <Drawer
      title={<>设备物理信息配置<AddRoomTitle /></>}
      placement={'right'}
      closable={false}
      onClose={() => setShowAllDevice(false)}
      open={showAllDevice}
      width={1060}
      style={{ background: '#fafafa' }}
    >
      <Flex align={'flex-start'} wrap={'wrap'} gap={12}>
        {machineRooms.map(e => (
          <Card key={e.id} title={buildMachineRoomTitle(e)} extra={<AddMachineTitle id={e.id} />}
                style={{ width: 500 }}>
            <Flex gap={12} vertical>
              {e.machines && e.machines.map(f => <Flex key={f.id} vertical className={'machine-block'}>
                <Flex gap={8} justify={'space-between'}>
                  <Flex gap={8} align={'center'}>
                    <CloudServerOutlined style={{ fontSize: 20 }} />
                    <Flex align={'center'}>
                      <div className={'text16 weight'}>
                        <Tooltip title={f.id}>{f.name ? f.name : f.publicAddress}</Tooltip>
                      </div>
                      (
                      <div className={'text12'}>{f.name ? f.publicAddress + ' # ' : ''}</div>
                      <Tooltip title={'内网IP'}>
                        <div className={'text12'}>{f.internalAddress ? f.internalAddress : '-'}</div>
                      </Tooltip>)
                    </Flex>
                    <KeyValueEditModal keyValues={[{ key: 'name', value: f.name, label: '机器名称' },
                      { key: 'publicAddress', value: f.publicAddress, label: '公网域名' },
                      { key: 'internalAddress', value: f.internalAddress, label: '内网地址' }]} title={'机器'}
                                       onChange={(keyValues) => handleChangeMachine(keyValues, f)} />

                    <Tooltip title={'删除机器'}>
                      <Popconfirm title={'确认删除机器'} onConfirm={() => handelDelMachine(f.id)}>
                        <CloseCircleOutlined style={{ fontSize: 14 }} className={'pointer'} />
                      </Popconfirm>
                    </Tooltip>

                  </Flex>

                  <div>
                    <AddPortTitle id={f.id} />
                  </div>

                </Flex>

                <Flex gap={8} style={{ marginLeft: 20 }} wrap={'wrap'}>
                  {f.ports && f.ports.map(p => <Flex key={p.id}>
                    <Badge status="success" />
                    <Tooltip title={p.id}>{p.port}</Tooltip>
                    <Tooltip title={'删除端口'}>
                      <Popconfirm title={'确认删除端口'} onConfirm={() => handelDelPort(p.id)}>
                        <CloseCircleOutlined style={{ fontSize: 14 }} className={'pointer'} />
                      </Popconfirm>
                    </Tooltip>
                  </Flex>)}
                </Flex>
              </Flex>)}
            </Flex>
          </Card>
        ))}
      </Flex>
    </Drawer>
  </>;
};

export default DeviceMng;