import { Form, Select } from 'antd';
import React, { FC, useEffect } from 'react';
import { queryAllMaster } from '@/services/UserController';

const OpenScopeSelector: FC<{ width?: any, labelCol?: any }> = ({ width, labelCol }) => {
  const [masterOptions, setMasterOptions] = React.useState<any[]>([{ label: '全平台开放', value: 'ALL' }]);
  useEffect(() => {
    queryAllMaster(['ADMIN', 'MERCHANT', 'OPERATOR']).then(res => {
      if (res && Array.isArray(res)) {
        const masterOptions = [{ label: '全平台开放', value: 'ALL' }];
        if (Array.isArray(res)) {
          res.forEach(item => {
            // @ts-ignore
            masterOptions.push({
              label: item.nickName + (item.corpName ? '@' + item.corpName : ''),
              value: item.id + '',
            });
          });
        }
        setMasterOptions(masterOptions);
      }
    });
  }, []);

  return <Form.Item
    label="开放范围"
    initialValue={'ALL'}
    name="extInfo.openScope"
    rules={[{ required: true, message: '请选择正确的开放范围' }]}
    style={{ width: width ? width : '100%' }}
    labelCol={labelCol ? labelCol : null}
  >
    <Select options={masterOptions} style={{ width: 176 }} allowClear showSearch optionFilterProp="label" />
  </Form.Item>;
};

export default OpenScopeSelector;