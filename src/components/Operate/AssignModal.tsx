import { queryAllMaster } from '@/services/UserController';
import { assignCreativeTo, CreativeVO } from '@/services/CreativeController';
import { Alert, Flex, message, Modal, Select } from 'antd';
import React, { useEffect, useState } from 'react';

interface AssignModalProps {
  batch: CreativeVO;
  onChange: () => void;
  onCancel: () => void;
}

const AssignModal: React.FC<AssignModalProps> = ({ batch, onChange, onCancel }) => {
  const [masterOptions, setMasterOptions] = useState<Array<any>>([]);
  const [assignUserId, setAssignUserId] = useState<number | null>(null);

  useEffect(() => {
    initMasterOptions();
  }, []);

  function initMasterOptions() {
    if (masterOptions && masterOptions.length > 0) {
      return;
    }

    queryAllMaster(['MERCHANT', 'OPERATOR']).then(res => {
      if (res && Array.isArray(res)) {
        const masterOptions = [];
        res.forEach(item => {
          // @ts-ignore
          masterOptions.push({
            label: item.nickName + (item.corpName ? '@' + item.corpName : ''),
            value: item.id + '',
          });
        });

        setMasterOptions(masterOptions);
      }
    });
  }

  function confirmAssignTo() {
    if (!assignUserId || !batch) {
      return;
    }
    assignCreativeTo(batch.id, assignUserId).then(res => {
      if (res) {
        message.success('转交成功');
        onChange();
        onCancel();
      }
    });
  }

  return <Modal open={true} title={'设置指定用户'} onCancel={onCancel} onOk={confirmAssignTo}>
    <Flex vertical gap={16}>
      <Alert message={'当前任务已归属于商家“' + batch.userNick + '”，请谨慎操作！！！'}
             type={'error'} />
      <div>
        指定商家：<Select options={masterOptions} style={{ width: 176 }} showSearch
                         optionFilterProp="label" defaultActiveFirstOption={true} value={assignUserId}
                         onChange={(e) => setAssignUserId(e)} />
      </div>
      <div style={{ color: 'red' }}>注意：当前操作不会扣除用户图片点数</div>
    </Flex>
  </Modal>;
};

export default AssignModal;