/* 自定义级联选择器样式 - 使用更具体的选择器确保只影响这个组件 */
.cloth-category-cascader .ant-cascader-dropdown,
.cloth-category-dropdown {
  min-width: 400px !important;
  width: 400px !important; /* 固定宽度而不是自适应 */
  height: auto !important;
  max-height: none !important;
  /* 确保下拉菜单在输入框正下方 */
  transform: translateY(0) !important;
}

/* 增大下拉菜单的宽度和高度 */
.cloth-category-dropdown .ant-cascader-menus,
.cloth-category-cascader + .ant-cascader-dropdown .ant-cascader-menus {
  display: flex !important;
  flex-direction: row !important;
  padding: 8px 0 !important;
  background-color: #fff !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  width: auto !important;
  min-width: 400px !important;
  height: auto !important;
  max-height: none !important;
}

/* 调整菜单容器样式 */
.cloth-category-dropdown .ant-cascader-menu,
.cloth-category-cascader + .ant-cascader-dropdown .ant-cascader-menu {
  width: 150px !important; /* 固定宽度而不是最小宽度 */
  min-width: 150px !important;
  height: auto !important;
  max-height: none !important;
  overflow: visible !important;
}

/* 调整选项样式 */
.cloth-category-dropdown .ant-cascader-menu-item,
.cloth-category-cascader + .ant-cascader-dropdown .ant-cascader-menu-item {
  padding: 8px 16px !important;
  line-height: 20px !important;
  white-space: nowrap !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
}

/* 选中项样式 */
.cloth-category-dropdown .ant-cascader-menu-item-active,
.cloth-category-cascader + .ant-cascader-dropdown .ant-cascader-menu-item-active {
  background-color: #e6f7ff !important;
  font-weight: 500 !important;
}

/* 鼠标悬停样式 */
.cloth-category-dropdown .ant-cascader-menu-item:hover,
.cloth-category-cascader + .ant-cascader-dropdown .ant-cascader-menu-item:hover {
  background-color: #f5f5f5 !important;
}

/* 调整多选标签样式 */
.cloth-category-cascader .ant-select-selection-item {
  background-color: #f0f0f0 !important;
  border-radius: 4px !important;
  margin-right: 6px !important;
  padding: 0 8px !important;
  font-size: 12px !important;
}

/* 移除滚动条 */
.cloth-category-dropdown .ant-cascader-menu::-webkit-scrollbar,
.cloth-category-cascader + .ant-cascader-dropdown .ant-cascader-menu::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

.cloth-category-dropdown .ant-cascader-menu,
.cloth-category-cascader + .ant-cascader-dropdown .ant-cascader-menu {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

/* 增加选项间距 */
.cloth-category-dropdown .ant-cascader-checkbox,
.cloth-category-cascader + .ant-cascader-dropdown .ant-cascader-checkbox {
  margin-right: 8px !important;
}

/* 调整下拉菜单位置，避免被截断 */
.cloth-category-dropdown,
.cloth-category-cascader + .ant-cascader-dropdown {
  position: absolute !important;
  z-index: 1050 !important;
  margin-top: 4px !important; /* 与输入框保持一定间距 */
  /* 不要覆盖原始的top和left值，让它保持在输入框下方 */
}
