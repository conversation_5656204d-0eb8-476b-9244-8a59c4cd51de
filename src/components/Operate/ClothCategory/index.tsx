import type { ClothCategoryGroup } from '@/services/ElementController';
import { Cascader, Space } from 'antd';
import React, { useState } from 'react';
import './style.css';

interface ClothCategorySelectProps {
  value?: string[];
  clothCategoryCfg: ClothCategoryGroup[];
  onChange?: (values: string[]) => void;
  onFocus?: () => void;
  width?: number | string;
}

const ClothCategorySelect: React.FC<ClothCategorySelectProps> = ({
  value = [],
  clothCategoryCfg,
  onChange,
  onFocus,
  width = '100%',
}) => {
  const [selectedValues, setSelectedValues] = useState<string[]>(
    value && Array.isArray(value) ? value : [],
  );

  // 处理Cascader的选择变化
  const handleCascaderChange = (value: any) => {
    // 处理级联选择器的值变化
    // 级联选择器返回的值是一个二维数组，每个子数组表示一个选择路径 [mainCategory, subcategory]
    // 我们需要提取所有的subcategory值
    let flatValues: string[] = [];

    if (Array.isArray(value)) {
      // 处理普通选择和全选的情况
      value.forEach((path) => {
        if (Array.isArray(path) && path.length > 1) {
          // 正常的选择路径 [mainCategory, subcategory]
          flatValues.push(path[1]);
        } else if (Array.isArray(path) && path.length === 1) {
          // 可能是全选了某个主分类，需要添加该分类下的所有子分类
          const mainCategory = path[0];
          const group = clothCategoryCfg.find(
            (g) => g.mainCategory === mainCategory,
          );
          if (group) {
            const subCategories = group.children.map(
              (child) => child.subcategory,
            );
            flatValues = [...flatValues, ...subCategories];
          }
        }
      });
    }

    // 去重
    flatValues = Array.from(new Set(flatValues));
    setSelectedValues(flatValues);
    onChange?.(flatValues);
  };

  // 将数据转换为Cascader需要的格式
  const cascaderOptions = clothCategoryCfg?.map((group) => ({
    value: group.mainCategory,
    label: group.mainCategoryName,
    children: group.children.map((child) => ({
      value: child.subcategory,
      label: child.subcategoryName,
    })),
  }));

  // 将selectedValues转换为Cascader需要的格式 [[mainCategory, subcategory], ...]
  const cascaderValue = selectedValues
    .map((subValue) => {
      for (const group of clothCategoryCfg) {
        const child = group.children.find((c) => c.subcategory === subValue);
        if (child) {
          return [group.mainCategory, subValue];
        }
      }
      return [];
    })
    .filter((v) => v.length > 0);

  return (
    <Space direction="vertical" style={{ width: width }}>
      <Cascader
        className="cloth-category-cascader"
        style={{ width: '100%' }}
        options={cascaderOptions}
        multiple
        maxTagCount={4}
        value={cascaderValue}
        onChange={handleCascaderChange as any}
        onFocus={() => onFocus?.()}
        placeholder="请选择服装款式"
        showSearch
        expandTrigger="hover"
        showCheckedStrategy={Cascader.SHOW_CHILD}
        changeOnSelect
        dropdownRender={(menu) => (
          <div
            onMouseDown={(e) => e.preventDefault()}
            style={{ height: 'auto', maxHeight: '400px' }}
          >
            {menu}
          </div>
        )}
        popupClassName="cloth-category-dropdown"
        dropdownStyle={{
          width: '400px',
          height: 'auto',
          maxHeight: '400px',
          overflow: 'visible',
        }}
        // 确保下拉菜单在输入框下方
        getPopupContainer={(triggerNode) =>
          triggerNode.parentNode as HTMLElement
        }
        // 自定义折叠标签的显示
        maxTagPlaceholder={() => {
          // 直接从当前选中的值中获取被折叠的标签
          // 这样更可靠，因为我们已经有了完整的选中值
          const displayedValues = cascaderValue.slice(0, 3).map((v) => v[1]); // 前3个显示的值
          const hiddenValues = selectedValues.filter(
            (v) => !displayedValues.includes(v),
          ); // 被隐藏的值

          // 获取隐藏值的名称
          const hiddenLabels: string[] = [];
          hiddenValues.forEach((subValue) => {
            for (const group of clothCategoryCfg) {
              const child = group.children.find(
                (c) => c.subcategory === subValue,
              );
              if (child) {
                hiddenLabels.push(child.subcategoryName);
                break;
              }
            }
          });

          // 如果没有隐藏的标签，返回空字符串
          if (hiddenLabels.length === 0) {
            return '';
          }

          // 如果隐藏的标签数量少于等于3个，直接显示所有标签
          if (hiddenLabels.length <= 3) {
            return hiddenLabels.join(', ');
          }

          // 如果隐藏的标签数量超过3个，显示前2个加上数量
          return `${hiddenLabels.slice(0, 2).join(', ')} 等 ${hiddenLabels.length} 个`;
        }}
      />
    </Space>
  );
};

export default ClothCategorySelect;
