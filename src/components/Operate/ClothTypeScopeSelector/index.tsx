import React from 'react';
import { Flex, Select, Space } from 'antd';
import { ClothTypeScopeItem } from '@/services/ElementController';

interface ClothTypeScopeSelectorProps {
    value?: ClothTypeScopeItem[];
    onChange?: (value: ClothTypeScopeItem[]) => void;
    scopeConfig?: ClothTypeScopeItem[];
}

const ClothTypeScopeSelector: React.FC<ClothTypeScopeSelectorProps> = ({
    value = [],
    onChange,
    scopeConfig = [],
}) => {

    value = value && Array.isArray(value) ? value : [];

    const handleChange = (dimensionKey: string, selectedValues: string[]) => {
        console.log('ClothTypeScopeSelector handleChange', dimensionKey, selectedValues);

        let current = [...(value || [])];
        const existingIndex = current.findIndex(item => item.key === dimensionKey);

        if (existingIndex >= 0) {
            // 更新已存在维度的值
            current[existingIndex] = { key: dimensionKey, values: selectedValues };
        } else {
            // 添加新维度
            current.push({ key: dimensionKey, values: selectedValues });
        }

        onChange?.(current);
    };

    return (
        <Space style={{ width: '100%', flexWrap: 'wrap' }}>
            {scopeConfig && scopeConfig.length > 0 && scopeConfig.map((scope, index) => (
                <Flex key={scope.key} align="center" gap={8}>
                    <div style={{ width: 'auto' }}>{scope.key}</div>
                    <Select
                        mode="tags"
                        style={{ width: 'auto', minWidth: 150 }}
                        placeholder={`请选择${scope.key}`}
                        value={value?.find(v => v.key === scope.key)?.values || []}
                        onChange={(newValues) => handleChange(scope.key, newValues)}
                        allowClear
                        options={scope.values.map(v => ({ label: v, value: v }))}
                    />
                </Flex>
            ))}
        </Space>
    );
};

export default ClothTypeScopeSelector;