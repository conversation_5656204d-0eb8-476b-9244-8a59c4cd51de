import { Button, Flex, Form, Input, Modal, Space } from 'antd';
import React, { useEffect } from 'react';
import { editElementType, SceneType } from '@/services/ElementController';
import IconFont from '@/components/IconFont';
import { PlusOutlined } from '@ant-design/icons';
import { deepCopy } from '@/utils/utils';
import { ElementKey } from '@/services/CreativeController';
import { FaceBaseType } from '@/pages/Operate/Face';

interface ElementEditModalProps {
  title: string;

  //见：ElementConfigKeyEnum
  elementKey: ElementKey;
  visible: boolean;
  typeList: SceneType[];
  onCancel: () => void;
  onSave: () => void;
}

const ElementEditModal: React.FC<ElementEditModalProps> = ({
                                                             title,
                                                             elementKey,
                                                             visible,
                                                             typeList,
                                                             onCancel,
                                                             onSave,
                                                           }) => {
  const [data, setData] = React.useState<SceneType[]>([]);
  const [form] = Form.useForm();

  useEffect(() => {
    setData(typeList);
  }, []);

  useEffect(() => {
    form.setFieldsValue({ items: data });
  }, [data, form]);

  const handleSave = () => {
    form.validateFields().then(values => {
      editElementType(elementKey, values.items).then(res => {
        if (res) {
          onSave();
        }
      });
    });
  };

  const remove = (index) => {
    let copy = deepCopy(data);
    copy.splice(index, 1);
    setData(copy);
  };

  const add = () => {
    setData([...data, { code: '', name: '' }]);
  };

  if (!visible) {
    return null;
  }

  return (
    <Modal
      open={visible}
      title={title}
      onCancel={onCancel}
      onOk={handleSave}
      okText="保存"
    >
      <Form form={form} layout="vertical">
        <Form.List name="items">
          {(fields) => (
            <>
              {fields.map(({ key, name, fieldKey, ...restField }, index) => (
                <Flex key={key} style={{ display: 'flex' }} align="flex-start" justify={'flex-start'}
                      hidden={FaceBaseType.includes(form.getFieldValue('items')[index].code)}>
                  key：
                  <Form.Item
                    {...restField}
                    name={[name, 'code']}
                    rules={[{ required: true, message: '请输入 key' }]}
                    style={{ flex: 1, marginRight: 8 }}
                  >
                    <Input placeholder="Key" disabled={data[index] && !!data[index].code} />
                  </Form.Item>
                  value：
                  <Form.Item
                    {...restField}
                    name={[name, 'name']}
                    rules={[{ required: true, message: '请输入 value' }]}
                    style={{ flex: 1 }}
                  >
                    <Input placeholder="Value" />
                  </Form.Item>

                  <IconFont type={'icon-quxiao'} style={{ fontSize: 28 }} onClick={() => remove(name)} />
                </Flex>
              ))}

              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => add()}
                  icon={<PlusOutlined />}
                >
                  新增一行
                </Button>
              </Form.Item>
            </>
          )}
        </Form.List>
      </Form>
    </Modal>
  );
};

export default ElementEditModal;