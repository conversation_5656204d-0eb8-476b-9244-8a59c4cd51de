import { Button, Flex, Modal, Pa<PERSON><PERSON>, Tooltip, Card } from 'antd';
import React, { useEffect, useState } from 'react';
import ImgPreview from '@/components/ImgPreview';
import {
  queryImagesByElementWithPage, queryImagesByElementWithPageFormTask,
} from '@/services/CreativeController';
import Radio from 'antd/lib/radio';
import { PlusOutlined, MinusOutlined } from '@ant-design/icons';
import MasterSelector from '@/components/MasterSelector';
import { RefreshButton } from '@/components/Common/CommonComponent';
import IconFont from '@/components/IconFont';
import '@/components/BasicReplace/StyleLoraDrawer.less';

interface ElementImageRecordProps {
  elementId: number;
  currentId?: number;
  batchNum?: number;
  value?: string | string[];
  isIcon?: boolean;
  onChange?: (value: string | string[]) => void;
  mode?: 'select' | 'view';
  maxSelect?: number; // 最大可选择数量
}

const ElementImageRecord: React.FC<ElementImageRecordProps> = ({
  elementId,
  currentId,
  batchNum = 10,
  value,
  onChange,
  isIcon,
  mode = 'view',
  maxSelect = 1, // 默认为单选
}) => {
  const [showRecord, setShowRecord] = useState(false);
  const [allImages, setAllImages] = useState<string[]>([]);
  const [previewImg, setPreviewImg] = useState<null | string>(null);
  const [previewImgList, setPreviewImgList] = useState<Array<string>>([]);
  const [searchUserId, setSearchUserId] = useState<number | null>(null);
  const [searchTestFlag, setSearchTestFlag] = useState<string | null>(currentId ? 'current' : 'user');
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  // 临时选中的图片，用于在弹窗内操作
  const [tempSelectedImages, setTempSelectedImages] = useState<string[]>([]);

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(5);

  // 处理初始值
  useEffect(() => {
    if (value) {
      if (Array.isArray(value)) {
        setSelectedImages(value);
      } else {
        setSelectedImages(value ? [value] : []);
      }
    } else {
      setSelectedImages([]);
    }
  }, [value]);

  // 当currentId变化时，更新searchTestFlag
  useEffect(() => {
    if (currentId) {
      setSearchTestFlag('current');
    } else {
      setSearchTestFlag('user');
    }
  }, [currentId]);

  const handleShowRecord = () => {
    if (allImages.length > 0) {
      setShowRecord(true);
      setTempSelectedImages([...selectedImages]); // 初始化临时选中状态
      return;
    }
    fetchData();
  };

  const fetchData = () => {
    // 如果searchTestFlag为current，则sceneId为currentId，elementId为null，否则相反
    const sceneId = searchTestFlag === 'current' ? currentId : null;
    const elementIdValue = searchTestFlag === 'current' ? null : elementId;
    const testFlag = searchTestFlag === 'current' ? 'poseSampleDiagram' : (searchTestFlag === 'test' ? 'testCase' : null);

    const query = {
      elementId: elementIdValue,
      sceneId,
      userId: searchUserId,
      bizTag: testFlag,
      bizTagNull: searchTestFlag === 'user',
      pageNum: page,
      pageSize,
    };

    queryImagesByElementWithPage(query).then(res => {
      if (res) {
        setAllImages(res.list || []);
        setShowRecord(true);
        setTotal(res.totalCount || 0);
        // 不要在每次获取新数据时重置临时选择，保持之前的选择状态
        if (!showRecord) {
          // 只在第一次打开弹窗时初始化
          setTempSelectedImages([...selectedImages]);
        }
      }
    });
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setPage(page);
    if (pageSize) {
      setPageSize(pageSize);
    }
  };

  const handleSelectImage = (image: string) => {
    if (mode === 'select') {
      // 在弹窗内仅操作临时状态
      if (tempSelectedImages.includes(image)) {
        // 如果已选择，则移除
        setTempSelectedImages(prevSelected => prevSelected.filter(img => img !== image));
      } else {
        // 点击其他图片时，取消当前选中的图片，选中新点击的图片
        setTempSelectedImages([image]);
      }
    }
  };

  const handleConfirm = () => {
    if (mode === 'select' && onChange) {
      setSelectedImages(tempSelectedImages);
      // 根据maxSelect决定返回单个值还是数组
      if (maxSelect === 1) {
        onChange(tempSelectedImages[0] || '');
      } else {
        onChange(tempSelectedImages);
      }
      setShowRecord(false);
    }
  };

  const handleCancel = () => {
    setShowRecord(false);
    // 放弃临时选择的图片
    setTempSelectedImages([...selectedImages]);
  };

  const ImageCard = ({ image, width, showSelector = false }) => {
    const isSelected = tempSelectedImages.includes(image);
    
    return (
      <div style={{ position: 'relative', display: 'inline-block' }}>
        <img
          src={image}
          width={width}
          alt={''}
          onClick={() => {
            // Always trigger preview on image click
            setPreviewImg(image);
            setPreviewImgList(allImages);
          }}
          style={{ cursor: 'pointer', borderRadius: '6px' }}
        />
        
        {/* 圆形选择框 */}
        {showSelector && (
          <div
            className={`circular-checkbox ${isSelected ? 'checked' : ''}`}
            onClick={(e) => {
              e.stopPropagation();
              handleSelectImage(image);
            }}
            style={{
              position: 'absolute',
              top: '8px',
              right: '8px',
              width: '20px',
              height: '20px',
              borderRadius: '50%',
              background: isSelected ? '#366EF4' : 'rgba(255, 255, 255, 0.9)',
              border: `2px solid ${isSelected ? '#366EF4' : '#d9d9d9'}`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              zIndex: 10,
              boxShadow: isSelected 
                ? '0 2px 8px rgba(54, 110, 244, 0.4), 0 0 0 2px rgba(54, 110, 244, 0.2)'
                : '0 2px 4px rgba(0, 0, 0, 0.1)'
            }}
            onMouseEnter={(e) => {
              if (!isSelected) {
                e.currentTarget.style.borderColor = '#366EF4';
                e.currentTarget.style.background = 'rgba(255, 255, 255, 1)';
                e.currentTarget.style.transform = 'scale(1.1)';
                e.currentTarget.style.boxShadow = '0 2px 8px rgba(54, 110, 244, 0.3)';
              } else {
                e.currentTarget.style.background = '#4d7fff';
                e.currentTarget.style.borderColor = '#4d7fff';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(54, 110, 244, 0.5), 0 0 0 3px rgba(54, 110, 244, 0.3)';
                e.currentTarget.style.transform = 'scale(1.05)';
              }
            }}
            onMouseLeave={(e) => {
              if (!isSelected) {
                e.currentTarget.style.borderColor = '#d9d9d9';
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.9)';
                e.currentTarget.style.transform = 'scale(1)';
                e.currentTarget.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
              } else {
                e.currentTarget.style.background = '#366EF4';
                e.currentTarget.style.borderColor = '#366EF4';
                e.currentTarget.style.boxShadow = '0 2px 8px rgba(54, 110, 244, 0.4), 0 0 0 2px rgba(54, 110, 244, 0.2)';
                e.currentTarget.style.transform = 'scale(1)';
              }
            }}
          >
            {isSelected && (
              <div style={{
                color: 'white',
                fontSize: '12px',
                fontWeight: 'bold',
                lineHeight: 1
              }}>
                ✓
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  useEffect(() => {
    if (!showRecord) return;
    fetchData();
  }, [searchTestFlag, searchUserId, currentId, page, pageSize]);

  // 删除临时选中的图片
  const handleRemoveTempImage = (image: string) => {
    setTempSelectedImages(prevSelected => prevSelected.filter(img => img !== image));
  };

  return <>
    <Flex gap={12} align="center">
      {mode === 'select' && (
        <div
          style={{
            width: 80,
            height: 80,
            border: '1px dashed #d9d9d9',
            borderRadius: 6,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',
            overflow: 'hidden',
            backgroundColor: '#fafafa',
          }}
        >
          {selectedImages.length > 0 ? (
            <>
              <img
                src={selectedImages[0]}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  cursor: 'pointer',
                }}
                onClick={() => {
                  setPreviewImg(selectedImages[0]);
                  setPreviewImgList(selectedImages);
                }}
              />
              <div
                style={{
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0,
                  height: 20,
                  background: 'rgba(0,0,0,0.45)',
                  color: '#fff',
                  fontSize: 12,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                }}
                onClick={handleShowRecord}
              >
                {selectedImages.length > 1 ? `已选${selectedImages.length}张` : '点击更换'}
              </div>
            </>
          ) : (
            <div
              style={{
                cursor: 'pointer',
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              onClick={handleShowRecord}
            >
              <Flex vertical gap={2} align="center">
                <PlusOutlined style={{ fontSize: 20, color: '#999' }} />
                <span style={{ color: '#999', fontSize: 12 }}>选择图片</span>
              </Flex>
            </div>
          )}
        </div>
      )}
      {mode === 'view' && !isIcon && (
        <Button
          className={'operate-btn'}
          size={'small'}
          onClick={handleShowRecord}
        >
          记录
        </Button>
      )}
      {mode === 'view' && isIcon && (
        <Tooltip title={'查看历史创作记录'}>
          <IconFont type={'icon-lishirenwu_weixuanzhong1'} style={{ color: '#727375', fontSize: 16 }}
            onClick={handleShowRecord} />
        </Tooltip>
      )}
    </Flex>

    {showRecord &&
      <Modal
        open={true}
        width={'90%'}
        centered
        onCancel={handleCancel}
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          <Button key="confirm" type="primary" onClick={handleConfirm}>
            确认
          </Button>,
        ]}
        styles={{
          body: { padding: '16px', height: 'calc(90vh)', display: 'flex', flexDirection: 'column' }
        }}
      >
        {/* 顶部固定区域 */}
        <div style={{ marginBottom: '16px' }}>
          <div className={'text16 weight'} style={{ marginBottom: '12px' }}>已选择: {tempSelectedImages.length}/{maxSelect}</div>
          
          <Card style={{ background: '#F5F5F5' }} styles={{ body: { padding: 0 } }}>
            <Flex gap={8} style={{ position: 'relative', width: '100%' }} wrap={'wrap'}>
              {tempSelectedImages.length > 0 ? (
                tempSelectedImages.map((image, index) => (
                  <Flex vertical key={index} align={'center'} gap={4}
                    style={{
                      border: '1px dashed #e8e8e8',
                      borderRadius: 8,
                      padding: 4,
                      position: 'relative',
                      height: 'auto',
                      background: '#f6ffed',
                      transition: 'all 0.3s ease',
                    }}>
                    <div style={{ position: 'relative' }}>
                      <ImageCard image={image} width={80} />
                      {/* 选中状态指示器 */}
                      <div
                        style={{
                          position: 'absolute',
                          top: '8px',
                          right: '8px',
                          width: '18px',
                          height: '18px',
                          borderRadius: '50%',
                          background: '#52c41a',
                          border: '2px solid #52c41a',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          zIndex: 10,
                          boxShadow: '0 2px 8px rgba(82, 196, 26, 0.4)',
                        }}
                      >
                        <div style={{
                          color: 'white',
                          fontSize: '10px',
                          fontWeight: 'bold',
                          lineHeight: 1
                        }}>
                          ✓
                        </div>
                      </div>
                    </div>
                    <Button size={'small'} onClick={() => handleRemoveTempImage(image)} block icon={<MinusOutlined />}>删除</Button>
                  </Flex>
                ))
              ) : (
                <Flex vertical gap={8} align={'center'} justify={'center'} className={'width-100'}
                  style={{ padding: 16 }}>
                  <IconFont type={'icon-kongzhuangtai'} style={{ fontSize: 32, color: '#000000' }} />
                  <div className={'text16 font-pf color-b'}>未选择</div>
                </Flex>
              )}
            </Flex>
          </Card>
          
          <Flex gap={8} justify={'flex-start'} style={{ marginTop: '16px' }}>
            <RefreshButton refresh={fetchData} />

            <Radio.Group optionType={'button'} onChange={e => {
              setSearchTestFlag(e.target.value);
              setPage(1);
            }}
              defaultValue={searchTestFlag}>
              <Radio value={'all'}>全部</Radio>
              <Radio value={'user'}>只看用户创作</Radio>
              <Radio value={'test'}>只看AB测试</Radio>
              {currentId && <Radio value={'current'}>只看当前姿势</Radio>}
            </Radio.Group>

            <MasterSelector
              onChange={userId => {
                setSearchUserId(userId);
                setPage(1);
              }}
              width={160}
              roleTypes={['ADMIN', 'OPERATOR', 'MERCHANT']}
              placeholder={'创建人检索'}
            />
          </Flex>
        </div>
        
        {/* 滚动区域 */}
        <div style={{ flex: 1, overflowY: 'auto', display: 'flex', flexDirection: 'column' }}>
          <div className={'text16 weight'} style={{ marginBottom: '12px' }}>请选择:</div>

          <Flex gap={8} wrap style={{ marginBottom: '16px' }}>
            {allImages.length > 0 && allImages.map((image, index) => {
              const isSelected = tempSelectedImages.includes(image);
              const isPreviewActive = previewImg === image;
              
              return (
                <Flex
                  vertical
                  key={index}
                  align={'center'}
                  gap={4}
                  style={{
                    boxShadow: isPreviewActive 
                      ? '0 0 0 2px #366EF4' 
                      : isSelected 
                        ? '0 0 0 2px #52c41a' 
                        : 'none',
                    border: isSelected 
                      ? '1px solid #52c41a' 
                      : '1px dashed #e8e8e8',
                    borderRadius: 8,
                    padding: 4,
                    position: 'relative',
                    width: 'calc(120px + 8px)', // 图片宽度 + padding
                    flexShrink: 0, // 防止flex布局下的压缩
                    background: isSelected ? '#f6ffed' : 'transparent',
                    transition: 'all 0.3s ease',
                  }}
                >
                  <ImageCard image={image} width={120} showSelector={mode === 'select'} />
                </Flex>
              );
            })}
          </Flex>
        </div>

        {/* 分页区域 */}
        <div style={{ 
          display: 'flex', 
          flexDirection: 'column', 
          gap: '8px',
          padding: '16px 0',
          borderTop: '1px solid #f0f0f0',
          marginTop: 'auto'
        }}>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            color: '#666',
            fontSize: '14px'
          }}>
            <span>共 <span style={{ color: '#1890ff', fontWeight: 500 }}>{total}</span> 个批次，展示图片数量不固定，与具体批次有关</span>
            <span>当前为第 <span style={{ color: '#1890ff', fontWeight: 500 }}>{(page - 1) * pageSize + 1}</span> ～ <span style={{ color: '#1890ff', fontWeight: 500 }}>{Math.min(page * pageSize, total)}</span> 批次</span>
          </div>
          <Pagination
            current={page}
            pageSize={pageSize}
            total={total}
            onChange={handlePageChange}
            showSizeChanger
            pageSizeOptions={[10, 20, 30]}
            showQuickJumper
            style={{ textAlign: 'center' }}
          />
        </div>
      </Modal>
    }

    {previewImg !== null &&
      <ImgPreview
        previewVisible={!!previewImg}
        handleCancelPreview={() => {
          setPreviewImg(null);
          setPreviewImgList([]);
        }}
        previewImage={previewImg}
        needSwitch={!!previewImgList}
        previewIdx={previewImgList.findIndex(item => item === previewImg)}
        previewImgs={previewImgList}
        showTools={true}
        showBatchInfo={true}
      />
    }
  </>;
};

export default ElementImageRecord;