import {
  Button,
  Checkbox,
  DatePicker,
  Flex,
  Form,
  Input,
  message,
  Modal,
  notification,
  Pagination,
  Tabs,
  TabsProps,
  Tag,
  Tooltip,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { ImageCase, queryImageCaseByPage } from '@/services/ImageCaseController';
import { LoadingOutlined, RedoOutlined } from '@ant-design/icons';
import { PromptDictVO, queryImageBadTags } from '@/services/PromptDictController';
import ImgPreview from '@/components/ImgPreview';
import { addSys, ImageCaseSyncConfig, queryImageCaseSyncConfig, updateSys } from '@/services/SystemController';
import { IMAGE_CASE_SYNC_CONFIG } from '@/constants';
import dayjs from 'dayjs';
import { MemoText } from '@/components/Common/CommonComponent';


const defaultPageSize = 100;

// 标签类型、顶部标签栏
const pictureCaseEnum = [
  {
    key: 'badCase',
    label: 'BadCase图库',
    showTags: true,
  },
  {
    key: 'goodCase',
    label: 'GoodCase图库',
  },
  {
    key: 'manualReplacement',
    label: '人工介入图库',
  },
  {
    key: 'lowQuality',
    label: '低质量图库',
    showTags: true,
  },
];


const ImageCaseBlock: React.FC = ({}) => {
  const [tagType, setTagType] = useState(pictureCaseEnum[0].key);
  const [data, setData] = useState<Array<ImageCase>>([]);
  const [tags, setTags] = useState<Array<PromptDictVO>>([]);

  const [showTags, setShowTags] = useState<Array<number>>([]);
  const [beginSearchTime, setBeginSearchTime] = useState<string | null>(null);
  const [endSearchTime, setEndSearchTime] = useState<string | null>(null);
  const [searchBatchId, setSearchBatchId] = useState<string | null>(null);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPageSize);
  const [total, setTotal] = useState(0);

  const [detailImage, setDetailImage] = useState<string | null>(null);
  const [detailImageList, setDetailImageList] = useState<Array<string>>([]);


  const [showDialog, setShowDialog] = useState(false);
  const [form] = Form.useForm();

  // 是否开启编辑
  const [isOpenEdit, setIsOpenEdit] = useState<boolean | undefined>(false);
  // 图库同步配置
  const [imageCaseSyncConfigInfo, setImageCaseSyncConfigInfo] = useState<ImageCaseSyncConfig>();

  // 检查图片的 RGBA 值
  const checkRgba = (file: File): Promise<boolean> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img: HTMLImageElement = new Image();
        img.onload = () => {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          if (!ctx) {
            resolve(false);
            return;
          }
          canvas.width = img.width;
          canvas.height = img.height;
          ctx.drawImage(img, 0, 0);

          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          const data = imageData.data;

          // 检查是否存在透明像素
          for (let i = 3; i < data.length; i += 4) {
            if (data[i] !== 255) {
              resolve(true);
              return;
            }
          }
          resolve(false);
        };
        img.onerror = () => resolve(false);
        img.src = e.target?.result as string;
      };
      reader.onerror = () => resolve(false);
      reader.readAsDataURL(file);
    });
  };

  // 标签列表
  const tagItems: TabsProps['items'] = pictureCaseEnum;

  useEffect(() => {

    fetchImageCaseData();

    // 获取配置信息
    fetchConfig();
  }, []);

  useEffect(() => {
    fetchImageCaseData();
  }, [pageNum, pageSize, showTags, beginSearchTime, endSearchTime, tagType, searchBatchId]);

  useEffect(() => {
    queryImageBadTags(tagType).then(res => {
      if (res && res.length > 0) {
        setTags(res.sort((a, b) => a.word > b.word ? 1 : -1));
      }
    });
  }, [tagType]);

  // 获取 BadCase 数据
  const fetchImageCaseData = () => {
    const payload = {
      pageSize,
      pageNum,
      tagIds: showTags,
      beginSearchTime: beginSearchTime,
      endSearchTime: endSearchTime,
      imageCaseType: tagType,
      batchId: searchBatchId ? searchBatchId : undefined,
    };

    // 查询 BadCase 列表
    queryImageCaseByPage(payload).then(res => {
      if (res) {
        // 设置数据
        setData(res.list ?? []);

        // 设置总数
        setTotal(res?.totalCount ?? 0);
      }
    });
  };

  // 获取当前图库的配置信息
  const getCurrentImageCaseSyncConfig = () => {
    // 校验是哪种类型的图库
    switch (tagType) {
      case pictureCaseEnum[0].key:
        return imageCaseSyncConfigInfo?.badCase;
      case pictureCaseEnum[1].key:
        return imageCaseSyncConfigInfo?.goodCase;
      case pictureCaseEnum[2].key:
        return imageCaseSyncConfigInfo?.manualReplacement;
      case pictureCaseEnum[3].key:
        return imageCaseSyncConfigInfo?.lowQuality;
      default:
        return null;
    }
  };

  // 获取图库同步配置
  const fetchConfig = () => {
    queryImageCaseSyncConfig().then(res => {
      if (res) {
        // 判断 res 是否为 json 字符串
        if (typeof res === 'string') {
          // 解析 JSON 字符串
          const parse: ImageCaseSyncConfig = JSON.parse(res) as ImageCaseSyncConfig;
          // 设置配置信息
          setImageCaseSyncConfigInfo(parse);
        } else {
          // 若配置不存在，则直接创建默认配置
          addSys({
            confKey: IMAGE_CASE_SYNC_CONFIG,
            confValue: JSON.stringify({}),
            memo: '图片案例同步配置',
          }).then(res => {
          });
        }
      }
    });
  };

  const handleShowTags = (id, checked) => {
    const copy = [...showTags];
    if (checked) {
      copy.push(id);
    } else {
      copy.splice(copy.indexOf(id), 1);
    }
    setShowTags(copy);
  };

  const handlePageChange = (pageNum: number, pageSize?: number) => {
    setPageNum(pageNum);
    if (pageSize) {
      setPageSize(pageSize);
    }
  };

  const handlePreview = (imageCase) => {
    setDetailImage(imageCase.url);
    setDetailImageList(data.map(item => item.url));
  };

  // 时间范围发生变化
  const dateRangeChange = (dates, dateStrings) => {
    // 设置时间
    setBeginSearchTime(dateStrings[0]);
    setEndSearchTime(dateStrings[1]);
  };

  // 同步配置
  const handleSync = () => {
    // 清空 form 表单
    form.resetFields();

    // 为 form 表单赋值
    form.setFieldsValue(getCurrentImageCaseSyncConfig());

    // 开启弹窗
    setShowDialog(true);
  };

  // 表单提交
  const handleCommit = (values: Record<string, any>) => {
    // 锁定编辑状态
    setIsOpenEdit(false);

    // 处理数据
    const syncConfig = {
      targetServer: values.targetServer,
      targetPath: values.targetPath,
    };

    // 保持其他配置不变，只更新当前选中类型的配置
    // 确保所有必需的属性都有默认值，避免 undefined
    const requestParams = {
      badCase: { targetServer: '', targetPath: '' },
      goodCase: { targetServer: '', targetPath: '' },
      manualReplacement: { targetServer: '', targetPath: '' },
      lowQuality: { targetServer: '', targetPath: '' },
      ...imageCaseSyncConfigInfo,
      [tagType]: syncConfig,
    };

    // 更新系统配置
    updateSys({
      confKey: IMAGE_CASE_SYNC_CONFIG,
      confValue: JSON.stringify(requestParams),
    }).then(res => {
      if (res) {
        setImageCaseSyncConfigInfo(requestParams);

        // 提示消息
        notification.success({ message: '更新成功' });
      }
    });

    // 清空表单
    form.resetFields();
    // 关闭弹窗
    setShowDialog(false);
  };

  // 获取当前的图片类型
  const getCurrentPictureCaseByKey = () => {
    return pictureCaseEnum.find(item => item.key === tagType);
  };


  // 切换编辑状态
  const changeEditStatus = () => {
    // 切换编辑状态
    setIsOpenEdit(!isOpenEdit);
  };


  const ImageCard = ({ imageCase }) =>
    <Flex vertical gap={8} style={{
      border: '1px solid #d9d9d9',
      padding: 8,
      background: '#FFFFFF',
      borderRadius: 8,
    }}>
      <div onClick={() => handlePreview(imageCase)} className={'pointer'} style={{ margin: '0 auto' }}>
        <img src={imageCase.url} alt={'img'} style={{ width: 150, borderRadius: 8 }} />
      </div>
      <Flex gap={4} wrap={'wrap'}>
        {imageCase.tagDetails &&
          imageCase.tagDetails.map((tag, index) => <Tag key={index} color={'cyan'}>{tag.title}</Tag>)
        }
        <MemoText value={imageCase.batchId} />
      </Flex>
      <div style={{ fontSize: 12, fontWeight: 'normal' }}>
        <span style={{ fontWeight: 'bold' }}>最后修改人</span>：{imageCase.operatorNick}
      </div>
      <div style={{ fontSize: 12, fontWeight: 'normal' }}>
        <span style={{ fontWeight: 'bold' }}>同步状态</span>：{
        imageCase.extInfo?.isNeedUpload === false ?
          <span style={{ color: '#52c41a' }}>无需同步</span> :
          (imageCase.syncStatus ?
            <span style={{ color: 'green' }}>同步成功</span> :
            (imageCase.reSyncCount >= 10 ?
              <span style={{ color: 'red' }}>同步失败</span> :
              (imageCase.reSyncCount > 0 ?
                <span style={{ color: '#faad14' }}>
                    <LoadingOutlined spin style={{ marginRight: 4 }} />
                    同步中
                  </span> :
                <span style={{ color: '#1677ff' }}>待同步</span>)))
      }
      </div>

      <div style={{ fontSize: 12, fontWeight: 'normal' }}>
        <span style={{ fontWeight: 'bold' }}>创建时间</span>：{imageCase.createTime}
      </div>
      <div style={{ fontSize: 12, fontWeight: 'normal' }}>
        <span style={{ fontWeight: 'bold' }}>同步时间</span>：{imageCase.syncTime}
      </div>
    </Flex>;


  return <>
    <Tabs defaultActiveKey={tagType} items={tagItems} onChange={(key) => {
      setTagType(key);
      setPageNum(1);
      setBeginSearchTime(null);
      setEndSearchTime(null);
    }} size={'middle'}
          indicator={{ size: 102 }} style={{ justifyContent: 'center' }} type={'card'} />


    <Flex gap={16} vertical>
      <Flex gap={16}>
        <Tooltip title={'刷新页面数据'}>
          <Button icon={<RedoOutlined />} onClick={() => {
            fetchImageCaseData();
            message.success('刷新成功');
          }} />
        </Tooltip>

        {pictureCaseEnum.some(item => tagType === item.key && item.showTags) &&
          <Flex wrap={'wrap'} gap={8} align={'center'}>
            {tags.map(s => (
              <Checkbox key={s.id} onChange={e => handleShowTags(s.id, e.target.checked)}
                        style={{ fontSize: 12 }} checked={showTags.includes(s.id)}><span
                className={'color-72'}>{s.word}</span>
              </Checkbox>
            ))}
          </Flex>
        }
        {/*  时间查询 */}
        <DatePicker.RangePicker
          showTime
          value={[
            beginSearchTime ? dayjs(beginSearchTime) : null,
            endSearchTime ? dayjs(endSearchTime) : null,
          ]}
          onChange={dateRangeChange}
        />

        <Input placeholder={'出图任务id'} style={{ width: 200 }} onChange={e=> setSearchBatchId(e.target.value)} />

        {/* 图库同步配置 */}
        <Button className="models-image-card-button" onClick={handleSync} type={'primary'}>图库同步配置</Button>
      </Flex>

      <Flex gap={16} wrap={'wrap'}>
        {data.map((imageCase, index) => <ImageCard key={index} imageCase={imageCase} />)}
      </Flex>
    </Flex>


    <Pagination
      current={pageNum}
      pageSize={pageSize}
      total={total}
      onChange={handlePageChange}
      showTotal={(total) => `共 ${total} 张图片`}
      showSizeChanger // 允许用户更改每页显示条数
      pageSizeOptions={[defaultPageSize, defaultPageSize * 2, defaultPageSize * 3]}
      showQuickJumper // 允许用户快速跳转到某一页
      style={{ marginTop: '16px', textAlign: 'center' }}
    />


    {showDialog &&
      <Modal open={true} title={getCurrentPictureCaseByKey()?.label + '同步配置'} width={'800px'} closable={false}
             mask={true} maskClosable={false} centered={true} okButtonProps={{ disabled: !isOpenEdit }}
             onCancel={() => {
               setShowDialog(false);
               setIsOpenEdit(false);
             }}
             onOk={form.submit}
             footer={(_, { OkBtn, CancelBtn }) => (
               <>
                 <CancelBtn />
                 <Button variant="outlined" color="primary"
                         onClick={changeEditStatus}>{isOpenEdit ? '锁定' : '取消锁定'}</Button>
                 <OkBtn />
               </>
             )}>

        <Form labelCol={{ span: 4 }}
              wrapperCol={{ span: 18 }}
              initialValues={{ remember: true }}
              onFinish={handleCommit}
              autoComplete="off"
              form={form}>
          <Form.Item hidden label="id" name="id">
            <Input />
          </Form.Item>
          <Form.Item label="目标服务器地址" name="targetServer" style={{ width: '100%' }}
                     rules={[{ required: true, message: '请输入目标服务器ip地址/域名' },
                       { type: 'url', message: '请输入正确的url地址' }]}>
            <Input disabled={!isOpenEdit} style={{ width: 600 }} />
          </Form.Item>

          <Form.Item label="目标服务器路径" name="targetPath" style={{ width: '100%' }}
                     rules={[{ required: true, message: '请输入目标服务器上传路径' },
                       { pattern: /^[a-zA-Z0-9_\-\s\.\/]+$/, message: '请输入正确的文件夹路径' },
                     ]}>
            <Input disabled={!isOpenEdit} style={{ width: 600 }} />
          </Form.Item>
        </Form>
      </Modal>
    }

    {detailImage &&
      <ImgPreview
        previewVisible={!!detailImage}
        handleCancelPreview={() => {
          setDetailImage(null);
          setDetailImageList([]);
        }}
        previewImage={detailImage}
        needSwitch={!!detailImageList}
        previewIdx={detailImageList.findIndex(item => item === detailImage)}
        previewImgs={detailImageList}
      />
    }

  </>;
};

export default ImageCaseBlock;