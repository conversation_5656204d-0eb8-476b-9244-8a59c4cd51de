import { Button, Card, Flex, Input, message, Modal, notification, Upload, UploadFile, UploadProps, Pagination } from 'antd';
import React, { useEffect, useState } from 'react';
import { MinusOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { queryImagesByElementWithPage } from '@/services/CreativeController';
import IconFont from '@/components/IconFont';
import ImgPreview from '@/components/ImgPreview';
import { UPLOAD_URL } from '@/constants';
import { FileType, getBase64 } from '@/utils/utils';
import { convertPngToJpg } from '@/utils/imageUtils';

interface LoraImageSelectorProps {
  elementId: number;
  value?: Array<string>;
  maxChoose?: number;
  onChange?: (value: Array<string>) => void;
  isOpenScopeAll?: boolean;
  styleImageList?: string[];
  isLoraScene?: boolean;
}

const ElementImagesSelector: React.FC<LoraImageSelectorProps> = ({ elementId, value, maxChoose = 10, onChange, isOpenScopeAll = true, styleImageList = [], isLoraScene = false }) => {
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [showSelector, setShowSelector] = useState(false);
  const [allImages, setAllImages] = useState<string[]>([]);
  const [allImagesExtMap, setAllImagesExtMap] = useState<any>({});
  const [showImage, setShowImage] = useState('');
  const [showImageList, setShowImageList] = useState<Array<string>>([]);
  const [previewImage, setPreviewImage] = useState<null | string>(null);

  const [fileList, setFileList] = useState<UploadFile[]>([]);

  // 添加分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    //清空数据
    clear();

    if (Array.isArray(value)) {
      setSelectedImages(value);
      // 兼容json array string
    } else if (typeof value === 'string') {
      setSelectedImages(JSON.parse(value));
    } else {
      setSelectedImages([]);
    }

  }, []);

  useEffect(() => {
    if (!showSelector) {
      return;
    }

    if (!isOpenScopeAll) {
      // 非全平台开放，直接使用 styleImageList
      setAllImages(styleImageList);
      setTotal(styleImageList.length);
      setShowSelector(true);
    } else {
      // 如果是全平台开放，走原有逻辑
      queryImagesByElementWithPage({
        elementId: elementId,
        bizTag: isLoraScene ? 'poseSampleDiagram' : null,
        pageNum: currentPage,
        pageSize: pageSize
      }).then(res => {
        if (res) {
          setAllImages(res.list || []);
          setTotal(res.totalCount || 0);
          setShowSelector(true);
        } else {
          setAllImages([]);
          setTotal(0);
        }
      });
    }
  }, [showSelector, isOpenScopeAll, styleImageList, currentPage, pageSize]);

  // 处理分页变化
  const handlePageChange = (page: number, pageSize?: number) => {
    setCurrentPage(page);
    if (pageSize) {
      setPageSize(pageSize);
    }
  };

  //清空数据
  const clear = () => {
    setSelectedImages([]);
    setAllImages([]);
    setFileList([]);
    setAllImagesExtMap({});
  };

  const addImage = (image: string) => {
    if ((selectedImages.length + fileList.length) >= maxChoose) {
      message.warning(`最多能选择${maxChoose}张图片`);
      return;
    }

    const newValue = [...selectedImages, image];
    setSelectedImages(newValue);

    onChange?.([...newValue, ...fileList.map(e => e.response.data)]);
  };

  const delImage = (image: string) => {
    const newValue = selectedImages.filter(e => e !== image);
    setSelectedImages(newValue);

    onChange?.([...newValue, ...fileList.map(e => e.response.data)]);
  };

  let isWarning = false;
  const handleFileChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    const file = newFileList[0];

    console.log('success:', file);

    if (file && file.response && !file.response.success) {
      console.log('event:', file);
      notification.error({ message: '上传文件异常，请重试' });
      setFileList([]);
      return;
    }

    if (selectedImages.length + newFileList.length > maxChoose) {
      if (!isWarning) {
        message.error('最多能选择' + maxChoose + '张图片');
        isWarning = true;

        setTimeout(() => isWarning = false, 300);
      }
      return;
    }

    setFileList(newFileList);

    console.log('fileList:', newFileList);
    onChange?.([...selectedImages, ...newFileList.filter(e => e.response !== null).map(e => e.response.data)]);
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType);
    }

    setShowImage(file.url || (file.preview as string));
    setShowImageList([]);
  };

  const ImageCard = ({ image, width, isAll = false }) => {
    return (<>
      <div>
        <img src={image} width={width} alt={''} onClick={() => {
          setShowImage(image);
          setShowImageList(isAll ? allImages : selectedImages);
        }} style={{ cursor: 'pointer' }} />
      </div>
    </>);
  };

  return (<>
    <Flex gap={8} wrap={'wrap'} align={'center'}>
      <Input hidden value={selectedImages} />
      <>
        {selectedImages && selectedImages?.length > 0 && selectedImages?.map((image, index) => (
          <ImageCard key={index} image={image} width={80} />
        ))}

        {selectedImages && (selectedImages.length + fileList.length) < maxChoose &&
          <Button size={'small'} style={{ width: 100, height: 100 }} type="dashed"
            onClick={() => setShowSelector(true)}
            block icon={<PlusOutlined />}>从历史选择</Button>
        }

        {selectedImages && (selectedImages.length + fileList.length) === maxChoose &&
          <Button size={'small'} style={{ width: 100, height: 100 }} type="dashed" onClick={() => setShowSelector(true)}
            block icon={<PlusOutlined />}>变更</Button>
        }

        <Upload
          action={UPLOAD_URL}
          listType="picture-card"
          multiple={true}
          fileList={fileList}
          onPreview={handlePreview}
          onChange={handleFileChange}
          beforeUpload={convertPngToJpg}
          accept={'.jpg, .jpeg, .png'}
        >
          {selectedImages && (selectedImages.length + fileList.length) >= maxChoose ? null :
            <div><UploadOutlined />上传图片<br />
              <span className={'text12 color-a0'}>支持jpg、jpeg和png格式</span>
            </div>}
        </Upload>

      </>
    </Flex>

    <Modal open={showSelector}
      onCancel={() => setShowSelector(false)}
      onOk={() => {
        if (onChange) {
          onChange(selectedImages || []);
        }
        setShowSelector(false);
      }}
      width={'90%'} centered
      styles={{
        body: { padding: '16px', height: 'calc(90vh)', display: 'flex', flexDirection: 'column' }
      }}
      footer={[
        <Button key="cancel" onClick={() => setShowSelector(false)}>
          取消
        </Button>,
        <Button key="confirm" type="primary" onClick={() => {
          if (onChange) {
            onChange(selectedImages || []);
          }
          setShowSelector(false);
        }}>
          确认
        </Button>,
      ]}
    >
      {/* 顶部固定区域 */}
      <div style={{ marginBottom: '16px' }}>
        <div className={'text16 weight'}>已选择: {selectedImages?.length}/{maxChoose}</div>

        <Card style={{ background: '#F5F5F5', marginTop: '12px' }} styles={{ body: { padding: 0 } }}>
          <Flex gap={8} style={{ position: 'relative', width: '100%' }} wrap={'wrap'}>
            <>
              {selectedImages && selectedImages.length > 0 && selectedImages.map((image, index) => (
                <Flex vertical key={index} align={'center'} gap={4}
                  style={{
                    border: '1px dashed #e8e8e8',
                    borderRadius: 8,
                    padding: 4,
                    position: 'relative',
                    height: 'auto',
                  }}>
                  <ImageCard key={index} image={image} width={80} />
                  <Button size={'small'} onClick={() => delImage(image)} block icon={<MinusOutlined />}>删除</Button>
                  <div style={{ position: 'absolute', right: 8, top: 8 }}>
                    <IconFont type={'icon-gou-lan'} style={{ fontSize: 16, color: '#366EF4' }} />
                  </div>
                </Flex>
              ))}

              {selectedImages && (selectedImages.length <= 0) &&
                <Flex vertical gap={8} align={'center'} justify={'center'} className={'width-100'}
                  style={{ padding: 16 }}>
                  <IconFont type={'icon-kongzhuangtai'} style={{ fontSize: 32, color: '#000000' }} />
                  <div className={'text16 font-pf color-b'}>未选择</div>
                </Flex>
              }
            </>
          </Flex>
        </Card>
      </div>

      {/* 滚动区域 */}
      <div style={{ flex: 1, overflowY: 'auto', marginBottom: '16px' }}>
        <div className={'text16 weight'} style={{ marginBottom: '12px' }}>请选择:</div>

        <Flex gap={8} wrap>
          {allImages.length > 0 && allImages.map((image, index) => (
            <Flex vertical key={index} align={'center'} gap={4}
              style={{
                boxShadow: previewImage === image ? '0 0 0 2px #366EF4' : 'none',
                border: '1px dashed #e8e8e8',
                borderRadius: 8,
                padding: 4,
                position: 'relative',
                width: 'calc(120px + 8px)', // 图片宽度 + padding
                flexShrink: 0, // 防止flex布局下的压缩
              }}>
              <ImageCard image={image} width={120} isAll={true} />

              {!selectedImages?.includes(image) &&
                <Button size={'small'} onClick={() => addImage(image)} block icon={<PlusOutlined />}>添加</Button>
              }

              {selectedImages?.includes(image) &&
                <Button size={'small'} onClick={() => delImage(image)} block icon={<MinusOutlined />}>删除</Button>
              }

              {selectedImages?.includes(image) &&
                <div style={{ position: 'absolute', right: 8, top: 8 }}>
                  <IconFont type={'icon-gou-lan'} style={{ fontSize: 24, color: '#366EF4' }} />
                </div>
              }
            </Flex>
          ))}
        </Flex>
      </div>

      {/* 分页区域 */}
      {isOpenScopeAll && (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '8px',
          padding: '16px 0',
          borderTop: '1px solid #f0f0f0'
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            color: '#666',
            fontSize: '14px'
          }}>
            <span>共 <span style={{ color: '#1890ff', fontWeight: 500 }}>{total}</span> 个批次，展示图片数量不固定，与具体批次有关</span>
            <span>当前为第 <span style={{ color: '#1890ff', fontWeight: 500 }}>{(currentPage - 1) * pageSize + 1}</span> ～ <span style={{ color: '#1890ff', fontWeight: 500 }}>{Math.min(currentPage * pageSize, total)}</span> 批次</span>
          </div>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            onChange={handlePageChange}
            showSizeChanger
            pageSizeOptions={[5, 10, 20, 50]}
            showQuickJumper
            style={{ textAlign: 'center' }}
          />
        </div>
      )}
    </Modal>

    <ImgPreview
      previewVisible={!!showImage}
      handleCancelPreview={(previewImage) => {
        setShowImage('');
        setShowImageList([]);
        setPreviewImage(previewImage);
      }}
      previewImage={showImage}
      needSwitch={!!showImageList}
      previewIdx={showImageList.findIndex(item => item === showImage)}
      previewImgs={showImageList}
      showTools={true}
      imageClick={imageUrl => {
        selectedImages.includes(imageUrl) ? delImage(imageUrl) : addImage(imageUrl);
      }}
      isEnableGou={(previewImage) => selectedImages.includes(previewImage)}
      isBlueGou={true}
      selectedImages={selectedImages}
      modelId={elementId}
      imagesExtMap={allImagesExtMap}
    />

  </>);
};

export default ElementImagesSelector;