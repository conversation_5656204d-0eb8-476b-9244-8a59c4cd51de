import React, { FC, useEffect, useState } from 'react';
import { COMPARISON_PARAMS, ComparisonParam } from '@/services/TestController';
import { Checkbox, Flex, Modal } from 'antd';

export interface ComparisonParamsBlockProps {
  value: ComparisonParam | null;
  attrName: any;
  form: any;
}

const ComparisonParamsBlock: FC<ComparisonParamsBlockProps> = ({ value, attrName, form }) => {
  const [data, setData] = useState<Array<ComparisonParam>>([]);
  const [showModal, setShowModal] = useState(false);
  const [current, setCurrent] = useState<ComparisonParam | null>(null);

  useEffect(() => {
    const list = COMPARISON_PARAMS;
    setData(list);
    if (value) {
      const find = list.find(e => e.key === value.key);
      if (find) {
        handleChange(true, find);
      }
    } else if (list.length === 1) {
      handleChange(true, list[0]);
    }
  }, []);

  const handleChange = (checked: boolean, item: ComparisonParam) => {
    setCurrent(checked ? item : null);
    let value = null;
    if (checked) {
      // @ts-ignore
      value = [{ key: item.key, value: item.options[0] }, { key: item.key, value: item.options[1] }];
    }
    form.setFieldValue(attrName, value);
  };

  return <>
    <Flex align={'center'} justify={'flex-start'} wrap={'wrap'} gap={4}>
      <Flex gap={4}>
        {data.map(item => <Checkbox checked={!!current} key={item.key}
                                    onChange={e => handleChange(e.target.checked, item)}>{item.title}</Checkbox>)}
      </Flex>

      {current &&
        <Flex gap={4}>
          <Flex gap={4}>
            {current.options &&
              <>
                <div className={'color-error'}>实验组：{current.options[0]}</div>
                <div className={'color-brand'}>对照组：{current.options[1]}</div>
              </>
            }
          </Flex>
        </Flex>
      }

    </Flex>

    {showModal &&
      <Modal open={true} onCancel={() => setShowModal(false)}>

      </Modal>
    }
  </>;
};

export default ComparisonParamsBlock;