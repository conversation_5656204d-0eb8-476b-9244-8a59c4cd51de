import { Flex, Modal, Spin, Tabs } from 'antd';
import React, { useEffect, useState, useMemo } from 'react';
import { analysisRatio, extInfoAnalysisRatio } from '@/services/TestController';
import { TEST_PLAN } from '@/constants';
import { message } from 'antd';
import type { TestPlan } from '@/services/TestController';
import AnalysisPieChart from './echart/AnalysisPieChart';
import AnalysisBarChart from './echart/AnalysisBarChart';
import AnalysisStackBarChart from './echart/AnalysisStackBarChart';
import { BadCaseData, SimilarityData, BadCaseResponse, SimilarityResponse } from '@/types/analysis';
import { emptyAnalysis } from '@/utils/analysisUtils';
import './styles/AnalysisModal.less';

// 组件属性
interface Props {
  item: TestPlan;
  onCancel: () => void;
}

// 子实验项图表组件属性
interface ExperimentItemChartsProps {
  badCaseData?: BadCaseData;
  similarityData?: SimilarityData;
  defaultEmptyAnalysis: any;
}

// 子实验项图表组件
function ExperimentItemCharts({ badCaseData, similarityData, defaultEmptyAnalysis }: ExperimentItemChartsProps) {
  // 检查是否有 BadCase 数据
  const hasBadCaseData = useMemo(() => {
    if (!badCaseData?.experimentalGroupMap || !badCaseData?.controlGroupMap) {
      return false;
    }
    // 检查是否有任何非零数据
    return Object.values(badCaseData.experimentalGroupMap).some(item => item.count > 0) ||
           Object.values(badCaseData.controlGroupMap).some(item => item.count > 0);
  }, [badCaseData]);

  // 确保数据存在，即使在初始状态下也使用默认空数据
  const experimentalGroupMap = badCaseData?.experimentalGroupMap ?? defaultEmptyAnalysis.experimentalGroupMap;
  const controlGroupMap = badCaseData?.controlGroupMap ?? defaultEmptyAnalysis.controlGroupMap;
  
  // 确保图表有唯一的key，避免重用导致的渲染问题
  const chartKey = useMemo(() => Math.random().toString(36).substring(7), []);

  
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* BadCase 分析区域 */}
      <div className="analysis-section">
        <div className="section-title">BadCase 分析</div>
        <div className="charts-container">
          {/* BadCase 柱状图 */}
          <div style={{ flex: 1, height: '400px' }}>
            <AnalysisBarChart
              key={`bar-chart-${chartKey}`}
              experimentalGroupMap={experimentalGroupMap}
              controlGroupMap={controlGroupMap}
            />
          </div>
          {/* BadCase 饼图 */}
          <div className="pie-chart-container">
            <AnalysisPieChart
              key={`pie-chart-${chartKey}`}
              experimentalGroupMap={experimentalGroupMap}
              controlGroupMap={controlGroupMap}
            />
          </div>
        </div>
      </div>

      {/* 相似度分析区域 */}
      <div className="analysis-section">
        <div className="section-title">相似度分析</div>
        <div style={{ width: '100%', height: '400px' }}>
          <AnalysisStackBarChart
            key={`similarity-chart-${chartKey}`}
            experimentalGroupMap={similarityData?.experimentalGroupMap || {}}
            controlGroupMap={similarityData?.controlGroupMap || {}}
          />
        </div>
      </div>
    </div>
  );
}

// 分析模态框
const AnalysisModal: React.FC<Props> = ({ item, onCancel }) => {

  // 加载状态 
  const [loading, setLoading] = useState(false);
  // BadCase 数据
  const [badCaseData, setBadCaseData] = useState<BadCaseResponse>();
  // 相似度分析结果
  const [similarityData, setSimilarityData] = useState<SimilarityResponse>();
  // 异常类型列表
  const [abnormalTypes, setAbnormalTypes] = useState<string[]>([]);

  // 获取所有异常类型
  const getAbnormalTypes = (data: BadCaseResponse): string[] => {
    if (!data?.resultAnalysisRatioVO?.experimentalGroupMap) return [];
    return Object.keys(data.resultAnalysisRatioVO.experimentalGroupMap);
  };

  useEffect(() => {
    // 获取数据
    const fetchData = async () => {
      if (!item.id) return;
      setLoading(true);

      try {
        // 获取BadCase分析结果
        const response = await analysisRatio(item.id, TEST_PLAN);
        if (response && typeof response === 'object') {
          setBadCaseData(response as BadCaseResponse);
          setAbnormalTypes(getAbnormalTypes(response as BadCaseResponse));
        }

        // 获取相似度分析结果
        const res = await extInfoAnalysisRatio(item.id);
        if (res && typeof res === 'object') {
          setSimilarityData(res as SimilarityResponse);
        }

      } catch (error) {
        message.error('获取分析结果失败');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [item.id]);

  // 使用动态的空数据
  const defaultEmptyAnalysis = useMemo(() => emptyAnalysis(abnormalTypes), [abnormalTypes]);

  return (
    <Modal
      title={
        <div className="modal-title">
          数据分析
          {item.status === 'COMPARING' && (
            <span className="comparing-status">
              数据正在进行统计...
            </span>
          )}
        </div>
      }
      open={true}
      onCancel={onCancel}
      footer={null}
      width={'99%'}
      centered
      className="detail-modal"
      styles={{
        body: {
          maxHeight: 'calc(100vh - 200px)',
          overflowY: 'auto',
          padding: '24px',
          background: '#f8f9fa',
        }
      }}
    >
      <div className="detail-modal-content">
        {/* 基本信息 */}
        <div className="detail-section">
          <div className="detail-section-title">
            <div className="title-indicator" />
            基本信息
          </div>
          <Flex vertical gap={16}>
            <Flex gap={40}>
              <div className="detail-item">
                <div className="detail-label">计划名称</div>
                <div className="detail-value">{item.name}</div>
              </div>
              <div className="detail-item">
                <div className="detail-label">计划状态</div>
                <div className="detail-value">{item.statusName}</div>
              </div>
            </Flex>
          </Flex>
        </div>

        {/* 汇总分析 */}
        <div className="detail-section">
          <div className="detail-section-title">
            <div className="title-indicator" />
            测试计划汇总分析（整体汇总）
          </div>
          <Spin spinning={loading}>
            {/* 汇总分析 */}
            <ExperimentItemCharts
              badCaseData={badCaseData?.resultAnalysisRatioVO}
              similarityData={similarityData?.resultAnalysisRatioVO}
              defaultEmptyAnalysis={defaultEmptyAnalysis}
            />
          </Spin>
        </div>

        {/* 子实验项分析 */}
        <div className="detail-section">
          <div className="detail-section-title">
            <div className="title-indicator" />
            实验项具体分析（单个分析）
          </div>
          <Spin spinning={loading}>
            <Tabs
              type="card"
              items={(item.items || []).map((experimentItem) => {
                // 获取子实验项的 BadCase 数据
                const itemBadCaseData = badCaseData?.childAnalysisRatioVOList?.find(
                  child => child.id === experimentItem.id
                );
                
                // 获取子实验项的相似度数据
                const itemSimilarityData = similarityData?.childAnalysisRatioMap?.[experimentItem.id?.toString()];

                return {
                  key: experimentItem.id.toString(),
                  label: experimentItem.name || `子实验项 ${experimentItem.id}`,
                  children: (
                    <div style={{ background: '#fff', borderRadius: '8px', padding: '16px' }}>
                      <ExperimentItemCharts
                        key={`experiment-item-${experimentItem.id}`}
                        badCaseData={itemBadCaseData}
                        similarityData={itemSimilarityData}
                        defaultEmptyAnalysis={defaultEmptyAnalysis}
                      />
                    </div>
                  ),
                };
              })}
            />
          </Spin>
        </div>
      </div>
    </Modal>
  );
};

export default AnalysisModal; 