import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

// 类型定义
interface BadCaseData {
  totalCount: number;
  count: number;
  ratio: number;
}

// 图表数据项
interface ChartDataItem {
  name: string;
  value: number;
  ratio: number;
  totalCount: number;
}

// 组件属性
interface Props {
  experimentalGroupMap: Record<string, BadCaseData>;
  controlGroupMap: Record<string, BadCaseData>;
}

// 图表常量配置
const CHART_COLORS = [
  '#5470c6',
  '#91cc75',
  '#fac858',
  '#ee6666',
  '#73c0de',
  '#3ba272',
  '#fc8452',
];

// 图表样式
const CHART_STYLE = {
  height: '450px',
  width: '100%',
  background: '#fff',
  borderRadius: '8px',
  padding: '24px',
};

// 默认数据结构 - 用于处理空数据情况
const DEFAULT_DATA = {
  totalCount: 0,
  count: 0,
  ratio: 0,
};

// 饼图组件
const AnalysisPieChart: React.FC<Props> = ({ 
  // 设置默认空对象，避免解构时出错
  experimentalGroupMap = {}, 
  controlGroupMap = {} 
}) => {
  // 图表引用
  const chartRef = useRef<HTMLDivElement>(null);
  // 图表实例
  const chartInstance = useRef<echarts.ECharts>();

  // 数据处理函数
  const processGroupData = (groupData: Record<string, BadCaseData>): ChartDataItem[] => {
    return Object.entries(groupData)
      // 过滤掉 count 为 0 的数据
      .filter(([_, value = DEFAULT_DATA]) => value.count > 0)
      // 将数据转换为图表数据项
      .map(([name, value = DEFAULT_DATA]) => ({
        name,
        value: value.count,
        ratio: value.ratio,
        totalCount: value.totalCount,
      }));
  };

  // 获取饼图系列配置
  const getPieSeriesConfig = (data: ChartDataItem[], center: [string, string]) => ({
    // 饼图类型
    type: 'pie',
    // 饼图半径
    radius: ['30%', '45%'],
    // 饼图中心
    center,
    // 避免标签重叠
    avoidLabelOverlap: true,
    // 饼图样式
    itemStyle: {
      borderRadius: 4,
      borderColor: '#fff',
      borderWidth: 2,
    },
    // 标签配置
    label: {
      // 显示标签
      show: true,
      // 标签格式化 - 处理空数据显示
      formatter: (params: any) => {
        if (params.value === 0) {
          return '暂无数据';
        }
        return `${params.name}: ${params.value}个\n${params.percent}%`;
      },
    },
    // 强调样式配置
    emphasis: {
      label: {
        show: true,
        fontSize: '14',
        fontWeight: 'bold'
      }
    },
    // 数据
    data,
  });

  // 初始化图表
  useEffect(() => {
    // 初始化图表
    if (!chartRef.current) return;
    // 初始化图表实例
    chartInstance.current = echarts.init(chartRef.current);
    // 销毁图表实例
    return () => chartInstance.current?.dispose();
  }, []);

  // 监听窗口大小变化
  useEffect(() => {
    // 处理窗口大小变化
    const handleResize = () => chartInstance.current?.resize();
    window.addEventListener('resize', handleResize);

    // 销毁监听
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 更新图表数据
  useEffect(() => {
    // 确保图表实例存在
    if (!chartInstance.current) return;

    // 处理实验组数据
    const experimentalPieData = processGroupData(experimentalGroupMap);
    // 处理对照组数据
    const controlPieData = processGroupData(controlGroupMap);

    // 处理实验组总数
    const experimentalTotal = experimentalPieData.reduce((sum, item) => sum + item.value, 0);
    // 处理对照组总数
    const controlTotal = controlPieData.reduce((sum, item) => sum + item.value, 0);

    // 图表配置
    const pieOption = {
      // 标题
      title: [
        {
          text: 'BadCase 占比分析',
          left: 'center',
          top: 0,
          textStyle: { fontSize: 18, fontWeight: 600 }
        },
        {
          // 实验组标题 - 添加空数据处理
          text: `实验组${experimentalTotal ? `（${experimentalTotal}个）` : '（暂无数据）'}`,
          left: '25%',
          top: 40,
          textAlign: 'center',
          textStyle: { fontSize: 16, fontWeight: 'bold' }
        },
        {
          // 对照组标题 - 添加空数据处理
          text: `对照组${controlTotal ? `（${controlTotal}个）` : '（暂无数据）'}`,
          left: '75%',
          top: 40,
          textAlign: 'center',
          textStyle: { fontSize: 16, fontWeight: 'bold' }
        }
      ],
      // 提示框
      tooltip: {
        trigger: 'item',
        // 提示框格式化 - 添加空数据处理
        formatter: (params: any) => {
          const { name, value, data } = params;
          if (value === 0) {
            return `${name}: 暂无数据`;
          }
          return `异常：${name}<br/>本组数量：${value}个<br/>异常总数：${data.totalCount}个<br/>本组异常标签占比：${data.ratio}%`;
        },
      },
      // 图例
      legend: [{
        // 图例方向
        orient: 'horizontal',
        // 图例位置
        bottom: 10,
        // 图例居中
        left: 'center',
        // 图例格式化
        formatter: (name: string) => {
          if (name === '暂无数据') {
            return '';
          }
          const experimentalItem = experimentalPieData.find(d => d.name === name);
          const controlItem = controlPieData.find(d => d.name === name);
          return `${name}:\n实验组: ${experimentalItem?.value || 0}个/${experimentalItem?.totalCount || 0}个 (${experimentalItem?.ratio || 0}%)\n对照组: ${controlItem?.value || 0}个/${controlItem?.totalCount || 0}个 (${controlItem?.ratio || 0}%)`;
        },
        textStyle: { lineHeight: 20 },
        type: 'scroll',
        pageButtonPosition: 'end',
      }],
      // 系列
      series: [
        { 
          name: '实验组', 
          // 当数据为空时显示"暂无数据"
          ...getPieSeriesConfig(experimentalPieData.length ? experimentalPieData : [{ name: '暂无数据', value: 1, ratio: 0, totalCount: 0 }], ['25%', '45%']),
          label: {
            ...getPieSeriesConfig([], []).label,
            formatter: (params: any) => {
              if (params.name === '暂无数据') {
                return '暂无数据';
              }
              return `${params.name}: ${params.value}个\n${params.percent}%`;
            }
          }
        },
        { 
          name: '对照组', 
          // 当数据为空时显示"暂无数据"
          ...getPieSeriesConfig(controlPieData.length ? controlPieData : [{ name: '暂无数据', value: 1, ratio: 0, totalCount: 0 }], ['75%', '45%']),
          label: {
            ...getPieSeriesConfig([], []).label,
            formatter: (params: any) => {
              if (params.name === '暂无数据') {
                return '暂无数据';
              }
              return `${params.name}: ${params.value}个\n${params.percent}%`;
            }
          }
        }
      ],
      // 颜色
      color: CHART_COLORS,
    };

    // 设置图表数据
    chartInstance.current.setOption(pieOption);
  }, [experimentalGroupMap, controlGroupMap]);

  // 返回图表
  return <div ref={chartRef} style={CHART_STYLE} />;
};

export default AnalysisPieChart; 