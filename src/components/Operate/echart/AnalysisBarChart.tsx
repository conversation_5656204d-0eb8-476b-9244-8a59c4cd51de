import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

// 类型定义
interface BadCaseData {
  totalCount: number;
  count: number;
  ratio: number;
}

// 图表数据项
interface ChartDataItem {
  name: string;
  value: number;
  ratio: number;
  totalCount: number;
}

// 组件属性
interface Props {
  experimentalGroupMap: Record<string, BadCaseData>;
  controlGroupMap: Record<string, BadCaseData>;
}

// 图表常量配置
const CHART_COLORS = [
  '#91cc75',
  '#5470c6',
];

// 图表样式
const CHART_STYLE = {
  height: '100%',
  width: '100%',
};

const AnalysisBarChart: React.FC<Props> = ({ experimentalGroupMap, controlGroupMap }) => {
  // 图表引用
  const chartRef = useRef<HTMLDivElement>(null);
  // 图表实例
  const chartInstance = useRef<echarts.ECharts>();

  // 数据处理函数
  const processGroupData = (groupData: Record<string, BadCaseData>): ChartDataItem[] => {
    return Object.entries(groupData)
      // 移除过滤条件，显示所有数据
      .map(([name, value]) => ({
        name,
        value: value.count,
        ratio: value.ratio,
        totalCount: value.totalCount,
      }));
  };

  // 初始化图表
  useEffect(() => {
    // 确保 DOM 元素已经准备好
    if (!chartRef.current) return;

    // 初始化新实例
    chartInstance.current = echarts.init(chartRef.current);

    return () => {
      // 销毁实例
      chartInstance.current?.dispose();
    };
  }, []);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      // 确保实例存在
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 更新图表数据
  useEffect(() => {
    // 确保数据和实例都存在
    if (!experimentalGroupMap || !controlGroupMap || !chartInstance.current) return;

    const experimentalData = processGroupData(experimentalGroupMap);
    const controlData = processGroupData(controlGroupMap);

    // 获取所有类别名称
    const categories = Array.from(
      new Set([
        ...Object.keys(experimentalGroupMap),
        ...Object.keys(controlGroupMap),
      ])
    );

    const barOption = {
      title: {
        text: 'BadCase 数量对比',
        left: 'center',
        top: 0,
        textStyle: { fontSize: 18, fontWeight: 600 }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        formatter: (params: any[]) => {
          const [experimental, control] = params;
          return `异常：${experimental.name}<br/>
                  实验组：${experimental.value}个 (${experimental.data.ratio}%)<br/>
                  对照组：${control.value}个 (${control.data.ratio}%)`;
        }
      },
      legend: {
        data: ['实验组', '对照组'],
        bottom: 10,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: categories,
        axisLabel: {
          interval: 0,
          rotate: 30
        }
      },
      yAxis: {
        type: 'value',
        name: '异常数量'
      },
      series: [
        {
          name: '实验组',
          type: 'bar',
          data: categories.map(category => {
            const item = experimentalData.find(d => d.name === category);
            return item ? { 
              value: item.value,
              ratio: item.ratio,
              totalCount: item.totalCount
            } : { value: 0, ratio: 0, totalCount: 0 };
          })
        },
        {
          name: '对照组',
          type: 'bar',
          data: categories.map(category => {
            const item = controlData.find(d => d.name === category);
            return item ? {
              value: item.value,
              ratio: item.ratio,
              totalCount: item.totalCount
            } : { value: 0, ratio: 0, totalCount: 0 };
          })
        }
      ],
      color: CHART_COLORS,
    };

    chartInstance.current.setOption(barOption);
  }, [experimentalGroupMap, controlGroupMap]);

  return <div ref={chartRef} style={CHART_STYLE} />;
};

export default AnalysisBarChart; 