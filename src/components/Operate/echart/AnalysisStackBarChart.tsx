import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';


// 相似度分析类型
const SimilarityAnalysisEnum = {
  SCENE_SIMILARITY: {
    label: '场景相似度',
    value: 'sceneSimilarity',
  },
  FACE_SIMILARITY: {
    label: '模特相似度',
    value: 'faceSimilarity',
  },
  MATERIAL_SIMILARITY: {
    label: '服饰相似度',
    value: 'materialSimilarity',
  },
} as const;

// 类型定义
interface SimilarityData {
  totalCount: number;
  goodCount: number;
  badCount: number;
  notOperationCount: number;
}

// 生成相似度数据映射类型
type SimilarityDataMap = {
  [K in typeof SimilarityAnalysisEnum[keyof typeof SimilarityAnalysisEnum]['value']]?: SimilarityData;
};

// 组件属性
interface Props {
  experimentalGroupMap?: SimilarityDataMap;
  controlGroupMap?: SimilarityDataMap;
}

// 图表常量配置
const CHART_COLORS = {
  experimental: {
    high: '#87d068',    // 深蓝色
    unrated: '#D9D9D9', // 深灰色
    low: '#E66',     // 红色
  },
  control: {
    high: '#5470C6',    // 浅绿色
    unrated: '#D9D9D9', // 深灰色
    low: '#E66',     // 红色
  },
};

// 获取所有相似度类型
const SIMILARITY_TYPES = Object.values(SimilarityAnalysisEnum);

// 图表样式
const CHART_STYLE = {
  height: '400px',
  width: '100%',
};

// 默认数据结构
const DEFAULT_DATA = Object.values(SimilarityAnalysisEnum).reduce((acc, type) => ({
  ...acc,
  [type.value]: {
    totalCount: 0,
    goodCount: 0,
    badCount: 0,
    notOperationCount: 0,
    count: 0,
    ratio: 0,
  },
}), {});

// 图表组件
const AnalysisStackBarChart: React.FC<Props> = ({
  experimentalGroupMap = DEFAULT_DATA,
  controlGroupMap = DEFAULT_DATA,
}) => {
  // 图表引用
  const chartRef = useRef<HTMLDivElement>(null);
  // 图表实例
  const chartInstance = useRef<echarts.ECharts>();

  // 验证数据有效性
  const validateData = (data: any) => {
    if (!data || typeof data !== 'object') {
      return DEFAULT_DATA;
    }
    return data;
  };

  // 获取数据值的辅助函数
  const getDataValue = (map: any, type: string, field: string) => {
    if (!map || typeof map !== 'object') {
      return 0;
    }
    const typeData = map[type];
    if (!typeData || typeof typeData !== 'object') {
      return 0;
    }
    return typeData[field] ?? 0;
  };

  // 获取相似度类型的值
  const getSimilarityType = (name: string) => {
    const type = SIMILARITY_TYPES.find(type => type.label === name);
    return type?.value || '';
  };

  // 初始化图表
  useEffect(() => {
    // 确保 DOM 元素已经准备好
    if (!chartRef.current) return;

    // 设置一个小延时确保 DOM 完全渲染
    const timer = setTimeout(() => {
      // 如果已经有实例，先销毁
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
      // 初始化新实例
      chartInstance.current = echarts.init(chartRef.current);

      // 立即设置初始数据
      updateChart();
    }, 0);

    return () => {
      // 清理定时器
      clearTimeout(timer);
      // 销毁实例
      chartInstance.current?.dispose();
    };
  }, []);

  // 更新图表数据
  const updateChart = () => {
    if (!chartInstance.current) return;

    // 验证输入数据
    const validatedExperimentalGroupMap = validateData(experimentalGroupMap);
    const validatedControlGroupMap = validateData(controlGroupMap);

    // 获取有数据的相似度类型
    const availableTypes = SIMILARITY_TYPES.filter(type =>
      validatedExperimentalGroupMap?.[type.value] || validatedControlGroupMap?.[type.value],
    );

    // 设置图表选项
    const barOption = {
      // 设置提示框
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#eee',
        borderWidth: 1,
        padding: [12, 16],
        textStyle: {
          color: '#666',
          fontSize: 13,
        },
        // 设置提示框内容
        formatter: function(params) {
          // 获取类别名称（场景相似度/模特相似度/服饰相似度）
          const categoryName = params[0]?.name || '';

          // 分离实验组和对照组数据
          const experimental = params.slice(0, 3);
          const control = params.slice(3);

          // 计算实验组总数
          const experimentalTotal = experimental.reduce((sum, item) => sum + (item?.value || 0), 0);
          // 计算对照组总数
          const controlTotal = control.reduce((sum, item) => sum + (item?.value || 0), 0);

          // 构建提示信息
          let result = `<div style="font-weight: bold; font-size: 14px; color: #333; margin-bottom: 8px;">${categoryName}</div>`;
          result += '<div style="margin-bottom: 6px; border-bottom: 1px solid #eee;"></div>';

          // 添加相似度低的数据
          result += `<div style="margin: 8px 0;">
                <span style="color: ${CHART_COLORS.experimental.unrated};">●</span>  暂未打分：
                <span style="margin-right: 12px;">实验组 ${experimental[2]?.value || 0}个 <span style="color: #999;">(${experimentalTotal ? ((experimental[2]?.value || 0) / experimentalTotal * 100).toFixed(1) : '0.0'}%)</span></span>
                <span style="color: ${CHART_COLORS.control.unrated};">●</span>
                <span style="margin-left: 12px;">对照组 ${control[2]?.value || 0}个 <span style="color: #999;">(${controlTotal ? ((control[2]?.value || 0) / controlTotal * 100).toFixed(1) : '0.0'}%)</span></span>
            </div>`;

          // 添加未打分的数据
          result += `<div style="margin: 8px 0;">
            <span style="color: ${CHART_COLORS.experimental.low};">●</span>相似度低：
            <span style="margin-right: 12px;">实验组 ${experimental[1]?.value || 0}个 <span style="color: #999;">(${experimentalTotal ? ((experimental[1]?.value || 0) / experimentalTotal * 100).toFixed(1) : '0.0'}%)</span></span>
            <span style="color: ${CHART_COLORS.control.low};">●</span>
            <span style="margin-left: 12px;">对照组 ${control[1]?.value || 0}个 <span style="color: #999;">(${controlTotal ? ((control[1]?.value || 0) / controlTotal * 100).toFixed(1) : '0.0'}%)</span></span>
          </div>`;

          // 添加相似度高的数据
          result += `<div style="margin: 8px 0;">
            <span style="color: ${CHART_COLORS.experimental.high};">●</span> 相似度高：
            <span style="margin-right: 12px;">实验组 ${experimental[0]?.value || 0}个 <span style="color: #999;">(${experimentalTotal ? ((experimental[0]?.value || 0) / experimentalTotal * 100).toFixed(1) : '0.0'}%)</span></span>
            <span style="color: ${CHART_COLORS.control.high};">●</span>
            <span style="margin-left: 12px;">对照组 ${control[0]?.value || 0}个 <span style="color: #999;">(${controlTotal ? ((control[0]?.value || 0) / controlTotal * 100).toFixed(1) : '0.0'}%)</span></span>
          </div>`;

          return result;
        },
      },
      label: {
        show: true,
        formatter: (params) => Math.round(params.value * 1000) / 10 + '%',
      },
      // 设置图例
      legend: {
        data: ['相似度高', '暂未打分', '相似度低'],
        bottom: 10,
        left: 'center',
        textStyle: {
          fontSize: 12,
        },
        itemWidth: 15,
        itemHeight: 10,
      },
      // 设置图表布局
      grid: {
        top: '15%',
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
      // 设置X轴
      xAxis: [{
        type: 'category',
        data: availableTypes.map(type => type.label),
        axisLabel: {
          interval: 0,
          fontSize: 12,
          rotate: 0,
        },
        axisTick: {
          alignWithLabel: true,
        },
      }],
      // 设置Y轴
      yAxis: [{
        type: 'value',
        name: '数量',
        nameTextStyle: {
          fontSize: 12,
        },
        axisLabel: {
          formatter: '{value}个',
          fontSize: 12,
        },
        min: 0,
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
        },
      }],
      // 设置系列
      series: [
        {
          name: '相似度高',
          type: 'bar',
          stack: 'experimental',
          barWidth: '30%',
          emphasis: {
            focus: 'series',
          },
          itemStyle: {
            color: CHART_COLORS.experimental.high,
          },
          label: {
            show: true,
            position: 'inside',
            formatter: (params) => {
              const total = getDataValue(validatedExperimentalGroupMap, getSimilarityType(params.name), 'totalCount');
              return total ? `${params.value}个（${Math.round((params.value / total) * 1000) / 10}%）` : '0个';
            },
          },
          data: availableTypes.map(type => getDataValue(validatedExperimentalGroupMap, type.value, 'goodCount')),
        },
        {
          name: '相似度低',
          type: 'bar',
          stack: 'experimental',
          barWidth: '30%',
          emphasis: {
            focus: 'series',
          },
          itemStyle: {
            color: CHART_COLORS.experimental.low,
          },
          label: {
            show: true,
            position: 'inside',
            formatter: (params) => {
              const total = getDataValue(validatedExperimentalGroupMap, getSimilarityType(params.name), 'totalCount');
              return total ? `${params.value}个（${Math.round((params.value / total) * 1000) / 10}%）` : '0个';
            },
          },
          data: availableTypes.map(type => getDataValue(validatedExperimentalGroupMap, type.value, 'badCount')),
        },
        {
          name: '暂未打分',
          type: 'bar',
          stack: 'experimental',
          barWidth: '30%',
          emphasis: {
            focus: 'series',
          },
          itemStyle: {
            color: CHART_COLORS.experimental.unrated,
          },
          label: {
            show: true,
            position: 'inside',
            formatter: (params) => {
              const total = getDataValue(validatedExperimentalGroupMap, getSimilarityType(params.name), 'totalCount');
              return total ? `${params.value}个（${Math.round((params.value / total) * 1000) / 10}%）` : '0个';
            },
          },
          data: availableTypes.map(type => getDataValue(validatedExperimentalGroupMap, type.value, 'notOperationCount')),
        },
        {
          name: '相似度高',
          type: 'bar',
          stack: 'control',
          barWidth: '30%',
          emphasis: {
            focus: 'series',
          },
          itemStyle: {
            color: CHART_COLORS.control.high,
          },
          label: {
            show: true,
            position: 'inside',
            formatter: (params) => {
              const total = getDataValue(validatedControlGroupMap, getSimilarityType(params.name), 'totalCount');
              return total ? `${params.value}个（${Math.round((params.value / total) * 1000) / 10}%）` : '0个';
            },
          },
          data: availableTypes.map(type => getDataValue(validatedControlGroupMap, type.value, 'goodCount')),
        },
        {
          name: '相似度低',
          type: 'bar',
          stack: 'control',
          barWidth: '30%',
          emphasis: {
            focus: 'series',
          },
          itemStyle: {
            color: CHART_COLORS.control.low,
          },
          label: {
            show: true,
            position: 'inside',
            formatter: (params) => {
              const total = getDataValue(validatedControlGroupMap, getSimilarityType(params.name), 'totalCount');
              return total ? `${params.value}个（${Math.round((params.value / total) * 1000) / 10}%）` : '0个';
            },
          },
          data: availableTypes.map(type => getDataValue(validatedControlGroupMap, type.value, 'badCount')),
        },
        {
          name: '暂未打分',
          type: 'bar',
          stack: 'control',
          barWidth: '30%',
          emphasis: {
            focus: 'series',
          },
          itemStyle: {
            color: CHART_COLORS.control.unrated,
          },
          label: {
            show: true,
            position: 'inside',
            formatter: (params) => {
              const total = getDataValue(validatedControlGroupMap, getSimilarityType(params.name), 'totalCount');
              return total ? `${params.value}个（${Math.round((params.value / total) * 1000) / 10}%）` : '0个';
            },
          },
          data: availableTypes.map(type => getDataValue(validatedControlGroupMap, type.value, 'notOperationCount')),
        },
      ],
    };

    // 设置图表选项
    chartInstance.current.setOption(barOption);
  };

  // 监听数据变化
  useEffect(() => {
    updateChart();
  }, [experimentalGroupMap, controlGroupMap]);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      chartInstance.current?.resize();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 返回图表组件
  return <div ref={chartRef} style={CHART_STYLE} />;
};

export default AnalysisStackBarChart; 