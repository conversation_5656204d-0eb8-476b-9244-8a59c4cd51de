import React, { FC, useEffect, useState } from 'react';
import { Button, Form, message, Modal, Tooltip } from 'antd';
import {
  ExperienceModelOpenDetail,
  modifyExperienceModelOpenCfg,
  queryExperienceModelOpenCfgById,
} from '@/services/SystemController';
import { ElementSelector } from '@/components/Operate/MerchantPreferenceSetting';
import { ElementConfig } from '@/services/ElementController';

interface SettingExperienceOpenCfgProps {
  id: number;
  title: string;
  faceList: Array<ElementConfig>;
  sceneList: Array<ElementConfig>;
}

const ExperienceOpenCfgSetting: FC<SettingExperienceOpenCfgProps> = ({ id, title, faceList, sceneList }) => {
  const [data, setData] = useState<ExperienceModelOpenDetail | null>(null);
  const [showModal, setShowModal] = useState(false);

  const [form] = Form.useForm();

  const fetchData = async () => {
    const res = await queryExperienceModelOpenCfgById(id);
    if (res) {
      setData(res);
    }
    return res;
  };

  const handleShowModal = async () => {
    form.resetFields();
    const res = data ? data : await fetchData();
    form.setFieldsValue(res);
    setShowModal(true);
  };

  const handleFinish = async () => {
    const row = (await form.validateFields()) as ExperienceModelOpenDetail;
    const payload = { ...row, id };

    modifyExperienceModelOpenCfg(payload).then(res => {
      if (res) {
        message.success('修改成功');
        setShowModal(false);
        fetchData();
      }
    });
  };

  return <>
    <Tooltip title={'限制能够使用的模特和场景'}>
      <Button className={'operate-btn'} onClick={() => handleShowModal()} size={'small'}>限制</Button>
    </Tooltip>

    {showModal &&
      <Modal open={true} centered title={`设置服装"${title}"的开放限制`}
             onCancel={() => setShowModal(false)} onOk={form.submit} width={800} maskClosable={false}>
        <Form form={form} onFinish={handleFinish}>
          <Form.Item label="限定模特：" name={'faces'} style={{ marginTop: 16 }}>
            <ElementSelector elementList={faceList} />
          </Form.Item>

          <Form.Item label="限定场景：" name={'scenes'}>
            <ElementSelector elementList={sceneList} />
          </Form.Item>
        </Form>
      </Modal>
    }

  </>;
};

export default ExperienceOpenCfgSetting;