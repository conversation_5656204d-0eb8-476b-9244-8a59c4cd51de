import {
  Button,
  Col,
  Flex,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Select,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import React, { useEffect, useState } from 'react';
import {
  createShowCase,
  deleteShowCase,
  queryAllShowCase,
  ShowCaseVO,
  updateShowCase,
} from '@/services/ShowCaseController';
import { ElementConfig, getElementConfig } from '@/services/ElementController';
import { ALL_LORAS } from '@/constants';
import { formatText } from '@/utils/utils';
import ElementWithTypeSelector from '@/components/Creative/ElementWithTypeSelector';
import UploadFormItem from '@/components/Common/UploadFormItem';

interface ShowCaseBlockProps {

}

export const SHOW_CASE_TAGS = ['日韩系', '甜美可爱', '法式文艺', '时尚优雅', '性感御姐', '个性时髦'];
const tagsOptions = SHOW_CASE_TAGS.map(item => ({ label: item, value: item }));

const ShowCaseBlock: React.FC<ShowCaseBlockProps> = ({}) => {
  const [data, setData] = useState<Array<ShowCaseVO>>([]);
  const [loading, setLoading] = useState(false);
  const [opItem, setOpItem] = useState<ShowCaseVO | null>(null);
  const [add, setAdd] = useState(false);
  const [configs, setConfigs] = useState<Array<ElementConfig>>([]);
  const [elementSelectType, setElementSelectType] = useState<string | null>(null);

  const [form] = Form.useForm();

  useEffect(() => {
    init();
  }, []);

  const init = async () => {
    const res = await getElementConfig('CREATE_IMAGE');
    if (res) {
      setConfigs(res);
      fetchData(res);
    }
  };

  const fetchData = (newConfigs: ElementConfig[] | null | undefined = null) => {
    setLoading(true);
    queryAllShowCase().then(res => {
      setLoading(false);
      if (res) {
        res.forEach(item => {
          // @ts-ignore
          item.face = getSelectedElement(item.faceId, 'FACE', newConfigs);
          // @ts-ignore
          item.scene = getSelectedElement(item.sceneId, 'SCENE', newConfigs);
        });

        //根据top进行排序
        res.sort((a, b) => b.topped ? .1 : -1);
        setData(res);
      }
    });
  };

  const handleAdd = () => {
    setOpItem(null);

    form.resetFields();
    setAdd(true);
  };

  const handleEdit = (item) => {
    form.resetFields();
    setAdd(false);

    form.setFieldsValue(item);
    setOpItem(item);
  };

  const closeModal = () => {
    setOpItem(null);
    setAdd(false);
  };

  const handleCommit = async (values) => {
    const method = add ? createShowCase : updateShowCase;
    method(values).then(res => {
      if (res) {
        message.success('保存成功');
        setAdd(false);
        setOpItem(null);
        fetchData();
      }
    });
  };

  const handleDelete = async (id) => {
    deleteShowCase(id).then(res => {
      if (res) {
        message.success('删除成功');
        fetchData();
      }
    });
  };

  const handleSelectElement = (item) => {
    if (elementSelectType === 'FACE') {
      form.setFieldValue('faceId', item.id);
    } else {
      form.setFieldValue('sceneId', item.id);
    }

    setElementSelectType(null);
  };

  const getSelectedElement = (id, configKey, newConfigs = null) => {
    const find = (newConfigs ? newConfigs : configs).find(item => item.configKey === configKey);
    const children = find?.children;
    if (!children) {
      return null;
    }
    return children?.find(item => item.id === id);
  };

  const getTypeTitle = (configKey: string) => {
    return configKey === 'FACE' ? '模特' : '场景';
  };

  const getConfigByType = (configKey: string) => {
    const find = configs.find(item => item.configKey === configKey);
    return find ? find : configs[0];
  };

  const buildMiniImg = (url, title: null | string = null, isVideo = false) => {
    const size = 48;
    return <Flex gap={4} align={'center'}>
      {!isVideo &&
        <>
          <img src={url} alt={'img'} width={size} height={size} style={{ borderRadius: 8 }} />
          {title &&
            <div>{title}</div>
          }
        </>
      }
      {isVideo &&
        <>
          <video width={size} height={'auto'} autoPlay={true} muted loop={true}>
            <source src={url} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </>
      }

    </Flex>;
  };

  const formatClothCollocation = (value) => {
    const getList = (value) => {
      if (!value) return [];
      return value.split(',');
    };

    const list = [...getList(value.shoe), ...getList(value.tops), ...getList(value.bottoms), ...getList(value.others), ...getList(value.props)];
    return <Flex gap={0} wrap={'wrap'}>
      {list.map(item => <Tag key={item}>{item}</Tag>)}
    </Flex>;
  };

  const columns = [
    { title: 'ID', dataIndex: 'id', key: 'id' },
    { title: '名称', dataIndex: 'name', key: 'name' },
    {
      title: '是否置顶',
      dataIndex: 'topped',
      key: 'topped',
      render: topped => (<div className={topped ? 'color-error' : ''}>{topped ? '是' : '否'}</div>),
    },
    { title: '类型', dataIndex: 'typeName', key: 'typeName' },
    {
      title: '结果图',
      dataIndex: 'mainUrl',
      key: 'mainUrl',
      render: (url, record) => (buildMiniImg(url, null, record.type === 'VIDEO')),
    },
    { title: '服装', dataIndex: 'modelUrl', key: 'modelUrl', render: url => (buildMiniImg(url)) },
    {
      title: '模特',
      dataIndex: 'face',
      key: 'faceUrl',
      render: (value: ElementConfig) => (buildMiniImg(value?.showImage, value?.name)),
    },
    {
      title: '场景',
      dataIndex: 'scene',
      key: 'scene',
      render: (value: ElementConfig) => (buildMiniImg(value?.showImage, value?.name)),
    },
    {
      title: '服装搭配',
      dataIndex: 'clothCollocation',
      key: 'clothCollocation',
      render: (value) => (formatClothCollocation(value)),
    },
    {
      title: '默认选中',
      dataIndex: 'defChecked',
      key: 'defChecked',
      render: (checked) => (checked ? '是' : '否'),
    },
    { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
    { title: '修改时间', dataIndex: 'modifyTime', key: 'modifyTime' },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <span>
          <Button type="link" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Popconfirm title={'确定要删除吗？'} onConfirm={() => handleDelete(record.id)}>
            <Button type="link" danger> 删除 </Button>
          </Popconfirm>
        </span>
      ),
    },
  ];

  const ElementImgCard = ({ value, type }) => {
    const size = 64;
    const item = getSelectedElement(value, type);
    const url = item?.showImage || ALL_LORAS;
    const name = item?.name || '请选择';
    return <Tooltip title={'点击可选择'}>
      <div style={{ position: 'relative', width: size, height: size }} className={'pointer'}
           onClick={() => setElementSelectType(type)}>

        <img src={url} alt={'img'} width={size} height={size}
             style={{ borderRadius: 8 }} />


        <Flex align={'flex-end'} justify={'center'} className={'color-w'}
              style={{ position: 'absolute', bottom: 0, width: '100%', height: '100%' }}>
          {formatText(name, 4)}
        </Flex>
      </div>
    </Tooltip>;
  };

  return <div className={'tag-management'} style={{ gap: 8 }}>
    <Flex style={{ marginBottom: 8 }}>
      <Button onClick={handleAdd}>新增</Button>
    </Flex>

    <Table
      columns={columns}
      dataSource={data}
      loading={loading}
      rowKey="id"
      className="tm-table"
    />

    {(add || opItem) &&
      <Modal open={true} onCancel={closeModal} maskClosable={false} centered closeIcon={null}
             onOk={form.submit} width={800}>
        <Form form={form} onFinish={handleCommit} labelCol={{ span: 4 }} wrapperCol={{ span: 22 }}>
          <Form.Item name="id" rules={[{ required: false }]} hidden>
            <Input disabled />
          </Form.Item>
          <Form.Item name="status" rules={[{ required: false }]} hidden>
            <Input disabled />
          </Form.Item>
          <Form.Item name="order" rules={[{ required: false }]} hidden>
            <Input disabled />
          </Form.Item>
          <Form.Item name="showImage" rules={[{ required: false }]} hidden>
            <Input disabled />
          </Form.Item>
          <Form.Item name="modelMiniUrl" rules={[{ required: false }]} hidden>
            <Input disabled />
          </Form.Item>
          <Form.Item name="name" label={'名称'} rules={[{ required: false }]}>
            <Input placeholder={'案例名称，可空，不在前台展示'} />
          </Form.Item>
          <Form.Item name="type" label={'类型'} rules={[{ required: true }]} initialValue={'IMAGE'}>
            <Radio.Group options={[{ label: '图片', value: 'IMAGE' }, { label: '视频', value: 'VIDEO' }]}
                         optionType="button" buttonStyle="solid" />
          </Form.Item>
          <Form.Item name="topped" label={'是否置顶'} rules={[{ required: true }]} initialValue={false}>
            <Radio.Group options={[{ label: '否', value: false }, { label: '是', value: true }]}
                         optionType="button" buttonStyle="solid" />
          </Form.Item>
          <Row style={{ marginTop: -12 }}>
            <Col span={12}>
              <UploadFormItem form={form} name="mainUrl" label={'主图/视频'} labelSpan={8} required={true} />
            </Col>
            <Col span={12}>
              <UploadFormItem form={form} name="modelUrl" label={'服装'} required={true} />
            </Col>
          </Row>

          <Row style={{ marginTop: -12 }}>
            <Col span={12}>
              <Form.Item labelCol={{ span: 8 }} name="faceId" label="模特" rules={[{ required: true }]}>
                <Input hidden />
                <ElementImgCard value={form.getFieldValue('faceId')} type={'FACE'} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="sceneId" label="场景" rules={[{ required: true }]}>
                <Input hidden />
                <ElementImgCard value={form.getFieldValue('sceneId')} type={'SCENE'} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="tags" label={'标签'} rules={[{ required: true }]}>
            <Select mode="tags" placeholder="请选择（可多选）" style={{ width: 626 }}
                    allowClear={true} options={tagsOptions} />
          </Form.Item>

          <Row style={{ marginTop: -12 }}>
            <Col span={12}>
              <Form.Item labelCol={{ span: 8 }} name={['clothCollocation', 'tops']} label="上装">
                <Input placeholder="输入上装(中文)" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name={['clothCollocation', 'bottoms']} label="下装">
                <Input placeholder="输入下装(中文)" />
              </Form.Item>
            </Col>
          </Row>
          <Row style={{ marginTop: -12 }}>
            <Col span={12}>
              <Form.Item labelCol={{ span: 8 }} name={['clothCollocation', 'shoe']} label="鞋子">
                <Input placeholder="输入鞋子(中文)" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name={['clothCollocation', 'others']} label="其它">
                <Input placeholder="输入其他(中文)" />
              </Form.Item>
            </Col>
          </Row>
          <Row style={{ marginTop: -12 }}>
            <Col span={24}>
              <Form.Item name={['clothCollocation', 'props']} label="道具">
                <Input placeholder="输入道具(中文)" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="memo" label="备注">
            <Input.TextArea />
          </Form.Item>
        </Form>
      </Modal>
    }

    {elementSelectType &&
      <ElementWithTypeSelector current={[]}
                               title={getTypeTitle(elementSelectType)}
                               config={getConfigByType(elementSelectType)}
                               onChange={(value) => handleSelectElement(value[0])}
                               onClose={() => setElementSelectType(null)} />
    }

  </div>;
};

export default ShowCaseBlock;