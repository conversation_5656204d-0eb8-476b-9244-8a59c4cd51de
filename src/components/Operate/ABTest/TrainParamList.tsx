import React, { useEffect, useState } from 'react';
import { Row, Col, Flex, Form, Popconfirm, Radio, Select, SelectProps } from 'antd';
import { CloseCircleOutlined } from '@ant-design/icons';
import { ComponentConfig } from './ParamsSelectList';
import { selectTrainCommonParams } from './ParamsSelectList';
import { queryMaterialModelList } from '@/services/MaterialModelController';

// 渲染表单项
const renderFormItem = (
  paramConfig: ComponentConfig,
  index: number,
  paramKey: string,
  trainParam: boolean = false,
  isComparison: boolean = false,
  comparisonIndex?: number,
  label?: string,
  value?: any,
) => {
  const baseProps = {
    label: isComparison ? label : paramConfig.label,
    valuePropName: paramConfig.type === 'checkbox' ? 'checked' : 'value',
    rules: paramConfig.required ? [{ required: true, message: `请设置${paramConfig.label}` }] : undefined,
  };

  // 实验组的默认值
  const defaultExperimentValue = {
    key: Date.now().toString(),
    resolution: '1024',
    contentOrStyle: 'content',
    rank: 32,
    alpha: 16,
    maxTrainStep: 2500,
    lr: 0.00020,
    dropout: 0.20,
  };

  if (isComparison && trainParam) {
    return (
      <Form.Item
        {...baseProps}
        name={[index, 'comparisonParams', comparisonIndex!, 'value']}
        initialValue={comparisonIndex === 0 ? defaultExperimentValue : value}>
        {paramConfig.component}
      </Form.Item>
    );
  }

  return (
    <Form.Item
      {...baseProps}
      name={[index, 'sharedParams', paramConfig.name ? paramConfig.name.join('.') : paramKey]}
      initialValue={paramConfig.defaultValue}>
      {paramConfig.component}
    </Form.Item>
  );
};

// 训练参数
interface TrainParamListProps {
  // index
  index: number;
  // 删除
  onRemove: (index: number, key: string) => void;
  // Form 实例
  form: any;
}

// 训练参数列表
const TrainParamList: React.FC<TrainParamListProps> = ({
  index,
  onRemove,
  form,
}) => {
  // 获取训练参数配置
  const trainParams = selectTrainCommonParams(index, false, { form });
  // 获取当前 trainType 的值
  const trainType = Form.useWatch(['items', index, 'sharedParams', 'trainType'], form);
  // 服装选项
  const [clothingOptions, setClothingOptions] = useState<SelectProps['options']>([]);


  useEffect(() => {
    // 获取服装列表
    queryMaterialModelList({ materialType: 'cloth' }).then(res => {
      if (res) {
        setClothingOptions(res.map(item => {
          return { label: item.name, value: item.id, clothLoraTrainDetail: item.clothLoraTrainDetail };
        }));
      }
    });
  }, []);


  return (
    <Row key={`${index}`}>
      <Col span={24}>
        <div style={{ position: 'relative', background: '#fafafa', borderRadius: '8px', padding: '16px 24px' }}>
          {/* 标题和删除按钮 */}
          <Flex justify="space-between" align="center" style={{ marginBottom: 16 }}>
            <div className="text14 weight color-1a">公共参数</div>
            <Popconfirm
              title="确认删除"
              description="您确定要删除这个训练参数吗？"
              onConfirm={() => onRemove(index, 'trainParam')}
              okText="确定"
              cancelText="取消">
              <CloseCircleOutlined style={{
                fontSize: 16,
                color: '#999',
                cursor: 'pointer',
                transition: 'all 0.3s',
              }} />
            </Popconfirm>
          </Flex>

          <Form.Item
            name={[index, 'sharedParams', 'trainType']}
            initialValue="clone">
            <Radio.Group 
              optionType="button" 
              buttonStyle="solid"
              onChange={(e) => {
                if (e.target.value === 'clone') {
                  // 清空对照组参数
                  form.setFields([{
                    name: ['items', index, 'comparisonParams', 1, 'value'],
                    value: undefined
                  }]);
                }
              }}>
              <Radio value="clone">克隆服装</Radio>
              <Radio value="create">新创建服装</Radio>
            </Radio.Group>
          </Form.Item>

          {/* 公共参数列表 */}
          <Row gutter={[24, 16]}>
            {Object.entries(trainParams).map(([paramKey, paramConfig]) =>
              !paramConfig.isSetComparison && (trainType === 'create' || paramConfig.name?.[0] === 'trainType') && (
                <Col span={paramConfig.colSpan || 6} key={paramKey}>
                  {renderFormItem(paramConfig, index, paramKey)}
                </Col>
              )
            )}

            {/* 当为克隆类型时展示服装选择输入框 */}
            {
              trainType === 'clone' &&
              <Col span={24} key="trainType">
                <Form.Item
                  label="待克隆服装"
                  name={[index, 'sharedParams', 'clothingLoraId']}
                  rules={[{ required: true, message: '请选择待克隆服装' }]}>
                  <Select
                    options={clothingOptions}
                    placeholder="请选择服装"
                    onChange={(value, option: any) => {
                      if (option?.clothLoraTrainDetail) {
                        // 构建训练参数对象
                        const trainParamValue = {
                          key: Date.now().toString(),
                          resolution: option.clothLoraTrainDetail.resolution || '1024',
                          contentOrStyle: option.clothLoraTrainDetail.contentOrStyle || 'content',
                          rank: option.clothLoraTrainDetail.rank || 32,
                          alpha: option.clothLoraTrainDetail.alpha || 16,
                          maxTrainStep: option.clothLoraTrainDetail.maxTrainStep || 2500,
                          lr: option.clothLoraTrainDetail.lr || 0.00020,
                          dropout: option.clothLoraTrainDetail.dropout || 0.20,
                        };
                        
                        // 只更新对照组参数
                        form.setFields([{
                          name: ['items', index, 'comparisonParams', 1, 'value'],
                          value: trainParamValue
                        }]);
                      }
                    }}
                  />
                </Form.Item>
              </Col>
            }
          </Row>

          <div className="text14 weight color-1a">实验参数</div>

          {/* 实验组和对照组 */}
          <Flex align="flex-start" gap={24} style={{ background: '#fff', padding: '16px 24px', borderRadius: '4px', marginTop: 16 }}>
            <Flex vertical style={{ flex: 1 }}>
              <div className="text12 color-72" style={{ marginBottom: 8 }}>实验组</div>
              {Object.entries(trainParams).map(([paramKey, paramConfig]) =>
                paramConfig.isSetComparison && (
                  <Col span={paramConfig.colSpan || 6} key={paramKey}>
                    {renderFormItem(paramConfig, index, paramKey, true, true, 0, paramConfig.label)}
                  </Col>
                )
              )}
            </Flex>

            <div style={{ width: 1, background: '#f0f0f0', margin: '24px 0' }} />

            <Flex vertical style={{ flex: 1, position: 'relative' }}>
              <div className="text12 color-72" style={{ marginBottom: 8 }}>对照组</div>
              {Object.entries(trainParams).map(([paramKey, paramConfig]) =>
                paramConfig.isSetComparison && (
                  <Col span={paramConfig.colSpan || 6} key={paramKey}>
                    {renderFormItem(paramConfig, index, paramKey, true, true, 1, paramConfig.label)}
                  </Col>
                )
              )}
              {/* 当trainType为clone时显示遮罩层 */}
              {trainType === 'clone' && (
                <div
                  style={{
                    position: 'absolute',
                    top: -8,
                    left: -8,
                    right: -8,
                    bottom: -8,
                    background: 'rgba(138, 138, 138, 0.75)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    zIndex: 1,
                    borderRadius: '8px',
                    transition: 'all 0.3s ease',
                  }}>
                  <div style={{ 
                    color: '#FFFFFF', 
                    fontSize: '15px', 
                    fontWeight: 500,
                    letterSpacing: '0.5px',
                  }}>克隆模式下参数自动填充</div>
                </div>
              )}
            </Flex>
          </Flex>
        </div>
      </Col>
    </Row>
  );
};

export default TrainParamList;
