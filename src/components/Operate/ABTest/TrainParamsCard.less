.train-params-card {
  .param-row {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 24px;
    padding: 24px;
    background: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);

    .param-item {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .param-label {
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }

      .resolution-select,
      .content-style-select,
      .rank-input,
      .alpha-input,
      .train-step-input,
      .lr-input,
      .dropout-input {
        width: 100%;
        min-width: 60px;
        max-width: 200px;
      }

      .ant-input-number-handler-wrap {
        opacity: 0;
        transition: opacity 0.2s;
      }

      .ant-input-number:hover .ant-input-number-handler-wrap {
        opacity: 1;
      }
    }
  }
}