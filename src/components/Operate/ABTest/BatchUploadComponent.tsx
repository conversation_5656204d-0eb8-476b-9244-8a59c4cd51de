import React, { useEffect, useState } from 'react';
import { Input, message, Tabs } from 'antd';
import "./less/BatchUploadComponent.less"
import UploadImageList, { UploadItem } from './UploadImageList';
import { compressFile, convertHeicToJpeg } from '@/utils/imageUtils';
import { uploadFile } from '@/services/FileController';

// 定义 TabItem 接口
interface TabItem {
  // 标签页key
  key: string;
  // 标题
  title: string;
}

// 定义 resultUploadItem 接口
interface resultUploadItem {
  // 颜色组
  colorGroupNumber: number;
  // 类型
  viewTags: 'full' | 'upper';
  // 图片地址
  imgUrl?: string;
}

interface materialDetail {
  // 全身图集合
  fullShotImgList: Array<resultUploadItem>;
  // 半身图集合
  detailShotImgList: Array<resultUploadItem>;
}

// 最终请求结果入参
interface ResultParams {
  colorDescriptions?: Array<string>;
  materialDetail?: materialDetail
}

const BatchUploadComponent: React.FC<{
  value?: any;
  onChange?: (value: any) => void;
}> = ({ value, onChange }) => {
  // 上传中
  const [loading, setLoading] = useState(false);
  // 激活的标签页
  const [activeKey, setActiveKey] = useState<string>('1');
  // 标签页
  const [tabs, setTabs] = useState<TabItem[]>([{ key: '1', title: '上传颜色 1' }]);

  // 补充该颜色准确名称 列表
  const [colorDescriptions, setColorDescriptions] = useState<string[]>([]);

  // 最终结果参数
  const [ResultParams, setResultParams] = useState<ResultParams>(value || {
    colorDescriptions: [],
    materialDetail: {
      fullShotImgList: [],
      detailShotImgList: []
    }
  });

  // 上传图片列表
  const [uploadItems, setUploadItems] = useState<UploadItem[]>([]);


  useEffect(() => {
    if (value?.colorDescriptions?.length > 0 ||
      (value?.materialDetail?.fullShotImgList?.length > 0 || value?.materialDetail?.detailShotImgList?.length > 0)) {

      // 初始化最大颜色组数量
      let maxColorGroup = 1;

      // 更新colorDescriptions
      if (value.colorDescriptions) {
        const descriptions = [...value.colorDescriptions];
        while (descriptions.length < maxColorGroup) {
          descriptions.push('');
        }
        setColorDescriptions(descriptions);
      }

      // 创建一个新的uploadItems数组
      const uploadItemList: UploadItem[] = [];

      // 遍历value.materialDetail，根据type分类处理
      value.materialDetail?.fullShotImgList?.forEach(item => {
        maxColorGroup = Math.max(maxColorGroup, item.colorGroupNumber);
        uploadItemList.push({
          colorGroup: item.colorGroupNumber,
          type: 'full',
          index: uploadItemList.length,
          tabKey: item.colorGroupNumber.toString(),
          url: item.imgUrl,
          imgUrl: item.imgUrl,
          uploadSizeError: false,
          uploadSizeRatioError: false
        });
      });

      value.materialDetail?.detailShotImgList?.forEach(item => {
        maxColorGroup = Math.max(maxColorGroup, item.colorGroupNumber);
        uploadItemList.push({
          colorGroup: item.colorGroupNumber,
          type: 'upper',
          index: uploadItemList.length,
          tabKey: item.colorGroupNumber.toString(),
          url: item.imgUrl,
          imgUrl: item.imgUrl,
          uploadSizeError: false,
          uploadSizeRatioError: false
        });
      });

      // 根据最大colorGroupNumber生成对应数量的标签页
      const newTabs = Array.from({ length: maxColorGroup }, (_, index) => ({
        key: (index + 1).toString(),
        title: `上传颜色 ${index + 1}`
      }));

      // 更新标签页
      setTabs(newTabs);

      // 更新uploadItems
      setUploadItems(uploadItemList);

      // 更新ResultParams
      setResultParams(value);
    }
  }, [value]);

  useEffect(() => {
    // 创建materialDetail对象
    const materialDetail: materialDetail = {
      fullShotImgList: [],
      detailShotImgList: []
    };

    // 遍历uploadItems，根据type分类处理
    uploadItems.forEach(item => {
      // 只处理已经上传成功且有imgUrl的项
      if (item.imgUrl) {
        const resultItem: resultUploadItem = {
          colorGroupNumber: item.colorGroup,
          viewTags: item.type,
          imgUrl: item.imgUrl
        };

        // 根据type分别添加到对应列表
        if (item.type === 'full') {
          materialDetail.fullShotImgList.push(resultItem);
        } else if (item.type === 'upper') {
          materialDetail.detailShotImgList.push(resultItem);
        }
      }
    });


    // 更新ResultParams并触发onChange
    const updatedResultParams = {
      ...ResultParams,
      colorDescriptions: colorDescriptions,
      materialDetail: materialDetail
    };
    if (JSON.stringify(updatedResultParams) !== JSON.stringify(ResultParams)) {
      setResultParams(updatedResultParams);
      onChange?.(updatedResultParams);
    }
  }, [uploadItems, colorDescriptions]);



  // 处理删除图片
  const handleDeleteImage = (item: UploadItem) => {
    // 清理本地资源
    if (item.file && item.url) {
      URL.revokeObjectURL(item.url);
    }
    // 过滤掉要删除的项
    const newItems = uploadItems.filter(i => i.index !== item.index);
    // 更新上传项
    setUploadItems(newItems);
  };

  // 处理单个文件上传
  const handleUploadImgItem = async (file: File, uploadItem: UploadItem) => {
    try {
      // 检查文件类型
      const processedFile = await convertHeicToJpeg(file);
      if (!processedFile || !processedFile.type.startsWith('image/')) {
        message.error('不支持的文件格式');
        return;
      }

      // 创建图片对象
      const img = new Image();
      const objectUrl = URL.createObjectURL(processedFile);

      //  加载图片
      img.onload = async () => {
        const sizeError = img.width < 1500 || img.height < 2000;
        const ratioError = Math.abs(img.width / img.height - 3 / 4) >= 0.01;

        // 封装上传项
        const updatedItem = {
          ...uploadItem,
          url: objectUrl,
          file: processedFile,
          uploadSizeError: sizeError,
          uploadSizeRatioError: ratioError,
          tabKey: activeKey
        };

        try {
          const compressedFile = await compressFile(processedFile);
          const imgUrl = await uploadFile(compressedFile);

          if (imgUrl) {
            updatedItem.imgUrl = imgUrl;
            updatedItem.url = imgUrl;
            // @ts-ignore
            updatedItem.file = undefined;
            URL.revokeObjectURL(objectUrl);
          }
        } catch (error) {
          console.error('上传失败:', error);
        }

        // 更新图片数据
        const newItems = [...uploadItems];

        // 查找是否已经存在相同的index
        const existingIndex = newItems.findIndex(item => item.index === uploadItem.index);
        if (existingIndex >= 0) {
          newItems[existingIndex] = updatedItem;
        } else {
          newItems.push(updatedItem);
        }

        // 更新图片数据
        setUploadItems(newItems);
      };

      img.onerror = () => {
        message.error('图片加载失败');
        URL.revokeObjectURL(objectUrl);
      };

      img.src = objectUrl;

    } catch (error) {
      console.error('文件处理错误:', error);
    }
  };

  // 批量上传处理
  const handleBatchUpload = async (files: FileList | File[], uploadType: 'full' | 'upper', colorGroup: number) => {
    setLoading(true);
    try {
      const uploadPromises = Array.from(files).map((file, index) => {
        const uploadItem: UploadItem = {
          colorGroup: colorGroup,
          type: uploadType,
          index: uploadItems.length + index,
          tabKey: activeKey
        };
        return handleUploadImgItem(file, uploadItem);
      });

      await Promise.all(uploadPromises);
    } catch (error) {
      console.error('批量上传错误:', error);
    } finally {
      setLoading(false);
    }
  };

  // 添加新标签页
  const addTab = () => {
    // 检查是否超过最大标签页数量
    if (tabs.length >= 3) {
      message.info('最多只能添加3个颜色标签页');
      return;
    }

    // 添加新标签页
    const newIndex = tabs.length + 1;
    const newKey = `${newIndex}`;
    setTabs([...tabs, { key: newKey, title: `上传颜色 ${newIndex}` }]);
    setActiveKey(newKey);
  };

  // 删除标签页
  const removeTab = (targetKey: string) => {
    const newTabs = tabs.filter(tab => tab.key !== targetKey);

    // 重新调整标签页名称和key
    const updatedTabs = newTabs.map((_, index) => ({
      key: `${index + 1}`,
      title: `上传颜色 ${index + 1}`
    }));

    setTabs(updatedTabs);

    // 更新激活的标签页
    if (activeKey === targetKey) {
      const lastTab = updatedTabs[updatedTabs.length - 1];
      setActiveKey(lastTab?.key || 'tab-1');
    } else {
      // 如果删除的不是当前激活的标签页，需要更新当前激活标签页的key
      const currentTabIndex = updatedTabs.findIndex(tab =>
        tab.title === tabs.find(t => t.key === activeKey)?.title
      );
      if (currentTabIndex !== -1) {
        setActiveKey(updatedTabs[currentTabIndex].key);
      }
    }

    // 更新图片数据的tabKey
    const newItems = uploadItems.map(item => {
      if (item.tabKey === targetKey) {
        return null; // 将被删除的标签页下的图片标记为null
      }
      // 更新其他图片的tabKey以匹配新的标签页key
      const oldTabIndex = parseInt(item.tabKey || '1');
      const newTabIndex = oldTabIndex > parseInt(targetKey)
        ? oldTabIndex - 1
        : oldTabIndex;
      return {
        ...item,
        tabKey: `${newTabIndex}`
      };
    }).filter(item => item !== null); // 移除被标记为删除的图片


    // 更新图片数据
    setUploadItems(newItems);
  };


  return (
    <div className="abtest-batch-upload-component">
      <Tabs
        activeKey={activeKey}
        onChange={setActiveKey}
        type="editable-card"
        onEdit={(targetKey, action) => {
          if (action === 'add') {
            addTab();
          } else if (action === 'remove' && typeof targetKey === 'string') {
            removeTab(targetKey);
          }
        }}
        items={tabs.map(tab => ({
          key: tab.key,
          label: (<span>{tab.title}</span>),
          closable: tabs.length > 1,
          children: (
            <div>
              <div className="upload-section">
                <div className='upload-section-item'>
                  <div><span style={{ color: 'red' }}>*</span> 1.上传全身图（至少5张）</div>
                  <UploadImageList
                    uploadItems={uploadItems.filter(item => item.type === 'full')}
                    loading={loading}
                    activeKey={tab.key}
                    onUploadItem={(file, item) => file ? handleUploadImgItem(file, { ...item, type: 'full' }) : handleDeleteImage(item)}
                    onBatchUpload={(files) => handleBatchUpload(files, 'full', parseInt(tab.key))}
                  />
                </div>

                <div className='upload-section-item'>
                  <div><span style={{ color: 'red' }}>*</span>  2.上传上半身图（至少5张）</div>
                  <UploadImageList
                    uploadItems={uploadItems.filter(item => item.type === 'upper')}
                    loading={loading}
                    activeKey={tab.key}
                    onUploadItem={(file, item) => file ? handleUploadImgItem(file, { ...item, type: 'upper' }) : handleDeleteImage(item)}
                    onBatchUpload={(files) => handleBatchUpload(files, 'upper', parseInt(tab.key))}
                  />
                </div>
              </div>

              <div className={'color-desc-block'} style={{ marginTop: '10px' }}>
                <span >补充该颜色准确名称：</span>

                <Input
                  className={'color-desc-input'}
                  value={colorDescriptions[parseInt(tab.key) - 1] || ''}
                  onChange={(e) => {
                    // 获取当前标签页的索引
                    const currentIndex = parseInt(tab.key) - 1;

                    // 创建新的颜色描述数组，确保数组长度足够
                    const updatedDescriptions = [...colorDescriptions];
                    while (updatedDescriptions.length <= currentIndex) {
                      updatedDescriptions.push('');
                    }

                    // 直接更新对应索引位置的值
                    updatedDescriptions[currentIndex] = e.target.value;

                    // 更新colorDescriptions
                    setColorDescriptions(updatedDescriptions);
                  }}
                  placeholder={'请输入颜色的准确名称，如：卡其色、浅棕色、深蓝绿色（非必填）'}
                />
                <span style={{ color: '#969799', fontSize: 14, fontWeight: 'normal' }}>帮助ai更好的还原服装颜色</span>
              </div>
            </div>
          ),
        }))}
      />
    </div>
  );
};

export default BatchUploadComponent;