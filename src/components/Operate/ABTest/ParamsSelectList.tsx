import LoraSelectFormItem from '@/components/Common/LoraSelectFormItem';
import { ElementSelector } from '@/components/Operate/MerchantPreferenceSetting';
import { Checkbox, Input, InputNumber, Radio, Select } from 'antd';
import { ElementConfig } from '@/services/ElementController';
import { getUserInfo } from '@/utils/utils';
import BatchUploadComponent from './BatchUploadComponent';
import TrainParamsCard from './TrainParamsCard';

// 获取用户信息
const userInfo = getUserInfo();

interface ParamOption {
  value: string;
  label: string;
}

// 出图/训练
const paramType = {
  // 出图
  creativePic: 'creativePic',
  //训练
  trainPic: 'trainPic',
};

// 正面/背面
const bodyTypeOptions: ParamOption[] = [
  { value: 'front view', label: '正面' },
  { value: 'back view', label: '背面' },
];

// 全身/半身
const positionOptions: ParamOption[] = [
  { value: 'whole body', label: '全身' },
  { value: 'upper body', label: '上半身' },
  { value: 'lower body', label: '下半身' },
];

// 尺寸
const creationSizeOptions: ParamOption[] = [
  { label: '768x1024', value: 'THREE_FOUR' },
  { label: '1024x1024', value: 'ONE_ONE' },
  { label: '1340x1785', value: 'THREE_FOUR_LG_N' },
  { label: '1536x1536', value: 'ONE_ONE_LG' },
  { label: '1080x1920', value: 'NINE_SIXTEEN_2K' },
  { label: '1152x1536', value: 'P_1152_1536' },
  { label: '1620x2100', value: 'P_1620_2100' },
  { label: '1200x600', value: 'P_1200_600' },
];

const repairFaceTypeOptions: ParamOption[] = [
  { label: '不修脸', value: 'N' },
  { label: 'flux', value: 'v_2' },
  { label: 'PW', value: 'PW' },
  { label: 'sd1.5', value: 'v_1' },
];

const faceSwapModelOptions: ParamOption[] = [
  { label: 'Reactor(默认)', value: 'reactor' },
  { label: 'InstantID', value: 'instantId' },
];

const fillModelOptions: ParamOption[] = [
  { label: 'flux1-fill-dev', value: 'flux1-fill-dev.safetensors' },
  { label: 'fluxFillFP8_v10', value: 'fluxFillFP8_v10.safetensors' },
];
const baseModelDTypeOptions: ParamOption[] = [
  { label: '默认', value: 'default' },
  { label: 'FP8', value: 'fp8_e4m3fn' },
];

const ksamplers = ['euler', 'dpmpp_2m', 'deis'];
const schedulers = ['beta', 'normal', 'sgm_uniform', 'ddim_uniform', 'kl_optimal'];

// ksamplerOptions是ksamplers * schedulers以｜分隔的笛卡尔积
const ksamplerOptions: ParamOption[] = ksamplers.flatMap(ksampler => schedulers.map(scheduler => {
    return { label: `${ksampler} + ${scheduler}`, value: `${ksampler}|${scheduler}` };
  },
));

const instantIdModelOptions = [
  { label: 'Epicrealismxl_Hades', value: 'sdxl/Epicrealismxl_Hades.safetensors' },
  { label: 'epicrealismXL_VXIAbeast4SLightning', value: 'sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors' },
  { label: 'realvisxlV40_v40LightningBakedvae', value: 'sdxl/realvisxlV40_v40LightningBakedvae.safetensors' },
  { label: 'realvisxlV50Lightning.Ng9I', value: 'sdxl/realvisxlV50Lightning.Ng9I.safetensors' },
  { label: 'juggernautXL_v9Rdphoto2Lightning', value: 'sdxl/juggernautXL_v9Rdphoto2Lightning.safetensors' },
];

const loraSwapFaceOptions: ParamOption[] = [
  { label: '是', value: 'Y' },
  { label: '否', value: 'N' },
];

// 组件配置
interface ComponentConfig {
  key?: string;
  // 组件
  component: JSX.Element;
  // 组件类型
  type?: string;
  // 组件数据列表
  dataList?: any[];
  // 默认值
  defaultValue?: any;
  //是否必填
  required?: boolean;
  // 组件名称
  label?: string;
  // 是否展示改组件
  isShowParams?: boolean;
  // 是否可以设置为训练参数
  isTrainParam?: boolean;
  // 是否可以设置为实验参数
  isSetComparison?: boolean;
  // 列数(组件占用列数)
  colSpan?: number;
  // 请求到后端的名称
  name?: string[];
}

// 组件配置参数
interface Props {
  form?: any;
  faceList?: ElementConfig[];
  sceneList?: ElementConfig[];
  onChange?: (item: any, name: any) => void;
  namePrefix?: string[];
  namePath?: (string | number)[];
}

// 获取配置数据
const getConfigData = (index: number, isExperimental: boolean, props: Props = {}): Record<string, ComponentConfig> => {
  return {
    loraId: {
      component: (
        <LoraSelectFormItem
          name={props.namePath || [...(props.namePrefix || []), index, 'comparisonParams', isExperimental ? 0 : 1, 'value']}
          rootKey={'items'}
          form={props.form}
          onChange={props.onChange}
          required={true} noStyle={true} />
      ),
      type: 'custom',
      defaultValue: undefined, required: true,
      label: '服饰',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: true,
      colSpan: 6,
    },
    face: {
      component: (
        <ElementSelector placeholder="请选择模特" elementList={props.faceList || []} maxCount={1} />
      ),
      type: 'custom',
      defaultValue: undefined,
      required: true,
      label: '模特',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 9,
    },
    scene: {
      component: (
        <ElementSelector placeholder="请选择场景" elementList={props.sceneList || []} maxCount={1} />
      ),
      type: 'custom',
      defaultValue: undefined,
      required: true,
      label: '场景',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 9,
    },
    bodyType: {
      component: (
        <Select options={bodyTypeOptions} placeholder="请选择正面/背面" />
      ),
      type: 'select',
      dataList: bodyTypeOptions,
      defaultValue: bodyTypeOptions[0].value, required: false,
      label: '正面/背面',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 6,
    },
    position: {
      component: (
        <Select options={positionOptions} placeholder="请选择全身/半身" />
      ),
      type: 'select',
      dataList: positionOptions,
      defaultValue: positionOptions[0].value, required: false,
      label: '全身/半身',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 6,
    },
    proportion: {
      component: (
        <Select options={creationSizeOptions} placeholder="选择出图尺寸" />
      ),
      type: 'select',
      dataList: creationSizeOptions,
      defaultValue: creationSizeOptions[2].value, required: true,
      label: '尺寸',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 6,
    },
    imageNum: {
      component: (
        <InputNumber placeholder="请输入" min={1} max={999} style={{ width: 80 }} />
      ),
      type: 'inputNumber',
      defaultValue: 15,
      required: true,
      label: '出图数量',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
    },
    enableAntiBlurLora: {
      component: (
        <Checkbox />
      ),
      type: 'checkbox',
      label: '背景虚化',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
    },
    // enableNewModel: {
    //   component: (
    //     <Checkbox />
    //   ),
    //   type: 'checkbox',
    //   label: '是否启用新模型',
    //   isShowParams: true,
    //   isSetComparison: true,
    //   isTrainParam: false,
    //   colSpan: 4,
    // },
    clothModelStrength: {
      component: (
        <InputNumber placeholder={'系统默认'} />
      ),
      label: '服装强度',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'loraStrength'],
    },
    sceneModelStrength: {
      component: (
        <InputNumber placeholder={'系统默认'} />
      ),
      label: '场景强度',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'sceneLoraStrength'],
    },
    faceModelStrength: {
      component: (
        <InputNumber placeholder={'系统默认'} />
      ),
      label: '模特强度',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'FACE', 'extInfo', 'faceLoraStrength'],
    },
    faceRepairStrength: {
      component: (
        <Select options={repairFaceTypeOptions} placeholder="修脸类型" style={{ width: 120 }} allowClear />
      ),
      label: '修脸类型',
      type: 'select',
      dataList: repairFaceTypeOptions,
      defaultValue: null,
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'FACE', 'extInfo', 'repairFaceType'],
    },
    loraSwapFace: {
      component: (
        <Select options={loraSwapFaceOptions} placeholder="默认" allowClear />
      ),
      label: '是否换脸',
      type: 'select',
      dataList: loraSwapFaceOptions,
      defaultValue: null,
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'FACE', 'extInfo', 'loraSwapFace'],
    },
    faceAfterStrength: {
      component: (
        <InputNumber placeholder={'默认模特配置'} />
      ),
      label: '后修脸强度',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'FACE', 'extInfo', 'faceAfterStrength'],
    },
    repairFaceDenoise: {
      component: (
        <InputNumber placeholder={'系统默认'} />
      ),
      label: '后修脸denoise',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'repairFaceDenoise'],
    },
    faceTags: {
      component: (
        <Input placeholder={'模特默认形象'} style={{ width: 120 }} />
      ),
      label: '模特形象',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'FACE', 'tags'],
    },
    speedUpSwitch: {
      component: (
        <Checkbox />
      ),
      type: 'checkbox',
      label: '推理加速',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'speedUpSwitch'],
    },
    swapFaceModel: {
      component: (
        <Select options={faceSwapModelOptions} placeholder="换脸模型" style={{ width: 130 }} allowClear />
      ),
      label: '换脸模型',
      type: 'select',
      dataList: faceSwapModelOptions,
      defaultValue: null,
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'FACE', 'extInfo', 'swapModelType'],
    },
    sampler: {
      component: (
        <Select options={ksamplerOptions} placeholder="换脸模型" allowClear />
      ),
      label: '出图采样器',
      type: 'select',
      dataList: ksamplerOptions,
      defaultValue: null,
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 6,
      name: ['testContext', 'samplerName'],
    },
    schedulerSteps: {
      component: (
        <InputNumber placeholder={'系统默认'} />
      ),
      label: '出图步数',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'lora', 'extInfo', 'increaseSteps'],
    },
    faceDetailerMaxSize: {
      component: (
        <InputNumber placeholder={'系统默认'} />
      ),
      label: '修脸maxSize',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'faceDetailerMaxSize'],
    },
    faceDetailerCropFactor: {
      component: (
        <InputNumber placeholder={'系统默认'} />
      ),
      label: '修脸cropFactor',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'faceDetailerCropFactor'],
    },
    forcePW: {
      component: (
        <Checkbox />
      ),
      type: 'checkbox',
      label: '前置PW开关',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'isForcePW'],
    },
    noshowFace: {
      component: (
        <Checkbox />
      ),
      type: 'checkbox',
      label: '不展示人脸',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'noshowFace'],
    },
    instantIdModel: {
      component: (
        <Select options={instantIdModelOptions} placeholder="instantId模型" allowClear />
      ),
      label: 'instantId模型',
      type: 'select',
      dataList: instantIdModelOptions,
      defaultValue: null,
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 5,
      name: ['testContext', 'instantIdModel'],
    },
    reactorAndInstantId: {
      component: (
        <Checkbox />
      ),
      type: 'checkbox',
      label: 'reactor+instantId',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'reactorAndInstantId'],
    },
    pulidAndInstantId: {
      component: (
        <Checkbox />
      ),
      type: 'checkbox',
      label: 'pulid换脸',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'pulidAndInstantId'],
    },
    fillModel: {
      component: (
        <Select options={fillModelOptions} placeholder="fill模型" allowClear />
      ),
      label: 'fill模型',
      type: 'select',
      dataList: fillModelOptions,
      defaultValue: null,
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 6,
      name: ['testContext', 'fillModel'],
    },
    baseModelDType: {
      component: (
        <Select options={baseModelDTypeOptions} placeholder="底模量化类型" allowClear />
      ),
      label: '底模量化类型',
      type: 'select',
      dataList: baseModelDTypeOptions,
      defaultValue: null,
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'baseModelDType'],
    },
    newInstantId: {
      component: (
        <Checkbox />
      ),
      type: 'checkbox',
      label: '新instantId方案',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'newInstantId'],
    },
    enablePromptCorrect: {
      component: (
        <Checkbox />
      ),
      type: 'checkbox',
      label: '开启prompt优化',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'enablePromptCorrect'],
    },
    instantIdV2: {
      component: (
        <Checkbox />
      ),
      type: 'checkbox',
      label: 'instantIdV2方案',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 4,
      name: ['testContext', 'instantIdV2'],
    }
  };
};


// 获取组件配置
const getParamConfig = (key: string, index: number, isExperimental: boolean, props: Props = {}): ComponentConfig | null => {
  const configs = getConfigData(index, isExperimental, props);
  if (key && key.indexOf('.') >= 0) {
    for (let configsKey in configs) {
      if (!configs[configsKey].name) {
        continue;
      }

      if (configs[configsKey].name.join('.') === key) {
        return { key: configsKey, ...configs[configsKey] };
      }
    }
  }
  return configs[key] ? { key, ...configs[key] } : null;
};

// 获取基础配置信息
export const selectCommonParams = (index: number, isExperimental: boolean, props: Props = {}): Record<string, ComponentConfig> => {
  // 获取组件配置列表
  const configs = getConfigData(index, isExperimental, props);

  // 过滤掉不展示的组件
  return Object.entries(configs).reduce((acc, [key, config]) => {
    if (config.isShowParams) {
      acc[key] = config;
    }
    return acc;
  }, {} as Record<string, ComponentConfig>);
};


// 服装种类
const clothingTypeOptions: ParamOption[] = [
  { label: '套装（上装+下装）', value: 'TwoPiece' },
  { label: '仅上装', value: 'Tops' },
  { label: '仅下装', value: 'Bottoms' },
  { label: '连体服装（如连衣裙）', value: 'OnePiece' },
  { label: '泳衣', value: 'SwimSuit' },
  { label: '内衣', value: 'SexyLingerie' },
];
// 渠道商和运营可以设置内衣
if (userInfo && (userInfo?.roleType === 'DISTRIBUTOR' || userInfo?.roleType === 'OPERATOR')) {
  clothingTypeOptions.push({ label: '内衣', value: 'Underwear' });
}

// 服装款式
const clothStyleTypeOptions: ParamOption[] = [
  { label: '女款', value: 'female' },
  { label: '男款', value: 'male' },
  { label: '男女同款', value: 'unisex' },
];


// 获取训练配置数据
const getTrainConfigData = (index: number, isExperimental: boolean, props: Props = {}): Record<string, ComponentConfig> => {
  return {
    name: {
      component: (
        <Input placeholder="请输入服装款式的名称" />
      ),
      defaultValue: undefined,
      required: true,
      label: '服装名称',
      isShowParams: true,
      isSetComparison: false,
      isTrainParam: false,
      colSpan: 24,
    },
    clothType: {
      component: (
        <Radio.Group defaultValue="TwoPiece" buttonStyle="solid">
          {clothingTypeOptions.map(option => (
            <Radio.Button key={option.value} value={option.value}>{option.label}</Radio.Button>
          ))}
        </Radio.Group>
      ),
      defaultValue: 'TwoPiece',
      required: true,
      label: '服装种类',
      isShowParams: true,
      isSetComparison: false,
      isTrainParam: false,
      colSpan: 16,
    },
    clothStyleType: {
      component: (
        <Radio.Group defaultValue="female" buttonStyle="solid">
          {clothStyleTypeOptions.map(option => (
            <Radio.Button key={option.value} value={option.value}>{option.label}</Radio.Button>
          ))}
        </Radio.Group>
      ),
      defaultValue: 'female',
      required: true,
      label: '服装款式',
      isShowParams: true,
      isSetComparison: false,
      isTrainParam: false,
      colSpan: 8,
    },
    detailImgList: {
      component: (
        <BatchUploadComponent
          value={props.form?.getFieldValue([...(props.namePrefix || []), index, 'detailImgList'])}
          onChange={(value) => {
            if (props.form) {
              props.form.setFieldValue([...(props.namePrefix || []), index, 'detailImgList'], value);
            }
          }}
        />
      ),
      defaultValue: [],
      required: false,
      label: '上传服装图片',
      isShowParams: true,
      isSetComparison: false,
      isTrainParam: false,
      colSpan: 24,
    },
    trainParam: {
      component: (
        <TrainParamsCard
          value={props.form?.getFieldValue([...(props.namePrefix || []), index, 'trainParam'])}
          onChange={(value) => {
            if (props.form) {
              // 更新当前组的训练参数
              props.form.setFields([{
                name: [...(props.namePrefix || []), index, 'trainParam'],
                value,
              }]);

              // 如果是实验组，同步更新对照组的训练参数
              if (isExperimental) {
                props.form.setFields([{
                  name: [...(props.namePrefix || []), index, 'comparisonParams', 0, 'trainParam'],
                  value,
                }]);
              }
            }
          }}
        />
      ),
      required: true,
      label: '训练参数',
      isShowParams: true,
      isSetComparison: true,
      isTrainParam: false,
      colSpan: 24,
      name: ['resolution', 'contentOrStyle', 'rank', 'alpha', 'maxTrainStep', 'lr', 'dropout'],
    },
  };
};

// 获取训练组件配置
const getTrainParamConfig = (key: string, index: number, isExperimental: boolean, props: Props = {}): ComponentConfig | null => {
  const configs = getTrainConfigData(index, isExperimental, props);
  if (key && key.indexOf('.') >= 0) {
    for (let configsKey in configs) {
      if (!configs[configsKey].name) {
        continue;
      }

      if (configs[configsKey].name.join('.') === key) {
        return { key: configsKey, ...configs[configsKey] };
      }
    }
  }
  return configs[key] ? { key, ...configs[key] } : null;
};

// 获取训练基础配置信息
export const selectTrainCommonParams = (index: number, isExperimental: boolean, props: Props = {}): Record<string, ComponentConfig> => {
  // 获取组件配置列表
  const configs = getTrainConfigData(index, isExperimental, props);

  // 过滤掉不展示的组件
  return Object.entries(configs).reduce((acc, [key, config]) => {
    if (config.isShowParams) {
      acc[key] = config;
    }
    return acc;
  }, {} as Record<string, ComponentConfig>);
};


export {
  paramType,
  bodyTypeOptions,
  positionOptions,
  creationSizeOptions,
  //======== 出图组件 ========
  getParamConfig,
  getConfigData,
  //======== 训练组件 ========
  getTrainParamConfig,
  getTrainConfigData,
  type ParamOption,
  type ComponentConfig,
  type Props,
};
