import React, { useState } from 'react';
import { Upload, message, Spin, Tooltip, Flex, Image } from 'antd';
import IconFont from '@/components/IconFont';
import './less/BatchUploadComponent.less';

// 定义 UploadItem 接口
export interface UploadItem {
  // 颜色组
  colorGroup: number;
  // 类型
  type: 'full' | 'upper';
  index: number;
  // 图片地址
  url?: string;
  // 文件
  file?: File;
  //  图片地址
  imgUrl?: string;
  // 上传大小错误
  uploadSizeError?: boolean;
  // 上传大小比例错误
  uploadSizeRatioError?: boolean;
  // tabKey
  tabKey?: string;
}

// 定义 UploadImageListProps 接口
interface UploadImageListProps {
  // 上传项列表
  uploadItems: UploadItem[];
  // 加载中
  loading: boolean;
  // 当前激活的 Tab
  activeKey: string;
  // 上传图片
  onUploadItem: (file: File, uploadItem: UploadItem) => void;
  // 批量上传图片
  onBatchUpload: (files: FileList | File[]) => void;
}

const UploadImageList: React.FC<UploadImageListProps> = ({
  uploadItems,
  loading,
  activeKey,
  onUploadItem,
  onBatchUpload
}) => {
  // 预览相关状态
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');

  // 处理预览
  const handlePreview = (url: string) => {
    setPreviewImage(url);
    setPreviewVisible(true);
  };

  // 处理删除上传项
  const handleDelete = (item: UploadItem) => {
    onUploadItem(undefined as unknown as File, item);
  };

  return (
    <div className={`batch-upload-list`}>
      {/* 已上传图片列表 */}
      {uploadItems.filter(item => item.tabKey === activeKey && item.url).map((item, index) => (
        <div key={index}
          className="batch-upload-item"
          onDrop={(e) => {
            // 处理拖放事件
            e.preventDefault();

            // 获取拖放文件
            const file = e.dataTransfer.files[0];
            // 更新上传项
            onUploadItem(file, item);
          }}

          // 处理拖拽事件
          onDragOver={(e) => e.preventDefault()}>

          <div className="img-container">
            <img
              src={item.url}
              alt="已上传图片"
              className="uploaded-img"
              onClick={() => {
                const previewUrl = item.url || item.imgUrl;
                if (previewUrl) {
                  handlePreview(previewUrl);
                }
              }}
            />

            {item.uploadSizeRatioError && (
              <div className="bottom-status-row" style={{ background: '#FEF3E6' }}>
                <Flex gap={5}>
                  <IconFont type="icon-a-lujing2" style={{ fontSize: 14 }} />
                  <div>图片比例建议4:3</div>
                </Flex>
              </div>
            )}

            {item.uploadSizeError && (
              <>
                <div className="size-error-overlay"></div>
                <div className="bottom-status-row" style={{ background: '#FEF3E6' }}>
                  <Flex gap={5}>
                    <IconFont type="icon-cuowu" style={{ fontSize: 14 }} />
                    <div>像素过低请重新上传</div>
                  </Flex>
                </div>
              </>
            )}

            <Tooltip title="重新上传">
              <Upload
                accept=".jpg,.jpeg,.png,.heic,.heif"
                showUploadList={false}
                className="upload-again"
                beforeUpload={(file) => {
                  // 重新上传图片
                  onUploadItem(file, item);
                  return false;
                }}>
                <IconFont type="icon-icon_qiehuan" />
              </Upload>
            </Tooltip>

            <IconFont
              className="upload-remove"
              type="icon-icon_shanchu"
              onClick={() => handleDelete(item)}
            />
          </div>
        </div>
      ))}

      {/* 上传区域 */}
      <Spin spinning={loading} tip="文件上传中...">
        <Upload
          accept=".jpg,.jpeg,.png,.heic,.heif"
          multiple
          showUploadList={false}
          beforeUpload={(file, fileList) => {
            if (fileList.indexOf(file) === fileList.length - 1) {
              onBatchUpload(fileList);
            }
            return false;
          }}>
          <div className="batch-custom-upload">
            {!loading && (
              <>
                <IconFont type="icon-a-shangchuantupian1x" style={{ fontSize: '32px' }} />
                <div className="card-title">点击/拖拽图片至此</div>
                <div className="upload-text">（支持批量上传）</div>
              </>
            )}
          </div>
        </Upload>
      </Spin>

      {/* 图片预览 */}
      {previewImage && (
        <Image
          style={{ display: 'none' }}
          preview={{
            visible: previewVisible,
            onVisibleChange: (visible) => setPreviewVisible(visible),
            afterOpenChange: (visible) => !visible && setPreviewImage(''),
          }}
          src={previewImage}
        />
      )}
    </div>
  );
};

export default UploadImageList;