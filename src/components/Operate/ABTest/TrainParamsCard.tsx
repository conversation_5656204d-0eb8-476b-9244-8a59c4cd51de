import React, { useEffect, useState } from 'react';
import { InputNumber, Select } from 'antd';
import './TrainParamsCard.less';

interface TrainParam {
  id?: number;
  key: string;
  resolution: string;
  contentOrStyle: string;
  rank: number;
  alpha: number;
  maxTrainStep: number;
  lr: number;
  dropout: number;
}

interface TrainParamsCardProps {
  onChange?: (value: TrainParam) => void;
  value?: TrainParam;
}

// 训练分辨率选项
const trainResolutionOptions = [
  { label: '1024', value: '1024' },
  { label: '1536', value: '1536' },
  { label: '2048', value: '2048' },
];

// 学习内容
const contentStyleOptions = [
  { label: 'content', value: 'content' },
  { label: 'style', value: 'style' },
];

const TrainParamsCard: React.FC<TrainParamsCardProps> = ({ onChange, value }) => {
  // 训练参数
  const [trainParam, setTrainParam] = useState<TrainParam>(() => {
    if (value) return value;
    return {
      key: Date.now().toString(),
      resolution: '1024',
      contentOrStyle: 'content',
      rank: 32,
      alpha: 16,
      maxTrainStep: 2500,
      lr: 0.00020,
      dropout: 0.20,
    };
  });

  // 监听外部 value 的变化
  useEffect(() => {
    if (value && JSON.stringify(value) !== JSON.stringify(trainParam)) {
      console.log('value changed', value);

      setTrainParam(value);
    }
  }, [value]);

  // 更新训练参数
  const handleParamChange = (field: keyof TrainParam, newValue: any) => {
    const updatedParam = { ...trainParam, [field]: newValue };
    setTrainParam(updatedParam);
    onChange?.(updatedParam);
  };

  return (
    <div className="train-params-card">
      <div className="param-row">
        <div className="param-item">
          <div className="param-label">训练分辨率</div>
          <Select
            mode="tags"
            maxCount={1}
            value={Array.isArray(trainParam.resolution) ? trainParam.resolution[0] : trainParam.resolution}
            options={trainResolutionOptions}
            onChange={(value) => handleParamChange('resolution', Array.isArray(value) ? value[value.length - 1] : value)}
            className="resolution-select"
          />
        </div>

        <div className="param-item">
          <div className="param-label">学习内容</div>
          <Select
            value={trainParam.contentOrStyle}
            options={contentStyleOptions}
            onChange={(value) => handleParamChange('contentOrStyle', value)}
            className="content-style-select"
          />
        </div>

        <div className="param-item">
          <div className="param-label">Rank</div>
          <InputNumber
            value={trainParam.rank}
            onChange={(value) => handleParamChange('rank', value)}
            min={1}
            className="rank-input"
          />
        </div>

        <div className="param-item">
          <div className="param-label">Alpha</div>
          <InputNumber
            value={trainParam.alpha}
            onChange={(value) => handleParamChange('alpha', value)}
            min={1}
            precision={0}
            className="alpha-input"
          />
        </div>

        <div className="param-item">
          <div className="param-label">训练步数</div>
          <InputNumber
            value={trainParam.maxTrainStep}
            onChange={(value) => handleParamChange('maxTrainStep', value)}
            min={1}
            step={100}
            className="train-step-input"
          />
        </div>

        <div className="param-item">
          <div className="param-label">学习率</div>
          <InputNumber
            value={trainParam.lr}
            onChange={(value) => handleParamChange('lr', value)}
            min={0}
            step={0.0001}
            className="lr-input"
          />
        </div>

        <div className="param-item">
          <div className="param-label">Dropout</div>
          <InputNumber
            value={trainParam.dropout}
            onChange={(value) => handleParamChange('dropout', value)}
            min={0}
            max={1}
            step={0.1}
            className="dropout-input"
          />
        </div>
      </div>
    </div>
  );
};

export default TrainParamsCard;






























