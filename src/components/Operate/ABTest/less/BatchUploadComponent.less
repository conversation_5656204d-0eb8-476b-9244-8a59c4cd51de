// 添加组件作用域
.abtest-batch-upload-component {

  // 上传区域部分
  .upload-section {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: stretch;


    .upload-section-item {
      width: 48%;
      display: flex;
      flex-direction: column;

      .upload-img-title {
        margin-bottom: 8px;
      }

      .batch-upload-list {
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        padding: 12px;
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        flex: 1;

        &.error {
          border: 1px solid #FC343F;
        }
      }

      // 已上传图片项
      .batch-upload-item {
        position: relative;
        width: 120px;
        height: 160px;

        .img-container {
          width: 100%;
          height: 100%;
          position: relative;

          .uploaded-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
          }

          .bottom-status-row {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 4px 8px;
          }

          .size-error-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 77, 79, 0.1);
          }

          // 操作按钮
          .upload-again,
          .upload-remove {
            position: absolute;
            right: 8px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.45);
            border-radius: 50%;
            cursor: pointer;
            color: #fff;
            font-size: 14px;
            opacity: 0;
            transition: opacity 0.3s;
          }
      
          .upload-again {
            top: 8px;
          }
      
          .upload-remove {
            top: 40px;
          }
      
          &:hover {
            .upload-again,
            .upload-remove {
              opacity: 1;
            }
          }
        }
      }

      // 上传区域
      .batch-custom-upload {
        width: 120px;
        height: 160px;
        border: 1px dashed #d9d9d9;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &:hover {
          border-color: #1890ff;
        }

        .card-title {
          margin-top: 8px;
          color: rgba(0, 0, 0, 0.85);
        }

        .upload-text {
          color: rgba(0, 0, 0, 0.45);
        }
      }
    }
  }

  .color-desc-block {
    display: flex;
    align-items: center;
    gap: 8px;

    .color-desc-input {
      width: 400px;
    }
  }
}
