import React, { useEffect, useState } from 'react';
import {
  Button,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Select,
  Table,
  Upload,
  Image,
  Space,
  Tag,
  Tooltip
} from 'antd';
import {
  createShootingStyle,
  deleteShootingStyleById,
  findAllShootingStyles,
  ShootingStyleVO,
  updateShootingStyleById,
} from '@/services/ShootingStyleController';
import { PlusOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import { PromptDictVO, queryDictByKeyAndTags, queryListByTypeAndTags } from '@/services/PromptDictController';
import { UPLOAD_URL } from '@/constants';

interface ShootingStyleBlockProps { }

// 定义树形数据结构
interface TreeNode {
  key: string;
  title?: string;
  type1Name?: string;
  type2Name?: string;
  type2EnName?: string;
  representativeBrand?: string;
  classicElements?: string;
  examImageUrls?: string;
  styleDesc?: string;
  modelTags?: string;
  createTime?: string;
  modifyTime?: string;
  id?: number;
  children?: TreeNode[];
  isCategory?: boolean; // 标识是否为分类节点
}

const ShootingStyleBlock: React.FC<ShootingStyleBlockProps> = () => {
  const [data, setData] = useState<Array<ShootingStyleVO>>([]);
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentItem, setCurrentItem] = useState<ShootingStyleVO | null>(null);
  const [presetCategory, setPresetCategory] = useState<string>(''); // 预设的一级分类
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [maleModelTags, setMaleModelTags] = useState<PromptDictVO[]>([]);
  const [femaleModelTags, setFemaleModelTags] = useState<PromptDictVO[]>([]);

  const [form] = Form.useForm();

  // 将平铺数据转换为树形结构
  const convertToTreeData = (flatData: ShootingStyleVO[]): TreeNode[] => {
    const categoryMap = new Map<string, TreeNode>();
    
    // 先创建一级分类节点
    flatData.forEach(item => {
      if (item.type1Name && !categoryMap.has(item.type1Name)) {
        categoryMap.set(item.type1Name, {
          key: `category-${item.type1Name}`,
          title: item.type1Name,
          type1Name: item.type1Name,
          children: [],
          isCategory: true
        });
      }
    });

    // 然后添加二级分类数据
    flatData.forEach(item => {
      if (item.type1Name && categoryMap.has(item.type1Name)) {
        const category = categoryMap.get(item.type1Name)!;
        category.children!.push({
          key: `item-${item.id}`,
          ...item,
          isCategory: false
        });
      }
    });

    return Array.from(categoryMap.values());
  };

  // 渲染带有Tooltip的文本内容
  const renderTextWithTooltip = (text: string, maxLines: number = 3) => {
    if (!text) return '-';
    
    const lineHeight = 20;
    const maxHeight = lineHeight * maxLines;
    
    return (
      <Tooltip title={text} overlayStyle={{ maxWidth: 400 }}>
        <div
          style={{
            maxHeight: maxHeight,
            lineHeight: `${lineHeight}px`,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: maxLines,
            WebkitBoxOrient: 'vertical',
            wordBreak: 'break-word',
            cursor: 'pointer'
          }}
        >
          {text}
        </div>
      </Tooltip>
    );
  };

  useEffect(() => {
    fetchData();

    queryListByTypeAndTags('FACE_TYPES', ['style', 'male']).then(res => {
      if (res) {
        setMaleModelTags(res);
      }
    });
    queryListByTypeAndTags('FACE_TYPES', ['style', 'female']).then(res => {
      if (res) {
        setFemaleModelTags(res);
      }
    });
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await findAllShootingStyles();
      if (res) {
        setData(res);
        setTreeData(convertToTreeData(res));
      }
    } catch (error) {
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setCurrentItem(null);
    setPresetCategory('');
    form.resetFields();
    setFileList([]);
    setModalVisible(true);
  };

  // 添加二级分类时，预设一级分类
  const handleAddSubCategory = (categoryName: string) => {
    setCurrentItem(null);
    setPresetCategory(categoryName);
    form.resetFields();
    form.setFieldsValue({
      type1Name: categoryName
    });
    setFileList([]);
    setModalVisible(true);
  };

  const handleEdit = (item: ShootingStyleVO) => {
    setCurrentItem(item);
    setPresetCategory('');

    // 处理模特标签数据
    let modelTagsData = { male: [], female: [] };
    if (item.modelTags) {
      try {
        const parsedTags = JSON.parse(item.modelTags);
        modelTagsData = {
          male: parsedTags['男模'] || parsedTags['male'] || [],
          female: parsedTags['女模'] || parsedTags['female'] || []
        };
      } catch (e) {
        // 如果解析失败，保持默认值
      }
    }

    form.setFieldsValue({
      ...item,
      modelTags: modelTagsData
    });

    // 处理示例图片
    if (item.examImageUrls) {
      try {
        const urls = JSON.parse(item.examImageUrls);
        const files = urls.map((url: string, index: number) => ({
          uid: `${index}`,
          name: `image-${index}`,
          status: 'done' as const,
          url: url,
        }));
        setFileList(files);
      } catch (e) {
        setFileList([]);
      }
    } else {
      setFileList([]);
    }

    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    setLoading(true);
    try {
      await deleteShootingStyleById(id);
      message.success('删除成功');
      fetchData();
    } catch (error) {
      message.error('删除失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values: any) => {
    setLoading(true);
    try {
      // 处理示例图片URLs
      const imageUrls = fileList
        .filter(file => file.status === 'done')
        .map(file => file.url || (file.response && file.response.success ? file.response.data : null))
        .filter(Boolean);

      // 处理模特标签数据
      let modelTagsString = '';
      if (values.modelTags) {
        const modelTagsObj = {
          '男模': values.modelTags.male || [],
          '女模': values.modelTags.female || []
        };
        modelTagsString = JSON.stringify(modelTagsObj);
      }

      const payload = {
        ...values,
        examImageUrls: JSON.stringify(imageUrls),
        modelTags: modelTagsString,
        id: currentItem?.id,
      };

      if (currentItem) {
        await updateShootingStyleById(payload);
        message.success('更新成功');
      } else {
        await createShootingStyle(payload);
        message.success('添加成功');
      }

      fetchData();
      setModalVisible(false);
    } catch (error) {
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  const handleUploadChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    setFileList(newFileList);

    // 处理上传成功的文件
    newFileList.forEach(file => {
      if (file.status === 'done' && file.response && file.response.success) {
        message.success('图片上传成功');
      } else if (file.status === 'error') {
        message.error('图片上传失败，请重试');
      }
    });
  };

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>上传</div>
    </div>
  );

  const renderExamImages = (examImageUrls: string) => {
    if (!examImageUrls) return '-';

    try {
      const urls = JSON.parse(examImageUrls);
      if (!Array.isArray(urls) || urls.length === 0) return '-';

      return (
        <Image.PreviewGroup>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: 4, maxWidth: 300 }}>
            {urls.map((url: string, index: number) => (
              <Image
                key={index}
                width={30}
                height={30}
                src={url}
                style={{
                  borderRadius: 4,
                  objectFit: 'cover',
                  cursor: 'pointer'
                }}
                preview={{
                  src: url
                }}
              />
            ))}
          </div>
        </Image.PreviewGroup>
      );
    } catch (e) {
      return '-';
    }
  };

  const renderModelTags = (modelTags: string) => {
    if (!modelTags) return '-';

    try {
      const tags = JSON.parse(modelTags);
      return (
        <Space wrap>
          {tags['男模'] && tags['男模'].length > 0 && (
            <Tag color="blue">
              男模: {tags['男模'].join(', ')}
            </Tag>
          )}
          {tags['女模'] && tags['女模'].length > 0 && (
            <Tag color="pink">
              女模: {tags['女模'].join(', ')}
            </Tag>
          )}
        </Space>
      );
    } catch (e) {
      return modelTags;
    }
  };

  const columns = [
    { 
      title: 'ID', 
      dataIndex: 'id', 
      key: 'id', 
      width: 80,
      render: (text: any, record: TreeNode) => {
        return record.isCategory ? '' : text;
      }
    },
    { 
      title: '分类层级', 
      dataIndex: 'title', 
      key: 'title', 
      width: 180,
      render: (text: any, record: TreeNode) => {
        if (record.isCategory) {
          return (
            <div style={{ 
              padding: '6px 8px',
              backgroundColor: '#f0f8ff',
              borderRadius: '4px',
              border: '1px solid #d9d9d9'
            }}>
              <span style={{ 
                fontWeight: 'bold', 
                color: '#1890ff', 
                fontSize: '13px'
              }}>
                {record.type1Name}
              </span>
            </div>
          );
        }
        return (
          <div style={{ 
            paddingLeft: '15px',
            position: 'relative'
          }}>
            <span style={{ 
              fontSize: '12px',
              color: '#666'
            }}>
              {record.type2Name}
            </span>
          </div>
        );
      }
    },
    { 
      title: '英文名称', 
      dataIndex: 'type2EnName', 
      key: 'type2EnName', 
      width: 100,
      render: (text: any, record: TreeNode) => {
        return record.isCategory ? '' : (
          <div style={{ fontSize: '12px' }}>
            {text || '-'}
          </div>
        );
      }
    },
    { 
      title: '代表性品牌', 
      dataIndex: 'representativeBrand', 
      key: 'representativeBrand', 
      width: 100,
      render: (text: any, record: TreeNode) => {
        return record.isCategory ? '' : renderTextWithTooltip(text, 2);
      }
    },
    { 
      title: '经典元素', 
      dataIndex: 'classicElements', 
      key: 'classicElements', 
      width: 120,
      render: (text: any, record: TreeNode) => {
        return record.isCategory ? '' : renderTextWithTooltip(text, 2);
      }
    },
    {
      title: '示例图片',
      dataIndex: 'examImageUrls',
      key: 'examImageUrls',
      width: 120,
      render: (text: any, record: TreeNode) => {
        if (record.isCategory) return '';
        
        if (!text) return '-';
        
        try {
          const urls = JSON.parse(text);
          if (!Array.isArray(urls) || urls.length === 0) return '-';
          
          return (
            <div style={{ display: 'flex', gap: 2 }}>
              {urls.slice(0, 3).map((url: string, index: number) => (
                <Image
                  key={index}
                  width={25}
                  height={25}
                  src={url}
                  style={{
                    borderRadius: 3,
                    objectFit: 'cover',
                    cursor: 'pointer'
                  }}
                  preview={{
                    src: url
                  }}
                />
              ))}
              {urls.length > 3 && (
                <span style={{ 
                  fontSize: '11px', 
                  color: '#999', 
                  alignSelf: 'center'
                }}>
                  +{urls.length - 3}
                </span>
              )}
            </div>
          );
        } catch (e) {
          return '-';
        }
      }
    },
    {
      title: '风格描述',
      dataIndex: 'styleDesc',
      key: 'styleDesc',
      width: 150,
      render: (text: any, record: TreeNode) => {
        return record.isCategory ? '' : renderTextWithTooltip(text, 2);
      }
    },
    {
      title: '模特类型',
      dataIndex: 'modelTags',
      key: 'modelTags',
      width: 200,
      render: renderModelTags,
    },
    { 
      title: '创建时间', 
      dataIndex: 'createTime', 
      key: 'createTime', 
      width: 110,
      render: (text: any, record: TreeNode) => {
        return record.isCategory ? '' : (
          <div style={{ fontSize: '11px' }}>
            {text ? text.substring(0, 10) : '-'}
          </div>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right' as const,
      render: (_, record: TreeNode) => {
        if (record.isCategory) {
          return (
            <Button 
              type="link" 
              size="small"
              onClick={() => handleAddSubCategory(record.type1Name!)}
            >
              添加
            </Button>
          );
        }
        return (
          <Space size={4}>
            <Button type="link" size="small" onClick={() => handleEdit(record as ShootingStyleVO)}>
              编辑
            </Button>
            <Popconfirm
              title="确定要删除这个风格配置吗？"
              onConfirm={() => handleDelete(record.id!)}
            >
              <Button type="link" danger size="small">
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <div className="shooting-style-management">
      <style>
        {`
          .shooting-style-management .ant-table-tbody > tr.category-row {
            background-color: #f8f9fa !important;
            border-top: 2px solid #e9ecef !important;
            border-bottom: 1px solid #e9ecef !important;
          }
          .shooting-style-management .ant-table-tbody > tr.category-row:hover {
            background-color: #e9ecef !important;
          }
          .shooting-style-management .ant-table-tbody > tr.item-row {
            background-color: #ffffff !important;
            border-left: 3px solid #f0f8ff !important;
          }
          .shooting-style-management .ant-table-tbody > tr.item-row:hover {
            background-color: #f8f9fa !important;
          }
          .shooting-style-management .ant-table-tbody > tr.category-row > td {
            border-bottom: 1px solid #e9ecef !important;
            padding: 10px 12px !important;
          }
          .shooting-style-management .ant-table-tbody > tr.item-row > td {
            border-bottom: 1px solid #f0f0f0 !important;
            padding: 6px 12px !important;
          }
        `}
      </style>
      
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" onClick={handleAdd}>
          新增风格配置
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={treeData}
        loading={loading}
        rowKey="key"
        scroll={{ x: 1280 }}
        pagination={false}
        expandable={{
          defaultExpandAllRows: true,
          childrenColumnName: 'children',
        }}
        size="small"
        rowClassName={(record: TreeNode) => {
          if (record.isCategory) {
            return 'category-row';
          }
          return 'item-row';
        }}
      />

      <Modal
        title={currentItem ? '编辑风格配置' : presetCategory ? `添加二级分类 - ${presetCategory}` : '新增风格配置'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={800}
        destroyOnClose
        centered
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Form.Item
            name="type1Name"
            label="一级分类名称"
            rules={[{ required: true, message: '请输入一级分类名称' }]}
          >
            <Input 
              placeholder="请输入一级分类名称" 
              disabled={!!presetCategory}
            />
          </Form.Item>

          <Form.Item
            name="type2Name"
            label="二级分类名称"
            rules={[{ required: true, message: '请输入二级分类名称' }]}
          >
            <Input placeholder="请输入二级分类名称" />
          </Form.Item>

          <Form.Item
            name="type2EnName"
            label="英文名称"
            rules={[{ required: false, message: '请输入英文名称' }]}
          >
            <Input placeholder="请输入英文名称" />
          </Form.Item>

          <Form.Item name="representativeBrand" label="代表性品牌">
            <Input placeholder="请输入代表性品牌" />
          </Form.Item>

          <Form.Item name="classicElements" label="经典元素">
            <Input.TextArea rows={3} placeholder="请输入经典元素" />
          </Form.Item>

          <Form.Item name="styleDesc" label="风格描述">
            <Input.TextArea rows={4} placeholder="请输入风格描述" />
          </Form.Item>

          <Form.Item label="模特类型">
            <div style={{ border: '1px solid #d9d9d9', borderRadius: 6, padding: 16 }}>
              <Form.Item
                name={['modelTags', 'male']}
                label="男模标签"
                style={{ marginBottom: 16 }}
              >
                <Select
                  mode="tags"
                  placeholder="请输入男模标签，支持多个"
                  style={{ width: '100%' }}
                  tokenSeparators={[',']}
                  options={maleModelTags.map(tag => ({ label: tag.word, value: tag.word }))}
                />
              </Form.Item>
              <Form.Item
                name={['modelTags', 'female']}
                label="女模标签"
                style={{ marginBottom: 0 }}
              >
                <Select
                  mode="tags"
                  placeholder="请输入女模标签，支持多个"
                  style={{ width: '100%' }}
                  tokenSeparators={[',']}
                  options={femaleModelTags.map(tag => ({ label: tag.word, value: tag.word }))}
                />
              </Form.Item>
            </div>
          </Form.Item>

          <Form.Item label="示例图片">
            <Upload
              action={UPLOAD_URL}
              multiple={true}
              accept='image/*'
              listType="picture-card"
              fileList={fileList}
              onChange={handleUploadChange}
              beforeUpload={(file) => {
                const isImage = file.type.startsWith('image/');
                if (!isImage) {
                  message.error('只能上传图片文件!');
                  return false;
                }
                return true;
              }}
            >
              {fileList.length >= 20 ? null : uploadButton}
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ShootingStyleBlock;