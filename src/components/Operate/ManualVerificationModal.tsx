import { Button, Flex, Modal, Card, Empty, Upload, message, notification, UploadFile, UploadProps } from 'antd';
import React, { useEffect, useState } from 'react';
import ImgPreview from '@/components/ImgPreview';
import { UploadOutlined } from '@ant-design/icons';
// import {
//   queryImagesByElementWithPage,
// } from '@/services/CreativeController';
import { selectShowImageByScene, ShowImageVO } from '@/services/ElementController';
import { RefreshButton } from '@/components/Common/CommonComponent';
import IconFont from '@/components/IconFont';
import { UPLOAD_URL } from '@/constants';
import { FileType, getBase64 } from '@/utils/utils';
import { convertPngToJpg } from '@/utils/imageUtils';
import '@/components/BasicReplace/StyleLoraDrawer.less';

interface ManualVerificationModalProps {
  visible: boolean;
  onClose: () => void;
  sceneId: number;
  children: Array<any>; // 姿势项数据
  allTabList: Array<{ key: string; value: string; label: string }>; // 所有姿势类型
  onImageSelect?: (poseItemId: number, imageUrl: string) => void; // 图片选择回调
}

interface ImageCardProps {
  image: string;
  width: number;
  poseKey: string;
  isSelected: boolean;
  onImageClick: (image: string) => void;
  onSelectChange: (poseKey: string, image: string) => void;
}

const ManualVerificationModal: React.FC<ManualVerificationModalProps> = ({
  visible,
  onClose,
  sceneId,
  children = [],
  allTabList = [],
  onImageSelect,
}) => {
  const [allImages, setAllImages] = useState<{ [key: string]: string[] }>({});
  const [previewImg, setPreviewImg] = useState<null | string>(null);
  const [previewImgList, setPreviewImgList] = useState<Array<string>>([]);
  const [selectedImages, setSelectedImages] = useState<{ [key: string]: string }>({});
  const [loading, setLoading] = useState(false);
  const [uploadFileList, setUploadFileList] = useState<{ [key: string]: UploadFile[] }>({});

  // 统一的主题色
  const PRIMARY_COLOR = '#52c41a';
  const PRIMARY_COLOR_HOVER = '#73d13d';
  const PRIMARY_COLOR_LIGHT = '#f6ffed';
  const PRIMARY_COLOR_SHADOW = 'rgba(82, 196, 26, 0.4)';
  const PRIMARY_COLOR_SHADOW_LIGHT = 'rgba(82, 196, 26, 0.2)';

  // 获取姿势项的显示名称
  const getPoseItemName = (poseItem: any) => {
    if (!poseItem?.type || !Array.isArray(poseItem.type)) {
      return '未知姿势';
    }

    // 找到匹配的姿势类型
    const matchedType = allTabList.find(tab => {
      const tabKeys = tab.key.split(',');
      return tabKeys.every(key => poseItem.type.includes(key));
    });

    if (matchedType) {
      // 检查是否有数字后缀
      const numberSuffix = poseItem.type.find(type => /^\d+$/.test(type));
      return numberSuffix ? `${matchedType.label}${numberSuffix}` : matchedType.label;
    }

    return poseItem.type.join(',');
  };

  // 获取姿势项的唯一键
  const getPoseItemKey = (poseItem: any) => {
    if (!poseItem?.type || !Array.isArray(poseItem.type)) {
      return 'unknown';
    }
    return poseItem.type.sort().join(',');
  };

  const fetchData = () => {
    if (!sceneId) {
      console.warn('sceneId 不能为空');
      return;
    }

    setLoading(true);
    
    // 调用 selectShowImageByScene 接口获取场景对应的姿势展示图
    selectShowImageByScene(sceneId).then(res => {
      if (res) {
        const groupedImages: { [key: string]: string[] } = {};
        
        // 遍历 children，为每个姿势项查找对应的图片数据
        children.forEach(poseItem => {
          const poseKey = getPoseItemKey(poseItem);
          const poseItemId = poseItem.id;
          
          if (poseItemId && res[poseItemId]) {
            const showImageVO: ShowImageVO = res[poseItemId];
            // 使用 showImages 作为图片列表
            groupedImages[poseKey] = showImageVO.showImages || [];
            
            // 如果有选中的展示图，将其设置为默认选中
            if (showImageVO.selectedShowImage) {
              setSelectedImages(prev => ({
                ...prev,
                [poseKey]: showImageVO.selectedShowImage
              }));
            }
          } else {
            // 如果没有找到对应的数据，设置为空数组
            groupedImages[poseKey] = [];
          }
        });

        setAllImages(groupedImages);
      } else {
        // 如果接口返回空，初始化为空数据
        const groupedImages: { [key: string]: string[] } = {};
        children.forEach(poseItem => {
          const poseKey = getPoseItemKey(poseItem);
          groupedImages[poseKey] = [];
        });
        setAllImages(groupedImages);
      }
    }).catch(error => {
      console.error('获取场景姿势展示图失败:', error);
      // 出错时也要初始化数据结构
      const groupedImages: { [key: string]: string[] } = {};
      children.forEach(poseItem => {
        const poseKey = getPoseItemKey(poseItem);
        groupedImages[poseKey] = [];
      });
      setAllImages(groupedImages);
    }).finally(() => {
      setLoading(false);
    });
  };

  const handleSelectImage = (poseKey: string, image: string) => {
    setSelectedImages(prev => {
      const newSelected = { ...prev };
      if (newSelected[poseKey] === image) {
        // 如果已选中，则取消选择
        delete newSelected[poseKey];
      } else {
        // 选择新图片
        newSelected[poseKey] = image;
        
        // 触发父组件的回调进行更新操作
        const matchedPoseItem = children.find(poseItem => getPoseItemKey(poseItem) === poseKey);
        if (matchedPoseItem && matchedPoseItem.id && onImageSelect) {
          onImageSelect(matchedPoseItem.id, image);
        }
      }
      return newSelected;
    });
  };

  const handleImageClick = (image: string, poseKey: string) => {
    setPreviewImg(image);
    // 获取当前姿势项的所有图片用于预览，包括额外显示的选中图片
    const currentPoseImages = allImages[poseKey] || [];
    const selectedImage = selectedImages[poseKey];
    
    // 构建完整的预览图片列表
    let previewList = [...currentPoseImages];
    if (selectedImage && !currentPoseImages.includes(selectedImage)) {
      previewList.push(selectedImage);
    }
    
    setPreviewImgList(previewList);
  };

  // 处理文件上传
  const handleFileChange = (poseKey: string): UploadProps['onChange'] => {
    return ({ fileList: newFileList }) => {
      const file = newFileList[0];

      console.log('上传文件状态:', file);

      if (file && file.response && !file.response.success) {
        console.log('上传失败:', file);
        notification.error({ message: '上传文件异常，请重试' });
        setUploadFileList(prev => ({ ...prev, [poseKey]: [] }));
        return;
      }

      // 更新文件列表状态
      setUploadFileList(prev => ({ ...prev, [poseKey]: newFileList }));

      // 如果上传成功，获取URL并调用回调
      if (file && file.response && file.response.success) {
        const uploadedUrl = file.response.data;
        console.log('上传成功，URL:', uploadedUrl);
        
        // 将上传的图片设置为选中状态
        setSelectedImages(prev => ({
          ...prev,
          [poseKey]: uploadedUrl
        }));
        
        // 触发父组件的回调进行更新操作
        const matchedPoseItem = children.find(poseItem => getPoseItemKey(poseItem) === poseKey);
        if (matchedPoseItem && matchedPoseItem.id && onImageSelect) {
          onImageSelect(matchedPoseItem.id, uploadedUrl);
        }
        
        // 清空上传组件的文件列表，不进行回显
        setUploadFileList(prev => ({ ...prev, [poseKey]: [] }));
        
        message.success('图片上传成功');
      }
    };
  };

  // 处理文件预览
  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType);
    }

    setPreviewImg(file.url || (file.preview as string));
    setPreviewImgList([]);
  };

  useEffect(() => {
    if (visible) {
      fetchData();
    } else {
      // 清空上传文件列表状态
      setUploadFileList({});
    }
  }, [visible]);

  // 优化后的图片卡片组件
  const ImageCard: React.FC<ImageCardProps> = ({ 
    image, 
    width, 
    poseKey, 
    isSelected, 
    onImageClick, 
    onSelectChange 
  }) => {
    return (
      <div 
        className="image-card-container"
        style={{ 
          position: 'relative', 
          display: 'inline-block',
          borderRadius: '8px',
          padding: isSelected ? '2px' : '4px',
          background: isSelected ? PRIMARY_COLOR_LIGHT : 'transparent',
          border: isSelected ? `3px solid ${PRIMARY_COLOR}` : '3px solid transparent',
          transition: 'all 0.3s ease',
          cursor: 'pointer',
          boxShadow: isSelected ? `0 4px 12px ${PRIMARY_COLOR_SHADOW}` : 'none'
        }}
      >
        <img
          src={image}
          width={width}
          alt=""
          onClick={() => onImageClick(image)}
          style={{ 
            borderRadius: '6px',
            border: '1px solid #f0f0f0',
            transition: 'all 0.3s ease',
            display: 'block',
            width: '100%',
            height: 'auto'
          }}
        />
        
        {/* 优化后的圆形选择框 */}
        <div
          className="circular-checkbox"
          onClick={(e) => {
            e.stopPropagation();
            onSelectChange(poseKey, image);
          }}
          style={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            width: '24px',
            height: '24px',
            borderRadius: '50%',
            background: isSelected ? PRIMARY_COLOR : 'rgba(255, 255, 255, 0.9)',
            border: `2px solid ${isSelected ? PRIMARY_COLOR : '#d9d9d9'}`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            zIndex: 10,
            boxShadow: isSelected 
              ? `0 2px 8px ${PRIMARY_COLOR_SHADOW}, 0 0 0 2px ${PRIMARY_COLOR_SHADOW_LIGHT}`
              : '0 2px 4px rgba(0, 0, 0, 0.1)',
            fontSize: '12px',
            fontWeight: 'bold',
            color: isSelected ? 'white' : 'transparent',
            transform: 'scale(1)'
          }}
          onMouseEnter={(e) => {
            const target = e.currentTarget;
            if (!isSelected) {
              target.style.borderColor = PRIMARY_COLOR;
              target.style.background = 'rgba(255, 255, 255, 1)';
              target.style.transform = 'scale(1.1)';
              target.style.boxShadow = `0 2px 8px ${PRIMARY_COLOR_SHADOW_LIGHT}`;
            } else {
              target.style.background = PRIMARY_COLOR_HOVER;
              target.style.borderColor = PRIMARY_COLOR_HOVER;
              target.style.transform = 'scale(1.05)';
              target.style.boxShadow = `0 4px 12px ${PRIMARY_COLOR_SHADOW}, 0 0 0 3px ${PRIMARY_COLOR_SHADOW_LIGHT}`;
            }
          }}
          onMouseLeave={(e) => {
            const target = e.currentTarget;
            if (!isSelected) {
              target.style.borderColor = '#d9d9d9';
              target.style.background = 'rgba(255, 255, 255, 0.9)';
              target.style.transform = 'scale(1)';
              target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
            } else {
              target.style.background = PRIMARY_COLOR;
              target.style.borderColor = PRIMARY_COLOR;
              target.style.transform = 'scale(1)';
              target.style.boxShadow = `0 2px 8px ${PRIMARY_COLOR_SHADOW}, 0 0 0 2px ${PRIMARY_COLOR_SHADOW_LIGHT}`;
            }
          }}
        >
          {isSelected ? '✓' : ''}
        </div>
      </div>
    );
  };

  return (
    <>
      <Modal
        open={visible}
        width={'90%'}
        centered
        onCancel={onClose}
        footer={null}
        styles={{
          body: { padding: '16px', height: 'calc(80vh)', display: 'flex', flexDirection: 'column' }
        }}
        style={{ 
          top: 20
        }}
        zIndex={1100}
        destroyOnClose={true}
        maskClosable={false}
        title={
          <div>
            <div style={{ fontSize: '16px', fontWeight: 600 }}>
              人工核验 - 姿势图片选择
            </div>
            <div style={{ 
              fontSize: '12px', 
              color: '#666', 
              fontWeight: 'normal',
              marginTop: '4px'
            }}>
              选择图片之后会直接更新数据
            </div>
          </div>
        }
      >

        
        {/* 滚动区域 */}
        <div style={{ flex: 1, overflowY: 'auto', display: 'flex', flexDirection: 'column' }}>
          {loading ? (
            <div style={{ 
              display: 'flex', 
              justifyContent: 'center', 
              alignItems: 'center', 
              height: '200px' 
            }}>
              <IconFont type={'icon-jiazai'} style={{ fontSize: 32 }} />
              <span style={{ marginLeft: 8 }}>加载中...</span>
            </div>
          ) : (
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: '1fr 1fr', 
              gap: '16px',
              padding: '0 8px'
            }}>
               {children.length === 0 ? (
                 <div style={{ gridColumn: '1 / -1' }}>
                   <Empty
                     image={Empty.PRESENTED_IMAGE_SIMPLE}
                     description="暂无姿势项数据"
                     style={{ margin: '60px 0' }}
                   />
                 </div>
               ) : (
                 children.map((poseItem, index) => {
                const poseKey = getPoseItemKey(poseItem);
                const poseName = getPoseItemName(poseItem);
                const poseImages = allImages[poseKey] || [];
                const selectedImage = selectedImages[poseKey];

                return (
                  <Card
                    key={index}
                    style={{
                      border: selectedImage ? `2px solid ${PRIMARY_COLOR}` : '2px solid #e8e8e8',
                      borderRadius: '12px',
                      position: 'relative',
                      background: selectedImage ? PRIMARY_COLOR_LIGHT : '#fff',
                      transition: 'all 0.3s ease',
                      boxShadow: selectedImage ? `0 4px 12px ${PRIMARY_COLOR_SHADOW}` : '0 2px 8px rgba(0, 0, 0, 0.06)'
                    }}
                    styles={{
                      body: { padding: '16px' }
                    }}
                  >
                    {/* 左上角姿势名称 */}
                    <div
                      style={{
                        position: 'absolute',
                        top: '-1px',
                        left: '16px',
                        background: selectedImage ? PRIMARY_COLOR : '#1890ff',
                        color: 'white',
                        padding: '4px 12px',
                        borderRadius: '0 0 8px 8px',
                        fontSize: '14px',
                        fontWeight: 'bold',
                        zIndex: 1,
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                      }}
                    >
                      {poseName}
                      {selectedImage && (
                        <span style={{ marginLeft: 8 }}>✓ 已选择</span>
                      )}
                    </div>

                    {/* 图片网格和上传按钮 */}
                    <div style={{ marginTop: '20px' }}>
                      <div style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(auto-fill, minmax(120px, 1fr))',
                        gap: '12px',
                        width: '100%'
                      }}>
                        {/* 现有图片 */}
                        {poseImages.map((image, imgIndex) => (
                          <ImageCard
                            key={imgIndex}
                            image={image}
                            width={120}
                            poseKey={poseKey}
                            isSelected={selectedImages[poseKey] === image}
                            onImageClick={(img) => handleImageClick(img, poseKey)}
                            onSelectChange={handleSelectImage}
                          />
                        ))}
                        
                        {/* 如果selectedImage不在poseImages中，则额外显示 */}
                        {selectedImage && !poseImages.includes(selectedImage) && (
                          <div style={{ position: 'relative' }}>
                            <ImageCard
                              key={`selected-${poseKey}`}
                              image={selectedImage}
                              width={120}
                              poseKey={poseKey}
                              isSelected={true}
                              onImageClick={(img) => handleImageClick(img, poseKey)}
                              onSelectChange={handleSelectImage}
                            />
                            {/* 手动上传标识 */}
                            <div style={{
                              position: 'absolute',
                              top: '-2px',
                              left: '4px',
                              background: '#ff4d4f',
                              color: 'white',
                              padding: '2px 6px',
                              borderRadius: '4px',
                              fontSize: '10px',
                              fontWeight: 'bold',
                              zIndex: 20,
                              boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                              transform: 'scale(0.9)'
                            }}>
                              手动上传
                            </div>
                          </div>
                        )}
                        
                        {/* 上传按钮作为网格中的一项 */}
                        <div 
                          className="image-card-container"
                          style={{ 
                            position: 'relative', 
                            borderRadius: '8px',
                            padding: '4px',
                            background: 'transparent',
                            border: '3px solid transparent',
                            transition: 'all 0.3s ease',
                            cursor: 'pointer',
                            boxShadow: 'none',
                            width: '120px',
                            minHeight: '120px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          <Upload
                            action={UPLOAD_URL}
                            listType="picture-card"
                            fileList={uploadFileList[poseKey] || []}
                            onPreview={handlePreview}
                            onChange={handleFileChange(poseKey)}
                            beforeUpload={convertPngToJpg}
                            accept={'.jpg, .jpeg, .png'}
                            maxCount={1}
                            showUploadList={{
                              showPreviewIcon: true,
                              showRemoveIcon: true,
                              showDownloadIcon: false,
                            }}
                            style={{ 
                              width: 'auto',
                              height: 'auto'
                            }}
                          >
                            {(!uploadFileList[poseKey] || uploadFileList[poseKey].length === 0) && (
                              <div style={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '120px',
                                height: '120px',
                                border: '2px dashed #d9d9d9',
                                borderRadius: '6px',
                                cursor: 'pointer',
                                transition: 'all 0.3s ease',
                                background: '#fff'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.borderColor = PRIMARY_COLOR;
                                e.currentTarget.style.background = PRIMARY_COLOR_LIGHT;
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.borderColor = '#d9d9d9';
                                e.currentTarget.style.background = '#fff';
                              }}
                              >
                                <UploadOutlined style={{ 
                                  fontSize: '20px', 
                                  color: '#666',
                                  marginBottom: '6px'
                                }} />
                                <div style={{ 
                                  fontSize: '11px', 
                                  color: '#666',
                                  textAlign: 'center',
                                  lineHeight: '1.2'
                                }}>
                                  上传图片<br />
                                  <span style={{ fontSize: '9px', color: '#999' }}>
                                    jpg/png
                                  </span>
                                </div>
                              </div>
                            )}
                          </Upload>
                        </div>
                      </div>
                      
                      {/* 当没有图片时显示空状态 */}
                      {poseImages.length === 0 && (
                        <div style={{ 
                          textAlign: 'center', 
                          color: '#999', 
                          fontSize: '12px',
                          marginTop: '16px',
                          marginBottom: '16px'
                        }}>
                          暂无{poseName}的图片数据，可直接上传新图片
                        </div>
                      )}
                    </div>
                  </Card>
                );
                 })
               )}
             </div>
          )}
        </div>


      </Modal>

      {/* 图片预览 */}
      {previewImg !== null && (
        <ImgPreview
          previewVisible={!!previewImg}
          handleCancelPreview={() => {
            setPreviewImg(null);
            setPreviewImgList([]);
          }}
          previewImage={previewImg}
          needSwitch={!!previewImgList}
          previewIdx={previewImgList.findIndex(item => item === previewImg)}
          previewImgs={previewImgList}
          showTools={true}
          showBatchInfo={true}
        />
      )}
    </>
  );
};

export default ManualVerificationModal; 