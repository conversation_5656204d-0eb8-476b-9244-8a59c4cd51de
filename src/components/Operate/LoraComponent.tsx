import {
  ClothColorDetail,
  enableColor,
  MaterialModel,
  relabelModel,
  retrainModel,
  supplyColor,
  updateColorImage,
} from '@/services/MaterialModelController';
import { Button, Checkbox, Dropdown, Flex, Input, message, Modal, Popconfirm, Tooltip, Tag } from 'antd';
import React, { useEffect, useState } from 'react';
import { getMaterialInfoById } from '@/services/MaterialInfoController';
import { deepCopy } from '@/utils/utils';
import { LabelTypeItems } from '@/components/Lora/LoraPromptsSwitch';
import { HighlightOutlined, InteractionOutlined, CloseOutlined } from '@ant-design/icons';

interface ClothColorTitleProps {
  item: ClothColorDetail;
  model: MaterialModel | null | undefined;
  onChange: () => void;
}

export const ClothColorTitle: React.FC<ClothColorTitleProps> = ({ item, model, onChange }) => {
  const [updateIndex, setUpdateIndex] = useState<null | number | undefined>(null);
  const [originImages, setOriginImages] = useState<string[]>([]);
  const [updateImage, setUpdateImage] = useState<string | null>(null);
  const [detail, setDetail] = useState(item);
  const [colorChecked, setColorChecked] = useState(true);

  const infoId = model?.clothLoraTrainDetail.originalMaterialId;

  useEffect(() => {
    setDetail(item);
    setColorChecked(item.enable);
  }, [item]);

  const handleShowUpdateImage = (index: number) => {
    getMaterialInfoById(infoId).then(res => {
      if (res) {
        setOriginImages(res?.materialDetail?.fullShotImgList?.map(item => item.imgUrl) || []);
        setUpdateIndex(index);
      }
    });
  };

  const handleUpdateImage = () => {
    if (!updateImage || !updateIndex) {
      message.warning('请选择图片');
      return;
    }

    updateColorImage({
      id: model?.id, index: updateIndex, imgUrl: updateImage,
    }).then(res => {
      if (res) {
        message.success('修改成功');
        setUpdateImage(null);
        setUpdateIndex(null);
        if (detail) {
          const copy = deepCopy(detail);
          copy.showImage = updateImage;
          setDetail(copy);
        }
        onChange();
      }
    });
  };

  const handleEnableColor = (item) => {
    enableColor({ id: model?.id, index: item.index, enable: colorChecked }).then(res => {
      if (res) {
        message.success(`将颜色${item.index}设置为${colorChecked ? '可用' : '不可用'}成功`);
        onChange();
      }
    });
  };

  return <Flex vertical justify={'flex-start'} align={'center'}>

    <Flex align={'center'} gap={2}>
      <Popconfirm title={'会影响在前台的展示，是否确认'} onConfirm={() => handleEnableColor(detail)}>
        <Checkbox onChange={e => setColorChecked(e.target.checked)} checked={colorChecked} />
      </Popconfirm>
      {!detail.desc &&
        <div>颜色{detail.index}</div>
      }
      {detail.desc &&
        <div className={'color-error'}>{detail.desc}</div>
      }
    </Flex>

    {detail.showImg &&
      <Tooltip title={`点击更换颜色${detail.index}图片`}>
        <div onClick={() => handleShowUpdateImage(detail.index)} style={{ cursor: 'pointer' }}>
          <img src={detail.showImg} alt={detail.name} width={40} />
        </div>
      </Tooltip>
    }
    <div onClick={() => handleShowUpdateImage(detail.index)} style={{ cursor: 'pointer' }}>补充激活词</div>
    {updateIndex &&
      <Modal open={true} onCancel={() => setUpdateIndex(null)} onOk={handleUpdateImage} closeIcon={false}
             title={`变更颜色${updateIndex}图片`} width={800}>
        <Flex wrap={'wrap'} gap={12}>
          {
            originImages.map(item => {
              return <img key={item} src={item} alt={item} width={80}
                          onClick={() => setUpdateImage(item)} style={{ borderRadius: 8 }}
                          className={updateImage === item ? 'border-selected' : ''} />;
            })
          }
        </Flex>
      </Modal>
    }
  </Flex>;
};

export const ModifyColorConfig: React.FC<{
  onChange?: () => void;
}> = ({ onChange }) => {
  const [open, setOpen] = useState(false);
  const [ids, setIds] = useState('');

  const handleModify = () => {
    if (!ids) {
      message.warning('请输入服装id');
      return;
    }
    supplyColor(ids).then(res => {
      if (res) {
        message.success('修改成功');
        if (onChange) {
          onChange();
        }
        setOpen(false);
      }
    });
  };

  return <Flex>
    <Button style={{ padding: 8, fontSize: 12 }} type={'dashed'}
            onClick={() => setOpen(true)}>重置缺失颜色</Button>

    {open &&
      <Modal open={true} onCancel={() => setOpen(false)} title={'重置缺失颜色'}
             onOk={handleModify}>
        <Flex vertical>
          <div>服装id：</div>
          <div>
            <Input placeholder={'多个以英文逗号隔开'} style={{ width: 470 }} onChange={e => setIds(e.target.value)} />
          </div>
        </Flex>

      </Modal>
    }
  </Flex>;
};


export const BatchProcessBtn: React.FC<{
  title: string;
  placeholder?: string;
  batchMethod: (ids: string) => Promise<any>;
  fieldTitle?: string;
  onChange?: () => void;
}> = ({ title, batchMethod, fieldTitle = 'id列表', onChange, placeholder='多个以英文逗号隔开' }) => {
  const [open, setOpen] = useState(false);
  const [ids, setIds] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [inputValue, setInputValue] = useState('');

  // 处理输入值变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    
    // 检测是否包含逗号，如果有则自动分割
    if (value.includes(',')) {
      const newTags = value.split(',')
        .map(tag => tag.trim())
        .filter(tag => tag && !tags.includes(tag));
      
      if (newTags.length > 0) {
        const updatedTags = [...tags, ...newTags];
        setTags(updatedTags);
        setIds(updatedTags.join(','));
        setInputValue('');
      }
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      const trimmedValue = inputValue.trim();
      if (trimmedValue && !tags.includes(trimmedValue)) {
        const updatedTags = [...tags, trimmedValue];
        setTags(updatedTags);
        setIds(updatedTags.join(','));
        setInputValue('');
      }
    }
  };

  // 删除标签
  const removeTag = (tagToRemove: string) => {
    const updatedTags = tags.filter(tag => tag !== tagToRemove);
    setTags(updatedTags);
    setIds(updatedTags.join(','));
  };

  // 处理粘贴事件
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData('text');
    const newTags = pastedText.split(',')
      .map(tag => tag.trim())
      .filter(tag => tag && !tags.includes(tag));
    
    if (newTags.length > 0) {
      const updatedTags = [...tags, ...newTags];
      setTags(updatedTags);
      setIds(updatedTags.join(','));
    }
  };

  // 重置状态
  const resetForm = () => {
    setIds('');
    setTags([]);
    setInputValue('');
  };

  const handleModify = () => {
    if (!ids) {
      message.warning(`请输入${fieldTitle}`);
      return;
    }
    batchMethod(ids).then(res => {
      if (res) {
        message.success('处理成功');
        if (onChange) {
          onChange();
        }
        setOpen(false);
        resetForm();
      }
    });
  };

  const handleCancel = () => {
    setOpen(false);
    resetForm();
  };

  return <Flex>
    <Button style={{ padding: 8, fontSize: 12 }} type={'dashed'}
            onClick={() => setOpen(true)}>{title}</Button>

    {open &&
      <Modal 
        open={true} 
        onCancel={handleCancel} 
        title={
          <div style={{ 
            fontSize: 16, 
            fontWeight: 600, 
            color: '#262626',
            display: 'flex',
            alignItems: 'center',
            gap: 8
          }}>
            {title}
          </div>
        }
        onOk={handleModify} 
        width={650}
        okText="确认"
        cancelText="取消"
        bodyStyle={{ padding: '20px 24px' }}
      >
        <Flex vertical gap={16}>
          <div style={{ 
            fontSize: 14, 
            fontWeight: 500, 
            color: '#595959',
            marginBottom: 4
          }}>
            {fieldTitle}
          </div>
          
                     <div style={{
             minHeight: 120,
             maxHeight: 300,
             border: '1px solid #d9d9d9',
             borderRadius: 8,
             padding: 12,
             backgroundColor: '#fafafa',
             transition: 'border-color 0.3s',
             overflowY: 'auto',
             position: 'relative'
           }}>
            {/* 显示已添加的标签 */}
            {tags.length > 0 && (
              <div style={{ 
                marginBottom: 12,
                display: 'flex',
                flexWrap: 'wrap',
                gap: 6
              }}>
                                 {tags.map((tag, index) => {
                   // 处理长文本显示
                   const getDisplayText = (text: string) => {
                     if (text.length <= 30) return text;
                     
                     // 如果是路径格式，尝试显示文件名部分
                     if (text.includes('/')) {
                       const parts = text.split('/');
                       const fileName = parts[parts.length - 1];
                       if (fileName.length <= 25) {
                         return `.../${fileName}`;
                       }
                       return `${text.substring(0, 15)}...${text.substring(text.length - 10)}`;
                     }
                     
                     // 普通长文本截断
                     return `${text.substring(0, 15)}...${text.substring(text.length - 10)}`;
                   };
                   
                   return (
                     <Tooltip 
                       key={tag} 
                       title={
                         <div style={{ whiteSpace: 'nowrap' }}>
                           {tag}
                         </div>
                       } 
                       placement="top"
                       overlayStyle={{ maxWidth: 'none' }}
                     >
                       <Tag
                         closable
                         closeIcon={<CloseOutlined style={{ fontSize: 10 }} />}
                         onClose={() => removeTag(tag)}
                         style={{ 
                           marginBottom: 0,
                           padding: '4px 8px',
                           borderRadius: 6,
                           backgroundColor: '#e6f7ff',
                           border: '1px solid #91d5ff',
                           color: '#096dd9',
                           fontSize: 12,
                           fontWeight: 500,
                           display: 'flex',
                           alignItems: 'center',
                           gap: 4,
                           cursor: 'pointer',
                           transition: 'all 0.2s',
                           maxWidth: '200px',
                           wordBreak: 'break-all',
                           whiteSpace: 'nowrap',
                           overflow: 'hidden',
                           textOverflow: 'ellipsis'
                         }}
                         onMouseEnter={(e) => {
                           e.currentTarget.style.backgroundColor = '#bae7ff';
                           e.currentTarget.style.borderColor = '#69c0ff';
                         }}
                         onMouseLeave={(e) => {
                           e.currentTarget.style.backgroundColor = '#e6f7ff';
                           e.currentTarget.style.borderColor = '#91d5ff';
                         }}
                       >
                         <span style={{ 
                           overflow: 'hidden',
                           textOverflow: 'ellipsis',
                           whiteSpace: 'nowrap',
                           maxWidth: '160px',
                           display: 'inline-block'
                         }}>
                           {getDisplayText(tag)}
                         </span>
                       </Tag>
                     </Tooltip>
                   );
                 })}
              </div>
            )}
            
            {/* 输入框 */}
            <Input
              value={inputValue}
              placeholder={placeholder}
              style={{ 
                width: '100%',
                border: 'none',
                backgroundColor: 'transparent',
                boxShadow: 'none',
                padding: '8px 0'
              }}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onPaste={handlePaste}
            />
          </div>
          
          {/* 提示信息和统计 */}
          <Flex justify="space-between" align="center">
            <div style={{ 
              fontSize: 12, 
              color: '#8c8c8c',
              display: 'flex',
              alignItems: 'center',
              gap: 4
            }}>
              <span>💡</span>
              输入后按回车或逗号添加，支持直接粘贴逗号分隔的内容
            </div>
            {tags.length > 0 && (
              <div style={{
                fontSize: 12,
                color: '#595959',
                backgroundColor: '#f0f0f0',
                padding: '2px 8px',
                borderRadius: 10,
                fontWeight: 500
              }}>
                已添加: {tags.length} 项
              </div>
            )}
          </Flex>
        </Flex>
      </Modal>
    }
  </Flex>;
};

export const RetrainBtn: React.FC<{ id: number; showTitle?: string; materialType?: string; onlyLabel?: boolean; needUpscale?: boolean | null; onChange?: () => void; }>
  = ({ id, showTitle, materialType = 'cloth', onlyLabel = false, needUpscale = null, onChange }) => {
  const title = onlyLabel ? '抠图打标' : '训练';
  const labelTypeItems = LabelTypeItems[materialType];
  let items = [];
  if (labelTypeItems) {
    items = labelTypeItems.map(item => {
      return {
        key: item.tag,
        label: (<Popconfirm onConfirm={() => handleProcess(item.tag)}
                            title={`确定要以${item.label}方式重新${title}当前模型吗？模型将对客不可用`}>
          {item.label}
        </Popconfirm>),
      };
    });
  }

  const handleProcess = (labelType: string | null) => {
    const method = onlyLabel ? relabelModel : retrainModel;
    method(id, labelType, needUpscale === null ? null : (needUpscale ? 'Y' : 'N')).then(res => {
      if (res) {
        message.success(`重新${title}成功`);
        if (onChange) {
          onChange();
        }
      }
    });
  };

  return <Tooltip
    title={`重新以最新参数进行抠图打标，${onlyLabel ? '训练前需要人工确认' : '不经过人工确认直接开始训练'}`}>
    {items.length <= 1 && onlyLabel &&
      <Popconfirm title={`以最新的参数进行重新${title}，${onlyLabel ? '但不自动训练，' : ''}线上将不可用`}
                  onConfirm={() => handleProcess(null)}>
        {showTitle? showTitle : <HighlightOutlined style={{ color: '#727375', fontSize: 16 }} />}
      </Popconfirm>
    }
    {items.length <= 1 && !onlyLabel &&
      <Popconfirm title={`以最新的参数进行重新${title}，${onlyLabel ? '但不自动训练，' : ''}线上将不可用`}
                  onConfirm={() => handleProcess(null)}>
        {showTitle? showTitle : <InteractionOutlined style={{ color: '#727375', fontSize: 16 }} />}
      </Popconfirm>
    }
    {items.length > 1 && onlyLabel &&
      <Dropdown menu={{ items }} trigger={['click']}>
        {showTitle? showTitle : <HighlightOutlined style={{ color: '#727375', fontSize: 16 }} />}
      </Dropdown>
    }
    {items.length > 1 && !onlyLabel &&
      <Dropdown menu={{ items }} trigger={['click']}>
        {showTitle? showTitle : <InteractionOutlined style={{ color: '#727375', fontSize: 16 }} />}
      </Dropdown>
    }
  </Tooltip>;
};