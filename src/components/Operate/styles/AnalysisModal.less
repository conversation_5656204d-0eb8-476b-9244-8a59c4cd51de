.detail-modal {
  .modal-title {
    display: flex;
    align-items: center;
    gap: 12px;

    .comparing-status {
      font-size: 14px;
      color: #999;
    }
  }

  .detail-modal-content {
    .detail-section {
      background: #fff;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .detail-section-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 24px;

        .title-indicator {
          width: 4px;
          height: 16px;
          background: #1890ff;
          margin-right: 8px;
          border-radius: 2px;
        }
      }

      .detail-item {
        .detail-label {
          color: #666;
          margin-bottom: 8px;
        }

        .detail-value {
          font-weight: 500;
        }
      }
    }

    .analysis-section {
      margin-bottom: 32px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 16px;
        color: #333;
      }

      .charts-container {
        display: flex;
        gap: 24px;

        .pie-chart-container {
          flex: 1;
          min-width: 400px;
        }
      }
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
} 