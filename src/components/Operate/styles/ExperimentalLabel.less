// Experimental label styles
.experimental-label-wrapper {
  position: relative;
  overflow: visible;
  pointer-events: none;
}

.experimental-label {
  position: absolute;
  top: 0;
  right: 0;
  width: 60px;
  height: 60px;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 60px 60px 0;
    border-color: transparent #ffcc00 transparent transparent;
    z-index: 1;
  }
  
  span {
    position: absolute;
    top: 10px;
    right: 5px;
    transform: rotate(45deg);
    color: #333;
    font-size: 12px;
    font-weight: bold;
    z-index: 2;
  }
}
