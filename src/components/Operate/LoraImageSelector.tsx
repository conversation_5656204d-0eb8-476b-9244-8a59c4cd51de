import { Button, Card, Flex, Input, message, Modal, notification, Upload, UploadFile, UploadProps } from 'antd';
import React, { useEffect, useState } from 'react';
import { MinusOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { queryCreativesByPage, queryExampleImages } from '@/services/CreativeController';
import IconFont from '@/components/IconFont';
import ImgPreview from '@/components/ImgPreview';
import { UPLOAD_URL } from '@/constants';
import { FileType, getBase64 } from '@/utils/utils';
import { convertPngToJpg } from '@/utils/imageUtils';
import { clearExampleImages, updateMaterialModel } from '@/services/MaterialModelController';

interface LoraImageSelectorProps {
  modelId: number;
  value?: Array<string>;
  maxChoose?: number;
  onChange?: (value: Array<string>) => void;
}

const LoraImageSelector: React.FC<LoraImageSelectorProps> = ({ modelId, value, maxChoose = 10, onChange }) => {
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [showSelector, setShowSelector] = useState(false);
  const [allImages, setAllImages] = useState<string[]>([]);
  const [allImagesExtMap, setAllImagesExtMap] = useState<any>({});
  const [showImage, setShowImage] = useState('');
  const [showImageList, setShowImageList] = useState<Array<string>>([]);
  const [previewImage, setPreviewImage] = useState<null | string>(null);

  const [fileList, setFileList] = useState<UploadFile[]>([]);

  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

  useEffect(() => {
    return () => {
      //清空数据
      clear();
    };
  }, []);

  useEffect(() => {
    //清空数据
    clear();
    console.log('modelId', modelId);

    queryExampleImages(modelId).then(res => {
      if (res && Array.isArray(res)) {
        setSelectedImages(res);
        onChange?.(res);
      } else {
        setSelectedImages([]);
      }
    });
  }, [modelId]);

  useEffect(() => {
    if (!showSelector || allImages.length > 0) {
      return;
    }

    const queryRoleType = userInfo.roleType === 'DISTRIBUTOR' ? 'DISTRIBUTOR' : 'OPERATOR';

    queryCreativesByPage({
      modelId: modelId, queryRoleType, status: 'FINISHED',
      pageNum: 1, pageSize: 300,
    }).then((res) => {
      if (res && res.list) {
        let images = Array<string>();
        const imageExtMap = {};
        res.list.forEach((item, index) => {
          if (item.resultImages) {
            const filter = item.resultImages.filter(e => e != null);
            images.push(...filter);
            filter.forEach(e => {
              imageExtMap[e] = (item.faceName ? item.faceName : '') + '/' + (item.sceneName ? item.sceneName : '');
            });
          }
        });
        images = [...new Set(images)];
        setAllImages(images);
        setAllImagesExtMap(imageExtMap);
      }
    });
  }, [showSelector]);

  //清空数据
  const clear = () => {
    setSelectedImages([]);
    setAllImages([]);
    setFileList([]);
    setAllImagesExtMap({});
  };

  const addImage = (image: string) => {
    if ((selectedImages.length + fileList.length) >= maxChoose) {
      message.warning(`最多能选择${maxChoose}张图片`);
      return;
    }

    const newValue = [...selectedImages, image];
    setSelectedImages(newValue);

    onChange?.([...newValue, ...fileList.map(e => e.response.data)]);
  };

  const delImage = (image: string) => {
    const newValue = selectedImages.filter(e => e !== image);
    setSelectedImages(newValue);

    onChange?.([...newValue, ...fileList.map(e => e.response.data)]);
  };

  let isWarning = false;
  const handleFileChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    const file = newFileList[0];

    console.log('success:', file);

    if (file && file.response && !file.response.success) {
      console.log('event:', file);
      notification.error({ message: '上传文件异常，请重试' });
      setFileList([]);
      return;
    }

    if (selectedImages.length + newFileList.length > maxChoose) {
      if (!isWarning) {
        message.error('最多能选择' + maxChoose + '张图片');
        isWarning = true;

        setTimeout(() => isWarning = false, 300);
      }
      return;
    }

    setFileList(newFileList);

    console.log('fileList:', newFileList);
    onChange?.([...selectedImages, ...newFileList.filter(e => e.response != null).map(e => e.response.data)]);
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType);
    }

    setShowImage(file.url || (file.preview as string));
    setShowImageList([]);
  };

  const handleOk = () => {
    updateMaterialModel({ id: modelId, exampleImages: selectedImages }).then(res => {
      if (res) {
        message.success('精选图数据已提交');

        setShowSelector(false);
      }
    });
  };

  const clearAndSave = () => {
    clearExampleImages(modelId).then(res => {
      if (res) {
        setFileList([]);
        setSelectedImages([]);
        if (onChange) {
          onChange([]);
        }
        message.success('精选图数据已清空');
      }
    });
  };

  const ImageCard = ({ image, width, isAll = false }) => {
    return (<>
      <div>
        <img src={image} width={width} alt={''} onClick={() => {
          setShowImage(image);
          setShowImageList(isAll ? allImages : selectedImages);
        }} style={{ cursor: 'pointer' }} />
      </div>
    </>);
  };

  return (<>
    <Flex gap={8} wrap={'wrap'} align={'center'}>
      <Input hidden value={selectedImages} />
      <>

        {selectedImages.length > 0 && selectedImages.map((image, index) => (
          <ImageCard key={index} image={image} width={80} />
        ))}

        {(selectedImages.length + fileList.length) < maxChoose &&
          <Button size={'small'} style={{ width: 100, height: 100 }} type="dashed"
                  onClick={() => setShowSelector(true)}
                  block icon={<PlusOutlined />}>从历史选择</Button>
        }

        {(selectedImages.length + fileList.length) === maxChoose &&
          <Button size={'small'} style={{ width: 100, height: 100 }} type="dashed" onClick={() => setShowSelector(true)}
                  block icon={<PlusOutlined />}>变更</Button>
        }

        <Upload
          action={UPLOAD_URL}
          listType="picture-card"
          multiple={true}
          fileList={fileList}
          onPreview={handlePreview}
          onChange={handleFileChange}
          beforeUpload={convertPngToJpg}
          accept={'.jpg, .jpeg, .png'}
        >
          {(selectedImages.length + fileList.length) >= maxChoose ? null :
            <div><UploadOutlined />上传图片<br />
              <span className={'text12 color-a0'}>支持jpg、jpeg和png格式</span>
            </div>}
        </Upload>

        {(selectedImages.length + fileList.length) > 0 &&
          <Button size={'small'} style={{ width: 100 }} type="dashed" onClick={() => clearAndSave()}
                  block icon={<MinusOutlined />}>清空并保存</Button>
        }
      </>
    </Flex>

    <Modal open={showSelector} onCancel={() => setShowSelector(false)} onOk={handleOk}
           width={'calc(100vw * 0.96 + 10px)'} centered>

      <Flex gap={8} vertical style={{ height: 'calc(100vh - 100px)', overflowY: 'auto' }}>

        <div className={'text16 weight'}>已选择: {selectedImages.length}/{maxChoose}</div>

        <Card style={{ background: '#F5F5F5' }} styles={{ body: { padding: 0 } }}>
          <Flex gap={8} style={{ position: 'relative', width: '100%' }} wrap={'wrap'}>
            <>
              {selectedImages.length > 0 && selectedImages.map((image, index) => (
                <Flex vertical key={index} align={'center'} gap={4}
                      style={{
                        border: '1px dashed #e8e8e8',
                        borderRadius: 8,
                        padding: 4,
                        position: 'relative',
                        height: 'auto',
                      }}>
                  <ImageCard key={index} image={image} width={80} />
                  <Button size={'small'} onClick={() => delImage(image)} block icon={<MinusOutlined />}>删除</Button>
                  <div style={{ position: 'absolute', right: 8, top: 8 }}>
                    <IconFont type={'icon-gou-lan'} style={{ fontSize: 16, color: '#366EF4' }} />
                  </div>
                </Flex>
              ))}

              {(selectedImages.length <= 0) &&
                <Flex vertical gap={8} align={'center'} justify={'center'} className={'width-100'}
                      style={{ padding: 16 }}>
                  <IconFont type={'icon-kongzhuangtai'} style={{ fontSize: 32, color: '#000000' }} />
                  <div className={'text16 font-pf color-b'}>未选择</div>
                </Flex>
              }
            </>
          </Flex>
        </Card>

        <div className={'text16 weight'}>请选择:</div>

        <Flex gap={8} wrap>
          {allImages.length > 0 && allImages.map((image, index) => (
            <Flex vertical key={index} align={'center'} gap={4}
                  style={{
                    border: previewImage === image ? '2px solid #366EF4' : '1px dashed #e8e8e8',
                    borderRadius: 8, padding: 4, position: 'relative',
                  }}>
              <ImageCard image={image} width={120} isAll={true} />

              {!selectedImages.includes(image) &&
                <Button size={'small'} onClick={() => addImage(image)} block icon={<PlusOutlined />}>添加</Button>
              }

              {selectedImages.includes(image) &&
                <Button size={'small'} onClick={() => delImage(image)} block icon={<MinusOutlined />}>删除</Button>
              }

              {selectedImages.includes(image) &&
                <div style={{ position: 'absolute', right: 8, top: 8 }}>
                  <IconFont type={'icon-gou-lan'} style={{ fontSize: 24, color: '#366EF4' }} />
                </div>
              }
            </Flex>
          ))}
        </Flex>
      </Flex>
    </Modal>

    <ImgPreview
      previewVisible={!!showImage}
      handleCancelPreview={(previewImage) => {
        setShowImage('');
        setShowImageList([]);
        setPreviewImage(previewImage);
      }}
      previewImage={showImage}
      needSwitch={!!showImageList}
      previewIdx={showImageList.findIndex(item => item === showImage)}
      previewImgs={showImageList}
      showTools={true}
      imageClick={imageUrl => {
        selectedImages.includes(imageUrl) ? delImage(imageUrl) : addImage(imageUrl);
      }}
      isEnableGou={(previewImage) => selectedImages.includes(previewImage)}
      isBlueGou={true}
      selectedImages={selectedImages}
      modelId={modelId}
      imagesExtMap={allImagesExtMap}
      isExampleImages={true}
    />

  </>);
};

export default LoraImageSelector;