import { Checkbox, Col, Flex, Form, Input, InputNumber, message, Modal, Row, Select } from 'antd';
import { MerchantPreference, updateMerchantPreference } from '@/services/SystemController';
import React, { CSSProperties, useEffect, useState } from 'react';
import { ElementConfig } from '@/services/ElementController';
import ChildrenTabCard from '@/components/Operate/ChildrenTabCard';
import { queryAllMaster, UserVO } from '@/services/UserController';
import { creationSizeOptions } from '@/pages/Operate/Experiment/trainPlan';
import { getUserInfo } from '@/utils/utils';

interface MerchantPreferenceSettingProps {
  userId: number | null;
  userNick: string | null;
  merchantPreference: MerchantPreference | null;
  faceList: Array<ElementConfig>;
  sceneList: Array<ElementConfig>;
  newMerchant?: boolean;
  hasMerchantIds?: Array<number>,
  onCancel: () => void;
  changeMerchantPreference: (data: MerchantPreference) => void;
}

const clothStyle: CSSProperties = { width: 70, padding: 0, textAlign: 'center' };
const AllPreferenceTags: Array<{ key: string; value: string; label: string, style: CSSProperties }> = [
  { key: '1', value: '1', label: '偏好1', style: clothStyle },
  { key: '2', value: '2', label: '偏好2', style: clothStyle },
  { key: '3', value: '3', label: '偏好3', style: clothStyle },
  { key: '4', value: '4', label: '偏好4', style: clothStyle },
  { key: '5', value: '5', label: '偏好5', style: clothStyle },
];

const AllCreativePreferenceTags: Array<{ key: string; value: string; label: string, style: CSSProperties }> = [
  { key: 'female', value: 'female', label: '女模偏好', style: clothStyle },
  { key: 'male', value: 'male', label: '男模偏好', style: clothStyle },
];

const userInfo = getUserInfo();
export const ElementSelector: React.FC<{
  value?: number[],
  onChange?: (value: number[]) => void,
  elementList: Array<ElementConfig>,
  maxCount?: number,
  placeholder?: string
}> = ({
        value = null,
        onChange = undefined,
        elementList,
        maxCount = undefined,
        placeholder = undefined,
      }) => {

  let valueList: number[] = [];
  if (value) {
    valueList = (value as []).map(item => parseInt(item));
  }

  const filterOption = (input, option) => {
    console.log('filterOption', input, option.children['props'].item.name);
    if (option && option.children && option.children['props'] && option.children['props'].item) {
      return option.children['props'].item.name.toLowerCase().includes(input.toLowerCase());
    }
    return false;
  };

  const ElementOptionLabel = ({ item, size, objectFit }) => {
    return <div style={{ display: 'flex', alignItems: 'center' }}>
      <img src={item.showImage} alt={item.name}
           style={{ width: size, height: size, marginRight: 8, objectFit: objectFit }} />
      {item.name} {['OPERATOR', 'ADMIN'].includes(userInfo?.roleType || '') ? (
        (item.extInfo && (item.extInfo['experimental'] === 'true' || item.extInfo['experimental'] === true)) ?
          <div className={'color-error text12'}>(实验中)</div> :
          (item.status === 'PROD' ? '' : <div className={'color-error text12'}>(测试中)</div>
        )) : ''}
    </div>;
  };

  return <Select style={{ width: '100%' }} mode="tags" showSearch={true} maxCount={maxCount}
                 placeholder={placeholder}
                 filterOption={(input, option) => filterOption(input, option)}
                 optionLabelProp="label" value={valueList} onChange={onChange}>
    {elementList.map((item: ElementConfig) => (
      <Select.Option value={item.id} key={item.id}
                     label={<ElementOptionLabel item={item} size={20} objectFit={null} />}>
        <ElementOptionLabel item={item} size={60} objectFit={'contain'} />
      </Select.Option>
    ))}
  </Select>;
};

const MerchantPreferenceSetting: React.FC<MerchantPreferenceSettingProps> = ({
                                                                               userId,
                                                                               userNick,
                                                                               merchantPreference,
                                                                               faceList,
                                                                               sceneList,
                                                                               newMerchant = false,
                                                                               hasMerchantIds = [],
                                                                               onCancel,
                                                                               changeMerchantPreference,
                                                                             }) => {
  const [masterOptions, setMasterOptions] = useState<Array<any>>([]);
  const [newUserId, setNewUserId] = useState<number | null>(null);
  const [enableAutoCreative, setEnableAutoCreative] = useState<boolean>(false);
  const [form] = Form.useForm();

  useEffect(() => {
    form.resetFields();
    //兼容ChildrenTabCard的要求
    if (merchantPreference && merchantPreference.preferences) {
      merchantPreference.preferences.forEach((e, index) => {
        //@ts-ignore
        e.type = [(index + 1) + ''];
      });
    }
    form.setFieldsValue(merchantPreference ? merchantPreference : {});

    if (newMerchant) {
      initMasterOptions();
    }
  }, []);

  const handleFinish = async () => {
    const row = (await form.validateFields()) as MerchantPreference;
    const payload = { ...row, userId: (newMerchant ? newUserId : userId) };
    if(payload.preferences){
      payload.preferences.forEach(e => e.type = 'AUTO_DELIVERY');
    }
    if (payload.creativePreferences) {
      for (let i = 0; i < payload.creativePreferences.length; i++) {
        const e = payload.creativePreferences[i];

        e.type = 'CREATIVE';
        if (e.enableAutoCreative) {
          if (!e.imageNum || e.imageNum > 100) {
            message.error('请设置图片数量,数量要求小于100');
            return;
          }

          if (!e.imageProportion) {
            message.error('请设置图片比例');
            return;
          }

          if (!e.faces || !e.scenes) {
            message.error('请设置人脸和场景');
            return;
          }
        }
      }
    }

    updateMerchantPreference(payload).then(res => {
      if (res) {
        message.success('商家偏好修改成功');
        changeMerchantPreference(row);
        onCancel();
      }
    });
  };

  function initMasterOptions() {
    if (masterOptions && masterOptions.length > 0) {
      return;
    }

    queryAllMaster(['MERCHANT', 'OPERATOR', 'SYSTEM']).then(res => {
      if (res && Array.isArray(res)) {
        const masterOptions = [];
        res.filter(item => !hasMerchantIds.includes(item.id)).forEach(item => {
          // @ts-ignore
          masterOptions.push({
            label: item.nickName + (item.corpName ? '@' + item.corpName : ''),
            value: item.id + '',
          });
        });

        setMasterOptions(masterOptions);
      }
    });
  }

  return <Modal open={true} centered title={userNick ? `设置商家"${userNick}"的偏好` : '设置新增商家偏好'}
                onCancel={onCancel} onOk={form.submit} width={800} maskClosable={false}>
    <Form form={form} onFinish={handleFinish}>
      {newMerchant &&
        <Select options={masterOptions} style={{ width: 200, margin: '12px 0' }} showSearch allowClear
                placeholder={'选择商家'}
                optionFilterProp="label" defaultActiveFirstOption={true} value={newUserId}
                onChange={(e) => setNewUserId(e)} />
      }

      <div className={'color-1a text14 weight'} style={{ margin: '8px 0' }}>运营偏好：</div>

      <ChildrenTabCard children={form.getFieldValue('preferences')} opItemId={userId} name={'preferences'}
                       allTabList={AllPreferenceTags} form={form}
                       onChangeChildren={children => form.setFieldValue('preferences', children)}
                       formItems={(name, restField) => (<>

                         <Form.Item label="备注：" name={[name, 'memo']} style={{ width: '100%' }} {...restField}>
                           <Input placeholder={'请输入商家偏好（中文）'} />
                         </Form.Item>
                         <Form.Item label="偏好模特：" name={[name, 'faces']} {...restField}>
                           <ElementSelector elementList={faceList} />
                         </Form.Item>

                         <Form.Item label="偏好场景：" name={[name, 'scenes']} {...restField}>
                           <ElementSelector elementList={sceneList} />
                         </Form.Item>

                         <Row style={{ marginTop: -12 }}>
                           <Col span={12}>
                             <Form.Item name={[name, 'clothCollocation', 'tops']} label="上装" {...restField}>
                               <Input placeholder="输入上装(英文)" />
                             </Form.Item>
                           </Col>
                           <Col span={12}>
                             <Form.Item name={[name, 'clothCollocation', 'bottoms']} label="下装" {...restField}>
                               <Input placeholder="输入下装(英文)" />
                             </Form.Item>
                           </Col>
                         </Row>
                         <Row style={{ marginTop: -12 }}>
                           <Col span={12}>
                             <Form.Item name={[name, 'clothCollocation', 'shoe']} label="鞋子" {...restField}>
                               <Input placeholder="输入鞋子(英文)" />
                             </Form.Item>
                           </Col>
                           <Col span={12}>
                             <Form.Item name={[name, 'clothCollocation', 'others']} label="其它" {...restField}>
                               <Input placeholder="输入其他(英文)" />
                             </Form.Item>
                           </Col>
                         </Row>
                         <Row style={{ marginTop: -12 }}>
                           <Col span={24}>
                             <Form.Item name={[name, 'clothCollocation', 'props']} label="道具" {...restField}>
                               <Input placeholder="输入道具(英文)" />
                             </Form.Item>
                           </Col>
                         </Row>
                       </>)}
      />

      <div className={'color-1a text14 weight'} style={{ margin: '8px 0' }}>商家创作默认搭配：</div>

      <ChildrenTabCard children={form.getFieldValue('creativePreferences')} opItemId={userId}
                       name={'creativePreferences'} tagsName={'tags'}
                       allTabList={AllCreativePreferenceTags} form={form}
                       onChangeChildren={children => form.setFieldValue('creativePreferences', children)}
                       formItems={(name, restField) => (<>

                         <Row style={{ marginTop: -12 }}>
                           <Col span={12}>
                             <Form.Item name={[name, 'clothCollocation', 'tops']} label="上装" {...restField}>
                               <Input placeholder="输入上装(英文)" />
                             </Form.Item>
                           </Col>
                           <Col span={12}>
                             <Form.Item name={[name, 'clothCollocation', 'bottoms']} label="下装" {...restField}>
                               <Input placeholder="输入下装(英文)" />
                             </Form.Item>
                           </Col>
                         </Row>
                         <Row style={{ marginTop: -12 }}>
                           <Col span={12}>
                             <Form.Item name={[name, 'clothCollocation', 'shoe']} label="鞋子" {...restField}>
                               <Input placeholder="输入鞋子(英文)" />
                             </Form.Item>
                           </Col>
                           <Col span={12}>
                             <Form.Item name={[name, 'clothCollocation', 'others']} label="其它" {...restField}>
                               <Input placeholder="输入其他(英文)" />
                             </Form.Item>
                           </Col>
                         </Row>
                         <Row style={{ marginTop: -12 }}>
                           <Col span={24}>
                             <Form.Item name={[name, 'clothCollocation', 'props']} label="道具" {...restField}>
                               <Input placeholder="输入道具(英文)" />
                             </Form.Item>
                           </Col>
                         </Row>

                         <Flex vertical style={{ border: '1px solid #d9d9d9', padding: 8, borderRadius: 8 }}>
                           <Flex gap={24}>
                             <Form.Item label="开启自动出图" name={[name, 'enableAutoCreative']} {...restField}
                                        valuePropName={'checked'}>
                               <Checkbox />
                             </Form.Item>

                             <Form.Item label="不扣点数" name={[name, 'extInfo', 'withoutDeduction']} {...restField}
                                        valuePropName={'checked'}>
                               <Checkbox />
                             </Form.Item>

                             <Form.Item name={[name, 'imageNum']} label="每角度出图张数" {...restField}>
                               <InputNumber placeholder="请输入出图张数" style={{ width: 100 }} />
                             </Form.Item>


                             <Form.Item name={[name, 'imageProportion']} label="出图尺寸" {...restField}>
                               <Select options={creationSizeOptions} placeholder="选择出图尺寸"
                                       style={{ width: 140 }} />
                             </Form.Item>
                           </Flex>

                           <Form.Item label="偏好模特" name={[name, 'faces']} {...restField}>
                             <ElementSelector elementList={faceList} maxCount={1} />
                           </Form.Item>

                           <Form.Item label="偏好场景" name={[name, 'scenes']} {...restField}>
                             <ElementSelector elementList={sceneList} maxCount={2} />
                           </Form.Item>
                         </Flex>

                       </>)}
      />


    </Form>
  </Modal>;

};

export default MerchantPreferenceSetting;