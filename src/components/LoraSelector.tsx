import { Checkbox, Flex, Input, InputNumber, Modal, Pagination, Radio, Segmented, Select, Dropdown, Tooltip } from 'antd';
import {
  AllModelStatus,
  getClothMarkIcon,
  MaterialModel,
  queryMaterialModelByPage,
  querySystemModelList,
} from '@/services/MaterialModelController';
import React, { useCallback, useEffect, useState, useRef, useMemo } from 'react';
import './LoraSelector.less';
import { LoraType } from '@/services/CreativeController';
import { queryLoras4Distributor } from '@/services/DistributorController';
import IconFont from '@/components/IconFont';
import { AllAgeRanges, CreationClothTypeCheckBoxOptions } from '@/services/ElementController';
import { DEBOUNCE_DELAY } from '@/constants';
import debounce from 'lodash/debounce';


const { Option } = Select;

interface LoraSelectorProps {
  show: boolean;
  isOwner: boolean;
  type: LoraType;
  principalId?: number | null;
  authorized?: boolean;
  onChange?: (model: MaterialModel) => void;
  onCancel?: () => void;
}

const LoraSelector: React.FC<LoraSelectorProps> = ({ show, isOwner, principalId, type, authorized, onChange, onCancel }) => {
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const [showModal, setShowModal] = useState(false);
  const [isOwnerType, setIsOwnerType] = useState(isOwner);
  const [lora, setLora] = useState<MaterialModel | null>(null);
  const [loraList, setLoraList] = useState<Array<MaterialModel>>([]);
  const [modelType, setModelType] = useState<LoraType>('CUSTOM');

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(18);
  const [searchKey, setSearchKey] = useState<string | null>(null);
  const [searchValue, setSearchValue] = useState<string>(''); // 用于输入框显示的值
  const [searchId, setSearchId] = useState<number | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(['OPERATOR', 'ADMIN'].includes(userInfo.roleType) ? null : 'ENABLED');
  const [onlyShowV2, setOnlyShowV2] = useState(false);
  const [searchClothType, setSearchClothType] = useState<string>();
  const [searchAgeRange, setSearchAgeRange] = useState<string>();
  const [onlyShowDemo, setOnlyShowDemo] = useState(false);
  const [onlyShowColorSplit, setOnlyShowColorSplit] = useState(false);
  const [onlyExperimental, setOnlyExperimental] = useState(false);
  const [typeSelectOpen, setTypeSelectOpen] = useState(false);
  const [ageSelectOpen, setAgeSelectOpen] = useState(false);

  // 使用静态年龄范围选项
  const dynamicAgeRanges = useMemo(() => {
    return AllAgeRanges;
  }, []);

  // 防抖搜索函数
  const debouncedSearchKey = useCallback(
    debounce((value: string) => {
      setSearchKey(value || null);
    }, DEBOUNCE_DELAY),
    []
  );

  const handlePageChange = (page: number, pageSize?: number) => {
    setPage(page);
    if (pageSize) {
      setPageSize(pageSize);
    }
  };

  const handleCancel = () => {
    setShowModal(false);
    if (onCancel) {
      onCancel();
    }
  };

  const queryLoraList = useCallback(
    debounce(async (isOwner: boolean, page: number, pageSize: number, type, searchKey, searchId, selectedStatus, onlyShowV2, searchClothType, onlyShowDemo, onlyExperimental, searchAgeRange, onlyShowColorSplit) => {
      const ownerField = isOwner ? { isOwner } : {};
      const statusList = selectedStatus ? [selectedStatus] : ['ENABLED', 'TESTING'];
      const nameLike = searchKey ? searchKey.trim() : null;
      const userId = userInfo.roleType === 'DISTRIBUTOR' && principalId ? principalId : null;
      const queryParam = {
        ...ownerField, statusList, pageNum: page, pageSize, onlyShowV2,
        type, needModelPoint: true, nameLike, userId, id: searchId ? searchId : null,
        clothStyleType: searchClothType && searchClothType !== 'All' ? searchClothType.toLowerCase() : null,
        materialType: 'cloth', onlyShowDemo, onlyExperimental,
        ageRange: searchAgeRange && searchAgeRange !== 'ALL' ? searchAgeRange : null,
        onlyShowColorSplit,
      };
      let method = type === 'CUSTOM' ? queryMaterialModelByPage : querySystemModelList;

      if (type === 'CUSTOM' && userInfo.roleType === 'DISTRIBUTOR') {
        if (!authorized && !principalId) {
          setLoraList([]);
          return;
        }
        method = queryLoras4Distributor;
      }

      method(queryParam).then((res) => {
        if (res) {
          setLoraList(res.list || []);
          if (res?.totalCount !== null) {
            setTotal(res?.totalCount ?? 0);
          }
        }
      });
    }, 300), []);

  const changeLoraType = (newValue: boolean) => {
    if (isOwnerType === newValue) {
      return;
    }
    setIsOwnerType(newValue);
    setLora(null);
    setPage(1);
  };

  const handleConfirm = (lora) => {
    setShowModal(false);
    if (onChange) {
      onChange(lora!);
    }
  };

  useEffect(() => {
    if (!show) {
      return;
    }
    setShowModal(show);
    setIsOwnerType(isOwnerType);
    setLora(null);
    setModelType(type);
    queryLoraList(isOwnerType, page, pageSize, type, searchKey, searchId, selectedStatus, onlyShowV2, searchClothType, onlyShowDemo, onlyExperimental, searchAgeRange, onlyShowColorSplit);
  }, [page, pageSize]);

  useEffect(() => {
    if (!show) {
      return;
    }
    setPage(1);
    setShowModal(show);
    setIsOwnerType(isOwnerType);
    setLora(null);
    setModelType(type);
    queryLoraList(isOwnerType, page, pageSize, type, searchKey, searchId, selectedStatus, onlyShowV2, searchClothType, onlyShowDemo, onlyExperimental, searchAgeRange, onlyShowColorSplit);
  }, [show, isOwnerType, type, searchKey, searchId, selectedStatus, onlyShowV2, searchClothType, onlyShowDemo, onlyExperimental, queryLoraList, searchAgeRange, onlyShowColorSplit]);

  useEffect(() => {
    setIsOwnerType(isOwner);
  }, [isOwner]);

  // 组件卸载时清理防抖函数
  useEffect(() => {
    return () => {
      debouncedSearchKey.cancel();
    };
  }, [debouncedSearchKey]);

  return (
    <Modal open={showModal} onCancel={handleCancel} title={'选择服装'} footer={null}
      width={1200} closable={true} maskClosable={false} centered
      styles={{ header: { display: 'flex', flexDirection: 'row', justifyContent: 'center' } }}>
      <Flex vertical gap={16}>
        {modelType === 'CUSTOM' &&
          <Flex justify={'space-between'}>
            {userInfo.roleType !== 'DISTRIBUTOR' &&
              <Segmented
                defaultValue={isOwnerType}
                style={{ backgroundColor: '#E1E3EB', width: 'auto' }}
                onChange={(value) => changeLoraType(value as boolean)}
                options={[{ label: '我的服装', value: true }, { label: '团队服装', value: false }]}
              />
            }
            {userInfo.roleType === 'DISTRIBUTOR' &&
              <div />
            }

            <Flex gap={8} align={'center'}>
              {['OPERATOR', 'ADMIN', 'DEMO_ACCOUNT'].includes(userInfo.roleType) &&
                <>
                  <Dropdown
                    trigger={['hover']}
                    placement="bottomLeft"
                    open={typeSelectOpen}
                    onOpenChange={(visible) => setTypeSelectOpen(visible)}
                    dropdownRender={() => (
                      <div style={{ backgroundColor: 'white', boxShadow: '0 2px 8px rgba(0,0,0,0.15)', borderRadius: 4, padding: 4, maxHeight: 300, overflow: 'auto' }}>
                        {CreationClothTypeCheckBoxOptions.filter((item: any) => item.value !== 'Common' && item.value !== 'Unset').map((item: any) => (
                          <div
                            key={item.value}
                            style={{ padding: '5px 12px', cursor: 'pointer', borderRadius: 4, textAlign: 'left' }}
                            onClick={() => {
                              setSearchClothType(item.value);
                              setTypeSelectOpen(false);
                            }}
                            onMouseEnter={e => e.currentTarget.style.backgroundColor = '#f5f5f5'}
                            onMouseLeave={e => e.currentTarget.style.backgroundColor = 'transparent'}
                          >
                            {item.label}
                          </div>
                        ))}
                      </div>
                    )}
                  >
                    <Select
                      placeholder={'类型'}
                      allowClear={true}
                      value={searchClothType}
                      options={CreationClothTypeCheckBoxOptions.filter((item: any) => item.value !== 'Common' && item.value !== 'Unset')}
                      style={{ width: 84 }}
                      onClick={() => setTypeSelectOpen(!typeSelectOpen)}
                      onChange={(value) => setSearchClothType(value)}
                      dropdownStyle={{ display: 'none' }} // 隐藏原始下拉框
                    />
                  </Dropdown>

                  <Dropdown
                    trigger={['hover']}
                    placement="bottomLeft"
                    open={ageSelectOpen}
                    onOpenChange={(visible) => setAgeSelectOpen(visible)}
                    dropdownRender={() => (
                      <div style={{ backgroundColor: 'white', boxShadow: '0 2px 8px rgba(0,0,0,0.15)', borderRadius: 4, padding: 4, maxHeight: 300, overflow: 'auto' }}>
                        {dynamicAgeRanges.map((item: any) => (
                          <div
                            key={item.value}
                            style={{ 
                              padding: '5px 12px', 
                              cursor: item.disabled ? 'not-allowed' : 'pointer', 
                              borderRadius: 4, 
                              textAlign: 'left',
                              color: item.disabled ? '#ccc' : 'inherit',
                              opacity: item.disabled ? 0.5 : 1
                            }}
                            onClick={() => {
                              if (!item.disabled) {
                                setSearchAgeRange(item.value);
                                setAgeSelectOpen(false);
                              }
                            }}
                            onMouseEnter={e => {
                              if (!item.disabled) {
                                e.currentTarget.style.backgroundColor = '#f5f5f5'
                              }
                            }}
                            onMouseLeave={e => e.currentTarget.style.backgroundColor = 'transparent'}
                          >
                            {item.label}
                          </div>
                        ))}
                      </div>
                    )}
                  >
                    <Select
                      placeholder={'年龄'}
                      allowClear={true}
                      value={searchAgeRange}
                      options={dynamicAgeRanges}
                      style={{ width: 84 }}
                      onClick={() => setAgeSelectOpen(!ageSelectOpen)}
                      onChange={(value) => setSearchAgeRange(value)}
                      dropdownStyle={{ display: 'none' }} // 隐藏原始下拉框
                    />
                  </Dropdown>

                  <InputNumber placeholder={'服装id'} controls={false} style={{ width: 100 }}
                    onChange={(e) => setSearchId(e)} value={searchId} />
                </>
              }

              {['OPERATOR', 'ADMIN'].includes(userInfo.roleType) &&
                <>
                  <Flex align={'center'}>
                    <Checkbox onChange={e => setOnlyShowColorSplit(e.target.checked)}>多色拆分</Checkbox>
                    <Checkbox onChange={e => setOnlyShowDemo(e.target.checked)}>演示服装</Checkbox>
                    <Checkbox onChange={e => setOnlyExperimental(e.target.checked)}>实验服装</Checkbox>
                  </Flex>
                </>
              }

              <Input 
                placeholder={'服装名称模糊搜索'} 
                style={{ width: 200 }} 
                allowClear
                value={searchValue}
                onChange={(e) => {
                  const value = e.target.value;
                  setSearchValue(value); // 立即更新输入框显示
                  
                  // 如果清空了输入框，立即触发搜索
                  if (!value) {
                    debouncedSearchKey.cancel(); // 取消之前的防抖
                    setSearchKey(null);
                  } else {
                    // 否则使用防抖搜索
                    debouncedSearchKey(value);
                  }
                }} 
              />

              {(userInfo.roleType === 'OPERATOR' || userInfo.roleType === 'ADMIN') &&
                <Select value={selectedStatus} placeholder="所有状态" onChange={e => setSelectedStatus(e)}
                  style={{ width: 100 }} allowClear>
                  {AllModelStatus.filter((item) => item.value !== 'IN_TRAINING').map((item, index) => (
                    <Option key={index} value={item.value}>{item.label}</Option>
                  ))}
                </Select>
              }
            </Flex>
          </Flex>
        }
        <Flex gap={12} wrap={'wrap'} className={'lora-list-container'} style={{ minHeight: 440 }}>
          {loraList && loraList.length > 0 && loraList.map((item) => (
            <div key={item.id} className={'lora-item' + (lora && lora.id === item.id ? ' work-item-selected' : '')}
              onClick={() => handleConfirm(item)}>
              <Tooltip
                title={(userInfo.roleType === 'MERCHANT' && item.extInfo?.['memo']) ? item.extInfo['memo'] : null}
                placement="top"
              >
                <div className={'work-image-block'}>
                  {type === 'CUSTOM' &&
                    <div className={'work-item-block-desc text12 font-pf color-w'}>
                      剩余{item.modelPoint ? item.modelPoint : 0}张
                    </div>
                  }
                  <img src={item.showImage} alt={item.name} className={'lora-item-image'} />
                  <IconFont type={getClothMarkIcon(item)}
                    style={{ fontSize: 16, position: 'absolute', top: 8, right: 8 }} />
                </div>
              </Tooltip>
              <div className={'text14 font-pf color-n margin-top-4 margin-bottom-4 text-center'}
                style={{ minHeight: 40, overflowWrap: 'break-word', wordBreak: 'break-all' }}>
                {item.name}
              </div>
            </div>
          ))}
          {!loraList || loraList.length === 0 &&
            <div></div>
          }
        </Flex>
        <Flex align={'center'} justify={'center'}>
          <Pagination
            current={page}
            pageSize={pageSize}
            total={total}
            onChange={handlePageChange}
            showTotal={(total) => `共 ${total} 套服装`}
            showSizeChanger // 允许用户更改每页显示条数
            pageSizeOptions={[18, 27, 36]}
            showQuickJumper // 允许用户快速跳转到某一页
            style={{ marginTop: '16px', textAlign: 'center' }}
          />
        </Flex>
      </Flex>
    </Modal>
  );
}
  ;

export default LoraSelector;