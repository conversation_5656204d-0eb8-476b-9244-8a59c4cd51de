import { UploadOutlined } from '@ant-design/icons';
import React from 'react';
import './index.less';

interface UploadPlaceholderProps {
  onClick?: () => void;
  className?: string;
  style?: React.CSSProperties;
  title?: string;
  hint?: string;
}

const UploadPlaceholder: React.FC<UploadPlaceholderProps> = ({
  onClick,
  className = '',
  style,
  title = '点击或拖拽上传图片',
  hint = '支持 JPG、PNG 格式，大小不超过 10MB',
}) => {
  return (
    <div 
      className={`upload-placeholder ${className}`}
      onClick={onClick}
      style={style}
    >
      <div className="upload-icon">
        <UploadOutlined />
      </div>
      <div className="upload-text">{title}</div>
      {hint && <div className="upload-hint">{hint}</div>}
    </div>
  );
};

export default UploadPlaceholder;
