.multi-step-progress {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 8px;
}

.step {
  position: relative;
  flex: 1;
  overflow: hidden;
}

.step .ant-progress {
  width: 100%;
}

.step-start .ant-progress-outer .ant-progress-bg {
  border-radius: 8px 0 0 8px !important;
}

.step-start .ant-progress-inner {
  border-radius: 8px 0 0 8px !important;
}

.step-end .ant-progress-inner {
  border-radius: 0 8px 8px 0 !important;
}

.step-label {
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
}