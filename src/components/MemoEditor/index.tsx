import React, { useRef, useState } from 'react';
import { Button, Popover, Input, message, Tooltip } from 'antd';
import { EditOutlined } from '@ant-design/icons';

interface MemoEditorProps {
  memo: string | undefined;
  userId: number;
  onSave: (userId: number, memo: string) => Promise<void>;
  onSuccess?: () => void;
  lastModified?: string; // 最近修改时间
}

/**
 * 备注编辑器组件
 * 在文本旁边显示一个备注图标，点击可编辑备注内容
 */
const MemoEditor: React.FC<MemoEditorProps> = ({ memo, userId, onSave, onSuccess, lastModified }) => {
  // 使用useRef存储备注值，避免渲染问题
  const memoRef = useRef(memo || '');
  const hasMemo = !!memo;

  // 备注保存处理函数
  const handleSaveMemo = async () => {
    onSave(userId, memoRef.current).then(() => {
      if (onSuccess) {
        message.success('备注已保存');
        onSuccess();
      }
    }).catch(() => {
      message.error('备注保存失败');
    });
  };

  // 使用useState管理弹窗显示状态
  const [visible, setVisible] = useState(false);
  // 管理当前备注内容长度
  const [currentLength, setCurrentLength] = useState(memoRef.current.length);
  // 最大字数限制
  const MAX_LENGTH = 1000;

  // 备注内容的弹出框
  const memoContent = (
    <div style={{ width: 320 }}>
      {lastModified && (
        <div style={{ fontSize: '12px', color: '#999', marginBottom: 8 }}>
          最近一次修改时间: {lastModified}
        </div>
      )}
      <Input.TextArea
        count={{
          max: MAX_LENGTH,
          show: true,
        }}
        defaultValue={memoRef.current}
        onChange={(e) => {
          memoRef.current = e.target.value;
          setCurrentLength(e.target.value.length);
        }}
        autoSize={{ minRows: 3, maxRows: 12}}
        placeholder="填写备注内容"
        style={{ marginBottom: 8 }}
      />
      <Button
        type="primary"
        size="small"
        disabled={currentLength > MAX_LENGTH}
        onClick={() => {
          handleSaveMemo();
          setVisible(false); // 保存后关闭弹窗
        }}
        style={{ width: '100%', marginTop: 8 }}
      >
        保存
      </Button>
    </div>
  );

  return (
    <Popover
      placement="bottomLeft"
      content={memoContent}
      title={hasMemo ? '编辑备注' : '添加备注'}
      trigger="click"
      open={visible}
      onOpenChange={setVisible}
    >
      <Tooltip title={hasMemo ? (memo && memo.length > 20 ? `${memo.substring(0, 20)}...` : memo) : '添加备注'}>
        <span style={{
          marginLeft: 6,
          color: hasMemo ? '#faad14' : '#aaa',
          cursor: 'pointer',
          fontSize: 16,
          verticalAlign: 'middle',
        }}>
          <EditOutlined />
        </span>
      </Tooltip>
    </Popover>
  );
};

export default MemoEditor;
