import React, { useEffect, useRef, useState } from 'react';
import { Button, Dropdown, Flex, MenuProps, message, Modal, Spin, Tooltip } from 'antd';
import './ImgPreview.less';
import IconFont from '@/components/IconFont';
import { USER_INFO } from '@/constants';
import { deepCopy, download, getImageName, getTaskIdByImage, getUserInfo } from '@/utils/utils';
import {
  CreativeType,
  CreativeVO,
  fetchBatchByTask,
  fetchBatchStyleImagesMap,
  fetchMagicPrompt,
  fetchWorkflow,
  imageLike,
  monitorImgDownload,
  canImageUpscale, getCreativeTypeName,
} from '@/services/CreativeController';
import { downloadJson } from '@/utils/downloadUtils';
import ReactDOM from 'react-dom';
import { ZoomInOutlined } from '@ant-design/icons';
import WatermarkImage from '@/components/WatermarkImage';
import { BadCaseTips } from '@/components/Common/TagsComponent';
import UserFavorButton from '@/components/Favor/UserFavorButton';
import MaskOverlay from '@/components/imageOperate/MaskOverlay';
import FREE_BG from '@/assets/images/free-bg.png';
export interface ImgPreviewInfo {
  batchId: number,
  indexInBatch: number,
}
interface ImgPreviewProps {
  previewVisible: boolean;
  handleCancelPreview: (previewImage: string) => void;
  previewImage: string;
  needSwitch?: boolean;
  previewIdx?: number;
  previewImgs?: string[];
  likeObj?: JSON | null;
  likeCallback?: () => void;
  enableGou?: boolean;
  enableCha?: boolean;
  showTools?: boolean;
  type?: CreativeType;
  creativeBatch?: CreativeVO | null | undefined;
  needWatermark?: boolean;
  isBlueGou?: boolean;
  imageClick?: (imageUrl: string) => void;
  isEnableGou?: (imageUrl: string) => boolean;
  selectedImages?: string[];
  modelId?: number;
  batchId?: number;
  previewInfoList?: ImgPreviewInfo[];
  indexInBatch?: number;
  imagesExtMap?: Record<string, string>;
  mask?: string;
  originImg?: string;
  showBatchInfo?: boolean;
  faceAndMaskData?: { resultFaceImage: string, resultMaskImage: string }[];
  poseReferenceData?: { referenceOriginalImage: string }[];
  isExampleImages?: boolean;
}

const ImgPreview: React.FC<ImgPreviewProps> = ({
  previewVisible,
  handleCancelPreview,
  previewImage,
  needSwitch = false,
  previewIdx = 0,
  previewImgs = [],
  likeObj = null,
  likeCallback,
  enableGou = false,
  enableCha = false,
  showTools = false,
  creativeBatch,
  type,
  needWatermark = true,
  isBlueGou = false,
  imageClick,
  isEnableGou,
  selectedImages,
  modelId,
  previewInfoList,
  imagesExtMap,
  mask = '',
  originImg = '',
  showBatchInfo = false,
  faceAndMaskData,
  poseReferenceData,
  isExampleImages = false,
}) => {
  const userInfo = JSON.parse(localStorage.getItem(USER_INFO) || '{}');
  const [idx, setIdx] = useState<number>(previewIdx);
  const [prompt, setPrompt] = useState('');
  const [likeJson, setLikeJson] = useState<JSON | null>(null);

  const needSwitchRef = useRef(needSwitch);
  const previewImgsRef = useRef(previewImgs);
  const idxRef = useRef(idx);
  const [styleImages, setStyleImages] = useState<Record<string, string>>({});

  const [batchCache, setBatchCache] = useState<Record<number, CreativeVO>>({});
  const [currentBatch, setCurrentBatch] = useState<CreativeVO | undefined | null>(creativeBatch);
  const [exampleOriginBatch, setExampleOriginBatch] = useState<CreativeVO | undefined | null>(null);

  // 人脸图和蒙版图
  const [faceAndMask, setFaceAndMask] = useState<{ resultFaceImage: string, resultMaskImage: string }[]>([]);
  // 参考图
  const [poseReference, setPoseReference] = useState<{ referenceOriginalImage: string }[]>([]);
  // 跟踪图片加载状态
  const [loadingStates, setLoadingStates] = useState<{ [key: string]: boolean }>({});
  // 对比原图
  const [showCompare, setShowCompare] = useState<boolean>(false);
  const showFaceAndScene = currentBatch?.faceName || currentBatch?.sceneName;
  const showOriginalBatch : boolean = (['REPAIR_DETAIL', 'PARTIAL_REDRAW', 'REPAIR_HANDS', 'ERASE_BRUSH', 'REMOVE_WRINKLE', 'IMAGE_UPSCALE'].includes(currentBatch?.type || '')
      && currentBatch?.extInfo['originalBatchInfo']);

  useEffect(() => {
    needSwitchRef.current = needSwitch;
    previewImgsRef.current = previewImgs;
    idxRef.current = idx;
    setLikeJson(likeObj);
  }, [needSwitch, previewImgs, idx, likeObj]);

  useEffect(() => {
    // 在整个页面禁用右键菜单
    const handleContextMenu = (event) => {
      //禁用右键菜单
      if (userInfo && userInfo.roleType === 'MERCHANT') {
        event.preventDefault();
      }
    };

    document.addEventListener('contextmenu', handleContextMenu);

    // 清理事件监听器
    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
    };
  }, [userInfo]);

  useEffect(() => {
    setCurrentBatch(creativeBatch);
  }, [creativeBatch]);

  function switchImage(next: boolean) {
    if (needSwitchRef.current && previewImgsRef.current.length > 0) {
      if (next && idxRef.current < (previewImgsRef.current.length - 1)) {
        setIdx(idxRef.current + 1);
      } else if (!next && idxRef.current > 0) {
        setIdx(idxRef.current - 1);
      }
    }
  }

  const handleDownload = (path = '') => {
    download(path);
    if (currentBatch && Number(currentBatch?.id) > 0 && path) {
      monitorImgDownload(Number(currentBatch?.id), path);
    }
  };

  const handleKeyDown = (event) => {
    switch (event.key) {
      case 'ArrowLeft': {
        switchImage(false);
        break;
      }
      case 'ArrowRight': {
        switchImage(true);
        break;
      }
    }
  };

  const downloadWorkflow = (path: string) => {
    const taskId = getTaskIdByImage(path);
    if (!taskId) {
      message.warning('解析异常，无法获取workflow');
      return;
    }

    fetchWorkflow(taskId).then((res) => {
      if (res) {
        downloadJson(res);
      }
    });
  };

  const handleShowPrompt = (path: string) => {
    const imageName = getImageName(path);
    const taskId = getTaskIdByImage(path);
    if (!taskId) {
      message.warning('当前图片为上传图片，无法获取prompt');
      return;
    }

    fetchMagicPrompt(imageName).then((res) => {
      if (res && res.length > 0) {
        let context = res.replace('【negative】', '\n\n【逆向提示词】');
        context = context.replace('【positive】: ', '【正向提示词】: \n服装：\n');
        context = context.replace('\n\n\n', '\n\n场景：\n');
        context = context.replace('\n\n\n', '\n\n模特：\n');
        setPrompt(context);
      } else {
        setPrompt('无prompt 或 获取异常');
      }
    });
  };

  const toLike = (imageUrl: string, isLike: boolean, index: number) => {
    let taskId = getTaskIdByImage(imageUrl);
    if (!taskId) {
      taskId = index;
    }
    imageLike(taskId, currentBatch?.id, isLike, imageUrl).then((res) => {
      if (res) {
        let copy = [];
        if (likeJson) {
          copy = deepCopy(likeJson);
        }
        copy[String(taskId)] = isLike ? 'LIKE' : 'DISLIKE';
        // @ts-ignore
        setLikeJson(copy);

        if (likeCallback) {
          likeCallback();
        }
      }
    });
  };

  const like = (imageUrl: string, index: number) => {
    toLike(imageUrl, true, index);
  };

  const dislike = (imageUrl: string, index: number) => {
    toLike(imageUrl, false, index);
  };

  const isLike = (imageUrl: string, isLike: boolean, index: number) => {
    if (!imageUrl) {
      return false;
    }

    if (likeJson) {
      if (likeJson[imageUrl]){
        return likeJson[imageUrl] === (isLike ? 'LIKE' : 'DISLIKE');
      }
      let taskId = getTaskIdByImage(imageUrl);
      if (!taskId) taskId = index;
      return likeJson[taskId] === (isLike ? 'LIKE' : 'DISLIKE');
    }
    return false;
  };

  const gotoToolkit = (imageUrl: string, creativeType: CreativeType) => {
    localStorage.setItem('imageUrl', imageUrl);
    localStorage.setItem('creativeType', creativeType);
    if (modelId) {
      localStorage.setItem('modelId', modelId.toString());
    }
    window.open('/#/toolkit-menu', '_blank');
  }

  const handleImageClick = (imageUrl: string) => {
    if (imageClick) {
      imageClick(imageUrl);
    }
  };

  const showStyleImage = () => {
    return userInfo?.roleType === 'ADMIN' && currentBatch && currentBatch?.extInfo?.['isStyleScene'];
  };

  // 是否显示人脸和蒙版图
  const showFaceAndMaskImage = () => {
    // 仅当为ADMIN角色、基础款换衣类型且有任务列表时才可能显示
    return userInfo?.roleType === 'ADMIN' &&
      currentBatch &&
      currentBatch.type === 'BASIC_CHANGING_CLOTHES' &&
      currentBatch?.creativeTasksList &&
      currentBatch?.creativeTasksList.length > 0;
  };

  // 是否显示参考图
  const showPoseReferenceImage = () => {
    // 固定姿势创作时显示
    return userInfo?.roleType === 'ADMIN' &&
      currentBatch &&
      currentBatch.type === 'FIXED_POSTURE_CREATION' &&
      currentBatch?.creativeTasksList &&
      currentBatch?.creativeTasksList.length > 0;
  };

  // 获取人脸和蒙版图
  const getFaceAndMaskImageUrl = () => {
    if (!currentBatch || !currentBatch.creativeTasksList || currentBatch.creativeTasksList.length === 0) {
      return;
    }

    const faceAndMaskArray: { resultFaceImage: string, resultMaskImage: string }[] = [];

    // 基础款换衣时，previewImgs包含的是当前任务组的所有结果图
    // 需要根据当前预览的图片找到对应的任务
    if (currentBatch.type === 'BASIC_CHANGING_CLOTHES' && previewImgs && previewImgs.length > 0) {
      // 当前预览的图片URL
      const currentImageUrl = previewImgs[idx];

      // 找到包含当前图片的任务
      const currentTask = currentBatch.creativeTasksList.find(task =>
        task.resultImages && task.resultImages.some(img => img === currentImageUrl)
      );

      if (currentTask && currentTask.extInfo &&
        currentTask.extInfo.resultFaceImage &&
        currentTask.extInfo.resultMaskImage) {
        faceAndMaskArray.push({
          resultFaceImage: currentTask.extInfo.resultFaceImage,
          resultMaskImage: currentTask.extInfo.resultMaskImage
        });
      }
    }

    setFaceAndMask(faceAndMaskArray);
  };

  // 获取参考图
  const getPoseReferenceImageUrl = () => {
    if (!currentBatch || !currentBatch.creativeTasksList || currentBatch.creativeTasksList.length === 0) {
      return;
    }

    const poseReferenceArray: { referenceOriginalImage: string }[] = [];

    // 固定姿势创作时，previewImgs包含的是当前任务组的所有结果图
    // 需要根据当前预览的图片找到对应的任务
    if (currentBatch.type === 'FIXED_POSTURE_CREATION' && previewImgs && previewImgs.length > 0) {
      // 当前预览的图片URL
      const currentImageUrl = previewImgs[idx];

      // 找到包含当前图片的任务
      const currentTask = currentBatch.creativeTasksList.find(task =>
        task.resultImages && task.resultImages.some(img => img === currentImageUrl)
      );

      if (currentTask && currentTask.extInfo &&
        currentTask.extInfo.referenceOriginalImage) {
        poseReferenceArray.push({
          referenceOriginalImage: currentTask.extInfo.referenceOriginalImage
        });
      }
    }

    setPoseReference(poseReferenceArray);
  };

  const getStyleImageUrl = () => {
    if (!styleImages) return '';
    const taskId = getTaskIdByImage(previewImgs[idx]);
    if (!taskId) return '';
    if (!styleImages[taskId]) return '';
    return styleImages[taskId];
  };

  // 处理图片加载完成事件
  const handleImageLoaded = (imageUrl: string) => {
    setLoadingStates(prev => ({
      ...prev,
      [imageUrl]: false
    }));
  };

  const queryBatch = async (idx: number) => {
    let isExampleImage = currentBatch?.extInfo['bizTag'] === 'exampleImages' || isExampleImages;
    if ((showBatchInfo && !creativeBatch) || isExampleImage) {
      const imageUrl = previewImgs[idx];
      const taskId = getTaskIdByImage(imageUrl);
      if (!taskId) {
        return;
      }
      if (!batchCache[taskId]) {
        const res = await fetchBatchByTask(taskId);
        if (!res) return;
        batchCache[taskId] = res;
        setBatchCache(batchCache);
      }

      if (isExampleImage) {
        setExampleOriginBatch(batchCache[taskId]);
      } else {
        setCurrentBatch(batchCache[taskId]);
      }
    }
  }

  useEffect(() => {
    setCurrentBatch(creativeBatch);
    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  useEffect(() => {
    if (previewVisible) {
      setIdx(previewIdx); // 每次弹窗打开时重置索引
    }
  }, [previewVisible, previewIdx]);

  useEffect(() => {
    queryBatch(idx);
  }, [idx]);

  useEffect(() => {
    if (showStyleImage()) {

      fetchBatchStyleImagesMap(currentBatch?.id).then(res => {
        if (!res) return;
        setStyleImages(res);
      });
    }
  }, [currentBatch]);

  // 在currentBatch变更时获取人脸和蒙版图
  useEffect(() => {
    // 优先使用传入的faceAndMaskData
    if (faceAndMaskData && faceAndMaskData.length > 0) {
      setFaceAndMask(faceAndMaskData);
    } else if (showFaceAndMaskImage()) {
      getFaceAndMaskImageUrl();
    }
  }, [currentBatch, faceAndMaskData]);

  // 在图片索引(idx)变化时重新获取对应组的人脸和蒙版图
  useEffect(() => {
    // 如果没有传入数据，则在切换图片时获取
    if (!faceAndMaskData && showFaceAndMaskImage()) {
      getFaceAndMaskImageUrl();
    }
  }, [idx]);

  // 在currentBatch变更时获取参考图
  useEffect(() => {
    if (showPoseReferenceImage()) {
      getPoseReferenceImageUrl();
    }
  }, [currentBatch]);

  // 在图片索引(idx)变化时重新获取对应组的参考图
  useEffect(() => {
    if (showPoseReferenceImage()) {
      getPoseReferenceImageUrl();
    }
  }, [idx]);

  if (!previewVisible) {
    return null;
  }

  const FloatBtn = () => {
    return ReactDOM.createPortal(
      <>
        <div className={'img-detail-round img-detail-round-left'} onClick={() => switchImage(false)}>
          <IconFont type={'icon-icon_zuojiantou'} className={'img-icon-jiantou'} />
        </div>
        <div className={'img-detail-round img-detail-round-right'} onClick={() => switchImage(true)}>
          <IconFont type={'icon-icon_youjiantou'} className={'img-icon-jiantou'} />
        </div>
      </>,
      document.body,
    );
  };

  const handleMenuClick: MenuProps['onClick'] = (e) => {
    switch (e.key) {
      case '1':
        handleShowPrompt(previewImgs[idx]);
        break;
      case '2':
        downloadWorkflow(previewImgs[idx]);
        break;
    }
  };

  const items: MenuProps['items'] = [
    {
      label: '显示prompt',
      key: '1',
    },
    {
      label: '下载工作流',
      key: '2',
    },
  ];

  const menuProps = {
    items,
    onClick: handleMenuClick,
  };

  // 根据当前图片获取对应的场景信息
  const getCurrentImageScene = (batch: CreativeVO, currentImageUrl: string) => {
    if (!batch || !batch.creativeTasksList || batch.creativeTasksList.length === 0) {
      return '';
    }

    // 找到包含当前图片的任务
    const currentTask = batch.creativeTasksList.find(task =>
      task.resultImages && task.resultImages.some(img => img === currentImageUrl)
    );

    if (currentTask && currentTask.extInfo && (currentTask.extInfo as any).loraPath) {
      const loraPath = (currentTask.extInfo as any).loraPath;

      // 使用正则表达式解析路径中的场景信息
      // 1. 首先匹配文件名: "风格-熟女-咖啡馆裹身裙街拍_5489_20250106_152832-flux.safetensors"
      const fileNameMatch = loraPath.match(/([^/]+\.safetensors)$/);
      if (fileNameMatch) {
        const fileName = fileNameMatch[1];

        // 2. 去掉"-flux.safetensors"后缀，获取主要部分
        const sceneMatch = fileName.replace('-flux.safetensors', '');

        // 3. 按照下划线分割，获取主要场景名称部分
        const sceneParts = sceneMatch.split('_');
        if (sceneParts.length > 0) {
          const sceneName = sceneParts[0]; // 取第一部分: "风格-熟女-咖啡馆裹身裙街拍"
          return sceneName;
        }
      }
    }

    return '';
  };

  const isResultImage = (idx: number) : boolean => {
    return !!(previewInfoList && previewInfoList[idx] && previewInfoList[idx].batchId > 0 && previewInfoList[idx].indexInBatch >= 0);
  };

  const BatchInfoBlock: React.FC<{ batch: CreativeVO }> = ({ batch }) => (
    <div className={'img-preview-model-parameters'}>
      <div className={'weight'}>{getCreativeTypeName(batch?.type)}参数</div>
      <div>{batch?.id ? 'ID: ' + batch?.id : ''}</div>
      <div>{batch?.modelName ? '服装: ' + batch?.modelName : ''}</div>
      <div>{batch?.faceName ? '模特: ' + batch?.faceName : ''}</div>
      {/* 固定姿势创作显示当前图片对应的场景信息，其他类型显示原有场景名称 */}
      {batch?.type === 'FIXED_POSTURE_CREATION' && batch?.creativeTasksList && previewImgs[idx] ? (
        <div>
          场景: {getCurrentImageScene(batch, previewImgs[idx]) || '未知场景'}
        </div>
      ) : (
        <div>{batch?.sceneName ? '场景: ' + batch?.sceneName : ''}</div>
      )}
      {userInfo?.roleType !== 'MERCHANT' && (
        <>
          <div>{batch?.imageProportionName ? '尺寸: ' + batch?.imageProportionName : ''}</div>
          <div>颜色: {(batch?.extInfo && batch?.extInfo['clothColor'] ? batch?.extInfo['clothColor'] : (batch?.extInfo['colorIndex'] && batch?.extInfo['colorIndex'] > 0) ? `颜色${batch?.extInfo['colorIndex']}` : '随机')}</div>
          <div>视角: {batch?.extInfo && batch?.extInfo['cameraAngle']?.includes('back view') ? '背面' : '正面'} /
            {(!batch?.extInfo || batch?.extInfo['bodyTypeUnlimited'] === 'Y') ? '不限' : (batch?.extInfo['cameraAngle']?.includes('upper body') ? '上半身' : (batch?.extInfo['cameraAngle']?.includes('lower body') ? '下半身' : '全身'))}
          </div>
          <div>{batch?.operatorNick ? '创建人: ' + batch?.operatorNick : ''}</div>
          <div>{batch?.createTime ? batch?.createTime : ''}</div>
        </>
      )}
    </div>
  )

  return (
    <>
      <Modal className="img-preview-modal" open={previewVisible} closable={false} footer={null} width={'auto'}
        onCancel={() => handleCancelPreview(previewImgs[idx])}
        onOk={() => handleCancelPreview(previewImgs[idx])}
        zIndex={1200}>
        <Flex vertical justify={'space-between'} gap={16} className={'img-detail-container'}>
          <Flex style={{ position: 'relative' }} align={'center'} onClick={() => handleImageClick(previewImgs[idx])}
            className={imageClick ? 'pointer' : ''}>
            {type === 'REPAIR_HANDS' &&
              <div className={'repair-hands-tag'}>
                {idx === 0 && <div className={'text14 font-pf color-w'} style={{ padding: 4 }}>原图</div>}
                {idx !== 0 &&
                  <Flex style={{ padding: 4 }} gap={4} align={'center'} justify={'center'}>
                    <IconFont type={'icon-shoubuxiufu'} style={{ fontSize: 16 }} className={'color-w'} />
                    <div className={'text14 font-pf color-w'}>修复图{idx}</div>
                  </Flex>
                }
              </div>
            }
            {type === 'IMAGE_UPSCALE' &&
              <div className={'repair-hands-tag'}>
                {idx === 0 && <div className={'text14 font-pf color-w'} style={{ padding: 4 }}>原图</div>}
                {idx !== 0 &&
                  <Flex style={{ padding: 4 }} gap={4} align={'center'} justify={'center'}>
                    <ZoomInOutlined style={{ fontSize: 16 }} className={'color-w'} />
                    <div className={'text14 font-pf color-w'}>高清图</div>
                  </Flex>
                }
              </div>
            }
            <WatermarkImage src={showCompare ? originImg : needSwitch && previewImgs.length > 0 ? previewImgs[idx] : previewImage}
              className={showTools ? 'img-detail-img' : 'img-detail-img-no-tools'}
              needWatermark={needWatermark} />

            {mask && <MaskOverlay maskUrl={mask} maskColor="#61B3FF" style={{ top: 0, left: 0 }} showIcon />}

            {showStyleImage() && getStyleImageUrl() &&
              <img src={getStyleImageUrl()} alt={'img'}
                style={{ position: 'absolute', top: 0, right: -220, width: 200, height: 'auto' }} />
            }


            {/* 基础款换衣：人脸图和蒙版图，所有预览图都显示 */}
            {showFaceAndMaskImage() && faceAndMask.length > 0 && (
              <Flex vertical style={{ position: 'absolute', top: 0, right: -220, width: 200 }}>
                {faceAndMask.map((item, index) => (
                  <React.Fragment key={index}>
                    {item.resultFaceImage && (
                      <>
                        <div style={{ position: 'relative' }}>
                          <Spin spinning={loadingStates[item.resultFaceImage] !== false} tip="加载中..." style={{ width: '100%', height: '100%' }}>
                            <img
                              src={item.resultFaceImage}
                              alt={'人脸图'}
                              onLoad={() => handleImageLoaded(item.resultFaceImage)}
                              onError={() => handleImageLoaded(item.resultFaceImage)}
                              style={{ width: 200, height: 'auto', marginBottom: 8 }}
                            />
                          </Spin>
                          <div style={{ position: 'absolute', top: 8, right: 8, backgroundColor: 'rgba(0, 0, 0, 0.6)', padding: '2px 6px', borderRadius: '4px', zIndex: 10 }}>
                            <div className="text12 font-pf color-w">人脸图</div>
                          </div>
                        </div>
                      </>
                    )}
                    {item.resultMaskImage && (
                      <>
                        <div style={{ position: 'relative' }}>
                          <Spin spinning={loadingStates[item.resultMaskImage] !== false} tip="加载中..." style={{ width: '100%', height: '100%' }}>
                            <img
                              src={item.resultMaskImage}
                              alt={'蒙版图'}
                              onLoad={() => handleImageLoaded(item.resultMaskImage)}
                              onError={() => handleImageLoaded(item.resultMaskImage)}
                              style={{ width: 200, height: 'auto', marginBottom: 16 }}
                            />
                          </Spin>
                          <div style={{ position: 'absolute', top: 8, right: 8, backgroundColor: 'rgba(0, 0, 0, 0.6)', padding: '2px 6px', borderRadius: '4px', zIndex: 10 }}>
                            <div className="text12 font-pf color-w">蒙版图</div>
                          </div>
                        </div>
                      </>
                    )}
                  </React.Fragment>
                ))}
              </Flex>
            )}

            {/* 固定姿势创作：参考图，右上角显示 */}
            {showPoseReferenceImage() && poseReference.length > 0 && (
              <Flex vertical style={{ position: 'absolute', top: 0, right: -220, width: 200 }}>
                {poseReference.map((item, index) => (
                  <React.Fragment key={index}>
                    {item.referenceOriginalImage && (
                      <>
                        <div style={{ position: 'relative' }}>
                          <Spin spinning={loadingStates[item.referenceOriginalImage] !== false} tip="加载中..." style={{ width: '100%', height: '100%' }}>
                            <img
                              src={item.referenceOriginalImage}
                              alt={'参考图'}
                              onLoad={() => handleImageLoaded(item.referenceOriginalImage)}
                              onError={() => handleImageLoaded(item.referenceOriginalImage)}
                              style={{ width: 200, height: 'auto', marginBottom: 16, borderRadius: '5.16px', border: '0.65px solid #E0E0E0' }}
                            />
                          </Spin>
                          <div style={{ position: 'absolute', top: 8, left: 8, backgroundColor: 'rgba(0, 0, 0, 0.6)', padding: '2px 6px', borderRadius: '4px', zIndex: 10 }}>
                            <div className="text12 font-pf color-w">参考图</div>
                          </div>
                        </div>
                      </>
                    )}
                  </React.Fragment>
                ))}
              </Flex>
            )}

            {/*自定义图片：✅，❌*/}
            {(enableGou || isEnableGou && isEnableGou(previewImgs[idx])) && (
              <div style={{
                position: 'absolute',
                top: 16,
                right: 16,
                width: 64,
                height: 64,
                background: 'white',
                borderRadius: '50%',
              }}>
                {!isBlueGou && <IconFont type={'icon-gou'} style={{ fontSize: 64 }} />}
                {isBlueGou && <IconFont type={'icon-gou-lan'} style={{ fontSize: 64, color: '#366EF4' }} />}
              </div>
            )}

            {enableCha && (
              <div style={{
                position: 'absolute',
                top: 16,
                right: 16,
                width: 64,
                height: 64,
                background: 'white',
                borderRadius: '50%',
              }}>
                <IconFont type={'icon-cha'} style={{ fontSize: 64 }} />
              </div>
            )}
          </Flex>

          {showTools &&
            <Flex gap={8} align={'center'} justify={'center'}>
              {userInfo && (userInfo.roleType === 'OPERATOR' || userInfo.roleType === 'ADMIN') &&
                <>
                  <Flex gap={0}>
                    {imagesExtMap &&
                      <div>{imagesExtMap[previewImgs[idx]]}</div>
                    }
                  </Flex>
                </>
              }
              <Flex gap={4} className={'img-detail-repair-block img-detail-btn'}
                onClick={() => gotoToolkit(previewImgs[idx], 'REPAIR_DETAIL')}>
                <IconFont type={'icon-xiufu'} style={{ fontSize: 24 }} className={'color-1a'} />
                <div className={'text16 font-pf color-1a'}>细节修补</div>
              </Flex>

              <Flex gap={4} className={'img-detail-repair-block img-detail-btn'}
                onClick={() => gotoToolkit(previewImgs[idx], 'PARTIAL_REDRAW')}>
                <IconFont type={'icon-zhonghui'} style={{ fontSize: 24 }} className={'color-1a'} />
                <div className={'text16 font-pf color-1a'}>局部重绘</div>
              </Flex>

              <Flex gap={4} className={'img-detail-repair-block img-detail-btn'}
                onClick={() => gotoToolkit(previewImgs[idx], 'REPAIR_HANDS')}>
                <IconFont type={'icon-shoubuxiufu'} style={{ fontSize: 24 }} className={'color-1a'} />
                <div className={'text16 font-pf color-1a'}>手部修复</div>
              </Flex>

              <Flex gap={4} className={'img-detail-repair-block img-detail-btn'}
                onClick={() => gotoToolkit(previewImgs[idx], 'REMOVE_WRINKLE')}>
                <IconFont type={'icon-fuzhuangquzhou_weixuanzhong'} style={{ fontSize: 24 }} className={'color-1a'} />
                <div className={'text16 font-pf color-1a'}>衣服去皱</div>
              </Flex>

              <Flex gap={4} className={'img-detail-repair-block img-detail-btn'}
                onClick={() => gotoToolkit(previewImgs[idx], 'ERASE_BRUSH')}>
                <IconFont type={'icon-brush-3-line'} style={{ fontSize: 24 }} className={'color-1a'} />
                <div className={'text16 font-pf color-1a'}>消除笔</div>
              </Flex>

              {canImageUpscale(previewImgs[idx]) &&
                <Flex gap={4} className={'img-detail-repair-block img-detail-btn'} style={{ position: 'relative' }}
                  onClick={() => gotoToolkit(previewImgs[idx], 'IMAGE_UPSCALE')}>
                  <ZoomInOutlined style={{ fontSize: 24 }} className={'color-1a'} />
                  <div className={'text16 font-pf color-1a'}>2倍放大</div>

                  <div style={{ position: 'absolute', top: -6, right: 0, width: 70, height: 20, overflow: 'hidden' }}>
                    <div className={'text12 font-pf repair-free-icon-desc'}>0.4缪斯点</div>
                    <img src={FREE_BG} alt={'logo'} width={70} height={20}
                      style={{ position: 'absolute', top: 0, right: 0, zIndex: 1 }} />
                  </div>
                </Flex>
              }

              {getUserInfo()?.roleType === 'ADMIN' && originImg &&
                <Tooltip title={'对比原图'}>
                  <Flex gap={4} className={'img-detail-repair-block img-detail-btn'}
                    style={{ padding: 12 }}
                    onMouseDown={() => setShowCompare(true)}
                    onMouseUp={() => setShowCompare(false)}
                    onMouseLeave={() => setShowCompare(false)}>
                    <IconFont type={showCompare ? 'icon-compare-revert' : 'icon-compare'} style={{ fontSize: 24 }} className={'color-1a'} />
                  </Flex>
                </Tooltip>
              }

              {userInfo?.roleType !== 'ADMIN' &&
                previewInfoList && previewInfoList[idx] && previewInfoList[idx].batchId > 0 && previewInfoList[idx].indexInBatch >= 0 &&
                <UserFavorButton className={'img-detail-repair-block img-detail-btn'} favorType={'IMAGE'} itemId={previewInfoList[idx].batchId}
                  text={'收藏'} images={[previewInfoList[idx].indexInBatch]} imageIndex={previewInfoList[idx].indexInBatch} style={{ fontSize: 24 }} />
              }
              <Flex gap={16} className={'img-detail-repair-block img-detail-btn'}>
                <div className={''} onClick={() => like(previewImgs[idx], idx)}>
                  <IconFont type={isLike(previewImgs[idx], true, idx) ? 'icon-rongqi' : 'icon-xihuan_moren'}
                    className={'img-icon-like color-1a'} />
                </div>
                <Tooltip title={<BadCaseTips imgUrl={previewImgs[idx]} taskId={getTaskIdByImage(previewImgs[idx])} />}
                  trigger={'click'} styles={{ body: { width: 400 } }}>
                  <div className={''} onClick={() => dislike(previewImgs[idx], idx)}>
                    <IconFont
                      type={isLike(previewImgs[idx], false, idx) ? 'icon-buxihuan_dianji' : 'icon-buxihuan_moren'}
                      className={'img-icon-like color-1a'} />
                  </div>
                </Tooltip>
              </Flex>

              {!(userInfo && (userInfo.roleType === 'OPERATOR' || userInfo.roleType === 'ADMIN')) &&
                <Button className={'download-btn detail-image-download img-detail-btn'}
                  type="text"
                  onClick={() => handleDownload(previewImgs[idx])}
                  icon={<IconFont type={'icon-icon-download'} style={{ fontSize: 24, color: '#FFFFFF' }} />}
                >
                  <div className={'text14 weight font-pf color-w'}>下载</div>
                </Button>
              }
              {userInfo && (userInfo.roleType === 'OPERATOR' || userInfo.roleType === 'ADMIN') &&
                <Dropdown.Button className={'download-btn detail-image-download img-detail-btn'}
                  type="text" onClick={() => handleDownload(previewImgs[idx])}
                  menu={menuProps}>
                  <Flex align={'center'} gap={8} className={'text14 weight font-pf color-w'}>
                    <IconFont type={'icon-icon-download'} style={{ fontSize: 24, color: '#FFFFFF' }} />
                    下载
                  </Flex>
                </Dropdown.Button>
              }
            </Flex>
          }
        </Flex>

        {selectedImages &&
          <Flex vertical gap={16} wrap={'wrap'} justify={'flex-end'} className={'detail-flow-image-wrapper'}>
            {selectedImages.map((image, index) => (
              <div key={index} style={{ position: 'relative' }}>
                <img alt={'img'} src={image} width={100} height={'auto'}
                  className={previewImgs[idx] === image ? 'border-selected' : ''} />
                <div className={'image-selector-round'}
                  style={{ width: 16, height: 16, top: 4, right: 4 }}>{index + 1}</div>
              </div>
            ))}
          </Flex>
        }

        <>
          {(showFaceAndScene) &&
            <BatchInfoBlock batch={currentBatch} />
          }

          {(!showFaceAndScene && exampleOriginBatch) &&
            <BatchInfoBlock batch={exampleOriginBatch} />
          }
          {showOriginalBatch && currentBatch && isResultImage(idx) &&
            <BatchInfoBlock batch={currentBatch.extInfo['originalBatchInfo'] as CreativeVO} />
          }
        </>

      </Modal>

      {previewVisible && needSwitch && (
        <FloatBtn />
      )}

      <Modal
        open={!!prompt}
        onCancel={() => setPrompt('')}
        centered={true}
        width={820}
        zIndex={1300}
        footer={null}
        closable={false}
        getContainer={() => document.body}
      >
        <div style={{ whiteSpace: 'pre-line', width: 'auto' }}>{prompt}</div>
      </Modal>
    </>
  );
};

export default ImgPreview;