import React, { useEffect, useRef } from 'react';
import { Flex } from 'antd';
import WATER_MARK from '@/assets/icon/watermark.png';
import { IMAGE_LOADING, IMAGE_LOADING_11, IMAGE_QUEUE, IMAGE_QUEUE_11, isFilledImage } from '@/constants';
import MaskOverlay from '@/components/imageOperate/MaskOverlay';

interface WatermarkImageProps {
  src: string;
  className?: string;
  style?: React.CSSProperties;
  width?: number | string;
  height?: number | string;
  loading?: 'eager' | 'lazy' | undefined;
  needWatermark?: boolean;
  onClick?: () => void;
}

// 右键禁用
const handleContextMenu = (event) => {
  event.preventDefault();
};

const WatermarkImage: React.FC<WatermarkImageProps> = ({
                                                         src,
                                                         className = '',
                                                         style,
                                                         width,
                                                         height,
                                                         loading = undefined,
                                                         needWatermark = true,
                                                         onClick = () => {
                                                         },
                                                       }) => {

  return <>
    <div style={{ position: 'relative', width: 'auto', height: '100%', margin: '0 auto' }}>
      <img className={className} style={style} alt="img" onClick={onClick}
           src={src} width={width} height={height} loading={loading} />
      {/*src={src} width={width} height={height} loading={loading} onContextMenu={handleContextMenu}/>*/}

      {needWatermark && !isFilledImage(src) &&
        <Flex gap={8} style={{ position: 'absolute', bottom: 8, right: 8, width: (width ? width : '100%') }}
              justify={'flex-end'}>
          <img src={WATER_MARK} alt={'watermark'} className="no-inherit"
               style={{ width: (width ? `calc(${width}*35.34%) !important` : '35.34% !important') }}
               width={(width ? `calc(${width}*35.34%) !important` : '35.34% !important')} />
        </Flex>
      }
    </div>
  </>;
};

export default WatermarkImage;