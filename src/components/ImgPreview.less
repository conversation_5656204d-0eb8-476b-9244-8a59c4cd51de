.img-preview-modal {
  position: relative;
  top: 16px;
  display: flex;
  justify-content: center;
  align-items: center;

  // 清除图片预览的padding
  .ant-modal-content {
    padding: 0 !important;
  }
}

.img-detail-container {
  padding: 16px;
  background: #F5F6F9;
  border-radius: 16px;
  height: calc(100vh - 32px);
}

.img-detail-img {
  height: calc(100vh - 32px - 32px - 44px - 16px);
}

.img-detail-img-no-tools {
  height: calc(100vh - 64px);
}

.img-detail-round {
  width: 64px;
  height: 64px;
  border-radius: 205px;
  background: rgba(0, 0, 0, 0.4);
  opacity: 1;
  z-index: 1302;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.img-detail-round:hover .img-icon-jiantou:hover {
  color: #498FFF;
}

.img-detail-round-left {
  position: fixed;
  left: 40px;
  top: 40%;
}

.img-detail-round-right {
  position: fixed;
  right: 40px;
  top: 40%;
}

.img-icon-jiantou {
  font-size: 64px;
  z-index: 10003;
  color: #FFFFFF;
}

.img-like-round {
  width: 48px;
  height: 48px;
  opacity: 1;

  box-sizing: border-box;
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 50%;

  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.img-like-round:hover {
  background: rgba(255, 255, 255, 0.1);
  box-sizing: border-box;
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.img-icon-like {
  font-size: 24px;
  cursor: pointer;
}

.img-detail-btn {
  height: 44px !important;
}

.img-detail-repair-block {
  width: auto;
  height: 44px;
  border-radius: 8px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0 16px;
  gap: 4px;

  background: #FFFFFF;

  z-index: 0;
  cursor: pointer;
}


.detail-image-prompt {
  position: absolute !important;
  right: -136px !important;
  bottom: 80px !important;
  width: 120px !important;
  z-index: 1003;
}

.detail-image-workflow {
  position: absolute !important;
  right: -136px !important;
  bottom: 16px !important;
  width: 120px !important;
  z-index: 1003;
}

.detail-flow-image-wrapper {
  position: absolute;
  top: 0;
  left: calc(-200px * 2 + 24px);
  width: calc (124px * 2 - 12px);
  height: fit-content;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(5, 1fr);
  gap: 12px;
  padding: 12px;
  background-color: lightgray;
  border-radius: 8px;
}

.img-preview-model-parameters {

  position: absolute;
  left: 102%;
  bottom: 0;
  padding: 16px;
  width: 210px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.8);
  // 可以根据实际情况调整字体等样式，让其显示更美观
  font-size: 14px;
  color: #333;
  overflow-wrap: break-word;
  word-break: break-all;
}