import React, { forwardRef, useEffect, useImperativeHandle, useState, useRef, useCallback } from 'react';
import { Drawer, DrawerProps, Flex, Button, message, Skeleton, Empty, Spin, Modal } from 'antd';
import { CaretLeftOutlined, CaretRightOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons';
import './StyleLoraDrawer.less';
import { ElementConfig, batchQueryElementById, getBasicChangeConfig, queryFavorElements, queryElementByPage } from '@/services/ElementController';
import { AnimationType, generateStaggeredAnimationStyle } from '@/utils/animationUtils';
import ElementWithTypeSelector from '@/components/Creative/ElementWithTypeSelector';
import { MiniTag } from '@/components/Common/CommonComponent';

// 定义清晰的数据结构类型
interface ChildItem {
  id: number;
  name: string;
  image: string;
  selected?: boolean;
  referenceConfig?: string | { showImage: string } | any;
  backExtTags?: string;
  backTags?: string;
}

// 场景数据接口
interface ElementData {
  extInfo?: {
    openScope?: string | number;
    [key: string]: any;
  };
  children?: Array<{
    id: number;
    name: string;
    extInfo: {
      showImage?: string;
      styleImage?: string;
      [key: string]: any;
    };
    tags: string;
    extTags: string;
    [key: string]: any;
  }>;
}

// Lora项接口
interface LoraItem {
  id: number;
  name: string;
  image: string;
  selected: boolean;
  children: ChildItem[];
  backExtTags?: string;
  backTags?: string;
  isExclusiveScene?: boolean;
}

// 选中项接口
export interface SelectedItem {
  id: number;
  name: string;
  image: string;
  parentId: number;
  parentName: string;
  referenceConfig?: { [key: string]: any };
  backExtTags?: string;
  backTags?: string;
  loraId?: number; // 具体图片记录的ID
}

// 导出LoraItem接口以供其他组件使用
export type { LoraItem, ChildItem };

// 基础抽屉组件接口
export interface BaseDrawerProps extends Omit<DrawerProps, 'open'> {
  // 是否VIP用户
  isVipUser: boolean;
  // 是否展开
  expand: boolean;
  // 改变展开状态的回调
  changeExpand: (expand: boolean) => void;
  // 选中的参考图回调，返回完整选中项信息
  onSelect?: (selectedItems: SelectedItem[]) => void;
  // 风格描述，用于智能推荐
  styleDescription?: string;
  // 父组件已选择的参考图列表
  selectedReferences?: { imageUrl: string, isSelected: boolean }[];
  // 当前类型
  currentType: string;
  // 预加载的场景模型数据
  preloadedSceneModels?: any[];
  // 更新预加载场景模型数据的回调
  onPreloadedSceneModelsUpdate?: (newModels: any[]) => void;
  // 场景配置
  sceneConfig?: any;
  // 最大选择数量，不传或传null表示无限制，传数字表示最大选择数量
  maxSelectCount?: number | null;
  // 加载提示文案，不传默认为"正在为您智能匹配合适款式的参考图..."
  loadingText?: string;
  // 筛选条件，由父组件传递
  conditions?: any;
  // 场景数据加载状态，由父组件传递
  sceneDataLoading?: boolean;
}

// 基础抽屉组件引用
export interface BaseDrawerRef {
  // 获取抽屉组件实例
  getDrawerInstance: () => {
    open: boolean;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  };
}

// 滚动箭头组件
interface ScrollArrowProps {
  direction: 'left' | 'right';
  onClick: () => void;
}

// 滚动箭头组件
const ScrollArrow: React.FC<ScrollArrowProps> = ({ direction, onClick }) => {
  const handleClick = (e: React.MouseEvent) => {
    // 阻止事件冒泡，防止干扰其他点击事件
    e.stopPropagation();
    e.preventDefault();
    onClick();
  };

  return (
    <div
      className={`scroll-arrow scroll-arrow-${direction}`}
      onClick={handleClick}
    >
      {direction === 'left' ? <LeftOutlined /> : <RightOutlined />}
    </div>
  );
};

// 更新场景选择按钮组件，添加点击事件
const SceneTypeButtons: React.FC<{
  onShowAllScenes: () => void;
  isRecommended: boolean;
  onSwitchSceneType: (isRecommended: boolean) => void;
  onShowExclusiveScenes: () => void;
  onShowFavoriteScenes: () => void;
  currentButtonType: 'recommended' | 'exclusive' | 'favor' | 'all' | null;
}> = ({ onShowAllScenes, isRecommended, onSwitchSceneType, onShowExclusiveScenes, onShowFavoriteScenes, currentButtonType }) => {
  return (
    <Flex className="scene-type-buttons">
      <Button
        className={`scene-type-item ${currentButtonType === 'recommended' ? 'scene-type-item-checked' : ''} recommend-scene-button`}
        onClick={() => onSwitchSceneType(true)}
      >
        <span className="recommend-scene-text">推荐场景</span>
        <span className="recommend-scene-desc">
          根据上传服装版型为你推荐场景
        </span>
      </Button>



      <Button
        className={`scene-type-item ${currentButtonType === 'exclusive' ? 'scene-type-item-checked' : ''}`}
        onClick={onShowExclusiveScenes}
      >
        <span className="all-scene-text">专属场景</span>
      </Button>

      <Button
        className={`scene-type-item ${currentButtonType === 'favor' ? 'scene-type-item-checked' : ''}`}
        onClick={onShowFavoriteScenes}
      >
        <span className="all-scene-text">收藏场景</span>
      </Button>

      <Button
        className={`scene-type-item ${currentButtonType === 'all' ? 'scene-type-item-checked' : ''}`}
        onClick={() => {
          // 无论当前是什么状态，点击"全部场景"按钮都应该打开场景选择器
          onShowAllScenes();
        }}
        disabled={false}
      >
        <span className="all-scene-text">全部场景</span>
      </Button>
    </Flex>
  );
};

// Lora项组件
const LoraItemComponent: React.FC<{
  item: LoraItem;
  onClick: (id: number) => void;
  index: number;
}> = ({ item, onClick, index }) => {
  const handleClick = (e: React.MouseEvent) => {
    // 确保点击事件正确处理
    e.stopPropagation();
    onClick(item.id);
  };

  return (
    <div
      className={`lora-item ${item.selected ? 'selected' : ''}`}
      style={generateStaggeredAnimationStyle(
        AnimationType.FADE_IN_ITEM,
        index % 10,
        50
      )}
      onClick={handleClick}
    >
      <Flex
        vertical
        align="center"
      >
        <div style={{ position: 'relative' }}>
          <img
            src={item.image}
            width={108}
            height={108}
            className="item-image"
            alt={item.name}
          />
          {/* 专属场景标识 */}
          {item.isExclusiveScene && (
            <MiniTag title={'专属'} position={'rightTop'} textFontSize={10} />
          )}
        </div>
        <div className="item-name">{item.name}</div>
      </Flex>
    </div>
  );
};

// Lora列表组件
const LoraList: React.FC<{
  loraList: LoraItem[];
  onSelectItem: (id: number) => void;
  scrollContainerRef: React.RefObject<HTMLDivElement>;
  showLeftArrow: boolean;
  showRightArrow: boolean;
  onScroll: () => void;
  onWheel: (event: React.WheelEvent<HTMLDivElement>) => void;
  scrollTo: (direction: 'left' | 'right') => void;
}> = ({
  loraList,
  onSelectItem,
  scrollContainerRef,
  showLeftArrow,
  showRightArrow,
  onScroll,
  onWheel,
  scrollTo
}) => {
    // 独立的Lora项点击处理函数
    const handleItemClick = useCallback((id: number) => {
      onSelectItem(id);
    }, [onSelectItem]);

    return (
      <div className="lora-list-container">
        {/* 左箭头只有在可以向左滚动时显示 */}
        {showLeftArrow && (
          <div className="arrow-container left-arrow">
            <ScrollArrow
              direction="left"
              onClick={() => scrollTo('left')}
            />
          </div>
        )}

        {/* 主要滚动容器 */}
        <div className="lora-list-wrapper">
          <Flex
            gap={16}
            className="lora-list scrollbar-hidden"
            ref={scrollContainerRef}
            onScroll={onScroll}
            onWheel={onWheel}
            style={{
              overflowX: 'auto',
              overflowY: 'hidden',
              display: 'flex',
              flexWrap: 'nowrap',
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
              WebkitOverflowScrolling: 'touch',
              scrollBehavior: 'auto'
            }}
          >
            {loraList.map((item, index) => (
              <LoraItemComponent
                key={item.id}
                index={index}
                item={item}
                onClick={handleItemClick}
              />
            ))}
          </Flex>
        </div>

        {/* 右箭头只有在可以向右滚动时显示 */}
        {showRightArrow && (
          <div className="arrow-container right-arrow">
            <ScrollArrow
              direction="right"
              onClick={() => scrollTo('right')}
            />
          </div>
        )}
      </div>
    );
  };


// 子项图片组件
const ChildItemComponent: React.FC<{
  child: ChildItem;
  index: number;
  onPreview: (image: string, child: ChildItem) => void;
  onSelect?: (child: ChildItem) => void;
}> = ({ child, index, onPreview, onSelect }) => {
  // 使用工具类生成动画样式，包含交错动画效果
  const animationStyle = generateStaggeredAnimationStyle(
    AnimationType.FADE_IN_ITEM,
    index % 10,
    50 // 每项延迟50ms
  );

  // 处理图片点击 - 直接预览
  const handleImageClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onPreview(child.image, child);
  };

  // 处理选择框点击
  const handleSelectClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    if (onSelect) {
      onSelect(child);
    }
  };

  return (
    <Flex
      key={child.id}
      vertical
      className={`child-item ${child.selected ? 'selected' : ''}`}
      style={animationStyle}
    >
      <div
        className="image-container"
        onClick={handleImageClick}
      >
        <img
          src={child.image}
          alt={child.name || `图片${child.id}`}
        />

        {/* 右上角圆形选择框 */}
        <div
          className={`circular-checkbox ${child.selected ? 'checked' : ''}`}
          onClick={handleSelectClick}
        >
          {child.selected && (
            <div className="checkbox-icon">✓</div>
          )}
        </div>

        {/* 选中状态指示器（已被圆形选择框替代，暂时隐藏） */}
        {/* {child.selected && (
          <div className="selected-indicator">
            <div className="selected-icon">✓</div>
          </div>
        )} */}
        {/* 悬停遮罩 */}
        <div className="hover-mask" onClick={handleImageClick}>
          <div className="preview-text">点击预览</div>
        </div>
      </div>
    </Flex>
  );
};

// 加载骨架屏组件
const LoadingSkeleton: React.FC = () => {
  return (
    <Flex className="child-items-flex ani-fade-in" gap={8}>
      {Array.from({ length: 10 }).map((_, index) => {
        const animationStyle = generateStaggeredAnimationStyle(
          AnimationType.FADE_IN_ITEM,
          index,
          30 // 每项延迟30ms
        );

        return (
          <div key={index} className="skeleton-item" style={animationStyle}>
            <Skeleton.Image active style={{ width: 118, height: 160, borderRadius: 8 }} />
          </div>
        );
      })}
    </Flex>
  );
};


// 展开/收起按钮组件
const ExpandButton: React.FC<{
  isExpanded: boolean;
  hover: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  onClick: () => void;
}> = ({
  isExpanded,
  hover,
  onMouseEnter,
  onMouseLeave,
  onClick
}) => {
    return (
      <Flex
        vertical
        align="center"
        justify="center"
        className={`logo-combine-drawer-expend ${!isExpanded ? 'logo-combine-drawer-contract' : ''}`}
        onClick={onClick}
        onMouseLeave={onMouseLeave}
        onMouseEnter={onMouseEnter}
      >
        {isExpanded ? (
          <CaretLeftOutlined className={`expand-icon ${hover ? 'active' : 'default'}`} />
        ) : (
          <CaretRightOutlined className={`expand-icon ${hover ? 'active' : 'default'}`} />
        )}
      </Flex>
    );
  };


// 风格Lora抽屉组件 用于展示和选择参考图片，支持滚动浏览和多选
const StyleLoraDrawer = forwardRef<BaseDrawerRef, BaseDrawerProps>(({
  isVipUser,
  expand,
  changeExpand,
  onSelect,
  children,
  styleDescription = '',
  selectedReferences = [],
  currentType,
  preloadedSceneModels,
  onPreloadedSceneModelsUpdate,
  sceneConfig: externalSceneConfig,
  maxSelectCount,
  loadingText = '正在为您智能匹配合适款式的参考图...',
  conditions,
  sceneDataLoading = false,
  ...drawerProps
}, ref) => {
  // 状态管理
  const [open, setOpen] = useState(false);
  const [hover, setHover] = useState(false);
  const [selectedItems, setSelectedItems] = useState<SelectedItem[]>([]);
  const [loraList, setLoraList] = useState<LoraItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);

  // 添加新状态
  const [showAllScenes, setShowAllScenes] = useState(false);
  const [isRecommended, setIsRecommended] = useState(true);
  const [sceneConfig, setSceneConfig] = useState<any>(null);
  const [selectedShowType, setSelectedShowType] = useState('all');
  const [currentButtonType, setCurrentButtonType] = useState<'recommended' | 'exclusive' | 'favor' | 'all' | null>('recommended');

  // 添加预览状态
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewChild, setPreviewChild] = useState<ChildItem | null>(null);
  const [selectedParentForPreview, setSelectedParentForPreview] = useState<LoraItem | null>(null);

  // 添加新状态来记录当前选中的场景ID
  const [currentSelectedSceneId, setCurrentSelectedSceneId] = useState<number | null>(null);

  // 引用
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const autoLoadingRef = useRef<boolean>(false);

  // 监听抽屉展开状态
  useEffect(() => {
    setOpen(expand);

    // 当抽屉打开时，清空已选状态，等待新数据加载
    if (expand && !open) {
      setLoading(true);
      // 清空当前选中的场景ID
      setCurrentSelectedSceneId(null);
    }
  }, [expand, open]);

  // 监听父组件传递的selectedReferences变化
  useEffect(() => {
    if (selectedReferences.length === 0) {
      // 如果父组件清空了所有引用图片，则清空内部选中状态
      setSelectedItems([]);

      // 同时更新loraList中每个子项的选中状态
      setLoraList(prevList =>
        prevList.map(loraItem => ({
          ...loraItem,
          children: loraItem.children.map(child => ({
            ...child,
            selected: false
          }))
        }))
      );
      return;
    }

    // 只处理父组件删除单个图片的情况
    // 寻找不再存在于selectedReferences中的图片URL
    const selectedUrls = selectedReferences.map(ref => ref.imageUrl);

    // 找到内部selectedItems中需要移除的项
    const itemsToRemove = selectedItems.filter(item =>
      !selectedUrls.includes(item.image)
    );

    // 如果有项需要移除
    if (itemsToRemove.length > 0) {
      // 更新selectedItems
      const newSelectedItems = selectedItems.filter(item =>
        selectedUrls.includes(item.image)
      );
      setSelectedItems(newSelectedItems);

      // 同时更新loraList中对应项的选中状态
      setLoraList(prevList =>
        prevList.map(loraItem => ({
          ...loraItem,
          children: loraItem.children.map(child => ({
            ...child,
            selected: newSelectedItems.some(item =>
              item.image === child.image && item.parentId === loraItem.id
            )
          }))
        }))
      );
    }
  }, [selectedReferences]);

  // 监听loraList变化，同步选中状态
  useEffect(() => {
    if (loraList.length === 0 || selectedReferences.length === 0) return;

    // 收集所有已选中的子项
    const newSelectedItems: SelectedItem[] = [];

    // 扫描loraList中已选中的图片
    loraList.forEach(loraItem => {
      loraItem.children.forEach(child => {
        if (child.selected) {
          // 将已选中的子项添加到selectedItems中
          newSelectedItems.push({
            id: child.id,
            name: child.name,
            image: child.image,
            parentId: loraItem.id,
            parentName: loraItem.name,
            referenceConfig: child.referenceConfig,
            backTags: child.backTags,
            backExtTags: child.backExtTags,
            loraId: child.id // 添加loraId字段，使用具体图片记录的ID
          });
        }
      });
    });

    // 检查是否有实际变化，避免不必要的状态更新
    if (JSON.stringify(newSelectedItems) !== JSON.stringify(selectedItems)) {
      // 更新selectedItems
      setSelectedItems(newSelectedItems);
    }

  }, [loraList, selectedReferences]);

  // 自动勾选父组件中已选中但在子组件中未选中的图片
  useEffect(() => {
    if (loraList.length === 0 || selectedReferences.length === 0 || loading) return;

    // 获取当前选中的场景
    const selectedParent = loraList.find(item => item.selected);
    if (!selectedParent) return;

    // 获取父组件中已选中的图片URL列表
    const selectedImageUrls = selectedReferences.map(ref => ref.imageUrl);

    // 创建一个标记，标记是否有任何变更，避免无限循环
    let hasChanges = false;

    // 需要更新的子项集合
    const itemsToUpdate: { parentItem: LoraItem, childItem: ChildItem }[] = [];

    // 检查当前选中场景的子项
    selectedParent.children.forEach(child => {
      // 如果图片在父组件已选中列表中，但在子组件中未选中
      if (selectedImageUrls.includes(child.image) && !child.selected) {
        hasChanges = true;
        itemsToUpdate.push({
          parentItem: selectedParent,
          childItem: child
        });
      }
    });

    // 如果有需要更新的项，批量更新
    if (hasChanges && itemsToUpdate.length > 0) {
      // 创建所有需要选中的图片信息
      const newSelectedItems = [...selectedItems];

      // 更新列表
      setLoraList(prevList => {
        // 创建一个新的列表副本，避免直接修改state
        const newList = [...prevList];

        // 找到当前选中的场景
        const sceneIndex = newList.findIndex(item => item.id === selectedParent.id);
        if (sceneIndex < 0) return prevList;

        // 更新场景中的子项
        newList[sceneIndex] = {
          ...newList[sceneIndex],
          children: newList[sceneIndex].children.map(child => {
            // 检查该子项是否在待更新列表中
            const shouldUpdate = itemsToUpdate.some(
              item => item.childItem.id === child.id && item.childItem.image === child.image
            );

            // 如果需要更新，则设置selected为true
            if (shouldUpdate) {
              // 同时添加到选中项列表
              newSelectedItems.push({
                id: child.id,
                name: child.name,
                image: child.image,
                parentId: selectedParent.id,
                parentName: selectedParent.name,
                referenceConfig: child.referenceConfig,
                backTags: child.backTags,
                backExtTags: child.backExtTags,
                loraId: child.id // 使用具体图片记录的ID作为loraId
              });

              return { ...child, selected: true };
            }

            return child;
          })
        };

        return newList;
      });

      // 更新选中项列表
      setSelectedItems(newSelectedItems);
    }
  }, [loraList, selectedReferences, loading]);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getDrawerInstance() {
      return {
        open,
        setOpen
      };
    }
  }));

  // 加载推荐场景数据
  useEffect(() => {
    if (!open) return;

    // 直接使用预加载的场景模型数据
    if (preloadedSceneModels !== undefined) {
      if (preloadedSceneModels.length > 0) {
        // 保存场景配置
        setSceneConfig(preloadedSceneModels[0]);
        // 处理场景模型
        processSceneModels(preloadedSceneModels);
      } else {
        // 当预加载数据为空数组时，也需要调用processSceneModels来正确设置状态
        processSceneModels([]);
      }
    }
  }, [open, preloadedSceneModels]);

  // 处理场景模型数据
  const processSceneModels = async (sceneModels: any[]) => {
    try {
      setLoading(true);

      // 确保数据有效
      if (!sceneModels || sceneModels.length === 0) {
        console.warn('场景模型数据为空');
        setLoraList([]); // 清空列表，确保显示"暂无配置场景"
        setLoading(false);
        return;
      }

      // 创建Lora项列表
      const loraItems: LoraItem[] = [];

      // 遍历每个场景模型
      for (const model of sceneModels) {
        // 确保model有效
        if (!model || !model.id || !model.name || !model.showImage) {
          console.warn('无效的场景模型数据:', model);
          continue;
        }

        // 获取场景数据 children
        const children = model.children || [];
        const isExclusiveScene = model.isExclusiveScene;

        // 将获取到的图片转换为ChildItem数组，根据场景类型过滤不同的图片字段
        const childItems: ChildItem[] = Array.isArray(children) ?
          children
            .filter((elementItem) => {
              if (isExclusiveScene) {
                // 专属场景：只需要styleImage存在
                return typeof elementItem?.extInfo?.styleImage === 'string';
              } else {
                // 普通场景：只需要showImage存在
                return typeof elementItem?.extInfo?.showImage === 'string';
              }
            })
            .map((elementItem) => {
              // 根据场景类型选择不同的图片字段
              const imageUrl = isExclusiveScene ? elementItem.extInfo.styleImage : elementItem.extInfo.showImage;
              const isSelected = selectedReferences.some(ref => ref.imageUrl === imageUrl);

              return {
                id: elementItem.id,
                name: elementItem.name,
                image: imageUrl,
                selected: isSelected,
                referenceConfig: elementItem.extInfo,
                backTags: elementItem.tags || '',
                backExtTags: elementItem.extTags || '',
              };
            }) : [];

        // 检查场景是否有有效的子项数据
        if (childItems.length === 0) {
          continue;
        }

        // 只有当场景有子项数据时才添加到列表中
        loraItems.push({
          id: model.id,
          name: model.name,
          image: model.showImage,
          selected: false,
          children: childItems,
          backTags: model.tags || '',
          backExtTags: model.extTags || '',
          isExclusiveScene: isExclusiveScene
        });
      }

      // 重新排序 isExclusiveScene 为 true 的排在前面
      loraItems.sort((a, b) => {
        if (a.isExclusiveScene && !b.isExclusiveScene) return -1;
        if (!a.isExclusiveScene && b.isExclusiveScene) return 1;
        return 0;
      });

      // 更新Lora列表
      setLoraList(loraItems);

      // 如果过滤后没有有效的场景，直接返回（会显示"暂无配置场景"）
      if (loraItems.length === 0) {
        console.warn('过滤后没有有效的场景数据');
        return;
      }

      // 检查是否有已选中图片的场景
      const sceneWithSelectedImages = loraItems.findIndex(item =>
        item.children.some(child => child.selected)
      );

      // 如果有已选中图片的场景，选中该场景
      if (sceneWithSelectedImages >= 0) {
        setLoraList(prevList =>
          prevList.map((item, index) => ({
            ...item,
            selected: index === sceneWithSelectedImages
          }))
        );
      }
      // 否则默认选中第一个项
      else if (loraItems.length > 0) {
        selectFirstItem(loraItems);
      }
    } catch (error) {
      console.error('处理场景模型数据失败:', error);
      message.error('加载场景数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 默认选中第一个项
  const selectFirstItem = (items: LoraItem[]) => {
    // 检查是否有已有选中的场景
    const hasSelectedScene = items.some(item =>
      item.children.some(child => child.selected)
    );

    // 如果有已选中的图片，就不要自动选择第一个场景
    if (hasSelectedScene) {
      return;
    }

    // 否则默认选中第一个场景
    setLoraList(prevList =>
      prevList.map((item, index) => ({
        ...item,
        selected: index === 0
      }))
    );
  };

  // 处理水平滚动逻辑
  const handleScroll = useCallback(() => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;

      // 简单的边界判断
      setShowLeftArrow(scrollLeft > 1);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 1);
    }
  }, []);

  // 箭头按钮专用：滚动到指定方向（固定偏移量）
  const scrollTo = useCallback((direction: 'left' | 'right') => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;

      // 固定偏移量：一次滚动300px
      const ARROW_SCROLL_AMOUNT = 300;

      // 计算目标位置
      let targetScrollLeft;
      if (direction === 'right') {
        // 右箭头：向右滚动300px，但不超过最大滚动位置
        targetScrollLeft = Math.min(
          scrollLeft + ARROW_SCROLL_AMOUNT,
          scrollWidth - clientWidth
        );
      } else {
        // 左箭头：向左滚动300px，但不小于0
        targetScrollLeft = Math.max(
          scrollLeft - ARROW_SCROLL_AMOUNT,
          0
        );
      }

      // 平滑滚动到目标位置
      scrollContainerRef.current.scrollTo({
        left: targetScrollLeft,
        behavior: 'smooth'
      });

      // 滚动完成后更新箭头状态
      setTimeout(() => {
        handleScroll();
      }, 300);
    }
  }, [handleScroll]);

  // 处理滚轮和触控板事件（自由滚动）
  const handleWheel = useCallback((event: React.WheelEvent<HTMLDivElement>) => {
    if (!scrollContainerRef.current) return;

    const container = scrollContainerRef.current;

    // 触控板水平滚动：直接使用deltaX，不干预浏览器原生行为
    if (Math.abs(event.deltaX) > 0) {
      // 让浏览器自然处理水平滚动
      return;
    }

    // 鼠标滚轮垂直滚动：转换为水平滚动
    if (Math.abs(event.deltaY) > 0) {
      // 阻止默认的垂直滚动
      event.preventDefault();

      // 直接使用deltaY的值，不做过多处理，保持自然的滚动感
      const scrollAmount = event.deltaY;

      // 获取当前滚动位置
      const currentScrollLeft = container.scrollLeft;

      // 计算新的滚动位置（不做边界限制，让浏览器自然处理）
      const newScrollLeft = currentScrollLeft + scrollAmount;

      // 直接设置scrollLeft，让浏览器处理边界
      container.scrollLeft = newScrollLeft;

      // 立即更新箭头状态
      handleScroll();
    }
  }, [handleScroll]);

  // 初始化箭头状态并监听loraList变化
  useEffect(() => {
    if (loraList.length > 0 && scrollContainerRef.current) {
      // 确保DOM已渲染后再检查滚动状态
      const timeoutId = setTimeout(() => {
        handleScroll();
      }, 50);

      // 清理函数
      return () => clearTimeout(timeoutId);
    }
  }, [loraList, handleScroll]);

  // 监听窗口大小变化，更新箭头状态
  useEffect(() => {
    const handleResize = () => {
      if (scrollContainerRef.current) {
        handleScroll();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [handleScroll]);

  // 选择父级Lora项
  const handleSelectLoraItem = useCallback(async (id: number) => {
    // 如果已经选中，则不做任何操作
    const isAlreadySelected = loraList.find(item => item.id === id && item.selected);
    if (isAlreadySelected) return;

    // 找到当前选中的场景
    const selectedScene = loraList.find(item => item.id === id);
    if (!selectedScene) return;

    // 设置加载状态
    setLoading(true);

    try {
      // 如果场景已经有子项数据，直接切换
      if (selectedScene.children && selectedScene.children.length > 0) {
        setLoraList(prevList =>
          prevList.map(item => ({
            ...item,
            selected: item.id === id
          }))
        );
      } else {
        // 如果没有子项数据，则加载
        const elementsDataMap = await batchQueryElementById([id]);
        
        if (!elementsDataMap || !elementsDataMap[id]) {
          console.warn(`未找到ID为${id}的元素数据`);
          setLoading(false);
          return;
        }

        const elementData = elementsDataMap[id];
        const children = elementData.children || [];

        // 判断是否为专属场景
        const isExclusiveScene = !!(elementData?.extInfo?.openScope && 
          (typeof elementData.extInfo.openScope === 'number' || 
           (typeof elementData.extInfo.openScope === 'string' && !isNaN(Number(elementData.extInfo.openScope)))));

        // 处理子项数据
        const childItems: ChildItem[] = children
          .filter((elementItem) => {
            if (isExclusiveScene) {
              // 专属场景：优先使用styleImage，如果没有则使用showImage
              return typeof elementItem?.extInfo?.styleImage === 'string' || 
                     typeof elementItem?.extInfo?.showImage === 'string';
            } else {
              // 普通场景：只需要showImage存在
              return typeof elementItem?.extInfo?.showImage === 'string';
            }
          })
          .map((elementItem) => {
            // 根据场景类型选择不同的图片字段
            let imageUrl: string;
            if (isExclusiveScene) {
              // 专属场景：优先使用styleImage，如果没有则使用showImage
              imageUrl = elementItem.extInfo.styleImage || elementItem.extInfo.showImage || '';
            } else {
              // 普通场景：使用showImage
              imageUrl = elementItem.extInfo.showImage || '';
            }
            
            const isSelected = selectedReferences.some(ref => ref.imageUrl === imageUrl);
            
            return {
              id: elementItem.id,
              name: elementItem.name,
              image: imageUrl,
              selected: isSelected,
              referenceConfig: elementItem.extInfo,
              backTags: elementItem.tags || '',
              backExtTags: elementItem.extTags || ''
            };
          });

        // 更新场景数据，添加子项并设置为选中状态
        setLoraList(prevList =>
          prevList.map(item => {
            if (item.id === id) {
              return {
                ...item,
                children: childItems,
                selected: true
              };
            }
            return {
              ...item,
              selected: false
            };
          })
        );
      }
    } catch (error) {
      console.error('加载场景子项数据失败:', error);
      message.error('加载场景数据失败');
    } finally {
      setLoading(false);
    }
  }, [loraList, selectedReferences]);

  // 处理子项选择
  const handleSelectChildItem = useCallback((parentItem: LoraItem, childItem: ChildItem) => {
    // 检查选中数量限制（只有当maxSelectCount有值且大于0时才进行限制）
    if (maxSelectCount && maxSelectCount > 0) {
      const selectedCount = loraList.flatMap(item =>
        item.children.filter(child => child.selected)).length;

      // 如果当前未选中且已达到上限，则显示警告
      if (selectedCount >= maxSelectCount && !childItem.selected) {
        message.warning(`最多只能选择${maxSelectCount}张参考图！`);
        return;
      }
    }

    // 准备更新选中状态
    const newSelected = !childItem.selected;

    // 更新选中项列表
    let newSelectedItems = [...selectedItems];

    if (newSelected) {
      // 添加新选中项
      const completeItemInfo: SelectedItem = {
        id: childItem.id,
        name: childItem.name,
        image: childItem.image,
        parentId: parentItem.id,
        parentName: parentItem.name,
        referenceConfig: childItem.referenceConfig,
        backTags: childItem.backTags,
        backExtTags: childItem.backExtTags,
        loraId: childItem.id // 使用具体图片记录的ID作为loraId
      };

      newSelectedItems.push(completeItemInfo);
    } else {
      // 移除取消选中的项
      newSelectedItems = newSelectedItems.filter(
        item => !(item.id === childItem.id && item.parentId === parentItem.id && item.image === childItem.image)
      );
    }

    // 更新状态
    setSelectedItems(newSelectedItems);

    // 更新列表中的选中状态
    setLoraList(prevList =>
      prevList.map(loraItem => {
        if (loraItem.id !== parentItem.id) return loraItem;

        return {
          ...loraItem,
          children: loraItem.children.map(child => {
            if (child.id !== childItem.id) return child;
            return { ...child, selected: newSelected };
          })
        };
      })
    );

    // 调用父组件的回调函数
    if (typeof onSelect === 'function') {
      onSelect(newSelectedItems);
    }
  }, [loraList, selectedItems, onSelect, maxSelectCount]);

  // 处理图片预览
  const handlePreview = useCallback((imageUrl: string) => {
    setPreviewImage(imageUrl);
    setPreviewVisible(true);
  }, []);

  // 处理带选择功能的预览
  const handlePreviewWithSelection = useCallback((imageUrl: string, child: ChildItem) => {
    const selectedParent = loraList.find(item => item.selected);
    if (selectedParent) {
      setPreviewImage(imageUrl);
      setPreviewChild(child);
      setSelectedParentForPreview(selectedParent);
      setPreviewVisible(true);
    }
  }, [loraList]);

  // 处理预览关闭
  const handleCancelPreview = useCallback((imageUrl: string = '') => {
    setPreviewVisible(false);
    setPreviewChild(null);
    setSelectedParentForPreview(null);
  }, []);

  // 处理确认选择
  const handleConfirmSelection = useCallback(() => {
    if (previewChild && selectedParentForPreview) {
      handleSelectChildItem(selectedParentForPreview, previewChild);
      setPreviewVisible(false);
      setPreviewChild(null);
      setSelectedParentForPreview(null);
    }
  }, [previewChild, selectedParentForPreview, handleSelectChildItem]);

  // 渲染子项图片列表
  const renderChildItems = useCallback(() => {
    const selectedParent = loraList.find(item => item.selected);

    // 当父组件或当前组件正在加载时，显示加载状态
    if (loading || sceneDataLoading) {
      return (
        <div className="child-items-container">
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%',
            minHeight: '300px',
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)'
          }}>
            <Spin size="large" style={{ marginBottom: '16px' }} />
            <span style={{ fontSize: '14px', color: '#366EF4' }}>{loadingText}</span>
          </div>
        </div>
      );
    }

    // 当已经加载完成但loraList为空时，显示"暂无可用场景"
    if (loraList.length === 0) {
      return (
        <div className="child-items-container">
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%',
            minHeight: '300px',
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)'
          }}>
            <Empty
              className="ani-fade-in"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={<span style={{ fontSize: '16px', color: '#727375' }}>暂无可用场景</span>}
              style={{
                textAlign: 'center'
              }}
              imageStyle={{
                height: 60,
                margin: '0 auto'
              }}
            />
          </div>
        </div>
      );
    }

    if (!selectedParent) return null;

    // 如果正在加载且没有数据，显示加载骨架
    if (loading && (!selectedParent.children || selectedParent.children.length === 0)) {
      return (
        <div className="child-items-container">
          <LoadingSkeleton />
        </div>
      );
    }

    return (
      <div className="child-items-container">
        {selectedParent.children.length === 0 ? (
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%',
            minHeight: '300px',
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)'
          }}>
            <Empty
              className="ani-fade-in"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={<span style={{ fontSize: '16px', color: '#727375' }}>暂无数据</span>}
              style={{
                textAlign: 'center'
              }}
              imageStyle={{
                height: 60,
                margin: '0 auto'
              }}
            />
          </div>
        ) : (
          <Flex className="child-items-flex ani-fade-in" gap={8}>
            {selectedParent.children.map((child, index) => (
              <ChildItemComponent
                key={child.id}
                child={child}
                index={index}
                onPreview={handlePreviewWithSelection}
                onSelect={(selectedChild) => handleSelectChildItem(selectedParent, selectedChild)}
              />
            ))}
          </Flex>
        )}
      </div>
    );
  }, [loraList, loading, sceneDataLoading, handleSelectChildItem, handlePreview, preloadedSceneModels]);

  // useCallback 用于缓存函数引用,避免每次渲染都创建新函数
  // 只有当依赖项 changeExpand 变化时才会重新创建函数
  // 这样可以避免不必要的重渲染,优化性能
  // 处理抽屉关闭
  const handleDrawerClose = useCallback(() => {
    setOpen(false);
    changeExpand(false);
  }, [changeExpand]);

  // 处理展开按钮点击
  const handleExpandClick = useCallback(() => {
    changeExpand(true);
  }, [changeExpand]);

  // 处理收起按钮点击
  const handleCollapseClick = useCallback(() => {
    changeExpand(false);
  }, [changeExpand]);

  // 处理鼠标悬停
  const handleMouseEnter = useCallback(() => {
    setHover(true);
  }, []);

  // 处理鼠标离开
  const handleMouseLeave = useCallback(() => {
    setHover(false);
  }, []);

  // 处理显示全部场景
  const handleShowAllScenes = useCallback(() => {
    setCurrentButtonType('all');
    
    // 如果没有设置特定的显示类型，则设置为全部
    if (selectedShowType === 'all') {
      setSelectedShowType('all');
    }
    
    // 优先使用外部传入的sceneConfig
    if (externalSceneConfig) {
      setSceneConfig(externalSceneConfig);
      setShowAllScenes(true);
      return;
    }

    // 如果外部没有传入sceneConfig且内部sceneConfig不存在，先获取场景配置
    if (!sceneConfig) {
      setLoading(true);
      getBasicChangeConfig().then(recommendedLoras => {
        if (!recommendedLoras?.length) {
          setLoading(false);
          return;
        }

        // 过滤出configKey为SCENE的数据
        const sceneModels = recommendedLoras.filter(model => model.configKey === 'SCENE');
        if (sceneModels.length > 0) {
          setSceneConfig(sceneModels[0]);
          // 配置设置完成后显示全部场景
          setShowAllScenes(true);
        }
        setLoading(false);
      })
        .catch(error => {
          console.error('获取场景配置失败:', error);
          setLoading(false);
        });
    } else {
      // 如果sceneConfig已存在，直接显示全部场景
      setShowAllScenes(true);
    }
  }, [externalSceneConfig, sceneConfig, styleDescription, selectedShowType]);

  // 处理场景类型切换
  const handleSwitchSceneType = useCallback((isRecommended: boolean) => {
    setIsRecommended(isRecommended);
    setCurrentButtonType('recommended');

    // 如果从全部场景切换到推荐场景，重新加载推荐数据
    if (isRecommended && !loading && preloadedSceneModels?.length) {
      processSceneModels(preloadedSceneModels);
    }
  }, [loading, preloadedSceneModels]);

  // 构建查询参数的通用函数（参考ElementWithTypeSelector的buildQuery）
  const buildSceneQuery = useCallback((isExclusive: boolean = false) => {
    return {
      configKey: 'SCENE',
      pageNum: 1,
      pageSize: 20, // 获取更多数据
      isExclusive: isExclusive ? isExclusive : null,
      onlyLora: true,
      isHasShowImage: true, // 只查询具有展示图片 的元素
    };
  }, []);

  // 过滤场景数据的通用函数
  const filterScenesWithImages = useCallback((scenes: ElementConfig[]): LoraItem[] => {
    if (!scenes || scenes.length === 0) return [];

    const filteredItems: LoraItem[] = [];

    scenes.forEach(scene => {
      // 检查场景是否有展示图
      if (!scene.showImage) return;

      // 判断是否为专属场景
      const isExclusiveScene = !!(scene?.extInfo?.openScope && 
        (typeof scene.extInfo.openScope === 'number' || 
         (typeof scene.extInfo.openScope === 'string' && !isNaN(Number(scene.extInfo.openScope)))));

      // 创建场景项
      const loraItem: LoraItem = {
        id: scene.id,
        name: scene.name,
        image: scene.showImage,
        selected: false,
        children: [], // 暂时为空，后续需要时再加载
        backTags: (scene as any).tags || '',
        backExtTags: (scene as any).extTags || '',
        isExclusiveScene: isExclusiveScene
      };

      filteredItems.push(loraItem);
    });

    // 专属场景排在前面
    filteredItems.sort((a, b) => {
      if (a.isExclusiveScene && !b.isExclusiveScene) return -1;
      if (!a.isExclusiveScene && b.isExclusiveScene) return 1;
      return 0;
    });

    return filteredItems;
  }, []);



  // 处理显示专属场景
  const handleShowExclusiveScenes = useCallback(async () => {
    setCurrentButtonType('exclusive');
    setLoading(true);
    try {
      const query = buildSceneQuery(true); // 查询专属场景
      const response = await queryElementByPage(query);
      
      if (!response || !response.list) {
        message.info('暂无专属场景数据');
        return;
      }

      const filteredScenes = filterScenesWithImages(response.list);
      
      if (filteredScenes.length === 0) {
        message.info('专属场景中暂无可用数据');
      } else {
        setLoraList(filteredScenes);
        // 默认选中第一个场景
        if (filteredScenes.length > 0) {
          selectFirstItem(filteredScenes);
        }
        // 切换到非推荐模式
        setIsRecommended(false);
        message.success(`已加载 ${filteredScenes.length} 个专属场景`);
      }
    } catch (error) {
      console.error('获取专属场景失败:', error);
      message.error('获取专属场景失败');
    } finally {
      setLoading(false);
    }
  }, [buildSceneQuery, filterScenesWithImages]);

  // 处理显示收藏场景
  const handleShowFavoriteScenes = useCallback(async () => {
    setCurrentButtonType('favor');
    setLoading(true);
    try {
      const query = buildSceneQuery(false); // 查询收藏场景
      const response = await queryFavorElements(query);
      
      if (!response || !response.list) {
        message.info('暂无收藏场景数据');
        return;
      }

      const filteredScenes = filterScenesWithImages(response.list);
      
      if (filteredScenes.length === 0) {
        message.info('收藏场景中暂无可用数据');
      } else {
        setLoraList(filteredScenes);
        // 默认选中第一个场景
        if (filteredScenes.length > 0) {
          selectFirstItem(filteredScenes);
        }
        // 切换到非推荐模式
        setIsRecommended(false);
        message.success(`已加载 ${filteredScenes.length} 个收藏场景`);
      }
    } catch (error) {
      console.error('获取收藏场景失败:', error);
      message.error('获取收藏场景失败');
    } finally {
      setLoading(false);
    }
  }, [buildSceneQuery, filterScenesWithImages]);

  // 处理场景选择
  const handleSceneSelect = useCallback((value: Array<ElementConfig>) => {
    if (!value || !value.length) return;

    // 选中场景后关闭选择器
    setShowAllScenes(false);

    const selectedScene = value[0];

    // 检查选中的场景是否已在推荐列表中
    const existingSceneIndex = loraList.findIndex(item => item.id === selectedScene.id);

    if (existingSceneIndex >= 0) {
      // 如果场景已存在，直接激活为选中状态
      setLoraList(prevList =>
        prevList.map((item, index) => ({
          ...item,
          selected: index === existingSceneIndex
        }))
      );

      // 记录当前选中的场景ID
      setCurrentSelectedSceneId(selectedScene.id);

      // 计算需要滚动的位置
      if (scrollContainerRef.current) {
        const itemWidth = 124; // 每个卡片的宽度(108) + 间距(16)
        const scrollPosition = existingSceneIndex * itemWidth;

        // 使用平滑滚动到目标位置
        scrollContainerRef.current.scrollTo({
          left: scrollPosition,
          behavior: 'smooth'
        });
      }
      return;
    }

    // 如果场景不存在，使用batchQueryElementById查询场景数据
    setLoading(true);

    batchQueryElementById([selectedScene.id])
      .then(elementsDataMap => {
        if (!elementsDataMap) {
          console.warn('批量查询元素返回为空');
          setLoading(false);
          return;
        }

        const elementData = elementsDataMap[selectedScene.id] as ElementData;
        if (!elementData) {
          console.warn(`未找到ID为${selectedScene.id}的元素数据`);
          setLoading(false);
          return;
        }

        // 记录当前选中的场景ID
        setCurrentSelectedSceneId(selectedScene.id);

        // 获取场景数据 children
        const children = elementData.children || [];

        // 判断是否为专属场景（为数字或字符串类型的数字）
        const isExclusiveScene = !!(elementData?.extInfo?.openScope && 
          (typeof elementData.extInfo.openScope === 'number' || 
           (typeof elementData.extInfo.openScope === 'string' && !isNaN(Number(elementData.extInfo.openScope)))));

        // 将获取到的图片转换为ChildItem数组，根据场景类型过滤不同的图片字段
        const childItems: ChildItem[] = Array.isArray(children) ?
          children
            .filter((elementItem) => {
              const hasShowImage = typeof elementItem?.extInfo?.showImage === 'string';
              const hasStyleImage = typeof elementItem?.extInfo?.styleImage === 'string';
              
              if (isExclusiveScene) {
                // 专属场景：优先使用styleImage，如果没有则使用showImage
                return hasStyleImage || hasShowImage;
              } else {
                // 普通场景：只需要showImage存在
                return hasShowImage;
              }
            })
            .map((elementItem) => {
              // 根据场景类型选择不同的图片字段
              let imageUrl: string;
              if (isExclusiveScene) {
                // 专属场景：优先使用styleImage，如果没有则使用showImage
                imageUrl = elementItem.extInfo.styleImage || elementItem.extInfo.showImage || '';
              } else {
                // 普通场景：使用showImage
                imageUrl = elementItem.extInfo.showImage || '';
              }
              
              const isSelected = selectedReferences.some(ref => ref.imageUrl === imageUrl);
              
              
              return {
                id: elementItem.id,
                name: elementItem.name,
                image: imageUrl,
                selected: isSelected,
                referenceConfig: elementItem.extInfo,
                backTags: elementItem.tags || '',
                backExtTags: elementItem.extTags || ''
              };
            }) : [];

        // 检查场景是否有有效的子项数据
        if (childItems.length === 0) {
          console.log(`场景 "${selectedScene.name}" 没有有效的子项数据，不添加到列表中`);
          message.warning(`场景 "${selectedScene.name}" 暂无可用的参考图`);
          setLoading(false);
          return;
        }

        // 创建新的Lora项
        const newLoraItem: LoraItem = {
          id: selectedScene.id,
          name: selectedScene.name,
          image: selectedScene.showImage || '',
          selected: true,
          children: childItems,
          backTags: '',
          backExtTags: '',
          isExclusiveScene: isExclusiveScene
        };

        // 更新loraList，将新场景添加进去并设为选中状态
        setLoraList(prevList => {
          const newList = prevList.map(item => ({
            ...item,
            selected: false
          }));
          const finalList = [...newList, newLoraItem];

          return finalList;
        });

        // 关闭加载状态
        setLoading(false);

        // 切换到非推荐模式
        setIsRecommended(false);

        // 滚动到列表末尾
        setTimeout(() => {
          if (scrollContainerRef.current) {
            scrollContainerRef.current.scrollTo({
              left: scrollContainerRef.current.scrollWidth,
              behavior: 'smooth'
            });
          }
        }, 100);
      })
      .catch(error => {
        console.error('获取场景数据失败:', error);
        message.error('获取场景数据失败');
        setLoading(false);
      });
  }, [loraList, selectedReferences, preloadedSceneModels, onPreloadedSceneModelsUpdate]);

  // 监听 preloadedSceneModels 变化，保持选中状态
  useEffect(() => {
    if (currentSelectedSceneId && preloadedSceneModels) {
      
      // 更新 loraList 中的选中状态
      setLoraList(prevList => {
        // 检查当前选中的场景ID是否已经在列表中
        const sceneExists = prevList.some(item => item.id === currentSelectedSceneId);
        
        // 如果场景不存在于列表中，不要更新选中状态，避免覆盖新添加的场景
        if (!sceneExists) {
          console.log('场景不存在于列表中，跳过选中状态更新');
          return prevList;
        }
        
        return prevList.map(item => ({
          ...item,
          selected: item.id === currentSelectedSceneId
        }));
      });
    }
  }, [preloadedSceneModels, currentSelectedSceneId]);

  // 监听loraList变化，自动加载第一个选中场景的子项数据（避免无限循环）
  useEffect(() => {
    const selectedScene = loraList.find(item => item.selected);
    
    // 如果有选中的场景但没有子项数据，且不在加载状态，且没有正在自动加载，则自动加载
    if (selectedScene && selectedScene.children.length === 0 && !loading && !autoLoadingRef.current) {
      autoLoadingRef.current = true;
      
      // 使用setTimeout避免在同一个渲染周期内修改状态
      const timeoutId = setTimeout(async () => {
        try {
          setLoading(true);
          const elementsDataMap = await batchQueryElementById([selectedScene.id]);
          
          if (!elementsDataMap || !elementsDataMap[selectedScene.id]) {
            console.warn(`未找到ID为${selectedScene.id}的元素数据`);
            return;
          }

          const elementData = elementsDataMap[selectedScene.id];
          const children = elementData.children || [];

          // 判断是否为专属场景
          const isExclusiveScene = !!(elementData?.extInfo?.openScope && 
            (typeof elementData.extInfo.openScope === 'number' || 
             (typeof elementData.extInfo.openScope === 'string' && !isNaN(Number(elementData.extInfo.openScope)))));

          // 处理子项数据
          const childItems: ChildItem[] = children
            .filter((elementItem) => {
              if (isExclusiveScene) {
                // 专属场景：优先使用styleImage，如果没有则使用showImage
                return typeof elementItem?.extInfo?.styleImage === 'string' || 
                       typeof elementItem?.extInfo?.showImage === 'string';
              } else {
                // 普通场景：只需要showImage存在
                return typeof elementItem?.extInfo?.showImage === 'string';
              }
            })
            .map((elementItem) => {
              // 根据场景类型选择不同的图片字段
              let imageUrl: string;
              if (isExclusiveScene) {
                // 专属场景：优先使用styleImage，如果没有则使用showImage
                imageUrl = elementItem.extInfo.styleImage || elementItem.extInfo.showImage || '';
              } else {
                // 普通场景：使用showImage
                imageUrl = elementItem.extInfo.showImage || '';
              }
              
              const isSelected = selectedReferences.some(ref => ref.imageUrl === imageUrl);
              
              return {
                id: elementItem.id,
                name: elementItem.name,
                image: imageUrl,
                selected: isSelected,
                referenceConfig: elementItem.extInfo,
                backTags: elementItem.tags || '',
                backExtTags: elementItem.extTags || ''
              };
            });

          // 更新场景数据，添加子项并设置为选中状态
          setLoraList(prevList =>
            prevList.map(item => {
              if (item.id === selectedScene.id) {
                return {
                  ...item,
                  children: childItems,
                  selected: true
                };
              }
              return {
                ...item,
                selected: false
              };
            })
          );
        } catch (error) {
          console.error('自动加载场景子项数据失败:', error);
        } finally {
          setLoading(false);
          autoLoadingRef.current = false;
        }
      }, 100);
      
      return () => {
        clearTimeout(timeoutId);
        autoLoadingRef.current = false;
      };
    }
  }, [loraList, loading, selectedReferences]);

  return (
    <div className="style-lora-drawer-wrapper">
      <Drawer
        placement="left"
        closable={false}
        onClose={handleDrawerClose}
        open={open}
        getContainer={false}
        className="base-drawer"
        {...drawerProps}
      >
        <div className="drawer-content">
          {/* 风格 Lora 组件 */}
          <Flex vertical className="lora-grid">
            {/* 场景类型按钮 */}
            <SceneTypeButtons
              onShowAllScenes={handleShowAllScenes}
              isRecommended={isRecommended}
              onSwitchSceneType={handleSwitchSceneType}
              onShowExclusiveScenes={handleShowExclusiveScenes}
              onShowFavoriteScenes={handleShowFavoriteScenes}
              currentButtonType={currentButtonType}
            />

            {/* 风格 Lora 列表 */}
            <LoraList
              loraList={loraList}
              onSelectItem={handleSelectLoraItem}
              scrollContainerRef={scrollContainerRef}
              showLeftArrow={showLeftArrow}
              showRightArrow={showRightArrow}
              onScroll={handleScroll}
              onWheel={handleWheel}
              scrollTo={scrollTo}
            />
          </Flex>

          {/* 风格 Lora 图片列表 */}
          <div className="child-items-section">
            <div className="child-items-header">
              <span className="child-items-title">场景姿势图</span>
            </div>
            {renderChildItems()}
          </div>
        </div>

        {/* 收起按钮 */}
        <ExpandButton
          isExpanded={true}
          hover={hover}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          onClick={handleCollapseClick}
        />
      </Drawer>

      {/* 展开按钮 */}
      {!open && (
        <ExpandButton
          isExpanded={false}
          hover={hover}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          onClick={handleExpandClick}
        />
      )}

      {/* 场景选择器弹窗 */}
      {showAllScenes && sceneConfig && (
        <ElementWithTypeSelector
          title="风格场景"
          loraType='CUSTOM'
          config={sceneConfig}
          onClose={() => {
            setShowAllScenes(false);
            // 关闭时重置为默认状态
            setSelectedShowType('all');
          }}
          current={[]}
          onChange={handleSceneSelect}
          isVipUser={isVipUser}
          onlyLora={true}
          showConfirmFooter={true}
          isShowPosture={true}
          isHasShowImage={true}
          selectedShowType={selectedShowType}
          conditions={conditions}
        />
      )}

      {/* 自定义图片预览组件 */}
      <Modal
        className="img-preview-modal style-lora-preview-modal"
        open={previewVisible}
        closable={false}
        footer={null}
        width={'auto'}
        onCancel={() => handleCancelPreview()}
        onOk={() => handleCancelPreview()}
      >
        <Flex vertical justify={'space-between'} gap={16} className={'img-detail-container'}>
          <Flex style={{ position: 'relative' }} align={'center'} className={'preview-image-wrapper'}>
            <img
              src={previewImage}
              alt="预览图片"
              className="img-detail-img-no-tools preview-main-image"
            />

            {/* 选中状态指示器 - 类似原版的✅图标 */}
            {previewChild?.selected && (
              <div style={{
                position: 'absolute',
                top: 16,
                right: 16,
                width: 64,
                height: 64,
                background: 'white',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'
              }}>
                <span style={{ fontSize: 40, color: '#52c41a' }}>✓</span>
              </div>
            )}
          </Flex>

          {/* 底部按钮 - 采用原版的样式 */}
          <Flex gap={8} align={'center'} justify={'center'}>
            <Flex gap={4} className={'img-detail-repair-block img-detail-btn'}
              onClick={() => handleCancelPreview()}>
              <div className={'text16 font-pf color-1a'}>取消</div>
            </Flex>

            <Flex gap={4} className={'img-detail-repair-block img-detail-btn primary-action'}
              onClick={previewChild?.selected ? undefined : handleConfirmSelection}
              style={{
                opacity: previewChild?.selected ? 0.6 : 1,
                cursor: previewChild?.selected ? 'not-allowed' : 'pointer'
              }}>
              <div className={'text16 font-pf color-1a'}>
                {previewChild?.selected ? '已选择' : '选择'}
              </div>
            </Flex>
          </Flex>
        </Flex>
      </Modal>
    </div>
  );
});

export default StyleLoraDrawer;