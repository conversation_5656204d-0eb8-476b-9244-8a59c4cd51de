@import "@/app";

@body-width: calc(100vw - @menu-width);
@free-width: 24px;

// 定义颜色变量
@primary-color: #366EF4;
@border-light: #e8e8e8;
@border-selected: #366EF4;
@text-primary: #1A1B1D;
@text-secondary: #727375;
@bg-light: #FFFFFF;
@bg-hover: #F2F3FF;
@bg-gray: #F5F6F9;

.style-lora-drawer-wrapper {

  .ant-drawer-body {
    border: 1px solid @border-light;
    padding: 16px;
  }

  .base-drawer {
    position: absolute;
    width: calc(@body-width / 2) !important;
    height: 100% !important;
    background: @bg-light;
    box-sizing: border-box;

    :global {
      .ant-drawer-content-wrapper {
        box-shadow: none;
        position: relative;
      }

      .ant-drawer-content {
        background-color: @bg-light;
        border-radius: 0 8px 8px 0;
        box-shadow: 4px 0 12px rgba(0, 0, 0, 0.15);
        border: 1px solid @border-light;
        border-left: none;
      }

      .ant-drawer-body {
        padding: 0;
        height: 100%;
        border: 1px solid @border-light;
      }
    }
  }

  .logo-combine-drawer-expend {
    position: absolute;
    left: 100%;
    top: calc((100vh - @navigation-height - 126px) / 2);
    width: 32px;
    height: 130px;
    border-radius: 0 16px 16px 0;
    background: #E1E6FF;
    box-shadow: 4px 0 12px rgba(22, 100, 255, 0.15);
    border: 1px solid #B5C7FF;
    border-left: none;
    z-index: 99;
    cursor: pointer;
    transition: all 0.3s ease;
    
    // 呼吸灯效果
    &::before {
      content: '';
      position: absolute;
      top: -4px;
      left: 0px;
      right: -4px;
      bottom: -4px;
      border-radius: 0 20px 20px 0;
      background: linear-gradient(135deg, rgba(22, 100, 255, 0.25), rgba(148, 120, 234, 0.25));
      opacity: 0.7;
      z-index: -1;
      animation: breathe 2s infinite ease-in-out;
      box-shadow: 0 0 15px rgba(22, 100, 255, 0.25);
    }

    &:hover {
      background: #D4E3FF;
      box-shadow: 6px 0 16px rgba(22, 100, 255, 0.25);
      transform: translateX(2px);
      border-color: #618DFF;
      
      &::before {
        animation-duration: 1s;
        opacity: 1;
        background: linear-gradient(135deg, rgba(22, 100, 255, 0.4), rgba(148, 120, 234, 0.4));
        box-shadow: 0 0 25px rgba(22, 100, 255, 0.4);
      }
    }

    &:active {
      transform: translateX(1px) scale(0.98);
      transition: all 0.15s ease;
      background: #C8D9FF;
      box-shadow: 2px 0 8px rgba(22, 100, 255, 0.2);
    }

    &.logo-combine-drawer-contract {
      left: 0;
      right: auto;
      border-radius: 16px 0 0 16px;
      border: 1px solid #B5C7FF;
      border-right: none;
      
      &::before {
        border-radius: 20px 0 0 20px;
      }
      
      &:hover {
        transform: translateX(-2px);
      }
      
      &:active {
        transform: translateX(-1px) scale(0.98);
      }
    }
  }

  // 呼吸灯动画关键帧
  @keyframes breathe {
    0% {
      transform: scale(1);
      opacity: 0.7;
      box-shadow: 0 0 15px rgba(22, 100, 255, 0.25);
    }
    50% {
      transform: scale(1.05);
      opacity: 0.3;
      box-shadow: 0 0 28px rgba(22, 100, 255, 0.5);
    }
    100% {
      transform: scale(1);
      opacity: 0.7;
      box-shadow: 0 0 15px rgba(22, 100, 255, 0.25);
    }
  }

  .drawer-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .lora-grid {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 16px;
      justify-content: flex-start;
      align-items: flex-start;
      background-color: #F0F2F5;
      border: 2px solid #D1D5DB;
      padding: 12px;
      border-radius: 12px;
      z-index: 1000;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .lora-list-container {
        width: 100%;
        overflow: hidden;
        position: relative;
        
        .arrow-container {
          position: absolute;
          top: 0;
          height: 100%;
          display: flex;
          align-items: center;
          z-index: 15;
          pointer-events: none;
          
          &.left-arrow {
            left: 0;
          }
          
          &.right-arrow {
            right: 0;
          }
          
          .scroll-arrow {
            pointer-events: auto;
          }
        }
        
        .lora-list-wrapper {
          width: 100%;
          position: relative;
          z-index: 1;
        }

        .scroll-arrow {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 32px;
          height: 32px;
          background: rgba(0, 0, 0, 0.4);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          box-shadow: 0 2px 8px rgba(255, 255, 255, 0.5);
          z-index: 10;
          transition: all 0.3s;
          pointer-events: auto;

          &:hover {
            background: rgba(0, 0, 0, 0.6);
          }

          .anticon {
            color: #FFFFFF;
            font-size: 16px;
          }

          &.scroll-arrow-left {
            left: 4px;
          }

          &.scroll-arrow-right {
            right: 4px;
          }

          &.scroll-arrow-up {
            top: 4px;
          }

          &.scroll-arrow-down {
            bottom: 4px;
          }
        }

        .lora-list {
          width: 100%;
          overflow-x: auto;
          overflow-y: hidden;
          scrollbar-width: none;
          -ms-overflow-style: none;
          -webkit-overflow-scrolling: touch;
          scroll-behavior: auto; /* 确保滚动行为是即时的，不是平滑的 */

          &::-webkit-scrollbar {
            display: none;
          }
        }
        
        // 通用隐藏滚动条类
        .scrollbar-hidden {
          scrollbar-width: none;
          -ms-overflow-style: none;
          
          &::-webkit-scrollbar {
            display: none;
          }
        }
      }

      .lora-item {
        width: 108px;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        background: @bg-light;
        

        .item-name {
          font-size: 14px;
          color: @text-primary;
          margin-top: 4px;
          text-align: center;
          padding: 4px 0;
          width: 100%;
        }

        .item-image {
          object-fit: cover;
          border-bottom-left-radius: 0;
          border-bottom-right-radius: 0;
        }
      }
    }
  }

  .expand-icon {
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;

    &.active {
      color: #1664FF;
      transform: scale(1.05);
    }

    &.default {
      color: #1664FF;
      opacity: 0.8;
    }
  }

  .child-items-section {
    flex: 1;
    margin-top: 8px;
    background-color: #FAFAFA;
    border-radius: 12px;
    border: 2px solid #E1E3EB;
    box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.04);
    position: relative;
    display: flex;
    flex-direction: column;
    
    &::before {
      content: '';
      position: absolute;
      top: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 80%;
      height: 2px;
      background: linear-gradient(90deg, transparent, #D1D5DB, transparent);
    }
    
    .child-items-header {
      padding: 16px 20px 12px;
      text-align: center;
      
      .child-items-title {
        font-size: 16px;
        font-weight: 600;
        color: @text-primary;
        background: linear-gradient(90deg, #366EF4 0%, #9478EA 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 50%;
          transform: translateX(-50%);
          width: 40px;
          height: 2px;
          background: linear-gradient(90deg, #366EF4 0%, #9478EA 100%);
          border-radius: 1px;
        }
      }
    }
  }

  .child-items-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0 16px 16px;
    min-height: 100px;
  
    .child-items-flex {
      flex-wrap: wrap;
      justify-content: flex-start;
      width: 100%;
    }
  
    .child-item {
      margin: 8px 0 0 0;
      width: 118px;
      height: 160px;
      display: flex;
      border: 2px solid transparent;
      cursor: pointer;
      border-radius: 12px;
      box-sizing: border-box;
      position: relative;
      overflow: hidden;
      flex-basis: 118px;
      flex-grow: 0;
      flex-shrink: 0;
      opacity: 0;
      transition: all 0.3s ease;
      background: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        
        .hover-mask {
          opacity: 1;
        }
      }
  
      &.selected {
        border: 2px solid @primary-color;
        box-shadow: 0 4px 16px rgba(54, 110, 244, 0.25);
      }
  
      .image-container {
        width: 118px;
        height: 160px;
        overflow: hidden;
        position: relative;
        border-radius: 10px;
        cursor: pointer;
        
        img {
          width: 118px;
          height: 160px;
          object-fit: cover;
          border-radius: 10px;
          transition: transform 0.3s ease;
        }
        
        // 悬停遮罩
        .hover-mask {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            180deg, 
            transparent 0%, 
            rgba(0, 0, 0, 0.7) 100%
          );
          opacity: 0;
          transition: opacity 0.3s ease;
          display: flex;
          align-items: flex-end;
          justify-content: center;
          padding-bottom: 16px;
          z-index: 2;
          
          .preview-text {
            color: white;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 12px;
            backdrop-filter: blur(4px);
          }
        }
        
        // 圆形选择框
        .circular-checkbox {
          position: absolute;
          top: 8px;
          right: 8px;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.9);
          border: 2px solid #d9d9d9;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          z-index: 10;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          
          &:hover {
            border-color: @primary-color;
            background: rgba(255, 255, 255, 1);
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(54, 110, 244, 0.3);
          }
          
          &.checked {
            background: @primary-color;
            border-color: @primary-color;
            box-shadow: 0 2px 8px rgba(54, 110, 244, 0.4), 0 0 0 2px rgba(54, 110, 244, 0.2);
            
            &:hover {
              background: lighten(@primary-color, 10%);
              border-color: lighten(@primary-color, 10%);
              box-shadow: 0 4px 12px rgba(54, 110, 244, 0.5), 0 0 0 3px rgba(54, 110, 244, 0.3);
              transform: scale(1.05);
            }
            
            .checkbox-icon {
              color: white;
              font-size: 12px;
              font-weight: bold;
              line-height: 1;
            }
          }
        }

        // 选中状态指示器（已被圆形选择框替代，暂时注释）
        /* .selected-indicator {
          position: absolute;
          top: 8px;
          right: 8px;
          width: 20px;
          height: 20px;
          background: @primary-color;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 8px rgba(54, 110, 244, 0.4);
          z-index: 5;
          opacity: 0.3;
          
          .selected-icon {
            color: white;
            font-size: 12px;
            font-weight: bold;
          }
        } */
      }
    }
  
    .skeleton-item {
      margin: 8px 0 0 0;
      width: 118px;
      height: 160px;
      border-radius: 8px;
      flex-basis: 118px;
      flex-grow: 0;
      flex-shrink: 0;
      opacity: 0;
    }
  }
}

.lora-list-container {
  position: relative;
  overflow: hidden;

  .scroll-arrow-up,
  .scroll-arrow-down {
    position: absolute;
    width: 32px;
    height: 32px;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.5);
    z-index: 1;
    transition: all 0.3s;

    &:hover {
      background: rgba(0, 0, 0, 0.6);
    }

    .anticon {
      color: #FFFFFF;
      font-size: 16px;
    }
  }
}

.lora-item.selected {
  border: 2px solid @primary-color;
}

.round-checkbox {
  position: absolute;
  right: 8px;
  top: 8px;
  z-index: 10;
}

.round-checkbox .ant-checkbox-inner {
  border-radius: 50% !important;
  width: 18px !important;
  height: 18px !important;
  background-color: rgba(255, 255, 255, 0.8) !important;
  border: 1px solid #D9D9D9 !important;
}

.round-checkbox .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #366EF4 !important;
  border-color: #366EF4 !important;
}

.round-checkbox .ant-checkbox-inner::after {
  top: 45% !important;
}

.scene-type-buttons {
  display: flex;
  gap: 8px;

  .recommend-scene-button {
    width: 294px;
    border-radius: 32px;

    .recommend-scene-text {
      font-size: 14px;
      font-weight: 500;
      color: #1664FF;
    }

    .recommend-scene-desc {
      font-size: 12px;
      background: linear-gradient(90deg, #366EF4 0%, #9478EA 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-weight: normal;
    }
  }

  .all-scene-text {
    font-size: 14px;
  }
}

// 全局样式覆盖，确保圆形单选框
:global {
  .round-checkbox .ant-checkbox-inner {
    border-radius: 50% !important;
    width: 18px !important;
    height: 18px !important;
    background-color: rgba(255, 255, 255, 0.8) !important;
    border: 1px solid #D9D9D9 !important;
  }
  
  .round-checkbox .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #366EF4 !important;
    border-color: #366EF4 !important;
  }
  
  .round-checkbox .ant-checkbox-inner::after {
    top: 45% !important;
  }
}

// 预览弹窗样式 - 参考原版ImgPreview
.style-lora-preview-modal {
  position: relative;
  top: 16px;
  display: flex;
  justify-content: center;
  align-items: center;

  // 清除图片预览的padding
  .ant-modal-content {
    padding: 0 !important;
  }
  
  .img-detail-container {
    padding: 16px;
    background: #F5F6F9;
    border-radius: 16px;
    height: calc(100vh - 32px);
  }
  
  .preview-image-wrapper {
    height: calc(100vh - 32px - 32px - 44px - 16px);
  }
  
  .preview-main-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
  }
  
  .img-detail-repair-block {
    width: auto;
    height: 44px;
    border-radius: 8px;
    opacity: 1;

    /* 自动布局 */
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0 16px;
    gap: 4px;

    background: #FFFFFF;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 0;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
    }
    
    &.primary-action {
      background: @primary-color;
      
      .color-1a {
        color: white !important;
      }
      
      &:hover {
        background: lighten(@primary-color, 10%);
        box-shadow: 0 4px 12px rgba(54, 110, 244, 0.3);
      }
    }
  }
  
  .img-detail-btn {
    height: 44px !important;
  }
  
  // 文本样式类
  .text16 {
    font-size: 16px;
  }
  
  .font-pf {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }
  
  .color-1a {
    color: #1A1B1D;
  }
}