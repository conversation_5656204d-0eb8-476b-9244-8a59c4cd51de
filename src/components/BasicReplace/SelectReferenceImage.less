.select-reference-image {
  position: relative;
  overflow: visible;
  
  .reference-title {
    margin-bottom: 8px;
  }
  
  .reference-segmented {
    margin-bottom: 12px;
  }
  
  .reference-container {
    position: relative;
    width: 100%;
    min-height: 180px;
    
    .system-select-container {
      width: 100%;
      height: 100px;
      padding: 16px;
      border: 1px solid #E1E3EB;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        border-color: #366EF4;
        box-shadow: 0 0 0 2px rgba(54, 110, 244, 0.1);
      }
      
      .system-image {
        .reference-image {
          width: 64px;
          height: 64px;
          border-radius: 4px;
          margin-right: 16px;
          object-fit: cover;
        }
      }
      
      .system-arrow {
        color: #999;
      }
    }
    
    .empty-state {
      width: 100%;
      height: 120px;
      
      .empty-icon {
        font-size: 36px;
        color: #CCC;
        margin-bottom: 8px;
      }
      
      .empty-text {
        color: #999;
      }
    }
    
    .reference-result-box {
      width: 100%;
      min-height: 160px;
      border: 1px solid #E1E3EB;
      border-radius: 8px;
      padding: 16px;
      
      .thumbnail-container {
        position: relative;
        width: 100px;
        height: 100px;
        
        .thumbnail-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 4px;
        }
        
        .checkbox-style {
          position: absolute;
          top: 8px;
          right: 8px;
        }
      }
      
      .add-button {
        width: 100px;
        height: 100px;
        border: 1px dashed #CCCCCC;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        
        .add-icon {
          transform: rotate(90deg);
          font-size: 24px;
          color: #999;
          margin-bottom: 8px;
        }
        
        .add-text {
          color: #666;
        }
        
        &:hover {
          border-color: #366EF4;
          
          .add-icon, .add-text {
            color: #366EF4;
          }
        }
      }
    }
    
    .upload-button {
      height: 40px;
      padding: 0 16px;
      
      .upload-icon {
        margin-right: 8px;
      }
    }
    
    .upload-hint {
      margin-top: 12px;
      color: #999;
    }
    
    .bottom-bar {
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px solid #E1E3EB;
      
      .select-count {
        color: #666;
      }
      
      .delete-button {
        color: #F56C6C;
        cursor: pointer;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
} 