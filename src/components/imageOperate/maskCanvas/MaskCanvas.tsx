import { useRecolor } from '@/hooks/useRecolor';
import { fetchOssBlobUrl } from '@/utils/ossUtils';
import { Flex, Spin } from 'antd';
import type Konva from 'konva';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Image as KonvaImage, Layer, Rect, Stage } from 'react-konva';

// Cache for processed mask images, original images and recolored images
const processedMaskCache = new Map<string, HtmlImageElement>();
const originalImageCache = new Map<string, HtmlImageElement>();
const recoloredImageCache = new Map<string, HtmlImageElement>();

interface MaskCanvasProps {
  /** The source image element */
  imageElement: HtmlImageElement;
  /** The mask image element (should be a black and white image where white represents the selected area) */
  maskImageElement?: HtmlImageElement;
  /** Color to highlight the selected area (default: 'rgba(24, 144, 255, 0.5)') */
  highlightColor?: string;
  /** Target color for recoloring in HEX format (e.g. '#FF0000') */
  targetColor?: string;
  /** Width of the canvas (default: 600) */
  width?: number;
  /** Height of the canvas (default: 400) */
  height?: number;
  /** Callback when the canvas is clicked */
  onClick?: (e: any) => void;
  /** Display mode: 'highlight' shows transparent highlight, 'recolor' shows recolored image (default: 'highlight') */
  mode?: 'highlight' | 'recolor';
  /** Texture preservation level (0-1, default: 0.5) */
  texturePreserve?: number;
  /** Smart color adjustment (default: true) */
  smartColorAdjust?: boolean;
}

/**
 * A canvas component that displays an image with highlighted areas based on a mask.
 * The mask should be a black and white image where white represents the selected areas to highlight.
 */
const MaskCanvas: React.FC<MaskCanvasProps> = ({
  imageElement,
  maskImageElement,
  highlightColor = 'rgba(24, 144, 255, 0.5)',
  targetColor,
  width = 600,
  height = 400,
  onClick,
  mode = 'highlight',
  texturePreserve,
  smartColorAdjust = true,
}) => {
  // 使用 ref 存储不需要触发重新渲染的数据
  const imageRef = useRef<HtmlImageElement | null>(null);
  const maskImageRef = useRef<HtmlImageElement | null>(null);
  const recoloredCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const imageSizeRef = useRef({ width: 0, height: 0 });

  // 只有这些状态需要触发重新渲染
  const [image, setImage] = useState<HtmlImageElement | null>(null);
  const [maskImage, setMaskImage] = useState<HtmlImageElement | null>(null);
  const [recoloredCanvas, setRecoloredCanvas] = useState<HTMLCanvasElement | null>(
    null,
  );
  const [recoloredImage, setRecoloredImage] = useState<HTMLImageElement | null>(
    null,
  );
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
  const [isLoading, setIsLoading] = useState(true);

  const stageRef = useRef<Konva.Stage | null>(null);

  // 上一次处理的颜色和选项
  const lastTargetColorRef = useRef<string | undefined>(undefined);
  const lastSmartColorAdjustRef = useRef<boolean | undefined>(undefined);

  // 使用 useRecolor hook
  const { processImage, processedCanvas, isProcessing, error } = useRecolor();

  // 使用 useMemo 缓存计算结果
  const calculateImageSize = useMemo(() => {
    return (imgWidth: number, imgHeight: number) => {
      const imgRatio = imgWidth / imgHeight;
      const containerRatio = width / height;

      let newWidth, newHeight;

      if (imgRatio > containerRatio) {
        // Image is wider than container relative to height
        newWidth = width;
        newHeight = width / imgRatio;
      } else {
        // Image is taller than container relative to width
        newWidth = height * imgRatio;
        newHeight = height;
      }

      return { width: newWidth, height: newHeight };
    };
  }, [width, height]);

  // Set images directly from props
  useEffect(() => {
    setIsLoading(true);

    try {
      // Set original image
      if (imageElement) {
        // Store in cache using src as key
        if (imageElement.src) {
          originalImageCache.set(imageElement.src, imageElement);
        }
        
        // Update ref and state
        imageRef.current = imageElement;
        setImage(imageElement);
        
        // Calculate and set image size
        const newSize = calculateImageSize(imageElement.width, imageElement.height);
        imageSizeRef.current = newSize;
        setImageSize(newSize);
      }
      
      // Set mask image if provided
      if (maskImageElement) {
        // Process mask image to make black parts transparent
        const canvas = document.createElement('canvas');
        canvas.width = maskImageElement.width;
        canvas.height = maskImageElement.height;
        const ctx = canvas.getContext('2d');
        
        if (ctx) {
          // Draw the original mask
          ctx.drawImage(maskImageElement, 0, 0);
          
          // Get image data to process pixels
          const imageData = ctx.getImageData(
            0,
            0,
            canvas.width,
            canvas.height,
          );
          const data = imageData.data;
          
          // Convert black parts to transparent
          // In a black-and-white mask, R=G=B for each pixel
          for (let i = 0; i < data.length; i += 4) {
            // If pixel is black or near-black (allowing some tolerance)
            if (data[i] < 30) {
              // R value close to 0
              // Make it transparent
              data[i + 3] = 0; // Set alpha to 0
            }
          }
          
          // Put the processed data back to canvas
          ctx.putImageData(imageData, 0, 0);
          
          // Create a new image from the processed canvas
          const processedMaskImg = new Image();
          processedMaskImg.onload = () => {
            // Store in cache if src exists
            if (maskImageElement.src) {
              processedMaskCache.set(maskImageElement.src, processedMaskImg);
            }
            maskImageRef.current = processedMaskImg;
            setMaskImage(processedMaskImg);
          };
          processedMaskImg.src = canvas.toDataURL();
        } else {
          // Fallback if canvas context not available
          maskImageRef.current = maskImageElement;
          setMaskImage(maskImageElement);
        }
      } else {
        // No mask image needed
        setMaskImage(null);
      }
    } catch (error) {
      console.error('Error processing images:', error);
    }
  }, [imageElement, maskImageElement, calculateImageSize]);

  // Handle loading state
  useEffect(() => {
    const hasRequiredImages =
      image &&
      (maskImage || !maskImageElement) &&
      (mode !== 'recolor' || recoloredCanvas || !targetColor);

    setIsLoading(!hasRequiredImages);
  }, [image, maskImage, maskImageElement, recoloredCanvas, targetColor, mode]);

  // Calculate position to center the image
  const position = useMemo(() => {
    if (!imageSize.width || !imageSize.height) return { x: 0, y: 0 };

    return {
      x: (width - imageSize.width) / 2,
      y: (height - imageSize.height) / 2,
    };
  }, [imageSize.width, imageSize.height, width, height]);

  // 使用 useEffect 和 useRef 避免不必要的重新处理
  useEffect(() => {
    // 只在必要时处理图像
    if (mode === 'recolor' && image && maskImage && targetColor) {
      // 检查颜色是否变化或者 smartColorAdjust 选项变化，避免不必要的重新处理
      const shouldReprocess =
        targetColor !== lastTargetColorRef.current ||
        smartColorAdjust !== lastSmartColorAdjustRef.current ||
        !recoloredCanvas;

      if (shouldReprocess) {
        // 更新最后处理的颜色和选项
        lastTargetColorRef.current = targetColor;
        lastSmartColorAdjustRef.current = smartColorAdjust;

        console.log(
          'Processing image with smartColorAdjust:',
          smartColorAdjust,
        );
        // 处理图像，确保正确传递 smartColorAdjust 选项
        processImage(image, maskImage, targetColor, smartColorAdjust);
      }
    }
  }, [
    image,
    maskImage,
    targetColor,
    mode,
    processImage,
    smartColorAdjust,
    recoloredCanvas,
  ]);

  // 处理处理后的图像结果
  useEffect(() => {
    if (processedCanvas) {
      // 同时更新 ref 和 state
      recoloredCanvasRef.current = processedCanvas;
      setRecoloredCanvas(processedCanvas);
      const image = new Image();
      image.src = processedCanvas.toDataURL("image/png");
      image.onload = () => {
        setRecoloredImage(image);
      };
    }
  }, [processedCanvas]);

  if (
    isLoading ||
    !image ||
    (mode === 'recolor' && !recoloredCanvas && isProcessing)
  ) {
    return (
      <Flex style={{ width, height }} justify="center" align="center">
        <Spin size="large" />
      </Flex>
    );
  }

  return (
    <div style={{ position: 'relative', width, height }}>
      {/* 处理中状态显示加载指示器 */}
      {isProcessing && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.2)',
            zIndex: 10,
            pointerEvents: 'none', // 允许点击穿透
          }}
        >
          <Spin size="large" />
        </div>
      )}

      <Stage
        ref={stageRef}
        width={width}
        height={height}
        onClick={onClick}
        style={{
          overflow: 'hidden',
        }}
      >
        <Layer>
          {/* Original image as base layer */}
          {image && !recoloredCanvas && (
            <KonvaImage
              image={image}
              x={position.x}
              y={position.y}
              width={imageSize.width}
              height={imageSize.height}
            />
          )}
        </Layer>
        <Layer>
          {/* Conditional rendering based on mode */}
          {image && maskImage && mode === 'highlight' && (
            <>
              {/* Create a colored rectangle for highlight */}
              <Rect
                x={position.x}
                y={position.y}
                width={imageSize.width}
                height={imageSize.height}
                fill={highlightColor}
              />

              {/* Apply mask as clipping - white areas show the highlight */}
              <KonvaImage
                image={maskImage}
                x={position.x}
                y={position.y}
                width={imageSize.width}
                height={imageSize.height}
                globalCompositeOperation="destination-in"
              />
            </>
          )}

          {/* Recolored image overlay using the mask */}
          {image && maskImage && recoloredImage && mode === 'recolor' && (
            <>
              {/* Show the recolored image */}
              <KonvaImage
                image={recoloredImage}
                x={position.x}
                y={position.y}
                width={imageSize.width}
                height={imageSize.height}
              />
            </>
          )}
        </Layer>
      </Stage>
    </div>
  );
};

export default MaskCanvas;
