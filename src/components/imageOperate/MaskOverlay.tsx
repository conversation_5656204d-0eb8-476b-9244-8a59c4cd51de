import React, { useEffect, useRef, useState } from 'react';
import { downloadOss } from '@/services/SystemController';
import IconFont from '@/components/IconFont';

interface MaskOverlayProps {
  maskUrl: string;
  className?: string;
  style?: React.CSSProperties;
  maskColor?: string;
  invertMask?: boolean; // true: 黑底白文, false: 白底黑文
  opacity?: number;
  showIcon?: boolean;
}

const MaskOverlay: React.FC<MaskOverlayProps> = ({ 
  maskUrl, 
  className = '', 
  style = {}, 
  maskColor = '#61B3FF',
  invertMask = false, // 默认为白底黑文模式
  opacity = 0.5,
  showIcon = false,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [showMask, setShowMask] = useState<boolean>(true);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const loadMaskImage = async () => {
      if (!maskUrl) {
        return;
      }
      try {
        // 使用 downloadOss 接口获取图片数据
        const response = await downloadOss(maskUrl);
        const blob = await response.blob();
        const img = new Image();
        
        img.onload = () => {
          // 设置canvas尺寸与图片一致
          canvas.width = img.width;
          canvas.height = img.height;
          
          // 绘制原始蒙版
          ctx.drawImage(img, 0, 0);
          
          // 获取图片数据
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          const data = imageData.data;
          
          // 解析目标颜色
          const targetColor = {
            r: parseInt(maskColor.slice(1, 3), 16),
            g: parseInt(maskColor.slice(3, 5), 16),
            b: parseInt(maskColor.slice(5, 7), 16)
          };

          // 处理每个像素
          for (let i = 0; i < data.length; i += 4) {
            // 检查是否是黑色像素 (RGB值都很低)
            const isBlack = data[i] < 50 && data[i + 1] < 50 && data[i + 2] < 50;
            
            // 根据invertMask参数决定处理逻辑
            if (invertMask ? !isBlack : isBlack) {
              // 将目标像素转换为指定颜色
              data[i] = targetColor.r;     // R
              data[i + 1] = targetColor.g; // G
              data[i + 2] = targetColor.b; // B
              data[i + 3] = 200;           // Alpha (透明度)
            } else {
              // 将其他像素设为完全透明
              data[i + 3] = 0;
            }
          }
          
          // 将处理后的数据放回canvas
          ctx.putImageData(imageData, 0, 0);
        };

        img.src = URL.createObjectURL(blob);
      } catch (error) {
        console.error('Failed to load mask image:', error);
      }
    };

    loadMaskImage();
  }, [maskUrl, maskColor, invertMask]);

  return (
    <>
      <canvas
        ref={canvasRef}
        className={className}
        style={{
          position: 'absolute',
          width: 'auto',
          height: '100%',
          pointerEvents: 'none',
          ...style,
          opacity: showMask ? opacity : 0,
        }}
      />
      {showIcon && (
        <IconFont
          type="icon-show-mask"
          style={{
            fontSize: '24px',
            position: 'absolute',
            bottom: 8,
            right: 8,
            zIndex: 999,
            color: showMask ? '#fff' : '#ffffff7f',
            cursor: 'pointer',
            transition: 'color 0.3s ease',
          }}
          onMouseDown={() => setShowMask(false)}
          onMouseUp={() => setShowMask(true)}
          onMouseLeave={() => setShowMask(true)}
        />
      )}
    </>
  );
};

export default MaskOverlay; 