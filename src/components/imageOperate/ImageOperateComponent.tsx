import React, { useEffect, useState } from 'react';
import { Stage, Layer, Line, Image as KonvaImage } from 'react-konva';
import { Flex } from 'antd';
import type { BrushComponentProps } from './types';
import { OperateType } from './types';

// 图片操作组件属性
export interface ImageOperateComponentProps {
  //===================== 基础属性 ========================
  // 操作类型
  operateType: OperateType;
  // 最终处理Ref
  resultStageRef: React.RefObject<any>;
  // 宽度
  width: number;
  // 高度
  height: number;
  //===================== 组件属性 =========================
  // 笔刷组件属性
  brushComponent?: BrushComponentProps;
  //====================== 事件属性 ========================
  // 处理鼠标按下
  handleMouseDown?: (e: any) => void;
  // 处理鼠标移动
  handleMouseMove?: (e: any) => void;
  // 处理鼠标抬起
  handleMouseUp?: () => void;
  // 处理鼠标离开
  handleMouseLeave?: () => void;
  // 处理鼠标进入
  handleMouseEnter?: (e: any) => void;
}


const ImageOperateComponent: React.FC<ImageOperateComponentProps> = ({
  //============= 基础属性 ================
  operateType,
  resultStageRef,
  width,
  height,
  //============= 组件属性 ================
  brushComponent,
  //============= 事件属性 ================
  handleMouseDown,
  handleMouseMove,
  handleMouseUp,
  handleMouseLeave,
  handleMouseEnter,
}) => {

  // 当前操作组件
  const [currentOperateComponent, setCurrentOperateComponent] = useState<React.ReactNode>(null);


  // 根据操作类型渲染不同的组件 
  useEffect(() => {
    if (operateType === OperateType.ERASE) {
      setCurrentOperateComponent(renderEraseComponent());
    } else if (operateType === OperateType.FILL) {
      setCurrentOperateComponent(renderFillComponent());
    }
  }, [operateType, brushComponent]);


  // 渲染擦除组件
  const renderEraseComponent = () => {
    // 判断组件属性是否存在 
    if (!brushComponent) return null;

    // 获取组件属性
    const {
      backgroundStageRef, backgroundImageUrl, blankImage,
      isErasing, strokeWidth, lines, currentLine, pointerPos,
      setLines, selected, isUseWhiteBackgroundForMark
    } = brushComponent;


    return (
      <div className="operate-stage">
        <Flex style={{
          backgroundImage: `url(${backgroundImageUrl})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}>
          <div style={{ opacity: 0.5 }}>
            <Stage
              ref={backgroundStageRef}
              width={width}
              height={height}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseLeave}
              onMouseEnter={handleMouseEnter}
              style={{ cursor: 'none' }}
            >
              <Layer>
                <KonvaImage image={blankImage} width={width} height={height} opacity={0} />
              </Layer>

              <Layer>
                {/* 绘制所有已完成的线条 */}
                {lines.map((line, i) => (
                  <Line
                    key={i}
                    points={line.points.flatMap(p => [p.x, p.y])}
                    stroke={'rgba(97, 179, 255, 1)'}
                    strokeWidth={line.strokeWidth}
                    tension={0.5}
                    lineCap="round"
                    lineJoin="round"
                    globalCompositeOperation={line.tool === 'erase' ? 'destination-out' : 'source-over'}
                  />
                ))}

                {/* 绘制当前正在画的线条 */}
                {currentLine.length > 0 && (
                  <Line
                    points={currentLine.flatMap(p => [p.x, p.y])}
                    stroke={isErasing ? 'rgba(255, 255, 255, 0)' : 'rgba(97, 179, 255, 1)'}
                    strokeWidth={strokeWidth}
                    tension={0.5}
                    lineCap="round"
                    lineJoin="round"
                    globalCompositeOperation={isErasing ? 'destination-out' : 'source-over'}
                  />
                )}
              </Layer>
            </Stage>
            {/*鼠标图标*/}
            <div
              style={{
                display: selected ? 'block' : 'none',
                position: 'fixed',
                top: pointerPos.y - strokeWidth / 2,
                left: pointerPos.x - strokeWidth / 2,
                width: strokeWidth,
                height: strokeWidth,
                borderRadius: '50%',
                backgroundColor: isErasing ? 'rgba(255, 255, 255, 1)' : 'rgba(97, 179, 255, 0.5)',
                border: '1px solid rgba(97, 179, 255, 0.8)',
                pointerEvents: 'none',
                zIndex: 100,
                transform: 'translate(0, 0)',
              }}
            />
          </div>
        </Flex>
        <Stage width={width} height={height} ref={resultStageRef} style={{ display: 'none' }}>
          <Layer>
            <KonvaImage image={blankImage} width={width} height={height} />
            {lines.map((line, i) => (
              <Line
                key={i}
                points={line.points.flatMap(p => [p.x, p.y])}
                stroke={isUseWhiteBackgroundForMark ? 'rgba(0,0,0,1)' : 'rgba(255,255,255,1)'}
                strokeWidth={line.strokeWidth}
                tension={0.5}
                lineCap="round"
                lineJoin="round"
                globalCompositeOperation={line.tool === 'erase' ? 'destination-out' : 'source-over'}
              />
            ))}
          </Layer>
        </Stage>
      </div>
    );
  };

  // 渲染填充组件
  const renderFillComponent = () => {
    return <div>拼装组件</div>;
  };


  return (
    <>
      {currentOperateComponent}
    </>
  );
};

export default ImageOperateComponent;