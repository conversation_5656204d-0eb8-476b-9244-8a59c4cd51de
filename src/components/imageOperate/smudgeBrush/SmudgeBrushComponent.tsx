import React, { useState, useRef, useEffect } from 'react';
import { Button, Flex, Slider, Popconfirm, message } from 'antd';
import { MINI_LOADING_ICON } from '@/constants';
import ImageOperateComponent from '../ImageOperateComponent';
import './SmudgeBrushComponent.less';
import Konva from 'konva';
import { createBlankCanvas } from '@/utils/canvasUtils';
import useImage from 'use-image';
import type { ImagePoint, CustomLine } from '../types';
import { OperateType } from '../types';

// 组件属性
interface SmudgeBrushComponentProps {
  // 涂抹类型
  BrushType: string;
  // 背景图片
  bgImage?: any;
  // 是否使用白色背景
  isUseWhiteBackgroundForMark?: boolean;
  // 保存回调
  onSave: (base64: string) => void;
  // 取消回调
  onCancel?: () => void;
  // 开始修复时的回调
  onStartRepair?: () => void;
}

// 分段选择器配置
const SEGMENTED_OPTIONS = [
  { iconUrl: 'icon-tumo', text: '涂抹', value: 'smudge' },
  { iconUrl: 'icon-cachu', text: '清空', value: 'clear' },
];

const SmudgeBrushComponent: React.FC<SmudgeBrushComponentProps> = ({
  // 涂抹类型
  BrushType,
  // 是否使用白色背景(默认使用白色背景)
  isUseWhiteBackgroundForMark = true,
  // 背景图片
  bgImage,
  // 保存回调
  onSave,
  // 取消回调
  onCancel,
  // 开始修复时的回调
  onStartRepair,
}) => {
  // 涂抹相关状态
  const [isErasing, setIsErasing] = useState<boolean>(false);
  // 笔刷大小
  const [strokeWidth, setStrokeWidth] = useState<number>(30);
  // 线条
  const [lines, setLines] = useState<CustomLine[]>([]);
  // 是否选中
  const [selected, setSelected] = useState<boolean>(false);
  // 提交状态
  const [commiting, setCommiting] = useState<boolean>(false);
  // 鼠标是否在绘制中
  const isDrawing = useRef(false);
  // 当前线条
  const [currentLine, setCurrentLine] = useState<ImagePoint[]>([]);


  // 工具栏位置状态
  const [isDragging, setIsDragging] = useState(false);
  // 工具栏位置
  const [position, setPosition] = useState({ x: 24, y: 24 });
  // 是否可见
  const [isVisible, setIsVisible] = useState(false);
  // 拖动参考
  const dragRef = useRef<HTMLDivElement>(null);
  // 拖动开始位置
  const dragStartPositionRef = useRef({ x: 0, y: 0 });
  // 拖动初始位置
  const initialPositionRef = useRef({ x: 0, y: 0 });


  // 原始图片高度
  const [originalImageHeight, setOriginalImageHeight] = useState(0);
  // 原始图片宽度
  const [originalImageWidth, setOriginalImageWidth] = useState(0);
  // 图片宽度
  const [imageWidth, setImageWidth] = useState(570);
  // 图片高度
  const [imageHeight, setImageHeight] = useState(760);

  // 空白图片url(黑色)
  const [blankImageUrl, setBlankImageUrl] = useState<string>('');
  // 空白图片
  const [blankImage] = useImage(blankImageUrl, 'anonymous');

  // 背景图片url 模式，设置为背景
  const [backgroundImageUrl, setBackgroundImageUrl] = useState<string>('');
  // 图片比例
  const [proportion, setProportion] = useState<string>('');

  // 指针位置
  const [pointerPos, setPointerPos] = useState({ x: 0, y: 0 });

  // 背景图片Ref
  const backgroundStageRef = useRef<Konva.Stage>(null);
  // 最终处理Ref
  const resultStageRef = useRef<Konva.Stage>(null);


  // 添加入场动画
  useEffect(() => {
    // 添加入场动画
    requestAnimationFrame(() => setIsVisible(true));
  }, []);


  // 监听拖动
  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleDrag);
      window.addEventListener('mouseup', () => setIsDragging(false));
    }
    return () => {
      window.removeEventListener('mousemove', handleDrag);
      window.removeEventListener('mouseup', () => setIsDragging(false));
    };
  }, [isDragging]);

  // 监听图片大小变化
  useEffect(() => {
    resetPos();
  }, [imageWidth, imageHeight]);

  // 监听背景图片变化
  useEffect(() => {
    if (!bgImage) return;

    const loadImage = () => {
      let imageBlob;
      if (typeof bgImage === 'string' && bgImage.startsWith('data:')) {
        imageBlob = base64ToBlob(bgImage);
      } else if (bgImage instanceof Blob) {
        imageBlob = bgImage;
      }

      // 创建图片url
      const imageUrl = URL.createObjectURL(imageBlob);

      // 设置背景图片url（设置为背景，防止涂抹颜色重叠）
      setBackgroundImageUrl(imageUrl);

      const imageElement = new Image();
      imageElement.src = imageUrl;

      // 处理图片加载成功
      imageElement.onload = () => {
        // 设置原始图片尺寸
        setOriginalImageWidth(imageElement.width);
        setOriginalImageHeight(imageElement.height);

        const imageProportion = imageElement.width !== imageElement.height ? 'THREE_FOUR' : 'ONE_ONE';
        // 设置比例
        setProportion(imageProportion);
        // 根据比例计算图片宽高
        resizeWithProportion(imageProportion);
      };
    };

    loadImage();

    // 监听窗口大小变化
    window.addEventListener('resize', resize);
    return () => {
      window.removeEventListener('resize', resize);
    };
  }, [bgImage]);

  // 监听窗口大小变化
  const resize = () => {
    resizeWithProportion(null);
  };

  // 将base64转换为Blob
  const base64ToBlob = (base64: string) => {
    const parts = base64.split(';base64,');
    const contentType = parts[0].split(':')[1];
    const raw = window.atob(parts[1]);
    const rawLength = raw.length;
    const uInt8Array = new Uint8Array(rawLength);

    // 将base64转换为Uint8Array
    for (let i = 0; i < rawLength; ++i) {
      uInt8Array[i] = raw.charCodeAt(i);
    }

    // 返回Blob
    return new Blob([uInt8Array], { type: contentType });
  };

  // 重新构建
  const resetPos = () => {
    if (backgroundStageRef.current) {
      //生成一张空白的图片
      const tmpCanvas = createBlankCanvas(imageWidth, imageHeight, isUseWhiteBackgroundForMark ? '#FFFFFF' : '#000000');
      setBlankImageUrl(tmpCanvas.toDataURL('image/png'));

      const stage = backgroundStageRef.current;
      const container = stage.container();
      const rect = container.getBoundingClientRect();
      //解决加载页面时，直接展示画笔的问题
      if (window.scrollX == 0 && window.scrollY === 0) {
        setPointerPos({ x: 0, y: 0 });
        return;
      }
      setPointerPos({ x: rect.left + window.scrollX, y: rect.top + window.scrollY });
    }
  };

  // 根据比例计算图片宽高
  const resizeWithProportion = (predProportion: string | null = null) => {
    const height = window.innerHeight - 140;
    const width = (predProportion ? predProportion : proportion) !== 'THREE_FOUR' ? height : height / 4 * 3;
    setImageHeight(height);
    setImageWidth(width);
  };

  // 处理鼠标按下
  const handleMouseDown = (e: any) => {
    isDrawing.current = true;
    const stage = e.target.getStage();
    const point = stage.getPointerPosition();
    setCurrentLine([{ x: point.x, y: point.y }]);
  };

  // 处理鼠标移动
  const handleMouseMove = (e: any) => {
    const stage = e.target.getStage();
    const point = stage.getPointerPosition();
    const rect = stage.container().getBoundingClientRect();

    setPointerPos({
      x: point.x + rect.left + window.scrollX,
      y: point.y + rect.top + window.scrollY,
    });

    if (handlePointerPosChange) {
      handlePointerPosChange({
        x: point.x + rect.left + window.scrollX,
        y: point.y + rect.top + window.scrollY,
      });
    }

    if (!isDrawing.current) return;
    setCurrentLine((prevLine) => [...prevLine, { x: point.x, y: point.y }]);
  };

  // 处理鼠标抬起 
  const handleMouseUp = () => {
    isDrawing.current = false;
    if (currentLine.length > 0) {
      const newLine: CustomLine = {
        points: currentLine,
        tool: isErasing ? 'erase' : 'draw',
        strokeWidth,
        color: 'rgba(135, 206, 235, 0.6)',
      };
      setLines([...lines, newLine]);
      setCurrentLine([]);
    }
  };

  // 处理鼠标进入
  const handleMouseEnter = (e: any) => {
    const stage = e.target.getStage();
    const rect = stage.container().getBoundingClientRect();
    const point = stage.getPointerPosition();
    setPointerPos({
      x: point.x + rect.left + window.scrollX,
      y: point.y + rect.top + window.scrollY,
    });
    if (setSelected && !selected) {
      setSelected(true);
    }
  };

  // 处理鼠标离开
  const handleMouseLeave = () => {
    setPointerPos({ x: 0, y: 0 });
    if (handlePointerPosChange) {
      handlePointerPosChange({ x: 0, y: 0 });
    }
    if (setSelected) {
      setSelected(false);
    }
    isDrawing.current = false;
  };


  // 处理拖动开始
  const handleDragStart = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    dragStartPositionRef.current = { x: e.clientX, y: e.clientY };
    initialPositionRef.current = position;
  };

  // 处理拖动
  const handleDrag = (e: MouseEvent) => {
    if (!isDragging) return;

    // 计算拖动距离
    const deltaX = dragStartPositionRef.current.x - e.clientX;
    const deltaY = dragStartPositionRef.current.y - e.clientY;

    // 设置工具栏位置
    setPosition({
      x: initialPositionRef.current.x + deltaX,
      y: initialPositionRef.current.y + deltaY,
    });
  };

  // 处理工具栏点击
  const handleItemClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // 获取点击的值
    const value = e.currentTarget.getAttribute('data-value');
    // 清空线条
    if (value === 'clear') {
      setLines([]);
    } else {
      // 切换涂抹模式
      setIsErasing(!isErasing);
    }
  };

  // 处理指针位置变化
  const handlePointerPosChange = (pos: { x: number; y: number }) => {
    // 设置指针位置
    setPointerPos(pos);
  };

  // 渲染加载图标
  const renderLoadingIcon = () => (
    <img
      src={MINI_LOADING_ICON}
      width={16}
      height={16}
      alt="loading"
      className="loading-img"
    />
  );


  // 处理提交的图片信息
  const beforeSave = async () => {
    // 判断是否涂抹了区域
    if (lines.length <= 0) {
      message.warning('请长按鼠标涂抹需要修复的手部区域');
      return;
    }

    // 判断是否正在提交中
    if (commiting) {
      message.warning('正在提交中，请稍后再试');
      return;
    }

    // 设置提交状态
    setCommiting(true);
    
    // 通知父组件开始修复
    onStartRepair?.();

    // 获取图片数据
    const stage = resultStageRef.current;
    if (!stage) return;

    // 计算缩放比例
    const scaleX = originalImageWidth / imageWidth;
    const scaleY = originalImageHeight / imageHeight;

    // 创建一个临时的canvas来进行缩放
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = originalImageWidth;
    tempCanvas.height = originalImageHeight;
    const tempCtx = tempCanvas.getContext('2d');

    if (!tempCtx) return;

    // 获取当前stage的图像数据
    const stageCanvas = stage.toCanvas();

    // 使用临时canvas进行缩放
    tempCtx.scale(scaleX, scaleY);
    tempCtx.drawImage(stageCanvas, 0, 0);

    // 获取缩放后的图像数据
    const dataURL = tempCanvas.toDataURL('image/png', 1.0);

    // 执行提交处理
    await onSave(dataURL);

    // 设置提交状态
    setCommiting(false);
  };

  // 处理取消按钮点击
  const handleCancel = () => {
    setIsVisible(false);
    setTimeout(() => onCancel?.(), 300);
  };

  // 渲染顶部控制区域
  const renderTopControls = () => (
    <Flex vertical className="repair-body-left-top" gap={12} align="flex-start">
      <div className="text14 font-pf weight color-1a">请选择需要修复的区域</div>

      <div className="brush-segmented">
        {SEGMENTED_OPTIONS.map(({ iconUrl, text, value }) => (
          <div
            key={value}
            className={`brush-segmented-item ${value === 'clear' ? 'clear-option' : 'smudge-option'} ${value === 'smudge' && !isErasing ? 'selected' : ''}`}
            data-value={value}
            onClick={handleItemClick}
          >
            <div className="segmented-label">
              <i className={`iconfont ${iconUrl}`} style={{ fontSize: 16 }} />
              <span>{text}</span>
            </div>
          </div>
        ))}
      </div>

      <div className="text14 font-pf color-1a">笔刷大小</div>

      <Slider
        value={strokeWidth}
        min={8}
        max={100}
        tooltip={{ open: false }}
        onChange={setStrokeWidth}
        style={{ width: 250 }}
      />
    </Flex>
  );

  // 渲染底部按钮区域
  const renderBottomControls = () => (
    <Flex vertical style={{ padding: '18px' }} gap={12}>
      <Button
        className="smudge-brush-create-btn"
        disabled={lines.length <= 0}
        icon={commiting ? renderLoadingIcon() : undefined}
        onClick={beforeSave}
      >
        {commiting ? ' ' : '开始修复'}
      </Button>

      <Popconfirm
        title="你做的修改尚未保存，确认退出吗？"
        description="返回后相关编辑内容将无法恢复"
        onConfirm={handleCancel}
        okText="确定"
        cancelText="取消"
      >
        <Button className="cancel-btn" disabled={commiting}>
          取消修复
        </Button>
      </Popconfirm>
    </Flex>
  );

  return (
    <div className="smudge-brush-container">
      <div className="mask-overlay" />
      <div className="operate-content">
        <ImageOperateComponent
          //============= 基础属性 ================
          operateType={OperateType.ERASE}
          resultStageRef={resultStageRef}
          width={imageWidth}
          height={imageHeight}
          //============= 组件属性 ================
          brushComponent={{
            backgroundStageRef,
            backgroundImageUrl,
            blankImage,
            isErasing,
            strokeWidth,
            lines,
            currentLine,
            pointerPos,
            setLines,
            selected,
            isUseWhiteBackgroundForMark
          }}
          //============= 事件属性 ================
          handleMouseDown={handleMouseDown}
          handleMouseMove={handleMouseMove}
          handleMouseUp={handleMouseUp}
          handleMouseLeave={handleMouseLeave}
          handleMouseEnter={handleMouseEnter}
        />
      </div>
      <Flex
        ref={dragRef}
        className={`smudge-body-left ${isVisible ? 'visible' : 'hidden'} ${isDragging ? 'dragging' : ''}`}
        vertical
        justify="space-between"
        style={{ right: `${position.x}px`, bottom: `${position.y}px` }}
      >
        <div className="drag-handle" onMouseDown={handleDragStart}>
          <i className="iconfont icon-drag" />
        </div>
        {renderTopControls()}
        {renderBottomControls()}
      </Flex>
    </div>
  );
};

export default SmudgeBrushComponent;
