.smudge-body-left {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  user-select: none;
  padding: 0;
  width: 280px !important;
  height: 350px !important;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(0, 0, 0, 0.06);
  position: fixed;
  opacity: 0;
  transform: translate3d(0, 20px, 0);
  transition: all 0.3s ease;
  will-change: transform, opacity, right, bottom;
  z-index: 9999;

  &.visible {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }

  &.hidden {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
    pointer-events: none;
  }

  &.dragging {
    transition: none;
    cursor: grabbing;

    .drag-handle {
      cursor: grabbing;
      background: rgba(245, 245, 245, 0.9);

      .iconfont {
        color: #333;
        opacity: 1;
      }
    }
  }

  .drag-handle {
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: grab;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    background: rgba(250, 250, 250, 0.8);
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    transition: all 0.2s ease;

    &:active {
      cursor: grabbing;
      background: rgba(245, 245, 245, 0.9);
    }

    .iconfont {
      font-size: 16px;
      color: #666;
      opacity: 0.8;
    }

    &:hover {
      background: rgba(245, 245, 245, 0.9);

      .iconfont {
        color: #333;
        opacity: 1;
      }
    }
  }

  &-top {
    width: 100%;
    padding: 16px;

    .text14 {
      font-size: 14px;
      margin-bottom: 4px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 500;
    }

    .brush-segmented {
      margin: 12px 0;
    }

    .ant-slider {
      margin: 8px 0 0;
    }
  }

  &-bottom {
    width: 100%;
    position: relative;
    padding: 16px;
    background: rgba(250, 250, 250, 0.5);
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    border-top: 1px solid rgba(0, 0, 0, 0.04);
  }
}

// 自定义分段选择器样式
.brush-segmented {
  display: flex;
  width: 100%;
  background-color: rgba(225, 227, 235, 0.6);
  border-radius: 8px;
  padding: 3px;
  gap: 3px;

  &-item {
    flex: 1;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    user-select: none;
    font-size: 14px;

    &:hover {
      color: #366EF4;
    }

    // 涂抹选项样式
    &.smudge-option {
      &.selected {
        background: #fff;
        color: #366EF4;
        font-weight: 500;
        box-shadow: 0 2px 6px rgba(54, 110, 244, 0.15);
      }
    }

    // 清空选项样式
    &.clear-option {
      &:hover {
        background: rgba(255, 255, 255, 0.8);
      }
    }

    .iconfont {
      font-size: 16px;
      margin-right: 6px;
    }
  }
}

.smudge-brush-create-btn {
  width: 100%;
  height: 40px;
  background: linear-gradient(45deg, #366EF4, #4B7EF5);
  border-radius: 8px;
  color: #FFFFFF;
  font-size: 15px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(54, 110, 244, 0.2);

  &:hover {
    background: linear-gradient(45deg, #4B7EF5, #5C8DF6);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(54, 110, 244, 0.25);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px rgba(54, 110, 244, 0.2);
  }

  &:disabled {
    background: #E1E3EB;
    cursor: not-allowed;
    color: #A3A7B7;
    box-shadow: none;
    transform: none;
  }
}

.cancel-btn {
  width: 100%;
  height: 40px;
  background: #fff;
  border-radius: 8px;
  color: #666;
  font-size: 15px;
  font-weight: 500;
  border: 1px solid #E1E3EB;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;

  &:hover {
    color: #333;
    border-color: #ccc;
    background: #f9f9f9;
  }

  &:active {
    background: #f5f5f5;
  }
}

.loading-img {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.mask-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  pointer-events: auto;
}

// 涂抹区域容器
.smudge-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: transparent;

  .background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    pointer-events: none;
  }
}

// 笔刷指示器
.brush-indicator {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  z-index: 1000;
  transition: all 0.1s ease;
  will-change: transform;

  &.erasing {
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  &.drawing {
    background-color: rgba(97, 179, 255, 0.3);
    border: 1px solid rgba(97, 179, 255, 0.8);
  }
}

.smudge-brush-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;

  .mask-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1001;
  }

  .operate-content {
    position: relative;
    z-index: 1002;
  }

  .smudge-body-left {
    width: 244px;
    height: 100%;
    background: #FFFFFF;
  }
}