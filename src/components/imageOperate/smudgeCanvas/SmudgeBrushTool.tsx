import { Button, Flex, InputN<PERSON>ber, Segmented, <PERSON>lider, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import IconFont from '@/components/IconFont';
import '@/components/imageOperate/smudgeCanvas/SmudgeCanvas.less'

/**
 * 笔刷工具栏
 * @param className 拼接 className 便于修改样式
 * @param text
 * @param isErasing
 * @param switchSeg 清除按钮的回调
 * @param strokeWidth 笔刷直径
 * @constructor
 */
export const BrushSegment = ({className = "", text = '', isErasing = false, switchSeg}) => {
  const SegLabel = ({ iconUrl, text }) => {
    return (
      <Flex className='brush-segment-container' style={{ width: 62, height: 28, gap: 6 }} align={'center'} justify={'center'} >
        <IconFont className='smudge-toolkit-icon' type={iconUrl} />
        <div className={'text14 font-pf color-1a'}>{text}</div>
      </Flex>
    );
  };
  return (
    <Flex vertical className={'brush-tool-content'}>
      {text && <div className={'text14 font-pf color-1a brush-tool-text'}>{text}</div>}
      <Segmented
        value={isErasing}
        style={{ backgroundColor: '#E1E3EB'}}
        onChange={(value) => switchSeg(value as boolean)}
        options={[{
          label: <SegLabel iconUrl={'icon-tumo'} text={'涂抹'} />,
          value: false,
        }, {
          label: <SegLabel iconUrl={'icon-cachu'} text={'擦除'} />,
          value: true,
        }]}
      />
    </Flex>
  )
}



/**
 * 笔刷粗细调节
 * @param text
 * @param width
 * @param setWidth
 * @constructor
 */
export const StrokeWidthSlider = ({ text = '', width, setWidth}) => {
  return (
    <Flex vertical className='smudge-slider-container' >
      { text && <div className={'text14 font-pf color-1a brush-tool-text'}>{text}</div> }
      <Tooltip title={'笔刷大小'}>
        <Slider
          value={width}
          min={8}
          max={128}       // 超过 128 svg 就不显示了
          tooltip={{ open: false }}
          onChange={(e) => setWidth(e)}
          ariaRequired
        />
      </Tooltip>
    </Flex>
  );
};

/**
 * 移动缩放工具栏
 * @param className
 * @param isMoving
 * @param onMoveing
 * @param onReset
 * @param onUpscale
 * @param onDownscale
 * @param vertical
 * @param scaleRate 缩放比例
 * @param onScaleChange 缩放比例变化回调（可选）
 * @constructor
 */
export const SmudgeMoveTools = ({className = "", scaleRate = 100, isMoving, onMoving, onReset, onUpscale, onDownscale, onScaleChange, vertical = false, gap=8 }: {
  className?: string;
  scaleRate?: number;
  isMoving: boolean;
  onMoving: () => void;
  onReset: () => void;
  onUpscale: () => void;
  onDownscale: () => void;
  onScaleChange?: (value: number) => void;
  vertical?: boolean;
  gap?: number;
}) =>  {
  return (
    <Flex gap={gap} vertical={vertical} style={{alignItems: 'center'}}>
      <Tooltip title={'复位'}>
        <Button className='smudge-toolkit-button' onClick={onReset} icon={<IconFont type={'icon-reset'}/>} />
      </Tooltip>
      <Tooltip title={'移动'}>
        <Button className={`smudge-toolkit-button${isMoving?'-active':''}`} onClick={onMoving} icon={<IconFont type={'icon-move'} />} />
      </Tooltip>
      <Tooltip title={'缩小'}>
        <Button className='smudge-toolkit-button' onClick={onDownscale} icon={<IconFont type={'icon-minus'}/>} />
      </Tooltip>
      <Flex align="center" style={{gap: 2}}>
        <InputNumber
          className='smudge-toolkit-icon'
          style={{width: 40, textAlign: 'center'}}
          value={scaleRate}
          min={30}
          max={300}
          onChange={(value) => onScaleChange?.(value || 100)}
          onFocus={(e) => e.target.select()}
          controls={false}
          size="small"
        />
        <span className={'smudge-toolkit-icon'}>%</span>
      </Flex>
      <Tooltip title={'放大'}>
        <Button className='smudge-toolkit-button' onClick={onUpscale} icon={<IconFont type={'icon-plus'}/>} />
      </Tooltip>
    </Flex>
  )
}

/**
 * 撤销重做工具栏
 * @param className 拼接 className 便于修改样式
 * @param isErasing
 * @constructor
 */
export const SmudgeUndoTools = ({className = "", onUndo, onRedo, onClean,gap=8}) => {
  return (
    <Flex gap={gap}>
      <Tooltip title={'撤销'}>
        <Button className='smudge-toolkit-button' onClick={onUndo} icon={<IconFont type={'icon-undo'}/>} />
      </Tooltip>
      <Tooltip title={'重做'}>
        <Button className='smudge-toolkit-button' onClick={onRedo} icon={<IconFont type={'icon-redo'}/>} />
      </Tooltip>
      <Tooltip title={'清空'}>
        <Button className='smudge-toolkit-button' onClick={onClean} icon={<IconFont type={'icon-refresh'}/>} />
      </Tooltip>
     </Flex>
  )
}


/**
 * @param className 拼接 className 便于修改样式
 * @param switchSeg 清除按钮的回调
 * @param strokeWidth 笔刷直径
 * @param setStrokeWidth 笔刷直径回调
 * @constructor
 */
export const SmudgeBrushTool = ({className = "", switchSeg, strokeWidth, setStrokeWidth}) => {
  const SegLabel = ({ iconUrl, text }) => {
    return <Flex style={{ width: 62, height: 28, gap: 8 }} align={'center'} justify={'center'}>
      <IconFont type={iconUrl} width={16} height={16} />
      <div className={'text14 font-pf color-1a'}>{text}</div>
    </Flex>;
  };
  return (
    <Flex className={"brush-tool" + className ? " " + className : ""}
          gap={16} align={'flex-start'} justify={'center'}>
      <Flex vertical className={'brush-tool-content'}>
        <div className={'text14 font-pf color-1a brush-tool-text'}>笔刷属性</div>
        <Segmented
          value={false}
          style={{ backgroundColor: '#E1E3EB' }}
          onChange={(value) => switchSeg(value as boolean)}
          options={[{
            label: <SegLabel iconUrl={'icon-tumo'} text={'涂抹'} />,
            value: false,
          }, {
            label: <SegLabel iconUrl={'icon-cachu'} text={'清空'} />,
            value: true,
          }]}
        />
      </Flex>
      <Flex vertical className={'brush-tool-content'} style={{ width: 190 }}>
        <div className={'text14 font-pf color-1a brush-tool-text'}>笔刷大小</div>
        <Slider value={strokeWidth} min={8} max={150} tooltip={{ open: false }}
                onChange={e => setStrokeWidth(e)} />
      </Flex>
    </Flex>
  )
}


export const SmudgeToolkit = ({className = "", gap = 6, useEditorState}) => {
  useEffect(() => {
    return () => {
      // 在组件卸载时重置状态
      onReset();
      onClean();
    };
  }, []);

  const [editorState, setEditorState] = useEditorState;
  
  // 计算缩放百分比
  const scaleRate = Math.round(editorState.scale * 100);
  
  // 实现各种操作函数
  const switchSeg = (isErasing) => {
    setEditorState(prev => ({
      ...prev,
      isErasing,
      mode: 'draw'
    }));
  };
  
  const setStrokeWidth = (width) => {
    setEditorState(prev => ({
      ...prev,
      strokeWidth: width
    }));
  };
  
  const onReset = () => {
    setEditorState(prev => ({
      ...prev,
      scale: 1,
      position: {x: 0, y: 0}
    }));
  };
  
  const onMoving = () => {
    setEditorState(prev => ({
      ...prev,
      mode: prev.mode === 'move' ? 'draw' : 'move'
    }));
  };
  
  const onUpscale = () => {
    setEditorState(prev => {
      const oldScale = prev.scale;
      const newScale = Math.min(oldScale + 0.1, 3);
      
      // 使用标准画布尺寸 (大部分画布都是这个尺寸), 这个参数传递起来比较麻烦, 暂时先按这个来, 体感上差不太多
      const canvasWidth = 560;
      const canvasHeight = 560;
      
      // 以画布中心为基准计算新的位置
      const centerX = canvasWidth / 2;
      const centerY = canvasHeight / 2;
      
      // 计算当前中心点在图片坐标系中的位置
      const centerPointInImage = {
        x: (centerX - prev.position.x) / oldScale,
        y: (centerY - prev.position.y) / oldScale,
      };
      
      // 计算新的位置，使得图片在中心点保持不变
      const newPosition = {
        x: centerX - centerPointInImage.x * newScale,
        y: centerY - centerPointInImage.y * newScale,
      };
      
      return {
        ...prev,
        scale: newScale,
        position: newPosition
      };
    });
  };
  
  const onDownscale = () => {
    setEditorState(prev => {
      const oldScale = prev.scale;
      const newScale = Math.max(oldScale - 0.1, 0.5);
      
      // 使用标准画布尺寸 (大部分画布都是这个尺寸)
      const canvasWidth = 560;
      const canvasHeight = 560;
      
      // 以画布中心为基准计算新的位置
      const centerX = canvasWidth / 2;
      const centerY = canvasHeight / 2;
      
      // 计算当前中心点在图片坐标系中的位置
      const centerPointInImage = {
        x: (centerX - prev.position.x) / oldScale,
        y: (centerY - prev.position.y) / oldScale,
      };
      
      // 计算新的位置，使得图片在中心点保持不变
      const newPosition = {
        x: centerX - centerPointInImage.x * newScale,
        y: centerY - centerPointInImage.y * newScale,
      };
      
      return {
        ...prev,
        scale: newScale,
        position: newPosition
      };
    });
  };
  
  const maxRedoStackSize = 50; // 设置重做栈的最大大小

  const onUndo = () => {
    if (editorState.lines.length === 0) return;
    
    const lastLine = editorState.lines[editorState.lines.length - 1];
    setEditorState(prev => ({
      ...prev,
      lines: prev.lines.slice(0, -1),
      redoStack: [...prev.redoStack, lastLine].slice(-maxRedoStackSize) // 限制大小
    }));
  };
  
  const onRedo = () => {
    if (editorState.redoStack.length === 0) return;
    
    const lastRedoLine = editorState.redoStack[editorState.redoStack.length - 1];
    setEditorState(prev => ({
      ...prev,
      lines: [...prev.lines, lastRedoLine],
      redoStack: prev.redoStack.slice(0, -1)
    }));
  };
  
  const onClean = () => {
    setEditorState(prev => ({
      ...prev,
      lines: [],
      redoStack: []
    }));
  };
  
  const onScaleChange = (value) => {
    const newScale = Math.max(0.5, Math.min(3, value / 100)); // 限制在 0.5 到 3 之间
    setEditorState(prev => {
      const oldScale = prev.scale;
      
      // 使用标准画布尺寸 (大部分画布都是这个尺寸)
      const canvasWidth = 560;
      const canvasHeight = 560;
      
      // 以画布中心为基准计算新的位置
      const centerX = canvasWidth / 2;
      const centerY = canvasHeight / 2;
      
      // 计算当前中心点在图片坐标系中的位置
      const centerPointInImage = {
        x: (centerX - prev.position.x) / oldScale,
        y: (centerY - prev.position.y) / oldScale,
      };
      
      // 计算新的位置，使得图片在中心点保持不变
      const newPosition = {
        x: centerX - centerPointInImage.x * newScale,
        y: centerY - centerPointInImage.y * newScale,
      };
      
      return {
        ...prev,
        scale: newScale,
        position: newPosition
      };
    });
  };
  
  // 缩放回调函数 - 直接设置缩放值
  const handleScaleChange = (scale) => {
    setEditorState(prev => ({
      ...prev,
      scale: Math.max(0.3, Math.min(3, scale))
    }));
  };
  
  // 位置回调函数
  const handlePositionChange = (position) => {
    setEditorState(prev => ({
      ...prev,
      position
    }));
  };
  
  return (
    <Flex gap={gap} className='smudge-toolkit-container'>
      <BrushSegment isErasing={editorState.isErasing} switchSeg={switchSeg}/>
      {/*<span style={{alignContent: 'center', color: '#c5c3c3'}}>|</span>*/}
      <StrokeWidthSlider width={editorState.strokeWidth} setWidth={setStrokeWidth}/>
      <SmudgeMoveTools
        isMoving={editorState.mode === 'move'}
        onMoving={onMoving} 
        onReset={onReset} 
        onDownscale={onDownscale} 
        onUpscale={onUpscale} 
        onScaleChange={onScaleChange}
        scaleRate={scaleRate}
        gap={gap}
      />
      {/*<span style={{alignContent: 'center', color: '#c5c3c3'}}>|</span>*/}
      <SmudgeUndoTools onUndo={onUndo} onRedo={onRedo} onClean={onClean} gap={gap} />
    </Flex>
  )
}

// 获取高级交互功能的Hook
export const useSmudgeInteraction = (useEditorState) => {
  const [editorState, setEditorState] = useEditorState;
  
  // 缩放回调函数 - 直接设置缩放值
  const handleScaleChange = (scale) => {
    setEditorState(prev => ({
      ...prev,
      scale: Math.max(0.3, Math.min(3, scale))
    }));
  };
  
  // 位置回调函数
  const handlePositionChange = (position) => {
    setEditorState(prev => ({
      ...prev,
      position
    }));
  };
  
  return {
    editorState,
    onScaleChange: handleScaleChange,
    onPositionChange: handlePositionChange
  };
};

export default SmudgeBrushTool; 