import React from 'react';
import SmudgeCanvasCopy from './SmudgeCanvasCopy';
import { SmudgeToolkit, useSmudgeInteraction } from './SmudgeBrushTool';
import useSmudgeToolkit from '@/hooks/useSmudgeToolkit';

/**
 * SmudgeCanvas 高级交互功能示例
 * 
 * 新的交互方案：
 * 鼠标操作：
 * - 滚轮上下滑动：图片上下移动
 * - Shift + 滚轮：图片左右移动
 * - Ctrl/Cmd + 滚轮：图片缩放
 * 
 * 触控板操作：
 * - 双指滑动：图片跟随手指移动（所有方向）
 * - 双指捏合：图片缩放
 * 
 * 左键涂抹功能保持不变
 */
const SmudgeCanvasExample = ({ stageCmtRef, imageUrl, lines, handleLinesChange }) => {
  // 使用涂抹工具包
  const useEditorState = useSmudgeToolkit();
  
  // 使用高级交互功能
  const { editorState, onScaleChange, onPositionChange } = useSmudgeInteraction(useEditorState);

  return (
    <div style={{ width: '100%', height: '600px' }}>
      <SmudgeCanvasCopy
        stageCmtRef={stageCmtRef}
        imageUrl={imageUrl}
        lines={editorState.lines}
        handleLinesChange={useEditorState.handleLinesChange}
        editorState={editorState}
        onScaleChange={onScaleChange}
        onPositionChange={onPositionChange}
      />
      <div style={{ marginTop: 16 }}>
        <SmudgeToolkit useEditorState={[editorState, useEditorState.setEditorState]} />
      </div>

      
      {/* 交互说明 */}
      <div style={{ marginTop: 16, padding: 16, backgroundColor: '#f5f5f5', borderRadius: 8 }}>
        <h4>🎮 交互操作说明：</h4>
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16 }}>
          <div>
            <h5>🖱️ 鼠标操作：</h5>
            <ul>
              <li>🎡 <strong>滚轮上下</strong> → 图片上下移动</li>
              <li>⬅️➡️ <strong>Shift + 滚轮</strong> → 图片左右移动</li>
              <li>🔍 <strong>Ctrl/Cmd + 滚轮</strong> → 图片缩放</li>
              <li>🖌️ <strong>左键拖拽</strong> → 涂抹功能</li>
            </ul>
          </div>
          <div>
            <h5>👆 触控板操作：</h5>
            <ul>
              <li>👆👆 <strong>双指滑动</strong> → 图片跟随移动</li>
              <li>🤏 <strong>双指捏合</strong> → 图片缩放</li>
              <li>👆 <strong>单指点击拖拽</strong> → 涂抹功能</li>
            </ul>
          </div>
        </div>
        <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
          ✨ 所有操作仅在图片区域内生效，确保不会干扰界面其他部分
        </div>
      </div>
    </div>
  );
};

export default SmudgeCanvasExample; 