import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import useImage from 'use-image';
import { createBlankCanvas } from '@/utils/canvasUtils';
import { Flex } from 'antd';
import { Image as KonvaImage, Layer, Line, Stage } from 'react-konva';
import { message } from 'antd';
import { imageToLines } from '@/utils/canvasUtils';

/**
 * 涂抹画板, 父组件通过 stageCmtRef 获得涂抹结果, 默认是白底黑纹的png格式的图片
 * 图片尺寸与组件显示尺寸一致, 比例与传入图片一致
 *
 * @param stageCmtRef <Stage/>组件的useRef, 由此获取画板的DOM
 * @param imageUrl 原始图片, 在画板中以背景的形式展示
 * @param strokeWidth 画笔粗细,
 * @param isErasing 暂时没用
 * @param lines 画笔笔迹的坐标集合,
 * @param onLinesChange 修改 lines 的回调
 * @param singleSmudge 是否限制涂抹次数, 开启后每次落笔会清空上次的结果(清空 lines)
 * @param reversal 是否反转涂层, 默认是白底黑涂层, 如果为true, 则涂层为黑底白涂层
 * @param originSize 是否使用原始图片尺寸, 默认是false, 使用组件显示尺寸
 * @constructor
 */
const SmudgeCanvas = ({ stageCmtRef, imageUrl, strokeWidth, lines, onLinesChange, singleSmudge = false, reversal = false, originSize = false, isErasing = false, maskImage = '' }) => {
  const [pointerPos, setPointerPos] = useState({ x: 0, y: 0 });
  const isDrawing = useRef(false);
  const [currentLine, setCurrentLine] = useState<Array<{ x: number, y: number }>>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const [imageWidth, setImageWidth] = useState(560);
  const [imageHeight, setImageHeight] = useState(560);
  const [displayWidth, setDisplayWidth] = useState(560);
  const [displayHeight, setDisplayHeight] = useState(560);
  const [blankImageUrl, setBlankImageUrl] = useState('');
  // @ts-ignore
  const [blankImage] = useImage(blankImageUrl, 'Anonymous');
  const [lineColor, setLineColor] = useState('rgba(97, 179, 255, 1)');
  const stageRef = useRef(null);
  const [scale, setScale] = useState(1);

  // 添加防抖相关状态
  const lastMoveTimeRef = useRef(0);
  const requestIdRef = useRef<number | null>(null);
  const lastPosRef = useRef({ x: 0, y: 0 });

  // 添加状态来跟踪图片是否已加载
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const imageRef = useRef({ width: 0, height: 0, scale: 1 });

  // 初始化时只创建一次空白画布
  const strokeColor = reversal ? 'rgba(255,255,255,1)' : 'rgba(0, 0, 0, 1)';
  const blankImageColor = reversal ? '#000000' : '#FFFFFF';

  useEffect(() => {
    if (imageWidth > 0 && imageHeight > 0) {
      const tmpCanvas = createBlankCanvas(imageWidth, imageHeight, blankImageColor);
      setBlankImageUrl(tmpCanvas.toDataURL('image/png'));
    }
  }, [imageWidth, imageHeight, blankImageColor]);

  useEffect(() => {
    if (containerRef.current && imageUrl) {
      const rect = containerRef.current.getBoundingClientRect();
      const containerWidth = rect.width;
      const containerHeight = rect.height;

      const image = new Image();
      image.src = imageUrl;
      image.onload = () => {
        // 保存原始图片尺寸
        imageRef.current = {
          width: image.width,
          height: image.height,
          scale: 1
        };

        // 计算显示尺寸和缩放比例
        const imageAspectRatio = image.width / image.height;
        const containerAspectRatio = containerWidth / containerHeight;

        let displayW, displayH, scale;
        if (imageAspectRatio > containerAspectRatio) {
          displayW = containerWidth;
          displayH = containerWidth / imageAspectRatio;
          scale = image.width / displayW;
        } else {
          displayH = containerHeight;
          displayW = containerHeight * imageAspectRatio;
          scale = image.height / displayH;
        }

        setImageWidth(image.width);
        setImageHeight(image.height);
        setDisplayWidth(displayW);
        setDisplayHeight(displayH);
        if (originSize) {
          setScale(scale);
        }
        setIsImageLoaded(true);
      };
    }
    return () => {
      onLinesChange([]);
      if (requestIdRef.current) {
        cancelAnimationFrame(requestIdRef.current);
      }
    };
  }, [imageUrl]);

  // 修改蒙层处理逻辑
  useEffect(() => {
    if (maskImage && maskImage !== '' && isImageLoaded) {
      const maskImg = new Image();
      maskImg.src = maskImage;

      maskImg.onload = () => {
        const originalWidth = maskImg.width;
        const originalHeight = maskImg.height;

        // 使用保存的原始图片尺寸计算缩放
        const scaleX = displayWidth / imageRef.current.width;
        const scaleY = displayHeight / imageRef.current.height;

        imageToLines(maskImage, {
          strokeWidth: Math.max(strokeWidth * scaleX, 1), // 确保线条宽度也相应缩放
          sampleRate: Math.max(2, Math.floor(Math.min(originalWidth, originalHeight) / 300)),
          threshold: 40,
          lineColor: 'rgba(97, 179, 255, 1)',
          isBlackBackground: reversal
        }).then(result => {
          let processedLines = result.map(line => ({
            ...line,
            points: line.points.map(point => ({
              x: point.x * scaleX,
              y: point.y * scaleY
            })),
            tool: 'draw',
            strokeWidth: Math.max(strokeWidth * scaleX, 1),
          }));

          if (processedLines.length > 100) {
            processedLines = mergeCloseLines(processedLines);
            processedLines = processedLines.map(line => ({
              ...line,
              points: simplifyPoints(line.points, 2)
            }));
          }

          onLinesChange(processedLines);
        }).catch(error => {
          console.error('转换遮罩图像失败:', error);
          message.error('转换遮罩图像失败，请手动涂抹');
        });
      };

      maskImg.onerror = () => {
        console.error('蒙层图像加载失败');
        message.error('蒙层图像加载失败，请手动涂抹');
      };
    }
  }, [maskImage, isImageLoaded, displayWidth, displayHeight]);

  // 优化: 简化resetPos函数，不再每次创建新画布
  const resetPos = useCallback(() => {
    if (stageRef.current) {
      // @ts-ignore
      const pos = stageRef.current.getPointerPosition();
      if (pos) {
        setPointerPos({ x: pos.x, y: pos.y });
      }
    }
  }, []);

  const handleMouseEnter = useCallback(() => {
    resetPos();
  }, [resetPos]);

  const handleMouseLeave = useCallback(() => {
    setPointerPos({ x: 0, y: 0 });
    if (requestIdRef.current) {
      cancelAnimationFrame(requestIdRef.current);
      requestIdRef.current = null;
    }
  }, []);

  const handleMouseDown = useCallback(() => {
    isDrawing.current = true;
    if (singleSmudge) {
      onLinesChange([]);
    }
    // @ts-ignore
    const pos = stageRef.current?.getPointerPosition();
    if (pos) {
      // @ts-ignore
      setCurrentLine([{ x: pos.x, y: pos.y }]);
    }
  }, [singleSmudge, onLinesChange]);

  // 优化: 使用requestAnimationFrame和节流来处理鼠标移动
  const handleMouseMove = useCallback(() => {
    // @ts-ignore
    const pos = stageRef.current?.getPointerPosition();
    if (!pos) return;

    // 保存当前位置供requestAnimationFrame使用
    lastPosRef.current = { x: pos.x, y: pos.y };

    // 如果已经有排队的动画帧更新，不再创建新的
    if (requestIdRef.current) return;

    // 使用requestAnimationFrame保证平滑渲染
    requestIdRef.current = requestAnimationFrame(() => {
      requestIdRef.current = null;

      // 更新指针位置
      setPointerPos(lastPosRef.current);

      // 如果正在绘制，添加点到当前线条
      if (isDrawing.current) {
        const now = Date.now();
        // 添加节流以减少线条中的点数量
        if (now - lastMoveTimeRef.current > 16) { // 约60fps
          setCurrentLine(prevLine => [...prevLine, lastPosRef.current]);
          lastMoveTimeRef.current = now;
        }
      }
    });
  }, []);

  const handleMouseUp = useCallback(() => {
    isDrawing.current = false;
    // @ts-ignore
    const pos = stageRef.current?.getPointerPosition();
    if (pos && currentLine.length > 0) {
      // @ts-ignore
      onLinesChange(prevLines => [...prevLines, {
        points: [...currentLine, { x: pos.x, y: pos.y }],
        tool: isErasing ? 'erase' : 'draw',
        strokeWidth,
        color: lineColor,
      }]);
      setCurrentLine([]);
    }
  }, [currentLine, isErasing, strokeWidth, lineColor, onLinesChange]);

  // 使用useMemo缓存线条渲染，提高性能
  const renderedLines = useMemo(() => {
    return lines.map((line, i) => (
      <Line
        key={i}
        points={line.points.flatMap(p => [p.x, p.y])}
        stroke={'rgba(97, 179, 255, 1)'}
        strokeWidth={line.strokeWidth}
        tension={0.5}
        lineCap="round"
        lineJoin="round"
        globalCompositeOperation={line.tool === 'erase' ? 'destination-out' : 'source-over'}
      />
    ));
  }, [lines]);

  // 使用useMemo缓存当前线条渲染
  const renderedCurrentLine = useMemo(() => {
    if (currentLine.length === 0) return null;

    return (
      <Line
        points={currentLine.flatMap(p => [p.x, p.y])}
        stroke={isErasing ? 'rgba(255, 255, 255, 1)' : lineColor}
        strokeWidth={strokeWidth}
        tension={0.5}
        lineCap="round"
        lineJoin="round"
        globalCompositeOperation={isErasing ? 'destination-out' : 'source-over'}
      />
    );
  }, [currentLine, isErasing, lineColor, strokeWidth]);

  // 使用useMemo缓存隐藏画布中的线条
  const renderedHiddenLines = useMemo(() => {
    return lines.map((line, i) => (
      <Line
        key={i}
        points={line.points.flatMap(p => [p.x * scale, p.y * scale])}
        stroke={strokeColor}
        strokeWidth={line.strokeWidth * scale}
        tension={0.5}
        lineCap="round"
        lineJoin="round"
        globalCompositeOperation={line.tool === 'erase' ? 'destination-out' : 'source-over'}
      />
    ));
  }, [lines, scale, strokeColor]);

  return (
    <Flex ref={containerRef} style={{
      height: '100%', width: '100%',
      backgroundImage: `url(${imageUrl})`,
      backgroundSize: 'contain',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      borderRadius: 8,
      border: '1px dashed #D8D8D8',
    }} justify={'center'}>

      <div style={{
        opacity: 0.5,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <Flex style={{ height: 'auto', width: 'auto', position: 'relative' }}>
          <Stage width={displayWidth}
                 height={displayHeight}
                 onMouseDown={handleMouseDown}
                 onMousemove={handleMouseMove}
                 onMouseup={handleMouseUp}
                 onMouseEnter={handleMouseEnter}
                 onMouseLeave={handleMouseLeave}
                 ref={stageRef}
                 style={{ cursor: 'none' }}  // 隐藏默认鼠标指针
          >
            <Layer>
              <KonvaImage image={blankImage} width={displayWidth} height={displayHeight} opacity={0} />
              {renderedLines}
              {renderedCurrentLine}
            </Layer>
          </Stage>
          {/*优化: 使用transform替代位置计算，减少重排*/}
          <div
            style={{
              display: pointerPos.y > 0 || pointerPos.x > 0 ? 'block' : 'none',
              position: 'absolute',
              top: 0,
              left: 0,
              width: strokeWidth,
              height: strokeWidth,
              borderRadius: '50%',
              backgroundColor: isErasing ? 'rgba(255, 255, 255, 1)' : 'rgba(65, 112, 156, 0.8)',
              pointerEvents: 'none',
              zIndex: 100,
              transform: `translate(${pointerPos.x - strokeWidth / 2}px, ${pointerPos.y - strokeWidth / 2}px)`,
              willChange: 'transform', // 提示浏览器此元素将频繁变化
            }}
          />
        </Flex>
      </div>
      <Stage className={'repair-detail-stage'}
        width={originSize ? imageWidth : displayWidth}
        height={originSize ? imageHeight : displayHeight}
        ref={stageCmtRef}
        style={{ display: 'none' }}>
        <Layer>
          <KonvaImage image={blankImage} width={originSize ? imageWidth : displayWidth} height={originSize ? imageHeight : displayHeight} />
          {renderedHiddenLines}
        </Layer>
      </Stage>
    </Flex>
  );
};

interface Point {
  x: number;
  y: number;
}

interface Line {
  points: Point[];
  tool: string;
  strokeWidth: number;
  color: string;
}

function mergeCloseLines(lines: Line[], distanceThreshold = 10): Line[] {
  if (lines.length <= 1) return lines;

  const result: Line[] = [];
  let currentLine = lines[0];

  for (let i = 1; i < lines.length; i++) {
    const nextLine = lines[i];

    // 检查当前线的末点和下一条线的起点之间的距离
    const currentEnd = currentLine.points[currentLine.points.length - 1];
    const nextStart = nextLine.points[0];

    const distance = Math.sqrt(
      Math.pow(currentEnd.x - nextStart.x, 2) +
      Math.pow(currentEnd.y - nextStart.y, 2)
    );

    if (distance < distanceThreshold &&
      currentLine.tool === nextLine.tool &&
      currentLine.strokeWidth === nextLine.strokeWidth) {
      // 如果足够近且属性相同，合并这两条线
      currentLine = {
        ...currentLine,
        points: [...currentLine.points, ...nextLine.points]
      };
    } else {
      // 否则将当前线添加到结果中，开始处理下一条线
      result.push(currentLine);
      currentLine = nextLine;
    }
  }

  // 添加最后一条处理的线
  result.push(currentLine);

  return result;
}

function simplifyPoints(points: Point[], tolerance = 1): Point[] {
  if (points.length <= 2) return points;

  if (points.length < 10) return points;

  const simplified: Point[] = [];
  for (let i = 0; i < points.length; i += Math.max(1, Math.floor(points.length / 100))) {
    simplified.push(points[i]);
  }

  // 确保包含最后一个点
  if (simplified[simplified.length - 1] !== points[points.length - 1]) {
    simplified.push(points[points.length - 1]);
  }

  return simplified;
}

export default SmudgeCanvas;