# 🎛️ SmudgeCanvas 交互灵敏度配置文档

## 📍 配置位置
所有交互灵敏度配置都位于：
```
src/components/imageOperate/smudgeCanvas/SmudgeCanvasCopy.tsx
```
在 `handleWheel` 函数开始处的 **"🎛️ 交互灵敏度配置区域"**

---

## 🔍 缩放相关配置

### `SCALE_SENSITIVITY = 1.08`
- **作用**: 每次缩放的步长
- **推荐范围**: 1.05 - 1.15
- **调整建议**:
  - 值越小，缩放越细腻，需要更多滚轮滚动
  - 值越大，缩放越快，但可能过于敏感
  - 1.05 = 超级细腻，1.15 = 较快缩放

### `SCALE_MIN = 0.3`
- **作用**: 最小缩放倍数（30%）
- **推荐范围**: 0.1 - 0.5
- **调整建议**: 太小可能看不清图片，太大限制了缩小能力

### `SCALE_MAX = 3.0`
- **作用**: 最大缩放倍数（300%）
- **推荐范围**: 2.0 - 5.0
- **调整建议**: 根据用户需求调整，过大可能影响性能

---

## 🖱️ 鼠标操作灵敏度配置

### `MOUSE_WHEEL_VERTICAL_SENSITIVITY = 0.8`
- **作用**: 鼠标滚轮上下移动图片的灵敏度
- **推荐范围**: 0.5 - 1.5
- **调整建议**:
  - 0.5 = 移动很慢，适合精细调整
  - 1.0 = 标准速度
  - 1.5 = 移动较快，适合快速浏览

### `MOUSE_WHEEL_HORIZONTAL_SENSITIVITY = 0.8`
- **作用**: Shift + 滚轮左右移动图片的灵敏度
- **推荐范围**: 0.5 - 1.5
- **调整建议**: 同上，一般与垂直移动保持一致

---

## 👆 触控板操作灵敏度配置

### `TRACKPAD_SCROLL_SENSITIVITY = 1.0`
- **作用**: 触控板双指滑动移动图片的灵敏度
- **推荐范围**: 0.8 - 1.2
- **调整建议**:
  - 0.8 = 移动较慢，更精确
  - 1.0 = 标准速度，接近原生手势
  - 1.2 = 移动较快，减少手势幅度

### `TRACKPAD_DETECTION_THRESHOLD = 0.1`
- **作用**: 触控板双指滑动的检测阈值
- **推荐范围**: 0.05 - 0.2
- **调整建议**:
  - 值越小，越容易触发触控板模式
  - 值越大，越不容易误触发
  - 如果经常误判，增大此值

---

## 🎯 使用场景优化建议

### 精细操作场景 (设计、修图)
```typescript
const MOUSE_WHEEL_VERTICAL_SENSITIVITY = 0.5;
const MOUSE_WHEEL_HORIZONTAL_SENSITIVITY = 0.5;
const TRACKPAD_SCROLL_SENSITIVITY = 0.8;
const SCALE_SENSITIVITY = 1.05;
```

### 快速浏览场景 (预览、查看)
```typescript
const MOUSE_WHEEL_VERTICAL_SENSITIVITY = 1.2;
const MOUSE_WHEEL_HORIZONTAL_SENSITIVITY = 1.2;
const TRACKPAD_SCROLL_SENSITIVITY = 1.2;
const SCALE_SENSITIVITY = 1.12;
```

### 平衡场景 (当前配置)
```typescript
const MOUSE_WHEEL_VERTICAL_SENSITIVITY = 0.8;
const MOUSE_WHEEL_HORIZONTAL_SENSITIVITY = 0.8;
const TRACKPAD_SCROLL_SENSITIVITY = 1.0;
const SCALE_SENSITIVITY = 1.08;
```

---

## 🔧 调试技巧

1. **测试不同设备**: 在不同的鼠标、触控板上测试
2. **用户反馈**: 收集用户对操作手感的反馈
3. **A/B测试**: 可以准备几套配置供用户选择
4. **渐进调整**: 每次只调整一个参数，观察效果

---

## ⚠️ 注意事项

1. **保持一致性**: 相关操作的灵敏度应该保持相对一致
2. **避免极值**: 过大或过小的值都可能导致不良体验
3. **测试兼容性**: 在不同浏览器和操作系统上测试
4. **备份配置**: 修改前记录当前工作良好的配置

---

## 📝 修改记录模板

```
日期: YYYY-MM-DD
修改人: [姓名]
修改原因: [用户反馈/性能优化/新需求等]
修改内容:
- MOUSE_WHEEL_VERTICAL_SENSITIVITY: 0.8 → 0.6
- 原因: 用户反馈移动过快，需要更精细的控制
测试结果: [效果描述]
``` 