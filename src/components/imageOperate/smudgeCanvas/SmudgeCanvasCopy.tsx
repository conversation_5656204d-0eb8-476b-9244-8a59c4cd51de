import { EditorProps, LineConfig } from '@/hooks/useSmudgeToolkit';
import { getGroupPointerPosition } from '@/utils/canvasUtils';
import { Flex } from 'antd';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Group,
  Image as KonvaImage,
  Layer,
  Line,
  Rect,
  Stage,
} from 'react-konva';
import useImage from 'use-image';

export interface SmudgeCanvasProps {
  stageCmtRef: any;
  imageUrl: string;
  lines: LineConfig[];
  handleLinesChange: (lines: LineConfig[]) => void;
  handleSizeChange?: (width: number, height: number) => void;
  singleSmudge?: boolean;
  reversal?: boolean;
  originSize?: boolean;
  editorState: EditorProps;
  onScaleChange?: (scale: number) => void;
  onPositionChange?: (position: { x: number; y: number }) => void;
  keep?: boolean;
}

/**
 * 涂抹画板, 父组件通过 stageCmtRef 获得涂抹结果, 默认是白底黑纹的png格式的图片
 * 图片尺寸与组件显示尺寸一致, 比例与传入图片一致
 *
 * @param stageCmtRef <Stage/>组件的useRef, 由此获取画板的DOM
 * @param imageUrl 原始图片, 在画板中以背景的形式展示
 * @param strokeWidth 画笔粗细,
 * @param lines 画笔笔迹的坐标集合,
 * @param handleLinesChange 修改 lines 的回调
 * @param singleSmudge 是否限制涂抹次数, 开启后每次落笔会清空上次的结果(清空 lines)
 * @param reversal (未完成) 是否反转涂层, 默认是白底黑涂层, 如果为true, 则涂层为黑底白涂层
 * @param originSize 是否使用原始图片尺寸, 默认是false, 使用组件显示尺寸
 * @constructor
 */
const SmudgeCanvas: React.FC<SmudgeCanvasProps> = ({
  stageCmtRef,
  imageUrl,
  lines = [],
  handleLinesChange,
  handleSizeChange,
  singleSmudge = false,
  reversal = false,
  originSize = false,
  editorState,
  onScaleChange,
  onPositionChange,
  keep = false,
}) => {
  const isDrawing = useRef(false);
  const [currentLine, setCurrentLine] = useState<
    Array<{ x: number; y: number }>
  >([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const [imageWidth, setImageWidth] = useState(560);
  const [imageHeight, setImageHeight] = useState(560);
  const [displayWidth, setDisplayWidth] = useState(560);
  const [displayHeight, setDisplayHeight] = useState(560);
  // @ts-ignore
  const [image] = useImage(imageUrl);
  const [lineColor, setLineColor] = useState('rgba(97, 179, 255, 1)');
  const [scale, setScale] = useState(1);
  const groupRef = useRef(null);

  // 添加状态跟踪鼠标是否在图片范围内
  const [isMouseInImageArea, setIsMouseInImageArea] = useState(false);

  const imageAreaRef = useRef({ x: 0, y: 0, width: 0, height: 0 });

  const stageRef = useRef(null);

  // 添加移动模式的拖动状态
  const [isDragging, setIsDragging] = useState(false);
  const [lastDragPos, setLastDragPos] = useState({ x: 0, y: 0 });

  // 从传入的editorState中获取isErasing
  const isErasing = editorState.isErasing;
  const strokeWidth = editorState.strokeWidth;
  // 实际笔刷粗细需要反向缩放
  const solvedStokeWidth = strokeWidth / editorState.scale;
  // 添加防抖相关状态
  const lastMoveTimeRef = useRef(0);
  const requestIdRef = useRef<number | null>(null);
  const lastPosRef = useRef({ x: 0, y: 0 });

  const imageRef = useRef({ width: 0, height: 0, scale: 1 });

  // 初始化时只创建一次空白画布
  const hideLineColor = reversal ? 'rgba(255,255,255,1)' : 'rgba(0, 0, 0, 1)';
  const blankImageColor = reversal ? '#000000' : '#FFFFFF';

  useEffect(() => {
    if (containerRef.current && imageUrl) {
      const rect = containerRef.current.getBoundingClientRect();
      const containerWidth = rect.width;
      const containerHeight = rect.height;

      const image = new Image();
      image.src = imageUrl;
      image.onload = () => {
        // 保存原始图片尺寸
        imageRef.current = {
          width: image.width,
          height: image.height,
          scale: 1,
        };

        // 计算显示尺寸和缩放比例
        const imageAspectRatio = image.width / image.height;
        const containerAspectRatio = containerWidth / containerHeight;

        let displayW, displayH, scale;
        if (imageAspectRatio > containerAspectRatio) {
          displayW = containerWidth;
          displayH = containerWidth / imageAspectRatio;
          scale = image.width / displayW;
        } else {
          displayH = containerHeight;
          displayW = containerHeight * imageAspectRatio;
          scale = image.height / displayH;
        }
        setImageWidth(image.width);
        setImageHeight(image.height);
        setDisplayWidth(displayW);
        setDisplayHeight(displayH);
        if (handleSizeChange) {
          handleSizeChange(displayW, displayH);
        }

        // 计算图片在容器中的位置（居中显示）
        const offsetX = (containerWidth - displayW) / 2;
        const offsetY = (containerHeight - displayH) / 2;

        // 保存图片区域信息
        imageAreaRef.current = {
          x: offsetX,
          y: offsetY,
          width: displayW,
          height: displayH,
        };

        if (originSize) {
          setScale(scale);
        }
      };
    }
    return () => {
      if (!keep) {
        handleLinesChange([]);
      }
      if (requestIdRef.current) {
        cancelAnimationFrame(requestIdRef.current);
      }
    };
  }, [imageUrl]);

  const handleMouseEnter = useCallback(() => {}, []);

  const handleMouseLeave = useCallback(() => {
    if (requestIdRef.current) {
      cancelAnimationFrame(requestIdRef.current);
      requestIdRef.current = null;
    }
    setIsMouseInImageArea(false);
    isDrawing.current = false;
    // 重置拖动状态
    setIsDragging(false);
  }, []);

  // ==================== 🎛️ 交互灵敏度配置区域 ====================
  // 📝 在此处集中管理所有操作的灵敏度，方便日后调整维护

  // 🔍 缩放相关配置
  const SCALE_SENSITIVITY = 1.08; // 缩放步长 (1.05-1.15 推荐范围)
  const SCALE_MIN = 0.3; // 最小缩放倍数
  const SCALE_MAX = 3.0; // 最大缩放倍数

  // 🖱️ 鼠标操作灵敏度配置
  const MOUSE_WHEEL_VERTICAL_SENSITIVITY = 0.8; // 鼠标滚轮上下移动灵敏度 (推荐: 0.5-1.5)
  const MOUSE_WHEEL_HORIZONTAL_SENSITIVITY = 0.8; // Shift+滚轮左右移动灵敏度 (推荐: 0.5-1.5)

  // 👆 触控板操作灵敏度配置
  const TRACKPAD_SCROLL_SENSITIVITY = 1.0; // 触控板双指滑动灵敏度 (推荐: 0.8-1.2)
  const TRACKPAD_DETECTION_THRESHOLD = 0.1; // 触控板检测阈值 (推荐: 0.05-0.2)

  // ==================== 🎛️ 配置区域结束 ====================

  // 滚轮和触控板事件处理 - 新的交互方案
  // 1. 鼠标: 滚轮上下移动; Shift+滚轮左右移动; Ctrl/Cmd+滚轮缩放
  // 2. 触控板: 双指滑动移动; 双指捏合缩放 (通过Ctrl键检测)
  const handleWheel = useCallback(
    (e) => {
      if (!isMouseInImageArea) return;

      e.evt.preventDefault();

      const stage = e.target.getStage();
      if (!stage) return;

      const pointer = stage.getPointerPosition();
      const scaleBy = SCALE_SENSITIVITY;

      // 1. 检测修饰键 - Ctrl/Cmd + 滚轮 = 缩放
      if (e.evt.ctrlKey || e.evt.metaKey) {
        const delta = -e.evt.deltaY;
        const newScale =
          delta > 0 ? editorState.scale * scaleBy : editorState.scale / scaleBy;
        const clampedScale = Math.max(SCALE_MIN, Math.min(SCALE_MAX, newScale));

        // 以鼠标位置为中心缩放
        const oldScale = editorState.scale;
        const mousePointTo = {
          x: (pointer.x - editorState.position.x) / oldScale,
          y: (pointer.y - editorState.position.y) / oldScale,
        };

        const newPos = {
          x: pointer.x - mousePointTo.x * clampedScale,
          y: pointer.y - mousePointTo.y * clampedScale,
        };

        onScaleChange?.(clampedScale);
        onPositionChange?.(newPos);
      }
      // 2. Shift + 滚轮 = 左右移动
      else if (e.evt.shiftKey) {
        // 强制阻止浏览器的默认 Shift+滚轮行为
        e.evt.stopPropagation();

        // 检测不同浏览器可能的滚轮值
        const deltaValue =
          e.evt.deltaY || e.evt.deltaX || e.evt.wheelDelta || 0;
        const deltaX = -deltaValue * MOUSE_WHEEL_HORIZONTAL_SENSITIVITY;

        const newPos = {
          x: editorState.position.x + deltaX,
          y: editorState.position.y,
        };

        onPositionChange?.(newPos);
      }
      // 3. 触控板双指滑动检测 - 图片跟随手指移动
      else if (Math.abs(e.evt.deltaX) > TRACKPAD_DETECTION_THRESHOLD) {
        // 有横向滑动分量，通常是触控板双指滑动
        const deltaX = -e.evt.deltaX * TRACKPAD_SCROLL_SENSITIVITY;
        const deltaY = -e.evt.deltaY * TRACKPAD_SCROLL_SENSITIVITY;

        const newPos = {
          x: editorState.position.x + deltaX,
          y: editorState.position.y + deltaY,
        };

        onPositionChange?.(newPos);
      }
      // 4. 纯鼠标滚轮 = 上下移动
      else {
        const deltaY = -e.evt.deltaY * MOUSE_WHEEL_VERTICAL_SENSITIVITY;

        const newPos = {
          x: editorState.position.x,
          y: editorState.position.y + deltaY,
        };

        onPositionChange?.(newPos);
      }
    },
    [
      isMouseInImageArea,
      editorState.scale,
      editorState.position,
      onScaleChange,
      onPositionChange,
    ],
  );

  // 阻止右键菜单
  const handleContextMenu = useCallback((e) => {
    e.evt.preventDefault();
  }, []);

  // Group级别的鼠标事件处理 - 处理左键涂抹
  const handleMouseDown = useCallback(
    (e) => {
      // 检查鼠标位置是否在图片区域内
      if (!isMouseInImageArea) {
        return;
      }

      // 只处理左键，右键由Stage处理
      if (e.evt.button !== 0) {
        return;
      }

      const pos = getGroupPointerPosition(e, groupRef.current);

      // 如果是移动模式，开始拖动
      if (editorState.mode === 'move') {
        const stage = e.target.getStage();
        if (stage) {
          const pointer = stage.getPointerPosition();
          setIsDragging(true);
          setLastDragPos(pointer);
        }
        return;
      }

      isDrawing.current = true;
      if (singleSmudge) {
        handleLinesChange([]);
      }

      setCurrentLine([{ x: pos.x, y: pos.y }]);
    },
    [singleSmudge, handleLinesChange, editorState.mode, isMouseInImageArea],
  );

  const handleMouseMove = useCallback(
    (e) => {
      // 如果不在图片区域内，不处理后续操作
      if (!isMouseInImageArea) {
        return;
      }

      // 使用switch语句处理不同的模式
      switch (editorState.mode) {
        // 移动模式
        case 'move':
          if (isDragging) {
            const stage = e.target.getStage();
            if (stage) {
              const pointer = stage.getPointerPosition();
              
              // 计算拖动距离
              const deltaX = pointer.x - lastDragPos.x;
              const deltaY = pointer.y - lastDragPos.y;

              // 更新图片位置
              const newPos = {
                x: editorState.position.x + deltaX,
                y: editorState.position.y + deltaY,
              };

              onPositionChange?.(newPos);
              setLastDragPos(pointer);
            }
          }
          break;

        // 绘制模式（默认）
        case 'draw':
          // 如果没有开始绘制，直接返回
          if (!isDrawing.current) {
            return;
          }

          const pos = getGroupPointerPosition(e, groupRef.current);
          // 保存当前位置供requestAnimationFrame使用
          lastPosRef.current = { x: pos.x, y: pos.y };

          // 如果已经有排队的动画帧更新，不再创建新的
          if (requestIdRef.current) return;

          // 使用requestAnimationFrame保证平滑渲染
          requestIdRef.current = requestAnimationFrame(() => {
            requestIdRef.current = null;

            // 如果正在绘制，添加点到当前线条
            if (isDrawing.current) {
              const now = Date.now();
              // 添加节流以减少线条中的点数量
              if (now - lastMoveTimeRef.current > 16) {
                // 约60fps
                setCurrentLine((prevLine) => [...prevLine, lastPosRef.current]);
                lastMoveTimeRef.current = now;
              }
            }
          });
          break;
        default:
          break;
      }
    },
    [editorState.mode, isMouseInImageArea, isDragging, editorState.position, lastDragPos, onPositionChange],
  );

  const handleMouseUp = useCallback(
    (e) => {
      // 只处理左键抬起
      if (e.evt.button !== 0) {
        return;
      }

      switch (editorState.mode) {
        case 'move':
          // 结束拖动
          setIsDragging(false);
          break;

        case 'draw':
          const pos = getGroupPointerPosition(e, groupRef.current);
          if (pos && currentLine.length > 0) {
            const newLine = {
              points: [...currentLine, { x: pos.x, y: pos.y }],
              tool: isErasing ? 'erase' : 'draw',
              strokeWidth: solvedStokeWidth,
              color: lineColor,
            };
            // 确保处理函数正确调用
            if (typeof handleLinesChange === 'function') {
              const currentLines = Array.isArray(lines) ? lines : [];
              handleLinesChange([...currentLines, newLine]);
            }
            setCurrentLine([]);
            isDrawing.current = false;
          }
          break;
        default:
          break;
      }
    },
    [
      currentLine,
      isErasing,
      strokeWidth,
      lineColor,
      handleLinesChange,
      editorState.mode,
      lines,
    ],
  );

  // 使用useMemo缓存线条渲染，提高性能
  const renderedLines = useMemo(() => {
    if (!Array.isArray(lines)) return null;

    return lines.map((line, i) => (
      <Line
        key={i}
        points={line.points.flatMap((p) => [p.x, p.y])}
        stroke={lineColor}
        strokeWidth={line.strokeWidth}
        tension={0.5}
        lineCap="round"
        lineJoin="round"
        globalCompositeOperation={
          line.tool === 'erase' ? 'destination-out' : 'source-over'
        }
      />
    ));
  }, [lines]);

  // 使用useMemo缓存当前线条渲染
  const renderedCurrentLine = useMemo(() => {
    if (currentLine.length === 0) return null;
    return (
      <Line
        points={currentLine.flatMap((p) => [p.x, p.y])}
        stroke={lineColor}
        strokeWidth={solvedStokeWidth}
        tension={0.5}
        lineCap="round"
        lineJoin="round"
        globalCompositeOperation={isErasing ? 'destination-out' : 'source-over'}
      />
    );
  }, [currentLine, isErasing, lineColor, strokeWidth]);

  // 使用useMemo缓存隐藏画布中的线条
  const renderedHiddenLines = useMemo(() => {
    return lines.map((line, i) => (
      <Line
        key={i}
        points={line.points.flatMap((p) => [p.x * scale, p.y * scale])}
        stroke={hideLineColor}
        strokeWidth={line.strokeWidth * scale}
        tension={0.5}
        lineCap="round"
        lineJoin="round"
        globalCompositeOperation={
          line.tool === 'erase' ? 'destination-out' : 'source-over'
        }
      />
    ));
  }, [lines, scale, hideLineColor]);

  return (
    <Flex
      ref={containerRef}
      style={{
        height: '100%',
        width: '100%',
        borderRadius: 8,
        border: '1px dashed #D8D8D8',
        position: 'relative',
        overflow: 'hidden',
      }}
      justify={'center'}
      align={'center'}
    >
      <Stage
        ref={stageRef}
        width={containerRef.current?.clientWidth}
        height={containerRef.current?.clientHeight}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onWheel={handleWheel}
        onContextMenu={handleContextMenu}
        style={{
          cursor: isMouseInImageArea
            ? editorState.mode === 'move'
              ? isDragging ? 'grabbing' : 'grab'
              : `url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='${strokeWidth}' height='${strokeWidth}' viewBox='0 0 ${strokeWidth} ${strokeWidth}'><circle cx='${strokeWidth / 2}' cy='${strokeWidth / 2}' r='${strokeWidth / 2 - 1}' fill='white' fill-opacity='0.5' stroke='%2397B3FF' stroke-width='1'/></svg>") ${strokeWidth / 2} ${strokeWidth / 2}, none`
            : 'default',
        }}
        draggable={false}
      >
        <Layer
          scale={{ x: editorState.scale, y: editorState.scale }}
          position={editorState.position}
        >
          <Group
            globalCompositeOperation={'source-over'}
            x={imageAreaRef.current.x}
            y={imageAreaRef.current.y}
          >
            <KonvaImage
              image={image}
              width={displayWidth}
              height={displayHeight}
            />
          </Group>
        </Layer>
        <Layer
          scale={{ x: editorState.scale, y: editorState.scale }}
          position={editorState.position}
        >
          <Group
            globalCompositeOperation={'source-over'}
            x={imageAreaRef.current.x}
            y={imageAreaRef.current.y}
            onMouseEnter={() => {
              setIsMouseInImageArea(true);
            }}
            onMouseLeave={() => {
              setIsMouseInImageArea(false);
            }}
            onMouseDown={handleMouseDown}
            onMousemove={handleMouseMove}
            onMouseup={handleMouseUp}
            ref={groupRef}
          >
            {renderedLines}
            {renderedCurrentLine}
            <Rect
              width={displayWidth}
              height={displayHeight}
              fill={lineColor}
              opacity={0.5}
              globalCompositeOperation={'source-in'}
            />
          </Group>
        </Layer>
      </Stage>

      <Stage
        className={'smudge-hide-stage'}
        width={originSize ? imageWidth : displayWidth}
        height={originSize ? imageHeight : displayHeight}
        ref={stageCmtRef}
        style={{ display: 'none' }}
      >
        <Layer>
          <Rect
            width={originSize ? imageWidth : displayWidth}
            height={originSize ? imageHeight : displayHeight}
            fill={'#ffffff'}
          />
        </Layer>
        <Layer>
          {renderedHiddenLines}
          <Rect
            width={originSize ? imageWidth : displayWidth}
            height={originSize ? imageHeight : displayHeight}
            fill={'#000000'}
            globalCompositeOperation={'source-in'}
          />
        </Layer>
      </Stage>
    </Flex>
  );
};

export default SmudgeCanvas;
