// 图片点
import React from 'react';

// 操作类型枚举
export enum OperateType {
  // 擦除
  ERASE = 'erase',
  // 图片填充
  FILL = 'fill',
}

// 涂抹类型
export enum BrushType{
  // 消除笔
  ERASER = 'eraser',
  // 修手
  HAND = 'hand',
}

// 图片点
export interface ImagePoint {
  x: number;
  y: number;
}

// 自定义线条
export interface CustomLine {
  points: Array<ImagePoint>;
  tool: string;
  strokeWidth: number;
  color: string;
}


// 笔刷组件属性
export interface BrushComponentProps {
  // 背景图片Ref
  backgroundStageRef: React.RefObject<any>;
  // 背景图片地址
  backgroundImageUrl: string;
  // 空白图片
  blankImage: HTMLImageElement | undefined;
  // 是否正在擦除
  isErasing: boolean;
  // 笔刷大小
  strokeWidth: number;
  // 线条数据
  lines: CustomLine[];
  // 当前线条
  currentLine: ImagePoint[];
  // 指针位置
  pointerPos: ImagePoint;
  // 设置线条数据
  setLines: (lines: CustomLine[]) => void;
  // 是否选中
  selected?: boolean;
  // 是否使用白色背景
  isUseWhiteBackgroundForMark?: boolean;
}

