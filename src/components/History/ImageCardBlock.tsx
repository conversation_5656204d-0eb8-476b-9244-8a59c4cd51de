import WatermarkImage from '@/components/WatermarkImage';
import {
  addCreativeDemoTag,
  CreativeVO,
  getCreativeStatusName,
  getCreativeTypeName,
  isBatchLike, isDemoTag,
  isExampleImage,
  isProcessing,
  isRefineCompleted,
  isRefineInit,
  setCreativeToFail,
} from '@/services/CreativeController';
import { Button, Flex, message, Popconfirm, Tooltip } from 'antd';
import React from 'react';
import { formatText, formatTimeDiffMinute, getUserInfo } from '@/utils/utils';
import { MinusOutlined, PlayCircleFilled, PlusOutlined, ToolFilled } from '@ant-design/icons';
import IconFont from '@/components/IconFont';
import { getClothMarkIconByCreative } from '@/services/MaterialModelController';
import { MemoText } from '@/components/Common/CommonComponent';
import './ImageCardBlock.less';
import UserFavorButton from '@/components/Favor/UserFavorButton';

interface ImageCardBlockProps {
  item: CreativeVO;
  isOperate?: boolean;
  isDistributor?: boolean;
  ownerType?: string;
  width?: number | string;
  isCompact?: boolean;
  onClick: (item: CreativeVO) => void;
  failCallback?: () => void;
}

const userInfo = getUserInfo();
const ImageCardBlock: React.FC<ImageCardBlockProps> = ({
  item,
  isOperate = false,
  isDistributor = false,
  ownerType = isDistributor ? 'customer' : 'mine',
  width,
  isCompact = false,
  onClick,
  failCallback,
}) => {

  const setFail = (item) => {
    setCreativeToFail(item.id).then(res => {
      if (failCallback) {
        failCallback();
      }
      message.success('设置成功');
    });
  };

  const handleAddDemoTag = (item) => {
    addCreativeDemoTag(item.id).then(res => {
      if (!res) return;

      if (failCallback) {
        failCallback();
      }
      message.success('设置成功');
    })
  }

  const formatCloth = (item: CreativeVO) => {
    if (!isCompact) {
      return item.modelName;
    }
    const exampleImage = isExampleImage(item);
    return <>
      {formatText(item.modelName, exampleImage ? 6 : 10)}
      {exampleImage &&
        <>(<span style={{ color: 'red' }}>精选图</span>)</>
      }
    </>;
  };

  const getDownLoadTimes = (item: CreativeVO) => {
    return item.resultImages ? item.resultImages.filter((imgUrl: string) => item?.extInfo['downloadedImgs']?.includes(imgUrl)).length : 0;
  };

  // 获取当前已处理的图片数量（仅在处理中状态显示）
  const getCurrentImageCount = () => {
    if (!isProcessing(item)) return '';
    return `${item.resultImages ? item.resultImages.length : 0}/`;
  };

  // 获取图片总数
  const getTotalImageCount = () => {
    if (item.type === 'REPAIR_HANDS') {
      return 4;
    }

    // 基础款换衣(出 4 张图)
    if (item.type === 'BASIC_CHANGING_CLOTHES' || item.type === 'CLOTHING_SWAP') {
      return item.batchCnt * 4;
    }

    // 基础款换衣(出 3 张图)
    if (item.type === 'FIXED_POSTURE_CREATION') {
      return item.batchCnt * 3;
    }

    if (item.type === 'CREATE_VIDEO' && item.extInfo['originImage']) {
      return item.extInfo['originImage'].length;
    }

    // 细节修补
    if (item.type === 'REPAIR_DETAIL') {
      return item.batchCnt * 2;
    }

    // 姿势示例图 (特殊展示)
    if (item.type === 'POSE_SAMPLE_DIAGRAM') {
      return item.extInfo['imageCount'] === undefined ? (item.resultImages?.length || 0) : item.extInfo['imageCount'];
    }

    return item.batchCnt;
  };

  return (
    <div className="history-image-card" style={{ width: width ? width : undefined, gap: isCompact ? 4 : 8 }}>
      <div className="history-img-cover" onClick={() => onClick(item)}
        style={{ cursor: 'pointer', width: width ? width : undefined, height: width ? width : undefined }}>

        {item?.modelMarkedColor && item?.modelMarkedColor !== 'None' && (
          <IconFont type={getClothMarkIconByCreative(item)}
            style={{ fontSize: 16, position: 'absolute', top: 8, right: 8 }} />
        )}

        {isOperate && ['testCase', 'systemGen'].includes(item.extInfo['bizTag']) &&
          <div className={'history-image-biz-tag'}>
            {item.extInfo['bizTag'] === 'testCase' && <Tooltip title={'AB实验自动出图'}><span>测</span></Tooltip>}
            {item.extInfo['bizTag'] === 'systemGen' &&
              <Tooltip title={'客户服装训练后自动出图'}><span>自</span></Tooltip>}
          </div>
        }

        {item.showImage ? (
          <WatermarkImage src={item.showImage} height={'100%'} needWatermark={item.type !== 'CREATE_VIDEO'} />
        ) : (
          <div className="history-img-no-image">
            该任务无底图
          </div>
        )}

        {isOperate &&
          <Flex align={'center'} justify={'center'} vertical={true} gap={8}
            style={{
              width: (item.status === 'PROCESSING' ? '100%' : ''),
              height: (item.status === 'PROCESSING' ? '100%' : ''),
              position: 'absolute', background: 'rgba(41, 41, 41, 0.4)',
              padding: 8, borderRadius: 8
            }}>
            {item.status === 'PROCESSING' &&
              <>
                <l-helix size="45" speed="2.5" color={'blue'} />
                <div className={'color-w text16'}>出图中</div>
              </>
            }

            {item.status === 'QUEUE' &&
              <div className={'color-w text16'}>未开始</div>
            }

            {item.status !== 'QUEUE' && item.type !== 'CREATE_VIDEO' &&
              <div className={'color-w text12'}>
                {item.status === 'FAILED' ? (
                  <span style={{
                    color: '#ff4d4f',
                    backgroundColor: 'rgba(255, 77, 79, 0.1)',
                    padding: '2px 6px',
                    borderRadius: '4px',
                    fontWeight: 'bold',
                    fontSize: '12px'
                  }}>生成失败</span>
                ) : (
                  <>
                    {item.status === 'PROCESSING' ? '已用时' : '耗时'}
                    {formatTimeDiffMinute(item.extInfo['startTime'], item.extInfo['endTime'])}
                  </>
                )}
              </div>
            }
          </Flex>
        }

        <div className="history-img-count">
          共{getCurrentImageCount()}{getTotalImageCount()}张
        </div>

        {isRefineInit(item) && (isOperate || isDistributor) &&
          <div style={{ position: 'absolute', top: 8, right: 8 }}>
            <ToolFilled style={{ fontSize: 32, color: 'red' }} className={'color-brand'} />
          </div>
        }

        {isRefineCompleted(item) && (isOperate || isDistributor) &&
          <div style={{ position: 'absolute', top: 8, right: 8 }}>
            <ToolFilled style={{ fontSize: 32 }} className={'color-brand'} />
          </div>
        }

        {isOperate && (isBatchLike(item, true) || isBatchLike(item, false)) &&
          <Flex gap={8} style={{ position: 'absolute', top: 8, left: 8, border: '1px solid red' }}>
            {isBatchLike(item, true) &&
              <IconFont type={'icon-rongqi'} className={'img-icon-like color-1a'} />
            }

            {isBatchLike(item, false) &&
              <IconFont type={'icon-buxihuan_dianji'} className={'img-icon-like color-1a'} />
            }
          </Flex>
        }

        {(isOperate || isDistributor) && getDownLoadTimes(item) > 0 &&
          <div className={'history-detail-img-icon color-w'}
            style={{ backgroundColor: 'green', left: 8, width: 50, alignItems: 'center', justifyContent: 'center' }}>
            <Tooltip title={`图片已被下载${getDownLoadTimes(item)}张`}>
              <IconFont type={'icon-a-shangchuan1x'} style={{ fontSize: '12px' }} /> x {getDownLoadTimes(item)}
            </Tooltip>
          </div>
        }

        {item.type === 'CREATE_VIDEO' &&
          <Flex gap={8} justify={'center'} align={'center'}
            style={{ position: 'absolute', top: 0, left: 0, width: (width ? width : '100%'), height: '100%' }}>
            <PlayCircleFilled style={{ fontSize: 32, color: '#FFFFFF' }} />
          </Flex>
        }
      </div>
      <div className={'history-image-card-info-block'}>
        <div className={'history-image-card-info-inner-left'}>
          {!(item.type === 'CREATE_IMAGE' && isCompact) &&
            <div className="history-image-card-info text14 font-pf weight color-1a">
              功能：{getCreativeTypeName(item.type, item.bizType)}
              {isExampleImage(item) &&
                <>(<span style={{ color: 'red' }}>精选图</span>)</>
              }
              {['ADMIN', 'OPERATOR'].includes(userInfo?.roleType ? userInfo?.roleType : '') &&
                <MemoText value={item.id} />
              }

              {['ADMIN'].includes(userInfo?.roleType ? userInfo?.roleType : '') &&
                <Popconfirm title={`是否将当前创作记录${isDemoTag(item) ? '从演示数据中删除' : '添加到演示数据'}`}
                  onConfirm={() => handleAddDemoTag(item)}>
                  <div className={'image-demo-block' + (isDemoTag(item) ? ' image-demo-block-selected' : '')}>
                    <Tooltip title={`点击${isDemoTag(item) ? '从演示数据中删除' : '添加到演示数据'}`}>
                      {isDemoTag(item) ? <MinusOutlined /> : <PlusOutlined />}演示
                    </Tooltip>
                  </div>
                </Popconfirm>
              }
            </div>
          }

          {item.type === 'CREATE_IMAGE' &&
            <div className="history-image-card-info font-pf text14">服装：{formatCloth(item)}</div>
          }

          {item.type === 'CREATE_VIDEO' && item.title &&
            <div className="history-image-card-info font-pf text14">标题：{item.title}</div>
          }

          {(item.type === 'CREATE_IMAGE' || item.type === 'LOGO_COMBINE') && !isOperate && item?.faceName && !isCompact &&
            <div className="history-image-card-info">模特：{item.faceName}</div>
          }

          {(isOperate || (isDistributor && ownerType === 'customer') || userInfo?.roleType === 'DISTRIBUTOR') &&
            <div className="history-image-card-info">商户：{item.userNick}</div>
          }

          {(isOperate || (isDistributor && ownerType === 'customer') || userInfo?.roleType === 'DISTRIBUTOR') &&
            <>
              <div className="history-image-card-info">操作人：{item.operatorNick}</div>
              {item.deliveryName &&
                <div className="history-image-card-info">交付人：{item.deliveryName}</div>
              }
            </>
          }

          <div className="history-image-card-info">状态：{getCreativeStatusName(item.status)}</div>

          <div className="history-image-card-info">时间：{item.createTime}</div>

          {isOperate &&
            <Flex gap={8}>
              {(item.status === 'PROCESSING' || item.status === 'QUEUE') &&
                <Popconfirm title={`确认置为失败`}
                  description={`是否要将该创作纪录置为失败？置为失败不退缪斯点`}
                  onConfirm={() => setFail(item)}
                >
                  <Button>置为失败</Button>
                </Popconfirm>
              }
            </Flex>
          }
        </div>
        {(userInfo?.roleType !== 'ADMIN') && item.status === 'FINISHED' && !isCompact &&
          <div className={'history-image-card-info-inner-right'}>
            <UserFavorButton favorType={item.type === 'CREATE_VIDEO' ? 'VIDEO' : 'IMAGE'} itemId={item.id} images={Array.from(item.resultImages?.keys() || [])} />
          </div>
        }
      </div>
    </div>
  );
};

export default ImageCardBlock;