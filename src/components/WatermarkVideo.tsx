import React from 'react';
import { Flex } from 'antd';
import WATER_MARK from '@/assets/icon/watermark.png';
import { PlayCircleFilled } from '@ant-design/icons';

interface WatermarkVideoProps {
  className?: string;
  src: string;
  width?: number | string;
  controls?: boolean;
  needWatermark?: boolean;
  autoPlay?: boolean;
  onClick?: () => void;
}

const WatermarkVideo: React.FC<WatermarkVideoProps> = ({
                                                         className = '',
                                                         src,
                                                         width,
                                                         controls = true,
                                                         needWatermark = true,
                                                         autoPlay = false,
                                                         onClick,
                                                       }) => {

  return <>
    <div style={{ position: 'relative', width: 'auto', height: '100%' }} className={'pointer ' + className} onClick={() => {
      if (onClick) onClick();
    }}>
      <video width={'auto'} height={'100%'} controls={controls} autoPlay={autoPlay} muted style={{maxWidth: src ?  'auto' : '100%'}}>
        <source src={src} type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {needWatermark &&
        <Flex gap={8} style={{ position: 'absolute', bottom: '10%', right: 8, width: (width ? width : '100%') }}
              justify={'flex-end'}>
          <img src={WATER_MARK} alt={'watermark'} className="no-inherit"
               style={{ width: (width ? `calc(${width}*35.34%) !important` : '35.34% !important') }}
               width={(width ? `calc(${width}*35.34%) !important` : '35.34% !important')} />
        </Flex>
      }
      {!controls &&
        <Flex gap={8} style={{ position: 'absolute', top: 0, left: 0, width: (width ? width : '100%'), height: '100%' }}
              justify={'center'} align={'center'}>
          <PlayCircleFilled style={{ fontSize: 46, color: '#FFFFFF' }} />
        </Flex>
      }
    </div>
  </>;
};

export default WatermarkVideo;