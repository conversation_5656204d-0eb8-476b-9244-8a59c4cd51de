import { <PERSON><PERSON>, But<PERSON>, Flex, Segmented } from 'antd';
import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  ElementConfig,
  getElementTypes,
  getMerchantRecentElement,
  queryElementByPage,
   queryFavorElements,
  SceneType,
} from '@/services/ElementController';
import { BizType, CreativeType, ElementKey, LoraType } from '@/services/CreativeController';
import { deepCopy, getUserInfo } from '@/utils/utils';
import ElementWithTypeSelector from '@/components/Creative/ElementWithTypeSelector';
import IconFont from '@/components/IconFont';
import { ExperienceModelOpenDetail } from '@/services/SystemController';
import TopupModal from '@/pages/Topup/Topup';
import TextArea from 'antd/lib/input/TextArea';
import '@/components/Creative/ElementWithTypeBlock.less';
import { querySystemScene } from '@/services/PromptDictController';
import NewLabel from '@/components/new/NewLabel';
import { IS_TRIAL_ACCOUNT } from '@/constants';
import { UploadImage } from '@/pages/FaceSceneSwitch';
import UserFavorButton from '@/components/Favor/UserFavorButton';
import { favorManager } from '@/utils/favorManager';
import { useNavigate } from 'react-router-dom';
import MerchantPreferenceBlock, { MerchantPreferenceVO } from '@/components/Creative/MerchantPreferenceBlock';
import { MiniTag } from '@/components/Common/CommonComponent';
import debounce from 'lodash/debounce';

const userInfo = getUserInfo();
export const isLockedElement = (loraType: LoraType,
  configKey: string,
  id: number,
  experienceModelOpenCfg: ExperienceModelOpenDetail | null | undefined,
  isVipUser: boolean) => {
  // 演示账户和非vip账户才需要加锁逻辑，演示账户不展示被锁的元素
  if (isVipUser && !['DEMO_ACCOUNT', 'OPERATOR', 'ADMIN'].includes(userInfo?.roleType || '')) {
    return false;
  }

  if (!experienceModelOpenCfg) {
    return false;
  }

  const configList = configKey === 'FACE' ? experienceModelOpenCfg?.faces : experienceModelOpenCfg?.scenes;

  if (!configList || configList.length === 0) {
    return false;
  }

  return !configList?.includes(id);
};

export const ElementLockMask = ({ paddingTop = 64, backdropFilter = 'blur(10px)' }) => {
  return <>
    <Flex align={'center'} justify={'center'} style={{
      background: 'rgba(0, 0, 0, 0.3)', zIndex: 3, width: '100%',
      height: '100%', position: 'absolute', top: 0, left: 0,
      paddingTop,
    }}>
      <Flex align={'center'} justify={'center'} gap={4} style={{
        height: 32, borderRadius: 25, padding: '0 8px', background: 'rgba(255, 255, 255, 0.4)',
        backdropFilter,
      }}>
        <IconFont type={'icon-suo'} style={{ fontSize: 16, color: '#fff' }} />
        <div className={'text14 color-w'}>vip解锁</div>
      </Flex>
    </Flex>
  </>;
};

export interface ElementWithTypeBlockProps {
  config: ElementConfig;
  moreTitle?: string;
  defaultType: string;
  current: Array<ElementConfig>;
  acceptTypes?: Array<string>,
  conditions?: any;
  needExclusive?: boolean;
  needFavor?: boolean; // 显示我的收藏
  popShowAll?: boolean; //弹窗展示全部
  moreIcon: string;
  moreBg: string;
  orderByType?: boolean;
  loraType?: LoraType;
  experienceModelOpenCfg?: ExperienceModelOpenDetail | null | undefined;
  isVipUser?: boolean;
  modelId?: number | null | undefined;
  creativeType?: CreativeType;
  uploadImgProps?: { tag: string, image: string, onImageChange: any, onShowImageSelector: any, uploadProps: any };
  foot?: any;
  showConfirmFooter?: boolean;
  onChange: (configId: number, value: Array<ElementConfig> | null, isPreset?: boolean, customScene?: string) => void;
  refreshConfig?: () => Promise<ElementConfig[]>;
  bizType?: BizType;
  onlyLora?: boolean;
}

const defaultSystemScene = [
  {
    name: '场景示例1',
    desc: '豪华的泳池边，地中海风格的建筑，有华丽的石栏杆、茂盛的棕榈树和大陶罐中盛开的鲜艳花朵。小桌上放着一杯热带果汁。头顶上是湛蓝的天空，飘过几朵云',
  },
  {
    name: '场景示例2',
    desc: '简洁素雅的白墙，左侧有拱形墙面，自然光从右上角照进，左侧阴影。拱形墙面旁有一小段台阶，通往略高处的平台，平台上摆放着两个花瓶和一个浅色碗，背景有深棕色窗帘。站着 一只手插裤袋另一只手打招呼',
  },
  {
    name: '场景示例3',
    desc: '清晰的生活风情照片，秋天，杭州西湖断桥，落满五彩缤纷的落叶, 群山 清澈的湖水 秋日的阳光. 明亮 高清. 坐在长椅上, 聚集人物',
  },
  {
    name: '场景示例4',
    desc: '在纽约布鲁克林的一座现代工业风格的房子里，有金色的时光灯。房间有裸露的砖墙，为空间增添了质朴的质感。房间有落地窗，可以欣赏到外面城市景观的广阔景色。地板是木制的。房间中央有一张宽大的柔软织物沙发和一张圆形咖啡桌。室内有大窗户, 坐在沙发上, 聚焦人物',
  },
  {
    name: '场景示例5',
    desc: '街道 现代风格 星巴克门店外面，独特屋顶的星巴克门店 星巴克LOGO，门口摆放着户外桌椅，阳光。全身看镜头, 写实风格',
  },
];

export const filterByCondition = (origin: ElementConfig[], conditions: any, acceptTypes: Array<string> | undefined) => {
  let filter = origin;
  if (conditions && conditions.faceIds) {
    filter = filter.filter(c => conditions.faceIds.includes(c.id));
  }

  if (conditions && conditions.clothType) {
    filter = filter.filter(c => conditions.clothType === 'Unisex' ? c.type.some(type => ['Female', 'Male'].includes(type)) : (c.type.includes(conditions.clothType) || c.type.includes('Common')));
  }

  if (conditions && conditions.version) {
    filter = filter.filter(c => c.type.includes(conditions.version));
  }

  if (acceptTypes && acceptTypes.length > 0) {
    filter = filter.filter(c => c.type.some(type => acceptTypes.includes(type)));
  }

  if (conditions && conditions.positions) {
    filter = filter.filter(c => !c.styleScene || c.type.some(t => conditions.positions.includes(t)));
  }

  if (conditions && conditions.bodyTyps) {
    filter = filter.filter(c => !c.styleScene || c.type.some(t => conditions.bodyTyps.includes(t)));
  }

  // 根据服装的ageRange属性过滤模特和场景
  if (conditions && conditions.ageRange) {
    // 判断是否包含child关键字
    const isChildRange = conditions.ageRange.includes('child');
    if (isChildRange) {
      // 如果是儿童范围，保持原有逻辑，筛选包含对应ageRange的元素
      filter = filter.filter(c => !c.type || c.type.includes(conditions.ageRange));
    } else {
      // 如果是成人范围或为空，排除所有包含儿童标签的元素
      const childTypes = ['big-child', 'medium-child', 'small-child'];
      filter = filter.filter(c => !c.type || !c.type.some(type => childTypes.includes(type)));
    }
  }
  
  if (conditions && conditions.clothCategory) {
    filter = filter.filter(c => !c.extInfo?.clothCategory
      || c.extInfo.clothCategory.length === 0
      || c.extInfo.clothCategory.some(t => conditions.clothCategory.includes(t)));
  }
  return filter;
};

export const filerTypeList = (typeList, config: ElementConfig, acceptTypes) => {
  const copy = deepCopy(typeList);
  copy.forEach(type => {
    if (type.code === 'recent' || type.code === 'exclusive' || type.code === 'all' || type.code === 'favor' || !config || !config.children) {
      return;
    }

    if (acceptTypes && !acceptTypes.some(t => t === type.code)) {
      type.empty = true;
      return;
    }

    const elements = config.children.filter(c => c.type && c.type.includes(type.code));
    type.empty = elements.length <= 0;
  });
  return copy;
};

const ElementWithTypeBlock: React.FC<ElementWithTypeBlockProps> = ({
  config,
  moreTitle,
  defaultType,
  current,
  acceptTypes = [],
  conditions,
  needExclusive = false,
  needFavor = false,
  popShowAll = false,
  moreIcon,
  moreBg,
  orderByType = false,
  loraType = 'CUSTOM',
  experienceModelOpenCfg,
  isVipUser = false,
  modelId,
  creativeType = 'CREATE_IMAGE',
  uploadImgProps,
  foot = null,
  onChange,
  refreshConfig,
  showConfirmFooter = true,
  bizType = null,
  onlyLora = false,
}) => {
  const isMounted = useRef(false);

  const [showList, setShowList] = React.useState<Array<ElementConfig>>([]);
  const [showType, setShowType] = React.useState<string>(defaultType);
  const [showAll, setShowAll] = useState<boolean>(false);
  const [typeList, setTypeList] = useState<Array<SceneType>>([]);
  const [showTypeList, setShowTypeList] = useState<Array<SceneType>>([]);

  const [recentList, setRecentList] = useState<ElementConfig[]>([]);
  const [showAllPop, setShowAllPop] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('官方预设');
  const [error, setError] = useState<string>('');
  const maxShowNum = 9;
  const [systemScene, setSystemScene] = useState<{ name: string, desc: string }[]>(defaultSystemScene);
  const [showTopupModal, setShowTopupModal] = useState(false);
  const canCustomScene = config.configKey === 'SCENE' && loraType === 'CUSTOM';
  const isTrialAccount = sessionStorage.getItem(IS_TRIAL_ACCOUNT) === 'Y';
  const [customScene, setCustomScene] = useState<string>(systemScene[0].desc);
  const [selectedKey, setSelectedKey] = useState<string>(systemScene[0].name);
  const canUpload = config.configKey === 'SCENE' && creativeType === 'FACE_SCENE_SWITCH';

  const navigate = useNavigate();

  // 将doPreset函数移到fetchRecent之前
  const doPreset = (recentList: ElementConfig[]) => {
    const key = `preset.${config.configKey.toLowerCase()}`;
    const preset = sessionStorage.getItem(key);
    if (preset) {
      const presetObj = Number(preset);
      sessionStorage.removeItem(key);

      const find = config.children.find(e => e.id === presetObj);
      if (!find) {
        console.log('warn', '未找到预设值', preset);
        return;
      }

      if (!recentList.some(e => e.id === presetObj)) {
        recentList.unshift(find);
      }

      onChange(config.id, [find], true);
    }
  };

  // 加上handleShowAllPop函数
  const handleShowAllPop = async () => {
    if (refreshConfig) {
      const res = await refreshConfig();
      if (res) {
        setShowAllPop(true);
      }
    } else {
      setShowAllPop(true);
    }
  };

  // 构建查询条件 
  const buildQuery = (isExclusive: boolean) => {
    // 获取年龄范围
    const ageRange = conditions && conditions.ageRange ? conditions.ageRange : null;
    // 服装款式
    const clothCategory = conditions?.clothCategory ?? null;

    // 获取服装类型 
    const clothTypes = conditions.clothType ? (conditions.clothType === 'Unisex' ? ['Female', 'Male'] : [conditions.clothType, 'Common']) : null;
    // 获取服装类型
    let types = [...acceptTypes, ...(conditions && conditions.version ? [conditions.version] : [])];
    // types = [...types, ...(conditions && conditions.positions ? conditions.positions : []), ...(conditions && conditions.bodyType ? conditions.bodyType : [])];
    let ids = conditions.faceIds ? conditions.faceIds : [];
    const limitList = experienceModelOpenCfg ? (config.configKey === 'FACE' ? experienceModelOpenCfg.faces : experienceModelOpenCfg.scenes) : [];
    if ('DEMO_ACCOUNT' === userInfo?.roleType) {
      ids = [...ids, ...limitList ? limitList : []];
    }
    const orderBy = loraType === 'SYSTEM' && limitList && limitList.length > 0 ? `if(id in ('${limitList.join(",")}'),1,0) desc, is_new desc,if(status='PROD',0,1),\`order\`,modify_time desc` : null;

    return {
      configKey: config.configKey,
      pageNum: 1,
      pageSize: 9,
      ids: ids && ids.length > 0 ? ids : null,
      types,
      isExclusive: isExclusive ? isExclusive : null,
      typesOr: clothTypes,
      positions: conditions && conditions.positions ? conditions.positions : null,
      bodyTypes: conditions && conditions.bodyTypes ? conditions.bodyTypes : null,
      bizType,
      onlyLora,
      ageRange,
      orderBy,
      onlyExperimental: false,
    };
  };

  // 查询数据
  const fetchExclusive = async () => {
    queryElementByPage(buildQuery(true)).then(res => {
      if (!res) return;
      setShowList(res.list || []);
    });
  };

  const fetchFavor = async () => {
    queryFavorElements(buildQuery(false)).then(res => {
      if (!res) return;
      setShowList(res.list || []);
    });
  };

  // 使用useCallback和debounce包装fetchRecent函数，防止频繁调用
  const fetchRecent = async () => {
      // 获取配置key
      const key = config.configKey;
      // 获取年龄范围和服装类型，将条件值复制到本地变量
      const ageRange = conditions?.ageRange || null;
      const clothType = conditions?.clothType || null;
      
      // 获取年龄范围
      let ageRanges: string[] | null = [];
      if(modelId){
        const isChildRange = ageRange && typeof ageRange === 'string' && ageRange.includes('child');
        ageRanges = isChildRange ? null : ['big-child', 'medium-child', 'small-child'];
      }

      // 确定是否需要进行API调用
      const shouldFetchData = clothType || ageRange || !recentList || recentList.length === 0;
      
      if (!shouldFetchData) {
        console.log('跳过API调用，使用现有数据');
        return;
      }

      //查当前商户最近使用过的场景列表
      getMerchantRecentElement({
        key,
        userId: null,
        type: creativeType,
        bizType: bizType || 'ALL',
        ageRange,
        ageRanges,
        clothType,
        onlyExperimental: false,
      }).then(sceneListRes => {
        let list = sceneListRes ? sceneListRes : [];
        //对配置值进行预设
        doPreset(list);
        setRecentList(list);

        if (showType === 'recent') {
          setShowList(list.slice(0, maxShowNum));
        }
      });
  };

  useEffect(() => {
    setSceneType(String(defaultType));
    const key = config.configKey;

    //查场景分类列表
    getElementTypes(key as ElementKey).then(types => {
      if (types) {
        let typeList = [{ code: 'recent', name: '最近使用' }];

        if (!popShowAll) {
          typeList = [...typeList, ...types];
        }

        if (needExclusive) {
          typeList = [...typeList, { 'code': 'exclusive', name: `专属${config.name}` }];
        }

        if (needFavor) {
          // 添加收藏类型
          typeList = [...typeList, { 'code': 'favor', name: `收藏${config.name}` }];
        }

        if (popShowAll) {
          typeList = [...typeList, { 'code': 'all', name: `全部${config.name}` }];
        }

        setTypeList(typeList);
        setShowTypeList(typeList);
        refreshTypeList(typeList, acceptTypes);
      }

      //查当前商户最近使用过的场景列表
      fetchRecent();

      // 页面加载完成后设置标志位
      isMounted.current = true;
    });
    if (loraType === 'CUSTOM') {
      querySystemScene().then(result => {
        if (result && result.length > 0) {
          setSystemScene(result.map(item => ({ name: item.word, desc: item.prompt })));
        }
      });
    }


  }, []);

  // useEffect(() => {
  //   refreshTypeList(typeList, acceptTypes);
  // }, [acceptTypes]);

  useEffect(() => {
    if (!isMounted.current) return;
    fetchRecent();
  }, [bizType]);

  
  // 当条件变化时（特别是服装类型变化时）重新加载列表并更新过滤结果
  useEffect(() => {
    if (!isMounted.current) return;
    
    // 如果是服装类型变化，则直接调用fetchRecent获取新数据
    if (showType === 'recent') {
      fetchRecent();
    }
    
  }, [modelId]);

  useEffect(() => {
    setSceneType(showType as string);
  }, [config]);

  const refreshTypeList = (typeList, acceptTypes) => {
    const copy = filerTypeList(typeList, config, acceptTypes);
    setShowTypeList(copy);

    if (current) {
      const filter = filterByCondition(config.children, conditions, acceptTypes);
      const ret = current.filter(e => filter.some(f => f.id === e.id));
      if (current.length !== ret.length) {
        onChange(config.id, ret.length > 0 ? ret : null);
      }
    }

    if (acceptTypes && !acceptTypes.includes(showType) && !(showType === 'recent' || showType === 'exclusive' || showType === 'favor' || showType === 'all')) {
      setSceneType(acceptTypes[0]);
    }
  };

  // 获取最近使用列表
  const getExperienceDefaultRecentList = (recentList: ElementConfig[]) => {
    let filter = recentList && recentList.length > 0 ? recentList : [];

    // 如果用户是VIP用户，或者没有经验模型配置，则返回最近使用列表
    if (isVipUser || !experienceModelOpenCfg) {
      return filter;
    }

    // 获取经验模型配置列表
    const configList = config.configKey === 'FACE' ? experienceModelOpenCfg.faces : experienceModelOpenCfg.scenes;

    // 如果型配置列表不为空，则返回配置列表
    if (configList && Array.isArray(configList) && configList.length > 0) {
      return config.children.filter(e => configList.includes(e?.id));
    }

    // 如果配置列表为空，则返回所有配置
    return config.children;
  };

  const setSceneType = (type: string) => {
    if (type === 'all') {
      handleShowAllPop();
      setShowAll(true);
      return;
    }

    setShowType(type);

    //专属
    if (type === 'exclusive') {
      fetchExclusive();

      if (current && current.some(e => isLockedElement(loraType, e.configKey, e.id, experienceModelOpenCfg, isVipUser))) {
        onChange(config.id, null);
      }

      return;
    }

    if (type === 'favor') {
      fetchFavor();
      if (current && current.some(e => isLockedElement(loraType, e.configKey, e.id, experienceModelOpenCfg, isVipUser))) {
        onChange(config.id, null);
      }

      return;
    }

    if (!config || !config.children) {
      return;
    }

    //选中'最近使用'时将列表设置为近期使用列表
    let filter = type === 'recent' ? getExperienceDefaultRecentList(recentList) : config.children;

    // 处理收藏类型
    if (type === 'favor') {
      // 获取 favorManager 中的收藏列表
      const favorList = favorManager.getFavorList();

      // 如果收藏列表为空，尝试加载收藏列表
      if (favorList.length === 0 && !favorManager.isLoading()) {
        // 设置一个标志，防止重复请求
        const loadingFlag = favorManager.isLoading();
        if (!loadingFlag) {
          favorManager.fetchAllFavors().then(() => {
            // 加载完成后，只更新显示列表，不再调用 setSceneType
            const newFavorList = favorManager.getFavorList();
            const favorType = 'ELEMENT';
            const favoredIds = newFavorList
              .filter(item => item.type === favorType)
              .map(item => item.itemId);

            // 过滤出已收藏的元素
            const favoredElements = config.children.filter(item => favoredIds.includes(item.id));

            // 更新显示列表
            setShowList(favoredElements);
          });
        }

        // 先显示空列表，等数据加载完成后会自动更新
        setShowList([]);
        return;
      }

      // 根据配置类型过滤收藏列表
      const favorType = 'ELEMENT'; // 在这个组件中，所有类型都映射到 'ELEMENT'

      // 过滤出当前类型的收藏项
      const favoredIds = favorList
        .filter(item => item.type === favorType)
        .map(item => item.itemId);

      // 过滤出已收藏的元素
      filter = filter.filter(item => favoredIds.includes(item.id));
      // 确保收藏按钮不会被禁用
      const copy = deepCopy(showTypeList);
      const favorButton = copy.find(item => item.code === 'favor');
      if (favorButton) {
        favorButton.empty = false;
        setShowTypeList(copy);
      }
    } else if (type === 'exclusive') {
      filter = filter.filter(c => c.extInfo && c.extInfo['openScope'] && c.extInfo['openScope'] !== 'ALL');
    } else if (needExclusive && type !== 'recent') {
      filter = filter.filter(c => !c.extInfo || !c.extInfo['openScope'] || c.extInfo['openScope'] === 'ALL');
    }

    filter = filterByCondition(filter, conditions, acceptTypes);

    filter = filter.filter(e => !isLockedElement(loraType, e.configKey, e.id, experienceModelOpenCfg, isVipUser));
    if (current && current.some(e => isLockedElement(loraType, e.configKey, e.id, experienceModelOpenCfg, isVipUser))) {
      onChange(config.id, null);
    }
    if (current && current[0]?.styleScene && filterByCondition(current, conditions, acceptTypes).length <= 0) {
      onChange(config.id, null);
    }
    if (current && !filter.some(e => e.id === current[0].id)) {
      onChange(config.id, null);
    }

    if (type === 'recent' || type === 'exclusive' || type === 'all' || type === 'favor') {

      filter = filter.length > maxShowNum ? filter.slice(0, maxShowNum) : filter;

      if (current && type === 'recent') {
        current.forEach(item => {
          if (!filter.some(e => e.id === item.id)) {
            filter = [item, ...filter];
          }
        });
      }

      filter = filter.length > maxShowNum ? filter.slice(0, maxShowNum) : filter;

      // 重新排序
      filter = sortElementsByIsNew(filter);

      // 赋值
      setShowList(filter);
      return;
    }

    const index = filter.findIndex(e => e.type.some(t => t === type));
    if (index === -1) {
      const find = typeList.find(type => filter && filter.some(item => item.type.some(e => e === type.code)));
      if (find) {
        type = find.code;
        setShowType(type);
      }
    }

    const copy = deepCopy(typeList);
    copy.forEach(type => {
      if (type.code !== 'recent' && type.code !== 'exclusive') {
        type.empty = !(filter && filter.some(item => item.type.some(e => e === type.code)));
      }
    });
    setShowTypeList(copy);

    if (current) {
      const ret = current.filter(e => filter.some(item => item.id === e.id) || filter.some(item => item.id === e.parentId));
      if (current.length !== ret.length) {
        onChange(config.id, ret.length > 0 ? ret : null);
      }
    }

    filter = filter.filter(c => c.type && c.type.includes(type));

    // 重新排序
    filter = sortElementsByIsNew(filter);

    // 赋值
    setShowList(filter);
  };

  // 根据 IsNew 重新排序
  const sortElementsByIsNew = (elements: ElementConfig[]): ElementConfig[] => {
    return elements.sort((a, b) => {
      // a 在前
      if (a.isNew && !b.isNew) {
        return -1;
      }
      // b 在前
      if (!a.isNew && b.isNew) {
        return 1;
      }
      // 保持原顺序
      return 0;
    });
  };

  const isMatch = (current, item) => {
    return current && (current.some(e => e.id === item.id) || current.some(e => e.parentId === item.id));
  };

  const handleChange = (item: ElementConfig) => {
    if (isLockedElement(loraType, item.configKey, item.id, experienceModelOpenCfg, isVipUser)) {
      setShowTopupModal(true);
      return;
    }

    onChange(config.id, [item]);
  };

  const handleSelectChange = (items: Array<ElementConfig>) => {
    onChange(config.id, items);
    setShowAllPop(false);
    
    // 创建更新后的recentList，确保新选择的项在列表中
    const recent = deepCopy(recentList);
    items.forEach(item => {
      if (!recent.some(e => e.id === item.id)) {
        recent.unshift(item);
      }
    });
    setRecentList(recent);

    setShowList(recent.slice(0, maxShowNum));
    setShowType('recent');
  };

  const handleTextAreaChange = (event) => {
    setError('');
    setCustomScene(event.target.value);
    setSelectedKey('');
    onChange(config.id, null, undefined, event.target.value);
  };
  const handleTextAreaBlur = (event) => {
    if (event.target.value === '') {
      setError('自定义场景不得为空');
    } else if (event.target.value.length < 20) {
      setError('自定义场景不少于20字');
    } else {
      setError('');
    }
    if (event.target.value !== '') {
      onChange(config.id, null, undefined, customScene);
    }
  };
  const handleSegmentedChange = (value) => {
    setError('');
    if (value === '官方预设') {
      setCustomScene('');
      onChange(config.id, null, undefined, '');
      uploadImgProps?.onImageChange('');
    } else if (value === '自定义') {
      setCustomScene(systemScene[0].desc);
      setSelectedKey(systemScene[0].name);
      onChange(config.id, null, undefined, systemScene[0].desc);
      uploadImgProps?.onImageChange('');
    } else if (value === '上传图片') {
      setCustomScene('');
      onChange(config.id, null, undefined, '');
    }
    setActiveTab(value as string);
  };

  const append = (value) => {
    setCustomScene(value);
    onChange(config.id, null, undefined, value);
  };

  const onLabelClick = (item: MerchantPreferenceVO) => {
    append(item.extInfo?.customSceneDesc);
    setSelectedKey('');
  };

  return (
    <div key={config.id} className={'work-item-container'}>
      <div
        className="tool-bar"
        style={{ display: 'flex', alignItems: 'baseline', gap: '8px' }}
      >
        <div
          className={'text16 font-pf color-n weight'}
          style={{ whiteSpace: 'nowrap' }}
        >
          选择{config.name}
          {moreTitle && <span className={'text16 color-72'}>{moreTitle}</span>}
        </div>
        <Flex
          justify={'space-between'}
          align={'center'}
          className={'width-100'}
        >
          {canCustomScene && !canUpload && (
            <Segmented
              className={'scene-type-segmented'}
              options={['官方预设', '自定义']}
              style={{ backgroundColor: '#E1E3EB', height: 32 }}
              value={activeTab}
              onChange={handleSegmentedChange}
            />
          )}
          {canCustomScene && canUpload && (
            <Segmented
              className={'scene-type-segmented'}
              options={['官方预设', '自定义', '上传图片']}
              style={{ backgroundColor: '#E1E3EB', height: 32 }}
              value={activeTab}
              onChange={handleSegmentedChange}
            />
          )}
        </Flex>
      </div>

      <div className={'scene-inner-container'}>
        {/* 用户自定义场景 */}

        {activeTab === '自定义' && canCustomScene && (
          <>
            <Flex
              gap={8}
              align={'center'}
              wrap={'wrap'}
              style={{ height: 'auto', marginBottom: 8 }}
            >
              <div className={'text12 color-96'}>试试这些</div>
              {systemScene?.map((item) => (
                <div
                  key={item.name}
                  className={
                    'text12 color-72 pointer' +
                    ' cloth-collocation-preset-item' +
                    (selectedKey === item.name ? '-selected' : '')
                  }
                  onClick={() => {
                    setError('');
                    append(item.desc);
                    setSelectedKey(item.name);
                  }}
                >
                  <div
                    className={selectedKey === item.name ? 'light-text' : ''}
                  >
                    {item.name}
                  </div>
                </div>
              ))}
            </Flex>
            <MerchantPreferenceBlock
              type={'CUSTOM_SCENE'}
              preference={{ extInfo: { customSceneDesc: customScene } }}
              onLabelClick={onLabelClick}
              className={'custom-scene-preference-block'}
            />
            {error && (
              <Alert type={'error'} showIcon closable message={error} />
            )}
            <TextArea
              className={'custom-scene-text'}
              placeholder={
                '请输入你希望为服装搭配的场景（构图背景 + 人物姿势 + 构图镜头 + 摄影风格）'
              }
              showCount={true}
              maxLength={600}
              style={{ height: 176, resize: 'none' }}
              rows={4}
              allowClear={true}
              value={customScene}
              onChange={handleTextAreaChange}
              onBlur={handleTextAreaBlur}
            />
          </>
        )}

        {/*场景分类tabs*/}
        {(activeTab === '官方预设' || config.configKey !== 'SCENE') && (
          <>
            <div className={'scene-type-row'}>
              {showTypeList.map((s) => (
                <Button
                  key={s.code}
                  className={
                    showType === s.code
                      ? 'scene-type-item scene-type-item-checked'
                      : 'scene-type-item'
                  }
                  onClick={() => setSceneType(s.code)}
                  disabled={s.empty}
                >
                  {s.name}
                </Button>
              ))}

              {/*创建我的模特*/}
              {config.configKey === 'FACE' && (
                <div
                  className={'create-my-face-container'}
                  onClick={() => navigate('/upload?type=face')}
                >
                  <IconFont
                    type={'icon-tianjia'}
                    style={{ fontSize: 16, color: '#1A1B1D' }}
                  />
                  <span className={'create-my-face-title'}>创建我的模特</span>
                </div>
              )}

              {config.configKey === 'SCENE' && (
                <div
                  className={'create-my-face-container'}
                  onClick={() => navigate('/upload?type=scene')}
                >
                  <IconFont
                    type={'icon-tianjia'}
                    style={{ fontSize: 16, color: '#1A1B1D' }}
                  />
                  <span className={'create-my-face-title'}>创建我的场景</span>
                </div>
              )}
            </div>
            <Flex wrap={true} className={'scene-item-block element-item-block'}>
              <>
                {showList.map((item) => (
                  <div
                    key={item.id}
                    style={{ position: 'relative' }}
                    className={
                      'work-item work-item-fixed' +
                      (isMatch(current, item) ? ' work-item-selected' : '')
                    }
                    onClick={() => handleChange(item)}
                  >
                    {/* 上新标签 */}
                    {item.isNew && (
                      <NewLabel width={40} height={16} top={-4} left={-5} />
                    )}

                    <div style={{ position: 'relative' }}>
                      <img
                        alt="img"
                        src={item.showImage}
                        className={
                          config.configKey === 'SCENE'
                            ? 'work-item-image-scene'
                            : 'work-item-image'
                        }
                      />

                      {item.extInfo && item.extInfo['openScope'] && item.extInfo['openScope'] !== 'ALL' &&
                        <MiniTag title={'专属'} position={'rightTop'} textFontSize={10} />
                      }

                      {isLockedElement(loraType, config.configKey, item?.id, experienceModelOpenCfg, isVipUser) &&
                        <ElementLockMask />
                      }

                      <div className={'element-block-item-block-right'}>
                        <UserFavorButton
                          className={'element-block-favor-button'}
                          favorType={'ELEMENT'}
                          itemId={item.id}
                        />
                      </div>
                    </div>
                    <div
                      className={'element-block-item-name text14 font-pf color-n margin-top-4 margin-bottom-4 text-center'}>
                      {item.name}
                    </div>
                  </div>
                ))}
                {showType !== 'exclusive' && (
                  <div
                    className={'work-item work-item-fixed'}
                    onClick={handleShowAllPop}
                  >
                    <div
                      style={{
                        backgroundImage: `url(${moreBg})`,
                        backgroundSize: 'cover',
                      }}
                      className={'work-item-fixed-height work-element-more'}
                    >
                      <div className={'more-lora-block work-element-more-block'} >
                        {moreIcon &&
                          <IconFont
                            type={moreIcon}
                            style={{ fontSize: 32, color: '#FFFFFF' }}
                          />
                        }
                        <div
                          className={
                            'text14 font-pf weight color-w text-center margin-top-4'
                          }
                        >
                          全部{showType === 'favor' ? '收藏' : config.name}
                        </div>
                      </div>
                    </div>
                    <div
                      className={
                        'text14 font-pf color-n margin-top-4 margin-bottom-4 text-center'
                      }
                    >
                      更多
                    </div>
                  </div>
                )}
              </>
            </Flex>
          </>
        )}
        {activeTab === '上传图片' && (
          <UploadImage
            tag={uploadImgProps?.tag}
            image={uploadImgProps?.image}
            onImageChange={uploadImgProps?.onImageChange}
            onShowImageSelector={uploadImgProps?.onShowImageSelector}
            uploadProps={uploadImgProps?.uploadProps}
          />
        )}
      </div>

      {!!foot && foot}

      {showAllPop && (
        <ElementWithTypeSelector
          current={current ? current : []}
          title={config.name}
          orderByType={orderByType}
          config={config}
          onClose={() => {
            setShowAllPop(false);
            setShowAll(false);
          }}
          recentList={recentList}
          onChange={handleSelectChange}
          conditions={conditions}
          acceptTypes={acceptTypes}
          loraType={loraType}
          experienceModelOpenCfg={experienceModelOpenCfg}
          showConfirmFooter={showConfirmFooter}
          bizType={bizType}
          onlyLora={onlyLora}
          isVipUser={isVipUser}
          selectedShowType={
            showAll ? 'all' : showType === 'favor' ? showType : 'all'
          }
        />
      )}

      {showTopupModal && !isTrialAccount && (
        <TopupModal
          visible={showTopupModal}
          onClose={() => setShowTopupModal(false)}
          onPaySuccess={() => setShowTopupModal(false)}
        />
      )}
    </div>
  );
};

export default ElementWithTypeBlock;