import React, { useEffect, useState } from 'react';
import { Button, Flex, Input, message, Modal, Tag, Tooltip } from 'antd';
import { ClothCollocation } from '@/services/MaterialModelController';
import '@/components/Creative/MerchantPreferenceBlock.less';
import {
  addMerchantPreference,
  deleteMerchantPreference,
  queryAllMerchantPreference, updateMerchantPreference,
} from '@/services/CreativeController';
import { deepCopy } from '@/utils/utils';

export type PreferenceType = 'CLOTH_COLLOCATION' | 'CUSTOM_SCENE';

const AllPreferenceType: Array<{key: PreferenceType, tag: string}> = [
  {key: 'CLOTH_COLLOCATION', tag: '搭配'},
  {key: 'CUSTOM_SCENE', tag: '场景'},
]

interface MerchantPreferenceBlockProps {
  type: PreferenceType;
  preference: MerchantPreferenceVO;
  onLabelClick: (item: MerchantPreferenceVO) => void;
  className?: string;
}

export interface MerchantPreferenceVO {
  id?: number;
  type?: PreferenceType;
  memo?: string;
  clothCollocation?: ClothCollocation;
  extInfo?: {
    customSceneDesc: string;
  };
}

const collocationItemsInfo = {
  shoe: '鞋子',
  tops: '上衣',
  bottoms: '裤子',
  others: '其他配饰',
  props: '道具',
};

const MerchantPreferenceBlock: React.FC<MerchantPreferenceBlockProps> = ({type, preference, onLabelClick, className = ''}) => {

  const tag = AllPreferenceType.find(item => item.key === type)?.tag || '搭配';
  const [modalVisible, setModalVisible] = useState(false);
  const [saveModalVisible, setSaveModalVisible] = useState(false);
  const [preferenceMemo, setPreferenceMemo] = useState('');
  const [selectedPreferenceIndex, setSelectedPreferenceIndex] = useState<number | null>(null);
  const [preferences, setPreferences] = useState<Array<MerchantPreferenceVO>>([]);
  const [saveType, setSaveType] = useState<'new' | 'existing' | null>(null);
  const [selectedLabel, setSelectedLabel] = useState<number | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    handlePreferenceChange();
  }, [preference]);

  async function fetchData (){
    const res = await queryAllMerchantPreference(type);
    setPreferences(res || []);
  }

  const handlePreferenceChange = () => {
    setSelectedLabel(null);
  }


  const handlePreferencesSave = () => {
    setSaveModalVisible(true);
  }

  const handleSave= () => {
    if (saveType === 'existing') {
      // 更新现有搭配的逻辑
      if (selectedPreferenceIndex !== null) {
        const payload = {
          id: preferences[selectedPreferenceIndex].id,
          type: type,
          memo: preferences[selectedPreferenceIndex].memo,
          clothCollocation: JSON.stringify(preference.clothCollocation),
          extInfo: {customSceneDesc: preference.extInfo?.customSceneDesc}
        };
        updateMerchantPreference(payload).then(res => {
          if (res) {
            setPreferences(res);
            message.success('更新成功');
          } else {
            message.warning('更新失败');
          }
        });
      }
      // 清空状态
      setSelectedPreferenceIndex(null);
      setSaveModalVisible(false);
      setSaveType(null);
      setModalVisible(false);
    } else {
      // 原有的新建搭配逻辑
      addPreference();
    }
  };

  const addPreference = () => {
    if (!preferenceMemo.trim()) {
      message.warning('请输入搭配名称');
      return;
    }

    if (isPreferenceValid(preference)) {
      const payload = {
        ...preference,
        clothCollocation: JSON.stringify(preference.clothCollocation),
        type: type,
        memo: preferenceMemo.trim(),
      };

      addMerchantPreference(payload).then(res => {
        if (res) {
          setPreferences(res);
          setModalVisible(false);
          setPreferenceMemo('');
          message.success('保存成功');
        }
      });
    }
  }

  const isPreferenceValid = (preference: MerchantPreferenceVO) => {
    if (!preference) 
      return false;
    switch (type) {
      case 'CLOTH_COLLOCATION':
        return !!( preference.clothCollocation && Object.keys(preference.clothCollocation).length > 0 );
      case 'CUSTOM_SCENE':
        return !!( preference.extInfo && preference.extInfo.customSceneDesc && preference.extInfo.customSceneDesc.trim().length > 20);
      default:
        return false;
    }
  }

  const handleSaveTypeSelect = (type: 'new' | 'existing') => {
    setSaveType(type);
    if (type === 'new') {
      setModalVisible(true);
      setSaveModalVisible(false);
    }
  }

  const handleLabelSelect = (item: MerchantPreferenceVO, index: number) => {
    onLabelClick(deepCopy(item));
    setTimeout(() => {
      setSelectedLabel(index);
      }, 50)
  }

  const onLabelClose = (id) => {
    const payload = {
      id: id,
      type: type,
    }
    deleteMerchantPreference(payload).then(res => {
      if (res) {
        setPreferences(res);
        message.success('删除成功');
      }
    })
  }

  return (
    <Flex
      align="center"
      className={'preference-block-container' + ' ' + className}
    >
      <div
        className="text14 font-medium color-33 preference-block-title"
        style={{ marginRight: '12px' }}
      >
        {`常用${tag}`}
      </div>
      <Flex wrap="wrap" flex={1} gap={8}>
        {Array.isArray(preferences) &&
          preferences.map((item, index) => (
            <div
              className={
                'preference-block-label' +
                (selectedLabel === index ? '-selected' : '')
              }
              key={index}
              onClick={() => {
                console.log('item', item);
                handleLabelSelect(item, index);
              }}
            >
              <span
                className={
                  'text12 color-33 pointer preference-block-label-item' +
                  (selectedLabel === index ? ' light-text' : '')
                }
              >
                {item.memo}
              </span>
              <span
                className="preference-block-label-delete"
                onClick={(e) => {
                  e.stopPropagation();
                  Modal.confirm({
                    centered: true,
                    title: '确认删除',
                    content: `确定要删除这个${tag}吗？`,
                    onOk: () => onLabelClose(item.id),
                    okText: '确认',
                    cancelText: '取消',
                  });
                }}
              >
                ✖
              </span>
            </div>
          ))}
        {Array.isArray(preferences) && preferences.length < 5 && (
          <Tooltip title={`保存${tag}为常用${tag}`}>
            <Button
              type="primary"
              size="small"
              className={'preference-block-save-button'}
              onClick={handlePreferencesSave}
              disabled={!isPreferenceValid(preference)}
            >
              {`保存${tag}`}
            </Button>
          </Tooltip>
        )}
        {/* 选择保存类型的弹窗 */}
        <Modal
          title={`保存${tag}`}
          open={saveModalVisible}
          centered
          footer={null}
          onCancel={() => {
            setSaveModalVisible(false);
            setSaveType(null);
            setSelectedPreferenceIndex(null);
          }}
        >
          <Flex>
            {/* 显示 collocation 信息 */}
            <div className="preference-block-info">
              {type === 'CLOTH_COLLOCATION' ? (
                <ul>
                  {Object.entries(preference.clothCollocation || {}).map(
                    ([key, value]) =>
                      value && (
                        <li key={key}>
                          <strong>{collocationItemsInfo[key]}:</strong> {value}
                        </li>
                      ),
                  )}
                </ul>
              ) : (
                <div>
                  <strong>自定义场景: </strong>{' '}
                  {preference.extInfo?.customSceneDesc}
                </div>
              )}
            </div>
          </Flex>
          <Flex vertical gap={16} style={{ padding: '16px 0' }}>
            <Button
              type="primary"
              block
              onClick={() => handleSaveTypeSelect('new')}
            >
              {`保存为新${tag}`}
            </Button>
            {preferences.length > 0 && (
              <>
                <Button
                  type="default"
                  block
                  onClick={() => handleSaveTypeSelect('existing')}
                >
                  {`更新已有${tag}`}
                </Button>
                {saveType === 'existing' && (
                  <Flex vertical gap={8}>
                    <div className="text14 color-72">
                      {`选择要更新的${tag}：`}
                    </div>
                    <Flex gap={8} wrap="wrap">
                      {preferences.map((item, index) => (
                        <div
                          className={ 'preference-block-label' + (selectedPreferenceIndex === index ? '-selected' : '') }
                          key={index} onClick={() => { setSelectedPreferenceIndex(index); }}
                        >
                          <span
                            className={
                              'text12 color-33 pointer preference-block-label-item' +
                              (selectedPreferenceIndex === index ? ' light-text' : '')
                            }
                          >
                            {item.memo}
                          </span>
                        </div>
                      ))}
                    </Flex>
                    <Button
                      type="primary"
                      disabled={selectedPreferenceIndex === null}
                      onClick={handleSave}
                    >
                      确认更新
                    </Button>
                  </Flex>
                )}
              </>
            )}
          </Flex>
        </Modal>

        <Modal
          title={`保存新${tag}`}
          open={modalVisible}
          centered
          onOk={addPreference}
          okText="保存"
          cancelText="取消"
          onCancel={() => {
            setPreferenceMemo('');
            setModalVisible(false);
          }}
        >
          <Flex vertical gap={8}>
            <div className="text14 color-72">{'起个名字吧'}</div>
            <Input
              placeholder={`请输入${tag}名称`}
              value={preferenceMemo}
              maxLength={8}
              showCount
              onChange={(e) => setPreferenceMemo(e.target.value)}
            />
          </Flex>
        </Modal>
      </Flex>
    </Flex>
  );
}

export default MerchantPreferenceBlock;