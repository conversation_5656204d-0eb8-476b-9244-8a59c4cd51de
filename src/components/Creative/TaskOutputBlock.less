@import "@/app";
@import "@/pages/Creation/index";

@task-bar-height: calc(100vh - @navigation-height);
@task-bar-width: 110px;
@task-image-bar-width: calc(100vw - @work-block-width - @task-bar-width - @scroll-width);
@task-image-card-width: calc(@task-image-bar-width / 2 - 16px * 2 - 16px * 2);

@task-image-bar-width-video: calc((100vw - @menu-width) / 2 - @task-bar-width);
@task-image-card-width-video: calc(@task-image-bar-width-video / 2 - 16px * 2 - 16px * 2);


.task-bar {
  width: @task-bar-width;
  height: @task-bar-height;
  overflow-y: auto;
  gap: 16px;

  padding: 16px 0;
  align-self: stretch;

  box-sizing: border-box;
  /* 中性色/N3-边框、背板 */
  border-width: 0 0 0 1px;
  border-style: solid;
  border-color: #E1E3EB;
}

.task-image-bar {
  width: calc(100% - @task-bar-width);
  height: calc(100vh - @navigation-height);
  border-right: 1px solid #E1E3EB;
  overflow-y: auto;
}

.task-image-bar-look {
  background: radial-gradient(135% 98% at 100% 100%, rgba(234, 213, 255, 0.6) 0%, rgba(212, 211, 255, 0) 100%), radial-gradient(88% 88% at 111% 34%, rgba(232, 197, 255, 0.6) 0%, rgba(212, 211, 255, 0) 100%), radial-gradient(90% 96% at 0% 100%, rgba(255, 215, 167, 0.29) 0%, rgba(255, 255, 255, 0.05) 95%), radial-gradient(92% 92% at 0% 0%, rgba(255, 233, 183, 0.34) 0%, rgba(212, 211, 255, 0.2) 100%);
}

.task-image-bar-real-scene-shooting {
  background: radial-gradient(122% 58% at 99% 107%, rgba(211, 255, 243, 0.6) 0%, rgba(212, 211, 255, 0) 100%), radial-gradient(133% 133% at 100% 0%, rgba(186, 240, 206, 0.6) 0%, rgba(211, 255, 221, 0) 100%), radial-gradient(92% 92% at 0% 0%, rgba(148, 255, 225, 0.34) 0%, rgba(212, 211, 255, 0) 100%);
}

.task-image-bar-model-show {
  background: radial-gradient(122% 58% at 99% 107%, rgba(211, 244, 255, 0.6) 0%, rgba(212, 211, 255, 0) 100%), radial-gradient(133% 133% at 100% 0%, rgba(186, 228, 240, 0.6) 0%, rgba(211, 254, 255, 0) 100%), radial-gradient(92% 92% at 0% 0%, rgba(148, 232, 255, 0.34) 0%, rgba(212, 211, 255, 0) 100%);
}

.task-card-container {
  position: relative;
  margin-left: 6px;
  width: 88px;
  height: 88px;
  border-radius: 8px;
  flex-shrink: 0;
}

.task-card-container-selected {
  background: #DDD9FF;
  box-sizing: border-box;
  border: 1px solid #000BD9;
}

.task-card {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  padding: 4px;
}

.task-card-desc {
  width: 72px;
  height: 22px;
  border-radius: 4.32px;
  padding: 2px;
  background: rgba(0, 0, 0, 0.6);
}

.task-card-arrow {
  position: absolute;
  top: calc(50% - 6px);
  left: -12px;

  font-size: 12px;
  color: #000BD9;
}

.task-image-card {
  width: @task-image-card-width-video !important;
}

.task-image-mini {
  position: relative;
  width: calc(@task-image-card-width-video * 78 / 246);
  max-width: 127px;
  height: auto;
  min-height: 100px;

  border-radius: 8px;
  overflow: hidden;
}

.task-image-mini-square {
  height: calc(@task-image-card-width-video * 78 / 246);
}

.task-image-video {
  position: relative;
  width: calc(@task-image-card-width-video * 184 / 246);
  height: @task-image-card-width-video;
  max-height: 376px;
  background: #F0F1F4;

  border-radius: 8px;
  overflow: hidden;
  img {
    object-fit: contain;
  }
}

.task-image-video-upload {
  //width: calc(@task-image-card-width-video * 184 / 246) !important;
  width: 100%;
}

.task-bar-no-status {
  height: calc(100vh - @navigation-height) !important;
}

.task-slider-bar {
  left: 0;
  width: calc(100% - @task-bar-width);
}

/* 固定姿势创作特殊样式 */
.fixed-posture-image-block {
  position: relative;
  width: 100%;
  height: auto;
}

.fixed-posture-tag {
  position: absolute;
  left: 4px;
  top: 4px;
  height: 22px;
  width: auto;
  min-width: 22px;
  border-radius: 4px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 2px 6px;
  background: rgba(0, 0, 0, 0.6);
  z-index: 4;
}

.fixed-posture-image {
  width: 100%;
  height: auto;
  min-height: 180px;
  max-height: 450px;
  object-fit: cover;
  cursor: pointer;
  border-radius: 8px;
}