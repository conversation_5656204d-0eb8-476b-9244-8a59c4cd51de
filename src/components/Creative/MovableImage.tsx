import React, { useEffect, useRef } from 'react';
import useImage from 'use-image';
import { Group, Image as KonvaImage, Transformer } from 'react-konva';

interface MovableImageProps {
  image: any;
  isSelected: boolean;
  maskImage?: { src: string, width: number, height: number };
  onChange: (e: any) => void;
}

const MovableImage: React.FC<MovableImageProps> = ({ image, isSelected, maskImage, onChange }) => {
  const shapeRef = useRef(null);
  const trRef = useRef(null);
  const [img] = useImage(image.src);
  const [mask] = maskImage ? useImage(maskImage.src) : [null];

  useEffect(() => {
    if (isSelected) {
      // @ts-ignore
      trRef.current.nodes([shapeRef.current]);
      // @ts-ignore
      trRef.current.getLayer().batchDraw();
    }
  }, [isSelected]);
  return (
    <>
      <Group>
        <KonvaImage
          image={img}
          x={image.x}
          y={image.y}
          width={image.width}
          height={image.height}
          draggable
          ref={shapeRef}
          onDragEnd={(e) => {
            onChange({
              ...image,
              x: e.target.x(),
              y: e.target.y(),
            });
          }}
          onTransformEnd={(e) => {
            const node = shapeRef.current;
            const scaleX = node.scaleX();
            const scaleY = node.scaleY();
            node.scaleX(1);
            node.scaleY(1);
            onChange({
              ...image,
              x: node.x(),
              y: node.y(),
              rotation: node.rotation(),
              width: node.width() * scaleX,
              height: node.height() * scaleY,
            });
          }}
        />
        {maskImage && mask &&
          <KonvaImage image={mask} width={maskImage.width} height={maskImage.height} opacity={1}
                      listening={false} cache={true} cacheHint={{ diameter: 0 }}
          />
        }
        {isSelected &&
          <Transformer ref={trRef} cornerColor={'#618DFF'} borderStroke={'#618DFF'} anchorCornerRadius={8}
                       rotateAnchorCursor={'grab'} />
        }
      </Group>
    </>
  );
};

export default MovableImage;