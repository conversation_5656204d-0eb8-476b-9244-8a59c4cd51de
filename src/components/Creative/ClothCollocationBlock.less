.cloth-collocation-container {
  background: #E1F0FF;
}

.cloth-collocation-container-expend {
  background: none;
}

.cloth-collocation-container:hover {

  svg {
    color: #2D7DFF;
  }
}

.cloth-collocation-container-expend:hover {

  svg {
    color: #2D7DFF;
  }
}

.cloth-collocation-title {
  border-radius: 8px;
}

.cloth-collocation-preset-item {
  height: 26px;
  border-radius: 32px;
  opacity: 1;

  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 4px 16px;

  box-sizing: border-box;
  border: 1px solid #B5C7FF;
}

.cloth-collocation-preset-item:hover {
  border: 1px solid #618DFF;
  box-shadow: 4px 4px 10px 0 rgba(0, 0, 0, 0.1);
}

.light-text {
  background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  z-index: 0;
  /* 在这里输入你希望为服装搭配的一款 */
  color: #969799
}

.cloth-collocation-preset-item-selected {
  background: #D9E1FF;
  height: 26px;
  border-radius: 32px;
  opacity: 1;

  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 4px 16px;

  box-sizing: border-box;
  border: 1px solid #B5C7FF;
}

.cloth-collocation-label {
  height: 26px;
  border-radius: 32px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 4px 16px;
  box-sizing: border-box;
  border: 1px solid #B5C7FF;
}

.cloth-collocation-label:hover {
  border: 1px solid #618DFF;
  box-shadow: 4px 4px 10px 0 rgba(0, 0, 0, 0.1);
}

.cloth-collocation-label-selected {
  background: #D9E1FF;
  height: 26px;
  border-radius: 32px;
  opacity: 1;

  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 4px 16px;

  box-sizing: border-box;
  border: 1px solid #B5C7FF;
}

.cloth-collocation-label-item {
  margin-right: 4px;
}

.cloth-collocation-label-delete {
  display: flex;
  margin-left: 4px;
  align-items: center;
  cursor: pointer;
  color: #dbe5ff;
  font-size: 8px;
  line-height: 1;
  transition: color 0.3s ease
}

.cloth-collocation-label-delete:hover {
  color: #5a76d6;
}


.cloth-collocation-save-button {
  border-radius: 32px;
  height: 26px;
  padding: 0 16px;
  font-size: 12px
}

.ant-tag {
  margin-right: 0;

  &.ant-tag-blue {
    background-color: #e6f4ff;
    border-color: #91caff;
  }
}

.collocation-info {
  padding: 16px; // 内边距

  ul {
    list-style-type: none; // 去掉列表样式
    padding: 0; // 去掉内边距
    margin: 0; // 去掉外边距

    li {
      margin-bottom: 8px; // 列表项底部外边距
      font-size: 14px; // 列表项字体大小
      color: #555; // 列表项颜色

      strong {
        color: #333; // 加粗文本颜色
      }
    }
  }
}