import { <PERSON><PERSON>, Button, Flex, Modal, notification, Slider, Space, Tooltip } from 'antd';
import IconFont from '@/components/IconFont';
import { ExclamationCircleOutlined, QuestionCircleOutlined, ZoomInOutlined } from '@ant-design/icons';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import {
  batchQueryCreative, BizType,
  cancelCreativeQueue,
  clearCreative, CreateGuideBlockMap,
  CreativeType,
  CreativeVO,
  deleteCreative,
  downloadAllImages,
  getCreativeTypeName,
  isExampleImage,
  isProcessing,
  LoraType,
  queryActiveAndTodayCreative,
  queryCreativeById,
} from '@/services/CreativeController';
import { FILLED_IMAGE_MAP, GIFT_ICON, IS_TRIAL_ACCOUNT, isFilledImage, QUEUE_ICON } from '@/constants';
import MultiStepProgress from '@/components/MultiStepProgress';
import { useNavigate } from 'react-router-dom';
import ImgPreview, { ImgPreviewInfo } from '@/components/ImgPreview';
import { deepCopy, download, timeoutGradient } from '@/utils/utils';
import { useLocation } from '@@/exports';
import WatermarkImage from '@/components/WatermarkImage';
import './TaskOutputBlock.less';
import { calculateTimeDifference } from '@/utils/dateUtils';
import VideoCard from '@/components/Creative/VideoCard';

export interface TaskOutputBlockProps {
  types: Array<CreativeType>;
  loraType?: LoraType;
  sliderValue?: number;
  changeSliderValue?: (value: number) => void;
  pollingTimeout?: number;
  isSelectTask?: boolean;
  bizType?: BizType | null | undefined;
  bgClassName?: string;
  bgImg?: string;
}

export interface TaskOutputBlockRef {
  refresh: () => void;
  mock: (CreativeVO) => void;
}

interface ScheduleData {
  steps: Array<number>;
  subStep: number;
  finished: number;
  next?: number;
}

interface ImageCardProps {
  tag?: any;
  src: string;
  item: CreativeVO;
  ext?: any;
}

const buildSchedule = (size, finished, subSchedule) => {
  let array = new Array(size).fill(0);
  for (let i = 0; i < finished; i++) {
    array[i] = 100;
  }

  if (finished < size) {
    array[finished] = subSchedule;
  }
  return { steps: array, subStep: subSchedule, finished } as ScheduleData;
};

const getFilledImage = (item: CreativeVO) => {
  return FILLED_IMAGE_MAP[item.imageProportion][item.status];
};


const TaskOutputBlock = forwardRef<TaskOutputBlockRef, TaskOutputBlockProps>(({
                                                                                types,
                                                                                loraType = null,
                                                                                sliderValue,
                                                                                changeSliderValue,
                                                                                pollingTimeout = 3000,
                                                                                isSelectTask = false,
                                                                                bizType = 'NORMAL',
                                                                                bgClassName,
                                                                                bgImg,
                                                                              }, ref) => {
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const [todayList, setTodayList] = useState<Array<CreativeVO>>([]);
    const [schedule, setSchedule] = useState<ScheduleData | null>(null);
    const [queueSize, setQueueSize] = useState<number | null>(null);

    const [detailImage, setDetailImage] = useState('');
    const [opItem, setOpItem] = useState<CreativeVO | null>(null);
    const [detailImageList, setDetailImageList] = useState<Array<string>>([]);
    const [previewInfoList, setPreviewInfoList] = useState<ImgPreviewInfo[]>([]);
    const [likeObj, setLikeObj] = useState<any | null>({});
    const [deleteId, setDeleteId] = useState<number | null>(null);
    const [showId, setShowId] = useState<number>(0);

    const navigate = useNavigate();
    const location = useLocation();

    const isTrialAccount = sessionStorage.getItem(IS_TRIAL_ACCOUNT) === 'Y';
    const currentItem = todayList.find(e => e.id === showId);
    const GuideBlock = types.length <= 1 ? CreateGuideBlockMap[types[0]] : null;

    async function fetchData() {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      setTodayList([]);

      const res = await queryActiveAndTodayCreative(types, loraType, bizType, isSelectTask);
      if (res) {
        //设置
        const todayList = res.todayList;
        if (todayList && todayList.length > 0) {
          setTodayList(todayList);
          setShowId(todayList[0].id);

          if (todayList.some(e => isProcessing(e))) {
            //开始轮询状态
            pollingStatus(todayList);
          }
        }
      }
    }

    const refreshData = (id: number) => {
      queryCreativeById(id).then((res) => {
        if (!res || !todayList) {
          return;
        }
        const copy = deepCopy(todayList);
        let index = copy.findIndex((item: { id: number; }) => item.id === id);

        if (index !== -1) {
          copy[index] = res;
          setTodayList(copy);
        }
      });
    };

    const timeoutRef = useRef();
    const scheduleRef = useRef<ScheduleData>(buildSchedule(currentItem ? currentItem.batchCnt : 1, 0, 99));

    // 处理进度数据
    function processSchedule(result: CreativeVO) {
      console.log('processSchedule', showId);
      if (result.id !== showId) {
        return;
      }

      const extInfo = result.extInfo ? result.extInfo : {};
      //排队中状态，只设置队列长度
      if (result.status === 'QUEUE') {
        setQueueSize(extInfo['queueSize']);
        return;
      }

      //如果是处理中状态，则更新处理中的进度信息
      const schedule = extInfo['schedule'];

      //如果没有进度信息时，新生成一个进度为0的信息
      if (schedule === null) {
        const scheduleData = buildSchedule(result.batchCnt, 0, 0);
        setSchedule(scheduleData);
        scheduleRef.current = scheduleData;
        return;
      }

      //有进度信息的则构造进度条数据
      let { finished, subSchedule } = schedule || { finished: 0, subSchedule: 0 };
      //处理缓存的节点的进度，mock成从0开始
      let nextStep = false;
      if (scheduleRef.current && (scheduleRef.current?.subStep > subSchedule) || scheduleRef.current?.subStep === 0) {
        nextStep = true;
      }
      const scheduleData = buildSchedule(result.batchCnt, finished, nextStep ? 0 : subSchedule);
      scheduleData.next = subSchedule;
      setSchedule(scheduleData);
      scheduleRef.current = scheduleData;

      //如果主进度条没有完成，这直接通过scheduleData展示即可
      if (!nextStep) {
        console.log('timeoutGradient.call.subStep !nextStep', scheduleData.subStep);
        return;
      }

      //如果主进度条有一个完成了，则模拟小进度条从0开始
      timeoutGradient(0, Math.floor(subSchedule / 10), 2500, {
        call: function(idx: number) {
          let scheduleData = scheduleRef.current;
          if (scheduleData) {
            const next = scheduleData.next;
            scheduleData = buildSchedule(scheduleData.steps.length, scheduleData.finished, idx * 10);
            scheduleData.next = next;
            setSchedule(scheduleData);
            scheduleRef.current = scheduleData;
          }
        },
      });
    }

    // 处理图片数据
    function processImages(todayList: Array<CreativeVO>, result: CreativeVO) {

      setTodayList(prevData => {
        const index = prevData.findIndex((item: { id: number; }) => item.id === result.id);
        const origin = index !== -1 ? prevData[index] : null;
        const originImages = origin?.resultImages ? origin?.resultImages : [];

        if (result.status === 'FAILED') {
          result.resultImages = [];
        } else {
          if (!result.resultImages) {
            result.resultImages = [];
          }

          const batchCnt = result.type === 'REPAIR_HANDS' ? 4 :
            result.type === 'REPAIR_DETAIL' ? result.batchCnt * 2 : result.batchCnt;
          if (originImages.length >= result.resultImages.length) {
            console.log('已存在图片，且图片数量未变更，不更新', originImages.length, result.resultImages.length);
            return prevData;
          }

          console.log('已存在图片，且图片数量发生变更，执行更新', originImages.length, result.resultImages.length, batchCnt);

          // console.log('判断是否需要刷新预览图片，', !!detailImage, detailImageList.length, result.resultImages.length);
          // if (detailImage && result.resultImages.length > detailImageList.length) {
          //   console.log('刷新detailImageList', detailImageList.length, result.resultImages.length);
          //   setDetailImageList([...result.resultImages]);
          // }
        }

        const copy = deepCopy(prevData);
        if (index !== -1) {
          copy[index] = result;
        } else {
          copy.unshift(result);
        }

        return copy;
      });
    }

    const syncStatus = (queryResult: void | Array<CreativeVO>, todayList: Array<CreativeVO>) => {
      if (!queryResult || queryResult.length <= 0 || !Array.isArray(queryResult)) {
        return todayList;
      }
      const updatedList = todayList.map(listItem => {
        const updateItem = queryResult.find(item => item.id === listItem.id);
        if (!updateItem) {
          return listItem;
        }

        // 合并数据，保留原有的重要信息
        const mergedItem = {
          ...listItem,
          ...updateItem,
          creativeTasksList: updateItem.creativeTasksList || listItem.creativeTasksList,
        };


        const newStatus = mergedItem.status;
        const extInfo = mergedItem.extInfo;

        //队列和处理中，更新进度状态
        if (extInfo && (newStatus === 'PROCESSING' || newStatus === 'QUEUE')) {
          processSchedule(mergedItem);
        }

        if (newStatus !== 'INIT') {
          processImages(todayList, mergedItem);
        }

        return mergedItem;
      });

      return updatedList;
    };

    const pollingStatus = (todayList) => {
      //每次调度前先清除定时器
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      async function polling(todayList) {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }

        let result;
        try {
          const processingIds = todayList.filter(e => isProcessing(e)).map(e => e.id);
          if (processingIds.length === 0) {
            return;
          }
          result = await batchQueryCreative(processingIds, isSelectTask);
        } catch (e) {
          console.log('polling status error=', e);
          return;
        }

        if (result) {
          const updatedList = syncStatus(result, todayList);
          setTodayList(updatedList);

          // @ts-ignore
          if (updatedList.some(e => isProcessing(e))) {
            // @ts-ignore
            timeoutRef.current = setTimeout(() => polling(updatedList), pollingTimeout); // 3秒后再次轮询
            return () => clearTimeout(timeoutRef.current);
          }
        }
      }

      // @ts-ignore
      timeoutRef.current = setTimeout(() => polling(todayList), 300); // 1秒后再次轮询
      return () => clearTimeout(timeoutRef.current);
    };

    const getShowResultImages = (item: CreativeVO) => {
      let resultImages = item.resultImages;
      if (!resultImages) {
        resultImages = [];
      }

      if (item.status === 'FINISHED') {
        return resultImages;
      }

      const batchCnt = item.type === 'REPAIR_HANDS' ? 4 :
        item.type === 'REPAIR_DETAIL' ? item.batchCnt * 2 :
          item.batchCnt;

      if (resultImages.length < batchCnt) {
        const loadingImg = getFilledImage(item);
        resultImages = [...resultImages, ...Array(batchCnt - resultImages.length).fill(loadingImg)];
      } else {
        resultImages = [...resultImages];
      }

      return resultImages;
    };

    const handleShowImage = (value: string, item: CreativeVO) => {
      if (isFilledImage(value)) {
        return;
      }
      setDetailImage(value);
      setOpItem(item);
      let imageList = item.resultImages ? item.resultImages : [];
      if (item.extInfo['originCustomSceneImg']) {
        imageList = [item.extInfo['originCustomSceneImg'], ...imageList];
      }
      if (item.extInfo['originImage']) {
        imageList = [item.extInfo['originImage'], ...imageList];
      }
      if (item.extInfo['clothOriginImage']) {
        imageList = [item.extInfo['clothOriginImage'], ...imageList];
      }
      if (item.extInfo['logoImageOss']) {
        imageList = [item.extInfo['logoImageOss'], ...imageList];
      }
      if (item.extInfo['referenceOriginalImage']) {
        imageList = [item.extInfo['referenceOriginalImage'], ...imageList];
      }

      // 展示收藏信息所需的列表
      let previewInfoList: ImgPreviewInfo[] = [];

      // 特殊类型创作, 添加参考图
      if (['FIXED_POSTURE_CREATION', 'BASIC_CHANGING_CLOTHES', 'CLOTHING_SWAP'].includes(item.type)) {
        imageList = [];  // 清空之前的列表
        item.creativeTasksList.forEach(task => {
          // 添加参考图
          if (task.extInfo.referenceOriginalImage) {
            imageList.push(task.extInfo.referenceOriginalImage);
            previewInfoList.push({ batchId: -1, indexInBatch: -1 });
          }
          // 如果任务已完成且有结果图，则添加结果图
          if (task.status === 'FINISHED' && task.resultImages && task.resultImages.length > 0) {
            task.resultImages.forEach(image => {
              imageList.push(image);
              let batchIndex = item.resultImages?.findIndex(img => img === image);
              previewInfoList.push({ batchId: item.id, indexInBatch: batchIndex === undefined ? -1 : batchIndex });
            });
          }
        });
      } else {
        // 展示收藏信息所需的列表
        previewInfoList = item.resultImages?.map((_, index) => ({
          batchId: item.id,
          indexInBatch: index,
        })) || [];
        // 填充不支持收藏的图片(原图等)
        for (let i = 0; i < imageList.length - (item.resultImages?.length || 0); i++) {
          previewInfoList = [
            {
              batchId: -1,
              indexInBatch: -1,
            },
            ...previewInfoList];
        }
      }

      // @ts-ignore
      setDetailImageList(imageList);


      setPreviewInfoList(previewInfoList);

      if (item.extInfo) {
        setLikeObj({ like: item.extInfo['like'], id: item.id });
      } else {
        setLikeObj(null);
      }
    };

    const gotoRepair = (taskId: number) => {
      navigate('/repair-hands?id=' + taskId, { state: { from: location.pathname } });
    };

    const handleCancelQueue = () => {
      if (!currentItem || currentItem.status !== 'QUEUE') {
        return;
      }

      cancelCreativeQueue(currentItem.id).then((res) => {
        if (res) {
          notification.success({ message: '生成图片取消成功' });
          fetchData();
        }
      });
    };

    const handleClear = () => {
      clearCreative(types, loraType).then((res) => {
        if (res) {
          setTodayList([]);
          fetchData();
        }
      });
    };

    const handleDelete = () => {
      deleteCreative(deleteId).then((res) => {
        if (res) {
          notification.success({ message: '删除成功' });
          setDeleteId(null);
          fetchData();
        }
      });
    };

    let downloading = false;
    const handleDownloadAll = () => {
      if (!currentItem || downloading) return;
      downloading = true;
      downloadAllImages(currentItem.id).then(res => {
        downloading = false;
        if (res) {
          download(res);
        }
      });
    };

    const handleHideMask = () => {
      setDetailImage('');
      setDetailImageList([]);
      setPreviewInfoList([]);
    };

    useImperativeHandle(ref, () => ({
      refresh: () => fetchData(),
      mock: (e: CreativeVO) => {
        const store = [e, ...todayList];
        setTodayList(store);
        setShowId(e.id);

        sessionStorage.setItem(`mockVideos_${types.join('_')}_${userInfo?.id}`, JSON.stringify(store));

        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      },
    }));

    useEffect(() => {
      fetchData();
      const store = sessionStorage.getItem(`mockVideos_${types.join('_')}_${userInfo?.id}`);
      if (store && Array.isArray(JSON.parse(store))) {
        setTodayList(JSON.parse(store));
        setShowId(JSON.parse(store)[0].id);
      }
      return () => {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      };
    }, []);

    useEffect(() => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      if (todayList.some(e => isProcessing(e))) {
        //开始轮询状态
        pollingStatus(todayList);
      }
    }, [showId]);

    useEffect(() => {
      fetchData();
    }, [bizType]);


    // 图片卡片组件
    const ImageCard: React.FC<ImageCardProps> = ({ tag, src, item, ext }) => {
      return <>
        <div className={'image-block'}>
          {tag &&
            <div className={'repair-hands-tag'}>{tag}</div>
          }
          <WatermarkImage src={src} className={'output-image'} loading={'lazy'}
                          onClick={() => handleShowImage(src, item)} />
          {ext}
        </div>
      </>;
    };

    // 是否需要显示状态栏
    const needShowStatusBar = () => {
      return !types.includes('CREATE_VIDEO');
    };

    // 手部修复失败组件
    const RepairHandsFailed = (item: CreativeVO) => {
      return <>
        <Flex align={'center'} justify={'center'} className={'repair-delete-icon'}
              onClick={() => setDeleteId(item.id)}>
          <IconFont type={'icon-icon_shanchu'} style={{ fontSize: 20, color: '#fff' }} />
        </Flex>
        <Flex vertical gap={4} className={'text12 font-pf color-w repair-failed-warning'}
              align={'center'} justify={'center'}
              onClick={() => handleShowImage(item.extInfo['originImage'], item)}>
          <ExclamationCircleOutlined style={{ fontSize: 16, color: '#fff' }} />
          未识别到手部
        </Flex>
        <Button type={'primary'} className={'repair-retry-btn'}
                onClick={() => gotoRepair(item.extInfo['originTask'])}>重新涂抹</Button>
      </>;
    };

    // 渲染组件
    return <Flex align={'flex-start'} justify={'space-between'} style={{ position: 'relative' }}
                 className={bgClassName ? bgClassName : undefined}>
      {bgImg &&
        <div style={{ position: 'absolute', bottom: 0, right: 68 }}>
          <img src={bgImg} width={330} alt={'bgImg'} />
        </div>
      }

      <Flex align={'flex-start'} justify={'center'}
            className={'task-image-bar width-100'}>
        {/*图片展示区域*/}
        {currentItem &&
          <>
            <div className={'image-container' + (needShowStatusBar() ? '' : ' task-bar-no-status')}
                 style={{ padding: '0 8px' }}>

              {/* 图片展示区域 */}
              <div key={currentItem.id} style={{ gap: 8 }}
                   className={'center-block width-100 margin-top-16' + (isExampleImage(currentItem) ? ' example-images-block' : ' normal-image-block')}>

                {/* 标题 */}
                {isExampleImage(currentItem) &&
                  <Flex gap={4} align={'center'} className={'example-images-title'}>
                    <img src={GIFT_ICON} alt={''} width={18} height={18} />
                    <div className={'text12 color-1a weight'}>MuseGate精选图</div>
                  </Flex>
                }

                {/* 标题 */}
                {currentItem.title &&
                  <Flex className={'width-100'} justify={'space-between'} align={'center'} gap={8}>
                    <Flex gap={12} justify={'flex-start'} align={'center'}>
                      <div className={'text14 color-72 weight'}>标题：{currentItem.title}</div>
                    </Flex>
                  </Flex>
                }

                {/* 图片展示区域 */}
                <div style={{ overflow: 'auto', gap: (currentItem.type === 'CREATE_VIDEO' ? 16 : 8) }}
                     className={'image-gallery width-100'}>

                  {currentItem.type !== 'CREATE_VIDEO' &&
                    <>
                      {currentItem.status === 'FAILED' &&
                        <Flex className={'width-100'} justify={'space-between'} align={'center'} gap={8}>
                          <Alert type={'error'} showIcon message={
                            <Flex justify={'space-between'} align={'center'}>
                              <div className={'text14 font-pf color-error'}>生成失败</div>
                              <Tooltip title={'删除该记录'}>
                                <IconFont type={'icon-icon_shanchu'} style={{ fontSize: 20 }} className={'color-96'}
                                          onClick={() => setDeleteId(currentItem.id)} />
                              </Tooltip>
                            </Flex>
                          } className={'width-100'} />
                        </Flex>
                      }
                      <Flex className={'width-100'} justify={'space-between'} align={'center'} gap={8}>
                        <Flex gap={12} justify={'flex-start'} align={'center'}>
                          <div
                            className={'creative-type text14 font-pf color-72'}>{getCreativeTypeName(currentItem.type, bizType)}</div>
                          {(currentItem.type === 'CREATE_IMAGE' || currentItem.type === 'FIXED_POSTURE_CREATION') &&
                            <Flex gap={4} justify={'flex-start'} align={'center'}
                                  className={'text14 font-pf color-72'}>

                              <Tooltip title={'服装名称'}>
                                <div>{currentItem.modelName}</div>
                              </Tooltip>

                              <>
                                <div>/</div>
                                <div>ID:{currentItem.id}</div>
                              </>

                            </Flex>
                          }
                          {(currentItem.type === 'FACE_SCENE_SWITCH' ||
                              currentItem.type === 'REMOVE_WRINKLE' ||
                              currentItem.type === 'BASIC_CHANGING_CLOTHES' ||
                              currentItem.type === 'REPAIR_DETAIL' ) &&
                            <Flex gap={4} justify={'flex-start'} align={'center'}
                                  className={'text14 font-pf color-72'}>
                              <div>ID:{currentItem.id}</div>
                            </Flex>
                          }
                        </Flex>
                        <div className={'text14 font-pf color-72'}
                             style={{ padding: 4 }}>{currentItem.createTime}</div>
                      </Flex>

                      <Flex className={'width-100'} justify={'space-between'} align={'center'} gap={8}>
                        <Flex gap={12} justify={'flex-start'} align={'center'} className={'width-100'}>
                          {(!['FACE_SCENE_SWITCH', 'REMOVE_WRINKLE', 'REPAIR_DETAIL', 'IMAGE_UPSCALE', 'CLOTH_RECOLOR'].includes(currentItem.type) || currentItem.faceName) ?
                            <div className={'creative-type text14 font-pf color-72'}>模特图参数</div>
                            : <div></div>}
                          {['CREATE_IMAGE', 'FACE_SCENE_SWITCH', 'PARTIAL_REDRAW', 'BASIC_CHANGING_CLOTHES', 'FIXED_POSTURE_CREATION'].includes(currentItem.type) &&
                            <Flex gap={4} justify={'flex-start'} align={'center'}
                                  className={'text14 font-pf color-72'}>
                              {currentItem.faceName &&
                                <>
                                  <Tooltip title={'模特名称'}>
                                    <div>{currentItem.faceName}</div>
                                  </Tooltip>
                                </>
                              }

                              {currentItem.sceneName ? (
                                <>
                                  <div>/</div>
                                  <Tooltip title={'场景名称'}>
                                    <div>{currentItem.sceneName}</div>
                                  </Tooltip>
                                </>) : currentItem.extInfo['originCustomScene'] ? (
                                <>
                                  <div>/</div>
                                  <div>{'自定义场景'}</div>
                                  <Tooltip title={currentItem.extInfo['originCustomScene']}>
                                    <QuestionCircleOutlined style={{ fontSize: 16 }}
                                                            className={'margin-left-4 color-96'} />
                                  </Tooltip>
                                </>
                              ) : (<></>)
                              }

                              {currentItem.extInfo && currentItem.extInfo['colorIndex'] !== null && currentItem.extInfo['colorIndex'] >= 0 &&
                                <>
                                  <div>/</div>
                                  {currentItem.extInfo['colorIndex'] === 0 &&
                                    <div>随机颜色</div>
                                  }
                                  {currentItem.extInfo['colorIndex'] > 0 &&
                                    <div>颜色{currentItem.extInfo['colorIndex']}</div>
                                  }
                                </>
                              }

                              {currentItem.extInfo && (currentItem.extInfo['cameraAngle']) &&
                                <>
                                  <div>/</div>
                                  <div>{currentItem.extInfo['cameraAngle'].includes('back view') ? '背面' : '正面'}</div>
                                  <div>/</div>
                                  <div>{currentItem.extInfo['bodyTypeUnlimited'] === 'Y' ? '不限' : (currentItem.extInfo['cameraAngle'].includes('upper body') ? '上半身' : (currentItem.extInfo['cameraAngle'].includes('lower body') ? '下半身' : '全身'))}</div>
                                  {!isTrialAccount && !currentItem.extInfo['enableAntiBlurLora'] &&
                                    <>
                                      <div>/</div>
                                      <div>背景虚化</div>
                                    </>
                                  }
                                  {userInfo.roleType !== 'MERCHANT' && currentItem.extInfo['enableNewModel'] &&
                                    <>
                                      <div>/</div>
                                      <div>新模型</div>
                                    </>
                                  }
                                </>
                              }
                            </Flex>
                          }

                        </Flex>
                        {currentItem && !isProcessing(currentItem) &&
                          <Button type={'primary'} className={'download-btn'} onClick={() => handleDownloadAll()}
                                  icon={<IconFont type={'icon-icon-download'}
                                                  style={{ fontSize: 16, color: '#FFFFFF' }} />}>
                            下载全部
                          </Button>
                        }
                      </Flex>
                    </>
                  }

                  {/* 细节修补, 单图换衣 */}
                  {(currentItem.type === 'REPAIR_DETAIL') &&
                    <>
                      {currentItem.extInfo['clothOriginImage'] &&
                        <ImageCard tag={<div className={'text12 font-pf color-w'}>参考</div>}
                                   src={currentItem.extInfo['clothOriginImage']} item={currentItem}
                                   ext={null} />
                      }
                    </>
                  }

                  {/*根据需要显示原图*/}
                  {['FACE_SCENE_SWITCH', 'REPAIR_HANDS', 'PARTIAL_REDRAW', 'REMOVE_WRINKLE', 'REPAIR_DETAIL', 'ERASE_BRUSH', 'IMAGE_UPSCALE'].includes(currentItem.type) &&
                    <ImageCard tag={<div className={'text12 font-pf color-w'}>原图</div>}
                               src={currentItem.extInfo['originImage']} item={currentItem}
                               ext={currentItem.status === 'FAILED' && currentItem.type === 'REPAIR_HANDS' ?
                                 <RepairHandsFailed {...currentItem} /> : null} />
                  }
                  {/*换头换背景*/}
                  {currentItem.type === 'FACE_SCENE_SWITCH' &&
                    <>
                      {currentItem.extInfo['originCustomSceneImg'] &&
                        <ImageCard tag={<div className={'text12 font-pf color-w'}>背景参考</div>}
                                   src={currentItem.extInfo['originCustomSceneImg']} item={currentItem}
                                   ext={null} />
                      }
                    </>
                  }

                  {/*印花上身*/}
                  {currentItem.type === 'LOGO_COMBINE' &&
                    <ImageCard tag={<div className={'text12 font-pf color-w'}>印花</div>}
                               src={currentItem.extInfo['logoImageOss']} item={currentItem} />
                  }

                  {/*基础款换衣特殊处理*/}
                  {(currentItem.type === 'BASIC_CHANGING_CLOTHES' || currentItem.type === 'CLOTHING_SWAP') && currentItem.creativeTasksList && (
                    <div>
                      {currentItem.creativeTasksList.map((task, taskIndex) => (
                        <Flex key={taskIndex} align="flex-start" gap={8} style={{ marginBottom: 8 }}>
                          {/* 参考图 */}
                          <ImageCard
                            tag={<div className="text12 font-pf color-w">参考图</div>}
                            src={task.extInfo.referenceOriginalImage}
                            item={currentItem}
                          />

                          {/* 生成图和占位图混合展示 */}
                          {Array(4).fill(null).map((_, index) => {
                            // 如果有对应索引的结果图，显示结果图
                            if (task.resultImages && task.resultImages[index]) {
                              return (
                                <ImageCard
                                  key={index}
                                  src={task.resultImages[index]}
                                  item={currentItem}
                                />
                              );
                            }
                            // 如果没有结果图且状态不是完成，显示占位图
                            if (task.status !== 'FINISHED') {
                              return (
                                <ImageCard
                                  key={index}
                                  src={getFilledImage(currentItem)}
                                  item={currentItem}
                                />
                              );
                            }
                            return null;
                          })}
                        </Flex>
                      ))}
                    </div>
                  )}

                  {/* 固定姿势创作 */}
                  {((currentItem.type === 'FIXED_POSTURE_CREATION') && currentItem.creativeTasksList) && (
                    <div style={{ width: '100%' }}>
                      {/* 顶部提示语 - 只在第一行显示 */}
                      <div style={{
                        textAlign: 'center',
                        padding: '12px 20px',
                        marginBottom: '16px',
                        background: 'linear-gradient(135deg, #F0F8FF 0%, #E6F3FF 100%)',
                        border: '1px solid #D6E7FF',
                        borderRadius: '8px',
                        position: 'relative',
                      }}>
                        <div style={{
                          color: '#1890FF',
                          fontSize: '13px',
                          fontWeight: '500',
                          lineHeight: '1.4',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          gap: '6px',
                        }}>
                        <span style={{
                          fontSize: '14px',
                          opacity: 0.8,
                        }}>ℹ️</span>
                          <span>参考图仅作为姿势和构图参考，最终生成结果可能因AI算法优化而有所差异</span>
                        </div>
                      </div>
                      {/* 每个任务占一行，一行四张图片：第一张参考图，其余三张结果图 */}
                      {currentItem.creativeTasksList.map((task, taskIndex) => (
                        <div key={taskIndex} style={{ marginBottom: 24, width: '100%' }}>
                          <Flex justify="flex-start" gap={12} style={{ width: '100%' }}>
                            {/* 参考图 - 固定宽度，简洁样式区分 */}
                            <div style={{ width: 'calc(25% - 8px)' }}>
                              <div
                                className="fixed-posture-image-block"
                                style={{
                                  border: '2px solid #1890FF',
                                  borderRadius: '8px',
                                  position: 'relative',
                                }}
                              >
                                <div
                                  className="fixed-posture-tag"
                                  style={{
                                    background: '#1890FF',
                                    color: 'white',
                                  }}
                                >
                                  <div className="text12 font-pf color-w">参考图</div>
                                </div>
                                <WatermarkImage
                                  src={task.extInfo.referenceOriginalImage}
                                  className="fixed-posture-image"
                                  loading="lazy"
                                  onClick={() => handleShowImage(task.extInfo.referenceOriginalImage, currentItem)}
                                />
                              </div>
                            </div>

                            {/* 结果图区域 - 三张图片 */}
                            <div style={{ width: '75%', display: 'flex', gap: '12px' }}>
                              {Array.from({ length: 3 }, (_, resultIndex) => (
                                <div key={resultIndex} style={{ width: 'calc(33.33% - 8px)' }}>
                                  {task.resultImages && task.resultImages[resultIndex] ? (
                                    <div className="fixed-posture-image-block">
                                      <div className="fixed-posture-tag">
                                        <div className="text12 font-pf color-w">结果图 {resultIndex + 1}</div>
                                      </div>
                                      <WatermarkImage
                                        src={task.resultImages[resultIndex]}
                                        className="fixed-posture-image"
                                        loading="lazy"
                                        onClick={() => handleShowImage(task.resultImages?.[resultIndex] || '', currentItem)}
                                      />
                                    </div>
                                  ) : (
                                    task.status !== 'FINISHED' && (
                                      <div className="fixed-posture-image-block" style={{ opacity: 0.6 }}>
                                        <WatermarkImage
                                          src={getFilledImage(currentItem)}
                                          className="fixed-posture-image"
                                          loading="lazy"
                                          style={{ filter: 'blur(1px)' }}
                                        />
                                      </div>
                                    )
                                  )}
                                </div>
                              ))}
                            </div>
                          </Flex>

                          {/* 任务分隔线 */}
                          {taskIndex < currentItem.creativeTasksList.length - 1 && (
                            <div style={{
                              height: '1px',
                              background: 'linear-gradient(90deg, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.05) 50%, rgba(0,0,0,0.1) 100%)',
                              margin: '16px 0',
                              width: '100%',
                            }} />
                          )}
                        </div>
                      ))}
                    </div>
                  )}


                  {/*除基础款换衣、固定姿势创作以及视频创作外的其他类型*/}
                  {!['CLOTHING_SWAP', 'CREATE_VIDEO', 'BASIC_CHANGING_CLOTHES', 'FIXED_POSTURE_CREATION'].includes(currentItem.type) &&
                    getShowResultImages(currentItem) !== null &&
                    getShowResultImages(currentItem).map((src, index) => (
                      <React.Fragment key={index + ''}>
                        {/*印花上身每行只展示4个*/}
                        {currentItem.type === 'LOGO_COMBINE' && index === 4 && currentItem.resultImages && currentItem.resultImages.length > 4 &&
                          <ImageCard src={''} item={currentItem} />
                        }

                        <ImageCard tag={currentItem.type === 'REPAIR_HANDS' ?
                          <IconFont type={'icon-shoubuxiufu'} style={{ fontSize: 16 }}
                                    className={'color-w'} /> : (currentItem.type === 'IMAGE_UPSCALE' ?
                            <ZoomInOutlined style={{ fontSize: 16 }} className={'color-w'} /> : null)}
                                   src={src} item={currentItem} />
                      </React.Fragment>
                    ))}

                  {/*视频创作*/}
                  {currentItem.type === 'CREATE_VIDEO' &&
                    <VideoCard item={currentItem} />
                  }
                </div>
                {currentItem.type === 'CREATE_VIDEO' &&
                  <Flex className={'width-100' + (isExampleImage(currentItem) ? '' : ' margin-bottom-12')}
                        justify={'flex-start'} align={'center'} gap={8}>
                    <div
                      className={'creative-type text14 font-pf color-72'}>{getCreativeTypeName(currentItem.type, bizType)}</div>
                    <div className={'text14 font-pf color-72'} style={{ padding: 4 }}>{currentItem.createTime}</div>
                  </Flex>
                }
              </div>

            </div>
          </>
        }

        {/* 需要显示状态栏 */}
        {needShowStatusBar() &&
          <div className={'right-block slider-bar task-slider-bar'}>
            {(currentItem?.status === 'QUEUE') &&
              <Space className={'message-card'}>
                <Space>
                  <img src={QUEUE_ICON} width={24} height={24} alt={'logo'} />
                  <div className={'text14 font-pf color-1a'}>
                    {queueSize ? `有${queueSize}个用户在排队，请等候` : '正在排队中，请等候'}
                  </div>
                </Space>
                <Button type="primary" className={'cancel-btn'} onClick={handleCancelQueue}>
                  <div className={'font-pf text16 weight color-70'}>取消排队</div>
                </Button>
              </Space>
            }

            {/* 进度条 */}
            {(currentItem?.status === 'PROCESSING') &&
              <div className={'col-start-block'}>
                {schedule &&
                  <MultiStepProgress steps={schedule.steps} subStep={schedule.subStep} resize={sliderValue} />
                }
                <div className={'font-pf text14 color-70 margin-bottom-4'}>
                  {currentItem?.type && currentItem?.type === 'REPAIR_HANDS' ? '正在进行手部修复' : '正在生成图片'}
                </div>
              </div>
            }

            {/* 缩小放大 */}
            {changeSliderValue &&
              <Space className={'font-pf text14 color-70'}>
                缩小
                <Slider value={sliderValue} min={12} max={22} tooltip={{ open: false }}
                        onChange={(e) => changeSliderValue(e)}
                        style={{ width: '224px' }} disabled={todayList.length <= 0} />
                放大
                <Tooltip title={'清空当前所有任务'}>
                  <Button type="primary" className={'cancel-btn'} style={{ height: 28, width: 76 }} onClick={handleClear}
                          disabled={todayList.length <= 0}>
                    <div className={'font-pf text16 color-70'}>清空</div>
                  </Button>
                </Tooltip>
              </Space>
            }
          </div>
        }
        {/* 没有当日创作任务时, 显示用户引导 */}
        {!todayList || todayList.length <= 0 && GuideBlock && <GuideBlock />}

      </Flex>


      {/* 任务栏 */}
      <Flex vertical className={'task-bar'} justify={'flex-start'} align={'center'} gap={16}>
        {todayList.map((item, index) => (
          <Flex key={index} align={'center'} justify={'center'} onClick={() => setShowId(item.id)}
                className={'task-card-container pointer' + (showId === item.id ? ' task-card-container-selected' : '')}>

            <Flex className={'task-card'} style={{ backgroundImage: `url(${item.showImage})`, backgroundSize: 'cover' }}
                  vertical justify={!isProcessing(item) ? 'flex-end' : 'center'} align={'center'}>

              {(item.status === 'FINISHED' && !isExampleImage(item)) &&
                <Flex align={'center'} justify={'center'} className={'text12 color-w task-card-desc'}>已完成</Flex>
              }
              {(item.status === 'FINISHED' && isExampleImage(item)) &&
                <Flex align={'center'} justify={'center'} className={'text12 color-1a task-card-desc'} gap={4}
                      style={{ background: 'linear-gradient(90deg, #FFCCB9 0%, #D6CEFF 100%)' }}>
                  <img src={GIFT_ICON} alt={''} width={16} height={16} />
                  <div>精选图</div>
                </Flex>
              }
              {item.status === 'FAILED' &&
                <>
                  <Flex align={'center'} justify={'center'} className={'text12 color-w task-card-desc'}
                        style={{ zIndex: 10 }}>
                    生成失败
                  </Flex>
                  <div style={{
                    position: 'absolute', top: 0, width: '100%', height: '100%',
                    background: 'linear-gradient(0deg, rgba(255, 0, 0, 0.2), rgba(255, 0, 0, 0.2))',
                    zIndex: 1, borderRadius: 8,
                  }}></div>
                </>
              }
              {(item.status === 'QUEUE' || item.status === 'INIT') &&
                <>
                  {/*<IconFont type={'icon-queue'} style={{ fontSize: 24, zIndex: 10, color: '#FFFFFF' }} />*/}
                  <div className={'text12 color-w'} style={{ textShadow: '0 0 4px rgba(0, 0, 0, 0.4)', zIndex: 10 }}>
                    排队中
                  </div>
                  <div style={{
                    position: 'absolute', width: '100%', height: '100%',
                    backgroundColor: 'rgba(0, 0, 0, 0.4)', zIndex: 1, borderRadius: 8,
                  }}></div>
                </>
              }
              {item.status === 'PROCESSING' &&
                <>
                  <IconFont type={'icon-loading'} style={{ fontSize: 24, zIndex: 10 }} className={'loading-img'} />
                  <div className={'text12 color-w'} style={{ textShadow: '0 0 4px rgba(0, 0, 0, 0.4)', zIndex: 10 }}>
                    {item.type === 'CREATE_VIDEO' ? '视频' : '图片'}生成中
                  </div>

                  {item.type === 'CREATE_VIDEO' &&
                    <div className={'text12 color-w0-6'}
                         style={{ textShadow: '0 0 4px rgba(0, 0, 0, 0.4)', zIndex: 10 }}>
                      请{calculateTimeDifference(item.createTime)}h后查看
                    </div>
                  }
                  <div style={{
                    position: 'absolute', width: '100%', height: '100%',
                    backgroundColor: 'rgba(0, 0, 0, 0.4)', zIndex: 1, borderRadius: 8,
                  }}></div>
                </>
              }
              {showId === item.id &&
                <IconFont type={'icon-a-duobianxing2'} className={'task-card-arrow'} />
              }
            </Flex>
          </Flex>
        ))}
      </Flex>

      {/* 图片预览 */}
      <ImgPreview
        previewVisible={!!detailImage}
        handleCancelPreview={handleHideMask}
        previewImage={detailImage}
        needSwitch={!!detailImageList}
        previewIdx={detailImageList.findIndex(item => item === detailImage)}
        previewImgs={detailImageList}
        likeObj={likeObj?.like}
        likeCallback={() => refreshData(likeObj?.id)}
        showTools={true}
        type={opItem?.type}
        creativeBatch={opItem}
        modelId={opItem?.modelId}
        previewInfoList={previewInfoList}
      />

      {/* 删除确认 */}
      {deleteId &&
        <Modal open={true} title={'确认删除此任务吗？'} onCancel={() => setDeleteId(null)} onOk={handleDelete}
               closable={false} okText={'确认并删除'} cancelText={'取消'} centered>
          删除后将无法复原，请再次确认
        </Modal>
      }
    </Flex>;
  },
);

export default TaskOutputBlock;