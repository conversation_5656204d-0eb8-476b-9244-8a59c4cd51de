import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Alert, Drawer, Flex, message, Popconfirm, Tooltip } from 'antd';
import './LogoCombineDrawer.less';
import { ElementConfig } from '@/services/ElementController';
import { Image as KonvaImage, Layer, Stage } from 'react-konva';
import useImage from 'use-image';
import MovableImage from '@/components/Creative/MovableImage';
import { CaretLeftOutlined, CaretRightOutlined, LoadingOutlined } from '@ant-design/icons';
import { fetchOssBlobUrl } from '@/utils/ossUtils';
import { removeBg } from '@/services/CreativeController';
import { deepCopy, download, getImageNameWithTag } from '@/utils/utils';

export interface LogoCombineDrawerProps {
  expand: boolean;
  clothStyle?: ElementConfig;
  logoImageUrl: string;
  logoFile: File | null;
  changeExpand: (expand: boolean) => void;
}

export interface LogoCombineDrawerRef {
  clearTransformer: () => void;
  getDataUrl: () => string;
  downloadImage: () => void;
}

const LogoCombineDrawer = forwardRef<LogoCombineDrawerRef, LogoCombineDrawerProps>(({
                                                                                      expand,
                                                                                      logoImageUrl,
                                                                                      logoFile,
                                                                                      changeExpand,
                                                                                      clothStyle,
                                                                                    }, ref) => {
    const [open, setOpen] = React.useState(false);
    const [imageWidth, setImageWidth] = useState(570);
    const [imageHeight, setImageHeight] = useState(760);
    const [bgImageUrl, setBgImageUrl] = useState('');
    const [maskImageUrl, setMaskImageUrl] = useState('');
    const [bgImageIndex, setBgImageIndex] = useState(0);
    const [rbgLogoImageUrl, setRbgLogoImageUrl] = useState('');
    // @ts-ignore
    const [bgImage] = useImage(bgImageUrl, 'Anonymous');
    // @ts-ignore 该字段只是用来初始化logo图片长宽
    const [logo] = useImage(logoImageUrl, 'Anonymous');

    const [showTooltip, setShowTooltip] = useState(false);

    const [logoImage, setLogoImage] = useState<any>(null);
    const [selected, setSelected] = useState<any>(false);
    const [hover, setHover] = useState(false);

    const stageRef = useRef(null);
    const bgRef = useRef(null);

    useEffect(() => {
      window.addEventListener('resize', resize);
      return () => {
        window.removeEventListener('resize', resize);
      };
    }, []);

    useEffect(() => {
      if (!logoFile) {
        return;
      }
      removeBg(logoFile).then(res => {
        if (!res) {
          return;
        }

        res.blob().then(blob => {
          setRbgLogoImageUrl(URL.createObjectURL(blob));
          setShowTooltip(true);
          setTimeout(() => {
            setShowTooltip(false);
          }, 8000);
        });
      });
    }, [logoFile]);

    useEffect(() => {
      if (logoImage) {
        setRbgLogoImageUrl('');
        const copy = deepCopy(logoImage);
        copy.src = logoImageUrl;
        setLogoImage(copy);
      }
    }, [logoImageUrl]);

    useEffect(() => {
      setOpen(expand);
    }, [expand]);

    useEffect(() => {
      if (clothStyle && clothStyle.belong === 'CUSTOM') {
        loadBgImage(clothStyle);
      }
      if (clothStyle && clothStyle.belong === 'SYSTEM' && clothStyle.children.length > bgImageIndex) {
        loadBgImage(clothStyle.children[bgImageIndex]);
      }
    }, [clothStyle, bgImageIndex]);

    useEffect(() => {
      resize();
    }, [bgImage, clothStyle?.belong]);

    const resize = () => {
      if (!bgImage) {
        return;
      }
      // @ts-ignore
      const [originWidth, originHeight] = [bgImage.naturalWidth, bgImage.naturalHeight];
      //计算图片宽高
      // @ts-ignore
      const colorSelectorHeight = 108 + 13;
      const height = window.innerHeight - 140 - colorSelectorHeight;//calc(100vh - @repair-header-height - 16px - 38px - 8px);
      const width = height / originHeight * originWidth;
      setImageHeight(height);
      setImageWidth(width);

      if (!logoImage && logo) {
        // @ts-ignore
        const [logoWidth, logoHeight] = [logo.naturalWidth, logo.naturalHeight];
        const targetWidth = width / 3;//背景图片的1/3
        const targetHeight = targetWidth / logoWidth * logoHeight;
        setLogoImage({
          src: logoImageUrl,
          x: (width - targetWidth) / 2,
          y: (height - targetHeight) / 2,
          width: targetWidth,
          height: targetHeight,
        });
      }
    };

    const loadBgImage = (config: ElementConfig) => {
      setMaskImageUrl('');
      fetchOssBlobUrl(config.showImage).then(res => {
        if (res) {
          setBgImageUrl(res);
        }
      });

      if (clothStyle && clothStyle.extInfo && clothStyle.extInfo['maskImage']) {
        fetchOssBlobUrl(clothStyle.extInfo['maskImage']).then(res => {
          if (res) {
            setMaskImageUrl(res);
          }
        });
      }
    };

    const changeLogo = (newAttrs) => {
      setLogoImage(newAttrs);
    };

    const changeLogoSrc = (src: string) => {
      if (src) {
        const copy = deepCopy(logoImage);
        copy.src = src;
        setLogoImage(copy);
      }
    };

    const calcWidth = () => {
      if (clothStyle) {
        const length = clothStyle.children.length;
        if (length > 14) {
          const mid = Math.round((length + 1) / 2);
          return 12 * 2 + 24 * Math.round((length + 1) / 2) + 8 * (mid - 1) + 2;
        }
      }

      return 'auto';
    };

    useImperativeHandle(ref, () => ({
      getDataUrl() {
        if (!logoImage || !bgImage) {
          message.warning('请先上传印花图');
          return null;
        }

        const scale = bgImage.naturalHeight / imageHeight; // Pixel ratio for higher quality
        const stage = stageRef.current;

        // @ts-ignore
        return stage.toDataURL({ pixelRatio: scale, quality: 1 });
      },
      clearTransformer() {
        setSelected(false);
      },
      downloadImage(){
        const dataUrl = this.getDataUrl();
        const fileName = bgImageUrl ? getImageNameWithTag(bgImageUrl, 'logo') : 'temp.jpg';
        download(dataUrl, fileName);
      }
    }));

    return <>
      <Drawer
        placement="left"
        closable={false}
        className={'logo-combine-drawer'}
        maskClosable={false}
        onClose={() => setOpen(false)}
        open={open}
        getContainer={false}
      >
        <Flex vertical gap={8} align={'center'} justify={'space-between'} style={{ height: '100%' }}>
          <Alert type="info" showIcon className={'color-brand text14 font-pf width-100'}
                 message="将图案调整到合适的位置" />

          <Stage width={imageWidth} height={imageHeight} ref={stageRef} onMouseLeave={() => setSelected(false)}
                 onMouseEnter={() => {
                   if (!selected) {
                     setSelected(true);
                   }
                 }}>
            <Layer>
              <KonvaImage image={bgImage} width={imageWidth} height={imageHeight} ref={bgRef} />
              {logoImage &&
                <MovableImage
                  image={logoImage}
                  maskImage={{ src: maskImageUrl, width: imageWidth, height: imageHeight }}
                  isSelected={selected}
                  onChange={(newAttrs) => changeLogo(newAttrs)}
                />
              }
            </Layer>
          </Stage>

          <Flex gap={8}>
            {clothStyle && clothStyle.belong === 'SYSTEM' &&
              <Flex vertical justify={'flex-start'} align={'flex-start'} gap={8}
                    className={'logo-combine-drawer-color-selector'} style={{ width: calcWidth() }}>
                <div className={'text14 font-pf color-96 width-100'}>服装颜色</div>
                <Flex wrap={'wrap'} align={'flex-start'} justify={'flex-start'} gap={8} className={'width-100'}>
                  {clothStyle && clothStyle?.children.map((item, index) => (
                    <div key={index} onClick={() => setBgImageIndex(index)}
                         className={'logo-combine-drawer-color-block' + (index === bgImageIndex ? ' logo-combine-drawer-color-block-selected' : '')}
                         style={{ background: item.extInfo['clothColor'] }}></div>
                  ))}
                </Flex>
              </Flex>
            }

            <Flex justify={'flex-start'} align={'flex-start'} gap={8} className={'logo-combine-drawer-color-selector'}>
              <div
                className={'logo-combine-drawer-bg-block' + (logoImage && logoImage.src !== rbgLogoImageUrl ? ' border-selected' : '')}
                onClick={() => {
                  changeLogoSrc(logoImageUrl);
                }}>
                <img src={logoImageUrl} width={84} height={84} alt={''} />
                <div className={'repair-hands-tag logo-combine-drawer-bg-tag'}>
                  <div className={'text14 font-pf color-w'}>原图</div>
                </div>
              </div>

              <div
                className={'logo-combine-drawer-bg-block' + (logoImage && logoImage.src === rbgLogoImageUrl ? ' border-selected' : '')}
                style={{
                  background: !rbgLogoImageUrl ? `url(${logoImageUrl})` : '',
                  backgroundSize: !rbgLogoImageUrl ? '84px 84px' : '',
                }}
                onClick={() => {
                  changeLogoSrc(rbgLogoImageUrl);
                }}>
                <Tooltip title="已去除印花背景，点击切换" open={showTooltip && expand}>
                  <img src={rbgLogoImageUrl} width={84} height={84} alt={''} />
                </Tooltip>
                <div className={'repair-hands-tag logo-combine-drawer-bg-tag'}>
                  <div className={'text14 font-pf color-w'}>去背景</div>
                </div>

                {!rbgLogoImageUrl &&
                  <Flex className={'logo-combine-drawer-bg-loading'} justify={'center'} align={'center'}>
                    <LoadingOutlined style={{ fontSize: 24, color: 'rgba(255, 255, 255, 0.85)' }} />
                  </Flex>
                }
              </div>

            </Flex>
          </Flex>
        </Flex>
        <Flex vertical align={'center'} justify={'center'} className={'logo-combine-drawer-expend'}
              onClick={() => changeExpand(false)} onMouseLeave={() => setHover(false)}
              onMouseEnter={() => setHover(true)}>
          <CaretLeftOutlined style={{ fontSize: 16, color: hover ? '#366EF4' : '#727375' }} />
        </Flex>
      </Drawer>

      {!open &&
        <Flex vertical align={'center'} justify={'center'}
              className={'logo-combine-drawer-expend logo-combine-drawer-contract'}
              onClick={() => changeExpand(true)} onMouseLeave={() => setHover(false)}
              onMouseEnter={() => setHover(true)}>
          <CaretRightOutlined style={{ fontSize: 16, color: hover ? '#366EF4' : '#727375' }} />
        </Flex>
      }
    </>;
  })
;

export default LogoCombineDrawer;