import { ElementConfig, ProportionType } from '@/services/ElementController';
import React, { useState } from 'react';
import { Flex, Modal } from 'antd';
import './ElementWithPresetBlock.less';
import ElementSelector from '@/components/Creative/ElementSelector';
import { LoraType } from '@/services/CreativeController';

export interface ElementWithPresetBlockProps {
  config: ElementConfig;
  current: Array<ElementConfig>;
  multipleOptions?: boolean;
  title?: string;
  proportion?: ProportionType;
  forceProportion?: boolean;
  maxChoose?: number;
  pageSize?: number;
  acceptTypes?: Array<string>,
  conditions?: any;
  onChange: (configId: number, value: Array<ElementConfig>) => void;
  changeType?: (type: string) => void;
}

const ElementWithPresetBlock: React.FC<ElementWithPresetBlockProps> = ({
                                                                         config,
                                                                         current,
                                                                         multipleOptions = false,
                                                                         title,
                                                                         proportion = 'ONE_ONE',
                                                                         forceProportion = false,
                                                                         maxChoose = 8,
                                                                         pageSize = 8,
                                                                         acceptTypes,
                                                                         conditions,
                                                                         onChange,
                                                                         changeType,
                                                                       }) => {
  const [showSelector, setShowSelector] = useState(false);
  const [belong, setBelong] = useState<LoraType>('SYSTEM');

  const handleChange = (configId: number, value: Array<ElementConfig>) => {
    if (onChange) {
      onChange(configId, value);
    }
  };

  return <>
    <Flex vertical key={config.id} gap={8} justify={'flex-start'}>
      <div className={'text16 font-pf color-n weight'}>选择{config.name}{multipleOptions? '（可多选，最多可选' + maxChoose + '张）':''}</div>
      <ElementSelector title={title ? title : config.name} onChange={onChange} config={config} current={current}
                       proportion={proportion} multipleOptions={multipleOptions} maxChoose={maxChoose}
                       pageSize={pageSize} onMore={() => setShowSelector(true)} conditions={conditions}
                       acceptTypes={acceptTypes} changeType={changeType} changeBelong={(type) => setBelong(type)}
                       forceProportion={forceProportion} />
    </Flex>

    <Modal open={showSelector} onCancel={() => setShowSelector(false)} onOk={() => setShowSelector(false)}
           closable={false} maskClosable={false} title={'全部' + (title ? title : config.name)} centered
           className={'element-selector-container'}>
      <ElementSelector title={title ? title : config.name} onChange={handleChange} config={config} current={current}
                       pageSize={999999} proportion={proportion} multipleOptions={multipleOptions}
                       conditions={conditions} acceptTypes={acceptTypes} belong={belong}
                       forceProportion={forceProportion} />
    </Modal>
  </>;
};

export default ElementWithPresetBlock;