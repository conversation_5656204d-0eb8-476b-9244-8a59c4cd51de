import React, { useEffect, useState } from 'react';
import { Button, DatePicker, Flex, Input, message, Modal, Pagination, Select } from 'antd';
import IconFont from '@/components/IconFont';
import { getMaterialModelById, MaterialModel, queryMaterialModelByPage } from '@/services/MaterialModelController';
import WatermarkImage from '@/components/WatermarkImage';
import { ZoomInOutlined } from '@ant-design/icons';
import ImgPreview from '@/components/ImgPreview';
import { ClothMaterialImg, getMaterialInfoById, MaterialInfoVO } from '@/services/MaterialInfoController';
import { MINI_LOADING_ICON } from '@/constants';
import { UserVO } from '@/services/UserController';
import debounce from 'lodash/debounce';
const { Option } = Select;

const { RangePicker } = DatePicker;

interface ImageSelectorProps {
  value: Array<any>;
  maxChoose?: number;
  onFinish: (value: Array<any>) => void;
  onCancel: () => void;
  modelId?: number;
  originImage?: string;
  desc?: string;
}

const ClothImageSelector: React.FC<ImageSelectorProps> = ({ value = [], maxChoose = 4, onFinish, onCancel, modelId, originImage, desc='' }) => {
  const [selected, setSelected] = useState<Array<string>>(value);
  const [modelDetail, setModelDetail] = useState<MaterialInfoVO | null>(null);
  const [dataList, setDataList] = useState<Array<MaterialModel>>([]);
  const [total, setTotal] = useState(0);
  const [selectedModelId, setSelectedModelId] = useState<number>();
  const [searchType, setSearchType] = useState<string | null>(null);
  const [dates, setDates] = useState([]);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(8);
  const [onlyShowExampleImg, setOnlyShowExampleImg] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [previewImgList, setPreviewImgList] = useState<Array<string>>([]);
  const [commiting, setCommiting] = useState(false);
  const [nameLike, setNameLike] = useState<string | null>(null);
  const [creators, setCreators] = useState<Array<UserVO>>([])
  const [selectedCreator, setSelectedCreator] = useState<UserVO | null>(null);
  let n = 0;
  useEffect(() => {
    if (modelId) {
      getMaterialModelById(modelId).then(res => {
        if (res) {
          onShowClothDetail(res);
        }
      });
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [page, pageSize, dates, selectedModelId, searchType, onlyShowExampleImg, nameLike, selectedCreator]);

  const fetchData = async () => {
    console.log(n);
    n = n + 1;
    const query = {
      orderBy: 'id desc',
      // @ts-ignore
      dateFrom: dates?.[0]?.format('YYYYMMDD'),
      // @ts-ignore
      dateTo: dates?.[1]?.format('YYYYMMDD'),
      pageNum: page,
      pageSize: pageSize,
      statusList: ['ENABLED'],
      needModelPoint: false,
      nameLike: nameLike,
      creators: creators,
    };

    const ret = await queryMaterialModelByPage(query);
    if (ret) {
      setDataList(ret.list || []);
      setTotal(ret.totalCount || 0);
    }
  };

  const handleDateChange =debounce((newDates) => {
    setPage(1);
    setDates(newDates);
  }, 300);

  const handleNameLikeChange = debounce( (e) => {
    setNameLike(e.target.value);
    setPage(1);
  }, 500);

  const handlePageChange = (page: number, pageSize?: number) => {
    setPage(page);
    if (pageSize) {
      setPageSize(pageSize);
    }
  };

  const handleChange = (imgUrl: string) => {
    if (maxChoose == 1) {
      handleFinish(imgUrl);
      return;
    }
    let newItems = selected ? [...selected] : [];
    if (newItems.some(e => e === imgUrl)) {
      newItems = newItems.filter(e => e !== imgUrl);
    } else {
      newItems = [...newItems, imgUrl];
    }
    if (newItems.length > maxChoose) {
      message.warning(`最多可以选择${maxChoose}个`);
      return;
    }
    setSelected(newItems);
  };

  const handleReturn = () => {
    if (dataList.length === 0) {
      fetchData();
    }
    setModelDetail(null);
  }

  const handlePreviewImg = (imgUrl, allImages) => {
    setPreviewImage(imgUrl);
    setPreviewImgList(allImages);
  };

  const onShowClothDetail = async (model: MaterialModel) => {
    const mid = model?.clothLoraTrainDetail?.originalMaterialId || model?.materialInfoId;
    const res = await getMaterialInfoById(mid);
    if (res) {
      setModelDetail(res);
    } else {
      message.error('查询服装详情失败');
    }
  };

  const handlePreviewUrl = (url: string) => {
    setPreviewImage(url);
  };

  const getUpperDetailImgs = (imgList: ClothMaterialImg[]) => {
    return imgList?.filter(img => img.viewTags.includes('upper')) || [];
  };

  const getLowerDetailImgs = (imgList: ClothMaterialImg[]) => {
    return imgList?.filter(img => img.viewTags.includes('lower')) || [];
  };

  const SelectableImage = ({ imgUrl, allImages }) => (
    <div style={{ position: 'relative', borderRadius: 8, overflow: 'hidden', cursor: 'pointer' }}>
      <WatermarkImage src={imgUrl} height={200} onClick={() => handleChange(imgUrl)} />
      {selected.includes(imgUrl) && (
        <div style={{ position: 'absolute', right: 8, top: 8 }} onClick={() => handleChange(imgUrl)}>
          <IconFont type={'icon-gou-lan'} style={{ fontSize: 32, color: '#366EF4' }} />
        </div>
      )}
      <div className={'image-selector-round'} onClick={() => handlePreviewImg(imgUrl, allImages)}>
        <ZoomInOutlined style={{ fontSize: 16 }} />
      </div>
    </div>
  );

  const ImageCard = React.memo((item: MaterialModel) => (
    <div className="models-image-card">
      <div className="models-img-cover" style={{ cursor: 'pointer' }}>
        <img src={item.showImage} alt={item.name} onClick={() => onShowClothDetail(item)} />
      </div>
      <div className="models-image-card-name">{item.name}</div>
      <div className="models-image-card-info">创建时间：{item.createTime}</div>
      {item.clothTypeDesc && <div className="models-image-card-info">服装种类：{item.clothTypeDesc}</div>}
    </div>
  ));

  const uploadCardList = (imgList: ClothMaterialImg[]) => {
    return imgList?.map((img, index) => (
      <Flex align={'center'} justify={'center'} key={index} style={{ height: 200, width: 150}}>
        {img && <SelectableImage imgUrl={img.imgUrl} allImages={imgList.map(img => img.imgUrl)} />}
      </Flex>
    ));
  };
  const handleFinish = (imgUrl :string = '') => {
    if (selected.length === 0 && !imgUrl) {
      message.error('请选择至少一张图片');
      return;
    }
    setCommiting(true);
    if (imgUrl) {
      onFinish([imgUrl]);
    } else {
      onFinish(selected);
    }
    setCommiting(false);
  };

  const ModalFooter = () => {
    return (
      <Flex align={'center'} justify={'space-between'} style={{ marginTop: '8px' }}>
        {!modelDetail && (
          <Pagination
            current={page}
            pageSize={pageSize}
            total={total}
            onChange={handlePageChange}
            showSizeChanger={false}
            showQuickJumper
            style={{ textAlign: 'center' }}
          />
        )}
        {modelDetail && <div></div>}
        { maxChoose > 1 &&
          <Flex gap={8}>
            <Button onClick={onCancel}>取消</Button>
            <Button type={'primary'} onClick={() => handleFinish()} icon={commiting ?
              <img src={MINI_LOADING_ICON} width={16} height={16} alt={'logo'} className={'loading-img'} />
              : ''}>
              {commiting ? '' : '确定'}
            </Button>
          </Flex>
        }
      </Flex>
    )
  }

  return (
    <Modal open={true} width={1184} centered maskClosable={false} footer={<ModalFooter />} onCancel={onCancel}>
      <Flex vertical gap={16} style={{ height: 700, overflowY: 'auto', marginRight: 16 }}>
        <Flex gap={8} align={'center'} style={{ height: 140, width: '100%', background: '#F5F6F9', borderRadius: 8, padding: 8 }}>
          <img src={originImage}  alt={''}
               style={{objectFit: 'contain', maxHeight: "100%", width: 'auto', display: 'block', borderRadius: 8}}/>
          <Flex vertical>
            <div className={'text20'}>{desc}</div>
            <div className={'text20 color-brand'}>（选择半身人台图修复效果更佳）</div>
          </Flex>
        </Flex>

        <Flex vertical gap={8}>
          <Flex vertical className={'image-selector-history-wrapper'} gap={8}>
            {!modelDetail ? (
              <>
                <div className="models-filter-row">
                  <RangePicker onChange={handleDateChange} placeholder={['开始日期', '结束日期']} />
                  <Input placeholder={'服装名称模糊搜索'} style={{ width: 200 }} allowClear
                         onChange={handleNameLikeChange} />
                  <Select placeholder="请选择创建者" onChange={(e) => {setSelectedCreator(e.target.value)}}
                          style={{ width: 200 }} allowClear>
                    {Array.isArray(creators) && creators.length > 0 && creators.map((creator, index) => (
                      <Option key={creator.id} value={creator.id}>{creator.nickName}</Option>
                    ))}
                  </Select>
                </div>
                <Flex wrap={'wrap'} gap={16}>
                  {dataList.map(item => (
                    <ImageCard key={item.id} {...item} />
                  ))}
                </Flex>
              </>
            ) : (
              <>
                <Flex align={'center'} justify={'flex-start'} gap={8}>
                  <Button onClick={handleReturn}
                          icon={<IconFont type={'icon-youjiantou_16px'}
                                          style={{ transform: 'rotate(180deg)', fontSize: 16 }} />}>
                    返回
                  </Button>
                </Flex>
                <div>
                  <Flex justify={'flex-start'} gap={10} wrap={'wrap'}>
                    <div>
                      <div>衣服名称:</div>
                      <Input style={{ width: '434px' }} value={modelDetail.name} disabled={false} maxLength={20} />
                    </div>
                    <div>
                      <div>衣服种类:</div>
                      <Select style={{ width: '434px' }} value={modelDetail?.subType} disabled={true}>
                        <Select.Option value="Tops">上装</Select.Option>
                        <Select.Option value="Bottoms">下装</Select.Option>
                        <Select.Option value="TwoPiece">上下装</Select.Option>
                        <Select.Option value="OnePiece">连体衣</Select.Option>
                        <Select.Option value="SwimSuit">泳装</Select.Option>
                      </Select>
                    </div>
                  </Flex>
                  {getUpperDetailImgs(modelDetail?.materialDetail?.detailShotImgList || [])?.length > 0 && (
                    <div>
                      <div style={{margin: '24px 0 8px 0' }}>上半身细节图</div>
                      <Flex gap={16} justify={'flex-start'} wrap={'wrap'}>
                        {uploadCardList(getUpperDetailImgs(modelDetail?.materialDetail?.detailShotImgList || []))}
                      </Flex>
                    </div>
                  )}
                  {getLowerDetailImgs(modelDetail?.materialDetail?.detailShotImgList || [])?.length > 0 && (
                    <div>
                      <div style={{margin: '24px 0 8px 0' }}>下半身细节图</div>
                      <Flex gap={16} justify={'flex-start'} wrap={'wrap'}>
                        {uploadCardList(getLowerDetailImgs(modelDetail?.materialDetail?.detailShotImgList || []))}
                      </Flex>
                    </div>
                  )}
                  <div>
                    <div style={{margin: '24px 0 8px 0' }}>全身图</div>
                    <Flex gap={16} justify={'flex-start'} wrap={'wrap'}>
                      {uploadCardList(modelDetail?.materialDetail?.fullShotImgList || [])}
                    </Flex>
                  </div>
                  {modelDetail?.materialDetail?.moreImgList && modelDetail?.materialDetail?.moreImgList?.length > 0 && (
                    <div>
                      <div style={{margin: '24px 0 8px 0' }}>补充姿势图</div>
                      <Flex gap={16} justify={'flex-start'} wrap={'wrap'}>
                        {uploadCardList(modelDetail?.materialDetail?.moreImgList || [])}
                      </Flex>
                    </div>
                  )}
                </div>
              </>
            )}
          </Flex>
        </Flex>
      </Flex>

      {previewImage != null && (
        <ImgPreview
          previewVisible={!!previewImage}
          handleCancelPreview={() => {
            setPreviewImage(null);
            setPreviewImgList([]);
          }}
          previewImage={previewImage}
          needSwitch={!!previewImgList}
          previewIdx={previewImgList.findIndex(item => item === previewImage)}
          previewImgs={previewImgList}
          showTools={false}
        />
      )}
    </Modal>
  );
};

export default ClothImageSelector;