@import "@/app";

@body-width: calc(100vw - @menu-width);
@body-padding: 16px;
@element-selected-block-width: calc(@body-width / 2 - @body-padding * 2 - 12px * 4);

.avatar-uploader {
  width: 100%;
  height: 100%;
  background: #F2F3FF;;
  border-radius: 8px 8px 0 0;
  border: 1px dashed #366EF4;

  .ant-upload-select {
    width: 100% !important;
    height: 100% !important;
    border: 0 !important;
  }
}

.item-block {
  width: calc(@body-width / 2 - @body-padding * 2 - 24px);
}

.element-image-wrapper {
  position: relative;
  width: 100%;
  height: auto;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #FFFFFF;
}

.element-image-wrapper-34-forced {
  padding-top: 133.33% !important; /* 4/3 ratio */
  position: relative;
}

.element-image-wrapper-11-forced {
  padding-top: 100% !important; /* 4/3 ratio */
  position: relative;
}

.element-image-item-forced {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;

  position: absolute;
  top: 0;
  left: 0;
}

.element-image-wrapper-34 {
  height: calc(100% * 4 / 3) !important;
}

.element-image-wrapper-11 {
  height: 100% !important;
}

.element-image-item {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}

.element-modal-item {
  min-width: 162px !important;
  min-height: 244px !important;
  flex-shrink: 0;
  
  .element-image-wrapper {
    min-height: 216px !important;
    
    .element-image-item {
      min-width: 162px !important;
      min-height: 216px !important;
      object-fit: cover;
    }
  }
  
  .element-block-item-name {
    min-height: 28px !important;
    font-size: 14px !important;
    overflow: hidden;
    padding: 4px 8px !important;
    box-sizing: border-box;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    
    span {
      display: -webkit-box !important;
      -webkit-line-clamp: 2 !important;
      -webkit-box-orient: vertical !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      line-height: 14px !important;
      max-height: 28px !important;
      word-break: break-all !important;
    }
  }
}

.element-selected-container {
  padding: 8px;
  border-radius: 8px;
  opacity: 1;
  background: rgba(0, 0, 0, 0.8);
  width: calc(@element-selected-block-width + 8px * 2);
}

.element-selected-block {
  padding: 8px;
  width: @element-selected-block-width;
}

.element-selected-image-container {
  width: calc((@element-selected-block-width - 8px * 2 - 8px * 7) / 8) !important;
}

.element-selected-image-wrapper-34 {
  height: calc((@element-selected-block-width - 8px * 2 - 8px * 7) / 8 * 4 / 3 - 5px) !important;
}

.element-selected-image-wrapper-11 {
  height: calc((@element-selected-block-width - 8px * 2 - 8px * 7) / 8) !important;
}