@element-carousel-height: 504px;

/* 全局样式覆盖，确保弹窗正确定位 */
.ant-modal-wrap:has(.element-selector-modal) {
  padding: 8px 28px !important;

  .ant-modal {
    top: 0 !important;
    padding-bottom: 0 !important;
    margin: 0 !important;
    max-width: none !important;
  }

  .ant-modal-header {
    padding-bottom: 0 !important;
  }

  /* 响应式弹窗适配 */
  @media (max-height: 700px) {
    padding: 4px 32px !important;
    /* 小屏幕减少外边距 */
  }

  @media (max-height: 500px) {
    padding: 2px 16px !important;
    /* 极小屏幕最小边距 */
  }
}

.element-selector-modal {

  /* 覆盖 Ant Design Modal 的默认样式 */
  .ant-modal-header {
    border-bottom: none !important;
    background: rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 24px 24px 0 0 !important;
  }


  .ant-modal {
    top: 0 !important;
    padding-bottom: 0 !important;
    margin: 0 !important;
    max-width: none !important;
  }

  .ant-modal-content {
    height: calc(100vh - 16px) !important; /* 减去上下wrapper padding (8px * 2) */
    margin: 0 !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    display: flex !important;
    flex-direction: column !important;
    border-radius: 24px !important;
    overflow: hidden !important;
  }

  .ant-modal-header {
    padding: 16px 24px !important;
    margin-bottom: 0 !important;
    flex-shrink: 0 !important;
  }

  .ant-modal-body {
    padding: 0 !important;
    overflow: hidden !important;
    /* 使用flex布局，自动占用剩余空间 */
    flex: 1 !important;
    min-height: 0 !important; /* 允许flex项目缩小 */
    background-color: #fff;
    display: flex !important;
    flex-direction: column !important;
    border-radius: 0 0 24px 24px !important;

    /* 响应式最小高度调整 */
    @media (max-height: 800px) {
      min-height: 350px !important;
    }

    @media (max-height: 600px) {
      min-height: 300px !important;
    }

    @media (max-height: 500px) {
      min-height: 250px !important;
    }
  }



  .cancel-btn {
    width: 212px;
    height: 38px;
    border-radius: 8px;
    opacity: 1;

    /* 自动布局 */
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 8px 32px;
    gap: 8px;

    background: #FFFFFF;

    box-sizing: border-box;
    /* 中性色/N3-边框、背板 */
    border: 1px solid #E1E3EB;
  }

  .ok-btn {
    width: 212px;
    height: 38px;
    border-radius: 8px;
    opacity: 1;

    /* 自动布局 */
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 8px 32px;
    gap: 8px;

    z-index: 1;
  }

  .carousel-title {
    position: absolute;
    left: 8px;
    top: 8px;
    width: 72px;
    height: 28px;
    border-radius: 4px;
    opacity: 1;

    /* 自动布局 */
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 4px 8px;
    gap: 4px;

    background: rgba(0, 0, 0, 0.6);

    /* body/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: normal;
    line-height: 20px;
    letter-spacing: 0px;

    font-variation-settings: "opsz" auto;
    /* 白色 */
    color: #FFFFFF;
  }

  /* 轮播图样式（已移除dots，统一使用数字计数器） */

  .carousel-config-name {
    margin-top: 8px;

    font-family: PingFang SC;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    text-align: center;
    letter-spacing: 0px;

    font-variation-settings: "opsz" auto;
    /* 中性色/N8-主文字 */
    color: #1A1B1D;
  }



  .element-modal-wrapper {
    padding: 8px 8px 0 8px;
    flex: 1; /* 占用modal-body的全部空间 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-sizing: border-box;
    min-height: 0; /* 允许flex项目缩小 */
    position: relative; /* 为绝对定位的footer提供参考 */

    .element-modal-img-block {
      padding: 0 12px 0 16px;
      flex: 1;
      min-height: 0;
      display: flex;
      gap: 16px;
      align-items: flex-start;
      /* 添加相对定位，作为固定轮播图的参考容器 */
      position: relative;
      /* 确保容器宽度稳定 */
      width: 100%;
      box-sizing: border-box;
 

      /* 右侧列表区域样式 */
      >div:last-child {
        flex: 1;
        min-width: 0;
        overflow-x: hidden;
        overflow-y: auto;
        /* 确保宽度稳定，不因滚动条而变化 */
        width: 100%;
        box-sizing: border-box;
      }
    }

    .element-modal-footer {
      position: absolute;
      bottom: 0;
      left: 16px;
      right: 16px;
      z-index: 1000;
      padding: 8px 24px 0 24px;
      background: rgba(255, 255, 255, 0.5);
      backdrop-filter: blur(10px);
      border-top: 1px solid rgba(225, 227, 235, 0.3);
      border-radius: 0 0 24px 24px;
      height: 55px;
  

      /* 分页组件样式优化 */
      .ant-pagination {
        margin: 0;

        .ant-pagination-total-text {
          margin-right: 16px;
        }

        .ant-pagination-item {
          margin: 0 2px;
        }

        .ant-pagination-options {
          margin-left: 16px;
        }
      }
    }
  }

  /* 轮播图容器统一样式 */
  .element-carousel-container {
    position: sticky;
    top: 8px;
    align-self: flex-start;
    /* 统一的CSS变量定义，确保动态计算一致 */
    --available-height: calc((100vh - 200px) * 0.8);
    --available-width: 45vw;
    --scale-by-height: calc(var(--available-height) * 420 / 520);
    --scale-by-width: calc(var(--available-width) * 520 / 420);
    --container-width: min(var(--available-width), var(--scale-by-height));

    width: min(var(--available-width), var(--scale-by-height));
    height: fit-content;
    max-width: 50%;
    min-width: 320px;
    flex-shrink: 0;
  }

  /* 空白状态容器 - 根据屏幕尺寸响应式补偿高度 */
  .element-carousel-empty-container {
    position: sticky;
    top: 8px;
    align-self: flex-start;
    /* 使用与有内容状态相同的基础计算 */
    --available-height: calc((100vh - 200px) * 0.8);
    --available-width: 45vw;
    --scale-by-height: calc(var(--available-height) * 420 / 520);
    --container-width: min(var(--available-width), var(--scale-by-height));

    width: min(var(--available-width), var(--scale-by-height));
    /* 默认补偿30px（13寸屏幕） */
    height: calc(var(--container-width) * 520 / 420 + 30px);
    max-width: 50%;
    min-width: 320px;
    min-height: calc(320px * 520 / 420 + 30px);
    flex-shrink: 0;

    /* 自动布局 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0px;
    gap: 8px;
    background-color: #F5F6F9;
    border-radius: 8px;

    /* 16寸屏幕 - 补偿40px */
    @media (min-width: 1440px) and (max-width: 1728px) {
      height: calc(var(--container-width) * 520 / 420 + 40px);
      min-height: calc(320px * 520 / 420 + 40px);
    }

    /* 16寸以上屏幕 - 补偿70px */
    @media (min-width: 1729px) {
      height: calc(var(--container-width) * 520 / 420 + 70px);
      min-height: calc(320px * 520 / 420 + 70px);
    }
  }

  .element-carousel-empty-title {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: normal;
    line-height: 20px;
    text-align: center;
    letter-spacing: 0px;
    color: #727375;
    margin-top: 8px;
  }

  .element-carousel-empty-content {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: normal;
    line-height: 20px;
    text-align: center;
    letter-spacing: 0px;

    font-variation-settings: "opsz" auto;
    /* 品牌色/N6-hover */
    color: #366EF4;

    z-index: 2;
  }



  .element-model-btn-look {
    background: radial-gradient(50% 50% at 50% 50%, #B7B6FF 0%, rgba(228, 228, 255, 0.1) 100%) !important;
    box-sizing: border-box !important;
    border: 1px solid #FFFFFF !important;
    backdrop-filter: blur(10px) !important;
    color: #873BFA !important;
    font-weight: 500 !important;
  }

  .element-model-btn-real-scene-shooting {
    background: radial-gradient(50% 50% at 50% 50%, #E5F6FF 1%, rgba(229, 246, 255, 0.1) 100%) !important;
    ;
    box-sizing: border-box !important;
    border: 1px solid #FFFFFF !important;
    backdrop-filter: blur(31px) !important;
    color: #00BDFF !important;
    font-weight: 500 !important;
  }

  .element-model-btn-model-show {
    background: radial-gradient(50% 50% at 50% 50%, #DCFFEE 0%, rgba(220, 255, 238, 0.1) 100%) !important;
    box-sizing: border-box !important;
    border: 1px solid #FFFFFF !important;
    backdrop-filter: blur(31px) !important;
    color: #27C165 !important;
    font-weight: 500 !important;
  }

  .custom-carousel-container {
    position: relative;
    width: 100%;
    /* 继承父容器的CSS变量，确保与空白状态完全一致 */
    height: calc(var(--container-width) * 520 / 420);
    min-height: calc(320px * 520 / 420);
    

    .custom-carousel {
      width: 100%;
      /* 根据父容器宽度按420:520比例计算高度 */
      height: calc(var(--container-width) * 520 / 420);
      min-height: calc(320px * 520 / 420);
    }

    /* 箭头样式 */
    .carousel-arrow {
      opacity: 1 !important;
      transition: all 0.3s ease;
      background-color: rgba(0, 0, 0, 0.6) !important;
      cursor: pointer !important;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.8) !important;
        transform: translateY(-50%) scale(1.05) !important;
      }
      
      &:active {
        transform: translateY(-50%) scale(0.95) !important;
      }
    }

    &.hover-left {
      .carousel-arrow.prev {
        background-color: #366EF4 !important;
      }
    }

    &.hover-right {
      .carousel-arrow.next {
        background-color: #366EF4 !important;
      }
    }

    /* 自定义点样式 - 右下角定位，与适用范围对齐 */
    .custom-dots-bottom-right {
      position: absolute !important;
      bottom: -20px !important;
      /* 调整到与适用范围文字同一高度 */
      right: 12px !important;
      left: auto !important;
      text-align: right !important;
      width: auto !important;
      z-index: 4 !important;
      /* 提高层级确保在渐变背景之上 */

      li {
        display: inline-block !important;
        margin: 0 2px !important;

        button {
          width: 8px !important;
          height: 8px !important;
          border-radius: 50% !important;
          background-color: rgba(255, 255, 255, 0.6) !important;
          border: none !important;
          transition: all 0.3s ease !important;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
          /* 添加阴影增强可见性 */

          &:hover {
            background-color: rgba(255, 255, 255, 0.9) !important;
            transform: scale(1.1) !important;
          }
        }

        &.ant-carousel-dot-active button {
          background-color: #fff !important;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4) !important;
          transform: scale(1.2) !important;
        }
      }
    }
  }

  .element-carousel-img {
    width: 100%;
    --available-width: 45vw;
    object-fit: contain;
    border-radius: 8px;
    max-height: calc(var(--container-width) * 560 / 420);
  }

  /* 标签容器样式 */
  .element-tags-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 8px;
  }

  .element-tag {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 4px;
    padding: 4px 8px;
    backdrop-filter: blur(4px);
    transition: all 0.2s ease;
    user-select: none;

    &:hover {
      background: rgba(0, 0, 0, 0.8);
    }

    &.element-tag-clickable {
      cursor: pointer;

      &:hover {
        background: rgba(54, 110, 244, 0.8);
        box-shadow: 0 2px 8px rgba(54, 110, 244, 0.3);
      }

      &:active {
        transform: scale(0.95);
      }
    }

    &.element-tag-more {
      background: rgba(54, 110, 244, 0.7);

      .element-tag-text {
        font-weight: 500;
      }
    }

    .element-tag-text {
      font-size: 10px;
      line-height: 14px;
      color: #FFFFFF;
      font-weight: 400;
      letter-spacing: 0.5px;
      white-space: nowrap;
    }
  }

  /* 元素列表滚动容器样式 */
  .element-list-scroll-container {
    flex: 1;
    /* 关键：只在内容超出时显示滚动条 */
    overflow-y: auto;
    overflow-x: hidden;
    /* 固定宽度，防止因滚动条出现而变化 */
    width: 100%;
    box-sizing: border-box;
    /* 为底部固定元素预留空间，避免内容被遮挡 */
    padding-bottom: 60px;

    /* 隐藏滚动条 - Webkit浏览器 */
    &::-webkit-scrollbar {
      width: 0px;
      background: transparent;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: transparent;
    }

    /* 滚动行为优化 */
    scroll-behavior: smooth;

    /* Firefox 滚动条样式 - 隐藏滚动条 */
    scrollbar-width: none;

    /* 确保在不同屏幕高度下的适应性 */
    max-height: 100%;
    min-height: 0;
  }

  /* 元素列表内容样式 */
  .element-list-content {
    padding: 8px 0;
    min-height: 100%;

    /* 确保内容底部有足够的空间 */
    &::after {
      content: '';
      display: block;
      height: 16px;
    }
  }

  /* 响应式滚动条适配 - 保持隐藏状态 */
  @media (max-height: 800px) {
    .element-list-scroll-container {
      /* 中等屏幕高度时适当减少底部预留空间 */
      padding-bottom: 110px;

      /* 继续隐藏滚动条 */
      &::-webkit-scrollbar {
        width: 0px;
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: transparent;
      }
    }

    .element-list-content {
      padding: 4px 0;
      /* 减少内边距以节省空间 */

      &::after {
        height: 8px;
        /* 减少底部空间 */
      }
    }
  }

  @media (max-height: 600px) {
    .element-list-scroll-container {
      /* 小屏幕高度时减少底部预留空间 */
      padding-bottom: 100px;

      /* 继续隐藏滚动条 */
      &::-webkit-scrollbar {
        width: 0px;
        background: transparent;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: transparent;
      }
    }

    .element-list-content {
      padding: 2px 0;
      /* 进一步减少内边距 */

      &::after {
        height: 4px;
        /* 最小底部空间 */
      }
    }
  }

  /* 高分辨率屏幕优化 */
  @media (min-height: 1200px) {
    .element-list-scroll-container {
      /* 大屏幕高度时增加底部预留空间 */
      padding-bottom: 140px;

      /* 继续隐藏滚动条 */
      &::-webkit-scrollbar {
        width: 0px;
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: transparent;
      }
    }

    .element-list-content {
      padding: 12px 0;
      /* 大屏幕时增加内边距 */

      &::after {
        height: 24px;
        /* 更多底部空间 */
      }
    }
  }

  /* 元素模态框项目基础样式 */
  .element-modal-item {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    display: flex;
    flex-direction: column;
    /* 修复hover时的宽度变化问题 */
    width: 100% !important;
    min-width: 0 !important;
    /* 添加基础的最大尺寸限制 */
    max-width: 300px !important;
    max-height: 400px !important;
    box-sizing: border-box !important;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
      /* 确保hover状态下宽度不变 */
      width: 100% !important;
      min-width: 0 !important;
      max-width: 300px !important;
    }

    &.work-item-selected {
      border: 2px solid #366EF4;
      box-shadow: 0 4px 16px rgba(54, 110, 244, 0.2);
      /* 确保选中状态下宽度不变 */
      width: 100% !important;
      min-width: 0 !important;
      max-width: 300px !important;
    }

    .element-image-wrapper {
      width: 100% !important;
      aspect-ratio: 3/4 !important;
      /* 固定图片区域比例，不被文字影响 */
      border-radius: 8px 8px 0 0;
      overflow: hidden;
      position: relative;
      flex-shrink: 0;
      /* 防止图片被压缩 */
      /* 添加最大高度限制 */
      max-height: 320px !important;

      .element-image-item {
        width: 100% !important;
        height: 100% !important;
        object-fit: cover;
        display: block;
        transition: transform 0.2s ease;
      }
    }

    .element-block-item-name {
      width: 100% !important;
      min-height: 60px !important;
      /* 为文字预留固定空间，添加最大高度限制 */
      max-height: 80px !important;
      font-size: 14px !important;
      font-weight: normal;
      color: #1A1B1D;
      text-align: center;
      box-sizing: border-box;
      background: #fff;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      border-radius: 0 0 8px 8px;
      flex-shrink: 0;
      /* 文字区域不被压缩 */
      /* 添加文字溢出处理 */
      overflow: hidden !important;
      padding: 8px 12px !important;

      span {
        display: -webkit-box !important;
        -webkit-line-clamp: 2 !important;
        -webkit-box-orient: vertical !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        line-height: 18px !important;
        word-break: break-word !important;
        white-space: normal !important;
      }
    }
  }

  /* 响应式网格布局 - 根据屏幕宽高和左侧轮播图占用空间动态调整 */
  .responsive-element-grid {
    display: grid;
    gap: 8px;
    width: 100%;

    /* 动态计算可用空间 */
    --available-height: calc((100vh - 200px) * 0.75);
    --available-width: 45vw;
    --scale-by-height: calc(var(--available-height) * 420 / 520);
    --left-container-width: min(var(--available-width), var(--scale-by-height));

    /* 使用 auto-fill 确保正常排列，不会因为最大宽度而换行 */
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));

    @media (min-aspect-ratio: 5/2) {
      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    /* 标准宽屏优化（宽高比 1.5 - 2.5） */
    @media (min-aspect-ratio: 3/2) and (max-aspect-ratio: 5/2) {
      grid-template-columns: repeat(auto-fill, minmax(170px, 1fr));
    }

    /* 接近方形屏幕（宽高比 1.0 - 1.5） */
    @media (max-aspect-ratio: 3/2) {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    /* 高分辨率屏幕特殊处理 */
    @media (min-width: 2560px) {
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    @media (min-width: 3440px) {
      grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }

    /* 小屏幕保护 - 使用固定列数确保合理的元素大小 */
    @media (max-width: 1024px) {
      grid-template-columns: repeat(3, minmax(0, min(200px, 1fr)));
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, minmax(0, min(220px, 1fr)));
    }

    /* 垂直空间优化 */
    @media (max-height: 800px) {
      gap: 6px;
      /* 减少间距以节省空间 */
    }

    @media (max-height: 600px) {
      gap: 4px;
      /* 进一步减少间距 */
    }

    .element-modal-item {
      width: 100% !important;
      min-width: 0 !important;
      /* 添加最大高度限制，防止元素过大 */
      max-height: 400px !important;
      box-sizing: border-box !important;

      .element-image-wrapper {
        width: 100% !important;
        min-width: 0 !important;
        aspect-ratio: 3/4 !important;
        /* 限制图片区域的最大高度 */
        max-height: 320px !important;
        flex-shrink: 0 !important;

        .element-image-item {
          width: 100% !important;
          height: 100% !important;
          min-width: 0 !important;
          min-height: 0 !important;
        }
      }

      .element-block-item-name {
        width: 100% !important;
        min-width: 0 !important;
        min-height: 60px !important;
        /* 限制文字区域的最大高度 */
        max-height: 80px !important;
        flex-shrink: 0 !important;
        font-size: 14px !important;
        /* 添加文字溢出处理 */
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        padding: 8px 12px !important;

        /* 在小屏幕或高列数时调整字体大小 */
        @media (max-width: 1200px),
        (min-width: 2560px) {
          font-size: 12px !important;
          min-height: 50px !important;
          max-height: 70px !important;
          padding: 6px 10px !important;
        }

        @media (max-height: 800px) {
          font-size: 12px !important;
          min-height: 45px !important;
          max-height: 65px !important;
          padding: 6px 10px !important;
        }

        span {
          display: -webkit-box !important;
          -webkit-line-clamp: 2 !important;
          -webkit-box-orient: vertical !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
          line-height: 18px !important;
          word-break: break-word !important;
          white-space: normal !important;
        }
      }
    }
  }

}