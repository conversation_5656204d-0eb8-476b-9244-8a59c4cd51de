@import "@/app";

@body-width: calc(100vw - @menu-width);
@free-width: 52px;
@color-selector-height: 108px;

.logo-combine-drawer {
  position: relative;
  width: calc(@body-width / 2 - @free-width) !important;
  height: 100% !important;
  background: #FFFFFF;
  box-sizing: border-box;
  /* 中性色/N2-背景色 */
  border-width: 0 0 0 0.5px;
  border-style: solid;
  border-color: #EDEEF3;
  box-shadow: 8px 4px 10px 0px rgba(82, 81, 115, 0.1);
  //overflow: inherit !important;

  .ant-drawer-body {
    padding: 16px !important;
  }
}

.logo-combine-drawer-expend {
  position: fixed;
  left: calc(100vw - @free-width);
  top: calc((100vh - @navigation-height - 126px) / 2);
  width: 24px;
  height: 126px;

  border-radius: 0 256px 256px 0;

  background: #FFFFFF;

  box-shadow: 8px 4px 10px 0 rgba(82, 81, 115, 0.1);
  z-index: 99;
}

.logo-combine-drawer-expend:hover {
  /* 品牌色/B1 */
  background: #F2F3FF;
}

.logo-combine-drawer-contract {
  left: calc(100vw - @body-width / 2);
  right: calc(@body-width / 2 - 24px) !important;
}

.logo-combine-drawer-image-card {
  width: calc(100% - 84px - 24px);
}

.logo-combine-drawer-color-selector {
  height: auto;
  border-radius: 8px;
  padding: 12px;
  background: #F5F6F9;
  box-sizing: border-box;
  border: 0.5px solid #E1E3EB;
}

.logo-combine-drawer-color-block {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid #D8D8D8;;
}

.logo-combine-drawer-color-block-selected {
  border: 2px solid #366EF4 !important;
}

.logo-combine-drawer-bg-block {
  position: relative;
  width: 84px;
  height: 84px;
  border-radius: 8px;
  overflow: hidden;

  background: url("@/assets/images/bg-transparent_grid.png") no-repeat;
  background-size: 84px 84px;
  cursor: pointer;
}

.logo-combine-drawer-bg-tag {
  padding: 4px 8px !important;
}

.logo-combine-drawer-bg-loading {
  position: absolute;
  left: 0;
  top: 0;
  width: 84px;
  height: 84px;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.4);
  padding-top: 18px;
}

.logo-combine-spin {
  .ant-spin-container {
    height: 100%;
  }
  .ant-spin {
    max-height: 100% !important;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .ant-spin-text {
      margin-top: 8px;
    }
  }
}