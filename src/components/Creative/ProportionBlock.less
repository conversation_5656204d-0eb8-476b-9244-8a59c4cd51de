// Experimental label styles
.tag-label-wrapper {
  position: relative;
  overflow: visible;
  pointer-events: none;
}

.tag-label {
  position: absolute;
  top: 0;
  right: 0;
  width: 40px;
  height: 40px;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 40px 40px 0;
    border-color: transparent #ffcc00 transparent transparent;
    z-index: 1;
  }

  span {
    position: absolute;
    top: 10px;
    right: -3px;
    transform: rotate(45deg);
    color: #333;
    font-size: 10px;
    font-weight: bold;
    z-index: 2;
  }
}
