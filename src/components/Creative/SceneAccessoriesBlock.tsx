import React, { FC, useState } from 'react';
import { Checkbox, Flex } from 'antd';
import IconFont from '@/components/IconFont';

interface SceneAccessoriesBlockProps {
  data: any;
  changeData: (value) => void;
}

const ITEMS = [
  { key: 'N', label: '否' },
  { key: 'Y', label: '是' },
];

const SceneAccessoriesBlock: FC<SceneAccessoriesBlockProps> = ({ data, changeData }) => {
  const [value, setValue] = useState<string>(data.useSceneAccessories);

  const changeValue = (value) => {
    changeData({ ...data, useSceneAccessories : value });
    setValue(value);
  };

  return <Flex vertical gap={8}>
    <Flex gap={8} justify={'space-between'} align={'center'}>
      <Flex gap={0} align={'center'} style={{ width: 120 }}>
        <IconFont type={'icon-a-juxing160'} style={{ fontSize: 16, color: '#0052D9', marginLeft: -6 }} />
        <div className={'text14 color-1a weight'}>使用场景配饰</div>
      </Flex>
      <Flex gap={8} align={'center'} wrap={'wrap'} className={'width-100'}>
        {ITEMS.map((item, index) =>
          <div key={index}
               className={'cloth-collocation-preset-item text12 color-72 pointer' + (value === item.key ? ' cloth-collocation-preset-item-selected' : '')}
               onClick={() => changeValue(item.key)}>
            {item.label}
          </div>)
        }
      </Flex>
    </Flex>
  </Flex>;
};

export default SceneAccessoriesBlock;