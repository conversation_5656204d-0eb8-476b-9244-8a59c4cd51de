import { LoraType } from '@/services/CreativeController';
import {
  addCustomElement,
  ElementConfig,
  getElementTypes,
  ProportionType,
  queryCustomElement,
  SceneType,
} from '@/services/ElementController';
import { Badge, Button, Flex, message, notification, Segmented, Tooltip, Upload, UploadFile, UploadProps } from 'antd';
import { ALL_LORAS, ALL_LORAS_11, MORE_LORA_ICON, UPLOAD_PRO_URL } from '@/constants';
import { PlusOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import { compressFile } from '@/utils/imageUtils';
import './ElementSelector.less';
import IconFont from '@/components/IconFont';

interface ElementSelectorProps {
  belong?: LoraType;
  config: ElementConfig;
  current: Array<ElementConfig>;
  title: string;
  multipleOptions?: boolean;
  maxChoose?: number,
  acceptTypes?: Array<string>,
  conditions?: any;
  proportion?: ProportionType;
  pageSize?: number;
  forceProportion?: boolean;
  onChange: (configId: number, value: Array<ElementConfig>) => void;
  changeType?: (type: string) => void;
  changeBelong?: (belong: LoraType) => void;
  onMore?: () => void;
}

const addedElements: Array<string> = [];

const ElementSelector: React.FC<ElementSelectorProps> = ({
                                                           belong = 'SYSTEM',
                                                           config,
                                                           current,
                                                           title,
                                                           multipleOptions = false,
                                                           maxChoose = 8,
                                                           acceptTypes,
                                                           conditions,
                                                           proportion,
                                                           pageSize = 8,
                                                           forceProportion = false,
                                                           onChange,
                                                           changeType,
                                                           changeBelong,
                                                           onMore,
                                                         }) => {

  const [showList, setShowList] = React.useState<Array<ElementConfig>>([]);
  const [belongType, setBelongType] = React.useState<LoraType>(belong);
  const [typeList, setTypeList] = useState<Array<SceneType>>();
  const [showType, setShowType] = useState('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [hasMore, setHasMore] = useState(false);
  const [isModal, setIsModal] = useState(false);

  useEffect(() => {
    setBelongType(belong);
    setIsModal(pageSize > 8);
    // @ts-ignore
    if (config.children) {
      setShowList(config.children.slice(0, pageSize));

      let hasType = config.children.some(item => item.type);

      hasType && getElementTypes(config.configKey).then(res => {
        if (res && res.length > 0) {
          res.forEach(type => {
            if (config && config.children) {
              const elements = config.children.filter(c => c.type && c.type.includes(type.code));
              type.empty = elements.length <= 0 || (acceptTypes && !acceptTypes.some(t => t === type.code));
            }
          });
          setTypeList(res);
          //默认选第一个type
          setShowType(res[0].code);
        }
      });
    }
  }, []);

  useEffect(() => {
    if (belongType === 'SYSTEM') {
      let filter = config.children;
      if (showType) {
        filter = filter.filter(item => item.type && item.type.some(type => type === showType));
      }

      if (conditions && conditions.type) {
        // extInfo.clothStyleImgs.logoLocation
        filter = filter.filter(item => conditions.type.every(type => item.type && item.type.includes(type)));
      }
      if (conditions && conditions.clothStyles) {
        filter = filter.filter(item => conditions.clothStyles.some(style => style.belong === 'CUSTOM' || (item.extInfo && item.extInfo['clothStyles'] && JSON.parse(item.extInfo['clothStyles']).some(e => e === style.id))));
      }

      //条件变更时，过滤已选择的部分
      if (current && current.length > 0) {
        const checked = current.filter(item => filter.some(e => e.id === item.id));
        const customChecked = current.filter(item => item.belong === 'CUSTOM');
        checked.push(...customChecked);
        if (checked.length !== current.length) {
          changeSelectedItems(checked);
        }
      }

      setShowList(filter?.slice(0, pageSize));
      setHasMore(filter?.length > pageSize);
    } else {
      queryCustom(showType);
    }
  }, [showType, belongType, conditions]);

  useEffect(() => {
    if (changeBelong) {
      changeBelong(belongType);
    }
  }, [belongType]);

  useEffect(() => {
    if (changeType) {
      changeType(showType);
    }
  }, [showType]);

  useEffect(() => {
    if (showList && showList.length > 0
      && current && !showList.some(item => current.some(e => e.id === item.id))) {
      if (config.configKey === 'CLOTH_STYLE') {
        handleChange(showList[0]);
      }
    }
  }, [showList]);

  const queryCustom = (showType) => {
    queryCustomElement({ pageSize, pageNum: 1, type: showType, configKey: config.configKey }).then(res => {
      if (res && (res.list != null)) {
        setShowList(res.list);
        setHasMore(res.hasNextPage ? res.hasNextPage : false);
      }
    });
  };

  const needShowMore = () => {
    if (pageSize > 1000) {
      return false;
    }
    return hasMore;
  };

  const handleChange = (item: ElementConfig) => {
    let newItems = current ? [...current] : [];
    //多选的情况下，会有选取、反选，单选时只设置一个值
    if (multipleOptions) {
      if (newItems.some(e => e.id === item.id)) {
        newItems = newItems.filter(e => e.id !== item.id);
      } else {
        newItems = [...newItems, item];
      }
      if (newItems.length > maxChoose) {
        message.warning(`最多可以选择${maxChoose}个${title}`);
        return;
      }
    } else {
      newItems = [item];
    }

    changeSelectedItems(newItems);
  };

  const changeSelectedItems = (newItems: Array<ElementConfig>) => {
    if (onChange) {
      onChange(config.id, newItems);
    }
  };

  const beforeUpload = async (file) => {
    return await compressFile(file) as File;
  };

  const handleUploadChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    const file = newFileList[newFileList.length - 1];

    if (file && file.response && !file.response.success) {
      notification.error({ message: '上传图片异常，请重试' });
      setFileList([]);
      return;
    }

    if (file && file.response && file.response.success) {
      const showImage = file.response.data;
      if (addedElements.indexOf(showImage) === -1) {
        addedElements.push(showImage);

        addCustomElement({ showImage, configKey: config.configKey, tag: showType, name: '我的' + title }).then(res => {
          if (res) {
            setBelongType('CUSTOM');
            queryCustom(showType);
          }
        });
      }

    }
    setFileList([file]);
  };

  const SelectedReferBlock = () => {
    return <Flex vertical gap={8} className={'element-selected-block'}>
      <Flex gap={8} justify={'flex-start'} align={'center'} className={'scene-item-block no-min-height'}>
        {current && current.map(
          (item) => (
            <div key={item.id} className={'work-item element-selected-image-container border-selected'}>
              <div onClick={() => handleChange(item)} style={{ borderRadius: 8 }}
                   className={'element-image-wrapper' + (proportion === 'THREE_FOUR' ? ' element-selected-image-wrapper-34' : ' element-selected-image-wrapper-11')}>
                <img src={item.showImage} alt={''} className={'element-image-item'} />
                <div style={{ position: 'absolute', right: 4, top: 4 }}>
                  <IconFont type={'icon-gou-lan'} style={{ fontSize: 16, color: '#366EF4' }} />
                </div>
              </div>
            </div>
          ))
        }
        {(!current || current.length <= 0) &&
          <Flex vertical gap={8} align={'center'} justify={'center'} style={{ padding: 17, width: '100%' }}>
            <IconFont type={'icon-kongzhuangtai'} style={{ fontSize: 24, color: '#FFFFFF' }} />
            <div className={'text16 font-pf color-e1'}>未选择参考图</div>
          </Flex>
        }
      </Flex>
      <Flex justify={'space-between'}>
        <div className={'text16 weight color-f5'}>已选择{current ? current.length : 0}/{maxChoose}</div>
        <div className={'text16 color-e1 pointer'} onClick={() => changeSelectedItems([])}>全部取消</div>
      </Flex>
    </Flex>;
  };

  return <div className={'scene-inner-container no-min-height'} style={{ gap: 8 }}>

    {multipleOptions && isModal &&
      <div className={'element-selected-container'}>
        {/*调整当前组件中的ant-tooltip最大宽度*/}
        <SelectedReferBlock />
      </div>
    }

    {/*场景分类tabs*/}
    <Flex justify={'space-between'} gap={8}>
      <Segmented
        value={belongType}
        style={{ backgroundColor: '#E1E3EB' }}
        onChange={(value) => setBelongType(value as LoraType)}
        options={[{ label: '预设' + title, value: 'SYSTEM' }, { label: '我的' + title, value: 'CUSTOM' }]}
      />

      <Flex gap={8}>
        {typeList && typeList.map(s => (
          <Button key={s.code} onClick={() => setShowType(s.code)} disabled={s.empty}
                  className={showType === s.code ? 'scene-type-item scene-type-item-checked' : 'scene-type-item'}>
            {s.name}
          </Button>
        ))}

        {multipleOptions && !isModal &&
          <div className={'margin-right-12'}>
            {/*调整当前组件中的ant-tooltip最大宽度*/}
            <style>{`.ant-tooltip { max-width: 100vw !important;}`}</style>
            <Tooltip title={<SelectedReferBlock />} arrow={false} placement="bottomRight">
              <Badge dot color="#366EF4">
                <a className={'text14 font-pf color-brand'}>
                  查看已选图片{current ? current.length : 0}/{maxChoose}&nbsp;
                </a>
              </Badge>
            </Tooltip>
          </div>
        }
      </Flex>
    </Flex>

    <Flex wrap className={'scene-item-block' + (isModal ? '' : ' item-block')}>
      <Flex vertical align={'center'} justify={'center'}
            className={'work-item' + (isModal ? ' element-modal-item' : '')}>
        <Upload
          listType="picture-card"
          className="avatar-uploader"
          showUploadList={false}
          fileList={fileList}
          action={UPLOAD_PRO_URL}
          beforeUpload={beforeUpload}
          onChange={handleUploadChange}
          accept="image/png, image/jpeg"
        >
          <PlusOutlined style={{ fontSize: 24 }} className={'color-brand'} />
        </Upload>
        <div className={'text14 font-pf color-n margin-top-4 margin-bottom-4 text-center'}>
          上传我的{title}
        </div>
      </Flex>

      {showList.map((item) => (
        <div key={item.id}
             className={'work-item' + (isModal ? ' element-modal-item' : '') + (current && current.some(e => e.id === item.id) ? ' work-item-selected' : '')}
             style={{ position: 'relative' }} onClick={() => handleChange(item)}>
          <div
            className={'element-image-wrapper' + (proportion === 'THREE_FOUR' ? ' element-image-wrapper-34' : ' element-image-wrapper-11') + (forceProportion ? '-forced' : '')}>
            <img alt="img" src={item.showImage} className={'element-image-item' + (forceProportion ? '-forced' : '')} />

            {multipleOptions && current && current.some(e => e.id === item.id) &&
              <div style={{ position: 'absolute', right: 4, top: 4 }}>
                <IconFont type={'icon-gou-lan'} style={{ fontSize: 32, color: '#366EF4' }} />
              </div>
            }
          </div>
          <div className={'text14 font-pf color-n margin-top-4 margin-bottom-4 text-center'}>
            {item.name}
          </div>
        </div>
      ))}

      {needShowMore() &&
        <Flex vertical align={'center'} justify={'center'}
              className={'work-item' + (isModal ? ' element-modal-item' : '')}
              onClick={onMore} style={{ position: 'relative' }}>
          <div
            className={'element-image-wrapper' + (proportion === 'THREE_FOUR' ? ' element-image-wrapper-34' : ' element-image-wrapper-11') + (forceProportion ? '-forced' : '')}>
            <img src={proportion === 'THREE_FOUR' ? ALL_LORAS : ALL_LORAS_11} alt={'all'}
                 className={'element-image-item' + (forceProportion ? '-forced' : '')} />
            <div className={'more-lora-block'}>
              <img src={MORE_LORA_ICON} alt={'more'} style={{ width: 32, height: 32 }} />
              <div className={'text14 font-pf weight color-w text-center margin-top-4'}>全部{title}</div>
            </div>
          </div>

          <div
            className={'text14 font-pf color-n margin-top-4 margin-bottom-4 text-center'}>更多
          </div>
        </Flex>
      }

    </Flex>
  </div>;
};

export default ElementSelector;