.preference {
  &-block-container {
    padding: 12px 16px;
    background-color: #fff;
    border-radius: 8px;
    border: 0.8px solid #ebedf0;
  }

  &-block-label {
    height: 26px;
    border-radius: 32px;
    opacity: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 4px 16px;
    box-sizing: border-box;
    border: 1px solid #B5C7FF;
  }

  &-block-label:hover {
    border: 1px solid #618DFF;
    box-shadow: 4px 4px 10px 0 rgba(0, 0, 0, 0.1);
  }

  &-block-label-selected {
    background: #D9E1FF;
    height: 26px;
    border-radius: 32px;
    opacity: 1;

    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 4px 16px;

    box-sizing: border-box;
    border: 1px solid #B5C7FF;
  }

  &-block-label-item {
    margin-right: 4px;
  }

  &-block-label-delete {
    display: flex;
    margin-left: 4px;
    align-items: center;
    cursor: pointer;
    color: #dbe5ff;
    font-size: 8px;
    line-height: 1;
    transition: color 0.3s ease
  }

  &-block-label-delete:hover {
    color: #5a76d6;
  }

  &-block-save-button {
    border-radius: 32px;
    height: 26px;
    padding: 0 16px;
    font-size: 12px
  }

  &-block-info {
    padding: 16px; // 内边距

    ul {
      list-style-type: none; // 去掉列表样式
      padding: 0; // 去掉内边距
      margin: 0; // 去掉外边距

      li {
        margin-bottom: 8px; // 列表项底部外边距
        font-size: 14px; // 列表项字体大小
        color: #555; // 列表项颜色

        strong {
          color: #333; // 加粗文本颜色
        }
      }
    }
  }


  &-block-label {
    height: 26px;
    border-radius: 32px;
    opacity: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 4px 16px;
    box-sizing: border-box;
    border: 1px solid #B5C7FF;
  }

  &-block-title {

  }
}