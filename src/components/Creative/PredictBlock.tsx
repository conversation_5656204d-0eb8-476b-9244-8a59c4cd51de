import { Flex, Tooltip } from 'antd';
import IconFont from '@/components/IconFont';
import { formatMillennials } from '@/utils/format';
import React from 'react';
import { ExclamationCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { CreativeType } from '@/services/CreativeController';
import { getUserInfo } from '@/utils/utils';

interface PredictBlockProps {
  creativeType: CreativeType;
  type?: string;
  imagePoint?: number;
  data?: any;
  predictVO: any;
  font?: number;
}

const PredictBlock: React.FC<PredictBlockProps> = ({ creativeType, type = 'CUSTOM', imagePoint, data, predictVO, font = 24 }) => {
  const userInfo = getUserInfo();
  return (
    <Flex vertical justify={'center'} align={'flex-end'} gap={4} >
      <Flex className={'text16 font-pf weight color-1a'} align={'center'} justify={'center'}>
        本次生成需消耗
        {type === 'SYSTEM' &&
          <>
            <IconFont type={'icon-icon_tupian'} style={{ fontSize: 24 }}  />
            {userInfo && userInfo.roleType === 'DISTRIBUTOR' ? 0 : data.imageNum}体验点
          </>
        }
        {type === 'CUSTOM' &&
          <>
            {predictVO &&
              <>
                {predictVO.modelPoint &&
                  <>
                    <IconFont type={'icon-icon_tupian'} style={{ fontSize: font }} />
                    {predictVO.modelPoint}创作图
                  </>
                }
                {predictVO.givePoint &&
                  <>
                    <IconFont type={'icon-icon_tupian'} style={{ fontSize: font }} />
                    {predictVO.givePoint}赠送创作图
                  </>
                }
                {predictVO.musePoint != null &&
                  <>
                    <IconFont type={'icon-icon_mousidian'} style={{ fontSize: font }} />
                    {predictVO.musePoint}缪斯点
                  </>
                }
                {!predictVO.modelPoint && !predictVO.givePoint && !predictVO.musePoint &&
                  <span>本次创作免费</span>
                }
              </>
            }
            {!predictVO &&
              <>
                <IconFont type={'icon-icon_tupian'} style={{ fontSize: font }} />
                {data ? data.imageNum : 0}创作图
              </>
            }
            {creativeType === 'CREATE_IMAGE' &&
              <Tooltip title="创作每张图片扣除顺序：服装套餐图片张数->赠送创作图->缪斯点。"
                       styles={{ body: { width: 198 } }}>
                <QuestionCircleOutlined style={{ fontSize: 16 }} className={'margin-left-4 color-96'} />
              </Tooltip>
            }
          </>
        }
      </Flex>
      <Flex justify={'flex-end'} gap={8}>
        {type === 'SYSTEM' &&
          <div className={'text14 font-pf color-96'}>剩余{formatMillennials(imagePoint)}体验点</div>
        }
        <Flex className={'text14 font-pf color-96'} align={'center'} justify={'center'}>
          <a target={'_blank'} href={'disclaimer.html'} className={'color-96'}>免责声明 <ExclamationCircleOutlined
            style={{ fontSize: 16 }} className={'color-96'} /></a>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default PredictBlock;