@import "@/app";

@image-width: calc((1184px - 16px * 7) / 5);
@content-height: calc(46px + 8px + 32px + 8px + @image-width * 2 + 16px + (20px * 2 + 8px) * 2 - 12px);

.image-selector-selected {
  width: 1136px;
  min-height: 82px;
  border-radius: 8px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.8);
}

.image-selector-selected-item {
  width: auto !important;
}

.image-selector-selected-image {
  width: auto;
  height: 66px;
}

.image-selector-history-wrapper {
  min-height: @content-height;
  height: auto;

  .ant-tabs-tab {
    width: 102px;
    justify-content: center;
  }
}

.image-selector-history-block {
  max-height: @content-height;
  overflow-y: auto;
}

.image-selector-round {
  position: absolute;
  right: 8px;
  bottom: 8px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  background: rgba(255, 255, 255, 0.6);
}