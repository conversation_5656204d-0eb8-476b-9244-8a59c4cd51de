import { MaterialModel } from '@/services/MaterialModelController';
import { CloseCircleFilled, SearchOutlined } from '@ant-design/icons';
import { Button, Flex, Input, Popover, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import IconFont from '../IconFont';
import { queryDictByKeyAndTags } from '@/services/PromptDictController';

interface ColorOption {
  englishName: string;
  chineseName: string;
  colorValue: string;
}

interface ClothColorBlockProps {
  value: string;
  model: MaterialModel | null | undefined;
  onChange: (value: string) => void;
}

// 验证十六进制颜色代码的函数
const isValidHexColor = (color: string): boolean => {
  // 检查是否为空
  if (!color) return false;
  
  // 标准十六进制颜色格式：#RRGGBB 或 #RGB
  const hexRegex = /^#([A-Fa-f0-9]{3}){1,2}$/;
  return hexRegex.test(color);
};

// 确保颜色值有效，如果无效则返回默认颜色
const ensureValidColor = (color: string): string => {
  if (isValidHexColor(color)) {
    return color;
  }
  return '#FFFFFF';
};

const ClothColorBlock: React.FC<ClothColorBlockProps> = ({
  value,
  model,
  onChange,
}) => {
  const [selectedColor, setSelectedColor] = useState<string>(value || '');
  const [searchText, setSearchText] = useState<string>('');
  const [predefinedColors, setPredefinedColors] = useState<ColorOption[]>([]);
  const [filteredColors, setFilteredColors] = 
    useState<ColorOption[]>([]);

  useEffect(() => {
    queryDictByKeyAndTags('CLOTH_COLOR', [['color']]).then(res => {
      if (res) {
        const colors = res.color?.map(item => {
          return {
            englishName: item.prompt,
            chineseName: item.word,
            colorValue: ensureValidColor(item.memo),
          };
        }) || [];
        setPredefinedColors(colors);
        setFilteredColors(colors);
      }
    });
  }, []);

  useEffect(() => {
    // 初始化时，如果没有选择颜色，设置为空
    if (!value) {
      setSelectedColor('');
      onChange('');
    } else {
      setSelectedColor(value);
    }
  }, [value]);

  // 处理搜索
  const handleSearch = (text: string) => {
    setSearchText(text);
    if (!text) {
      setFilteredColors(predefinedColors);
      return;
    }

    const filtered = predefinedColors.filter(
      (color) =>
        color.englishName.toLowerCase().includes(text.toLowerCase()) ||
        color.chineseName.includes(text),
    );
    setFilteredColors(filtered);
  };

  // 处理颜色选择
  const handleColorSelect = (color: ColorOption) => {
    setSelectedColor(`${color.englishName}/${color.chineseName}`);
    onChange(`${color.englishName}/${color.chineseName}`);
  };
  
  // 处理输入框变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSelectedColor(value);
    
    // 如果是空值，更新为空
    if (value === '') {
      onChange('');
      return;
    }
    
    // 如果是有效的颜色名称格式，直接更新
    if (value.includes('/')) {
      onChange(value);
      return;
    }
    
    // 如果是有效的十六进制颜色值
    if (isValidHexColor(value)) {
      // 尝试查找匹配的颜色名称，但不修改用户输入
      const matchingColor = predefinedColors.find(
        color => color.colorValue.toLowerCase() === value.toLowerCase()
      );
      
      // 无论是否找到匹配的颜色，都保持用户原始输入
      onChange(value);
    }
  };

  // 清除颜色选择
  const handleClearColor = (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止点击事件冲突
    setSelectedColor('');
    onChange('');
    setSearchText('');
    setFilteredColors(predefinedColors);
  };

  // 根据颜色名称获取颜色值
  const getColorValueByName = (colorName: string): string => {
    if (!colorName) return '';

    const [englishName] = colorName.split('/');
    const colorOption = predefinedColors.find(
      (color) => color.englishName === englishName,
    );
    return colorOption ? ensureValidColor(colorOption.colorValue) : 'rgba(0, 0, 0, 0)';
  };

  // 颜色卡片组件
  const ColorCard = ({ color }: { color: ColorOption }) => {
    const isSelected =
      selectedColor === `${color.englishName}/${color.chineseName}`;

    return (
      <Tooltip title={`${color.englishName} / ${color.chineseName}`}>
        <div
          className={`color-card ${isSelected ? 'selected' : ''}`}
          onClick={() => handleColorSelect(color)}
          style={{
            backgroundColor: color.colorValue,
            width: '80px',
            height: '80px',
            margin: '4px',
            borderRadius: '4px',
            cursor: 'pointer',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            border: isSelected ? '2px solid #1890ff' : '1px solid #d9d9d9',
            boxShadow: isSelected ? '0 0 5px rgba(24, 144, 255, 0.5)' : 'none',
            padding: '2px',
          }}
        >
          <div
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.8)',
              padding: '2px 4px',
              borderRadius: '2px',
              fontSize: '9px',
              textAlign: 'center',
              width: '95%',
              wordBreak: 'break-word',
              overflow: 'hidden',
              maxHeight: '80%',
            }}
          >
            <div style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
              {color.englishName}
            </div>
            <div style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
              {color.chineseName}
            </div>
          </div>
        </div>
      </Tooltip>
    );
  };

  // 颜色选择器内容
  const colorPickerContent = (
    <div style={{ width: '300px', height: '400px', display: 'flex', flexDirection: 'column' }}>
      <div style={{ padding: '8px 8px 0 8px', position: 'sticky', top: 0, backgroundColor: '#fff', zIndex: 1 }}>
        <Input
          placeholder="搜索颜色名称"
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => handleSearch(e.target.value)}
          style={{ marginBottom: '8px' }}
        />
      </div>
      <div style={{ flex: 1, overflowY: 'auto', padding: '0 8px' }}>
        <Flex wrap="wrap" gap={4} justify="start" style={{ minHeight: '300px' }}>
          {filteredColors.map((color, index) => (
            <ColorCard key={index} color={color} />
          ))}
        </Flex>
        {filteredColors.length === 0 && (
          <div style={{ textAlign: 'center', padding: '16px 0' }}>
            没有找到匹配的颜色
          </div>
        )}
      </div>
    </div>
  );

  return (
    <Flex
      vertical
      justify={'flex-start'}
      align={'flex-start'}
      className={'work-item-container'}
    >
      <Flex>
        <div className={'text16 font-pf color-n weight'}>自定义服装颜色</div>
        <Tooltip
          title={'修改服装的默认颜色，纯色服装效果更佳'}
          placement="topRight"
        >
          <IconFont type={'icon-yiwen1'} style={{ fontSize: 18 }} />
        </Tooltip>
      </Flex>
      <Flex gap={8} className={'width-100'} style={{ marginTop: '8px' }}>
        <Input.Group compact style={{ width: '100%', display: 'flex' }}>
          <Popover
            content={colorPickerContent}
            title="选择颜色"
            trigger="click"
            placement="bottomLeft"
            overlayStyle={{ width: '320px' }}
          >
            <Button style={{ width: 'auto' }}>
              <SearchOutlined />
            </Button>
          </Popover>
          <Input
            placeholder="输入或选择颜色"
            value={selectedColor}
            onChange={handleInputChange}
            style={{ flex: 1 }}
            prefix={null}
            suffix={
              selectedColor ? (
                <>
                  <div
                    style={{
                      width: '16px',
                      height: '16px',
                      borderRadius: '2px',
                      backgroundColor: getColorValueByName(selectedColor),
                      marginRight: '8px',
                    }}
                  />
                  <CloseCircleFilled
                    style={{ color: '#999', cursor: 'pointer' }}
                    onClick={handleClearColor}
                  />
                </>
              ) : <span />
            }
          />
        </Input.Group>
      </Flex>
    </Flex>
  );
};

export default ClothColorBlock;
