import { But<PERSON>, Checkbox, DatePicker, Divider, Flex, message, Modal, Pagination, Select, Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import './ImageSelector.less';
import IconFont from '@/components/IconFont';
import {
  ALLCreativeType,
  CreativeVO,
  queryCreativeById,
  queryCreativesByPage,
} from '@/services/CreativeController';
import { MaterialModel } from '@/services/MaterialModelController';
import ImageCardBlock from '@/components/History/ImageCardBlock';
import WatermarkImage from '@/components/WatermarkImage';
import { ZoomInOutlined } from '@ant-design/icons';
import ImgPreview from '@/components/ImgPreview';

const { RangePicker } = DatePicker;

let typeNotIn = ['CREATE_VIDEO', 'LOGO_COMBINE', 'FIX_VIDEO_FACE', 'CLOTH_AUTO_SEGMENT'];

interface ImageSelectorProps {
  value: Array<any>;
  maxChoose?: number;
  excludesType?: Array<string> | null;
  onFinish: (value: Array<any>, modelId?: number ) => void;
  onCancel: () => void;
}

const ImageSelector: React.FC<ImageSelectorProps> = ({ value = [], maxChoose = 4, excludesType, onFinish, onCancel }) => {
  const [selected, setSelected] = useState<Array<string>>(value);
  const [batchDetail, setBatchDetail] = useState<CreativeVO | null>(null);

  const [dataList, setDataList] = useState<Array<CreativeVO>>([]);
  const [total, setTotal] = useState(0);
  const [searchType, setSearchType] = useState<string | null>(null);
  const [dates, setDates] = useState([]);
  const [loraModels, setLoraModels] = useState<Array<MaterialModel>>([]);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [onlyShowExampleImg, setOnlyShowExampleImg] = useState(false);

  const [previewImg, setPreviewImg] = useState<string | null>(null);
  const [previewImgList, setPreviewImgList] = useState<Array<string>>([]);
  const [selectedModelId, setSelectedModelId] = useState<number|undefined>();

  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

  if(excludesType){
    typeNotIn = [...typeNotIn, ...excludesType];
  }

  useEffect(() => {
    fetchData();
  }, [page, pageSize]);

  useEffect(() => {
    setPage(1)
    fetchData();
  }, [dates, selectedModelId, searchType, onlyShowExampleImg]);

  async function fetchData() {
    let query = {
      orderBy: 'id desc',
    };

    if (dates && dates.length > 1) {
      // @ts-ignore
      query['dateFrom'] = dates[0].format('YYYYMMDD');
      // @ts-ignore
      query['dateTo'] = dates[1].format('YYYYMMDD');
    }

    //只看精选图
    if (onlyShowExampleImg) {
      query['bizTag'] = 'exampleImages';
    }

    if (selectedModelId) {
      query['modelId'] = selectedModelId;
    }

    if (page && pageSize) {
      query['pageNum'] = page;
      query['pageSize'] = pageSize;
    }

    if (searchType) {
      query['type'] = searchType === 'ALL' ? null : searchType;
    }

    if (typeNotIn) {
      query['typeNotIn'] = typeNotIn;
    }

    query['status'] = 'FINISHED';

    let ret = await queryCreativesByPage(query);
    if (ret) {
      setDataList(ret?.list || []);
      if (ret?.totalCount !== null && ret?.totalCount !== undefined) {
        setTotal(ret.totalCount);
      }
    }
  }

  const handleDateChange = (newDates) => {
    setPage(1);
    setDates(newDates);
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setPage(page);
    if (pageSize) {
      setPageSize(pageSize);
    }
  };

  const handleChange = (imgUrl: string, modelId?: number) => {
    setSelectedModelId(modelId);
    
    let newItems = selected ? [...selected] : [];

    if (newItems.some(e => e === imgUrl)) {
      newItems = newItems.filter(e => e !== imgUrl);
    } else {
      newItems = [...newItems, imgUrl];
    }
    if (newItems.length > maxChoose) {
      message.warning(`最多可以选择${maxChoose}个`);
      return;
    }

    setSelected(newItems);
  };

  const showBatchDetail = (item: CreativeVO) => {
    //走服务端查一下这个创作的最新信息
    queryCreativeById(item.id).then(res => {
      if (res) {
        setBatchDetail(item);
      }
    });
  };

  const handlePreviewImg = (imgUrl, allImages) => {
    setPreviewImg(imgUrl);
    setPreviewImgList(allImages);
  };

  const tabs = [{ key: 'ALL', label: '全部' },
    ...ALLCreativeType.filter(e => !typeNotIn.some(i => i === e.key) && (userInfo.roleType === 'OPERATOR' || e.key !== 'IMAGE_UPSCALE'))];

  const SelectableImage = ({ imgUrl, modelId, allImages }) => (
    <div style={{ position: 'relative', borderRadius: 8, overflow: 'hidden', cursor: 'pointer' }}>
      <WatermarkImage src={imgUrl} height={200} onClick={() => handleChange(imgUrl, modelId)} />
      {selected.some(e => e === imgUrl) &&
        <div style={{ position: 'absolute', right: 8, top: 8 }} onClick={() => handleChange(imgUrl, modelId)}>
          <IconFont type={'icon-gou-lan'} style={{ fontSize: 32, color: '#366EF4' }} />
        </div>
      }
      <div className={'image-selector-round'} onClick={() => handlePreviewImg(imgUrl, allImages)}>
        <ZoomInOutlined style={{ fontSize: 16 }} />
      </div>
    </div>
  );

  return <Modal open={true} width={1184} centered maskClosable={false} footer={null} onCancel={onCancel}>
    <Flex vertical gap={16} style={{ height: 'auto' }}>
      <Flex align={'center'} justify={'center'} className={'text20 weight color-1a'}>历史创作</Flex>

      <Flex className={'image-selector-selected'} wrap={'wrap'} gap={8}>
        {!selected || selected.length <= 0 &&
          <Flex vertical gap={8} align={'center'} justify={'center'} style={{ width: '100%' }}>
            <IconFont type={'icon-kongzhuangtai'} style={{ fontSize: 24, color: '#FFFFFF' }} />
            <div className={'text16 font-pf color-e1'}>未选择</div>
          </Flex>
        }
        {selected && selected.length > 0 && selected.map((item) => (
          <div key={item} className={'work-item image-selector-selected-item'}>
            <div onClick={() => handleChange(item)} style={{ borderRadius: 8 }}
                 className={'element-image-wrapper'}>
              <img src={item} alt={''} className={'image-selector-selected-image'} />
              <div style={{ position: 'absolute', right: 4, top: 4 }}>
                <IconFont type={'icon-gou-lan'} style={{ fontSize: 16, color: '#366EF4' }} />
              </div>
            </div>
          </div>
        ))}
      </Flex>

      <Flex vertical gap={8}>

        <Flex vertical className={'image-selector-history-wrapper'} gap={8}>
          <Flex vertical hidden={!!batchDetail} className={'image-selector-history-wrapper'} gap={8}>
            <Tabs defaultActiveKey="1" items={tabs} onChange={key => setSearchType(key)} size={'middle'}
                  indicator={{ size: 102 }} className={'width-100'} style={{ marginTop: -16 }} />

            <Flex align={'center'} justify={'flex-end'} style={{ marginTop: -16 }} gap={8}>
              <Checkbox onChange={e => setOnlyShowExampleImg(e.target.checked)}>只看精选图</Checkbox>
              <RangePicker onChange={handleDateChange} placeholder={['开始日期', '结束日期']} />

              {searchType === 'CREATE_IMAGE' &&
                <Select value={selectedModelId} placeholder="请选择服装"
                        onChange={(value) => setSelectedModelId(value)}
                        style={{ width: 200 }} allowClear showSearch optionFilterProp="label"
                        options={loraModels ? loraModels.map(lm => ({ label: lm.name, value: lm.id })) : []} />
              }
            </Flex>

            <Flex wrap={'wrap'} gap={16}>
              {dataList.map(item => (
                <ImageCardBlock key={item.id} item={item} onClick={(e) => showBatchDetail(e)}
                                width={'calc((1184px - 16px * 7) / 5)'} isCompact={true} />
              ))}

            </Flex>
          </Flex>

          {batchDetail && (
            <>
              <Flex align={'center'} justify={'flex-start'} gap={8}>
                <Button onClick={() => {setBatchDetail(null);setSelectedModelId(undefined);}}
                        icon={<IconFont type={'icon-youjiantou_16px'}
                                        style={{ transform: 'rotate(180deg)', fontSize: 16 }} />}>
                  返回
                </Button>
              </Flex>

              <Flex wrap={'wrap'} gap={16} className={'image-selector-history-block'}>
                {batchDetail.resultImages?.map((img, index) => (
                  <SelectableImage key={index} imgUrl={img} modelId={batchDetail.modelId} allImages={batchDetail.resultImages} />
                ))}
              </Flex>
            </>
          )}
        </Flex>

        <Divider style={{ height: 0, margin: '-4px 0 -8px 0' }} />

        <Flex align={'center'} justify={'space-between'} style={{ marginTop: '8px' }}>
          <Flex hidden={!!batchDetail}>
            <Pagination
              current={page}
              pageSize={pageSize}
              total={total}
              onChange={handlePageChange}
              showSizeChanger={false} // 允许用户更改每页显示条数
              showQuickJumper // 允许用户快速跳转到某一页
              style={{ textAlign: 'center' }}
            />
          </Flex>

          {batchDetail &&
            <div></div>
          }

          <Flex gap={8}>
            <Button onClick={onCancel}>取消</Button>
            <Button type={'primary'} onClick={() => onFinish(selected, selectedModelId)}>确定</Button>
          </Flex>
        </Flex>
      </Flex>
    </Flex>

    {previewImg !== null &&
      <ImgPreview
        previewVisible={!!previewImg}
        handleCancelPreview={() => {
          setPreviewImg(null);
          setPreviewImgList([]);
        }}
        previewImage={previewImg}
        needSwitch={!!previewImgList}
        previewIdx={previewImgList.findIndex(item => item === previewImg)}
        previewImgs={previewImgList}
        showTools={false}
      />
    }
  </Modal>;
};

export default ImageSelector;