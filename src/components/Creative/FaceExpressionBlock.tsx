import React, { FC, useState } from 'react';
import { Flex } from 'antd';
import IconFont from '@/components/IconFont';

interface FaceExpressionBlockProps {
  data: any;
  changeData: (value) => void;
}

const EXPRESSION_CONFIG = [
  { key: 'cold', label: '冷酷' },
  { key: 'calm', label: '平静' },
  { key: 'smile', label: '微笑' },
  { key: 'laugh', label: '大笑' },
];

const FaceExpressionBlock: FC<FaceExpressionBlockProps> = ({ data, changeData }) => {
  const [value, setValue] = useState(data.expression);

  const changeExpression = (value) => {
    if(value === data.expression){
      setValue(null);
      changeData({ ...data, expression: value });
      return;
    }
    setValue(value);
    changeData({ ...data, expression: value });
  };

  return <Flex vertical gap={8}>
    <Flex gap={8} justify={'space-between'} align={'center'}>
      <Flex gap={0} align={'center'} style={{ width: 44 }}>
        <IconFont type={'icon-a-juxing160'} style={{ fontSize: 16, color: '#0052D9', marginLeft: -6 }} />
        <div className={'text14 color-1a weight'}>表情</div>
      </Flex>
      <Flex gap={8} align={'center'} wrap={'wrap'} className={'width-100'}>
        {EXPRESSION_CONFIG.map((item, index) =>
          <div key={index}
               className={'cloth-collocation-preset-item text12 color-72 pointer' + (value === item.key ? ' cloth-collocation-preset-item-selected' : '')}
               onClick={() => changeExpression(item.key)}>
            {item.label}
          </div>)
        }
      </Flex>
    </Flex>
  </Flex>;
};

export default FaceExpressionBlock;