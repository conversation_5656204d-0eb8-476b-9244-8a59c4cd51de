import IconFont from '@/components/IconFont';
import { Flex, Input } from 'antd';
import React, { useEffect, useState } from 'react';
import './ClothCollocationBlock.less';
import TextArea from 'antd/lib/input/TextArea';
import { deepCopy, getUserInfo } from '@/utils/utils';
import { MerchantSubPreference, queryCreativePreference } from '@/services/SystemController';
import { ClothCollocation, emptyClothCollocation } from '@/services/MaterialModelController';
import { PromptDictVO, querySystemCollocation } from '@/services/PromptDictController';
import MerchantPreferenceBlock, { MerchantPreferenceVO } from '@/components/Creative/MerchantPreferenceBlock';

export interface ClothCollocationDetail {
  id?: number;
  name: string;
  collocation: ClothCollocation | null;
}

interface ClothCollocationBlockProps {
  data: any;
  type: string | null;
  onChange: (data: any, isPreset?: boolean) => void;
  defaultExpend?: boolean;
}

// 配置化的搭配项目信息
interface CollocationItemConfig {
  key: keyof ClothCollocation;
  title: string;
  description: string;
  maxLength?: number;
  presetItems: string[];
}

const collocationItemsConfig: CollocationItemConfig[] = [
  {
    key: 'shoe',
    title: '鞋子',
    description: '在这里输入你希望为服装搭配的一款鞋子',
    presetItems: ['运动鞋', '高跟鞋', '马丁鞋', '居家袜子', '光脚'],
  },
  {
    key: 'tops',
    title: '上装',
    description: '在这里输入你希望为服装搭配的一款上装',
    presetItems: ['运动内衣', 'T恤'],
  },
  {
    key: 'bottoms',
    title: '下装',
    description: '在这里输入你希望为服装搭配的一款下装',
    presetItems: ['牛仔裤', '短裙', '长裤'],
  },
  {
    key: 'others',
    title: '配饰',
    description: '在这里输入你希望出现在画面的一些配饰',
    maxLength: 30,
    presetItems: ['针织帽子', '夸张的配饰耳环', '鸭舌帽', '斜挎包'],
  },
  {
    key: 'props',
    title: '道具',
    description: '在这里输入你希望出现在画面的一些道具',
    maxLength: 30,
    presetItems: ['咖啡杯', '时尚杂志', '登山杖', '手捧花'],
  },
];

const userInfo = getUserInfo();

const ItemBlock = ({ title, description, value, presetItems, maxLength = 20, onChange }) => {
  const [inputValue, setInputValue] = useState(value);

  // 添加预设项到输入框
  const append = (value) => {
    let res = inputValue ? `${inputValue},${value}` : value;
    if (res.length > maxLength) {
      res = res.substr(0, maxLength);
    }
    setInputValue(res);
    onChange(res);
  };

  const handleChange = (e) => {
    const value = e.target.value;
    setInputValue(value);
    onChange(value);
  };

  const buildDescription = (description: string) => {
    const text = title === '其他' ? '其他搭配' : title;
    const index = description.indexOf(text);
    if (index < 0) {
      return description;
    }
    return <>
      {description.substr(0, index)}
      <span className={'light-text'}>{text}</span>
      {description.substr(index + text.length, description.length)}
    </>;
  };

  // 当外部value变化时更新内部状态
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  return <Flex vertical gap={8}>
    <Flex gap={8} justify={'space-between'} align={'center'}>
      <Flex gap={0} align={'center'} style={{ width: 44 }}>
        <IconFont type={'icon-a-juxing160'} style={{ fontSize: 16, color: '#0052D9', marginLeft: -6 }} />
        <div className={'text14 color-1a weight'}>{title}</div>
      </Flex>

      {maxLength <= 30 &&
        <div style={{ position: 'relative' }} className={'width-100'}>
          <Input showCount maxLength={maxLength} allowClear
                 style={{ height: 44 }} value={inputValue} onChange={handleChange} />
          {!inputValue &&
            <div style={{ position: 'absolute', top: 12, left: 8 }}
                 className={'text14 color-96'}>{buildDescription(description)}</div>
          }
        </div>
      }

      {maxLength > 30 &&
        <TextArea placeholder={description} showCount value={inputValue} onChange={handleChange}
                  maxLength={maxLength} style={{ height: 72 }} />
      }
    </Flex>

    <Flex gap={8}>
      <div style={{ width: 44 }}></div>
      <Flex gap={8} align={'center'} wrap={'wrap'}>
        <div className={'text12 color-96'}>试试这些</div>
        {presetItems.map((item, index) =>
          <div key={index} className={'cloth-collocation-preset-item text12 color-72 pointer'}
               onClick={() => append(item)}>
            {item}
          </div>)
        }
      </Flex>
    </Flex>
  </Flex>;
};

// 使用collocationItemsConfig中的键来动态构建条件判断
const needExpend = (clothCollocation: ClothCollocation | null) => {
  if (!clothCollocation) return false;
  // 使用some方法检查是否至少有一个属性存在
  return collocationItemsConfig.some(config => !!clothCollocation[config.key]);
};

const isEmpty = (clothCollocation: ClothCollocation) => {
  // 使用every方法检查是否所有属性都不存在
  return collocationItemsConfig.every(config => !clothCollocation[config.key]);
};

const ClothCollocationBlock: React.FC<ClothCollocationBlockProps> = ({
                                                                       data,
                                                                       type,
                                                                       onChange,
                                                                       defaultExpend = false,
                                                                     }) => {
  const [clothCollocation, setClothCollocation] = useState<ClothCollocation | null>(null);
  const [preferences, setPreferences] = React.useState<Array<MerchantSubPreference>>([]);
  const [systemCollocation, setSystemCollocation] = useState<Array<PromptDictVO>>([]);
  const [expend, setExpend] = useState(needExpend(clothCollocation));
  const [isPreset, setIsPreset] = useState(false);

  useEffect(() => {
    queryCreativePreference().then(res => {
      if (res) {
        setPreferences(res);
      }
    });
    querySystemCollocation().then(res => {
      if (res && Array.isArray(res)) {
        setSystemCollocation(res);
      }
    });
  }, []);

  useEffect(() => {
    setExpend(defaultExpend);
  }, [defaultExpend]);

  useEffect(() => {
    const key = 'preset.clothCollocation';
    const preset = sessionStorage.getItem(key);
    if (preset) {
      const presetObj = JSON.parse(preset);
      setClothCollocation(presetObj);
      sessionStorage.removeItem(key);
      setIsPreset(true);
      setExpend(true);

      const copy = deepCopy(data);
      // 使用配置化的方式更新数据
      collocationItemsConfig.forEach(config => {
        copy.clothCollocation[config.key] = presetObj?.[config.key];
      });
      onChange({ ...copy }, true);

      return;
    }

    //未设置偏好时不做清除处理
    if (preferences.length <= 0 || isPreset) {
      return;
    }

    const find = !type ? null : preferences.find(e => e.tags.includes(type));

    if (find) {
      setExpend(needExpend(find?.clothCollocation));
      setClothCollocation(find?.clothCollocation);
    } else {
      setClothCollocation(emptyClothCollocation);
    }

    const copy = deepCopy(data);
    // 使用配置化的方式更新数据
    collocationItemsConfig.forEach(config => {
      copy.clothCollocation[config.key] = find ? find?.clothCollocation?.[config.key] : null;
    });
    onChange({ ...copy });
  }, [type]);

  const handleChange = (key, value) => {
    if (key.indexOf('.') >= 0) {
      const keys = key.split('.');
      const copy = deepCopy(data);
      copy[keys[0]][keys[1]] = value;
      onChange({ ...copy });
      setClothCollocation(pre => {
        const collocation = pre ? pre : emptyClothCollocation;
        collocation[keys[1]] = value;
        return collocation;
      });
    } else {
      onChange({ ...data, [key]: value });
    }
  };

  const getCollocation = (key: string) => {
    const result = systemCollocation.filter(item => item.tags && item.tags.includes(key));
    if (result.length <= 0) {
      // 直接从配置中获取预设项目
      const config = collocationItemsConfig.find(item => item.key === key);
      return config ? config.presetItems : [];
    }
    return result.map(item => item.word);
  };

  // 检查对象不为空, 属性不都为空
  const isClothCollocationValid = (clothCollocation: ClothCollocation | null): boolean => {
    if (clothCollocation) {
      for (const key in clothCollocation) {
        if (!!clothCollocation[key]) {
          return true;
        }
      }
    }
    return false;
  };

  const handleLabelClick = (item: MerchantPreferenceVO) => {
    console.log('handleLabelClick', item);
    setClothCollocation(item.clothCollocation || null);
    const copy = deepCopy(data);
    copy.clothCollocation = item.clothCollocation;
    onChange({ ...copy });
  }

  return (
    <div className={'work-item-container cloth-collocation-container' + (expend ? '-expend' : '')} style={{ gap: 8 }}>
      <Flex gap={12} className={'cloth-collocation-title pointer'} onClick={() => setExpend(!expend)}>
        <div className={'text16 font-pf color-n weight'}>自定义服装搭配
          <span className={'text14 color-72'} style={{ fontWeight: 400 }}>（选填）</span>
        </div>
        <IconFont type={'icon-mianxingxialajiantou'}
                  style={{ fontSize: 24, color: '#969799', transform: expend ? 'rotate(180deg)' : '' }} />
      </Flex>

      <Flex vertical gap={8} hidden={!expend}>
        <MerchantPreferenceBlock type={'CLOTH_COLLOCATION'} preference={{clothCollocation: (clothCollocation || undefined)}} onLabelClick={handleLabelClick} />
        {
          collocationItemsConfig.map((config) => (
            <ItemBlock
              key={config.key}
              title={config.title}
              description={config.description}
              presetItems={getCollocation(config.key)}
              onChange={(value) => handleChange(`clothCollocation.${config.key}`, value)}
              value={clothCollocation ? clothCollocation[config.key] : ''}
              maxLength={config.maxLength}
            />
          ))
        }
      </Flex>
    </div>
  );
};

export default ClothCollocationBlock;