import { Flex } from 'antd';
import React, { useEffect, useState } from 'react';
import { MaterialModel } from '@/services/MaterialModelController';
import { RANDOM_COLOR } from '@/constants';

interface ClothColorSelectorProps {
  value: number | null | undefined;
  model: MaterialModel | null | undefined;
  bodyType: string;
  position: string;
  onChange: (value: number | null | undefined) => void;
}

const ClothColorSelector: React.FC<ClothColorSelectorProps> = ({ value, model, bodyType, position, onChange }) => {
  const [current, setCurrent] = useState(value ? value : 0);
  const [colorList, setColorList] = useState(model?.colorList || []);

  useEffect(() => {
    const value = 0;
    setCurrent(value);
    let colorList = [];
    if (model?.clothTypeConfigs) {
      const find = model?.clothTypeConfigs.find(item => (!position || item.type.includes(position)) && (!bodyType || bodyType === 'unlimited' || item.type.includes(bodyType)));
      colorList = find?.colorList || [];
    }
    setColorList(colorList);
    onChange(value);
  }, [model, bodyType, position]);

  const handleChangeValue = (value: number) => {
    setCurrent(value);
    onChange(value);
  };

  const ItemCard = ({ value, title, image }) => {
    return <div style={{ width: 'width: calc((100% - 8px * 3) / 4)', height: 76 }}
                className={'image-size-block' + (current === value ? ' work-item-selected' : '')}
                onClick={() => handleChangeValue(value)}>
      <img alt="img" src={image} height={60} style={{ borderRadius: 4 }} />
      <Flex vertical gap={2} align={'flex-start'}>
        <div className={'text14 font-pf color-1a text-center'}>{title}</div>
      </Flex>
    </div>;
  };

  if (!colorList || colorList.length <= 1) {
    return null;
  }

  return <Flex vertical justify={'flex-start'} align={'flex-start'} className={'work-item-container'}>
    <div className={'text16 font-pf color-n weight'}>选择颜色</div>
    <Flex gap={8} className={'width-100'}>
      <ItemCard title={'随机颜色'} value={0} image={RANDOM_COLOR} />

      {colorList.map((item, index) =>
        <ItemCard key={item.index} title={`颜色${index + 1}`} value={item.index}
                  image={item.showImg} />)
      }
    </Flex>
  </Flex>;
};

export default ClothColorSelector;