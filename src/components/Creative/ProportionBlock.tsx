import React, { useEffect, useState, memo } from 'react';
import { MaterialModel } from '@/services/MaterialModelController';
import { LoraType } from '@/services/CreativeController';
import { ElementLockMask } from '@/components/Creative/ElementWithTypeBlock';
import { FILLED_IMAGE_MAP, IS_TRIAL_ACCOUNT } from '@/constants';
import TopupModal from '@/pages/Topup/Topup';
import { getUserInfo } from '@/utils/utils';
import './ProportionBlock.less';

export interface ProportionBlockProps {
  value: string;
  model?: MaterialModel | null | undefined;
  className?: string;
  style?: React.CSSProperties;
  type: LoraType;
  isVip: boolean;
  onChange: (value: string) => void;
}

const userInfo = getUserInfo();

// 使用memo包装组件以避免不必要的重新渲染
const ProportionBlock: React.FC<ProportionBlockProps> = memo(({ 
  value, 
  model, 
  className, 
  style, 
  type, 
  isVip, 
  onChange 
}) => {
  // 使用外部传入的value作为初始值
  const [current, setCurrent] = useState(value);
  const [showTopupModal, setShowTopupModal] = useState(false);
  const isTrialAccount = sessionStorage.getItem(IS_TRIAL_ACCOUNT) === 'Y';

  const [openLG11, setOpenLG11] = useState(false);
  const [openLG34, setOpenLG34] = useState(false);
  const [openLG916, setOpenLG916] = useState(false);
  const [openP_1152_1536, setOpenP_1152_1536] = useState(false);
  const [openP_1620_2100, setOpenP_1620_2100] = useState(false);
  const [openP_1200_600, setOpenP_1200_600] = useState(false);

  // 当外部value变化时，同步更新内部状态
  useEffect(() => {
    setCurrent(value);
  }, [value]);

  useEffect(() => {
    const isV2 = !model || model?.version === 'v_2';
    setOpenLG11(isV2 && !isTrialAccount);
    setOpenLG34(isV2);

    //9:16 1080x1920
    setOpenLG916(isV2);
    setOpenP_1152_1536(isV2);

    setOpenP_1200_600(isV2 && (userInfo?.roleType === 'OPERATOR' || userInfo?.roleType === 'ADMIN'));
    // setOpenP_1200_600(isV2);

    //1620x2100
    let show1620 = ['OPERATOR','ADMIN','DISTRIBUTOR'].includes(userInfo?.roleType || '') || userInfo?.masterId === 100632 || userInfo?.memo?.includes('虚拟商家') || false;
    setOpenP_1620_2100(show1620);
    // setOpenP_1620_2100(isV2);

    if (model && model?.version !== 'v_2' && (value === 'THREE_FOUR_LG' || value === 'ONE_ONE_LG' || value === 'THREE_FOUR_LG_N' || 'NINE_SIXTEEN_2K' || value === 'P_1152_1536' || value === 'P_1620_2100' || value === 'P_1200_600')) {
      handleProportionChange('THREE_FOUR');
    }
  }, [model]);

  const isLockProportion = (value) => {
    if (isTrialAccount) {
      return false;
    }

    if (userInfo?.id && [100402, 100401].includes(userInfo?.id)) {
      return false;
    }

    if (type === 'SYSTEM') {
      return userInfo?.roleType !== 'DISTRIBUTOR' && !isVip && FILLED_IMAGE_MAP[value].isLarge;
    } else {
      return isTrialAccount && !FILLED_IMAGE_MAP[value].isLarge;
    }
  };

  const handleProportionChange = (value: string) => {
    if (isLockProportion(value)) {
      setShowTopupModal(true);
      return;
    }
    setCurrent(value);
    onChange(value);
  };

  // 设置固定高度并防止高度抖动
  return (
    <div 
      className={'work-item-container ' + (className ? className : '')} 
      style={{ 
        ...style, 
        transition: 'none', 
        margin: 0, 
        overflow: 'visible', // 确保内容不被裁剪
        position: 'relative' // 定位上下文
      }}
    >
      <div className={'text16 font-pf color-n weight'}>图片尺寸</div>
      <div className={'work-item-row'} style={{ gap: 8 }}>

        {openP_1152_1536 &&
          <div
            className={'image-size-block' + (openP_1152_1536 ? ' image-size-block-LG' : '') + (current === 'P_1152_1536' ? ' work-item-selected' : '')}
            onClick={() => handleProportionChange('P_1152_1536')}
            style={{ position: 'relative', overflow: 'hidden' }}>
            <div className={'image-size-proportion-11 text14 font-pf color-3d text-center'}>
              高清<br />3:4
            </div>
            <div className={'image-size-title'}>
              <div className={'text14 font-pf color-1a weight'}>高清3:4</div>
              <div className={'text14 font-pf color-1a'}>1152x1536</div>
            </div>
            {isLockProportion('P_1152_1536') &&
              <ElementLockMask paddingTop={0} backdropFilter={''} />
            }
          </div>
        }

        {openLG11 &&
          <div
            className={'image-size-block' + (openLG11 ? ' image-size-block-LG' : '') + (current === 'ONE_ONE_LG' ? ' work-item-selected' : '')}
            onClick={() => handleProportionChange('ONE_ONE_LG')} style={{ position: 'relative', overflow: 'hidden' }}>
            <div className={'image-size-proportion-11 text14 font-pf color-3d text-center'}>
              高清<br />1:1
            </div>
            <div className={'image-size-title'}>
              <div className={'text14 font-pf color-1a weight'}>正方形</div>
              <div className={'text14 font-pf color-1a'}>1536x1536</div>
            </div>
            {isLockProportion('ONE_ONE_LG') &&
              <ElementLockMask paddingTop={0} backdropFilter={''} />
            }
          </div>
        }

        {!isLockProportion('THREE_FOUR') &&
          <div
            className={'image-size-block' + (openLG11 ? ' image-size-block-LG' : '') + (current === 'THREE_FOUR' ? ' work-item-selected' : '')}
            onClick={() => handleProportionChange('THREE_FOUR')}>
            <div className={'image-size-proportion-34 text14 font-pf color-3d'}>3:4</div>
            <div className={'image-size-title'}>
              <div className={'text14 font-pf color-1a weight'}>社交媒体</div>
              <div className={'text14 font-pf color-1a'}>768x1024</div>
            </div>
          </div>
        }

        {!isLockProportion('ONE_ONE') &&
          <div
            className={'image-size-block' + (openLG11 ? ' image-size-block-LG' : '') + (current === 'ONE_ONE' ? ' work-item-selected' : '')}
            onClick={() => handleProportionChange('ONE_ONE')} style={{ position: 'relative' }}>
            <div className={'image-size-proportion-11 text14 font-pf color-3d'}>1:1</div>
            <div className={'image-size-title'}>
              <div className={'text14 font-pf color-1a weight'}>正方形</div>
              <div className={'text14 font-pf color-1a'}>1024x1024</div>
            </div>
          </div>
        }


        {openLG34 &&
          <div
            className={'image-size-block' + (openLG34 ? ' image-size-block-LG' : '') + (current === 'THREE_FOUR_LG_N' ? ' work-item-selected' : '')}
            onClick={() => handleProportionChange('THREE_FOUR_LG_N')}
            style={{ position: 'relative', overflow: 'hidden' }}>
            <div className={'image-size-proportion-11 text14 font-pf color-3d text-center'}>
              高清<br />3:4
            </div>
            <div className={'image-size-title'}>
              <div className={'text14 font-pf color-1a weight'}>社交媒体</div>
              <div className={'text14 font-pf color-1a'}>1340x1785</div>
            </div>
            {isLockProportion('THREE_FOUR_LG_N') &&
              <ElementLockMask paddingTop={0} backdropFilter={''} />
            }
          </div>
        }

        {openLG916 &&
          <div
            className={'image-size-block' + (openLG916 ? ' image-size-block-LG' : '') + (current === 'NINE_SIXTEEN_2K' ? ' work-item-selected' : '')}
            onClick={() => handleProportionChange('NINE_SIXTEEN_2K')}
            style={{ position: 'relative', overflow: 'hidden' }}>
            <div className={'image-size-proportion-11 text14 font-pf color-3d text-center'}>
              高清<br />9:16
            </div>
            <div className={'image-size-title'}>
              <div className={'text14 font-pf color-1a weight'}>抖音9:16</div>
              <div className={'text14 font-pf color-1a'}>1080x1920</div>
            </div>
            {isLockProportion('NINE_SIXTEEN_2K') &&
              <ElementLockMask paddingTop={0} backdropFilter={''} />
            }
          </div>
        }

        {openP_1620_2100 &&
          <div
            className={'image-size-block' + (openP_1620_2100 ? ' image-size-block-LG' : '') + (current === 'P_1620_2100' ? ' work-item-selected' : '')}
            onClick={() => handleProportionChange('P_1620_2100')}
            style={{ position: 'relative', overflow: 'hidden' }}>
            <div className={'image-size-proportion-11 text14 font-pf color-3d text-center'}>
              高清<br />3:4
            </div>
            <div className={'image-size-title'}>
              <div className={'text14 font-pf color-1a weight'}>高清3:4</div>
              <div className={'text14 font-pf color-1a'}>1620x2100</div>
            </div>
            {isLockProportion('P_1620_2100') &&
              <ElementLockMask paddingTop={0} backdropFilter={''} />
            }
            <div className="tag-label-wrapper" style={{ position: 'absolute', top: 0, right: 0, zIndex: 1 }}>
              <div className="tag-label">
                <span>x2消耗</span>
              </div>
            </div>
          </div>
        }

        {openP_1200_600 &&
          <div
            className={'image-size-block' + (openP_1200_600 ? ' image-size-block-LG' : '') + (current === 'P_1200_600' ? ' work-item-selected' : '')}
            onClick={() => handleProportionChange('P_1200_600')}
            style={{ position: 'relative', overflow: 'hidden' }}>
            <div className={'image-size-proportion-11 text14 font-pf color-3d text-center'}>
              横版<br />2:1
            </div>
            <div className={'image-size-title'}>
              <div className={'text14 font-pf color-1a weight'}>横版2:1</div>
              <div className={'text14 font-pf color-1a'}>1200x600</div>
            </div>
            {isLockProportion('P_1200_600') &&
              <ElementLockMask paddingTop={0} backdropFilter={''} />
            }
          </div>
        }
      </div>

      {showTopupModal && !isTrialAccount &&
        <TopupModal visible={showTopupModal} onClose={() => setShowTopupModal(false)}
                  onPaySuccess={() => setShowTopupModal(false)} />
      }

    </div>
  );
});

export default ProportionBlock;