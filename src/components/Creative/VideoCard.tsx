import {
  Button, Col,
  Flex,
  Input,
  message, Modal,
  notification,
  Popconfirm,
  Radio,
  Select, Space, Spin,
  Tooltip,
  Upload,
  UploadProps,
} from 'antd';
import {
  apply2GenVideoClip,
  assignVideoOperator,
  batchQueryCreative, changeTempVideo,
  CreativeVO,
  fixVideoFace,
  isProcessing,
  removeFixFaceVideo, resetProcessing, updateOriginalImg4Video,
  uploadCreativeVideo, VideoClipTask,
} from '@/services/CreativeController';
import { calculateTimeDifference } from '@/utils/dateUtils';
import React, { useEffect, useRef, useState } from 'react';
import { ALL_LORAS, UPLOAD_URL } from '@/constants';
import { UploadFile } from 'antd/es/upload/interface';
import IconFont from '@/components/IconFont';
import ImgPreview from '@/components/ImgPreview';
import { deepCopy, download, getImageName, getTaskIdByImage, getUserInfo } from '@/utils/utils';
import VideoPreview from '@/components/VideoPreview';
import WatermarkVideo from '@/components/WatermarkVideo';
import { OperatorVO } from '@/services/UserController';
import { queryAllOperators } from '@/services/SystemController';
import { LoadingOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { ElementConfig, fetchTaskFace, getElementConfig } from '@/services/ElementController';
import ElementWithTypeSelector from '@/components/Creative/ElementWithTypeSelector';
import TextArea from 'antd/lib/input/TextArea';
import UserFavorButton from '@/components/Favor/UserFavorButton';
import ImageSelector from '@/components/Creative/ImageSelector';
import { DeletableImage } from '@/components/Common/ImageComponent';

const { Dragger } = Upload;

interface VideoCardProps {
  item: CreativeVO;
  width?: number | string;
  isOperate?: boolean;
  isDistributor?: boolean;
  updateCallback?: () => void;
}

const getProcessingList = (map: { [key: number]: CreativeVO }) => {
  const res: Array<CreativeVO> = [];

  if (!map) {
    return res;
  }

  Object.entries(map).forEach(([key, value]) => {
    if (map[key] && isProcessing(map[key])) {
      res.push(value);
    }
  });
  return res;
};

const getMapKey = (id: number, map: { [key: number]: CreativeVO }) => {
  let hit: number = 0;
  Object.entries(map).forEach(([key, value]) => {
    if (map[key] && map[key].id === id) {
      hit = Number(key);
    }
  });
  return hit;
};

const UploadDragger = ({ src, createTime, onChange, onRemove, clipTask }) => {

  const [loading, setLoading] = useState(false);
  const [fileUrl, setFileUrl] = useState<string | null>(src);
  const [fileList, setFileList] = useState<Array<UploadFile>>(src ? [{
    uid: '-1',
    name: getImageName(src),
    status: 'done',
    url: src,
  }] : []);

  const [prompt, setPrompt] = useState<string>(clipTask?.prompt || '');
  const [duration, setDuration] = useState<number | null>(clipTask?.duration || 5);
  const [autoGenLoading, setAutoGenLoading] = useState(false);
  const [clipTaskStatus, setClipTaskStatus] = useState(clipTask?.taskStatus || undefined);

  const preview = (e, src) => {
    e.stopPropagation();
  };

  const handleRemove = () => {
    onRemove();
    setFileUrl(null);
    setFileList([]);
  };

  const handleFileChange = ({ fileList }) => {
    let newFileList = [...fileList];

    for (let i = 0; i < newFileList.length; i++) {
      const file = newFileList[i];

      if (file && file.response && !file.response.success) {
        message.error('上传异常，请重试');
        return;
      }

      if (file && file.response && file.response.success) {
        const url = file.response.data;
        onChange(url);
        setFileUrl(url);
        setLoading(false);
      }
    }

    setFileList(newFileList);
  };

  const uploadProps: UploadProps = {
    fileList: fileList,
    onChange(info) {
      handleFileChange(info);
    },
    onRemove: onRemove,
    accept: 'video/*',
    action: UPLOAD_URL,
    showUploadList: true,
    listType: 'picture',
    name: 'file',
    beforeUpload: (file, fileList1) => {
      setLoading(true);
    },
  };

  function onClickAutoGen() {
    if (!prompt) {
      message.warning('请先填写prompt');
      return;
    }
    if (!duration) {
      message.warning('请先选择时长');
      return;
    }
    setAutoGenLoading(true); // 开启 loading 动画
    setTimeout(() => {
      apply2GenVideoClip({
        id: clipTask.id,
        index: clipTask.index,
        prompt: prompt,
        duration: duration,
      }).then(res => {
        if (res) {
          setClipTaskStatus('INIT');
          message.success('提交成功');
        } else {
          message.error('提交失败');
        }
      });
      setAutoGenLoading(false); // 操作完成后关闭 loading 动画
    }, 0);
  }

  return (
    < >
      {/*没视频，或提交的任务失败了，需要提交任务 或 上传文件*/}
      {!fileUrl && clipTask && (!clipTaskStatus || clipTaskStatus === 'FAILED') &&

        <Flex vertical={true} justify={'space-evenly'} align={'start'} style={{width: '100%'}}>

          {/*API生成*/}
          <Flex vertical={true} style={{ border: '1px dashed', width: '100%', borderRadius: 8, padding: 12 }}>

            <span style={{ fontWeight: 500, fontSize: 14 }}>自动生成（推荐）</span>

            <span>prompt（中英文皆可）:</span>
            <TextArea rows={2} style={{ height: 30, maxHeight: 75 }}
                      value={prompt}
                      defaultValue={''}
                      onChange={e => setPrompt(e.target.value)} />

            <span>时长:&nbsp;{duration || 5}秒</span>

            <Button size={'small'}
                    type={'default'}
                    disabled={!prompt}
                    loading={autoGenLoading}
                    onClick={onClickAutoGen}>
              提交自动生成
            </Button>

          </Flex>

          {clipTaskStatus === 'FAILED' &&
            <div style={{ color: 'red', margin: '4px auto' }}>上次任务失败了，需要重试或手工上传</div>
          }

          {clipTaskStatus !== 'FAILED' && <div style={{ margin: '4px auto' }}>或者</div>}

          {/*手工上传*/}
          <Dragger className={'task-image-video-upload'} {...uploadProps}>
            <Flex style={{ position: 'relative', width: '100%', height: '100%' }} justify={'start'} align={'center'}
                  vertical>
              <Button style={{ width: 132, height: 38, border: '1px solid #B5C7FF' }}
                      icon={<IconFont type={'icon-shangchuan'} style={{ fontSize: 16, color: '#366EF4' }} />}>
                <div className={'color-36 weight'}>上传/拖入视频</div>
              </Button>
              <div className={'text14 color-96 weight'}
                   style={{ marginTop: 4 }}>预计剩余{calculateTimeDifference(createTime)}h
              </div>

              {loading &&
                <Flex vertical justify={'center'} align={'center'} className={'width-100'}
                      style={{ position: 'absolute', height: '100%', backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
                  <LoadingOutlined style={{ fontSize: 32 }} />
                </Flex>
              }

            </Flex>
          </Dragger>
        </Flex>

      }

      {/*没视频，已经提交生成任务，正在生成视频*/}
      {!fileUrl && clipTask && (clipTaskStatus === 'INIT' || clipTaskStatus === 'PENDING' || clipTaskStatus === 'RUNNING') &&
        <>
          <div className="models-traing-mask" style={{ justifyContent: 'center' }}>
            <l-helix size="45" speed="2.5" color="blue" />
            <div className="models-training-title">生成视频中</div>
          </div>
        </>
      }

      {fileUrl &&
        <div style={{ position: 'relative', width: '100%', height: '100%' }}>
          <video width="100%" height="100%" controls muted onClick={(e) => preview(e, fileUrl)}>
            <source src={fileUrl} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
          <div className={'image-selector-round video-delete-round'} onClick={handleRemove}
               style={{ position: 'absolute', right: 8, top: 8, cursor: 'pointer' }}>
            <IconFont type={'icon-icon_shanchu'} style={{ fontSize: 16 }} />
          </div>
        </div>
      }
    </>
  );
};

const VideoCard: React.FC<VideoCardProps> = ({ item, width = '100%', isOperate = false, updateCallback, isDistributor = false }) => {
  const [fileMap, setFileMap] = useState<{ [key: number]: string }>({});
  const [previewImg, setPreviewImg] = useState<string | null>(null);
  const [previewImgList, setPreviewImgList] = useState<Array<string>>([]);
  const [previewVideo, setPreviewVideo] = useState<string | null>(null);
  const [previewVideoIndex, setPreviewVideoIndex] = useState<number>();
  const [allOperators, setAllOperators] = useState<Array<OperatorVO>>([]);
  const [assignOperator, setAssignOperator] = useState<string | null>(null);
  const [faceMap, setFaceMap] = useState<{ [key: number]: ElementConfig }>({});
  const [fixFaceMap, setFixFaceMap] = useState<{ [key: number]: CreativeVO }>({});
  const [faceConfig, setFaceConfig] = useState<ElementConfig | null>(null);
  const [faceSelectIndex, setFaceSelectIndex] = useState<number | null>(null);
  const timeoutRef = useRef();
  const batchCnt = item.extInfo && item.extInfo['originImage'] ? item.extInfo['originImage'].length : 1;

  const [showResetImg, setShowResetImg] = useState(false);
  const [showImageSelector, setShowImageSelector] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [selectedResetImgIndex, setSelectedResetImgIndex] = useState<number>();

  const preview = (previewImg, previewImgList) => {
    setPreviewImg(previewImg);
    setPreviewImgList(previewImgList);
  };

  const changeFile = (src, index) => {
    const copy = deepCopy(fileMap);
    copy[index] = src;
    setFileMap(copy);
    changeTempVideo(item.id, index, src);
  };

  const removeFile = (index) => {
    const copy = deepCopy(fileMap);
    delete copy[index];
    setFileMap(copy);
    changeTempVideo(item.id, index, null);
  };

  const handleAssignVideoOperator = (mobile: string) => {
    assignVideoOperator(item.id, mobile).then(res => {
      if (res) {
        message.success('设置成功');
        setAssignOperator(mobile);
      }
    });
  };

  function isDownloaded(imgUrl: string, creativeBatchVO: CreativeVO) {
    return imgUrl && creativeBatchVO?.extInfo['downloadedImgs']?.includes(imgUrl);
  }

  const handleFixFace = (index: number) => {
    if (!faceMap[index]) {
      message.warning('请先选择模特');
      return;
    }

    if (!fileMap[index]) {
      message.warning('请先上传视频');
      return;
    }

    fixVideoFace({
      batchId: item.id,
      faceId: faceMap[index]?.id,
      videoUrl: fileMap[index],
      index,
      imageUrl: item.extInfo['originImage'][index],
    }).then(res => {
      if (res) {
        message.info('开始视频修脸，请稍后');
        const copy = deepCopy(fixFaceMap);
        copy[index] = res;
        setFixFaceMap(copy);

        //开始轮询
        pollingStatus(copy);
      }
    });
  };

  const handleResetProcessing = () => {
    resetProcessing(item.id).then(res => {
      if (res) {
        message.success('重置成功');

        if (updateCallback) {
          updateCallback();
        }
      }
    });
  };

  const handleCommit = () => {
    const videos: Array<string> = [];
    for (let i = 0; i < batchCnt; i++) {
      // @ts-ignore
      let video = fixFaceMap[i]?.resultImages?.[0];
      if (!video) {
        video = fileMap[i];
      }
      if (video) {
        videos.push(video);
      }
    }

    if (videos.length < batchCnt) {
      message.warning(`视频数量${videos.length}小于${batchCnt}，请继续提交视频`);
      return;
    }
    uploadCreativeVideo({ id: item.id, videos }).then(res => {
      if (res) {
        notification.success({ message: '提交成功' });

        if (updateCallback) {
          updateCallback();
        }
      }
    });
  };

  const handleRemove = (index) => {
    removeFixFaceVideo(item.id, index).then(res => {
      if (res) {
        notification.success({ message: '删除成功' });
        const copy = deepCopy(fixFaceMap);
        copy[index] = null;
        setFixFaceMap(copy);
      }
    });
  };

  const removeFace = (index) => {
    const copy = deepCopy(faceMap);
    delete copy[index];
    setFaceMap(copy);
  };

  const fetchFace = async () => {
    const images = item.extInfo['originImage'];
    if (!images) return;

    const newFaceMap = {};
    for (let i = 0; i < images.length; i++) {
      const taskId = getTaskIdByImage(images[i]);
      if (!taskId) continue;

      const face = await fetchTaskFace(taskId);
      if (face && face.id) {
        face.fixed = true;
        newFaceMap[i] = face;
      }
    }
    setFaceMap(newFaceMap);
  };

  const fetchFixFaceData = async () => {
    if (!isProcessing(item)) return;

    const idMap: { [key: number]: number } = {};
    const ids: Array<number> = [];
    for (let i = 0; i < batchCnt; i++) {
      const batchId = item.extInfo[`related_${i}`];
      if (batchId) {
        ids.push(batchId);
        idMap[batchId] = i;
      }
    }

    const data: { [key: number]: CreativeVO } = {};
    if (ids && ids.length > 0) {

      const res = await batchQueryCreative(ids);
      if (!res || res.length <= 0) return;

      res.forEach(item => {
        const i = idMap[item.id];
        data[i] = item;
      });
    }

    setFixFaceMap(data);
    pollingStatus(data);
  };

  const selectFace = async (index) => {
    if (!faceConfig) {
      const res = await getElementConfig('FIX_VIDEO_FACE');
      if (!res) return;
      setFaceConfig(res[0]);
    }
    setFaceSelectIndex(index);
  };

  const handleSelectFace = (e: ElementConfig) => {
    if (faceSelectIndex == null) return;
    const copy = deepCopy(faceMap);
    copy[faceSelectIndex] = e;
    setFaceMap(copy);
    setFaceSelectIndex(null);
  };

  const pollingStatus = (fixFaceMap) => {
    //每次调度前先清除定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (getProcessingList(fixFaceMap).length <= 0) {
      return;
    }

    async function polling() {
      const ids = getProcessingList(fixFaceMap).filter(e => isProcessing(e)).map(e => e.id);
      if (!ids) {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        return;
      }

      let result;
      try {
        result = await batchQueryCreative(ids);
      } catch (e) {
        console.log('polling status error=', e);
      }

      if (result && result.length > 0) {
        let isCompleted = true;
        const data = deepCopy(fixFaceMap);
        result.forEach(item => {
          const i = getMapKey(item.id, data);
          data[i] = item;
          isCompleted = isCompleted && !isProcessing(item);
        });
        setFixFaceMap(data);

        if (isCompleted) {
          if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
          }
        }
      }

      // @ts-ignore
      if (getProcessingList(fixFaceMap).length > 0) {
        // @ts-ignore
        timeoutRef.current = setTimeout(() => polling(), 3000); // 3秒后再次轮询
        return () => clearTimeout(timeoutRef.current);
      }
    }

    // @ts-ignore
    timeoutRef.current = setTimeout(() => polling(), 300); // 1秒后再次轮询
    return () => clearTimeout(timeoutRef.current);
  };

  useEffect(() => {
    setAssignOperator(null);
    if (isOperate) {
      const map = {};
      for (let i = 0; i < batchCnt; i++) {
        const tempVideo = item.extInfo[`temp_video_${i}`];
        if (tempVideo) {
          map[i] = tempVideo;
        }
      }
      setFileMap(map);

      fetchFace();
      fetchFixFaceData();
    }
  }, [item]);

  useEffect(() => {
    if (!isOperate) {
      return;
    }

    queryAllOperators().then(res => {
      if (res && res.length > 0) {
        setAllOperators(res);
      }
    });

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const uploadProps: UploadProps = {
    onChange(info) {
      handleFileChange(info);
    },
    accept: 'image/*',
    action: UPLOAD_URL,
    name: 'file',
  };

  const handleFileChange = ({ file }) => {
    if (file && file.response && !file.response.success) {
      message.error('上传异常，请重试');
      return;
    }

    if (file && file.response && file.response.success) {
      const url = file.response.data;
      if (url){
        setSelectedImages([url]);
      }
    }
  };

  return (

    <Flex vertical justify={'space-between'} className={'width-100'}>
      <Flex wrap={'wrap'} gap={16}>
        {item.extInfo && item.extInfo['originImage'] && item.extInfo['originImage'].map((src, index) => (
          <Flex key={index} gap={8} align={'flex-start'} justify={'flex-start'}
                style={{ width: 'auto', minWidth: 150 }}>
            <Flex vertical gap={4}>
              <div className={'task-image-mini'}>
                <div className={'repair-hands-tag'}>
                  <div className={'text12 font-pf color-w'}>原图</div>
                </div>

                <img alt={'img'} src={src} className={'output-image'} loading={'lazy'}
                     onClick={() => preview(src, item.extInfo['originImage'])} />

                {isOperate &&
                  <div className={'history-detail-img-icon pointer'} onClick={() => download(src)}>
                    <IconFont type={'icon-a-shangchuan1x'} style={{ fontSize: '12px' }} />
                  </div>
                }
              </div>

              {isProcessing(item) && isOperate && item?.canShowChangeImg4VideoBatch &&
                <Tooltip title={'选图替换上面的原图，随后重新出视频'}>
                  <Button style={{color: 'red'}}
                          onClick={() => {
                            setShowResetImg(true);
                            setSelectedResetImgIndex(index);
                            setSelectedImages([]);
                          }}>换图</Button>
                </Tooltip>
              }

              {isOperate &&
                <Flex vertical gap={4}>
                  <div className={'task-image-mini'}>
                    <div className={'repair-hands-tag'}>
                      <div className={'text12 font-pf color-w'}>模特</div>
                    </div>

                    {faceMap[index] &&
                      <>
                        <img alt={'img'} src={faceMap[index].showImage} className={'output-image'} loading={'lazy'}
                             onClick={() => preview(faceMap[index].showImage, [faceMap[index].showImage])} />

                        {!faceMap[index].fixed &&
                          <div className={'image-selector-round video-delete-round'}
                               style={{
                                 position: 'absolute', right: 8, top: 8,
                                 cursor: 'pointer', width: 20, height: 20,
                               }}
                               onClick={() => removeFace(index)}>
                            <IconFont type={'icon-icon_shanchu'} style={{ fontSize: 16 }} />
                          </div>
                        }
                      </>
                    }

                    {!faceMap[index] &&
                      <div className={'output-image task-image-mini-square'} onClick={() => selectFace(index)}>
                        <img src={ALL_LORAS} alt={'all'} className={'lora-item-image'} />
                        <Flex align={'center'} justify={'center'} className={'color-w'}
                              style={{ position: 'absolute', width: '100%', height: '100%' }}>
                          选择模特
                        </Flex>
                      </div>
                    }

                  </div>

                  {isProcessing(item) &&
                    <Button onClick={() => handleFixFace(index)}>修脸</Button>
                  }

                </Flex>
              }

            </Flex>


            <Flex vertical className={'task-image-video'} align={'center'} justify={'start'}>
              {isProcessing(item) && !isOperate &&
                <>
                  <img src={src} alt={'img'} className={'task-image-video'} />

                  <div className="models-traing-mask" style={{ justifyContent: 'center' }}>
                    <l-helix size="45" speed="2.5" color="#D88FFF" />
                    <div className="models-training-title">生成视频中</div>
                    <div
                      className="models-training-finish-time">预估剩余时间：{calculateTimeDifference(item.createTime)}小时
                    </div>
                  </div>
                </>
              }

              {isProcessing(item) && isOperate &&
                <>
                  {!fixFaceMap[index] && !fileMap[index] &&
                    <UploadDragger
                      src={item.resultImages && item.resultImages[index] ? item.resultImages[index] : (fileMap[index] ? fileMap[index] : null)}
                      createTime={item.createTime}
                      clipTask={item?.videoClipGenTasks?.find(task => task.index === index) || {
                        id: item.id,
                        index: index,
                        prompt: '',
                        duration: item?.timeSecs4Video || 5,
                      }}
                      onChange={(src) => changeFile(src, index)}
                      onRemove={() => removeFile(index)} />
                  }
                  {!fixFaceMap[index] && fileMap[index] &&
                    <>
                      <WatermarkVideo src={fileMap[index]} controls={false}
                                      onClick={() => {
                                        setPreviewVideo(fileMap[index] ? fileMap[index] : '');
                                        setPreviewVideoIndex(index);
                                      }} />

                      <Popconfirm title={'是否删除当前视频'} onConfirm={() => removeFile(index)}>
                        <div className={'image-selector-round video-delete-round'}
                             style={{ position: 'absolute', right: 8, top: 8, cursor: 'pointer' }}>
                          <IconFont type={'icon-icon_shanchu'} style={{ fontSize: 16 }} />
                        </div>
                      </Popconfirm>
                    </>
                  }
                  {fixFaceMap[index] &&
                    <Flex vertical style={{ background: 'rgba(0, 0, 0, 0.6)', width: '100%', height: '100%' }}
                          align={'center'} justify={'center'} gap={8}>

                      {isProcessing(fixFaceMap[index]) &&
                        <>
                          <LoadingOutlined style={{ fontSize: 32, color: '#fff' }} />
                          <div className={'color-w weight text14'}>
                            {fixFaceMap[index].status === 'PROCESSING' ? '修复中' : '排队中'}，进度{fixFaceMap[index].extInfo && fixFaceMap[index].extInfo['schedule'] ? fixFaceMap[index].extInfo['schedule']['subSchedule'] : 0}%
                          </div>
                        </>
                      }

                      {!isProcessing(fixFaceMap[index]) &&
                        <>
                          <WatermarkVideo
                            src={fixFaceMap[index].resultImages ? fixFaceMap[index].resultImages[0] : ''}
                            controls={false}
                            onClick={() => {
                              setPreviewVideo(fixFaceMap[index].resultImages ? fixFaceMap[index].resultImages[0] : '');
                              setPreviewVideoIndex(index);
                            }} />

                          <div className={'repair-hands-tag'}>
                            <div className={'text12 font-pf color-w'}>修复后</div>
                          </div>

                          <Popconfirm title={'是否删除当前脸部已修复的视频'}
                                      onConfirm={() => handleRemove(index)}>
                            <div className={'image-selector-round video-delete-round'}
                                 style={{ position: 'absolute', right: 8, top: 8, cursor: 'pointer' }}>
                              <IconFont type={'icon-icon_shanchu'} style={{ fontSize: 16 }} />
                            </div>
                          </Popconfirm>
                        </>
                      }
                    </Flex>
                  }
                </>
              }

              {!isProcessing(item) &&
                <>
                  <WatermarkVideo src={item.resultImages ? item.resultImages[index] : ''} controls={false}
                                  onClick={() => {
                                    setPreviewVideo(item.resultImages ? item.resultImages[index] : '');
                                    setPreviewVideoIndex(index);
                                  }} />
                  <div className={'history-detail-img-icon-box'}>
                    {((isOperate || isDistributor) && isDownloaded(item.resultImages ? item.resultImages[index] : '', item)) &&
                      <div className={''} style={{ backgroundColor: 'green', display: 'flex', width: 20, height: 20,
                        alignItems: 'center', justifyContent: 'center', borderRadius: 5.68 }}>
                        <Tooltip title={'视频曾被下载'}>
                          <IconFont type={'icon-a-shangchuan1x'} style={{ fontSize: '12px' }} />
                        </Tooltip>
                      </div>
                    }
                    { getUserInfo()?.roleType !== 'ADMIN' && item.status === 'FINISHED' &&
                      <UserFavorButton
                        style={{
                          background: 'rgba(255,255,255,0.65)',
                          borderRadius: 5.68, display: 'flex',
                          width: 20, height: 20, alignItems: 'center',
                          justifyContent: 'center'
                      }}
                        favorType={item.type === 'CREATE_VIDEO' ? 'VIDEO' : 'IMAGE'}
                        itemId={item.id} images={[index]} imageIndex={index} />
                    }
                  </div>
                </>
              }

            </Flex>

          </Flex>
        ))}
      </Flex>

      {isOperate &&
        <Flex gap={16} justify={'flex-end'} align={'center'} className={'width-100'} style={{margin: '16px 0'}}>

          <div>指定跟进人：
            <Select style={{ width: '80px' }}
                    value={assignOperator ? assignOperator : (item.extInfo && item.extInfo['relatedOperator'] ? item.extInfo['relatedOperator'] : '')}
                    onChange={value => handleAssignVideoOperator(value)}>
              {allOperators.length > 0 && allOperators.map((operator, index) => (
                <Select.Option key={index} value={operator.mobile}>{operator.nickName}</Select.Option>
              ))}
            </Select>
          </div>

          {!isProcessing(item) &&
            <Popconfirm title={'是否确认重置为生成中'} onConfirm={handleResetProcessing}>
              <Button type={'dashed'}>重置为生成中</Button>
            </Popconfirm>
          }

          <Button type={'primary'} disabled={!isProcessing(item)} onClick={handleCommit} style={{marginRight: 86}}>确认交付</Button>
        </Flex>
      }

      {previewImg &&
        <ImgPreview
          previewVisible={!!previewImg}
          handleCancelPreview={() => {
            setPreviewImg(null);
            setPreviewImgList([]);
          }}
          previewImage={previewImg}
          needSwitch={true}
          previewIdx={previewImgList.findIndex(item => item === previewImg)}
          previewImgs={previewImgList}
        />
      }

      {previewVideo &&
        <VideoPreview src={previewVideo} showDownload={true} batch={item}
                      showTaskDetail={isOperate && item?.videoClipGenTasks != null}
                      onClose={() => setPreviewVideo(null)} indexInBatch={previewVideoIndex} />
      }

      {faceSelectIndex != null && faceConfig &&
        <ElementWithTypeSelector current={faceMap[faceSelectIndex] ? [faceMap[faceSelectIndex]] : []} title={'模特'}
                                 config={faceConfig} onChange={(value) => handleSelectFace(value[0])}
                                 onClose={() => setFaceSelectIndex(null)} />
      }

      {/*换图重新出视频*/}
      {showResetImg &&
        <Modal open={true} title={'换图'} width={600} onOk={()=>{
          updateOriginalImg4Video({
            batchId: item?.id,
            index: selectedResetImgIndex,
            imageUrl: selectedImages[0]
          }).then(res => {
            if (res){
              message.success('换图成功');
              if (updateCallback) {
                updateCallback();
              }
            }
          })
        }} onCancel={() => setShowResetImg(false)}>
          <>
            {selectedImages?.length >= 1 && selectedImages.map((image, index) => (
              <Col span={4.5} key={index}
                   style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                <img src={selectedImages[index]} alt={''}
                     style={{height: 184}}
                     onClick={()=>{
                  setPreviewImg(selectedImages[index]);
                }}/>
              </Col>
            ))}

            {selectedImages?.length < 1 &&
              <Flex vertical gap={8}>
                <Flex gap={16} align={'center'} justify={'center'}>
                  <Button type={'primary'} style={{ width: 196, height: 38 }}
                          icon={<IconFont type={'icon-lishirenwu_weixuanzhong1'} style={{ fontSize: 16 }} />}
                          onClick={(e)=>{
                            e.stopPropagation();
                            setShowImageSelector(true);
                          }}>
                    从历史创作中选择
                  </Button>
                  <Dragger {...uploadProps}>
                    <Button style={{ width: 132, height: 38, border: '1px solid #B5C7FF' }}
                            icon={<IconFont type={'icon-shangchuan'} style={{ fontSize: 16, color: '#366EF4' }} />}>
                      <div className={'color-36 weight'}>本地上传</div>
                    </Button>
                  </Dragger>
                </Flex>

                {showImageSelector &&
                  <ImageSelector maxChoose={1} value={selectedImages} onFinish={(value) => {
                    setSelectedImages(value);
                    setShowImageSelector(false);
                  }} onCancel={() => {
                    setSelectedImages([]);
                    setShowImageSelector(false);
                  }} />
                }
              </Flex>
            }
          </>

        </Modal>
      }

    </Flex>
  );
};

export default VideoCard;