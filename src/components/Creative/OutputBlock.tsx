import { Button, Flex, Modal, notification, Slider, Space } from 'antd';
import IconFont from '@/components/IconFont';
import { ExclamationCircleOutlined, ZoomInOutlined } from '@ant-design/icons';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import {
  cancelCreativeQueue,
  clearCreative,
  CreativeStatus,
  CreativeType,
  CreativeVO,
  deleteCreative,
  getCreativeTypeName,
  isExampleImage,
  LoraType,
  queryActiveAndTodayCreative,
  queryCreativeById,
} from '@/services/CreativeController';
import { GIFT_ICON, IMAGE_LOADING, IMAGE_LOADING_11, QUEUE_ICON } from '@/constants';
import MultiStepProgress from '@/components/MultiStepProgress';
import { useNavigate } from 'react-router-dom';
import ImgPreview from '@/components/ImgPreview';
import { deepCopy, timeoutGradient } from '@/utils/utils';
import { useLocation } from '@@/exports';
import WatermarkImage from '@/components/WatermarkImage';

export interface OutputBlockProps {
  types: Array<CreativeType>;
  loraType?: LoraType;
  status: CreativeStatus;
  refreshStatus: (status: CreativeStatus) => void;
  sliderValue?: number;
  changeSliderValue?: (value: number) => void;
}

export interface OutputBlockRef {
  polling: (id: number) => void;
}

interface ScheduleData {
  steps: Array<number>;
  subStep: number;
  finished: number;
  next?: number;
}

interface ImageCardProps {
  tag?: any;
  src: string;
  item: CreativeVO;
  ext?: any;
}

function buildSchedule(size, finished, subSchedule) {
  let array = new Array(size).fill(0);
  for (let i = 0; i < finished; i++) {
    array[i] = 100;
  }

  if (finished < size) {
    array[finished] = subSchedule;
  }
  return { steps: array, subStep: subSchedule, finished } as ScheduleData;
}

const OutputBlock = forwardRef<OutputBlockRef, OutputBlockProps>(({
                                                                    types,
                                                                    loraType = null,
                                                                    status,
                                                                    refreshStatus,
                                                                    sliderValue,
                                                                    changeSliderValue,
                                                                  }, ref) => {
    const [todayList, setTodayList] = useState<Array<CreativeVO>>([]);
    const [active, setActive] = useState<CreativeVO | null>(null);
    const [schedule, setSchedule] = useState<ScheduleData | null>(null);
    const [queueSize, setQueueSize] = useState<number | null>(null);

    const [detailImage, setDetailImage] = useState('');
    const [opItem, setOpItem] = useState<CreativeVO | null>(null);
    const [detailImageList, setDetailImageList] = useState<string[]>([]);
    const [likeObj, setLikeObj] = useState<any | null>({});
    const [deleteId, setDeleteId] = useState<number | null>(null);

    const navigate = useNavigate();
    const location = useLocation();

    async function fetchData() {
      const res = await queryActiveAndTodayCreative(types, loraType);
      if (res) {
        //设置
        const active = res.active;
        const todayList = res.todayList;
        if (todayList && todayList.length >= 0) {
          setTodayList(todayList);
        }

        if (active) {
          setActive(active);
          syncStatus(active, todayList);

          //开始轮询状态
          pollingStatus(active.id, todayList);
        }
      }
    }

    const refreshData = (id: number) => {
      queryCreativeById(id).then((res) => {
        if (!res || !todayList) {
          return;
        }
        const copy = deepCopy(todayList);
        let index = copy.findIndex((item: { id: number; }) => item.id === id);

        if (index !== -1) {
          copy[index] = res;
          setTodayList(copy);
        }
      });
    };

    const timeoutRef = useRef();
    const scheduleRef = useRef<ScheduleData>(buildSchedule(active ? active.batchCnt : 1, 0, 99));

    // 处理进度数据
    function processSchedule(extInfo: [], newStatus: CreativeStatus, result: CreativeVO) {
      //排队中状态，只设置队列长度
      if (newStatus === 'QUEUE') {
        setQueueSize(extInfo['queueSize']);
        return;
      }

      //如果是处理中状态，则更新处理中的进度信息
      const schedule = extInfo['schedule'];

      //如果没有进度信息时，新生成一个进度为0的信息
      if (schedule == null) {
        const scheduleData = buildSchedule(result.batchCnt, 0, 0);
        setSchedule(scheduleData);
        scheduleRef.current = scheduleData;
        return;
      }

      //有进度信息的则构造进度条数据
      let { finished, subSchedule } = schedule;
      //处理缓存的节点的进度，mock成从0开始
      let nextStep = false;
      if (scheduleRef.current && (scheduleRef.current?.subStep > subSchedule) || scheduleRef.current?.subStep === 0) {
        nextStep = true;
      }
      const scheduleData = buildSchedule(result.batchCnt, finished, nextStep ? 0 : subSchedule);
      scheduleData.next = subSchedule;
      setSchedule(scheduleData);
      scheduleRef.current = scheduleData;

      //如果主进度条没有完成，这直接通过scheduleData展示即可
      if (!nextStep) {
        console.log('timeoutGradient.call.subStep !nextStep', scheduleData.subStep);
        return;
      }

      //如果主进度条有一个完成了，则模拟小进度条从0开始
      timeoutGradient(0, Math.floor(subSchedule / 10), 2500, {
        call: function(idx: number) {
          let scheduleData = scheduleRef.current;
          if (scheduleData) {
            const next = scheduleData.next;
            scheduleData = buildSchedule(scheduleData.steps.length, scheduleData.finished, idx * 10);
            scheduleData.next = next;
            setSchedule(scheduleData);
            scheduleRef.current = scheduleData;
          }
        },
      });
    }

    // 处理图片数据
    function processImages(todayList: Array<CreativeVO>, result: CreativeVO) {

      setTodayList(prevData => {
        const index = prevData.findIndex((item: { id: number; }) => item.id === result.id);
        const origin = index !== -1 ? prevData[index] : null;
        const originImages = (origin?.resultImages ? origin?.resultImages : []).filter((item: string) => item !== IMAGE_LOADING && item !== IMAGE_LOADING_11);

        if (result.status === 'FAILED') {
          result.resultImages = [];
        } else {

          if (!result.resultImages) {
            result.resultImages = [];
          }

          const batchCnt = result.type === 'REPAIR_HANDS' ? 4 : result.batchCnt;
          if (originImages.length >= result.resultImages.length && (origin?.resultImages ? origin?.resultImages : []).length >= batchCnt) {
            console.log('已存在图片，且图片数量未变更，不更新');
            return prevData;
          }
          console.log('已存在图片，且图片数量发生变更，执行更新', originImages.length, result.resultImages.length);
          const loadingImg = result.imageProportion === 'THREE_FOUR' ? IMAGE_LOADING : IMAGE_LOADING_11;
          result.resultImages = [...result.resultImages, ...Array(batchCnt - result.resultImages.length).fill(loadingImg)];

          if (detailImage && detailImageList.length > result.resultImages.length) {
            const copy = [...result.resultImages];
            console.log('刷新detailImageList', detailImageList.length, copy.length);
            setDetailImageList(copy);
          }
        }

        const copy = deepCopy(prevData);
        if (index !== -1) {
          copy[index] = result;
        } else {
          copy.unshift(result);
        }

        return copy;
      });
    }

    const syncStatus = (active: void | CreativeVO, todayList: Array<CreativeVO>) => {
      if (!active) {
        return;
      }

      setActive(active);

      const newStatus = active.status;
      const extInfo = active.extInfo;

      //失败时，将状态置为初始化
      refreshStatus(newStatus === 'FAILED' ? 'INIT' : newStatus);

      //队列和处理中，更新进度状态
      if (extInfo && (newStatus === 'PROCESSING' || newStatus === 'QUEUE')) {
        processSchedule(extInfo, newStatus, active);
      }

      //如果流程结束，则清除定时器
      if (timeoutRef.current && (newStatus === 'FINISHED' || newStatus === 'FAILED')) {
        clearTimeout(timeoutRef.current);
      }

      if (todayList && newStatus !== 'QUEUE' && newStatus !== 'INIT') {
        processImages(todayList, active);
      }
    };

    const pollingStatus = (id, todayList) => {
      async function polling(id, todayList) {
        let result;
        try {
          result = await queryCreativeById(id);
          console.log('polling status result=', result && result.status);
        } catch (e) {
          console.log('polling status error=', e);
        }

        if (result) {
          syncStatus(result, todayList);
        }

        // @ts-ignore
        if (!result || result.status === 'PROCESSING' || result.status === 'QUEUE') {
          // @ts-ignore
          timeoutRef.current = setTimeout(() => polling(id, todayList), 3000); // 3秒后再次轮询
          return () => clearTimeout(timeoutRef.current);
        }
      }

      // @ts-ignore
      timeoutRef.current = setTimeout(() => polling(id, deepCopy(todayList)), 300); // 1秒后再次轮询
    };

    const handleShowImage = (value: string, item: CreativeVO) => {
      if (value === IMAGE_LOADING || value === IMAGE_LOADING_11) {
        return;
      }
      setDetailImage(value);
      setOpItem(item);
      let imageList = item.resultImages ? item.resultImages : [];
      if (item.extInfo['originImage']) {
        imageList = [item.extInfo['originImage'], ...imageList];
      }
      if (item.extInfo['logoImageOss']) {
        imageList = [item.extInfo['logoImageOss'], ...imageList];
      }
      // @ts-ignore
      setDetailImageList(imageList);

      if (item.extInfo) {
        setLikeObj({ like: item.extInfo['like'], id: item.id });
      } else {
        setLikeObj(null);
      }
    };

    const gotoRepair = (taskId: number) => {
      navigate('/repair-hands?id=' + taskId, { state: { from: location.pathname } });
    };

    const handleCancelQueue = () => {
      if (!active || active.status !== 'QUEUE') {
        return;
      }

      cancelCreativeQueue(active.id).then((res) => {
        if (res) {
          notification.success({ message: '生成图片取消成功' });
          refreshStatus('INIT');
          //取消轮询调度
          clearTimeout(timeoutRef.current);
        }
      });
    };

    const handleClear = () => {
      clearCreative(types, loraType).then((res) => {
        if (res) {
          setTodayList([]);
          fetchData();
        }
      });
    };

    const handleDelete = () => {
      deleteCreative(deleteId).then((res) => {
        if (res) {
          notification.success({ message: '删除成功' });
          setDeleteId(null);
          fetchData();
        }
      });
    };

    const handleHideMask = () => {
      setDetailImage('');
      setDetailImageList([]);
    };

    useImperativeHandle(ref, () => ({
      polling: (id) => {
        setSchedule(null);
        pollingStatus(id, todayList);
      },
    }));

    useEffect(() => {
      fetchData();

      return () => {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      };
    }, []);


    const ImageCard: React.FC<ImageCardProps> = ({ tag, src, item, ext }) => {
        return <>
          <div className={'image-block'}>
            {tag &&
              <div className={'repair-hands-tag'}>{tag}</div>
            }
            <WatermarkImage src={src} className={'output-image'} loading={'lazy'}
                            onClick={() => handleShowImage(src, item)} />
            {ext}
          </div>
        </>;
      }
    ;

    const RepairHandsFailed = (item: CreativeVO) => {
      return <>
        <Flex align={'center'} justify={'center'} className={'repair-delete-icon'}
              onClick={() => setDeleteId(item.id)}>
          <IconFont type={'icon-icon_shanchu'} style={{ fontSize: 20, color: '#fff' }} />
        </Flex>
        <Flex vertical gap={4} className={'text12 font-pf color-w repair-failed-warning'}
              align={'center'} justify={'center'}
              onClick={() => handleShowImage(item.extInfo['originImage'], item)}>
          <ExclamationCircleOutlined style={{ fontSize: 16, color: '#fff' }} />
          未识别到手部
        </Flex>
        <Button type={'primary'} className={'repair-retry-btn'}
                onClick={() => gotoRepair(item.extInfo['originTask'])}>重新涂抹</Button>
      </>;
    };

    return <>
      {/*图片展示区域*/}
      {todayList.length > 0 &&
        <div className={'center-block margin-left-8 margin-right-8'} style={{ paddingTop: 8 }}>
          <div
            className={'image-container' + (status === 'PROCESSING' || status === 'QUEUE' ? ' image-container-processing' : '')}>
            {todayList.map((item) => (
              <div key={item.id} style={{ gap: 8 }}
                   className={'center-block width-100 margin-top-16' + (isExampleImage(item) ? ' example-images-block' : ' normal-image-block')}>

                {isExampleImage(item) &&
                  <Flex gap={4} align={'center'} className={'example-images-title margin-bottom-8'}>
                    <img src={GIFT_ICON} alt={''} width={18} height={18} />
                    <div className={'text12 color-1a weight'}>MuseGate精选图</div>
                  </Flex>
                }

                <div style={{ overflow: 'auto' }} className={'image-gallery width-100'}>

                  {/*手部修复*/}
                  {item.type === 'REPAIR_HANDS' &&
                    <ImageCard tag={<div className={'text12 font-pf color-w'}>原图</div>}
                               src={item.extInfo['originImage']} item={item}
                               ext={item.status === 'FAILED' ? <RepairHandsFailed {...item} /> : null} />
                  }

                  {/*4K高清*/}
                  {item.type === 'IMAGE_UPSCALE' &&
                    <ImageCard tag={<div className={'text12 font-pf color-w'}>原图</div>}
                               src={item.extInfo['originImage']} item={item} />
                  }

                  {/*印花上身*/}
                  {item.type === 'LOGO_COMBINE' &&
                    <ImageCard tag={<div className={'text12 font-pf color-w'}>印花</div>}
                               src={item.extInfo['logoImageOss']} item={item} />
                  }

                  {/*通用*/}
                  {item.resultImages && item.resultImages.map((src, index) => (
                    <React.Fragment key={index + ''}>
                      {/*印花上身每行只展示4个*/}
                      {item.type === 'LOGO_COMBINE' && index === 4 && item.resultImages && item.resultImages.length > 4 &&
                        <ImageCard src={''} item={item} />
                      }
                      <ImageCard tag={item.type === 'REPAIR_HANDS' ?
                        <IconFont type={'icon-shoubuxiufu'} style={{ fontSize: 16 }}
                                  className={'color-w'} /> : (item.type === 'IMAGE_UPSCALE' ?
                          <ZoomInOutlined style={{ fontSize: 16 }} className={'color-w'} /> : null)}
                                 src={src} item={item} />
                    </React.Fragment>
                  ))}
                </div>
                <Flex className={'width-100' + (isExampleImage(item) ? '' : ' margin-bottom-12')}
                      justify={'flex-start'} align={'center'} gap={8}>
                  <div className={'creative-type text14 font-pf color-72'}>{getCreativeTypeName(item.type)}</div>
                  <div className={'text14 font-pf color-72'} style={{ padding: 4 }}>{item.createTime}</div>
                </Flex>
              </div>
            ))}
          </div>
        </div>
      }

      <div className={'right-block slider-bar'}>
        {(status === 'QUEUE') &&
          <Space className={'message-card'}>
            <Space>
              <img src={QUEUE_ICON} width={24} height={24} alt={'logo'} />
              <div className={'text14 font-pf color-1a'}>
                {queueSize ? `有${queueSize}个用户在排队，请等候` : '正在排队中，请等候'}
              </div>
            </Space>
            <Button type="primary" className={'cancel-btn'} onClick={handleCancelQueue}>
              <div className={'font-pf text16 weight color-70'}>取消排队</div>
            </Button>
          </Space>
        }
        {(status === 'PROCESSING') &&
          <div className={'col-start-block'}>
            {schedule &&
              <MultiStepProgress steps={schedule.steps} subStep={schedule.subStep} resize={sliderValue} />
            }
            <div className={'font-pf text14 color-70 margin-bottom-4'}>
              {active?.type && active?.type === 'REPAIR_HANDS' ? '正在进行手部修复' : '正在生成图片'}
            </div>
          </div>
        }

        {changeSliderValue &&
          <Space className={'font-pf text14 color-70'}>
            缩小
            <Slider value={sliderValue} min={12} max={22} tooltip={{ open: false }} onChange={(e) => changeSliderValue(e)}
                    style={{ width: '224px' }} disabled={todayList.length <= 0} />
            放大
            <Button type="primary" className={'cancel-btn'} style={{ height: 28, width: 76 }} onClick={handleClear}
                    disabled={todayList.length <= 0}>
              <div className={'font-pf text16 color-70'}>清空</div>
            </Button>
          </Space>
        }
      </div>

      <ImgPreview
        previewVisible={!!detailImage}
        handleCancelPreview={handleHideMask}
        previewImage={detailImage}
        needSwitch={!!detailImageList}
        previewIdx={detailImageList.findIndex(item => item === detailImage)}
        previewImgs={detailImageList}
        likeObj={likeObj?.like}
        likeCallback={() => refreshData(likeObj?.id)}
        showTools={true}
        type={opItem?.type}
        creativeBatch={opItem}
        modelId={opItem?.modelId}
      />

      {deleteId &&
        <Modal open={true} title={'确认删除此任务吗？'} onCancel={() => setDeleteId(null)} onOk={handleDelete}
               closable={false} okText={'确认并删除'} cancelText={'取消'} centered>
          删除后将无法复原，请再次确认
        </Modal>
      }
    </>;
  },
);

export default OutputBlock;