import IconFont from '@/components/IconFont';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  ClothCategoryGroup,
  ElementConfig,
  getClothCategoryCfg,
  getElementTypes,
  ProportionType,
  queryElementByPage,
  queryFavorElements,
  SceneType,
  batchQueryElementById,
  ElementConfigWithBlobs,
} from '@/services/ElementController';
import { Button, Carousel, Checkbox, Flex, Input, Modal, Select } from 'antd';
import { ElementLockMask, filterByCondition, isLockedElement } from '@/components/Creative/ElementWithTypeBlock';
import { FaceBaseType } from '@/pages/Operate/Face';
import { ALLBizTypes, BizType, LoraType } from '@/services/CreativeController';
import { ExperienceModelOpenDetail } from '@/services/SystemController';
import TopupModal from '@/pages/Topup/Topup';
import NewLabel from '@/components/new/NewLabel';
import { DEBOUNCE_DELAY, IS_TRIAL_ACCOUNT, USER_INFO } from '@/constants';
import './ElementWithTypeSelector.less';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import UserFavorButton from '@/components/Favor/UserFavorButton';
import { UserVO } from '@/services/UserController';
import { MiniTag } from '@/components/Common/CommonComponent';
import { useNavigate } from 'react-router-dom';
import { bodyTypeOptions, positionOptions } from '@/components/Operate/ABTest/ParamsSelectList';
import ClothCategorySelect from '../Operate/ClothCategory';
import ElementTags from './ElementTags';
import { PromptDictVO, queryDictByKeyAndTags } from '@/services/PromptDictController';
import { getTypeItemsByTagsFromData } from '@/utils/TagsUtils';

interface ElementWithTypeSelectorProps {
  config: ElementConfig;
  current: Array<ElementConfig>;
  recentList?: Array<ElementConfig> | [];
  title: string;
  orderByType?: boolean;
  proportion?: ProportionType;
  maxChoose?: number;
  forceProportion?: boolean;
  acceptTypes?: Array<string>,
  conditions?: any;
  loraType?: LoraType;
  experienceModelOpenCfg?: ExperienceModelOpenDetail | null | undefined;
  isVipUser?: boolean;
  onClose: () => void;
  onChange: (value: Array<ElementConfig>) => void;
  showConfirmFooter?: boolean;
  selectedShowType?: string;
  bizType?: BizType | undefined | null;
  onlyLora?: boolean;
  // 是否展示姿势图
  isShowPosture?: boolean;
  // 是否查询具有展示图片的元素，默认为 false
  isHasShowImage?: boolean;
}

const BIZ_TYPE_CLASS_MAP = {
  'LOOK': 'element-model-btn-look',
  'REAL-SCENE-SHOOTING': 'element-model-btn-real-scene-shooting',
  'MODEL-SHOW': 'element-model-btn-model-show',
};

export const sortByType: any = (list: Array<ElementConfig>, typeList: Array<SceneType>, orderByType: boolean, unlockedList: Array<number>) => {
  if ((!orderByType || !list || list.length <= 0 || !typeList || typeList.length <= 0) && !unlockedList) {
    return list;
  }

  // 创建一个映射关系，使得可以根据 code 快速查找其在 typeList 中的索引位置
  const codeIndexMap = typeList.reduce((acc, curr, index) => {
    acc[curr.code] = index;
    return acc;
  }, {});

  list.sort((a, b) => {
    if (unlockedList && unlockedList.length > 0) {
      if (unlockedList.includes(a.id) && !unlockedList.includes(b.id)) {
        return -1;
      } else if (!unlockedList.includes(a.id) && unlockedList.includes(b.id)) {
        return 1;
      }
    }

    // 含有上新标签的直接置顶
    if (a.isNew && !b.isNew) {
      return -1;
    } else if (!a.isNew && b.isNew) {
      return 1;
    }

    // 原有逻辑排序
    const aCode = a.type[0];
    const bCode = b.type[0];
    const aIndex = codeIndexMap[aCode];
    const bIndex = codeIndexMap[bCode];

    if (aIndex === undefined && bIndex === undefined) {
      // 如果两个都没有定义，则维持原有的顺序
      return list.indexOf(a) - list.indexOf(b);
    } else if (aIndex === undefined) {
      // 如果只有 a 没有定义，则 b 在前
      return 1;
    } else if (bIndex === undefined) {
      // 如果只有 b 没有定义，则 a 在前
      return -1;
    } else {

      if (a.status !== 'TEST' && b.status === 'TEST') {
        return -1;
      }

      if (a.status === 'TEST' && b.status !== 'TEST') {
        return 1;
      }

      // 如果都有定义，则根据索引排序
      if (aIndex !== bIndex) {
        return aIndex - bIndex;
      }

      //版本号比较
      // if (a.version !== b.version) {
      return b.version.localeCompare(a.version);
      // }
    }
  });

  return list;
};

const ElementWithTypeSelector: React.FC<ElementWithTypeSelectorProps> = ({
                                                                           config,
                                                                           current,
                                                                           recentList = [],
                                                                           title,
                                                                           orderByType = false,
                                                                           maxChoose = 1,
                                                                           proportion,
                                                                           forceProportion = false,
                                                                           acceptTypes = [],
                                                                           conditions,
                                                                           loraType = 'CUSTOM',
                                                                           experienceModelOpenCfg,
                                                                           isVipUser = false,
                                                                           onClose,
                                                                           onChange,
                                                                           showConfirmFooter = false,
                                                                           selectedShowType = 'all',
                                                                           bizType = 'ALL',
                                                                           onlyLora = false,
                                                                           isShowPosture = false,
                                                                           isHasShowImage = false,
                                                                         }) => {
  const isMounted = useRef(false);
  const carouselRef = useRef<any>(null);

  const useNewFaceType = config.configKey === 'FACE' && true;

  const [total, setTotal] = useState<number>(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(54);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Add carousel hover state here
  const [carouselHoverSide, setCarouselHoverSide] = useState<'left' | 'right' | null>(null);

  // 添加轮播图当前索引状态
  const [currentSlide, setCurrentSlide] = useState(0);

  const [name, setName] = useState<null | string>(null);
  const [searchValue, setSearchValue] = useState<string>(''); // 用于输入框显示的值
  const [activeSearchField, setActiveSearchField] = useState<'clothCategory' | 'name' | null>('clothCategory');
  
  // 防抖ref
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  const [showList, setShowList] = useState<Array<ElementConfig>>([]);
  const [typeList, setTypeList] = useState<Array<SceneType>>([]);
  const [showTypeList, setShowTypeList] = useState<Array<SceneType>>(typeList);
  const [subTypeList, setSubTypeList] = useState<Array<{ key: BizType, value: BizType; label: string; }>>();
  const [showType, setShowType] = useState(selectedShowType);
  const [showSubType, setShowSubType] = useState<Array<string>>([]);
  const [showTopupModal, setShowTopupModal] = useState(false);
  const isTrialAccount = sessionStorage.getItem(IS_TRIAL_ACCOUNT) === 'Y';
  const [bizTypeList, setBizTypeList] = useState<Array<{
    key: BizType,
    value: BizType;
    label: string;
    iconType?: string;
  }>>([]);
  const [showBizType, setShowBizType] = useState<BizType | null>(bizType);

  const [selectedItems, setSelectedItems] = useState<Array<ElementConfig>>(current || []);
  const [onlyExperimental, setOnlyExperimental] = useState(false);
  const [clothCategory, setClothCategory] = useState<string[]>(conditions?.clothCategory ?? []);
  const [clothCategoryCfg, setClothCategoryCfg] = useState<ClothCategoryGroup[]>([]);
  const [searchBodyType, setSearchBodyType] = useState<string | null>(null);
  const [searchPosition, setSearchPosition] = useState<string | null>(null);

  const [typeTags, setTypeTags] = useState<Record<string, Array<PromptDictVO>>>();
  const [selectSytleTypes, setSelectSytleTypes] = useState<string[]>([]);
  const [selectNationTypes, setSelectNationTypes] = useState<string[]>([]);

  // 新增：缓存姿势图数据
  const [postureImagesCache, setPostureImagesCache] = useState<Map<number, string[]>>(new Map());

  // 新增：当姿势图缓存更新时，强制重新渲染组件
  const [forceUpdate, setForceUpdate] = useState(0);

  // @ts-ignore
  const userInfo: UserVO = JSON.parse(localStorage.getItem(USER_INFO));
  const navigate = useNavigate();

  // 防抖搜索函数
  const debouncedSearch = useCallback((value: string) => {
    // 清除之前的定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    
    // 设置新的定时器
    debounceTimerRef.current = setTimeout(() => {
      setName(value);
    }, DEBOUNCE_DELAY); // 防抖延迟
  }, []);

  // 清理定时器的函数
  const clearDebounceTimer = useCallback(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
      debounceTimerRef.current = null;
    }
  }, []);

  const buildQuery = (isExclusive: boolean) => {
    let clothTypes = conditions && conditions.clothType ? (conditions.clothType === 'Unisex' ? ['Female', 'Male'] : [conditions.clothType, 'Common']) : [];
    clothTypes = [...(!clothTypes ? [] : clothTypes), ...acceptTypes];
    let types = [...(conditions && conditions.version ? [conditions.version] : [])];
    // types = [...types, ...(conditions && conditions.positions ? conditions.positions : [])];
    // types = [...types, ...(conditions && conditions.bodyType ? conditions.bodyType : [])];
    // types = [...types, ...(showSubType ? showSubType : [])];
    let find = ALLBizTypes.find(e => e.key === showBizType);
    types = [...types, ...(find?.includesTypes ? find?.includesTypes : [])];
    types = [...types, ...(['all', 'exclusive', 'recent', 'favor'].includes(showType) ? [] : (showType === 'child-model' ? [] : [showType]))];
    types = [...types, ...(searchBodyType ? [searchBodyType] : [])];
    types = [...types, ...(searchPosition ? [searchPosition] : [])];

    let typesOrList: string[][] = [];
    if (selectSytleTypes && selectSytleTypes.length > 0) {
      typesOrList.push(selectSytleTypes);
    }
    if (selectNationTypes && selectNationTypes.length > 0) {
      typesOrList.push(selectNationTypes);
    }

    let ids = conditions && conditions.faceIds ? conditions.faceIds : [];
    const limitList = experienceModelOpenCfg ? (config.configKey === 'FACE' ? experienceModelOpenCfg.faces : experienceModelOpenCfg.scenes) : [];
    if ('DEMO_ACCOUNT' === userInfo?.roleType) {
      ids = [...ids, ...limitList ? limitList : []];
    }

    let orderBy = current && current[0]?.id ? `if(id='${current[0]?.id}',1,0) desc,is_new desc,if(status='PROD',0,1),\`order\`,modify_time desc` : null;
    if (loraType === 'SYSTEM' && limitList && limitList.length > 0) {
      orderBy = `if(id in (${limitList.join(',')}),1,0) desc, is_new desc,if(status='PROD',0,1),\`order\`,modify_time desc`;
    }
    // 获取年龄范围
    const ageRange = conditions && conditions.ageRange ? conditions.ageRange : null;
    return {
      configKey: config.configKey,
      pageNum: page,
      pageSize: pageSize,
      ids: ids && ids.length > 0 ? ids : null,
      types,
      isExclusive: isExclusive ? isExclusive : null,
      typesOr: showSubType && showSubType.length > 0 ? showSubType : null,
      genderTypes: clothTypes,
      bizType: showBizType,
      positions: conditions && conditions.positions ? conditions.positions : null,
      bodyTypes: conditions && conditions.bodyTypes ? conditions.bodyTypes : null,
      name: activeSearchField === 'name' ? name : null,
      onlyLora,
      excludesTypes: find?.excludesTypes,
      orderBy,
      ageRange,
      ageRanges: showType === 'child-model' ? ['big-child', 'medium-child', 'small-child'] : null,
      onlyExperimental,
      clothCategory: activeSearchField === 'clothCategory' ? clothCategory : null,
      typesOrList,
      isHasShowImage,
    };
  };

  const fetchData = async (isLoadMore = false, targetPage?: number) => {
    if (isLoadMore) {
      setIsLoadingMore(true);
    }

    const currentPage = targetPage || (isLoadMore ? page : 1);
    const query = buildQuery(showType === 'exclusive');
    query.pageNum = currentPage;

    queryElementByPage(query).then(res => {
      if (!res) {
        setIsLoadingMore(false);
        return;
      }

      setTotal(res.totalCount || 0);

      if (isLoadMore) {
        // 追加数据
        setShowList(prev => {
          const newList = [...prev, ...(res.list || [])];
          return newList;
        });
      } else {
        // 重置数据
        setShowList(res.list || []);
      }

      // 检查是否还有更多数据：只有当返回的数据为空时才停止加载
      const hasMoreData = (res.list || []).length > 0;
      setHasMore(hasMoreData);
      setIsLoadingMore(false);
    }).catch(err => {
      console.error('❌ 数据加载失败:', err);
      setIsLoadingMore(false);
    });
  };

  const fetchFavor = async () => {
    setPage(1);
    queryFavorElements(buildQuery(false)).then(res => {
      if (!res) return;
      setTotal(res.totalCount || 0);
      setShowList(res.list || []);
      setHasMore(false); // 收藏列表一次性加载完毕
    });
  };

  const fetchMoreData = () => {
    if (isLoadingMore || !hasMore) {
      return;
    }

    const nextPage = page + 1;
    setPage(nextPage);
    fetchData(true, nextPage);
  };


  // 添加滚动节流处理
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 添加滚动事件监听器
  const handleScroll = (e: any) => {
    // 节流处理，避免频繁触发
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    scrollTimeoutRef.current = setTimeout(() => {
      const target = e.target;
      const scrollTop = target.scrollTop;
      const scrollHeight = target.scrollHeight;
      const clientHeight = target.clientHeight;
      const scrollPercent = (scrollTop / (scrollHeight - clientHeight)) * 100;

      // 当滚动到90%以上且有更多数据且未在加载时，自动触发加载
      if (scrollPercent >= 90 && hasMore && !isLoadingMore) {
        fetchMoreData();
      }
    }, 100); // 100ms 节流
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
      // 清理防抖定时器
      clearDebounceTimer();
    };
  }, [clearDebounceTimer]);

  const changeShowBizType = (bizType: BizType) => {
    setShowBizType(bizType);
    setShowType('all');
  };

  const handleChange = (item: ElementConfig) => {
    if (isLockedElement(loraType, item.configKey, item.id, experienceModelOpenCfg, isVipUser)) {
      setShowTopupModal(true);
      return;
    }

    if (maxChoose === 1 && !showConfirmFooter) {
      onChange([item]);
      // 重置轮播图索引
      setCurrentSlide(0);
      return;
    }

    let newItems = selectedItems || [];

    //多选的情况下，会有选取、反选，单选时只设置一个值
    if (newItems.some(e => e.id === item.id)) {
      newItems = newItems.filter(e => e.id !== item.id);
    } else {
      newItems = [...newItems, item];
    }
    if (newItems.length > maxChoose) {
      setSelectedItems(newItems.slice(newItems.length - maxChoose));
    } else {
      setSelectedItems(newItems);
    }

    // 重置轮播图索引
    setCurrentSlide(0);
  };

  const setSceneType = (type: string) => {
    if (!type) return;
    setShowType(type);

    if (bizTypeList && bizTypeList.length > 0) {
      setShowBizType(null);
    }
  };

  const changeShowSubType = (subType: string, checked: boolean) => {
    if (checked) {
      setShowSubType([...showSubType, subType]);
    } else {
      setShowSubType(showSubType.filter(t => t !== subType));
    }
  };

  const changeSelectTypes = (types: string[], setTypes: (types: string[]) => void, type: string, checked: boolean) => {
    if (checked) {
      setTypes([...types, type]);
    } else {
      setTypes(types.filter(t => t !== type));
    }
  };

  function getSceneShowImgs(item: ElementConfig): string[] {
    if (!item || !item.id) {
      return [];
    }

    // 是否展示姿势图
    if (isShowPosture) {
      // 新逻辑：从缓存中获取姿势图数据
      const cachedImages = postureImagesCache.get(item.id);
      if (cachedImages) {
        return cachedImages;
      }

      // 如果缓存中没有数据，触发异步加载
      loadPostureImages(item);

      // 返回空数组，等待异步加载完成
      return [];
    }

    try {
      // 如果场景为非公开，则显示对应样本前10张图片，作为轮播图
      if (item?.configKey === 'SCENE' && item?.styleScene === true && item?.extInfo && item?.extInfo['openScope'] && item?.extInfo['openScope'] !== 'ALL' && item?.showImgs4PrivateStyleScene) {
        return item.showImgs4PrivateStyleScene;
      }

      if (item.extInfo && item.extInfo['showImgs']) {
        const parsed = JSON.parse(item.extInfo['showImgs']) as string[];
        if (Array.isArray(parsed) && parsed.every(item => typeof item === 'string')) {
          return parsed;
        }
      }
      return [];
    } catch (e) {
      console.error('解析 showImgs 失败:', e);
      return [];
    }
  }

  // 新增：异步加载姿势图数据
  const loadPostureImages = async (item: ElementConfig) => {
    try {
      // 调用batchQueryElementById接口查询子数据
      const elementsDataMap = await batchQueryElementById([item.id]);

      if (!elementsDataMap || !elementsDataMap[item.id]) {
        console.warn(`未找到ID为${item.id}的元素数据`);
        return;
      }

      const elementData = elementsDataMap[item.id] as ElementConfigWithBlobs;
      const children = elementData.children || [];

      // 判断是否为专属场景
      const isExclusiveScene = !!(elementData?.extInfo?.openScope &&
        (typeof elementData.extInfo.openScope === 'number' ||
          (typeof elementData.extInfo.openScope === 'string' && !isNaN(Number(elementData.extInfo.openScope)))));

      // 根据场景类型提取对应的图片
      const images: string[] = [];

      children.forEach((childItem) => {
        let imageUrl: string | undefined;

        if (isExclusiveScene) {
          // 专属场景：展示styleImage数据
          imageUrl = childItem.extInfo?.styleImage;
        } else {
          // 公共场景：展示showImage数据
          imageUrl = childItem.extInfo?.showImage;
        }

        if (imageUrl && typeof imageUrl === 'string') {
          images.push(imageUrl);
        }
      });

      // 更新缓存
      setPostureImagesCache(prev => {
        const newCache = new Map(prev);
        newCache.set(item.id, images);
        return newCache;
      });

      // 触发重新渲染
      setForceUpdate(prev => prev + 1);

      console.log(`加载场景${item.name}的姿势图完成，共${images.length}张图片`);

    } catch (error) {
      console.error('加载姿势图失败:', error);

      // 即使失败也要设置缓存，避免重复请求
      setPostureImagesCache(prev => {
        const newCache = new Map(prev);
        newCache.set(item.id, []);
        return newCache;
      });

      // 触发重新渲染
      setForceUpdate(prev => prev + 1);
    }
  };

  const refresh = () => {
    // 重置分页状态
    setPage(1);
    setHasMore(true);

    let filter = showList;
    switch (showType) {
      case 'recent':
        filter = recentList;
        break;
      case 'favor':
        fetchFavor();
        return;
      default:
        fetchData(false);
        return;
    }

    if (showType === 'recent') {
      // 使用从ElementWithTypeBlock导入的filterByCondition函数
      // 该函数已经更新，支持新的ageRange过滤规则：
      // 当ageRange包含"child"时使用原有筛选逻辑
      // 当ageRange不包含"child"时排除所有包含儿童标签的元素
      filter = filterByCondition(filter, conditions, acceptTypes);
      const unlockedList = config.configKey === 'FACE' ? experienceModelOpenCfg?.faces : experienceModelOpenCfg?.scenes;
      filter = sortByType(filter, typeList, orderByType, unlockedList);
      setShowList(filter);
      setTotal(filter.length);
      setHasMore(false); // 最近使用列表一次性显示完毕
    }
  };

  const fetchTypeTags = async () => {
    const res = await queryDictByKeyAndTags('FACE_TYPES', [['style', 'female'], ['style', 'male'], ['nation'], ['special']]);
    if (res) {
      setTypeTags(res);
    }
    return res;
  };

  const getTypeItemsByTags = (tags: string[]) => {
    return getTypeItemsByTagsFromData(tags, typeTags);
  };

  const getStyleTypeItems = () => {
    if (!FaceBaseType.includes(showType)) {
      return [...getTypeItemsByTags(['style', 'female']), ...getTypeItemsByTags(['style', 'male'])];
    }
    return getTypeItemsByTags(['style', showType === 'male-model' ? 'male' : 'female']);
  };

  // 逐个渲染列表项
  useEffect(() => {
    if (!isMounted.current) return;
    setPage(1);
    refresh();
  }, [showSubType, showType, showBizType, pageSize, name, onlyExperimental, clothCategory, selectSytleTypes, selectNationTypes, searchBodyType, searchPosition]);

  // 页面变化时不再自动刷新，改为由无限滚动控制
  // useEffect(() => {
  //   if (!isMounted.current) return;
  //   refresh();
  // }, [page]);

  useEffect(() => {
    if ((!bizType || bizType === 'ALL') && config.configKey === 'SCENE') {
      setBizTypeList(ALLBizTypes);
    } else {
      setBizTypeList([]);
    }
  }, [bizType]);

  useEffect(() => {
    if (config.configKey === 'SCENE' || !useNewFaceType) {
      getElementTypes(config.configKey).then(res => {
        if (res) {
          let typeList = res;

          let find = ALLBizTypes.find(e => e.key === bizType);
          if (find && find.includesTypes) {
            typeList = typeList.filter(item => (find.includesTypes || []).includes(item.code));
          }
          if (find && find.excludesTypes) {
            typeList = typeList.filter(item => !(find.excludesTypes || []).includes(item.code));
          }

          if (config.configKey === 'FACE') {
            typeList = [{
              code: 'recent',
              name: '最近使用',
            }, {
              code: 'all',
              name: '全部',
            },
              ...typeList];
            // 童模
            typeList = [...typeList, { 'code': 'child-model', name: `童模` }];

            typeList = [...typeList, { code: 'favor', name: '收藏' }];
            // 专属
            typeList = [...typeList, { 'code': 'exclusive', name: `专属${title}` },
            ];
          } else if (config.configKey === 'SCENE') {
            let subTypeList = typeList.map(e => {
              return { key: e.code as BizType, value: e.code as BizType, label: e.name };
            });
            subTypeList = subTypeList.length <= 1 ? [] : subTypeList;
            //@ts-ignore
            setSubTypeList(subTypeList);

            typeList = [{ code: 'recent', name: '最近使用' },
              { code: 'favor', name: '收藏场景' },
              { 'code': 'exclusive', name: `专属${title}` }];
          }

          setTypeList(typeList);
          // setShowSubType(typeList)
          setShowTypeList(typeList);
          setSceneType(selectedShowType);
        }
      });
    }

    const initTypes = async () => {
      const res = await fetchTypeTags();
      if (!res) return;

      let typeList = getTypeItemsByTagsFromData(['special'], res)?.map(item => {
        return { code: item.value, name: item.label };
      });

      let find = ALLBizTypes.find(e => e.key === bizType);
      if (find && find.includesTypes) {
        typeList = typeList.filter(item => (find.includesTypes || []).includes(item.code));
      }
      if (find && find.excludesTypes) {
        typeList = typeList.filter(item => !(find.excludesTypes || []).includes(item.code));
      }

      if (config.configKey === 'FACE') {
        typeList = [{
          code: 'recent',
          name: '最近使用',
        }, {
          code: 'all',
          name: '全部',
        },
          ...FaceBaseType.map(e => {
            return { code: e, name: e === 'female-model' ? '女模' : '男模' };
          }),
          { 'code': 'child-model', name: `童模` },
          ...typeList,
        ];
        typeList = [...typeList, { code: 'favor', name: '收藏' }];
        typeList = [...typeList, { 'code': 'exclusive', name: `专属${title}` }];
      } else if (config.configKey === 'SCENE') {
        let subTypeList = typeList.map(e => {
          return { key: e.code as BizType, value: e.code as BizType, label: e.name };
        });
        subTypeList = subTypeList.length <= 1 ? [] : subTypeList;
        //@ts-ignore
        setSubTypeList(subTypeList);

        typeList = [{ code: 'recent', name: '最近使用' },
          { code: 'favor', name: '收藏场景' },
          { 'code': 'exclusive', name: `专属${title}` }];
      }

      setTypeList(typeList);
      // setShowSubType(typeList)
      setShowTypeList(typeList);
      setSceneType(selectedShowType);
    };

    if (config.configKey === 'FACE') {
      initTypes();
    }

    // 页面加载完成后设置标志位
    isMounted.current = true;
    refresh();

    getClothCategoryCfg().then(res => {
      if (res) {
        setClothCategoryCfg(res);
      }
    });
  }, []);

  useEffect(() => {
    if (!['recent', 'all', 'male-model', 'female-model'].includes(showType)) {
      setSelectSytleTypes([]);
    }
  }, [showType]);

  // 自定义箭头组件，onClick由Carousel自动绑定
  const CustomArrow = ({ direction, onClick }: { direction: 'prev' | 'next'; onClick?: (e: any) => void }) => {
    const ArrowIcon = direction === 'prev' ? LeftOutlined : RightOutlined;

    const handleClick = (e: React.MouseEvent) => {
      e.stopPropagation(); // 阻止事件冒泡
      if (onClick && typeof onClick === 'function') {
        onClick(e);
      }
    };

    return (
      <div
        className={`carousel-arrow ${direction}`}  // 添加类名
        style={{
          width: '32px',
          height: '32px',
          borderRadius: '50%',
          backgroundColor: 'rgba(0, 0, 0, 0.4)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#fff',
          cursor: 'pointer',
          fontSize: '20px',
          position: 'absolute',
          top: '50%',
          transform: 'translateY(-50%)',
          zIndex: 10, // 提高层级确保可点击
          opacity: 1,
          transition: 'all 0.3s ease',  // 添加过渡效果
          ...(direction === 'prev'
            ? { left: '8px' }
            : { right: '8px' }),
        }}
        onClick={handleClick}
      >
        <ArrowIcon />
      </div>
    );
  };

  return (<>
    <Modal
      open={true}
      className="element-selector-modal"
      footer={null}
      title={`选择${title}`}
      onCancel={onClose}
      width={`calc(100vw - 56px)`}
      centered={false}
      styles={{
        header: { display: 'flex', alignItems: 'center', justifyContent: 'center' },
        mask: {
          backgroundColor: 'rgba(0, 0, 0, 0.45)',
          backdropFilter: 'blur(8px)', // 添加毛玻璃效果
        },
        wrapper: {
          padding: '8px 28px',
        },
        content: {
          borderRadius: '24px',
          height: 'calc(100vh - 16px)', // 减去上下wrapper padding (8px * 2)
          overflow: 'hidden',
          padding: 0,
          margin: 0,
          backgroundColor: 'rgba(255, 255, 255, 0.95)', // 半透明背景
          backdropFilter: 'blur(20px)', // 毛玻璃效果
        },
      }}
    >
      <Flex vertical gap={8} className={'element-modal-wrapper'}>

        {/*分类标签行*/}
        <Flex vertical style={{
          background: 'rgba(245, 246, 249, 0.8)',
          width: '100%',
          padding: '8px 16px',
          borderRadius: '8px',
        }} gap={8}>
          <Flex justify={'space-between'} gap={8}>
            <Flex wrap={'wrap'} gap={8}>
              {showTypeList.filter(e => useNewFaceType || config.configKey !== 'FACE' ||
                FaceBaseType.includes(e.code) || e.code === 'all' || e.code === 'exclusive' ||
                e.code === 'recent' || e.code === 'favor' || e.code === 'child-model')
                .filter(e => !e.empty)
                .map(s => (
                  <Button key={s.code}
                          className={showType === s.code ? 'scene-type-item scene-type-item-checked' : 'scene-type-item'}
                          onClick={() => setSceneType(s.code)} disabled={s.empty}>
                    {s.name}
                  </Button>
                ))}

              {/*创建我的模特*/}
              {['FACE', 'SCENE'].includes(config.configKey) && (
                <div
                  className={'create-my-face-container'}
                  onClick={() => navigate(`/upload?type=${config.configKey.toLowerCase()}`)}
                >
                  <IconFont
                    type={'icon-tianjia'}
                    style={{ fontSize: 16, color: '#1A1B1D' }}
                  />
                  <span className={'create-my-face-title'}>创建我的{title}</span>
                </div>
              )}
            </Flex>

            <Flex align="center" gap={12} style={{ flexWrap: 'wrap' }}>
              {['OPERATOR', 'ADMIN'].includes(userInfo?.roleType) &&
                ['FACE', 'SCENE'].includes(config.configKey) && (
                  <Checkbox
                    style={{ fontSize: 12, whiteSpace: 'nowrap', flexShrink: 0 }}
                    checked={onlyExperimental}
                    onChange={e => {
                      setOnlyExperimental(e.target.checked);
                    }}
                  >
                    {config.configKey === 'FACE' ? '实验模特' : '实验场景'}
                  </Checkbox>
                )}
              {config.configKey === 'SCENE' &&
                <>
                  <Select options={bodyTypeOptions} onChange={e => setSearchBodyType(e)}
                          style={{width: 100}} placeholder={'正/背面'}/>

                  <Select options={positionOptions} onChange={e => setSearchPosition(e)}
                          style={{width: 100}} placeholder={'全/半身'}/>
                </>
              }
              {config.configKey === 'SCENE' && clothCategoryCfg && clothCategoryCfg.length > 0 && (
                <div style={{ minWidth: '250px', maxWidth: '400px', flexGrow: 1 }}>
                  <ClothCategorySelect
                    value={clothCategory}
                    clothCategoryCfg={clothCategoryCfg}
                    onChange={(value) => {
                      setClothCategory(value);
                      setActiveSearchField('clothCategory');
                    }}
                    onFocus={() => {
                      setActiveSearchField('clothCategory');
                    }}
                  />
                </div>
              )}
              <Input
                placeholder="按名称搜索"
                value={searchValue}
                onChange={e => {
                  const value = e.target.value;
                  setSearchValue(value); // 立即更新输入框显示
                  setActiveSearchField('name');
                  
                  // 如果清空了输入框，立即触发搜索
                  if (!value) {
                    clearDebounceTimer();
                    setName(null);
                  } else {
                    // 否则使用防抖搜索
                    debouncedSearch(value);
                  }
                }}
                onFocus={() => {
                  setActiveSearchField('name');
                }}
                allowClear
                maxLength={20}
                style={{ width: 200 }}
              />
            </Flex>
          </Flex>

          {bizTypeList && bizTypeList.length > 0 &&
            <Flex wrap={'wrap'} gap={8}>
              {bizTypeList.map(s => (
                <Button key={s.key} onClick={() => changeShowBizType(s.key)}
                        className={(showBizType === s.key ? ('scene-type-item scene-type-item-checked' + (BIZ_TYPE_CLASS_MAP[s.key] ? ' ' + BIZ_TYPE_CLASS_MAP[s.key] : '')) : 'scene-type-item')}>
                  {s.iconType && <IconFont type={s.iconType} style={{ fontSize: 16 }} />}
                  {s.label}
                </Button>
              ))}
            </Flex>
          }

          {config.configKey === 'FACE' && !useNewFaceType &&
            <Flex wrap={'wrap'} gap={8}>
              {showTypeList.filter(e => !FaceBaseType.includes(e.code) && e.code !== 'exclusive' && e.code !== 'recent' && e.code !== 'all' && e.code !== 'child-model').map(s => (
                <Checkbox key={s.code} onChange={e => changeShowSubType(s.code, e.target.checked)}
                          style={{ fontSize: 12 }} checked={showSubType.includes(s.code)}><span
                  className={'color-72'}>{s.name}</span>
                </Checkbox>
              ))}
            </Flex>
          }

          {config.configKey === 'FACE' && useNewFaceType &&
            <>
              {['recent', 'all', 'male-model', 'female-model'].includes(showType) &&
                <Flex wrap={'wrap'} gap={8} align={'center'}>
                  <div className={'text14 weight color-1a margin-right-8'}>风格</div>
                  {getStyleTypeItems().map(s => (
                    <Checkbox key={s.value}
                              onChange={e => changeSelectTypes(selectSytleTypes, setSelectSytleTypes, s.value, e.target.checked)}
                              style={{ fontSize: 12 }} checked={selectSytleTypes.includes(s.value)}><span
                      className={'color-72'}>{s.label}</span>
                    </Checkbox>
                  ))}
                </Flex>
              }

              <Flex wrap={'wrap'} gap={8} align={'center'}>
                <div className={'text14 weight color-1a margin-right-8'}>国别</div>
                {getTypeItemsByTags(['nation']).map(s => (
                  <Checkbox key={s.value}
                            onChange={e => changeSelectTypes(selectNationTypes, setSelectNationTypes, s.value, e.target.checked)}
                            style={{ fontSize: 12 }} checked={selectNationTypes.includes(s.value)}><span
                    className={'color-72'}>{s.label}</span>
                  </Checkbox>
                ))}
              </Flex>
            </>
          }

          {subTypeList && subTypeList.length > 0 &&
            <Flex wrap={'wrap'} gap={8}>
              {subTypeList.map(s => (
                <Checkbox key={s.key} onChange={e => changeShowSubType(s.key, e.target.checked)}
                          style={{ fontSize: 12 }} checked={showSubType.includes(s.key)}><span
                  className={'color-72'}>{s.label}</span>
                </Checkbox>
              ))}
            </Flex>
          }

        </Flex>

        {/*下方列表区*/}
        <div className={'element-modal-img-block'} style={{ flex: 1, height: '100%', position: 'relative' }}>

          {/*下方&左侧轮播图-空白-没选择场景*/}
          {(!selectedItems || selectedItems.length < 1) &&
            <div className={'element-carousel-empty-container'}>
              <Flex vertical={true} align="center" justify="center">
                <IconFont type={'icon-lujing5'} style={{ fontSize: 24 }} />
                <span className={'element-carousel-empty-title'}>暂未选择</span>
                <span className={'element-carousel-empty-content'}>右侧选择场景后展示该场景AI生成图效果</span>
              </Flex>
            </div>
          }

          {/*下方&左侧轮播图-空白-选择了没有配置轮播图的场景*/}
          {(selectedItems?.length >= 1 && getSceneShowImgs(selectedItems[selectedItems.length - 1])?.length === 0) &&
            <div className={'element-carousel-empty-container'}>
              <Flex vertical={true} align="center" justify="center">
                <IconFont type={'icon-lujing5'} style={{ fontSize: 24 }} />
                <span className={'element-carousel-empty-title'}>暂无示例图片，去出图试试吧~</span>
              </Flex>
            </div>
          }

          {/*下方&左侧轮播图-有内容*/}
          {selectedItems?.length >= 1 && getSceneShowImgs(selectedItems[selectedItems.length - 1])?.length > 0 &&
            <Flex vertical={true} align={'flex-start'} justify={'flex-start'} className={'element-carousel-container'}>

              <div
                className={`custom-carousel-container ${carouselHoverSide ? `hover-${carouselHoverSide}` : ''}`}
                onMouseMove={(e) => {
                  const { clientX, currentTarget } = e;
                  const { left, width } = currentTarget.getBoundingClientRect();
                  const mousePosition = clientX - left;

                  // 判断鼠标在左半侧还是右半侧
                  if (mousePosition < width / 2) {
                    setCarouselHoverSide('left');
                  } else {
                    setCarouselHoverSide('right');
                  }
                }}
                onMouseLeave={() => setCarouselHoverSide(null)}
              >
                <Carousel
                  ref={carouselRef}
                  fade={true}
                  autoplay={true}
                  arrows={true}
                  prevArrow={<CustomArrow direction="prev" />}
                  nextArrow={<CustomArrow direction="next" />}
                  autoplaySpeed={1500}
                  infinite={true}
                  dots={false}
                  beforeChange={(from, to) => setCurrentSlide(to)}
                  className="custom-carousel"
                >
                  {getSceneShowImgs(selectedItems[selectedItems.length - 1])?.map((url, index) => (
                    <div key={index} style={{ borderRadius: 8, position: 'relative' }}>
                      <img src={url} alt={`style-${index}`} className={'element-carousel-img'} />
                      {/* 左下角悬浮信息 */}
                      <div
                        style={{
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          background: 'linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100%)',
                          padding: '16px 12px 12px',
                          borderRadius: '0 0 8px 8px',
                          color: '#fff',
                          textAlign: 'left',
                        }}
                      >
                        <div
                          className={'carousel-config-name'}
                          style={{
                            fontSize: '16px',
                            fontWeight: 500,
                            marginBottom: '4px',
                            color: '#fff',
                            textAlign: 'left',
                          }}
                        >
                          {selectedItems[selectedItems.length - 1].name}
                        </div>

                        <div style={{ color: '#fff', textAlign: 'left' }}>
                          <div style={{
                            fontSize: '12px',
                            color: 'rgba(255, 255, 255, 0.8)',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '4px',
                            justifyContent: 'flex-start',
                          }}>
                            <span style={{ flexShrink: 0 }}>适用范围：</span>
                            <ElementTags
                              types={selectedItems[selectedItems.length - 1].type}
                              typeOptions={[...bodyTypeOptions, ...positionOptions]}
                              style={{
                                justifyContent: 'flex-start',
                                alignItems: 'center',
                                marginTop: 0,
                                gap: '4px',
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </Carousel>

                {/* 显示数字计数器 */}
                {getSceneShowImgs(selectedItems[selectedItems.length - 1])?.length > 1 && (
                  <div
                    style={{
                      position: 'absolute',
                      bottom: '-20px',
                      right: '12px',
                      background: 'rgba(0, 0, 0, 0.7)',
                      color: 'white',
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      fontWeight: '500',
                      zIndex: 4,
                      boxShadow: '0 2px 6px rgba(0, 0, 0, 0.3)',
                      backdropFilter: 'blur(4px)',
                    }}
                  >
                    {currentSlide + 1} / {getSceneShowImgs(selectedItems[selectedItems.length - 1])?.length}
                  </div>
                )}
              </div>
            </Flex>
          }

          {/*下方&右侧的场景、模特列表 - 无限滚动布局*/}
          <div
            id="element-list-scroll-container"
            className="element-list-scroll-container"
            onScroll={handleScroll}
            style={{
              flex: 1,
              overflow: 'auto', // 改回 auto 以便监听滚动事件
              minHeight: 0,
              maxHeight: '100%',
            }}
          >

            <div className="responsive-element-grid">
              {showList.map((item) => (
                <div
                  key={showType + item.id}
                  style={{
                    position: 'relative',
                    overflow: 'hidden',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                  }}
                  onClick={() => handleChange(item)}
                  className={'work-item element-modal-item' + (selectedItems && selectedItems.length > 0 && selectedItems.some(e => e.id === item.id) ? ' work-item-selected' : '')}
                >
                  {/* 上新标签 */}
                  {item.isNew &&
                    <NewLabel width={60} height={26} top={-4} left={-5} />
                  }

                  <div
                    className="element-image-wrapper"
                    style={{
                      width: '100%',
                      borderRadius: '8px 8px 0 0',
                    }}
                  >
                    <img
                      alt="img"
                      src={item.showImage}
                      className="element-image-item"
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        display: 'block',
                      }}
                    />

                    {item.extInfo && item.extInfo['openScope'] && item.extInfo['openScope'] !== 'ALL' &&
                      <MiniTag title={'专属'} position={'rightTop'} textFontSize={13} />
                    }

                    {maxChoose > 1 && selectedItems && selectedItems.length > 0 && selectedItems.some(e => e.id === item.id) &&
                      <div style={{ position: 'absolute', right: 4, top: 4 }}>
                        <IconFont type={'icon-gou-lan'} style={{ fontSize: 32, color: '#366EF4' }} />
                      </div>
                    }

                    {isLockedElement(loraType, config.configKey, item.id, experienceModelOpenCfg, isVipUser) &&
                      <ElementLockMask />
                    }

                    <div className={'element-block-item-block-right'}>
                      <UserFavorButton className={'element-block-favor-button'} favorType={'ELEMENT'}
                                       itemId={item.id} />
                    </div>

                  </div>
                  <div className="element-block-item-name">
                    {item.name}
                  </div>
                </div>
              ))}
            </div>

            {/* 加载状态指示器 */}
            {isLoadingMore && (
              <div style={{
                textAlign: 'center',
                padding: '20px',
                color: '#666',
              }}>
                <IconFont type="icon-jiazai" style={{ fontSize: '16px', marginRight: '8px' }} />
                正在加载更多...
              </div>
            )}

            {/* 结束提示 */}
            {!hasMore && showList.length > 0 && (
              <div style={{
                textAlign: 'center',
                padding: '20px',
                color: '#999',
                fontSize: '14px',
              }}>
                已显示全部
                {/* {showList.length} 个{title} */}
              </div>
            )}
          </div>

          {/* 底部固定的操作按钮 */}
          <div className="element-modal-footer" style={{ overflow: 'visible' }}>
            <Flex justify="space-between" align="center" wrap="wrap" gap={16}>
              {/* 左侧显示总数信息 */}
              <div style={{ flex: 1, minWidth: 200 }}>
                <span style={{ fontSize: '14px', color: '#666' }}>
                  {showList.length > 0 && `共 ${total} 个${title}`}
                </span>
              </div>

              {/* 右侧操作按钮 */}
              {showConfirmFooter && (
                <Flex gap={16} style={{ flexShrink: 0 }}>
                  <Button size="small" onClick={onClose} className="cancel-btn">
                    取消
                  </Button>
                  <Button
                    type="primary"
                    onClick={() => onChange(selectedItems)} className="ok-btn">
                    确定
                  </Button>
                </Flex>
              )}
            </Flex>
          </div>

        </div>
      </Flex>
    </Modal>

    {showTopupModal && !isTrialAccount &&
      <TopupModal visible={showTopupModal} onClose={() => setShowTopupModal(false)}
                  onPaySuccess={() => setShowTopupModal(false)} />
    }

  </>);
};

export default ElementWithTypeSelector;