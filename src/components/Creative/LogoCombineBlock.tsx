import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { <PERSON><PERSON>, Button, Flex, message, Tooltip, Spin } from 'antd';
import './LogoCombineDrawer.less';
import { Image as KonvaImage, Layer, Stage } from 'react-konva';
import useImage from 'use-image';
import MovableImage from '@/components/Creative/MovableImage';
import { LoadingOutlined } from '@ant-design/icons';
import { fetchOssBlobUrl } from '@/utils/ossUtils';
import { removeBg } from '@/services/CreativeController';
import { deepCopy, download, getImageNameWithTag } from '@/utils/utils';
import SmudgeBrushComponent from '@/components/imageOperate/smudgeBrush/SmudgeBrushComponent';
import { BrushType } from '../imageOperate/types';
import { createImageErase, queryImageErase } from '@/services/ImageOperateController';
import { IMAGE_OPERATE_ALIYUN } from '@/constants';

export interface LogoCombineBlockProps {
  isOpenSmudgeBrush: boolean;
  setIsOpenSmudgeBrush: (value: boolean) => void;
  movableImageUrl: string | null;
  movableFile: File | null;
  bgImageUrl?: string | null | undefined;
  bgFile: File | null;
  onCancel: () => void;
  onDisableRepairButton?: () => void;
}

export interface LogoCombineBlockRef {
  clearTransformer: () => void;
  getDataUrl: () => string;
  downloadImage: () => void;
}

const defaultBlockHeight = window.innerHeight - 56 - 38 - 16; //56->导航栏高度，38->提醒栏高度，138可移动图片demo的位置
const defaultBlockWidth = 57;

const LogoCombineBlock = forwardRef<LogoCombineBlockRef, LogoCombineBlockProps>(({
  isOpenSmudgeBrush,
  setIsOpenSmudgeBrush,
  movableImageUrl,
  movableFile,
  bgImageUrl,
  onCancel,
  onDisableRepairButton,
}, ref) => {

  // 图片宽度 
  const [imageWidth, setImageWidth] = useState(defaultBlockWidth);
  // 图片高度
  const [imageHeight, setImageHeight] = useState(defaultBlockHeight);

  // 背景图片blob url
  const [bgImageBlobUrl, setBgImageBlobUrl] = useState('');
  // 涂抹图片blob url
  const [maskImageUrl, setMaskImageUrl] = useState('');
  // 去背景1图片blob url
  const [rbgMovableImageBlobUrl, setRbgMovableImageBlobUrl] = useState('');
  // 去背景2图片blob url
  const [rbgMovableImageBlobUrl2, setRbgMovableImageBlobUrl2] = useState('');

  // @ts-ignore
  const [bgImage] = useImage(bgImageBlobUrl, 'Anonymous');
  // @ts-ignore 该字段只是用来初始化logo图片长宽
  const [logo] = useImage(movableImageUrl, 'Anonymous');

  // 是否显示去背景1和去背景2的提示
  const [showTooltip, setShowTooltip] = useState([false, false]);

  // 当前logo图片 
  const [logoImage, setLogoImage] = useState<any>(null);
  // 是否选中logo图片
  const [selected, setSelected] = useState<any>(false);
  // 是否开启去背景
  const [enableRemoveBG, setEnableRemoveBG] = useState(true);

  // 舞台引用
  const stageRef = useRef(null);
  // 背景图片引用
  const bgRef = useRef(null);

  // 是否正在处理图片
  const [isProcessing, setIsProcessing] = useState(false);
  // 定时器引用
  const timerRef = useRef<ReturnType<typeof setInterval> | null>(null);

  useEffect(() => {
    window.addEventListener('resize', resize);
    return () => {
      window.removeEventListener('resize', resize);
    };
  }, []);

  // 打开涂抹工具时，清空logo图片
  useEffect(() => {
    if (isOpenSmudgeBrush) {
      setLogoImage(null);
    }
  }, [isOpenSmudgeBrush]);

  // 开启去背景
  useEffect(() => {
    setEnableRemoveBG(true);

    if (!movableFile) {
      return;
    }

    doRemoveBG(0);
    doRemoveBG(1);
  }, [movableFile]);

  // 设置logo图片
  useEffect(() => {
    if (logoImage) {
      setRbgMovableImageBlobUrl('');
      const copy = deepCopy(logoImage);
      copy.src = movableImageUrl;
      setLogoImage(copy);
    }
  }, [movableImageUrl]);

  // 设置背景图片
  useEffect(() => {
    if (bgImageUrl) {
      loadBgImage(bgImageUrl);
    }
  }, [bgImageUrl]);

  // 设置舞台大小
  useEffect(() => {
    resize();
  }, [bgImage]);

  // 检查taskId并开始轮询
  useEffect(() => {
    const checkAndStartPolling = () => {
      const taskId = localStorage.getItem('taskId');

      // 如果taskId存在，则开始轮询
      if (taskId) {
        // 设置处理状态
        setIsProcessing(true);

        // 开始轮询
        startPolling();
      }
    };

    // 组件挂载时立即检查
    checkAndStartPolling();

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, []); // 仅在组件挂载时执行一次

  // 开始轮询
  const startPolling = () => {
    // 确保之前的定时器被清除
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    // 立即执行一次
    queryTaskStatus();
    // 设置定时器，每秒执行一次
    timerRef.current = setInterval(() => {
      queryTaskStatus();
    }, 3000);
  };

  // 每隔一秒去查询任务状态
  const queryTaskStatus = () => {
    const taskId = localStorage.getItem('taskId');
    if (taskId) {
      queryImageErase(IMAGE_OPERATE_ALIYUN, taskId).then(res => {
        if (!res) {
          return;
        }

        if (res.taskStatus === 'SUCCEEDED') {
          // 任务完成，清除定时器和taskId
          if (timerRef.current) {
            clearInterval(timerRef.current);
            timerRef.current = null;
          }

          // 设置背景图片
          fetchOssBlobUrl(res.outputImageUrl).then(blobUrl => {
            if (blobUrl) {
              // 设置背景图片
              setBgImageBlobUrl(blobUrl);

              // 关闭处理状态
              setIsProcessing(false);

              // 清除taskId
              localStorage.removeItem('taskId');

              // 清除原图片
              localStorage.removeItem('originalImage');
            }
          });
        } else if (res.taskStatus === 'FAILED') {
          // 任务失败，清除定时器和taskId
          if (timerRef.current) {
            clearInterval(timerRef.current);
            timerRef.current = null;
          }

          // 关闭处理状态
          setIsProcessing(false);

          // 
          message.error('图片处理失败，请重试');

          // 清除taskId
          localStorage.removeItem('taskId');
          // 清除原图片
          localStorage.removeItem('originalImage');
        }


      }).catch(error => {
        // 发生错误时也清除定时器和taskId
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }
        // 清除taskId
        localStorage.removeItem('taskId');
        // 清除原图片
        localStorage.removeItem('originalImage');

        // 查询失败则清空
        message.error('查询任务状态失败，请重试');
      });
    }
  };

  // 执行图片擦除
  const doRemoveBG = (index) => {
    removeBg(movableFile, index + 1).then(res => {
      if (!res) {
        return;
      }

      res.blob().then(blob => {
        if (index === 0) {
          setRbgMovableImageBlobUrl(URL.createObjectURL(blob));
        } else {
          setRbgMovableImageBlobUrl2(URL.createObjectURL(blob));
        }
        setShowTooltip(prevStates => {
          const newStates = [...prevStates];
          newStates[index] = true;
          return newStates;
        });
        setTimeout(() => {
          setShowTooltip(prevStates => {
            const newStates = [...prevStates];
            newStates[index] = false;
            return newStates;
          });
        }, 8000);
      });
    });
  };

  // 设置舞台大小
  const resize = () => {
    if (!bgImage) {
      setImageHeight(defaultBlockHeight);
      setImageWidth(defaultBlockWidth);

      return;
    }
    // @ts-ignore
    const [originWidth, originHeight] = [bgImage.naturalWidth, bgImage.naturalHeight];
    //计算图片宽高
    // @ts-ignore
    const height = defaultBlockHeight;//calc(100vh - @repair-header-height - 16px - 38px - 8px);
    const width = height / originHeight * originWidth;

    setImageHeight(height);
    setImageWidth(width);

    if (!logoImage && logo) {
      // @ts-ignore
      const [logoWidth, logoHeight] = [logo.naturalWidth, logo.naturalHeight];
      const targetWidth = width / 10;//背景图片的1/3
      const targetHeight = targetWidth / logoWidth * logoHeight;
      setLogoImage({
        src: movableImageUrl,
        x: (width - targetWidth) / 2,
        y: (height - targetHeight) / 2,
        width: targetWidth,
        height: targetHeight,
      });
    }
  };

  // 设置背景图片
  const loadBgImage = (bgImageUrl: string) => {
    if (bgImageUrl.startsWith('http://') || bgImageUrl.startsWith('https://')) {
      fetchOssBlobUrl(bgImageUrl).then(blobUrl => {
        if (blobUrl) {
          fetch(blobUrl)
            .then(res => res.blob())
            .then(blob => {
              const reader = new FileReader();
              reader.onloadend = () => {
                setBgImageBlobUrl(reader.result as string);
              };
              reader.readAsDataURL(blob);
            });
        }
      });
      return;
    }

    setBgImageBlobUrl(bgImageUrl);
  };

  // 设置logo图片
  const changeLogo = (newAttrs) => {
    setLogoImage(newAttrs);
  };

  // 切换logo图片
  const changeLogoSrc = (src: string) => {
    if (src) {
      const copy = deepCopy(logoImage);
      copy.src = src;
      setLogoImage(copy);
    }
  };

  useImperativeHandle(ref, () => ({
    downloadImage() {
      const dataUrl = this.getDataUrl();
      const fileName = bgImageUrl ? getImageNameWithTag(bgImageUrl, 'repair_detail') : 'temp.jpg';
      download(dataUrl, fileName);
    },
    getDataUrl() {
      if (!bgImage) {
        message.warning('请先上传背景图');
        return null;
      }

      const scale = bgImage.naturalHeight / imageHeight;
      const stage = stageRef.current;

      // @ts-ignore
      return stage.toDataURL({ pixelRatio: scale, quality: 1 });
    },
    clearTransformer() {
      setSelected(false);
    },
  }));


  // 执行图片擦除
  const doEraseBgImage = async (base64: string) => {
    // mask Base64
    const maskImage = base64;

    if (!bgImageBlobUrl) {
      message.warning('请先上传背景图');
      return;
    }

    if (!maskImage) {
      message.warning('请先选择传涂抹图');
      return;
    }

    const taskId = await createImageErase(IMAGE_OPERATE_ALIYUN, {
      originalImageBase64: bgImageBlobUrl,
      maskImageBase64: maskImage,
    });

    // 如果任务id存在，则表示任务正在执行，需要等待任务完成 
    if (taskId !== undefined && taskId) {
      // 将 taskId 存储到 localStorage 中
      localStorage.setItem('taskId', taskId);

      // 添加原图片到 localStorage
      if (bgImageUrl) {
        localStorage.setItem('originalImage', bgImageUrl);
      }

      // 关闭涂抹工具
      setIsOpenSmudgeBrush(false);

      // 开始轮询任务状态
      startPolling();

      // 页面进入处理状态
      setIsProcessing(true);

      // 禁用细节修复按钮
      onDisableRepairButton?.();
    }
  };

  const RBGBlock = ({ blobUrl, index }) =>
    <div
      className={'logo-combine-drawer-bg-block' + (logoImage && logoImage.src === blobUrl ? ' border-selected' : '')}
      style={{
        background: !blobUrl ? `url(${movableImageUrl})` : '',
        backgroundSize: !blobUrl ? '84px 84px' : '',
      }}
      onClick={() => changeLogoSrc(blobUrl)}>

      <Tooltip placement={'leftTop'} title={`方案${index}去除背景已完成，点击切换`} open={showTooltip[index - 1]}>
        <img src={blobUrl} width={84} height={84} alt={''} />
      </Tooltip>
      <div className={'repair-hands-tag logo-combine-drawer-bg-tag'}>
        <div className={'text14 font-pf color-w'}>去背景{index}</div>
      </div>

      {!blobUrl &&
        <Flex className={'logo-combine-drawer-bg-loading'} justify={'center'} align={'center'}>
          <LoadingOutlined style={{ fontSize: 24, color: 'rgba(255, 255, 255, 0.85)' }} />
        </Flex>
      }
    </div>;

  const NeedRBGBlock = () =>
    <Flex justify={'center'} align={'center'} gap={8} className={'logo-combine-drawer-bg-block'}
      onClick={() => setEnableRemoveBG(true)}>

      <Button style={{ height: 68, width: 68 }}>
        <Flex vertical>
          <div>点击</div>
          <div>去除背景</div>
        </Flex>
      </Button>
    </Flex>;


  return <>
    <Spin
      spinning={isProcessing}
      tip="图片正在处理中，请稍候..."
      wrapperClassName="logo-combine-spin"
    >
      <Flex vertical gap={8} align={'center'} justify={'flex-start'} style={{ height: '100%' }}>

        <Alert type="info" showIcon className={'color-brand text14 font-pf width-100'}
          message="将图案调整到合适的位置" />

        <Flex justify={'space-between'} align={'flex-start'} className={'width-100'}>

          <Flex justify={'center'} align={'center'} className={'logo-combine-drawer-image-card'}>
            <Flex justify={'center'} align={'center'}>
              {isOpenSmudgeBrush ? (
                <SmudgeBrushComponent
                  BrushType={BrushType.ERASER}
                  isUseWhiteBackgroundForMark={false}
                  bgImage={bgImageBlobUrl}
                  onSave={doEraseBgImage}
                  onCancel={onCancel}
                  onStartRepair={onDisableRepairButton}
                />
              ) : (
                <Stage
                  width={imageWidth}
                  height={imageHeight}
                  ref={stageRef}
                  onMouseLeave={() => setSelected(false)}
                  className={"repair-stage"}
                  onMouseEnter={() => {
                    if (!selected) {
                      setSelected(true);
                    }
                  }}
                >
                  <Layer>
                    <KonvaImage image={bgImage} width={imageWidth} height={imageHeight} ref={bgRef} />
                    {logoImage && (
                      <MovableImage
                        image={logoImage}
                        maskImage={{ src: maskImageUrl, width: imageWidth, height: imageHeight }}
                        isSelected={selected}
                        onChange={setLogoImage}
                      />
                    )}
                  </Layer>
                </Stage>
              )}
            </Flex>
          </Flex>


          {
            movableImageUrl &&
            <Flex vertical justify={'flex-start'} align={'center'} gap={8}
              className={'logo-combine-drawer-color-selector'}>
              <div
                className={'logo-combine-drawer-bg-block' + (logoImage && logoImage.src !== rbgMovableImageBlobUrl && logoImage.src !== rbgMovableImageBlobUrl2 ? ' border-selected' : '')}
                onClick={() => {
                  if (movableImageUrl) {
                    changeLogoSrc(movableImageUrl);
                  }
                }}>
                {movableImageUrl && <img src={movableImageUrl} width={84} height={84} alt={''} />}
                <div className={'repair-hands-tag logo-combine-drawer-bg-tag'}>
                  <div className={'text14 font-pf color-w'}>原图</div>
                </div>
              </div>

              {enableRemoveBG &&
                <>
                  <RBGBlock blobUrl={rbgMovableImageBlobUrl} index={1} />
                  <RBGBlock blobUrl={rbgMovableImageBlobUrl2} index={2} />
                </>
              }

              {!enableRemoveBG &&
                <>
                  <NeedRBGBlock />
                  <NeedRBGBlock />
                </>
              }

            </Flex>
          }

        </Flex>

      </Flex>
    </Spin>
  </>
    ;
})
  ;

export default LogoCombineBlock;