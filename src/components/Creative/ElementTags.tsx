import React, { useMemo, useState } from 'react';
import { Flex, Tooltip } from 'antd';

interface ElementTagsProps {
  /** 元素类型数组 */
  types: string[];
  /** 类型选项配置 */
  typeOptions: Array<{ value: string; label: string }>;
  /** 容器样式类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 最大显示标签数量，超出部分显示为 +N */
  maxDisplay?: number;
  /** 点击标签回调 */
  onTagClick?: (type: string, label: string) => void;
  /** 是否显示工具提示 */
  showTooltip?: boolean;
}

/**
 * 元素标签组件 - 用于显示元素的类型标签
 * 支持多种类型配置，自动过滤和显示匹配的标签
 * 
 * 特性：
 * - 自动过滤匹配的类型标签
 * - 支持最大显示数量限制
 * - 支持点击事件
 * - 优雅的动画效果
 * - 响应式设计
 */
const ElementTags: React.FC<ElementTagsProps> = ({
  types = [],
  typeOptions = [],
  className = '',
  style = {},
  maxDisplay,
  onTagClick,
  showTooltip = true
}) => {
  const [hoveredTag, setHoveredTag] = useState<string | null>(null);

  // 计算需要显示的标签
  const { displayTags, hiddenCount } = useMemo(() => {
    if (!types?.length || !typeOptions?.length) {
      return { displayTags: [], hiddenCount: 0 };
    }
    
    // 获取所有可用的类型值
    const availableTypes = typeOptions.map(option => option.value);
    
    // 过滤出匹配的类型并获取对应标签
    const filteredTags = types
      .filter(type => availableTypes.includes(type))
      .map(type => {
        const option = typeOptions.find(opt => opt.value === type);
        return {
          value: type,
          label: option?.label || type
        };
      });

    // 如果设置了最大显示数量，则进行截取
    if (maxDisplay && filteredTags.length > maxDisplay) {
      return {
        displayTags: filteredTags.slice(0, maxDisplay),
        hiddenCount: filteredTags.length - maxDisplay
      };
    }

    return {
      displayTags: filteredTags,
      hiddenCount: 0
    };
  }, [types, typeOptions, maxDisplay]);

  // 处理标签点击
  const handleTagClick = (type: string, label: string) => {
    onTagClick?.(type, label);
  };

  // 渲染标签内容
  const renderTag = (tag: { value: string; label: string }, index: number) => {
    const isHovered = hoveredTag === tag.value;
    const isClickable = !!onTagClick;

    const tagElement = (
      <div 
        key={`${tag.value}-${index}`} 
        className={`element-tag ${isClickable ? 'element-tag-clickable' : ''}`}
        onClick={() => isClickable && handleTagClick(tag.value, tag.label)}
        onMouseEnter={() => setHoveredTag(tag.value)}
        onMouseLeave={() => setHoveredTag(null)}
        style={{
          cursor: isClickable ? 'pointer' : 'default',
          transform: isHovered && isClickable ? 'scale(1.05)' : 'scale(1)',
        }}
      >
        <span className="element-tag-text">{tag.label}</span>
      </div>
    );

    // 如果需要显示工具提示且标签可点击
    if (showTooltip && isClickable) {
      return (
        <Tooltip key={`${tag.value}-${index}`} title={`点击查看 ${tag.label} 相关内容`}>
          {tagElement}
        </Tooltip>
      );
    }

    return tagElement;
  };

  // 如果没有标签要显示，则不渲染
  if (displayTags.length === 0) {
    return null;
  }

  return (
    <Flex 
      className={`element-tags-container ${className}`}
      style={style}
    >
      {displayTags.map(renderTag)}
      
      {/* 显示隐藏的标签数量 */}
      {hiddenCount > 0 && (
        <div className="element-tag element-tag-more">
          <span className="element-tag-text">+{hiddenCount}</span>
        </div>
      )}
    </Flex>
  );
};

export default ElementTags; 