.custom-scene-text .ant-input-data-count {
  bottom: 3px;
  right: 10px;
}

.scene-type-segmented .ant-segmented-item-label {
  width: 78px;
}

.custom-scene-preset-item {
  //width: 68px;
  height: 26px;
  border-radius: 32px;
  opacity: 1;

  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 4px 16px;

  box-sizing: border-box;
  border: 1px solid #B5C7FF;
}

.custom-scene-preset-item:hover {
  border: 1px solid #618DFF;
  box-shadow: 4px 4px 10px 0 rgba(0, 0, 0, 0.1);
}

.create-my-face-container {
  height: 30px;
  border-radius: 32px;

  /* 自动布局 */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  gap: 4px;

  /* 渐变边框实现 - 替代方案 */
  background: linear-gradient(#fff, #fff) padding-box,
    linear-gradient(90deg, #99C0FF 0%, #D4C9F7 100%) border-box;
  border: 1px solid transparent;

  z-index: 3;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 0 8px rgba(151, 192, 255, 0.5);
  }
}
.custom-scene-preference-block {
  padding: 0 0 12px 0 !important;
  background: #F5F6F9 !important;
  border: 0;
  .preference-block-title {
    font-size: 12px;
    color: #969799;
  }
}


.create-my-face-title {
  height: 22px;
  opacity: 1;

  font-family: 'DingTalk JinBuTi', sans-serif;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: 0em;

  /* 渐变文字实现 */
  background-image: linear-gradient(180deg, #2763DB 0%, #D025FF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}