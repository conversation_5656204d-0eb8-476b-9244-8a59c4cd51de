import { Flex } from 'antd';
import React from 'react';

interface HalfSelectItemBlockProps {
  image: string;
  title: string;
  description?: string;
  current: any;
  value: any;
  onClick: (value: any) => void;
}

const HalfSelectItemBlock: React.FC<HalfSelectItemBlockProps> = ({
                                                                   image,
                                                                   title,
                                                                   description,
                                                                   current,
                                                                   value,
                                                                   onClick,
                                                                 }) => {
  return <>
    <Flex gap={12} align={'center'} onClick={() => onClick(value)} style={{ background: '#FFFFFF' }}
          className={'logo-position-wrapper' + (current === value ? ' work-item-selected' : '')}>
      <img alt="img" src={image} width={42} height={42} style={{ borderRadius: 4 }} />
      <Flex vertical gap={2} align={'flex-start'}>
        <div className={'text14 font-pf color-1a text-center'}>{title}</div>
        {description &&
          <div className={'text14 font-pf color-72 text-center'}>{description}</div>
        }
      </Flex>
    </Flex>
  </>;
};

export default HalfSelectItemBlock;