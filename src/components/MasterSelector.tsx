import React, { useEffect } from 'react';
import { getDistributorCustomerByPage } from '@/services/DistributorController';
import { queryAllMaster, ROLE_TYPE } from '@/services/UserController';
import { Select } from 'antd';

interface MasterSelectorProps {
  value?: number;
  roleTypes?: Array<ROLE_TYPE> | null;
  placeholder?: string;
  width?: number;
  excludes?: Array<number>;
  onChange: (userId: number) => void;
}

const MasterSelector: React.FC<MasterSelectorProps> = ({
                                                         value,
                                                         roleTypes = null,
                                                         width = 434,
                                                         excludes = [],
                                                         placeholder = null,
                                                         onChange,
                                                       }) => {
  const [masterOptions, setMasterOptions] = React.useState<any[]>([]);

  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

  useEffect(() => {
    if (!userInfo) {
      return;
    }

    if (userInfo?.roleType !== 'DISTRIBUTOR') {
      queryAllMaster(roleTypes).then(res => {
        if (res && Array.isArray(res)) {
          fillMasterOptions(res);
        }
      });
    } else {
      //渠道商角色下，出现下拉菜单
      getDistributorCustomerByPage({
        pageNum: 1,
        pageSize: 1000,
        orderBy: 'id desc',
        includeCreatedCustomer: false,
        statusNotIn: ['REJECT', 'DISABLED'],
      }).then(res => {
        if (res && Array.isArray(res.list)) {
          fillMasterOptions(res.list);
        }
      });
    }
  }, []);

  const fillMasterOptions = (data: any[]) => {
    if (!data || data.length <= 0) {
      return;
    }
    const masterOptions = [];
    data.forEach(item => {
      // @ts-ignore
      masterOptions.push({
        label: item?.corpName ? item.nickName + '@' + item.corpName : item.nickName,
        value: item.id,
      });
    });
    setMasterOptions(masterOptions);
  };

  return <Select options={masterOptions.filter(item => !excludes.includes(Number(item.value)))} style={{ width }}
                 allowClear showSearch optionFilterProp="label"
                 onChange={onChange} defaultValue={value} placeholder={placeholder} />;
};

export default MasterSelector;