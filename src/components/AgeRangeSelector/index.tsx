import React, { useMemo } from 'react';
import { Button, Popover, Radio, Space } from 'antd';

interface AgeRangeSelectorProps {
  value: string;
  childSize?: string | null;
  onChange: (value: string) => void;
  onChildSizeChange?: (value: string | null) => void;
  showTopupIfNeed?: () => void;
}

// 年龄区段列表
export const AGE_RANGE_LIST = ['adult', 'big-child', 'medium-child', 'small-child'];

// 年龄区段选项
export const SELECT_AGE_RANGE_OPTIONS = [
  {
    label: <span>成人</span>,
    title: '成人',
    options: [
      { label: '成人', value: 'adult' },
    ]
  },
  {
    label: <span>儿童</span>,
    title: '儿童',
    options: [
      { label: '大童(140cm～160cm)', value: 'big-child' },
      { label: '中童(120cm～140cm)', value: 'medium-child' },
      { label: '小童(100cm～120cm)', value: 'small-child' },
    ]
  }
] as const;

// 儿童身高选项
const getChildSizeOptions = () => {
  return {
    'big-child': { label: '大童', range: '140cm～160cm', disabled: false },
    'medium-child': { label: '中童', range: '120cm～140cm', disabled: false },
    'small-child': { label: '小童', range: '100cm～120cm', disabled: false },
  } as const;
};



type ChildSize = 'big-child' | 'medium-child' | 'small-child';

const ChildSizeSelector: React.FC<{
  value: ChildSize | null;
  onChange: (value: ChildSize | null) => void;
  showTopupIfNeed?: () => void;
}> = ({ value, onChange, showTopupIfNeed }) => {
  const childSizeOptions = useMemo(() => getChildSizeOptions(), []);

  const handleChildSizeChange = (newValue: ChildSize | null) => {
    onChange(newValue);
    showTopupIfNeed?.();
  };

  const getChildSizeDisplay = (size: ChildSize) => {
    const option = childSizeOptions[size];
    return `${option.label}（${option.range}）`;
  };

  return (
    <Popover
      content={
        <div style={{ padding: '8px 0' }}>
          <Radio.Group
            buttonStyle="solid"
            onChange={(e) => handleChildSizeChange(e.target.value as ChildSize)}
            value={value}
          >
            {Object.entries(childSizeOptions).map(([key, { label, range, disabled }]) => (
              <Radio.Button key={key} value={key} disabled={disabled}>
                {label}（{range}）
              </Radio.Button>
            ))}
          </Radio.Group>
        </div>
      }
      trigger="click"
      placement="bottom"
      open={value === null}
    >
      <Button type="link" style={{ padding: '0 8px' }}>
        {value ? (
          <span>
            {getChildSizeDisplay(value)}
            <Button
              type="link"
              onClick={(e) => {
                e.stopPropagation();
                handleChildSizeChange(null);
              }}
              style={{ padding: '0 4px', marginLeft: 8 }}
            >
              重新选择
            </Button>
          </span>
        ) : (
          '请选择儿童身高'
        )}
      </Button>
    </Popover>
  );
};

const AgeRangeSelector: React.FC<AgeRangeSelectorProps> = ({
  value,
  childSize,
  onChange,
  onChildSizeChange,
  showTopupIfNeed,
}) => {
  const handleAgeRangeChange = (e: any) => {
    const newValue = e.target.value;
    onChange(newValue);
    if (newValue === 'adult' && onChildSizeChange) {
      onChildSizeChange(null);
    }
    showTopupIfNeed?.();
  };

  return (
    <Space>
      <Radio.Group
        buttonStyle="solid"
        onChange={handleAgeRangeChange}
        value={value}
      >
        <Radio.Button style={{ width: 96, borderRadius: 32, textAlign: 'center' }} value="adult">成人</Radio.Button>
        <Radio.Button style={{ width: 96, borderRadius: 32, textAlign: 'center' }} value="child">儿童</Radio.Button>
      </Radio.Group>

      {value === 'child' && (
        <ChildSizeSelector
          value={childSize as ChildSize | null}
          onChange={(newValue) => onChildSizeChange?.(newValue)}
          showTopupIfNeed={showTopupIfNeed}
        />
      )}
    </Space>
  );
};

export default AgeRangeSelector; 