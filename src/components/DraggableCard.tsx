import React, { useEffect, useState } from 'react';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';
import { Col, Row } from 'antd';

export interface DraggableData {
  id: number;
  content: JSX.Element;
}

const DraggableCardList = ({ data, ShowCard, onChange }) => {
    const [cards, setCards] = useState<Array<DraggableData>>([]);

    useEffect(() => {
      setCards(data);
    }, [data]);

    const onDragEnd = (result) => {
      if (!result.destination) {
        return;
      }

      const reorderedCards = Array.from(cards);
      const [removed] = reorderedCards.splice(result.source.index, 1);
      reorderedCards.splice(result.destination.index, 0, removed);

      setCards(reorderedCards);

      const data = reorderedCards.map((card, index) => {
        return { id: card.id, order: index + 1 };
      });
      onChange(data);
    };


    return (
      <DragDropContext onDragEnd={onDragEnd}>
        <Droppable droppableId="droppable" direction={'horizontal'}>
          {(provided) => (
            <Row gutter={[16, 24]}
                 {...provided.droppableProps}
                 ref={provided.innerRef}
            >
              {cards && cards.map((card, index) => (
                <Draggable key={card.id} draggableId={String(card.id)} index={index}>
                  {(provided) => (
                    <Col span={4.5}
                         ref={provided.innerRef}
                         {...provided.draggableProps}
                         {...provided.dragHandleProps}
                    >
                      <ShowCard {...card} />
                    </Col>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </Row>
          )}
        </Droppable>
      </DragDropContext>
    );
  }
;

export default DraggableCardList;