import { Flex } from 'antd';
import IconFont from '@/components/IconFont';
import {
  repairDetailGuideImages_1 as guideImages,
  repairDetailGuideImages_2 as guideImages2,
} from '@/assets/images/oss/StaticImage';
import React from 'react';

const ExampleImgBlock = ({ targetImg, rightImg, desc1, desc2, desc3 }) => {
  return (
    <Flex
      vertical
      align={'center'}
      className={'font-pf'}
      style={{
        borderRadius: 8,
        padding: '8px 8px',
        backgroundColor: '#F5F6F9',
      }}
    >
      <DoubleImageBlock
        image_1={targetImg}
        image_2={rightImg}
        desc1={desc1}
        desc2={desc2}
        desc3={desc3}
        right={true}
      />
    </Flex>
  );
};
const ImageBlock = ({
                      img,
                      label,
                      right,
                      desc1 = '',
                      desc2 = '',
                      radius = false,
                    }) => {
  return (
    <Flex vertical align={'center'} gap={4} className={'font-pf'}>
      <div
        style={{
          display: 'flex',
          width: 'auto',
          height: 'auto',
          position: 'relative',
        }}
      >
        <img
          src={img}
          width={150}
          height={200}
          alt={'示例图片'}
          style={{ borderRadius: radius ? 8 : 0 }}
        />
        <span
          className={'text12 normal color-w font-pf'}
          style={{
            position: 'absolute',
            top: 8,
            left: 8,
            lineHeight: '18px',
            borderRadius: 4,
            padding: '2px 2px',
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
          }}
        >
            {label}
          </span>
        <IconFont
          type={right ? 'icon-gou' : 'icon-cha'}
          style={{
            position: 'absolute',
            top: 8,
            right: 8,
            fontSize: '32px',
            backgroundColor: '#fff',
            borderRadius: 32,
          }}
        />

        {desc2 && (
          <span className={'text12 color-w repair-detail-image-sticker'}>
              {desc2}
            </span>
        )}
      </div>
      {desc1 && (
        <span className={'text14 normal color-72'} style={{ height: 20 }}>
            {desc1}
          </span>
      )}
    </Flex>
  );
};
const DoubleImageBlock = ({
                            image_1,
                            image_2,
                            desc1,
                            desc2,
                            desc3,
                            right,
                          }) => {
  return (
    <Flex vertical align={'center'} gap={4} className={'font-pf'}>
      <Flex style={{ borderRadius: 8, overflow: 'hidden' }}>
        <ImageBlock
          img={image_1}
          label={'待修复图'}
          right={right}
          desc2={desc2}
        />
        <ImageBlock
          img={image_2}
          label={'正确图片'}
          right={right}
          desc2={desc3}
        />
      </Flex>
      <span className={'text14 normal color-72'} style={{ height: 20 }}>
          {desc1}
        </span>
    </Flex>
  );
};
const RepairDetailExampleContent = () => {
  return (
    <Flex style={{ width: 648, height: 'auto', padding: 4 }} wrap gap={8}>
      <div
        className={'text16 weight'}
        style={{ lineHeight: '22px', width: '100%' }}
      >
        分别涂抹待修复图与正确图片中需要修复的区域
      </div>
      <div
        className={'text14 normal color-72'}
        style={{ lineHeight: '20px', width: '100%' }}
      >
        待修复图涂抹范围需贴合图形，正确图片涂抹范围需额外扩大
      </div>
      <ExampleImgBlock
        targetImg={guideImages2[3]}
        rightImg={guideImages2[4]}
        desc1={'部分区域不正确'}
        desc2={'涂抹范围贴合实际大小'}
        desc3={'涂抹范围大于实际大小'}
      />
      <ExampleImgBlock
        targetImg={guideImages2[0]}
        rightImg={guideImages2[1]}
        desc1={'logo&印花还原有误'}
        desc2={'涂抹范围贴合实际大小'}
        desc3={'涂抹范围大于实际大小'}
      />
      <div
        className={'text16 normal color-72'}
        style={{ lineHeight: '22px', width: '500px' }}
      >
        错误示例
      </div>
      <Flex
        style={{ padding: 8, borderRadius: 8, backgroundColor: '#F5F6F9' }}
        align={'center'}
        gap={12}
        justify={'space-between'}
      >
        <ImageBlock
          img={guideImages[8]}
          label={'待修复图'}
          right={false}
          desc1={'一次性抹多个区域'}
          radius
        />
        <ImageBlock
          img={guideImages[9]}
          label={'待修复图'}
          right={false}
          desc1={'涂抹范围过大'}
          radius
        />
        <DoubleImageBlock
          image_1={guideImages[10]}
          image_2={guideImages[11]}
          desc1={'涂抹区域不一致'}
          desc2=""
          desc3=""
          right={false}
        />
      </Flex>
    </Flex>
  );
};

export default RepairDetailExampleContent;