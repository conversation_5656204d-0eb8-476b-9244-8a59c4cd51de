.title {
  margin: 0 auto;
  font-weight: 200;
}

.repair-detail-guide-fix {
  display: flex;
  width: 100%;
  height: calc(100% - 72px);
  align-items: center;
  justify-content: center;
}

.repair-detail-guide-block {
  //margin: auto;
  font-family: "PingFang SC", serif;
  font-weight: 400;
}

.repair-detail-guide-title {
  text-align: center;
  align-content: center;
}

.repair-detail-guide-content {
  display: flex;
  flex-wrap: wrap;
  width: 530px;
  height: auto;
  gap: 8px;
  padding: 16px;
  border-radius: 6px;
  background-color: #fff;
  .imag-block {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    gap: 4px;
    position: relative;
  }
  .imag-container {
    display: flex;
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    img {
      height: 200px;
      width: 150px;
    }
  }
  .imag-sticker {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 26px;
    text-align: center;
    align-content: center;
    backdrop-filter: blur(10px);
    background: rgba(26, 27, 29, 0.4);
  }

  .tag-container {
    display: flex;
    width: 100%;
    gap: 8px;
    align-items: center;
    .tag-num {
      height: 22px;
      line-height: 18px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 4px;
      padding: 2px 4px;
    }
  }
}

// BasicReplaceGuideBlock styles
.sample-cases {
  width: 100%;
  padding: 16px;
  
  .sample-title {
    text-align: center;
  }
  
  .sample-subtitle {
    text-align: center;
  }
  
  .showcase-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    margin-top: 16px;
  }
  
  .sample-image-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    
    .sample-image-wrapper {
      position: relative;
      width: 150px;
      height: 200px;
      border-radius: 8px;
      overflow: hidden;
      
      .sample-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    
    .sample-caption {
      text-align: center;
    }
  }
  
  .model-indicator {
    position: absolute;
    bottom: 8px;
    left: 8px;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 4px;
    
    .model-indicator-avatar {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background: #fff;
    }
    
    .model-indicator-text {
      color: #fff;
    }
  }
  
  .arrow-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    
    img {
      width: 100%;
      height: auto;
    }
  }
}
