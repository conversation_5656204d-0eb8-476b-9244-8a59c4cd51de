import React from 'react';
import { Flex } from 'antd';
import '@/components/Guide/Guide.less'
import GRADIENT_ARROW_TIP from '@/assets/icon/gradient_arrow_tip.png'
import { repairDetailGuideImages_2 as guideImages } from '@/assets/images/oss/StaticImage';

const RepairDetailGuideBlock = () => {
  const ImageBlock = ({image, text1, text2 = ''}) => {
    return (
      <div className={"imag-block"}>
        <div className={"imag-container"}>
          <img src={image} alt={"用户引导"} />
          {text2 && <span className={"text12 color-w imag-sticker"}>{text2}</span>}
        </div>
        <span className={'text14 color-72'}>{text1}</span>
      </div>
    )
  }
  const Tag = ({text, num}) => {
    return (
      <div className={'tag-container'}>
        <span className={"tag-num text12 color-w"}>问题{num}</span>
        <span className={"text14 color-1a"} >{text}</span>
      </div>
    );
  }

  return (
    <div className={"repair-detail-guide-fix"}>
      <Flex vertical className={"repair-detail-guide-block"} gap={8}>
        <Flex vertical className={"repair-detail-guide-title"} gap={8}>
          <span className={"text16 weight color-1a"}>涂抹错误区域，生成正确图案</span>
          <span className={"text14 color-72"}>适用于logo、纽扣、腰带等错误细节替换</span>
        </Flex>
        <Flex className={"repair-detail-guide-content"}>
          <Tag text={"部分区域不正确"} num={1} />
          <ImageBlock image={guideImages[0]} text1={"涂抹待修复图"} text2={"涂抹范围贴合实际大小"} />
          <ImageBlock image={guideImages[1]} text1={"涂抹正确图"} text2={"涂抹范围大于实际大小"} />
          <img src={GRADIENT_ARROW_TIP} alt="arrow_tip" style={{ width: 24, height: 24, margin: 'auto 0 auto' }} />
          <ImageBlock image={guideImages[2]} text1={"修复完成"} />
          <Tag text={"logo&印花还原有误"} num={2} />
          <ImageBlock image={guideImages[3]} text1={"涂抹待修复图"} text2={"涂抹范围贴合实际大小"} />
          <ImageBlock image={guideImages[4]} text1={"涂抹正确图"} text2={"涂抹范围大于实际大小"} />
          <img src={GRADIENT_ARROW_TIP} alt="arrow_tip" style={{ width: 24, height: 24, margin: 'auto 0 auto' }} />
          <ImageBlock image={guideImages[5]} text1={"修复完成"} />
        </Flex>
      </Flex>
    </div>
  )
}
export default RepairDetailGuideBlock;