import React from 'react';
import { Flex } from 'antd';
import '@/components/Guide/Guide.less'
import GRADIENT_ARROW_TIP from '@/assets/icon/gradient_arrow_tip.png'
import {StaticImage } from '@/assets/images/oss/StaticImage';

const guideImages = StaticImage.clothingSwap.guideImages;

export const ImageBlock = ({image, text1, text2 = ''}) => {
  return (
    <div className={"imag-block"}>
      <div className={"imag-container"}>
        <img src={image} alt={"用户引导"} />
        {text2 && <span className={"text12 color-w imag-sticker"}>{text2}</span>}
      </div>
      <span className={'text14 color-72'}>{text1}</span>
    </div>
  )
}
const Tag = ({text = '', num}) => {
  return (
    <div className={'tag-container'}>
      <span className={"tag-num text12 color-w"}>示例{num}</span>
      {text && <span className={"text14 color-1a"} >{text}</span>}
    </div>
  );
}
const ClothingSwapGuideBlock = () => {


  return (
    <div className={"repair-detail-guide-fix"}>
      <Flex vertical className={"repair-detail-guide-block"} gap={8}>
        <Flex vertical className={"repair-detail-guide-title"} gap={8}>
          <span className={"text16 weight color-1a"}>只需一张服装图，生成模特上身商品图</span>
          <span className={"text14 color-72"}>建议只换上装或者只换下装</span>
        </Flex>
        <Flex className={"repair-detail-guide-content"}>
          <Tag num={1} />
          <ImageBlock image={guideImages[0]} text1={"上传参考图"} />
          <ImageBlock image={guideImages[1]} text1={"上传服装图"} />
          <img src={GRADIENT_ARROW_TIP} alt="arrow_tip" style={{ width: 24, height: 24, margin: 'auto 0 auto' }} />
          <ImageBlock image={guideImages[2]} text1={"生成效果"} />
          <Tag num={2} />
          <ImageBlock image={guideImages[3]} text1={"上传参考图"} />
          <ImageBlock image={guideImages[4]} text1={"上传服装图"} />
          <img src={GRADIENT_ARROW_TIP} alt="arrow_tip" style={{ width: 24, height: 24, margin: 'auto 0 auto' }} />
          <ImageBlock image={guideImages[5]} text1={"生成效果"} />
        </Flex>
      </Flex>
    </div>
  )
}
export default ClothingSwapGuideBlock;