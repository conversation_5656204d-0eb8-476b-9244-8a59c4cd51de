// renderSampleCases 组件样式
.sample-cases {
  padding: 12px;
  height: 80%;
  justify-content: center;
  align-items: center;
  
  .sample-title {
    line-height: 22px;
  }
  
  .sample-subtitle {
    line-height: 22px;
  }
  
  .showcase-container {

    margin-top: 0;
    width: 530px;
    height: 256px;
    background-color: #fff;
    border-radius: 6px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
  
  .arrow-container {
    position: relative;
    width: 24px;
    height: 24px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .sample-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  
  .sample-image-container {
    position: relative;
    width: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    .sample-image-wrapper {
      position: relative;
      width: 150px;
      height: 200px;
      border-radius: 8px;
      overflow: hidden;
      
      .sample-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    
    .sample-caption {
      text-align: center;
      margin-top: 8px;
    }
  }
  
  .model-indicator {
    position: absolute;
    width: 48px;
    height: 68px;
    left: 4px;
    bottom: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 4px;
    background-color: #F5F6F9;
    
    .model-indicator-avatar {
      margin-top: 2px;
      width: 43px;
      height: 43px;
      border-radius: 2px;
      background-color: #D8D8D8;
    }
    
    .model-indicator-text {
      margin-top: 2px;
    }
  }
}