import React from 'react';
import { Flex, Image } from 'antd';
import './index.less';
import { StaticImage } from '@/assets/images/oss/StaticImage';

const BasicReplaceGuideBlock: React.FC = () => {
  // 示例图片子组件
  const SampleImage = ({ imageUrl, caption, children = null }: {
    imageUrl: string;
    caption: string;
    children?: React.ReactNode
  }) => (
    <div className="sample-image-container">
      <div className="sample-image-wrapper">
        <Image
          src={imageUrl}
          preview={false}
          className="sample-image"
        />
        {children}
      </div>
      <div className="text14 color-96">
        {caption}
      </div>
    </div>
  );

  // 模特小图标子组件
  const ModelIndicator = () => (
    <div className="model-indicator">
      <div className="model-indicator-avatar" />
      <div className="text12 color-96 ">模特</div>
    </div>
  );

  return (
    <Flex
      className="sample-cases"
      vertical
      gap={12}
    >
      {/* 标题区域 */}
      <div className="text16 weight sample-title">
        仅需1张服装图，生成模特商拍图
      </div>
      <div className="text14 color-96 sample-subtitle">
        适用于T恤、卫衣等简单版型服装
      </div>

      {/* 示例展示区域 */}
      <div className="showcase-container">
        {/* 第一步：上传服装图 */}
        <SampleImage
          imageUrl={StaticImage.basicReplace.sampleCase[0]}
          caption="上传服装图"
        />

        {/* 第二步：选择参考图和模特 */}
        <SampleImage
          imageUrl={StaticImage.basicReplace.sampleCase[1]}
          caption="选择参考图和模特"
        >
          <ModelIndicator />
        </SampleImage>

        {/* 箭头 */}
        <div className="arrow-container">
          <Image
            src={StaticImage.basicReplace.sampleCase[2]}
            preview={false}
            className="sample-image"
          />
        </div>

        {/* 第三步：生成商拍图 */}
        <SampleImage
          imageUrl={StaticImage.basicReplace.sampleCase[3]}
          caption="生成商拍图"
        />
      </div>
    </Flex>
  );
};

export default BasicReplaceGuideBlock; 