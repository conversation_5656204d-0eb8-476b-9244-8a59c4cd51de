import { Flex } from 'antd';
import { StaticImage } from '@/assets/images/oss/StaticImage';
import React from 'react';
import GRADIENT_ARROW_TIP from '@/assets/icon/gradient_arrow_tip.png';
import { ImageBlock } from '@/components/Guide/ClothingSwapGuideBlock';
import '@/components/Guide/Guide.less'

const exampleImages = StaticImage.clothingSwap.exampleImages



const ClothingSwapExampleContent = () => {
  return (
    <Flex vertical style={{ width: 'auto', height: 'auto', padding: 4 }} wrap gap={8}>
      <span
        className={'text16 weight'}
        style={{ lineHeight: '22px' }}
      >
        分别涂抹待参考图与服装图片中需要替换的区域
      </span>
      <span
        className={'text14 normal color-72'}
        style={{ lineHeight: '20px' }}
      >
        参考图与服装图涂抹区域需要额外扩大
      </span>
      <Flex className={"repair-detail-guide-content"}
            style={{backgroundColor: '#F5F6F9', padding: 8, width: 'auto'}}
      >
        <ImageBlock image={exampleImages[0]} text1={"参考图"} text2={"涂抹范围需要额外扩大"} />
        <ImageBlock image={exampleImages[1]} text1={"需替换服装"} text2={"涂抹范围需要额外扩大"} />
        <img src={GRADIENT_ARROW_TIP} alt="arrow_tip" style={{ width: 24, height: 24, margin: 'auto 0 auto' }} />
        <ImageBlock image={exampleImages[2]} text1={"生成完成效果"} />
      </Flex>
    </Flex>
  );
};

export default ClothingSwapExampleContent;