import React, { useState, useEffect, forwardRef, useRef } from 'react';
import { Input, Checkbox, CheckboxProps, Button, Modal, notification, message } from 'antd';

const { TextArea } = Input;

import { Tag } from 'antd';
import { createTag, queryTagsList, TagVO } from '@/services/TagsController';

const { CheckableTag } = Tag;

interface InputProps {
  value?: string;
  onChange?: (value: string) => void;
  tagsType: string;
  placeholder?: string;
  rows?: number;
  onlyTagName?: boolean;
}

interface TagItem extends TagVO {
  checked: boolean;
}

const InputWithTags = forwardRef((props: InputProps, ref) => {
  const { value, onChange, tagsType, placeholder, rows, onlyTagName } = props;

  const [tagMap, setTagMap] = useState<Map<string, TagItem>>(new Map());

  // create new tag
  const [inputVisible, setInputVisible] = useState<boolean>(false);
  const [inputTagName, setInputTagName] = useState('');
  const [inputTagValue, setInputTagValue] = useState('');

  function fetchTags(){

    //load tags
    queryTagsList({ type: tagsType }).then(res => {
      if (res) {
        let tagsInValue = value ? parseTokens(value)
          .map(tag => tag.trim())
          .filter(tag => tag) : [];

        let m = new Map<string, TagItem>();
        res.forEach(t => {
          if (t.defChecked) {
            tagsInValue.push(t.detail);
          }
          m.set(t.detail, {...t, checked: t.defChecked || tagsInValue.includes(t.detail)});
        })

        setTagMap(m);
      }
    })

  }

  useEffect(() => {

    fetchTags();

  }, []);

  function parseTokens(input: string): string[] {
    const tokens: string[] = [];
    let currentToken = '';
    let inParentheses = false;
    let i = 0;

    while (i < input.length) {
      const char = input[i];

      if (char === '(') {
        if (inParentheses) {
          // 如果已经在括号内部，将 '(' 纳入当前 token
          currentToken += char;
        } else {
          // 如果遇到一个新的 '(', 开始一个新的 token
          if (currentToken.trim() !== '') {
            tokens.push(currentToken.trim());
          }
          currentToken = char;
          inParentheses = true;
        }
      } else if (char === ')') {
        currentToken += char;
        if (inParentheses) {
          // 如果在括号内，结束这个 token
          tokens.push(currentToken.trim());
          currentToken = '';
          inParentheses = false;
        }
      } else if (char === ',' || char === '.') {
        if (inParentheses) {
          // 如果在括号内，继续添加字符到当前 token
          currentToken += char;
        } else {
          // 在括号外，使用 ',' 和 '.' 结束当前 token
          if (currentToken.trim() !== '') {
            tokens.push(currentToken.trim());
          }
          currentToken = '';
        }
      } else {
        // 其他字符简单地加到当前 token
        currentToken += char;
      }
      i++;
    }

    // 捕获最后一个 token
    if (currentToken.trim() !== '') {
      tokens.push(currentToken.trim());
    }

    return tokens;
  }

  function escapeRegExp(text: string): string {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // 转义特殊字符
  }

  function onClickTag(checked: boolean, tagValue: string) {

    console.log('onClickTag:', checked, tagValue);

    let tagItem = tagMap.get(tagValue);
    if (tagItem) {
      tagItem.checked = checked;
      setTagMap(new Map(tagMap));
    }

    let newValue: string = value || '';

    //选中，追加到最后面
    if (checked && !value?.includes(tagValue)) {
      if (newValue && !newValue.trim().endsWith(',')) {
        newValue += ',';
      }
      newValue += tagValue;

      //取消选中，去除对应标签词
    } else if (!checked && value?.includes(tagValue)) {
      // 创建正则表达式：匹配 tagValue 后跟逗号、句号或若干空格
      const regex = new RegExp(escapeRegExp(tagValue) + '[,.\\s]*', 'g');
      newValue = newValue.replaceAll(regex, '');
      if (newValue.endsWith(',')) {
        newValue = newValue.replace(/[,.]+$/, '');
      }
    }

    // 调用外部的 onChange 函数更新 input 值
    onChange?.(newValue);
  }

  function onInput(e: React.ChangeEvent<HTMLTextAreaElement>) {
      const newValue = e.target.value;
      onChange?.(newValue);

    let tagsInValue = newValue ? parseTokens(newValue)
      .map(tag => tag.trim())
      .filter(tag => tag) : [];

    if (tagMap) {
      tagMap.forEach((tagItem, tagValue) => {
        tagItem.checked = tagsInValue && tagsInValue.includes(tagValue);
      })

      setTagMap(new Map(tagMap));
    }
  }

  //新建标签
  const handleNewTag = () => {
    if (inputTagName && inputTagValue) {
      createTag({
        type: tagsType,
        name: inputTagName,
        detail: inputTagValue

      }).then(id => {
        if (id){
          fetchTags();
        }

        setInputVisible(false);
      })
    } else {
      message.error('标签和提示词都不能为空');
    }
  };

  const showNewTag = () => {
    setInputVisible(true);
    setInputTagName('');
    setInputTagValue('');
  };

  return (
    <div style={{ display: 'flex', gap: '10px' }}>
      <TextArea
        rows={rows || 2}
        style={{ width: '600px' }}
        value={value}
        onChange={onInput}
        placeholder={placeholder}
      />
      <div
        style={{
          display: 'flex',
          alignItems: 'start',
          justifyContent: 'flex-start',
          flexWrap: 'wrap',
          flex: '1 0 10%',
        }}
      >
        {
          Array.from(tagMap.entries()).map(([tagValue, tagItem]) => (
            <CheckableTag key={tagValue} checked={tagItem.checked}
                          style={{ border: '1px solid #d9d9d9' }}
                          onChange={(checked) => onClickTag(checked, tagValue)}>
              {tagItem.name}
            </CheckableTag>
          ))
        }

        {/*新建标签：引导提示*/}
        {!inputVisible && (
          <Tag onClick={showNewTag} style={{ background: '#fff', borderStyle: 'dashed', cursor: 'pointer' }}>
            + 新增标签
          </Tag>
        )}

        {/*新建标签：进行新建*/}
        {inputVisible && (

          <Modal
            title="新增标签"
            open={inputVisible}
            onOk={handleNewTag}
            centered={true}
            closable={false}
            keyboard={false}
            maskClosable={false}
            onCancel={() => setInputVisible(false)}
            footer={[
              <Button key="back" onClick={() => setInputVisible(false)}>
                取消
              </Button>,
              <Button key="submit" type="primary" onClick={handleNewTag}>
                确定
              </Button>
            ]}
          >
            <div style={{ display: 'flex', flexDirection: 'column', paddingLeft: 10 }}>
              <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'start'}}>
                <div style={{marginBottom: 10}}>标签名:</div>
                <Input
                  type="text"
                  value={inputTagName}
                  onChange={e => {
                    setInputTagName(e.target.value);

                    if (onlyTagName) {
                      setInputTagValue(e.target.value);
                    }
                  }}
                />
              </div>

              {!onlyTagName && (
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'start', marginTop: 20 }}>
                  <div style={{ marginBottom: 10 }}>提示词:</div>
                  <TextArea
                    value={inputTagValue}
                    rows={4}
                    onChange={e => setInputTagValue(e.target.value)}
                  />
                </div>
              )}

            </div>

          </Modal>
        )}

      </div>
    </div>
  );
});

export default InputWithTags;