import React from 'react';
import { NEW_BACKGROUND, NEW_LABEL } from '@/constants';
import './index.less';

interface NewLabelProps {
  width?: number;
  height?: number;
  top?: number;
  left?: number;
}

const NewLabel: React.FC<NewLabelProps> = ({
                                             width,
                                             height,
                                             top,
                                             left,
                                           }) => {
  return (
    <div className="new-label-wrapper">
      <div className={'new-label-container'}
           style={{ width: `${width}px`, height: `${height}px`, top: `${top}px`, left: `${left}px` }}>
        <img loading={'lazy'} src={NEW_BACKGROUND} className="second-image" alt={'img'} />
        <img loading={'lazy'} src={NEW_LABEL} className="third-image" alt={'img'} />
      </div>
    </div>
  );
};

export default NewLabel;