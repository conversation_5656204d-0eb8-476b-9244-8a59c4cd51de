.new-label-wrapper {
  position: absolute;
  top: 8px;
  left: 10px;
  // 确保标签位于其他元素的上方
  z-index: 999;

  .new-label-container {
    position: relative;
    border-radius: 14px;
    opacity: 1;
    background: rgba(255, 255, 255, 0.4);

    display: flex;
    justify-content: center;
    align-items: center;
    width: 70px;
    height: 30px;

    .first-image {
      position: absolute;
      max-width: 100%;
      max-height: 100%;
    }

    .second-image {
      position: absolute;
      max-width: 80%;
      max-height: 80%;
    }
    .third-image {
      position: absolute;
      max-width: 70%;
      max-height: 70%;
    }


  }
}






