import React, { useState, useEffect } from 'react';
import { Modal, Form, Select, Input, Button, Table, message, Pagination, Radio, QRCode, Tooltip, Flex } from 'antd';
import { EyeOutlined } from '@ant-design/icons';
import { UserVO } from '@/services/UserController';
import { createRegisterPromotionCode, queryCodeByPage } from '@/services/CodeController';
import logoImage from '@/assets/icon/logo.png';
import QRCodeGenerator from 'qrcode';

// 推广注册码创建表单组件
export const CreatePromotionCodeModal: React.FC<{
    visible: boolean;
    onCancel: () => void;
    salesList: UserVO[];
    onSuccess?: () => void;
}> = ({ visible, onCancel, salesList, onSuccess }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [promotionType, setPromotionType] = useState<'external' | 'sales'>('external');

    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            setLoading(true);

            const codeData = {
                relatedUserId: values.salesId || null,
                externalNick: values.externalNick || null
            };

            const result = await createRegisterPromotionCode(codeData);
            if (result) {
                form.resetFields();
                if (onSuccess) onSuccess();
            } else {
                message.error('创建失败');
            }
        } catch (error) {
            console.error('表单验证失败:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleTypeChange = (e: any) => {
        setPromotionType(e.target.value);
        form.resetFields(['salesId', 'externalNick']);
    };

    return (
        <Modal
            title="创建邀请注册码"
            open={visible}
            onCancel={onCancel}
            centered={true}
            footer={[
                <Button key="cancel" onClick={onCancel}>
                    取消
                </Button>,
                <Button key="submit" type="primary" loading={loading} onClick={handleSubmit}>
                    创建
                </Button>,
            ]}
        >
            <Form form={form} layout="vertical">
                <span style={{color: '#727375'}}>说明：创建的注册邀请码，将产生绑定的专用注册链接，客户可通过PC打开链接或手机扫码，完成注册成功后，在下方的客户列表将显示该客户的‘邀请人’为此处的推广人昵称。</span>
                <Form.Item
                    name="promotionType"
                    label="关联推广人"
                    initialValue="external"
                    rules={[{ required: true }]}
                    hidden={true}
                >
                    <Radio.Group onChange={handleTypeChange} value={promotionType}>
                        <Radio.Button value="external">外部伙伴</Radio.Button>
                        <Radio.Button value="sales">内部销售</Radio.Button>
                    </Radio.Group>
                </Form.Item>

                {promotionType === 'sales' && (
                    <Form.Item
                        name="salesId"
                        label="选择销售人员"
                        rules={[{ required: true, message: '请选择销售人员' }]}
                    >
                        <Select
                            placeholder="请选择销售人员"
                            allowClear
                            options={salesList.map(sales => ({
                                value: sales.id,
                                label: sales.nickName,
                            }))}
                        />
                    </Form.Item>
                )}

                {promotionType === 'external' && (
                    <Form.Item
                        name="externalNick"
                        label="推广人昵称"
                        rules={[{ required: true, message: '请输入推广人昵称' }]}
                    >
                        <Input placeholder="推广人昵称" />
                    </Form.Item>
                )}
            </Form>
        </Modal>
    );
};

// 推广注册码列表组件
export const PromotionCodeListModal: React.FC<{
    visible: boolean;
    onCancel: () => void;
    salesList: UserVO[]; // 添加销售列表属性
    onSuccess?: () => void; // 添加成功回调
}> = ({ visible, onCancel, salesList, onSuccess }) => {
    const [data, setData] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [totalCount, setTotalCount] = useState(0);
    const [showCreateModal, setShowCreateModal] = useState(false); // 添加创建弹窗状态

    const fetchData = async () => {
        setLoading(true);
        try {
            const result = await queryCodeByPage({
                pageNum: currentPage,
                pageSize: pageSize,
                codeType: 'registerPromotion',
                orderBy: 'id desc'
            });

            if (result) {
                setData(result.list || []);
                setTotalCount(result.totalCount || 0);
            }
        } catch (error) {
            console.error('获取推广注册码列表失败:', error);
            message.error('获取列表失败');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (visible) {
            fetchData();
        }
    }, [visible, currentPage, pageSize]);

    const handlePageChange = (page: number, pageSize?: number) => {
        setCurrentPage(page);
        if (pageSize) {
            setPageSize(pageSize);
        }
    };

    function doDownload(url: string, fileName: string) {
        const a = document.createElement('a');
        a.download = fileName;
        a.href = url;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }

    // 使用QRCode.js生成大尺寸二维码并下载
    const generateAndDownloadQRCode = (text: string, fileName: string, size = 400) => {
        try {
            // 创建一个临时的canvas元素
            const canvas = document.createElement('canvas');

            // 使用QRCode.js生成二维码
            QRCodeGenerator.toCanvas(canvas, text, {
                width: size,
                margin: 4,
                color: {
                    dark: '#000000',
                    light: '#ffffff'
                },
                errorCorrectionLevel: 'H' // 高容错率，更容易被扫描
            }, (error) => {
                if (error) {
                    console.error('生成二维码失败:', error);
                    message.error('生成二维码失败');
                    return;
                }

                // 如果需要添加logo
                const ctx = canvas.getContext('2d');
                if (ctx) {
                    const img = new Image();
                    img.onload = () => {
                        // 计算logo的位置和大小
                        const logoSize = size * 0.15; // logo大小为二维码的15%
                        const logoX = (size - logoSize) / 2;
                        const logoY = (size - logoSize) / 2;

                        // 绘制白色背景
                        ctx.fillStyle = '#ffffff';
                        ctx.fillRect(logoX - 5, logoY - 5, logoSize + 10, logoSize + 10);

                        // 绘制logo
                        ctx.drawImage(img, logoX, logoY, logoSize, logoSize);

                        // 下载二维码
                        const dataUrl = canvas.toDataURL('image/png');
                        doDownload(dataUrl, fileName);
                    };
                    img.src = logoImage;
                } else {
                    // 如果不需要添加logo，直接下载
                    const dataUrl = canvas.toDataURL('image/png');
                    doDownload(dataUrl, fileName);
                }
            });
        } catch (error) {
            console.error('下载二维码失败:', error);
            message.error('下载二维码失败');
        }
    };

    const columns = [
        {
            title: 'ID',
            dataIndex: 'id',
            key: 'id',
        },
        {
            title: '推广人',
            key: 'nick',
            render: (_: string, record: any) => (
                <span>
                    {record.codeInfo ? record.codeInfo?.nick : ''}
                </span>
            ),
        },
        {
            title: '码值',
            key: 'code',
            render: (_: string, record: any) => <span>{record.code}</span>,
        },
        {
            title: '二维码（手机扫码后将跳转专用注册页面）',
            key: 'qrcode',
            render: (_: string, record: any) => {
                const url = record?.codeInfo?.url;
                if (!url) return null;

                return (
                    <Tooltip
                        title={
                            <QRCode
                                value={url}
                                size={200}
                                type="canvas"
                                bgColor="#fff"
                                icon={logoImage}
                                iconSize={40}
                            />
                        }
                    >
                        <Button type="link" icon={<EyeOutlined />} />
                    </Tooltip>
                );
            },
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
        },
        {
            title: '状态',
            dataIndex: 'codeStatus',
            key: 'codeStatus',
            render: (text: string) => (
                <span style={{ color: text === 'valid' ? 'green' : 'red' }}>
                    {text === 'valid' ? '有效' : '已失效'}
                </span>
            ),
        },
        {
            title: '操作',
            key: 'action',
            render: (_: string, record: any) => {
                const url = record?.codeInfo?.url;
                if (!url) return null;

                return (
                    <div>
                        <a
                            style={{ marginRight: 16 }}
                            onClick={() => {
                                generateAndDownloadQRCode(url, `${record.code}.png`, 300);
                            }}
                        >
                            <Tooltip title={'下载二维码图片，发给推广人，客户扫码注册，可关联到推广人'}>
                                下载二维码
                            </Tooltip>
                        </a>
                        <a
                            onClick={() => {
                                navigator.clipboard.writeText(url);
                                message.success('已复制到剪贴板');
                            }}
                        >
                            <Tooltip title={url}>
                                复制注册链接
                            </Tooltip>
                        </a>
                    </div>
                );
            },
        }
    ];

    // 创建成功后的回调
    const handleCreateSuccess = () => {
        setShowCreateModal(false);
        fetchData();
    };

    return (
        <Modal
            title="推广注册（找人推广）"
            open={visible}
            centered={true}
            onCancel={onCancel}
            width={1000}
            footer={[
                <Button key="create" type="primary" onClick={() => setShowCreateModal(true)}>
                    创建推广注册码
                </Button>,
                <Button key="close" onClick={onCancel}>
                    关闭
                </Button>,
            ]}
        >
            {/* 创建邀请码弹窗 */}
            {showCreateModal && (
                <CreatePromotionCodeModal
                    visible={showCreateModal}
                    onCancel={() => setShowCreateModal(false)}
                    salesList={salesList}
                    onSuccess={handleCreateSuccess}
                />
            )}

            <Table
                columns={columns}
                dataSource={data}
                rowKey="id"
                pagination={false}
                loading={loading}
            />
            <div style={{ marginTop: 16, textAlign: 'right' }}>
                <Pagination
                    current={currentPage}
                    pageSize={pageSize}
                    total={totalCount}
                    onChange={handlePageChange}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total) => `共 ${total} 条`}
                />
            </div>
        </Modal>
    );
};