import React, { useState, useEffect } from 'react';
import { Flex } from 'antd';
import Lottie from 'lottie-react';

interface ImageLoadingAnimationProps {
  width?: string;
  height?: string;
  image?: string;
  text?: string;
  scanAnimation: any;
  systemMattingStatus?: string | null;
}

// 系统涂抹处理状态
const SYSTEM_MATTING_STATUS = {
  // 等待
  WAITING: 'WAITING',
  // 处理中
  PROCESSING: 'PROCESSING',
  // 完成
  FINISHED: 'FINISHED',
  // 失败
  FAILED: 'FAILED',
};

const ImageLoadingAnimation: React.FC<ImageLoadingAnimationProps> = ({
  width = '100%',
  height = '100%',
  image,
  text = '正在识别服装区域，识别约1分钟左右，请稍等',
  scanAnimation,
  systemMattingStatus = null
}) => {
  const [progress, setProgress] = useState(0);
  
  useEffect(() => {
    let timer: NodeJS.Timeout;
    
    // 如果已有明确状态，直接设置进度
    if (systemMattingStatus === SYSTEM_MATTING_STATUS.FINISHED) {
      setProgress(100);
    } else if (systemMattingStatus === SYSTEM_MATTING_STATUS.FAILED) {
      // TODO: 失败后，显示失败动画
    } else if (systemMattingStatus === SYSTEM_MATTING_STATUS.PROCESSING) {
      // 正常倒计时逻辑，添加波动
      timer = setInterval(() => {
        setProgress(prevProgress => {
          // 如果进度未达到95%，使用波动增量
          if (prevProgress < 95) {
            // 基础增量是100/60，即60秒内完成90%
            const baseIncrement = 100 / 60;
            // 生成-30%到+30%之间的随机波动
            const fluctuation = (Math.random() * 0.6 - 0.3);
            // 实际增量 = 基础增量 * (1 + 波动值)
            const actualIncrement = baseIncrement * (1 + fluctuation);
            return Math.min(prevProgress + actualIncrement, 95);
          } 
          // 达到95%后，如果没有明确状态，就保持在95%
          return prevProgress;
        });
      }, 1000); // 每秒更新一次
    }
    
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [systemMattingStatus, image]);

  // 根据状态确定显示文本
  const displayText = () => {
    if (systemMattingStatus === SYSTEM_MATTING_STATUS.FAILED) return '服装识别失败，请重试';
    return text;
  };

  return (
    <div style={{
      width: width,
      height: height,
      position: 'relative',
      overflow: 'hidden',
      backgroundColor: 'rgba(0, 0, 0,0.6)',
    }}>
      {/* 图片 */}
      {image && (
        <img
          src={image}
          alt="原图"
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain',
            filter: 'brightness(0.5)',
          }}
        />
      )}


      {/* 动画遮罩层 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: systemMattingStatus === SYSTEM_MATTING_STATUS.FAILED ? -1 : 1,
        display: 'flex',
        alignItems: 'stretch',
        justifyContent: 'stretch'
      }}>
        <Lottie
          animationData={scanAnimation}
          loop={true}
          renderer="svg"
          style={{
            width: '100%',
            height: '100%',
            position: 'absolute',
            top: '50%',
            left: 0,
            transform: 'translateY(-50%) scale(1, 1.2)',
            transformOrigin: 'center center'
          }}
        />
      </div>


      {/* 进度条和文字 */}
      <Flex
        vertical
        justify="center"
        align="center"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: systemMattingStatus === SYSTEM_MATTING_STATUS.FAILED ? 0 : 1,
        }}
      >
        <div
          style={{
            width: '160px',
            height: '8px',
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            borderRadius: '8px',
            overflow: 'hidden',
            marginBottom: '16px',
          }}
        >
          <div
            style={{
              width: `${progress}%`,
              height: '100%',
              backgroundColor: systemMattingStatus === SYSTEM_MATTING_STATUS.FAILED ? 'red' : '#fff',
              borderRadius: '3px',
              transition: 'width 0.3s ease',
            }}
          />
        </div>
        <div style={{ color: '#fff', fontSize: '16px', textAlign: 'center' }}>
          {displayText()}
        </div>
      </Flex>

    </div>
  );
};

export default ImageLoadingAnimation;