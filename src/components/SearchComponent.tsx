import { DatePicker, Input, Select } from 'antd';
const { Search } = Input;
const { RangePicker } = DatePicker;

export default (props) => {
  const { type } = props;
  const configs = {
    ...props,
  };
  delete configs.type;
  const typeComponents = {
    select: <Select {...configs} />,
    search: <Search {...configs} />,
    dateRange: <RangePicker format="YYYY-MM-DD" showNow={true} {...configs} />,
  };
  return typeComponents[type] || null;
};
