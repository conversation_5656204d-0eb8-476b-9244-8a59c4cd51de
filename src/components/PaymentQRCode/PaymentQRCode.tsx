import React, { useState, useEffect, useRef } from 'react';
import { message, notification } from 'antd';
import QRCode from 'qrcode.react';
import IconFont from '@/components/IconFont';
import {
  createAlipayPayQRCode,
  createWeChatPayQRCode,
  queryAlipayPayResult,
  queryWeChatPayResult,
} from '@/services/PayController';
import { PricePlan } from '@/services/OrderInfoController';
import './PaymentQRCode.less';

export interface PaymentQRCodeProps {
  payType: 'wx' | 'alipay';
  selectedPlan: PricePlan;
  payMasterId?: number;
  onPaySuccess: (orderNo: string) => void;
  className?: string;
}

const PaymentQRCode: React.FC<PaymentQRCodeProps> = ({
  payType,
  selectedPlan,
  payMasterId,
  onPaySuccess,
  className = ''
}) => {
  const [qrCode, setQrCode] = useState('');
  const [orderId, setOrderId] = useState<string>('');
  const [payStatus, setPayStatus] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // 生成二维码
  const generateQRCode = async () => {
    if (!selectedPlan || isGenerating) return;
    
    setIsGenerating(true);
    try {
      const createQrCode = payType === 'wx' ? createWeChatPayQRCode : createAlipayPayQRCode;
      
      const data = await createQrCode({
        planCode: selectedPlan?.code,
        payAmount: selectedPlan?.amount ? selectedPlan?.amount : null,
        musePoint: selectedPlan?.musePoint || 0,
        creativeImgCountGave: selectedPlan?.creativeImgCountGave || 0,
        payMasterUserId: payMasterId ? payMasterId : null
      });

      if (data && data.codeUrl && data.orderNo) {
        setQrCode(data.codeUrl);
        setOrderId(data.orderNo);
        console.log(`生成${payType === 'wx' ? '微信' : '支付宝'}二维码返回的数据:`, data);
        startPolling(data.orderNo);
      } else {
        message.error(`生成${payType === 'wx' ? '微信' : '支付宝'}二维码失败`);
      }
    } catch (error) {
      console.error(`生成${payType === 'wx' ? '微信' : '支付宝'}二维码失败:`, error);
      message.error(`生成${payType === 'wx' ? '微信' : '支付宝'}二维码失败`);
    } finally {
      setIsGenerating(false);
    }
  };

  // 开始轮询支付结果
  const startPolling = (orderNo: string) => {
    // 检查是否已有定时器，若存在则先清除
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // 设置新的轮询定时器
    intervalRef.current = setInterval(async () => {
      try {
        const queryPayResult = payType === 'wx' ? queryWeChatPayResult : queryAlipayPayResult;
        const result = await queryPayResult(orderNo);
        
        if (result && result.tradeState) {
          setPayStatus(result.tradeState);

          if (result.tradeState === 'SUCCESS') {
            console.log(`${payType === 'wx' ? '微信' : '支付宝'}支付成功，orderNo:`, orderNo);
            clearInterval(intervalRef.current!);
            intervalRef.current = null;
            notification.success({ 
              message: `${payType === 'wx' ? '微信' : '支付宝'}支付成功`, 
              duration: 3 
            });
            onPaySuccess(orderNo);
          } else if (result.tradeState === 'NOTPAY' || result.tradeState === 'USERPAYING') {
            // 支付未完成，继续轮询
          } else {
            clearInterval(intervalRef.current!);
            intervalRef.current = null;
            message.error(`${payType === 'wx' ? '微信' : '支付宝'}支付失败`);
          }
        }
      } catch (error) {
        console.error(`查询${payType === 'wx' ? '微信' : '支付宝'}支付结果失败:`, error);
      }
    }, 2000);
  };

  // 清理定时器
  const cleanup = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  // 组件挂载时生成二维码
  useEffect(() => {
    generateQRCode();
    return cleanup;
  }, [selectedPlan, payType]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return cleanup;
  }, []);

  return (
    <div className={`payment-qr-code ${className}`}>
      <div className="qr-code-container">
        {qrCode ? (
          <div style={{ width: 116, height: 116, position: 'relative' }}>
            <QRCode value={qrCode} size={116} />
            <div
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                width: 18,
                height: 18,
                borderRadius: '50%',
                backgroundColor: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <IconFont 
                type={payType === 'wx' ? "icon-weixinzhifu" : 'icon-alipay-square-'} 
                style={{ fontSize: 18 }} 
              />
            </div>
          </div>
        ) : (
          <div 
            style={{ 
              width: 116, 
              height: 116, 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              backgroundColor: '#f5f5f5',
              borderRadius: 4
            }}
          >
            {isGenerating ? '生成中...' : '加载失败'}
          </div>
        )}
      </div>
      <div className="payment-info">
        <div className={'scan-pay-text'}>
          {payType === 'wx' ? '微信' : '支付宝'}扫码支付 
          <span className={'pay-amount-number'}>{selectedPlan?.amount}</span> 元
        </div>
        <div className="payment-agreement">
          支付即视为您同意
          <a href={'vip_service.html'} target={'_blank'}>《妙思门付费客户服务协议》</a>
        </div>
      </div>
    </div>
  );
};

export default PaymentQRCode;