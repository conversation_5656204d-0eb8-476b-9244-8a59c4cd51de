import { Form, Image, notification, Upload, UploadFile, UploadProps } from 'antd';
import { UPLOAD_PRO_URL, UPLOAD_URL } from '@/constants';
import { uploadButton } from '@/pages/Operate/Face';
import React, { useEffect, useState } from 'react';
import { FileType, getBase64 } from '@/utils/utils';
import VideoPreview from '@/components/VideoPreview';

interface UploadFormItemProps {
  form: any;
  name: string;
  label: string;
  required: boolean;
  labelSpan?: number;
  isProd?: boolean;
  maxCount?: number;
  listType?: 'text' | 'picture' | 'picture-card';
}

const isImgUrl = (url: string) => {
  console.log('isImgUrl',url,/\.(jpeg|jpg|gif|png|webp|svg|bmp)$/.test(url))
  //local uploaded image
  if (url.startsWith('data:image')){
    return true;
  }
  return /\.(jpeg|jpg|gif|png|webp|svg|bmp).*$/.test(url);
};
const UploadFormItem: React.FC<UploadFormItemProps> = ({
                                                         form, name, label, required, labelSpan, isProd, listType, maxCount = 1
                                                       }) => {

  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  useEffect(() => {
    const value = form.getFieldValue(name);
    const array = Array.isArray(value) ? value : [value];
    // @ts-ignore
    if (value ) {
      setFileList(array.map((e, idx) => {
        return {
          uid: '-' + (idx + 1), // 文件唯一标识，负数表示不是用户新上传的
          name: 'image.png', // 文件名
          status: 'done', // 状态有：uploading, done, error, removed
          url: e, // 图片路径
        }
      }));
    } else {
      setFileList([]);
    }
  }, []);

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
  };

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    const file = newFileList[0];

    if (file && file.response && !file.response.success) {
      console.log('event:', file);
      notification.error({ message: '上传文件异常，请重试' });
      setFileList([]);
      return;
    }

    setFileList(newFileList);

    let curr = form.getFieldValue(name);
    curr = curr?.fileList?.filter(e => e.url)?.map(e => e.url) || [];
    form.setFieldValue(name, [...curr, ...newFileList.filter(e => e?.response?.data).map(e => e.response.data)]);
  };

  return <>
    <Form.Item labelCol={{ span: labelSpan }} name={name} label={label} rules={[{ required: required }]}>
      <Upload
        action={isProd ? UPLOAD_PRO_URL : UPLOAD_URL}
        listType={listType ? listType : 'picture-card'}
        fileList={fileList}
        onPreview={handlePreview}
        onChange={handleChange}
        multiple={maxCount > 1}
      >
        {fileList.length >= maxCount ? null : uploadButton}
      </Upload>
    </Form.Item>

    {previewImage && isImgUrl(previewImage) && (
      <Image
        wrapperStyle={{ display: 'none' }}
        preview={{
          visible: previewOpen,
          onVisibleChange: (visible) => setPreviewOpen(visible),
          afterOpenChange: (visible) => !visible && setPreviewImage(''),
        }}
        src={previewImage}
      />
    )}

    {previewImage && !isImgUrl(previewImage) &&
      <VideoPreview src={previewImage} onClose={() => {
        setPreviewOpen(false);
        setPreviewImage('');
      }} />
    }

  </>;
};

export default UploadFormItem;