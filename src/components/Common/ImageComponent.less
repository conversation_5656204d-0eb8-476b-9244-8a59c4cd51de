.image-component-deletable-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  width: fit-content;
}

.image-component-deletable-icon {
  display: none;
  position: absolute;
  right: 8px;
  top: 8px
}

.image-component-deletable-container:hover {
  .image-component-deletable-icon {
    display: flex;
  }
}

.image-compare-modal {
  .modal-header {
    margin-bottom: 12px;
    text-align: center;

    .title {
      font-size: 20px;
      font-weight: 600;
      color: #1a1a1a;
      margin-bottom: 4px;
    }

    .subtitle {
      font-size: 14px;
      color: #666;
    }
  }

  .round-selector {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 8px;
    margin-top: -12px;
    flex-wrap: wrap;

    .round-item {
      width: 28px;
      height: 28px;
      border-radius: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      background: #f5f5f5;
      color: #666;
      font-size: 14px;
      transition: all 0.3s;

      &:hover {
        background: #e6f4ff;
        color: #1677ff;
      }

      &.active {
        background: #1677ff;
        color: #fff;
      }
    }
  }

  .compare-container {
    margin-bottom: 4px;
    height: calc(100vh - 100px);
    min-height: 300px;
    position: relative;

    .switch-arrow {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 68px;
      height: 68px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.85);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 100;
      transition: all 0.3s;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

      &:hover {
        background: #ffffff;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

        .anticon {
          color: #1677ff;
        }
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          background: rgba(255, 255, 255, 0.85);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

          .anticon {
            color: rgba(0, 0, 0, 0.45);
          }
        }
      }

      &.left {
        left: -20px;
      }

      &.right {
        right: -20px;
      }

      .anticon {
        font-size: 24px;
        color: rgba(0, 0, 0, 0.45);
        transition: color 0.3s;
      }
    }

    .draw-button {
      position: absolute;
      left: 50%;
      top: auto;
      bottom: 160px;
      transform: translate(-50%, 0);
      width: 100px;
      height: 100px;
      border-radius: 50%;
      background: rgba(245, 245, 245, 0.9);
      border: 3px solid #d9d9d9;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 22px;
      color: #666;
      z-index: 90;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      user-select: none;

      &:hover {
        transform: translate(-50%, -4px) scale(1.05);
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
      }

      &.active {
        background: rgba(240, 245, 255, 0.95);
        border-color: #1677ff;
        color: #1677ff;
        box-shadow: 0 4px 12px rgba(22, 119, 255, 0.2);

        &:hover {
          transform: translate(-50%, -4px) scale(1.05);
          box-shadow: 0 8px 16px rgba(22, 119, 255, 0.3);
        }
      }

      &.draw-button-bad.active {
        background: rgba(255, 242, 240, 0.95);
        border-color: #ff4d4f;
        color: #ff4d4f;
        box-shadow: 0 4px 12px rgba(255, 77, 79, 0.2);

        &:hover {
          transform: translate(-50%, -4px) scale(1.05);
          box-shadow: 0 8px 16px rgba(255, 77, 79, 0.3);
        }
      }
    }

    .draw-button-bad {
      bottom: 56px !important;
    }

    .image-block {
      flex: 1;
      border-radius: 8px;
      overflow: hidden;
      background: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      height: 100%;
      display: flex;
      flex-direction: column;

      .image-title {
        padding: 4px 12px;
        font-size: 14px;
        font-weight: 600;
        color: #1a1a1a;
        background: #fafafa;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: center;

        .title-text {
          margin-right: 8px;
        }

        .icon-group {
          margin-left: 4px;

          .img-icon-like {
            font-size: 16px;
            cursor: pointer;
            color: #666;
            transition: all 0.2s;
            padding: 4px;
            border-radius: 4px;

            &:hover {
              color: #1677ff;
              background: rgba(22, 119, 255, 0.1);
            }
          }
        }
      }

      .image-wrapper {
        position: relative;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;

        .compare-image {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
          border-radius: 4px;
          cursor: pointer;
          transition: transform 0.3s;

          &:hover {
            transform: scale(1.02);
          }
        }

        .score-tag {
          position: absolute;
          top: 20px;
          right: 12px;
          padding: 2px 10px;
          border-radius: 10px;
          font-size: 36px;
          font-weight: 600;

          &.positive {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
          }

          &.negative {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
          }

          &.draw {
            background: #f5f5f5;
            color: #666;
            border: 1px solid #d9d9d9;
          }
        }

        .score-tag-left {
          left: 12px;
          right: unset !important;
        }
      }

      .image-info {
        padding: 12px;
        background: #fafafa;
        border-top: 1px solid #f0f0f0;

        .info-item {
          display: flex;
          align-items: center;
          margin-bottom: 4px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            color: #666;
            margin-right: 8px;
          }

          .value {
            font-weight: 600;
            color: #1a1a1a;

            &.positive {
              color: #52c41a;
            }

            &.negative {
              color: #ff4d4f;
            }
          }
        }
      }
    }
  }

  .scoring-buttons {
    display: flex;
    justify-content: center;
    margin-bottom: 12px;

    .ant-btn {
      min-width: 100px;
      height: 32px;
      font-size: 14px;

      &.draw-btn {
        color: #666;
        border-color: #d9d9d9;

        &:hover {
          color: #1677ff;
          border-color: #1677ff;
        }
      }
    }
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 4px;
    border-top: 1px solid #f0f0f0;
    margin-bottom: -12px;
  }

  .same-good-radio {
    &.ant-radio-button-wrapper-checked {
      background: #52c41a !important;
      border-color: #52c41a !important;
      color: white !important;
      
      &:hover {
        background: #52c41a !important;
        border-color: #52c41a !important;
      }
    }
  }

  .same-bad-radio {
    &.ant-radio-button-wrapper-checked {
      background: #ff4d4f !important;
      border-color: #ff4d4f !important;
      color: white !important;
      
      &:hover {
        background: #ff4d4f !important;
        border-color: #ff4d4f !important;
      }
    }
  }
}


