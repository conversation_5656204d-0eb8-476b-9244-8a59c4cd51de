import { Flex } from 'antd';
import { FC, useRef, useState } from 'react';
import { Resizable } from 'react-resizable';
import 'react-resizable/css/styles.css';
import './FloatingWindow.less';

interface FloatingWindowProps {
  open?: boolean;
  title: string;
  showElement: string | JSX.Element;
}

const FloatingWindow: FC<FloatingWindowProps> = ({
  open = true,
  title,
  showElement,
}) => {
  let windowWith = window.innerWidth;
  const dragRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ x: windowWith / 2 - 150, y: 120 }); // 窗口位置
  const [size, setSize] = useState({ width: 300, height: 400 + 32 }); // 窗口大小
  const dragStartPos = useRef({ x: 0, y: 0 });

  // 使用useRef保持最新状态引用
  const isDraggingRef = useRef(false);

  // 修改状态时同步更新ref
  const setIsDragging = (value: boolean) => {
    isDraggingRef.current = value;
  };

  // 开始拖动
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!dragRef.current) return;

    // 获取元素实际位置
    const rect = dragRef.current.getBoundingClientRect();

    // 计算正确的初始偏移量
    dragStartPos.current = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };

    setIsDragging(true);

    // 使用命名函数保持引用一致
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDraggingRef.current || !dragRef.current) return;

    // 计算新位置时考虑滚动偏移
    const newX = e.clientX - dragStartPos.current.x;
    const newY = e.clientY - dragStartPos.current.y;

    // 边界检查示例
    const maxX = window.innerWidth - dragRef.current.offsetWidth;
    const maxY = window.innerHeight - dragRef.current.offsetHeight;

    setPosition({
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY)),
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  // 调整窗口大小
  const handleResize = (event, { size }) => {
    setSize(size);
  };

  if (!open) {
    return null;
  }

  return (
    <div
      style={{
        position: 'fixed',
        left: position.x,
        top: position.y,
        transform: `translate(-50%, 0)})`,
        zIndex: 100,
        background: 'rgba(255, 255, 255, 0.95)',
        width: size.width,
        height: size.height,
      }}
      ref={dragRef}
    >
      <Resizable
        width={size.width}
        height={size.height}
        onResize={handleResize}
        draggableOpts={{ enableUserSelectHack: false }}
        handleSize={[28, 28]}
      >
        <div
          style={{
            width: size.width,
            height: size.height,
            border: '1px solid #ddd',
            position: 'relative',
          }}
        >
          <Flex
            justify={'center'}
            style={{
              cursor: 'move',
              padding: '10px',
              background: '#f0f0f0',
              borderBottom: '1px solid #ddd',
            }}
            onMouseDown={handleMouseDown}
          >
            {title},按住可拖动/缩放
          </Flex>
          <Flex
            vertical
            justify={'center'}
            align={'center'}
            className={'floating-window-inner-container'}
          >
            {showElement}
          </Flex>
        </div>
      </Resizable>
    </div>
  );
};

export default FloatingWindow;
