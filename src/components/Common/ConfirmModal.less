.confirm-modal {
  .confirm-modal-title {
    font-size: 16px;
    font-weight: 500;
    color: #000000;
  }

  .ant-modal-content {
    padding: 24px;
  }

  .ant-modal-header {
    margin-bottom: 16px;
  }

  .ant-modal-body {
    padding: 0;
    margin-bottom: 24px;
  }

  .ant-modal-footer {
    margin-top: 0;
    padding: 0;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  .modal-cancel-button-normal {
    width: 212px !important;
    height: 38px !important;
    border: 1px solid #E1E3EB !important;
    border-radius: 8px !important;
    color: #969799 !important;
    font-size: 16px !important;
    font-weight: 500;
    background: white !important;
    transition: all 0.3s ease;

    &:hover {
      color: #366EF4 !important;
      border-color: #366EF4 !important;
      box-shadow: 0 0 8px rgba(54, 110, 244, 0.3);
    }
  }

  .modal-confirm-button-normal {
    width: 212px !important;
    height: 38px !important;
    border-radius: 8px !important;
    font-size: 16px !important;
    background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
    border: none !important;
    transition: all 0.3s ease;
    color: white !important;

    &:hover {
      background: linear-gradient(90deg, #5C8EF6 0%, #8273F1 100%) !important;
      box-shadow: 0 0 10px rgba(0, 96, 255, 0.4);
    }
  }

  .modal-confirm-button-warning {
    width: 212px !important;
    height: 38px !important;
    border-radius: 8px !important;
    font-size: 16px !important;
    color: #FFA000 !important;
    background: rgba(255, 255, 255, 0) !important;
    border: 1px solid #FFA000 !important;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 0 8px rgba(255, 160, 0, 0.5);
      border-color: #FFB74D !important;
      color: #FFB74D !important;
    }
  }

  .modal-confirm-button-danger {
    width: 212px !important;
    height: 38px !important;
    border-radius: 8px !important;
    font-size: 16px !important;
    color: #F44336 !important;
    background: rgba(255, 255, 255, 0) !important;
    border: 1px solid #F44336 !important;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 0 8px rgba(244, 67, 54, 0.5);
      border-color: #f76d64 !important;
      color: #f76d64 !important;
    }
  }
}
