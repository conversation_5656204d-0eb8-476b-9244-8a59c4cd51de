import { Button, Flex, Form, Input, message, Modal, Tooltip } from 'antd';
import { CopyOutlined, EditOutlined, PlusCircleTwoTone, RedoOutlined } from '@ant-design/icons';
import React, { FC, useEffect, useState } from 'react';
import IconFont from '@/components/IconFont';
import { copyToClipboard, deepCopy } from '@/utils/utils';
import './CommonComponent.less';

export const RefreshButton: FC<{ refresh: () => void }> = ({ refresh }) =>
  <Tooltip title={'刷新页面数据'}>
    <Button icon={<RedoOutlined />} onClick={() => {
      refresh();
      message.success('刷新成功');
    }} />
  </Tooltip>;

export const EditItemButton: FC<{
  id: number,
  value: string,
  tooltip: string,
  title: string,
  method: (id: number, value: string) => Promise<any>;
  onChange?: () => void;
  rules?: any[]
}>
  = ({ id, value, tooltip, title, method, onChange, rules }) => {

  const [open, setOpen] = useState(false);
  const [target, setTarget] = useState<string>(value);
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue({
      name: value,
    });

    return () => {
      form.resetFields();
    };
  }, []);

  const handleModify = () => {
    if (!target) {
      message.warning(`请输入${title}`);
      return;
    }
    method(id, target).then(res => {
      if (res) {
        message.success('修改成功');
        if (onChange) {
          onChange();
        }
        setOpen(false);
      }
    });
  };

  return <>
    <Tooltip title={tooltip}>
      <IconFont type={'icon-bianji'} className={'pointer'} style={{ fontSize: 16 }}
                onClick={() => setOpen(true)} />
    </Tooltip>

    {open &&
      <Modal open={true}
             onCancel={() => setOpen(false)} title={'修改' + title}
             onOk={() => {
               form.validateFields().then(() => {
                 handleModify();
               });
             }}>

        <Flex align={'center'}>
          <Form form={form}>
            <Form.Item label={title} name="name" style={{ width: '100%' }}
                       rules={rules || []}>
              <Input style={{ width: 400 }} value={target} onChange={e => setTarget(e.target.value)} />
            </Form.Item>
          </Form>
        </Flex>

      </Modal>
    }
  </>;
};

export const MemoText: FC<{ value: any }> = ({ value }) => {
  if (!value) return null;
  return <span style={{ color: '#727375', fontSize: 12 }}>{`(${value})`}</span>;
};

export const CopyIcon: FC<{ value: string }> = ({ value }) => {
  return <>
    <Tooltip title={'拷贝当前内容到剪切板'}>
      <CopyOutlined 
        style={{ color: '#727375', fontSize: 12 }} 
        onClick={() => {
          copyToClipboard(
            value,
            () => message.success('复制成功'),
            (error) => message.error('复制失败：' + error.message)
          );
        }} 
      />
    </Tooltip>
  </>;
};

type Position = 'leftTop' | 'rightTop' | 'leftBottom' | 'rightBottom';
export const MiniTag: FC<{ title?: string | Element | undefined | React.ReactNode, defaultValue?: string | Element | undefined, position?: Position, textFontSize?: number }>
  = ({ title, defaultValue, position = 'leftTop', textFontSize = 12}) => {
  let target = title;
  if (!target) {
    target = defaultValue ? defaultValue : '';
  }
  const positionStyle = {
    leftTop: { left: 8, top: 8 },
    rightTop: { right: 8, top: 8 },
    leftBottom: { left: 8, bottom: 8 },
    rightBottom: { right: 8, bottom: 8 },
  };
  if (!title) return null;
  return <Flex align={'center'} justify={'center'}
               style={{
                 position: 'absolute', background: 'rgba(0, 0, 0, 0.6)',
                 borderRadius: 4, padding: 4, ...positionStyle[position]
               }}>
    <div className={'color-w'} style={{ fontSize: textFontSize }}>{target as React.ReactNode}</div>
  </Flex>;
};

export const KeyValueEditModal: FC<{
  title?: string,
  isAdd?: boolean,
  keyValues: Array<{ key: string, value: any, label: string }>,
  onChange: (value: Array<{ key: string, value: any }>) => void
}> = ({ title, isAdd, keyValues, onChange }) => {
  const [open, setOpen] = useState(false);
  const [items, setItems] = useState(keyValues);

  useEffect(() => {
    setItems(keyValues);
  }, [keyValues]);

  const handleChange = (index: number, value: string) => {
    const copy = deepCopy(items);
    copy[index].value = value;
    setItems(copy);
  };

  const handleSave = () => {
    onChange(items);
    setOpen(false);
  };

  return (
    <>
      <Tooltip title={`${isAdd ? '新增' : '修改'}${title}`}>
        {!isAdd &&
          <EditOutlined style={{ fontSize: 14 }} onClick={() => setOpen(true)} />
        }
        {isAdd &&
          <PlusCircleTwoTone style={{ fontSize: 14 }} onClick={() => setOpen(true)} />
        }
      </Tooltip>

      {open &&
        <Modal open={true} title={title} width={400} onOk={handleSave} onCancel={() => setOpen(false)}>
          <Flex vertical>
            {items.map(({ key, value, label }, index) => <Flex key={index} align={'center'} gap={8}>
              <div style={{ width: 80 }}>{label ? label : key}:</div>
              <Input value={value} onChange={e => handleChange(index, e.target.value)} />
            </Flex>)}
          </Flex>
        </Modal>
      }
    </>
  );
};

export const ScrollNotice: FC<{ text: string | React.ReactNode, color?: string }> = ({ text, color }) => {
  return (
    <div className="scroll-notice-container">
      <div className="scroll-notice-content" style={{color}}>{text}</div>
    </div>
  );
};

export const HighlightText:  FC<{ content: string, words?: string[] }>
    = ({ content, words =['error'] }) => {
  // 将文本按照关键词分割成片段
  let parts: (string | React.ReactNode)[] = [content]; // 初始时整个文本作为一个片段
  words.forEach( word => {
    parts = parts.flatMap((part) => {
      if (typeof part === 'string') {
        return part.split(new RegExp(`(${word})`, 'gi')).map((text, index) =>
          index % 2 === 1 ? (
            <span key={index} style={{ backgroundColor: 'yellow', color: 'red', fontWeight: 'bold' }}>
                            {text}
                        </span>
          ) : text
        );
      } else {
        return part;
      }
    });
  });

  return <div>{parts}</div>;
}