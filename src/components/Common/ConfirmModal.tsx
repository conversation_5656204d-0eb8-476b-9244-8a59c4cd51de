import React from 'react';
import { Modal, Button } from 'antd';
import './ConfirmModal.less';

interface ConfirmModalProps {
  title?: React.ReactNode;
  content?: React.ReactNode;
  open: boolean;
  confirmLoading?: boolean;
  okText?: string;
  okType?: 'danger' | 'warning' | 'normal'
  cancelText?: string;
  width?: string | number;
  centered?: boolean;
  onOk?: () => void;
  onCancel?: () => void;
  // 特殊场景的标志
  isSpecialCase?: boolean;
  specialCaseText?: {
    title?: string;
    okText?: string;
    cancelText?: string;
    content?: React.ReactNode;
  };
  // 其他自定义属性
  pointsToDeduct?: number;
  itemName?: string;
  itemCount?: number;
}

const ConfirmModal: React.FC<ConfirmModalProps> = ({
  title = '操作确认',
  content,
  open,
  confirmLoading = false,
  okText = '确定',
  cancelText = '取消',
  width = 'auto',
  centered = false,
  okType = 'normal',
  onOk,
  onCancel,
  isSpecialCase = false,
  specialCaseText,
  pointsToDeduct,
  itemName,
  itemCount = 100,
}) => {
  // 处理确认按钮点击
  const handleOk = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    if (onOk) {
      onOk();
    }
  };

  // 处理取消按钮点击
  const handleCancel = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    if (onCancel) {
      onCancel();
    }
  };

  // 根据特殊场景决定显示的文本
  const displayTitle = isSpecialCase && specialCaseText?.title ? specialCaseText.title : title;
  const displayOkText = confirmLoading 
    ? '处理中' 
    : (isSpecialCase && specialCaseText?.okText ? specialCaseText.okText : okText);
  const displayCancelText = isSpecialCase && specialCaseText?.cancelText 
    ? specialCaseText.cancelText 
    : cancelText;
  
  // 构建内容
  const renderContent = () => {
    if (content) {
      return content;
    }
    
    if (isSpecialCase && specialCaseText?.content) {
      return specialCaseText.content;
    }
    
    // 默认内容
    return (
      <span style={{ fontSize: 14, color: '#727375' }}>
        {`将扣除 ${pointsToDeduct} 缪斯点，并创建"${itemName}"，含 ${itemCount} 张图片创作数量`}
      </span>
    );
  };

  return (
    <Modal
      className='confirm-modal'
      width={width}
      styles={{
        content: {
          borderRadius: 16,
        }
      }}
      title={<span className='confirm-modal-title'>{displayTitle}</span>}
      centered={centered}
      open={open}
      okText={displayOkText}
      cancelText={displayCancelText}
      cancelButtonProps={{ disabled: confirmLoading, className: 'modal-cancel-button' }}
      onCancel={handleCancel}
      footer={[
        <Button
          key="cancel"
          disabled={confirmLoading}
          className='modal-cancel-button-normal'
          onClick={handleCancel}
        >
          {displayCancelText}
        </Button>,
        <Button
          key="confirm"
          className={'modal-confirm-button-' + okType}
          loading={confirmLoading}
          onClick={handleOk}
        >
          {displayOkText}
        </Button>
      ]}
      closable={!confirmLoading}
      maskClosable={false}
      keyboard={false}
    >
      {renderContent()}
    </Modal>
  );
};

export default ConfirmModal;
