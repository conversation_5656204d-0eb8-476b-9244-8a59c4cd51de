.image-uploader-main-contain {
  width: 100%;
  height: 282px;
  flex-direction: column;
  background: #F5F6F9;
  border-radius: 8px;
  padding: 12px;
  &-fix {
    height: 100%;
    width: 100%;
  }
}

.image-uploader-title {
  font: 500 16px "PingFang SC";
  line-height: 22px;
  color: #1A1B1D;
}

.image-uploader-dragger {
  width: 100% !important;
  height: 100% ;
  background: #F5F6F9;
  border-radius: 8px;
}
.image-uploader-img-container {
  width: 100%;
  height: 50%;
  flex-grow: 1;
  border-radius: 8px;
  border: 1px dashed #D8D8D8;
  background: #FFFFFF;
  position: relative;
  box-sizing: border-box;
  object-fit: contain;
  .image-uploader-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center; /* 控制图片居中显示 */
  }
}

.image-uploader-button {
  position: absolute;
  right: 12px;
  top: 12px;
  height: auto;
  .image-uploader-upload-btn {
    width: 32px;
    height: 32px;
    border-radius: 16px;
    background-color: #F5F6F9;
    border: 1px solid #D9D9D9;
    &:hover {
      border: 1px solid #5C8DF6;
      .anticon {
        color: #5C8DF6;
      }
    }
  }
  .image-uploader-upload-btn-delete {
    width: 32px;
    height: 32px;
    border-radius: 16px;
    background-color: #F5F6F9;
    border: 1px solid #D9D9D9;
    &:hover {
      border: 1px solid #FC343F;
      .anticon {
        color: #FC343F;
      }
    }
  }
}

.image-upload-free-icon-desc {
  position: absolute;
  right: 8px;
  top: 2px;
  color: #864D02;
  z-index: 2;
}

.image-upload-on-charge {
  background: linear-gradient(90deg, #EAEAEA 0%, #D9D9D9 100%);
  position: absolute;
  border-radius: 0 8px 0 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 20px;
  width: 64px;
  top: 0;
  right: 0;
  z-index: 2;
}