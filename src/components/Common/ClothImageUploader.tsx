import React, { useState } from 'react';
import { Button, Flex, message, Tooltip, Upload, UploadProps } from 'antd';
import IconFont from '@/components/IconFont';
import { UPLOAD_URL } from '@/constants';
import '@/components/Common/ImageUploader.less'
import ClothImageSelector from '@/components/Creative/ClothImageSelector';
import { convertToJPG } from '@/utils/imageUtils';
const { Dragger } = Upload;
interface UploadImageProps {
  title?: string;
  image: string;
  modelId?: number;
  onImageChange: (value: any) => void;
  onImageClick?: () => void;
  className?: string;
  render?: React.ReactNode;
  originImage?: string;
  setLoading?: any;
  desc?: string;
}

/**
 * 单张图片上传组件(可以从历史创作中选择)
 * @param title 标题(可选)
 * @param image 图片地址
 * @param modelId
 * @param onImageChange 图片地址改变的回调
 * @param onImageClick 图片点击事件
 * @param className 方便外部修改样式
 * @param render
 * @param originImage
 * @param setLoading 上传状态
 * @param desc
 * @constructor
 */
const ClothImageUploader : React.FC<UploadImageProps> = ({
                                                      title,
                                                      image,
                                                      modelId,
                                                      onImageChange,
                                                      onImageClick,
                                                      className,
                                                      render,
                                                      originImage,
                                                      setLoading = undefined,
                                                      desc = ''
                                                    }) => {
  const [showClothSelector, setShowClothSelector] = useState(false);

  const uploadProps :UploadProps = {
    accept: 'image/png, image/jpeg',
    showUploadList: false,
    action: UPLOAD_URL,
    multiple: false,
    name: 'file',
    onChange(info) { handleFileChange(info, onImageChange); },
    beforeUpload: async (file) => {
      setLoading && setLoading(true);
      return await convertToJPG(file);
    }
  };
  const handleFileChange = (info, onImageChange) => {
    const {file} = info;
    // 检查文件是否上传成功
    if (file.status === 'done' && file.response && file.response.success) {
      // 处理上传成功的文件
      onImageChange(file.response.data);
      setLoading && setLoading(false);
    } else if (file.status === 'error') {
      // 处理上传错误的文件
      message.error('上传图片异常，请重试');
    }
  }
  const handleShowHistorySelector = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    setShowClothSelector(true);
  };

  return (
    <Flex
      vertical
      gap={8}
      className={
        'image-uploader-main-contain' +
        (title ? '' : '-fix') +
        (className ? ' ' + className : '')
      }
    >
      {title && <div className={'image-uploader-title'}>{title}</div>}
      {!image && (
        <Dragger className={'image-uploader-dragger'} {...uploadProps}>
          <Flex vertical justify={'space-between'} gap={8}>
            <Flex vertical gap={8}>
              {render}
              <Flex gap={16} align={'center'} justify={'center'}>
                <Button
                  type={'primary'}
                  style={{ width: 196, height: 38 }}
                  icon={
                    <IconFont
                      type={'icon-lishirenwu_weixuanzhong1'}
                      style={{ fontSize: 16 }}
                    />
                  }
                  onClick={handleShowHistorySelector}
                >
                  从上传图片中选择
                </Button>
                <Button
                  style={{
                    width: 132,
                    height: 38,
                    border: '1px solid #B5C7FF',
                  }}
                  icon={
                    <IconFont
                      type={'icon-shangchuan'}
                      style={{ fontSize: 16, color: '#366EF4' }}
                    />
                  }
                >
                  <div className={'color-36 weight'}>本地上传</div>
                </Button>
              </Flex>
              <div className="text14 color-96">
                点击/粘贴/拖拽图片至此，支持png、jpg格式
              </div>
            </Flex>
          </Flex>
        </Dragger>
      )}
      {image && (
        <Flex
          gap={14}
          justify={'center'}
          className={'image-uploader-img-container'}
        >
          <img
            src={image}
            alt={'上传原图'}
            className={'image-uploader-img'}
            onClick={onImageClick}
            style={{ cursor: onImageClick ? 'pointer' : 'default' }}
          />
          <Flex vertical gap={8} className={'image-uploader-button'}>
            <Upload {...uploadProps}>
              <Tooltip title={'重新上传'}>
                <Button
                  className={'image-uploader-upload-btn'}
                  icon={
                    <IconFont
                      type={'icon-icon_qiehuan'}
                      style={{ fontSize: 24 }}
                    />
                  }
                />
              </Tooltip>
            </Upload>
            <Button
              className={'image-uploader-upload-btn-delete'}
              icon={
                <IconFont type={'icon-icon_shanchu'} style={{ fontSize: 24 }} />
              }
              onClick={() => onImageChange('')}
            />
          </Flex>
        </Flex>
      )}
      {showClothSelector && (
        <ClothImageSelector
          value={image ? [image] : []}
          onFinish={(value) => {
            onImageChange(value && value.length > 0 ? value[0] : '');
            setShowClothSelector(false);
          }}
          onCancel={() => setShowClothSelector(false)}
          maxChoose={1}
          modelId={modelId}
          originImage={originImage}
          desc={desc}
        />
      )}
    </Flex>
  );
};
export default ClothImageUploader;