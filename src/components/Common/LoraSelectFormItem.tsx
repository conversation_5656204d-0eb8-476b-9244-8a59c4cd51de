import Form from 'antd/lib/form';
import { Button, Flex, Input, Tooltip } from 'antd';
import React, { FC, useEffect, useState } from 'react';
import { getMaterialModelById, MaterialModel } from '@/services/MaterialModelController';
import LoraSelector from '@/components/LoraSelector';
import { formatText } from '@/utils/utils';
import { CloseCircleOutlined } from '@ant-design/icons';

export interface LoraSelectFormItemProps {
  form: any;
  name: any;
  label?: string;
  restField?: any;
  required: boolean;
  labelSpan?: number;
  vertical?: boolean;
  rootKey?: string;
  noStyle?: boolean;
  btnTitle?: string;
  authorized?: boolean; // 是否有权限，目前用于渠道商的权限逻辑
  onChange?: (item: MaterialModel | null, name: any) => void;
}

const LoraSelectFormItem: FC<LoraSelectFormItemProps> = ({
                                                           form,
                                                           name,
                                                           label,
                                                           restField,
                                                           required,
                                                           labelSpan,
                                                           vertical = false,
                                                           rootKey,
                                                           onChange = null,
                                                           noStyle = false,
                                                           authorized = false,
                                                           btnTitle = "添加lora",
                                                         }) => {
  const [selected, setSelected] = useState<MaterialModel | null>(null);
  const [showSelector, setShowSelector] = useState(false);

  const handleChange = (item: MaterialModel | null) => {
    setShowSelector(false);
    setSelected(item);

    const id = item ? item.id : null;
    if (rootKey) {
      form.setFieldValue([rootKey, ...name], id);
    } else {
      form.setFieldValue(name, id);
    }

    if (onChange) {
      onChange(item, name);
    }
  };

  // 监听表单值变化并更新组件状态
  const updateSelectedModel = () => {
    const value = form.getFieldValue(name);

    // 处理实验参数的情况
    if (Array.isArray(name) && name.includes('comparisonParams')) {
      const [index, , paramIndex] = name;
      const values = form.getFieldsValue();
      const currentItem = values.items[index];
      if (currentItem?.comparisonParams?.[paramIndex]?.value) {
        const loraId = currentItem.comparisonParams[paramIndex].value;
        if (loraId && (!selected || selected.id !== loraId)) {
          getMaterialModelById(loraId).then(res => {
            if (!res) return;
            setSelected(res);
          });
        }
        return;
      }
    }

    // 处理共享参数的情况
    if (value && (!selected || selected.id !== value)) {
      getMaterialModelById(value).then(res => {
        if (!res) return;
        setSelected(res);
      });
    } else if (!value && selected) {
      setSelected(null);
    }
  };

  // 初始化时获取数据
  useEffect(() => {
    updateSelectedModel();
  }, []);

  return (
    <Form.Item
      noStyle
      shouldUpdate={(prevValues, curValues) => {
        const prevValue = form.getFieldValue(name);
        const curValue = form.getFieldValue(name);
        return prevValue !== curValue;
      }}
    >
      {() => (
        <>
          <Form.Item {...restField} labelCol={{ span: labelSpan }} name={name} label={label} noStyle={noStyle} required={required}>
            <Flex align={'center'}>
              <Input hidden />
              {selected &&
                <Flex gap={4}>
                  <Flex vertical={vertical} align={'center'}>
                    <img src={selected.showImage} alt={'lora'} width={30} />
                    <Tooltip title={selected.name}>
                      <div className={'text12'}>{formatText(selected.name, 8)}</div>
                    </Tooltip>
                  </Flex>
                  <CloseCircleOutlined style={{ fontSize: 16 }} onClick={() => handleChange(null)} />
                </Flex>
              }

              {!selected &&
                <Button onClick={() => setShowSelector(true)}>{btnTitle}</Button>
              }
            </Flex>
          </Form.Item>

          {showSelector &&
            <LoraSelector show={true} isOwner={false} type={'CUSTOM'} onChange={handleChange}
                          onCancel={() => setShowSelector(false)} authorized={authorized} />
          }
        </>
      )}
    </Form.Item>
  );
};

export default LoraSelectFormItem;