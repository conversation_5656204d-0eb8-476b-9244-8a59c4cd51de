import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Flex, message, Spin, Tooltip, Upload } from 'antd';
import IconFont from '@/components/IconFont';
import { compressFile, convertHeicToJpeg, convertToJPG } from '@/utils/imageUtils';
import { uploadFile } from '@/services/FileController';
import ImgPreview from '@/components/ImgPreview';
import { getUserInfo } from '@/utils/utils';

interface BatchUploaderProps {
  uploadBGList?: string[];
  uploadBGMaskList?: boolean[];
  value?: UploadItem[];
  maxCount?: number;
  minCount?: number;
  isUploadPro?: boolean;
  minSize?: number;
  checkNeedTopup?: () => boolean;
  onUploadChange: (uploadItems: UploadItem[]) => void;
}

export interface BatchUploaderRef {
  handleImageUploads: () => Promise<void>;
  clear: () => void;
  checkSize: () => boolean;
}

interface UploadItem {
  index: number;
  url?: string;//上传服务端前是本地地址，上传服务端后与imgUrl相同，为远程地址
  file?: any;//文件对象
  imgUrl?: string;//远程地址oss url
  uploadSizeError?: boolean; //是否上传大小超出限制
  uploadSizeRatioError?: boolean;//宽高比是否有误
}

export const lengthOfUpload = (list: UploadItem[] | null | undefined) => {
  if (!list) return 0;
  return list.filter(item => item.url && item.url !== '').length;
};

export const fetchUploadUrl = (list: UploadItem[] | null | undefined) => {
  if (!list) return [];
  return list.filter(item => item.url && item.url !== '' && item.imgUrl && item.imgUrl !== '').map(item => item.imgUrl);
};

const BatchUploader = forwardRef<BatchUploaderRef, BatchUploaderProps>(({
                                                                          uploadBGList = [],
                                                                          uploadBGMaskList = [],
                                                                          value = [],
                                                                          maxCount = 100,
                                                                          minCount = 1,
                                                                          isUploadPro = false,
                                                                          minSize = 1024,
                                                                          checkNeedTopup,
                                                                          onUploadChange,
                                                                        }, ref) => {

    const userInfo = getUserInfo();
    const [uploadItems, setUploadItems] = useState<UploadItem[]>([]);
    //图片数量错误
    const [imgCountErr, setImgCountErr] = useState('');
    const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null);
    const [loading, setLoading] = useState<boolean>(false); // 用于控制加载状态
    const multiple = maxCount !== 1;

    const updateImgItemByIndex = (uploadItem: UploadItem) => {
      setUploadItems(prevItems => {
        // 创建新的副本以避免直接修改状态
        const newItems = [...prevItems];

        // 检查该 index 是否已存在
        const index = newItems.findIndex(item => item.index === uploadItem.index);
        if (index !== -1) {
          // 如果存在，更新对应的项
          newItems[index] = uploadItem;
        } else {
          // 如果不存在，将新的项添加到数组中
          newItems.push(uploadItem);
        }
        return newItems;
      });
    };

    const handleUploadImgItem = async (file: any, uploadItem: UploadItem, doUpload: boolean = true, retry: boolean = false) => {
      try {
        // 转换 HEIC 文件
        const processedFile = await convertHeicToJpeg(file);
        if (processedFile && processedFile.type.startsWith('image/')) {
          const img = new Image();
          let objectUrl = URL.createObjectURL(processedFile);

          img.onload = () => {
            uploadItem.url = objectUrl;
            uploadItem.file = processedFile;

            // 检查图片宽度和高度
            uploadItem.uploadSizeError = img.width < minSize || img.height < minSize;

            updateImgItemByIndex(uploadItem);

            const uploadWithRetry = (retryCount, compressedFile) => {
              uploadFile(compressedFile, isUploadPro).then(imgUrl => {
                if (imgUrl) {
                  uploadItem.imgUrl = imgUrl;

                  //上传成功，更新url/file/objectUrl，清理浏览器缓存
                  uploadItem.url = imgUrl;
                  uploadItem.file = null;

                  URL.revokeObjectURL(objectUrl);
                  objectUrl = imgUrl;

                  updateImgItemByIndex(uploadItem);
                }
              }).catch(error => {
                if (retry && retryCount < 5) {
                  setTimeout(() => {
                    console.log('index,retryCount:', uploadItem.index, retryCount);
                    uploadWithRetry(retryCount + 1, compressedFile);
                  }, 1000);
                } else {
                  console.log(error);
                }
              });
            };

            if (doUpload) {
              //先压缩图像到1M以内，再进行上传
              compressFile(processedFile).then(f => {
                uploadWithRetry(0, f);
              });
            }
          };

          img.onerror = (event) => {
            console.error('Image load error:', event);
            message.error('无法加载图片，请检查文件格式');
            URL.revokeObjectURL(objectUrl);
          };

          img.src = objectUrl;
        }
      } catch (error) {
        console.error('File processing error:', error);
      }
    };

    const onFileChange = (event, item: UploadItem, uploadItems) => {
      const file = event.target.files[0];
      handleUploadImgItem(file, item, uploadItems);
      event.target.value = '';
    };

    const onFileDrop = (event, item: UploadItem) => {
      if (!checkNeedTopup || !checkNeedTopup()) {
        event.preventDefault();
        const file = event.dataTransfer.files[0];
        handleUploadImgItem(file, item);
        event.target.value = '';
      }
    };

    const handlePreviewUrl = (url: string | undefined) => {
      if (url) {
        setPreviewImageUrl(url);
      }
    };

    const getNextIndexByUploadItems = (items: UploadItem[] | undefined) => {
      if (!items) {
        return 0;
      }
      let max = -1;
      items.forEach(item => {
        if (item.index && item.index > max) {
          max = item.index;
        }
      });

      return max + 1;
    };

    const batchUploadFiles = async (files, uploadItems: UploadItem[] | undefined) => {
      if (files !== null) {

        console.log('batchUploadFiles,files', files);

        // 创建一个数组来存储所有的上传 Promise
        const uploadPromises: Promise<void>[] = [];

        for (let i = 0; i < files.length; i++) {
          try {
            let uploadImgItem: UploadItem = {
              index: getNextIndexByUploadItems(uploadItems) + i,
            };

            // 将每个上传操作的 Promise 添加到数组中
            const jpg = await convertToJPG(files[i]);
            const uploadPromise = handleUploadImgItem(jpg, uploadImgItem, userInfo?.roleType !== 'DEMO_ACCOUNT', false);
            uploadPromises.push(uploadPromise);
          } catch (error) {
            console.error('File processing error:', error);
          }
        }

        // 等待所有的上传操作完成
        await Promise.all(uploadPromises);
      }
    };

    const doHandleImageUploads = async () => {
      const startTime = Date.now();
      let totalUploadedImages = 0;
      for (let j = 0; j < uploadItems.filter(item => item.url).length; j++) {
        const img = uploadItems[j];
        if (img && img.url && !img.imgUrl) {
          handleUploadImgItem(img.file, img, true, true);
          totalUploadedImages++;
        }
      }

      // 轮询检查图片上传状态
      let isUploading = true;
      while (isUploading) {
        isUploading = false;

        // 遍历所有 colorGroup 检查是否有图片正在上传
        for (let i = 0; i < uploadItems.filter(item => item.url).length; ++i) {
          // 如果存在 url 已设置但 imgUrl 为空的图片，表示仍在上传中
          const img = uploadItems[i];
          if (img && img.url && !img.imgUrl) {
            isUploading = true;
            break;
          }
        }

        if (isUploading) {
          console.log('图片上传中');
          await new Promise(resolve => {
            setTimeout(resolve, 2000);
          });
        }
      }

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      console.log(`补偿上传图片完成，总共补偿上传图片张数：${totalUploadedImages}，总耗时：${totalTime}毫秒`);
    };

    useImperativeHandle(ref, () => ({
      handleImageUploads: () => doHandleImageUploads(),
      clear: () => setUploadItems([]),
      checkSize: () => lengthOfUpload(uploadItems) < minCount || lengthOfUpload(uploadItems) > maxCount,
    }))
    ;

    useEffect(() => {
      let imgNum = uploadItems?.filter(u => u?.url)?.length;
      if (imgNum > 0 && (imgNum < minCount)) {
        setImgCountErr(`请至少上传${minCount}张图片`);
      } else {
        setImgCountErr('');
      }
      onUploadChange(uploadItems);
    }, [uploadItems]);

    useEffect(() => {
      if (uploadItems.filter(e => e.url).length <= 0 && value?.filter(e => e.url).length > 0) {
        setUploadItems(value);
      }
    }, [value]);

    return (
      <div className="upload-section">
        <div className="batch-upload-list" style={{ border: imgCountErr ? '1px solid #FC343F' : '' }}>

          {/*已经上传的图片列*/}
          {uploadItems && uploadItems.length > 0 && uploadItems.filter(imgItem => imgItem.url).map((imgItem, idx) => {
            return (
              <div key={idx} className="batch-upload-item"
                   onDrop={(e) => onFileDrop(e, imgItem)}
                   onDragOver={(e) => e.preventDefault()}
                   data-index={idx}>

                <div className="img-container">
                  <img src={imgItem.url}
                       alt="Uploaded"
                       className="uploaded-img"
                       onClick={() => {
                         handlePreviewUrl(imgItem.url || imgItem.imgUrl);
                       }} />

                  {/*上传图片大小有错误-提醒*/}
                  {imgItem.uploadSizeError !== null && imgItem.uploadSizeError && (
                    <>
                      <div className="size-error-overlay"></div>
                      <div className={'bottom-status-row'} style={{ background: '#FEF3E6' }}>
                        <Tooltip title={`请上传${minSize}x${minSize}像素以上的图片`}>
                          <Flex gap={5}>
                            <IconFont type={'icon-cuowu'} style={{ fontSize: 14 }} />
                            <div>像素过低请重新上传</div>
                          </Flex>
                        </Tooltip>
                      </div>
                    </>
                  )}

                  {/*重新上传*/}
                  <Tooltip title="重新上传">
                    <Upload
                      accept=".jpg,.jpeg,.png,.heic,.heif,.webp"
                      showUploadList={false}
                      className="upload-again"
                      beforeUpload={async (file) => {
                        if (checkNeedTopup && checkNeedTopup()) {
                          return false;
                        }
                        const jpg = await convertToJPG(file);
                        onFileChange({ target: { files: [jpg] } }, imgItem, uploadItems);
                        return false; // 阻止 Upload 组件的默认上传行为
                      }}
                    >
                      <IconFont type={'icon-icon_qiehuan'} />
                    </Upload>
                  </Tooltip>

                  {/*清除图片*/}
                  <IconFont className="upload-remove" type={'icon-icon_shanchu'}
                            onClick={() => {
                              imgItem.file = null;
                              imgItem.url = '';
                              imgItem.imgUrl = '';
                              updateImgItemByIndex(imgItem);
                            }} />
                </div>
              </div>
            );
          })}

          {/*上传图片*/}
          {(uploadItems.filter(e => e.url).length < maxCount) &&
            <Spin spinning={loading} tip="文件上传中...">
              <Upload
                accept=".jpg,.jpeg,.png,.heic,.heif,.webp"
                showUploadList={false}
                multiple={multiple}
                beforeUpload={async (file, fileList) => {
                  // 获取当前文件在 fileList 中的索引
                  const fileIndex = fileList.findIndex(item => item.uid === file.uid);

                  if (fileIndex === 0 && checkNeedTopup && checkNeedTopup()) {
                    return false; // 阻止上传
                  }

                  // 判断当前文件是否是最后一个文件
                  const isLastFile = fileIndex === fileList.length - 1;

                  // 只有在最后一个文件时执行上传逻辑
                  if (isLastFile) {
                    setLoading(true); // 开始加载状态
                    // @ts-ignore
                    await new Promise(resolve => {
                      setTimeout(resolve, 0);
                    });

                    try {
                      await batchUploadFiles(fileList, uploadItems); // 异步上传
                    } catch (error) {
                      console.error('文件上传失败:', error);
                    } finally {
                      setLoading(false); // 结束加载状态
                      await new Promise(resolve => {
                        setTimeout(resolve, 0);
                      });
                    }
                  }

                  return false; // 阻止默认上传行为
                }}
              >
                <Flex gap={8}>
                  {uploadBGList && uploadBGList.map((item, idx) =>
                    <div className="batch-custom-upload" key={idx}>

                      <img src={item} alt={''}
                           style={{ width: '100%', height: '100%', position: 'absolute', zIndex: 0 }} />

                      {/*是否设置mask*/}
                      {uploadBGMaskList && uploadBGMaskList.length > idx && uploadBGMaskList[idx] &&
                        <div style={{
                          position: 'absolute',
                          width: '100%',
                          height: '100%',
                          background: 'rgba(255, 255, 255, 0.8)',
                        }} />
                      }

                      {!loading && (
                        <>
                          <IconFont type={'icon-a-shangchuantupian1x'} style={{ fontSize: '32px', zIndex: 1 }} />
                          <div className="card-title">点击/拖拽图片至此</div>
                          {multiple &&
                            <div className="upload-text">（支持批量上传）</div>
                          }
                        </>
                      )}
                    </div>,
                  )}
                </Flex>
              </Upload>
            </Spin>
          }
        </div>

        <Flex>
          {imgCountErr && <div style={{ fontSize: 16, color: '#FC343F' }}>{imgCountErr}</div>}
          {imgCountErr && uploadItems && uploadItems.length > 0 && uploadItems.filter(imgItem => imgItem.url && imgItem.uploadSizeError)?.length > 0 && ','}
          {uploadItems && uploadItems.length > 0 && uploadItems.filter(imgItem => imgItem.url && imgItem.uploadSizeError)?.length > 0 &&
            <div style={{ fontSize: 16, color: '#FC343F' }}>请上传{minSize}x{minSize}像素以上的图片</div>
          }
        </Flex>

        {previewImageUrl &&
          <ImgPreview
            previewVisible={true}
            handleCancelPreview={() => setPreviewImageUrl(null)}
            previewImage={previewImageUrl || ''}
            needWatermark={false}
          />
        }

      </div>
    );
  },
);

export default BatchUploader;