import React, { useState } from 'react';
import { Button, Flex, message, Tooltip, Upload, UploadProps } from 'antd';
import IconFont from '@/components/IconFont';
import ImageSelector from '@/components/Creative/ImageSelector';
import { UPLOAD_URL } from '@/constants';
import '@/components/Common/ImageUploader.less'
import { compressFile, convertToJPG, resizeImage } from '@/utils/imageUtils';
import FREE_BG from '@/assets/images/free-bg.png';
const { Dragger } = Upload;
interface UploadImageProps {
  title?: string;
  image: string;
  onImageChange: (value: any) => void;
  onModelIdChange?: (value: number) => void;
  onImageClick?: () => void;
  render?: React.ReactNode;
  afterRender?: React.ReactNode;
  className?: string;
  historyFree?: boolean;
  historyCost?: number | null | undefined;
  uploadCost?: number | null | undefined;
  uploadCharge?: boolean;
  setImageSource?: (value: any) => void;
  resize?: number | undefined;
  compress?: number | undefined;
  excludesType?: Array<string> | null;
}

/**
 * 重新上传和删除按钮
 * @param onImageChange 修改图片的回调
 * @param setImageSource 修改图片来源的回调, 入参: 'history' || 'upload'
 * @param resize
 * @param compress
 * @constructor
 */
export const ImageUploaderButton = ({onImageChange, setImageSource = (value: any) => {}, resize = 0, compress = 0 }) => {
  const uploadProps :UploadProps = {
    accept: 'image/png, image/jpeg',
    showUploadList: false,
    action: UPLOAD_URL,
    multiple: false,
    name: 'file',
    onChange(info) { handleFileChange(info, onImageChange); },
    beforeUpload: async (file) => {
      let processedFile: any = file;

      if (resize) {
        processedFile = await resizeImage(processedFile, resize);
      }

      if (compress) {
        processedFile = await compressFile(processedFile, compress);
      }

      // Always convert to JPG at the end
      return await convertToJPG(processedFile);
    },
  };
  const handleFileChange = (info, onImageChange) => {
    const {file} = info;
    // 检查文件是否上传成功
    if (file.status === 'done' && file.response && file.response.success) {
      // 处理上传成功的文件
      onImageChange(file.response.data);
      setImageSource && setImageSource('upload');
    } else if (file.status === 'error') {
      // 处理上传错误的文件
      message.error('上传图片异常，请重试');
    }
  }
  return (
    <Flex vertical gap={8} className={'image-uploader-button'}>
      <Upload {...uploadProps}>
        <Tooltip title={"重新上传"}>
          <Button className={'image-uploader-upload-btn'} icon={<IconFont type={'icon-icon_qiehuan'} style={{ fontSize: 24 }} />}/>
        </Tooltip>
      </Upload>
      <Button className={'image-uploader-upload-btn-delete'} icon={<IconFont type={'icon-icon_shanchu'} style={{ fontSize: 24 }} />}
              onClick={() => onImageChange('')}/>
    </Flex>
  )
}

/**
 * 单张图片上传组件(可以从历史创作中选择)
 * @param title 标题(可选)
 * @param image 图片地址
 * @param onImageChange 图片地址改变的回调
 * @param onModelIdChange 模型id改变的回调
 * @param onImageClick 图片点击事件
 * @param render 填充内容
 * @param afterRender 在图片上后添加额外内容
 * @param className 方便外部修改样式
 * @param historyFree 打开历史记录中选择免费图标
 * @param historyCost
 * @param uploadCost
 * @param excludesType
 * @param uploadCharge 打开本地上传收费图标
 * @param setImageSource
 * @param compress 是否压缩图片
 * @param resize
 * @constructor
 */
const ImageUploader : React.FC<UploadImageProps> = ({
                                                      title,
                                                      image,
                                                      onImageChange,
                                                      onModelIdChange,
                                                      onImageClick,
                                                      render,
                                                      afterRender,
                                                      className,
                                                      historyFree = false,
                                                      historyCost,
                                                      uploadCost = 0.4,
                                                      excludesType,
                                                      uploadCharge = false,
                                                      setImageSource,
                                                      resize = undefined,
                                                      compress = undefined,
                                                    }) => {
  const [showHistorySelector, setShowHistorySelector] = useState(false);

  const uploadProps :UploadProps = {
    accept: 'image/png, image/jpeg',
    showUploadList: false,
    action: UPLOAD_URL,
    multiple: false,
    name: 'file',
    onChange(info) { handleFileChange(info, onImageChange); },
    beforeUpload: async (file) => {
      let processedFile: any = file;
      
      if (resize) {
        processedFile = await resizeImage(processedFile, resize);
      }
      
      if (compress) {
        processedFile = await compressFile(processedFile, compress);
      }
      
      // Always convert to JPG at the end
      return await convertToJPG(processedFile);
    }
  };
  const handleFileChange = (info, onImageChange) => {
    const {file} = info;
    // 检查文件是否上传成功
    if (file.status === 'done' && file.response && file.response.success) {
      // 处理上传成功的文件
      onImageChange(file.response.data);
      setImageSource && setImageSource('upload');
    } else if (file.status === 'error') {
      // 处理上传错误的文件
      message.error('上传图片异常，请重试');
    }
  }
  const handleShowHistorySelector = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    setShowHistorySelector(true);
  };

  return (
    <Flex vertical gap={8} className={"image-uploader-main-contain" + (title ? '' : '-fix') + (className ? ' ' + className : '')}>
      {title && <div className={'image-uploader-title'}>{title}</div>}
      {!image &&
        <Dragger className={'image-uploader-dragger'} {...uploadProps}  >
          <Flex vertical justify={'space-between'} gap={8}>
            <Flex vertical gap={8}>
              <Flex gap={16} align={'center'} justify={'center'}>
                <Button type={'primary'} style={{ width: (historyFree || historyCost) ? 276 : 192, height: 38, border: '1px solid #B9C7FA', borderRadius: 9, boxShadow: '0px 0px 0px 0px rgba(0, 0, 0, 0)' }}
                        icon={<IconFont type={'icon-lishirenwu_weixuanzhong1'} style={{ fontSize: 16 }} />}
                        onClick={handleShowHistorySelector}>
                  从历史创作中选择
                  {historyFree &&
                    <>
                      <div className={'text12 font-pf image-upload-free-icon-desc'}>限时免费</div>
                      <img src={FREE_BG} alt={'logo'} width={64} height={20}
                           style={{ position: 'absolute', top: 0, right: 0, zIndex: 1 }} />
                    </>
                  }
                  {historyCost &&
                    <>
                      <div className={'text12 font-pf image-upload-free-icon-desc'}>{historyCost}缪斯点</div>
                      <img src={FREE_BG} alt={'logo'} width={70} height={20}
                           style={{ position: 'absolute', top: 0, right: 0, zIndex: 1 }} />
                    </>
                  }
                </Button>
                <Button style={{ width: uploadCharge ? 212 : 128, height: 38, border: '1px solid #B5C7FF', borderRadius: 9, boxShadow: '0px 0px 0px 0px rgba(0, 0, 0, 0)' }}
                        icon={<IconFont type={'icon-shangchuan'} style={{ fontSize: 16, color: '#366EF4' }} />}>
                  <div className={'color-36 weight'}>本地上传</div>
                  {uploadCharge &&
                    <>
                      <div className={'text12 font-pf image-upload-on-charge'} >{uploadCost}缪斯点</div>
                    </>
                  }
                </Button>
              </Flex>
              <div className="text14 color-96">点击/粘贴/拖拽图片至此，支持png、jpg格式</div>
              <Flex justify={'center'}>
                {render}
              </Flex>
            </Flex>
          </Flex>
        </Dragger>
      }
      {image &&
        <Flex gap={14} justify={'center'} className={'image-uploader-img-container'}>
          <img
            src={image}
            alt={'上传原图'}
            className={'image-uploader-img'}
            onClick={onImageClick}
            style={{ cursor: onImageClick ? 'pointer' : 'default' }}
          />
          {afterRender}
          <ImageUploaderButton onImageChange={onImageChange} setImageSource={setImageSource} resize={resize} compress={compress}/>
        </Flex>
      }
      {showHistorySelector &&
        <ImageSelector value={image? [image] : []} onFinish={(value, modelId) => {
          onImageChange(value && value.length > 0 ? value[0] : '');
          setImageSource && setImageSource('history');
          modelId && onModelIdChange && onModelIdChange(modelId);
          setShowHistorySelector(false);
        }} onCancel={() => setShowHistorySelector(false)} maxChoose={1} excludesType={excludesType}/>
      }
    </Flex>
  )
};
export default ImageUploader;