import { getUserInfo } from '@/utils/utils';
import { FC, useEffect, useState } from 'react';
import { Button, Flex, Input, message, Tag, Tooltip } from 'antd';
import { addImageBadCaseTag, BadCaseTag, queryImageBadCaseTags } from '@/services/ImageCaseController';
import {
  CheckOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { createPromptDict, PromptDictVO } from '@/services/PromptDictController';

const userInfo = getUserInfo();
export const BadCaseTips: FC<{
  imgUrl: any;
  taskId: any;
  id?: any;
  type?: any;
  onRefresh?: () => void;
}> = ({ imgUrl, taskId, id, type, onRefresh }) => {

  const [badCaseTags, setBadCaseTags] = useState<Array<BadCaseTag>>([]);
  const [addNewTag, setAddNewTag] = useState<boolean>(false)
  const [newTagName, setNewTagName] = useState<string | undefined>(undefined)

  useEffect(() => {
    fetchData();
  }, []);

  if (!userInfo || !(userInfo.roleType === 'OPERATOR' || userInfo.roleType === 'ADMIN')) {
    return null;
  }

  const fetchData = () => {
    queryImageBadCaseTags(imgUrl).then(res => {
      if (res) {
        setBadCaseTags(res);
      }
    });
  };

  const addBadCaseTag = (tagId: number, isAdd: boolean) => {
    addImageBadCaseTag(imgUrl, taskId, tagId, isAdd, id, type).then(res => {
      if (res) {
        fetchData();
        onRefresh?.();
      }
    });
  };

  const addTagAndSave = async () => {
    if (!newTagName || newTagName.trim().length < 2){
      message.error('请输入至少2位标签名');
      return;
    }
    const word = newTagName.trim();
    createPromptDict({word, prompt: word, type: 'IMAGE_TAGS', tags: ["system", "system", "badCase"]} as PromptDictVO).then(res => {
      if (res) {
        addBadCaseTag(res, true);
        setAddNewTag(false);
        setNewTagName(undefined);
        fetchData();
      }
    })
  }

  const TagItem: FC<{ item: BadCaseTag }> = ({ item }) => {
    return <Tag className={'pointer'} onClick={() => addBadCaseTag(item.id, !item.checked)}
                color={item.checked ? '#f50' : 'default'}>
      {item.title}
    </Tag>;
  };

  return <Flex align={'center'} justify={'flex-start'} wrap={'wrap'} gap={4}>
    {badCaseTags.map(item => <TagItem key={item.id} item={item} />)}
    {addNewTag && <Flex gap={4}>
      <Input style={{width:100}} placeholder="请输入标签名称" size={'small'}
             value={newTagName} onChange={(e) => setNewTagName(e.target.value)} />
      <Tooltip title={'保存并勾选'}>
        <Button type="primary" shape="circle" size={'small'}
                icon={<CheckOutlined />} onClick={() => addTagAndSave()} />
      </Tooltip>
      <Tooltip title={'取消'}>
        <Button shape="circle" size={'small'} icon={<CloseOutlined />}
                onClick={()=>setAddNewTag(false)} />
      </Tooltip>
    </Flex>}
    {!addNewTag &&
      <Tag className={'pointer'} onClick={() => setAddNewTag(true)}>+新增</Tag>
    }
  </Flex>;
};