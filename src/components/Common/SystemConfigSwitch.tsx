import { queryConfigByKeys } from '@/services/SystemController';
import React, { FC, useEffect, useState } from 'react';
import { Radio } from 'antd';

interface SystemConfigSwitchProps {
  value: string;
  tag?: string;
  keyLabels: LabelValue[];
  onChange: (value: string, tag?: string) => void;
}

export interface LabelValue {
  key: string;
  label: string;
  tag?: string;
}

export const formatPrompt = (prompt) => {
  if (!prompt) return '';

  let newVar = prompt?.replace(/\\n/g, '\n') || '';
  newVar = newVar.replaceAll('\\\'', '\'');
  newVar = newVar.replaceAll('\\\"', '\"');
  return newVar;
};

const SystemConfigSwitch: FC<SystemConfigSwitchProps> = ({ value, tag, keyLabels, onChange }) => {
  const [data, setData] = useState<any>(null);
  const [current, setCurrent] = useState<string | null | undefined>(null);

  if (!keyLabels) {
    return null;
  }

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    let hit: string | null | undefined;
    if (tag) {
      hit = keyLabels.find(item => item.tag === tag)?.key;
    }
    if (!hit && data) {
      Object.keys(data).forEach(key => {
        if (formatPrompt(data[key]) === value) {
          hit = key;
        }
      });
    }
    setCurrent(hit);
  }, [value, tag, data]);

  const fetchData = () => {
    queryConfigByKeys(keyLabels.filter(item => item.key !== 'NONE').map(item => item.key)).then(res => {
      if (!res) return;
      setData(res);
    });
  };

  return <>
    <Radio.Group optionType={'button'} value={current} size={'small'}
                 onChange={e => onChange(formatPrompt(data[e.target.value]), keyLabels.find(item => item.key === e.target.value)?.tag)}>
      {keyLabels.map(e => <Radio key={e.key} value={e.key}>{e.label}</Radio>)}
    </Radio.Group>
  </>;
};

export default SystemConfigSwitch;