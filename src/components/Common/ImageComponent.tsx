import './ImageComponent.less';
import WatermarkImage from '@/components/WatermarkImage';
import IconFont from '@/components/IconFont';
import React, { FC, useEffect, useState } from 'react';
import { Button, Flex, message, Modal, Radio, Tooltip } from 'antd';
import { scoringTestResult, SIMILARITY_TYPE, TestResult, updateExtInfo } from '@/services/TestController';
import { BadCaseTips } from './TagsComponent';
import {
  BarChartOutlined,
  CheckOutlined,
  CloseOutlined,
  CodeOutlined, DownloadOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  LeftOutlined,
  RightOutlined,
} from '@ant-design/icons';
import { getImageName, getTaskIdByImage } from '@/utils/utils';
import {
  fetchBatchClothDetailImage,
  fetchBatchStyleImagesMap,
  fetchMagicPrompt,
  fetchWorkflow,
} from '@/services/CreativeController';
import { downloadJson } from '@/utils/downloadUtils';
import { MemoText } from '@/components/Common/CommonComponent';
import FloatingWindow from '@/components/Common/FloatingWindow';

export const DeletableImage: FC<{
  imgUrl: string,
  needWatermark?: boolean,
  width?: number | string,
  height?: number | string,
  className?: string,
  iconSize?: number,
  onDelete: (imgUrl: string) => void,
  disabled?: boolean,
}> = ({ imgUrl, needWatermark, width, height, className, iconSize, onDelete, disabled = false }) => (
  <div className={'image-component-deletable-container'}
       onClick={e => e.stopPropagation()}>
    <WatermarkImage src={imgUrl} height={height} width={width} className={className} needWatermark={needWatermark} />

    {!disabled && (
      <div className={'image-component-deletable-icon image-selector-round video-delete-round'}
           onClick={() => onDelete(imgUrl)}>
        <IconFont type={'icon-icon_shanchu'} style={{ fontSize: iconSize ? iconSize : 16 }} />
      </div>
    )}
    <div className={'image-component-deletable-icon image-selector-round video-delete-round'}
         onClick={() => onDelete(imgUrl)}>
      <IconFont type={'icon-icon_shanchu'} style={{ fontSize: iconSize ? iconSize : 16 }} />
    </div>
  </div>
);

export const ImageCompareModal: FC<{
  results: Array<Array<TestResult>>,
  styleBatchIds?: Array<number | null>,
  onCancel: () => void,
  onRefresh: () => void,
  showDetail: () => void,
}> = ({ results, styleBatchIds = [], onCancel, onRefresh, showDetail }) => {
  const [current, setCurrent] = useState(0);
  const [localResults, setLocalResults] = useState<Array<Array<TestResult>>>(results);
  const max = results.reduce((acc, cur) => Math.max(acc, cur.length), 0);
  const [prompt, setPrompt] = useState('');
  const [styleImages, setStyleImages] = useState<Array<Map<number, string>>>();
  const [showSimilarityCards, setShowSimilarityCards] = useState<{ [key: number]: boolean }>({ 0: false, 1: false });
  const [displayOrders, setDisplayOrders] = useState<boolean[]>([]);
  const [showDetailTitle, setShowDetailTitle] = useState(false);
  const [clothDetailImage, setClothDetailImage] = useState<string|null>(null);

  // 添加初始化显示顺序的函数
  const initDisplayOrders = (length: number) => {
    const orders = Array(length).fill(false).map(() => Math.random() < 0.5);
    setDisplayOrders(orders);
  };

  // 添加更新数据但保持显示顺序的函数
  const updateResultsKeepOrder = (newResults: Array<Array<TestResult>>) => {
    setLocalResults(newResults);
    // 如果当前轮次超出范围，重置为0
    if (current >= newResults[0]?.length) {
      setCurrent(0);
    }
    // 如果是首次加载或数据长度变化，才重新初始化显示顺序
    if (displayOrders.length !== newResults[0]?.length) {
      initDisplayOrders(newResults[0].length);
    }
  };

  // 处理单选框数据回显
  const getRadioValue = (type: string) => {
    // 获取当前轮次的实验组和对照组结果
    const experimentalResult = localResults[0][current];
    const controlResult = localResults[1][current];

    // 获取当前轮次的实验组和对照组评分
    const experimentalScore = experimentalResult?.extInfo?.[type];
    const controlScore = controlResult?.extInfo?.[type];

    // 根据评分返回单选框的值
    if (experimentalScore === 1 && controlScore === 0) {
      return 'leftGood';
    } else if (experimentalScore === 1 && controlScore === 1) {
      return 'allGood';
    } else if (experimentalScore === 0 && controlScore === 0) {
      return 'allBad';
    } else if (experimentalScore === 0 && controlScore === 1) {
      return 'rightGood';
    }

    // 如果评分不存在，则返回 undefined
    return undefined;
  };

  // 处理相似度评分变化
  const handleSimilarityChange = async (id: number, type: string, value: number) => {
    // 若 id 为空，则不进行评分
    if (!id) {
      message.warning('当前测试结果Id为空，无法进行评分');
      return;
    }
    ;

    // 更新扩展信息
    const res = await updateExtInfo(id, type, value);
    if (!res) {
      message.error('评分失败');
      return;
    }
    ;

    // 更新本地数据状态
    setLocalResults(prev => prev.map(group =>
      group.map(item => {
        if (item.id === id) {
          return {
            ...item,
            extInfo: {
              ...item.extInfo,
              [type]: value,
            },
          };
        }
        return item;
      }),
    ));
  };

  // 处理相似度评分 - 同时处理实验组和对照组
  const handleSimilarityScoreChange = async (type: string, value: string) => {
    // 获取当前轮次的实验组和对照组结果
    const experimentalResult = localResults[0][current];
    const controlResult = localResults[1][current];

    // 如果当前轮次的实验组和对照组结果Id为空，则不进行评分
    if (!experimentalResult?.id || !controlResult?.id) {
      message.warning('当前测试结果Id为空，无法进行评分');
      return;
    }

    // 实验组
    let experimentalScore = 0;
    // 对照组
    let controlScore = 0;


    // 重新校验评分
    if (value === 'leftGood') {
      experimentalScore = 1;
      controlScore = 0;
    } else if (value === 'allGood') {
      experimentalScore = 1;
      controlScore = 1;
    } else if (value === 'allBad') {
      experimentalScore = 0;
      controlScore = 0;
    } else if (value === 'rightGood') {
      experimentalScore = 0;
      controlScore = 1;
    }


    try {
      // 同时更新实验组和对照组的评分
      await Promise.all([
        handleSimilarityChange(experimentalResult.id, type, experimentalScore),
        handleSimilarityChange(controlResult.id, type, controlScore),
      ]);
    } catch (error) {
      message.error('评分失败');
    }
  };

  // 处理服饰相似度评分
  const handleMaterialSimilarityChange = (e) => {
    handleSimilarityScoreChange(SIMILARITY_TYPE.materialSimilarity, e.target.value);
  };

  // 处理模特相似度评分
  const handleFaceSimilarityChange = (e) => {
    handleSimilarityScoreChange(SIMILARITY_TYPE.faceSimilarity, e.target.value);
  };

  // 处理场景相似度评分
  const handleSceneSimilarityChange = (e) => {
    handleSimilarityScoreChange(SIMILARITY_TYPE.sceneSimilarity, e.target.value);
  };

  const handleKeyDown = (event) => {
    // 如果当前焦点在Radio.Group内，不处理键盘事件
    if (event.target.closest('.ant-radio-group')) {
      return;
    }

    switch (event.key) {
      case 'ArrowLeft': {
        setCurrent(preData => preData === 0 ? preData : preData - 1);
        break;
      }
      case 'ArrowRight': {
        setCurrent(preData => preData < max - 1 ? preData + 1 : preData);
        break;
      }
    }
  };

  const handleRadioKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowLeft' || e.key === 'ArrowRight' || e.key === 'ArrowUp' || e.key === 'ArrowDown') {
      e.preventDefault();
    }
  };

  useEffect(() => {
    if (styleBatchIds?.length > 0) {
      initStyleImages(styleBatchIds);
    }

    // 初始化显示顺序
    if (results[0]?.length > 0) {
      initDisplayOrders(results[0].length);
    }

    if(localResults && localResults[0] && localResults[0][0]){
      fetchBatchClothDetailImage(localResults[0][0].batchId).then(res=>{
        if(!res)return;
        setClothDetailImage(res);
      })
    }

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  // 监听 results 变化时使用新的更新函数
  useEffect(() => {
    updateResultsKeepOrder(results);
  }, [results]);

  const initStyleImages = async (styleBatchIds: Array<number | null>) => {
    const result: Array<Map<number, string>> = [];
    for (let i = 0; i < styleBatchIds.length; i++) {
      const e = styleBatchIds[i];
      const res = !e ? undefined : await fetchBatchStyleImagesMap(e);
      if (res) {
        const mapData = new Map(
          Object.entries(res).map(([key, value]) => [parseInt(key, 10), value]),
        );
        result.push(mapData);
      }
    }
    setStyleImages(result);
  };

  const getStyleImage = (idx, url) => {
    const taskId = getTaskIdByImage(url);
    if (!taskId) return '';

    return styleImages && styleImages[idx] ? styleImages[idx]?.get(taskId) : '';
  };

  // 处理评分
  const handleScoring = async (item: TestResult, isDraw: boolean, score: boolean) => {
    const res = await scoringTestResult(item.id, isDraw, score);
    if (!res) return;
    message.success('设置成功');

    // 获取新数据但保持显示顺序
    onRefresh();
  };

  const handleShowPrompt = (path: string) => {
    const imageName = getImageName(path);
    const taskId = getTaskIdByImage(path);
    if (!taskId) {
      message.warning('当前图片为上传图片，无法获取prompt');
      return;
    }

    fetchMagicPrompt(imageName).then((res) => {
      if (res && res.length > 0) {
        let context = res.replace('【negative】', '\n\n【逆向提示词】');
        context = context.replace('【positive】: ', '【正向提示词】: \n服装：\n');
        context = context.replace('\n\n\n', '\n\n场景：\n');
        context = context.replace('\n\n\n', '\n\n模特：\n');
        setPrompt(context);
      } else {
        setPrompt('无prompt 或 获取异常');
      }
    });
  };

  const downloadWorkflow = (path: string) => {
    const taskId = getTaskIdByImage(path);
    if (!taskId) {
      message.warning('解析异常，无法获取workflow');
      return;
    }

    console.log(taskId, path);
    fetchWorkflow(taskId).then((res) => {
      if (res) {
        downloadJson(res);
      }
    });
  };

  const showGroupTitle = (index: number, batchId: number | null) => {
    if (showDetailTitle) {
      return <Flex align={'center'}>
        <EyeInvisibleOutlined style={{ cursor: 'pointer', color: '#1677ff', fontSize: 16 }}
                              onClick={() => setShowDetailTitle(false)} />
        {index === 0 ? '实验组' : '对照组'}<MemoText value={batchId} />
      </Flex>;
    }
    return <Flex align={'center'}>
      <EyeOutlined style={{ cursor: 'pointer', color: '#1677ff', fontSize: 16 }}
                   onClick={() => setShowDetailTitle(true)} />
      **组
    </Flex>;
  };

  return (
    <Modal
      open={true}
      onCancel={onCancel}
      onOk={onCancel}
      centered={true}
      maskClosable={false}
      closable={false}
      width={'100vw'}
      className="image-compare-modal"
      footer={null}>

      {/* 轮次选择器 */}
      <div className="round-selector">
        {localResults[0]?.map((result, index) => (
          <div key={index} className={`round-item ${current === index ? 'active' : ''}`}
               onClick={() => setCurrent(index)} style={{ position: 'relative' }}>
            <div>{index + 1}</div>
            {Number(result?.score) > 0 &&
              <div style={{ position: 'absolute', top: -10, right: -8 }}>
                <CheckOutlined checked={false} style={{ color: 'green' }} />
              </div>
            }
            {Number(result?.score) <= 0 &&
              <div style={{ position: 'absolute', top: -10, right: -8 }}>
                <CloseOutlined checked={false} style={{ color: 'red' }} />
              </div>
            }
            {Number(localResults[1][index]?.score) <= 0 &&
              <div style={{ position: 'absolute', bottom: -10, right: -8 }}>
                <CloseOutlined checked={false} style={{ color: 'red' }} />
              </div>
            }
            {Number(localResults[1][index]?.score) > 0 &&
              <div style={{ position: 'absolute', bottom: -10, right: -8 }}>
                <CheckOutlined checked={false} style={{ color: 'green' }} />
              </div>
            }
          </div>
        ))}
      </div>

      {/* 图片对比区域 */}
      <Flex justify="space-between" gap={24} className="compare-container">
        {/* 左箭头 */}
        <div
          className={`switch-arrow left ${current === 0 ? 'disabled' : ''}`}
          onClick={() => current > 0 && setCurrent(current - 1)}
        >
          <LeftOutlined style={{ fontSize: 24 }} />
        </div>

        <div style={{
          position: 'absolute',
          top: 48,
          left: 'calc(50% - 12px)',
          zIndex: 100,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 12,
        }}>
          <Tooltip title="查看详细信息">
            <EyeOutlined style={{ cursor: 'pointer', color: '#1677ff', fontSize: 24 }} onClick={showDetail} />
          </Tooltip>
          <Tooltip title={showSimilarityCards[0] || showSimilarityCards[1] ? '隐藏相似度评分' : '显示相似度评分'}>
            <div
              onClick={() => {
                const newValue = !(showSimilarityCards[0] || showSimilarityCards[1]);
                setShowSimilarityCards({
                  0: newValue,
                  1: newValue,
                });
              }}
              style={{
                cursor: 'pointer',
                width: 24,
                height: 24,
                borderRadius: '50%',
                background: showSimilarityCards[0] || showSimilarityCards[1] ? '#1677ff' : 'rgba(22, 119, 255, 0.15)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: showSimilarityCards[0] || showSimilarityCards[1] ? '#fff' : '#1677ff',
                fontSize: 16,
                boxShadow: showSimilarityCards[0] || showSimilarityCards[1] ? '0 2px 8px rgba(22, 119, 255, 0.25)' : 'none',
                border: '1px solid rgba(22, 119, 255, 0.25)',
                transition: 'all 0.3s ease',
              }}>
              <BarChartOutlined />
            </div>
          </Tooltip>

          {clothDetailImage &&
            <FloatingWindow title={'服装图片'} showElement={<img alt={'服装图片'} src={clothDetailImage}/>} />
          }

        </div>

        {/* 新的相似度评分卡片 - 居中显示 */}
        <div
          style={{
            position: 'absolute',
            top: showSimilarityCards[0] || showSimilarityCards[1] ? '120px' : '80px',
            left: '50%',
            transform: `translate(-50%, ${showSimilarityCards[0] || showSimilarityCards[1] ? '0' : '-10px'})`,
            zIndex: 100,
            background: 'rgba(255, 255, 255, 0.95)',
            padding: '16px',
            borderRadius: '12px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
            width: 450,
            backdropFilter: 'blur(8px)',
            border: '1px solid rgba(0, 0, 0, 0.06)',
            opacity: showSimilarityCards[0] || showSimilarityCards[1] ? 1 : 0,
            visibility: showSimilarityCards[0] || showSimilarityCards[1] ? 'visible' : 'hidden',
            transition: 'all 0.3s ease',
            maxHeight: 'calc(100vh - 400px)',
            overflowY: 'auto',
          }}>
          <Flex vertical gap={12}>
            <div style={{
              fontSize: '14px',
              fontWeight: 500,
              color: '#1677ff',
              borderBottom: '1px solid #f0f0f0',
              paddingBottom: '8px',
              marginBottom: '4px',
              textAlign: 'center',
              width: '100%',
            }}>
              相似度评分
            </div>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '4px 0',
            }}
                 onKeyDown={e => e.stopPropagation()}>
              <span style={{ fontSize: '13px', color: '#333', flex: '0 0 80px' }}>服饰相似度：</span>
              <Radio.Group
                size="small"
                buttonStyle="solid"
                value={getRadioValue(SIMILARITY_TYPE.materialSimilarity)}
                onChange={handleMaterialSimilarityChange}
              >
                {displayOrders[current] ? (
                  <>
                    <Radio.Button value={'leftGood'} style={{ width: 80, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>左边优秀</Radio.Button>
                    <Radio.Button value={'allGood'} className="same-good-radio"
                                  style={{ width: 50, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>一样高</Radio.Button>
                    <Radio.Button value={'allBad'} className="same-bad-radio"
                                  style={{ width: 50, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>一样低</Radio.Button>
                    <Radio.Button value={'rightGood'} style={{ width: 80, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>右边优秀</Radio.Button>
                  </>
                ) : (
                  <>
                    <Radio.Button value={'leftGood'} style={{ width: 80, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>左边优秀</Radio.Button>
                    <Radio.Button value={'allGood'} className="same-good-radio"
                                  style={{ width: 50, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>一样高</Radio.Button>
                    <Radio.Button value={'allBad'} className="same-bad-radio"
                                  style={{ width: 50, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>一样低</Radio.Button>
                    <Radio.Button value={'rightGood'} style={{ width: 80, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>右边优秀</Radio.Button>
                  </>
                )}
              </Radio.Group>
            </div>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '4px 0',
            }}>
              <span style={{ fontSize: '13px', color: '#333', flex: '0 0 80px' }}>模特相似度：</span>
              <Radio.Group
                size="small"
                buttonStyle="solid"
                value={getRadioValue(SIMILARITY_TYPE.faceSimilarity)}
                onChange={handleFaceSimilarityChange}
              >
                {displayOrders[current] ? (
                  <>
                    <Radio.Button value={'leftGood'} style={{ width: 80, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>左边优秀</Radio.Button>
                    <Radio.Button value={'allGood'} className="same-good-radio"
                                  style={{ width: 50, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>一样高</Radio.Button>
                    <Radio.Button value={'allBad'} className="same-bad-radio"
                                  style={{ width: 50, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>一样低</Radio.Button>
                    <Radio.Button value={'rightGood'} style={{ width: 80, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>右边优秀</Radio.Button>
                  </>
                ) : (
                  <>
                    <Radio.Button value={'leftGood'} style={{ width: 80, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>左边优秀</Radio.Button>
                    <Radio.Button value={'allGood'} className="same-good-radio"
                                  style={{ width: 50, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>一样高</Radio.Button>
                    <Radio.Button value={'allBad'} className="same-bad-radio"
                                  style={{ width: 50, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>一样低</Radio.Button>
                    <Radio.Button value={'rightGood'} style={{ width: 80, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>右边优秀</Radio.Button>
                  </>
                )}
              </Radio.Group>
            </div>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '4px 0',
            }}>
              <span style={{ fontSize: '13px', color: '#333', flex: '0 0 80px' }}>场景相似度：</span>
              <Radio.Group
                size="small"
                buttonStyle="solid"
                value={getRadioValue(SIMILARITY_TYPE.sceneSimilarity)}
                onChange={handleSceneSimilarityChange}
              >
                {displayOrders[current] ? (
                  <>
                    <Radio.Button value={'leftGood'} style={{ width: 80, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>左边优秀</Radio.Button>
                    <Radio.Button value={'allGood'} className="same-good-radio"
                                  style={{ width: 50, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>一样高</Radio.Button>
                    <Radio.Button value={'allBad'} className="same-bad-radio"
                                  style={{ width: 50, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>一样低</Radio.Button>
                    <Radio.Button value={'rightGood'} style={{ width: 80, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>右边优秀</Radio.Button>
                  </>
                ) : (
                  <>
                    <Radio.Button value={'leftGood'} style={{ width: 80, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>左边优秀</Radio.Button>
                    <Radio.Button value={'allGood'} className="same-good-radio"
                                  style={{ width: 50, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>一样高</Radio.Button>
                    <Radio.Button value={'allBad'} className="same-bad-radio"
                                  style={{ width: 50, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>一样低</Radio.Button>
                    <Radio.Button value={'rightGood'} style={{ width: 80, textAlign: 'center', padding: '0 2px' }}
                                  onKeyDown={handleRadioKeyDown}>右边优秀</Radio.Button>
                  </>
                )}
              </Radio.Group>
            </div>
          </Flex>
        </div>

        {/* 平局按钮 */}
        <div onClick={() => handleScoring(localResults[0][current], true, true)}
             className={`draw-button ${(localResults[0][current]?.score === true && localResults[1][current]?.score === true) ? 'active' : ''}`}>
          一样好
        </div>

        <div onClick={() => handleScoring(localResults[0][current], true, false)}
             className={`draw-button draw-button-bad ${(localResults[0][current]?.score === false && localResults[1][current]?.score === false) ? 'active' : ''}`}>
          一样差
        </div>

        {[0, 1].map((groupIndex) => {
          const displayIndex = displayOrders[current] ? (groupIndex === 0 ? 1 : 0) : groupIndex;
          const result = localResults[displayIndex];

          return (
            <div key={groupIndex} className="image-block">
              <div className="image-title">
                <Flex align="center">
                  <span className="title-text">{showGroupTitle(displayIndex, result[current]?.batchId)}</span>
                  <Flex gap={4} align="center" className="icon-group">
                    <IconFont
                      type={result[current]?.score === true ? 'icon-rongqi' : 'icon-xihuan_moren'}
                      className="img-icon-like" />
                    <Tooltip
                      key={`tooltip-${current}-${displayIndex}`}
                      title={<BadCaseTips
                        id={result[current]?.id}
                        type={'testResult'}
                        imgUrl={result[current]?.imageUrl}
                        taskId={result[current]?.taskId}
                        onRefresh={onRefresh}
                      />}
                      trigger={'click'}
                      overlayInnerStyle={{ width: 400 }}
                    >
                      <IconFont
                        type={result[current]?.caseId ? 'icon-buxihuan_dianji' : 'icon-buxihuan_moren'}
                        className="img-icon-like" />
                    </Tooltip>
                  </Flex>
                </Flex>
              </div>

              <div className="image-wrapper">
                <img
                  src={result[current]?.imageUrl}
                  alt={`${displayIndex === 0 ? '实验组' : '对照组'}图片`}
                  className="compare-image"
                  onClick={() => handleScoring(result[current], false, true)} />
                {result[current]?.score !== undefined && (
                  <div
                    className={`score-tag ${Number(result[current]?.score) > 0 ? 'positive' : 'negative'} ${groupIndex === 0 ? 'score-tag-left' : 'score-tag-right'}`}>
                    {Number(result[current]?.score) > 0 ? '优' : '差'}
                  </div>
                )}
                <div style={{
                  position: 'absolute', bottom: getStyleImage(displayIndex, result[current]?.imageUrl) ? 150 : 0,
                  right: 16, gap: 4
                }}>

                  <Tooltip title={"下载工作流"}>
                    <DownloadOutlined style={{ fontSize: 16 }} className={'pointer color-brand'}
                                  onClick={() => downloadWorkflow(result[current]?.imageUrl)} />
                  </Tooltip>

                  <Tooltip title={"查看prompt"}>
                    <CodeOutlined style={{ fontSize: 16 }} className={'pointer color-brand'}
                                  onClick={() => handleShowPrompt(result[current]?.imageUrl)} />
                  </Tooltip>
                </div>
                {getStyleImage(displayIndex, result[current]?.imageUrl) && (
                  <div style={{ position: 'absolute', bottom: 0, right: 0 }}>
                    <img src={getStyleImage(displayIndex, result[current]?.imageUrl)} alt={'style'}
                         style={{ width: 100, height: 'auto' }} />
                  </div>
                )}
              </div>
            </div>
          );
        })}

        {/* 右箭头 */}
        <div
          className={`switch-arrow right ${current >= (localResults[0]?.length - 1) ? 'disabled' : ''}`}
          onClick={() => current < (localResults[0]?.length - 1) && setCurrent(current + 1)}
        >
          <RightOutlined style={{ fontSize: 24 }} />
        </div>
      </Flex>

      {/* 底部操作区 */}
      <div className="modal-footer">
        <Button onClick={onCancel}>关闭</Button>
        <Button type="primary" onClick={onRefresh}>刷新</Button>
      </div>

      {prompt &&
        <Modal
          open={!!prompt}
          onCancel={() => setPrompt('')}
          centered={true}
          width={820}
          footer={null}
          closable={false}
        >
          <div style={{ whiteSpace: 'pre-line', width: 'auto' }}>{prompt}</div>
        </Modal>
      }

    </Modal>
  );
};