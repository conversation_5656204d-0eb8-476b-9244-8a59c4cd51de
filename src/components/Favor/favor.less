.favor {
  &-image-card {
    width: 248px;
    opacity: 1;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    padding: 0;
    z-index: 1;
    gap: 8px;
  }
  &-image-cover {
    position: relative;
    display: flex;
    width: 248px;
    height: 248px;
    border-radius: 8px;
    opacity: 1;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 8px;
    overflow: hidden;

    background: #F5F6F9;

    z-index: 0;
  }

  &-image-count {
    position: absolute;
    right: 12px;
    bottom: 12px;

    height: 29px;
    border-radius: 4px;
    opacity: 1;

    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 6px 12px;
    gap: 8px;

    background: rgba(0, 0, 0, 0.6);

    font-family: "PingFang SC";
    font-size: 12px;
    font-weight: 500;
    line-height: normal;
    text-align: right;
    letter-spacing: 0em;

    font-variation-settings: "opsz" auto;
    color: #FFFFFF;

    z-index: 0;
  }

  &-image-info-block {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  &-image-info {

    width: 100%;
    opacity: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0;
    gap: 4px;

    z-index: 1;

    font-size: 14px;
    font-family: "PingFang SC", serif;
    font-weight: 500;
    color: #1A1B1D;
  }

  &-image-list-container {
    height: calc(70vh);
    width: 100%;
    overflow-y: auto;
    padding: 16px;
    
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(248px, 1fr));
    grid-auto-rows: min-content;
    align-items: start;
    row-gap: 24px;
    column-gap: 16px;
  }

  &-image-list-item {
    position: relative;
    width: 248px;
    max-width: 100%;
    height: auto;
    min-height: 150px;
    justify-self: center;
    opacity: 1;
    display: flex;
    z-index: 0;
    cursor: pointer;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    }
  }
  
  &-image-list-item-water-mark {
    display: flex;
    width: 100%;
    height: 100%;
    object-fit: contain;
    justify-content: flex-start;
    border-radius: 12px;
    overflow: hidden;

    video {
      width: 100%;
      height: auto;
      object-fit: contain;
    }
  }

  &-button {
  }
}
