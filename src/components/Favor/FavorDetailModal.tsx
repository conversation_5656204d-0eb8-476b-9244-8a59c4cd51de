import { Flex, Modal, Spin } from 'antd';
import { FavorImageModel, getFavorImgDetail, queryFavorDetail, UserFavorType } from '@/services/UserFavorController';
import React, { useEffect, useState } from 'react';
import WatermarkImage from '@/components/WatermarkImage';
import '@/components/Favor/favor.less';
import UserFavorButton from '@/components/Favor/UserFavorButton';
import ImgPreview, { ImgPreviewInfo } from '@/components/ImgPreview';
import VideoPreview from '@/components/VideoPreview';
import { CreativeVO, queryCreativeById } from '@/services/CreativeController';
import WatermarkVideo from '@/components/WatermarkVideo';
import { favorManager } from '@/utils/favorManager';

interface FavorImageDetailModalProps {
  modelId: number;
  type: UserFavorType;
  open?: boolean;
  footer?: any;
  onCancel?: any;
  centered?: boolean;
}

const FavorDetailModal: React.FC<FavorImageDetailModalProps> = ({
                                                                  modelId,
                                                                  type,
                                                                  open = false,
                                                                  footer = null,
                                                                  onCancel = () => {
                                                                  },
                                                                  centered = false,
                                                                }) => {

  // 图片预览相关参数
  const [showList, setShowList] = useState<Array<FavorImageModel>>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [showImgPreview, setShowImagePreview] = useState<boolean>(false);
  const [previewImage, setPreviewImage] = useState<string>('');
  const [previewList, setPreviewList] = useState<Array<string>>([]);
  const [previewInfoList, setPreviewInfoList] = useState<ImgPreviewInfo[]>([]);
  const [selectedModel, setSelectedModel] = useState<FavorImageModel>();
  const [previewIdx, setPreviewIdx] = useState<number>();

  // 视频预览相关参数
  const [showVideoPreview, setShowVideoPreview] = useState(false);
  const [selectedBatch, setSelectedBatch] = useState<CreativeVO>();

  const [selectedCreation, setSelectedCreation] = useState<CreativeVO>();


  useEffect(() => {
    if (open && modelId) {
      // 立即清空旧数据，避免显示错误状态
      setShowList([]);
      setLoading(true);
      fetchData();
    }
    return () => {
      setShowList([]);
      setLoading(false);
    }
  }, [modelId, open]);

  async function fetchData() {
    try {
      const res = await queryFavorDetail({ modelId: modelId, type: type });
      if (res && typeof res === 'object' && Object.keys(res).length > 1) {
        setShowList(res.images);
        // 强制刷新收藏状态，确保按钮状态同步
        await favorManager.fetchAllFavors();
      }
    } finally {
      setLoading(false);
    }
  }

  const handlePreview = (item: FavorImageModel, index: number) => {
    if (item.type === 'IMAGE') {
      handleImagePreview(item, index);
    } else {
      handleVideoPreview(item);
    }
  };
  const handleImagePreview = (item: FavorImageModel, index: number) => {
    //走服务端查一下这个创作的最新信息
    queryCreativeById(item?.batchId).then(res => {
      if (res) {
        // 设置创建创作
        // setSelectedCreation(res);

        // 设置图片预览
        setShowImagePreview(true);
        setPreviewImage(item.image);
        setPreviewIdx(index);
        setPreviewList(showList.map((item) => item.image));
        setPreviewInfoList(showList.map((item, index) => (
          {batchId: item.batchId, indexInBatch: item.index })));
        setSelectedModel(item);
      }
    });
  };

  const handleVideoPreview = (item: FavorImageModel) => {
    queryCreativeById(item.batchId).then(res => {
      if (res) {
        setSelectedBatch(res);
        setSelectedModel(item);
        setShowVideoPreview(true);
      }
    });
  };

  return (
    <Modal
      open={open}
      footer={footer}
      onCancel={onCancel}
      centered={centered}
      width={`calc(70vw)`}
      destroyOnClose={true}
      style={{ padding: '0 0' }}
      title={
        <div style={{ fontSize: '18px', fontWeight: 'bold', margin: '8px 0' }}>
          收藏详情
        </div>
      }
    >
      <div className="favor-image-list-container">
        {loading ? (
          <div style={{ 
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: 'calc(70vh - 32px)',
            width: '100%',
            gridColumn: '1 / -1'
          }}>
            <Spin size="large" tip="加载中..." />
          </div>
        ) : (
          showList.length > 0 &&
          showList.map((item, index) => (
            <div key={`favor-image-${index}`} className="favor-image-list-item">
              {item.type === 'IMAGE' &&
                <WatermarkImage
                  className="favor-image-list-item-water-mark"
                  src={item.image}
                  loading="lazy"
                  onClick={() => handlePreview(item, index)}
                />
              }
              {item.type === 'VIDEO' &&
                <div style={{ display: 'flex', width: '100%', height: '100%' }}>
                  <WatermarkVideo
                    className="favor-image-list-item-water-mark"
                    src={item.image}
                    onClick={() => handlePreview(item, index)}
                    controls={false}
                  />
                </div>
              }
              <div className="user-favor-button">
                <UserFavorButton
                  key={`${item.batchId}-${item.index}-${modelId}`}
                  favorType={item.type}
                  itemId={item.batchId}
                  images={[item.index]}
                  imageIndex={item.index}
                />
              </div>
            </div>
          ))
        )}
      </div>

      <ImgPreview
        previewVisible={showImgPreview}
        handleCancelPreview={() => setShowImagePreview(false)}
        previewImage={previewImage}
        previewImgs={previewList}
        previewIdx={previewIdx}
        needSwitch={true}
        // creativeBatch={selectedCreation}
        previewInfoList={previewInfoList}
        showTools
        showBatchInfo
      />
      <VideoPreview src={selectedModel?.image || ''}
                    open={showVideoPreview} destroyOnClose={true}
                    onClose={() => setShowVideoPreview(false)}
                    batch={selectedBatch} showDownload={true}
                    indexInBatch={selectedModel?.index} />
    </Modal>
  );
};

export default FavorDetailModal;