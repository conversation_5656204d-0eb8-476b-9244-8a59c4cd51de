import React, { useState, useCallback, memo } from 'react';
import { Flex, Tooltip } from 'antd';
import IconFont from '../IconFont';
import { UserFavorType } from '@/services/UserFavorController';
import { useFavorStatus } from '@/hooks/useFavorStatus';
import ConfirmModal from '@/components/Common/ConfirmModal';
import '@/components/Favor/favor.less';

interface UserFavorButtonProps {
  favorType: UserFavorType;
  itemId: number;
  className?: string;
  images?: Array<number>;
  imageIndex?: number | undefined;
  style?: React.CSSProperties;
  text?: string;
}

// 使用 memo 包装组件，避免不必要的重新渲染
const UserFavorButton: React.FC<UserFavorButtonProps> = memo(({ 
  favorType, 
  itemId, 
  className = '', 
  images = [], 
  imageIndex = undefined ,
  style = {},
  text = '',
}) => {
    const [showModal, setShowModal] = useState<boolean>(false);
    const { isFavored, loading, addFavor, removeFavor } = useFavorStatus(
      favorType,
      itemId,
      imageIndex,
      images
    );

    // 点击处理函数
    const handleClick = useCallback((event: React.MouseEvent<HTMLElement>) => {
        event.stopPropagation();
        if (isFavored) {
          setShowModal(true);
        } else {
            addFavor();
        }
    }, [isFavored, addFavor, removeFavor]);

    // 确认移除收藏
    const handleConfirmRemove = useCallback(async () => {
        await removeFavor();
        setShowModal(false);
    }, [removeFavor]);
    
    return (
      <Flex className={'favor-button ' + className} onClick={handleClick}>
        <Tooltip title={'收藏'} styles={{body: {}}}>
          <IconFont
            style={{ color: '#FEB432FF', ...style }}
            role={'img'}
            type={isFavored ? 'icon-undoFavor' : 'icon-doFavor'}
          />
        </Tooltip>
        {text && <div className={'text16 font-pf color-1a'}>{text}</div>}
        <ConfirmModal
          title="取消收藏"
          content={<div style={{display: 'flex', width: '100%', justifyContent: 'center'}}>确认取消收藏</div>}
          centered
          okType={'danger'}
          confirmLoading={loading}
          onOk={handleConfirmRemove}
          onCancel={() => {setShowModal(false)}}
          open={showModal}
        />
      </Flex>
    );
});

export default UserFavorButton;