import { FavorCreationModel } from '@/services/UserFavorController';
import { Flex } from 'antd';
import WatermarkImage from '@/components/WatermarkImage';
import React from 'react';
import '@/components/Favor/favor.less';
import WatermarkVideo from '@/components/WatermarkVideo';
import { PlayCircleFilled } from '@ant-design/icons';

interface ImageCardBlockProps {
  item: FavorCreationModel;
  onclick?: any;
}
const ImageCardBlock: React.FC<ImageCardBlockProps> = ({item, onclick = () => {}}) => {
  return (
    <Flex vertical className="favor-image-card" onClick={onclick}>
      <div className="favor-image-cover">
        <WatermarkImage src={item.showImage} height={'100%'} needWatermark />
        {item.type === 'VIDEO' &&
          <Flex gap={8} style={{ position: 'absolute', top: 0, left: 0, width: ('100%'), height: '100%' }}
                justify={'center'} align={'center'}>
            <PlayCircleFilled style={{ fontSize: 46, color: '#FFFFFF' }} />
          </Flex>
        }
        <div className="favor-image-count">
          共{item.imageCount}{item.type === 'VIDEO' ? '个视频' : '张'}
        </div>
      </div>
      <div className="favor-image-info-block">
        <div className="favor-image-info">
          服装名称: {item.modelName ? item.modelName : <span style={{ color: '#999' }}>服装已删除</span>}
        </div>
      </div>
    </Flex>
  )
}
export default ImageCardBlock;