import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, Flex, Typography, Table, Tooltip, message, Empty } from 'antd';
import './CorpConfirmModal.less';
import { getUserInfo } from '@/utils/utils';
import { qiChaChaFuzzyQuery, QiChaChaModelVO, updateCorpAuthInfo } from '@/services/OrganizationController';
import { SearchOutlined } from '@ant-design/icons';

interface CorpConfirmModalProps {
  open: boolean;
  onCancel: () => void;
  onConfirm: (corpName: string) => void;
}

interface CorpData extends QiChaChaModelVO {
  key: string;
}

interface QiChaChaResponse {
  pageIndex: number;
  pageSize: number;
  total: number;
  data: QiChaChaModelVO[];
}

const CorpConfirmModal: React.FC<CorpConfirmModalProps> = ({ open, onCancel, onConfirm }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [corpInfo, setCorpInfo] = useState<{ name: string | undefined; creatorCorpOrgId?: number; extInfo: QiChaChaModelVO | null }>({
    name: undefined,
    creatorCorpOrgId: undefined,
    extInfo: null
  });
  const [tableData, setTableData] = useState<CorpData[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [modalWidth, setModalWidth] = useState(800);
  const userInfo = getUserInfo();

  // 检测当前设备是否是移动设备
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [isSmallMobile, setIsSmallMobile] = useState(window.innerWidth <= 480);

  // 监听窗口大小变化，调整弹窗宽度和设备类型
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      if (width <= 768) {
        setModalWidth(Math.min(width * 0.95, 800));
        setIsMobile(true);
        setIsSmallMobile(width <= 480);
      } else {
        setModalWidth(800);
        setIsMobile(false);
        setIsSmallMobile(false);
      }
    };

    handleResize(); // 初始加载时执行一次
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 初始化
  useEffect(() => {
    if (open) {
      const creatorCorpName = userInfo?.corpName;
      const creatorCorpOrgId = userInfo?.corpOrgId;

      if (creatorCorpOrgId) {
        //设置进入创建者企业
        setCorpInfo({
          name: creatorCorpName,
          creatorCorpOrgId: creatorCorpOrgId,
          extInfo: null
        });
      }

      if (creatorCorpName) {
        form.setFieldsValue({ corpName: creatorCorpName });
        handleSearch();
      }
    }
  }, [open]);

  // 查询企业信息
  const handleSearch = async (pageIndex?: number) => {
    const corpName = form.getFieldValue('corpName') || '';
    if (!corpName) {
      message.warning('请输入完整的企业名称');
      return;
    }
    
    setSearchLoading(true);
    try {
      const currentPage = pageIndex || pagination.current;
      const response = await qiChaChaFuzzyQuery(corpName, currentPage);
      if (response) {
        if (response.data.length === 0) {
          message.warning('未查询到相关企业信息');
          setTableData([]);
          setPagination(prev => ({ ...prev, total: 0 }));
          return;
        }
        const formattedData = response.data.map((item: QiChaChaModelVO) => ({
          ...item,
          key: item.no
        }));
        setTableData(formattedData);
        setPagination(prev => ({ ...prev, total: response.total }));
      }
    } catch (error) {
      console.error('查询企业信息失败:', error);
      message.error('查询企业信息失败');
    } finally {
      setSearchLoading(false);
    }
  };

  // 确认企业信息
  const handleConfirm = () => {
    if (corpInfo.creatorCorpOrgId && corpInfo.extInfo) {
      setLoading(true);
      updateCorpAuthInfo(corpInfo.creatorCorpOrgId, userInfo?.id, corpInfo.extInfo).then(res => {
        if (res) {
          message.success('企业信息确认成功');
          onConfirm(form.getFieldValue('corpName'));
        }
      }).finally(() => {
        setLoading(false);
      });
    }
  };

  // 根据设备类型获取表格列配置
  const getColumns = () => {
    if (isSmallMobile) {
      // 极小屏幕只显示企业名称和操作按钮
      return [
        {
          width: '75%',
          title: '企业名称',
          dataIndex: 'name',
          key: 'name',
          ellipsis: true,
          render: (text) => (
            <Tooltip title={text}>
              <span className="corp-name-cell">{text}</span>
            </Tooltip>
          ),
        },
        {
          width: '25%',
          title: '操作',
          align: 'center' as const,
          key: 'action',
          render: (_, record) => (
            <Button 
              type="link" 
              size="small"
              className="select-button"
              onClick={() => {
                if (selectedRowKeys.includes(record.key)) {
                  form.setFieldsValue({ corpName: '' });
                  setCorpInfo({
                    name: '',
                    creatorCorpOrgId: corpInfo.creatorCorpOrgId,
                    extInfo: null
                  });
                  setSelectedRowKeys([]);
                } else {
                  form.setFieldsValue({ corpName: record.name });
                  setCorpInfo({
                    name: record.name,
                    creatorCorpOrgId: corpInfo.creatorCorpOrgId,
                    extInfo: record
                  });
                  setSelectedRowKeys([record.key]);
                }
              }}
            >
              {selectedRowKeys.includes(record.key) ? '取消' : '选择'}
            </Button>
          ),
        },
      ];
    } else if (isMobile) {
      // 普通移动设备显示企业名称、操作按钮，减少注册地址显示空间
      return [
        {
          width: '60%',
          title: '企业名称',
          dataIndex: 'name',
          key: 'name',
          ellipsis: true,
          render: (text) => (
            <Tooltip title={text}>
              <span className="corp-name-cell">{text}</span>
            </Tooltip>
          ),
        },
        {
          width: '35%',
          title: '注册地',
          dataIndex: 'address',
          key: 'address',
          ellipsis: true,
          render: (text) => (
            <Tooltip title={text}>
              <span>{text}</span>
            </Tooltip>
          ),
        },
        {
          width: '15%',
          title: '操作',
          align: 'center' as const,
          key: 'action',
          render: (_, record) => (
            <Button 
              type="link" 
              size={isSmallMobile ? "small" : undefined}
              className="select-button"
              onClick={() => {
                if (selectedRowKeys.includes(record.key)) {
                  form.setFieldsValue({ corpName: '' });
                  setCorpInfo({
                    name: '',
                    creatorCorpOrgId: corpInfo.creatorCorpOrgId,
                    extInfo: null
                  });
                  setSelectedRowKeys([]);
                } else {
                  form.setFieldsValue({ corpName: record.name });
                  setCorpInfo({
                    name: record.name,
                    creatorCorpOrgId: corpInfo.creatorCorpOrgId,
                    extInfo: record
                  });
                  setSelectedRowKeys([record.key]);
                }
              }}
            >
              {selectedRowKeys.includes(record.key) ? '取消' : '选择'}
            </Button>
          ),
        },
      ];
    } else {
      // 桌面设备显示完整信息
      return [
        {
          width: '35%',
          title: '企业名称',
          dataIndex: 'name',
          key: 'name',
          ellipsis: true,
          render: (text) => (
            <Tooltip title={text}>
              <span className="corp-name-cell">{text}</span>
            </Tooltip>
          ),
        },
        {
          width: '50%',
          title: '注册地',
          dataIndex: 'address',
          key: 'address',
          ellipsis: true,
          render: (text) => (
            <Tooltip title={text}>
              <span>{text}</span>
            </Tooltip>
          ),
        },
        {
          width: '15%',
          title: '操作',
          align: 'center' as const,
          key: 'action',
          render: (_, record) => (
            <Button 
              type="link" 
              className="select-button"
              onClick={() => {
                if (selectedRowKeys.includes(record.key)) {
                  form.setFieldsValue({ corpName: '' });
                  setCorpInfo({
                    name: '',
                    creatorCorpOrgId: corpInfo.creatorCorpOrgId,
                    extInfo: null
                  });
                  setSelectedRowKeys([]);
                } else {
                  form.setFieldsValue({ corpName: record.name });
                  setCorpInfo({
                    name: record.name,
                    creatorCorpOrgId: corpInfo.creatorCorpOrgId,
                    extInfo: record
                  });
                  setSelectedRowKeys([record.key]);
                }
              }}
            >
              {selectedRowKeys.includes(record.key) ? '取消' : '选择'}
            </Button>
          ),
        },
      ];
    }
  };

  // 根据设备类型获取表格滚动区域高度
  const getTableScroll = () => {
    if (isSmallMobile) {
      return { y: 280 };
    } else if (isMobile) {
      return { y: 320 };
    }
    return { y: 420 };
  };

  return (
    <Modal
      title="企业信息确认"
      open={open}
      onCancel={onCancel}
      maskClosable={false}
      width={modalWidth}
      footer={[
        <Button
          key="confirm"
          type="primary"
          onClick={handleConfirm}
          style={{ width: '100%', height: isMobile ? '36px' : '40px', fontSize: isMobile ? '14px' : '16px' }}
          disabled={!corpInfo.name || !corpInfo.extInfo}
          loading={loading}
        >
          确认企业信息
        </Button>
      ]}
      centered
      className="corp-confirm-modal"
    >
      <Form form={form} layout="vertical" className="compact-form">
        <Flex gap={4} className="corp-form-container">
          <Form.Item
            name="corpName"
            style={{ flex: 1, width: isMobile ? '100%' : '80%', marginBottom: isMobile ? '8px' : '12px' }}
            rules={[{ required: true, message: '请输入企业完整名称' }]}
          >
            <Input 
              placeholder="请输入企业完整名称" 
              size={isMobile ? "middle" : "large"}
              suffix={
                <SearchOutlined 
                  className="search-icon" 
                  onClick={() => handleSearch()}
                  style={{ cursor: 'pointer', color: '#1890ff' }}
                />
              }
              onPressEnter={() => handleSearch()}
            />
          </Form.Item>
          {!isMobile && (
            <Button 
              type="primary" 
              loading={searchLoading}
              onClick={() => handleSearch()} 
              style={{ width: '20%' }}
            >
              查询
            </Button>
          )}
        </Flex>
        
        {isMobile && (
          <Button 
            type="primary" 
            loading={searchLoading}
            onClick={() => handleSearch()} 
            style={{ width: '100%', marginBottom: '8px', height: '32px' }}
          >
            查询企业信息
          </Button>
        )}
        
        <Typography.Text type="secondary" className="search-tip">
          提示：如未查询到企业信息，请确认企业名称是否完整准确
        </Typography.Text>
        
        <div className="table-container">
          <Table
            columns={getColumns()}
            dataSource={tableData}
            loading={searchLoading}
            locale={{
              emptyText: <Empty 
                image={Empty.PRESENTED_IMAGE_SIMPLE} 
                description={searchLoading ? "正在搜索..." : "暂无数据"} 
                className="empty-content"
              />
            }}
            pagination={{
              ...pagination,
              showSizeChanger: false,
              showQuickJumper: !isSmallMobile,
              showTotal: (total) => `共 ${total} 条`,
              size: isSmallMobile ? "small" : undefined,
              onChange: (page) => {
                setPagination(prev => ({ ...prev, current: page }));
                handleSearch(page);
              },
            }}
            className="corp-table"
            size={isMobile ? "small" : "middle"}
            scroll={getTableScroll()}
            rowSelection={{
              type: 'radio',
              selectedRowKeys,
              columnWidth: isSmallMobile ? 24 : 32,
              onChange: (selectedRowKeys) => {
                setSelectedRowKeys(selectedRowKeys);
                const selectedRecord = tableData.find(item => item.key === selectedRowKeys[0]);
                if (selectedRecord) {
                  form.setFieldsValue({ corpName: selectedRecord.name });
                  setCorpInfo({
                    name: selectedRecord.name,
                    creatorCorpOrgId: corpInfo.creatorCorpOrgId,
                    extInfo: selectedRecord
                  });
                }
              }
            }}
          />
        </div>
      </Form>
    </Modal>
  );
};

export default CorpConfirmModal; 