import '../../pages/Home/index.less';
import { <PERSON><PERSON>, Flex, Modal } from 'antd';
import { EyeOutlined, PlayCircleFilled } from '@ant-design/icons';
import IconFont from '@/components/IconFont';
import React, { FC, useState } from 'react';
import { recordClickBtnTrack, recordMouseHoverTrack } from '@/utils/trackingUtils';
import { TRACK_EVENT_BTN, TRACK_EVENT_MOUSE_HOVER } from '@/constants/TrackingConstants';
import { startMouseDuration, stopMouseDuration } from '@/utils/MouseDurationUtils';

interface ShowCaseCardProps {
  data: any;
  gotoCreateSame: (data) => void;
  index: number;
}

export const convertToList = (value) => {
  if (!value) return [];
  return value.split(',');
};

const ShowCaseCard: FC<ShowCaseCardProps> = ({ data, gotoCreateSame, index }) => {
  const [showItem, setShowItem] = useState<any>(null);

  const getAllTags = (clothCollocation) => {
    const tags: Array<string> = [...convertToList(clothCollocation.shoe)];
    tags.push(...convertToList(clothCollocation.tops));
    tags.push(...convertToList(clothCollocation.bottoms));
    tags.push(...convertToList(clothCollocation.others));
    tags.push(...convertToList(clothCollocation.props));
    return tags;
  };

  // 点击查看搭配
  const clickCheckMatch = (data: object) => {
    // 上报埋点信息
    recordClickBtnTrack(TRACK_EVENT_BTN.CLICK_CHECK_MATCH_BTN.label, data);

    //  设置具体信息
    setShowItem(data);
  };

  const handleMouseEnter = (event: React.MouseEvent<HTMLVideoElement>) => {
    const video = event.currentTarget;
    video.play();
  };

  const handleMouseLeave = (event: React.MouseEvent<HTMLVideoElement>) => {
    const video = event.currentTarget;
    video.pause();
    video.currentTime = 0; // 恢复到视频开头
  };

  const MiniImgTitleCard = ({ title, url }) => {
    return <Flex vertical align={'center'} justify={'center'} className={'home-mini-img-title'} gap={2}>
      <img loading={'lazy'} src={url} alt={'img'} width={40} height={40} style={{ borderRadius: 4 }} />
      <div className={'text14 color-3d font-pf'}>{title}</div>
    </Flex>;
  };

  const VideoCard = ({ data }) => <div className={'show-case-card-video'}>
      <video className={'home-show-case-width home-show-case-height pointer'}
             controls={false} loop={true} muted
             onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}
             style={{ borderRadius: 8 }}>
        <source src={data.mainUrl} type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      <div style={{  }}
           className={'show-case-card-video-div'}>
        <PlayCircleFilled style={{ fontSize: 46, color: '#FFFFFF' }} />
      </div>
    </div>
  ;

  const ShowCaseDetailImg = ({ url, tag }) => {
    return <div style={{ position: 'relative', borderRadius: 8, overflow: 'hidden' }}>
      <img loading={'lazy'} alt={'img'} width={356} src={url} />
      <div className={'home-tag text16 weight color-w'}>{tag}</div>
    </div>;
  };

  const ShowCaseDetailElement = ({ title, data }) => {
    return <Flex vertical className={'home-show-case-element text16 weight'} gap={8}>
      <Flex align={'center'} justify={'center'} className={'color-1a'}>{title}</Flex>
      <img alt={'img'} width={168} height={168} src={data.url} style={{ borderRadius: 8 }} />
      <Flex align={'center'} justify={'center'} className={'color-72'}>{data.name}</Flex>
    </Flex>;
  };

  return <div style={{ position: 'relative' }} className={'home-show-case-width'}>
    {data.type === 'IMAGE' &&
      <div className={'home-show-case-width home-show-case-height home-upscale-block'} style={{ overflow: 'hidden' }}
           onClick={() => clickCheckMatch(data)}
           onMouseEnter={() => startMouseDuration()}
           onMouseLeave={() => stopMouseDuration(TRACK_EVENT_MOUSE_HOVER.PICTURE_CASE_HOVER.label, data)}>
        <img loading={'lazy'} className={'home-show-case-width home-show-case-height pointer'} alt={'show-case'}
             src={data.showImage} style={{ borderRadius: 16 }} />
        <Flex align={'center'} justify={'center'} className={'home-show-case-mask pointer'} gap={2}>
          <EyeOutlined style={{ fontSize: 16, color: '#FFFFFF' }} />
          <div className={'text16 weight color-w'}>点击查看搭配</div>
        </Flex>
      </div>
    }

    {data.type === 'VIDEO' &&
      <div className={'home-show-case-width home-show-case-height'}
           style={{ overflow: 'hidden', borderRadius: 16 }}
           onMouseEnter={() => startMouseDuration()}
           onMouseLeave={() => stopMouseDuration(TRACK_EVENT_MOUSE_HOVER.VIDEO_CASE_HOVER.label, data)}>
        <VideoCard data={data} />
      </div>
    }

    <div className={'home-show-case-config-bg'} />

    <Flex align={'center'} justify={'center'}
          style={{
            position: 'absolute', top: 12, left: 12, width: 40, height: 30, borderRadius: 4,
            background: data.type === 'IMAGE' ? '#FFE3C8' : '#E0DAFD',
          }}>
      <div className={'text16 weight'} style={{ color: data.type === 'IMAGE' ? '#FF7011' : '#6636F4' }}>
        {data.type === 'IMAGE' ? '图片' : '视频'}
      </div>
    </Flex>

    <Flex style={{ position: 'absolute', bottom: 8, left: 8 }} align={'center'}>
      <MiniImgTitleCard title={'商品'} url={data.modelMiniUrl} />

      <IconFont type={'icon-jiahaodaibeiban'} style={{ fontSize: 16, margin: '0 -4px', zIndex: 2 }}
                className={'color-1a'} />

      <MiniImgTitleCard title={'场景'} url={data.scene ? data.scene.url : ''} />

      <IconFont type={'icon-jiahaodaibeiban'} style={{ fontSize: 16, margin: '0 -4px', zIndex: 2 }}
                className={'color-1a'} />

      <MiniImgTitleCard title={'模特'} url={data.face ? data.face.url: ''} />
    </Flex>

    {data.type === 'IMAGE' &&
      <Flex gap={8} align={'center'} justify={'center'} className={'home-creative-same-btn pointer'}
            onClick={() => gotoCreateSame(data)}>
        <div className={'text16 weight color-w'}>创作同款</div>
        <div className={'home-creative-same-btn-icon-bg'}>
          <IconFont type={'icon-cujiantou_1'} style={{ fontSize: 22, zIndex: 3 }} />
        </div>
      </Flex>
    }

    {showItem &&
      <Modal open={true} footer={null} onCancel={() => setShowItem(null)} width={1184} closable={false} maskClosable
             centered>
        <Flex vertical gap={16} style={{ position: 'relative' }}>
          <div style={{ position: 'absolute', right: -64, top: -24 }} onClick={() => setShowItem(null)}>
            <IconFont type={'icon-lujing'} style={{ fontSize: 24 }} className={'color-w pointer'} />
          </div>

          <Flex gap={16}>
            <Flex gap={8}>
              <ShowCaseDetailImg url={showItem.modelUrl} tag={'服装示意图'} />
              <ShowCaseDetailImg url={showItem.mainUrl} tag={'AI生成图'} />
            </Flex>

            <Flex vertical gap={16} style={{ width: 400 }}>
              <Flex align={'center'} justify={'space-between'}>
                <ShowCaseDetailElement title={'场景'} data={showItem.scene} />

                <IconFont type={'icon-jiahaodaibeiban'} style={{ fontSize: 16 }} className={'color-1a'} />

                <ShowCaseDetailElement title={'模特'} data={showItem.face} />
              </Flex>

              <Flex vertical align={'center'} justify={'flex-start'} className={'home-show-case-collocation'} gap={8}>
                <Flex align={'center'} justify={'center'} className={'text16 weight color-1a'}>搭配</Flex>
                <Flex align={'flex-start'} justify={'flex-start'} wrap={'wrap'} gap={8} className={'width-100'}>
                  {getAllTags(showItem.clothCollocation).map((item) =>
                    <div key={item} className={'home-show-case-collocation-tag text16 weight color-72'}>
                      {item}
                    </div>)}
                </Flex>
              </Flex>
            </Flex>
          </Flex>

          <Flex align={'center'} justify={'center'}>
            <Button className={'home-show-case-btn'} onClick={() => gotoCreateSame(data)}>
              <div className={'text16 weight color-w'}>创作同款</div>
              <IconFont type={'icon-cujiantou_1'} style={{ fontSize: 24 }} />
            </Button>
          </Flex>
        </Flex>
      </Modal>
    }
  </div>;
};

export default ShowCaseCard;