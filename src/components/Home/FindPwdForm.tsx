import '@/pages/Login/index.less';
import React, { FC, useEffect, useState } from 'react';
import { ProFormCaptcha } from '@ant-design/pro-form';
import { Button, Flex, Form, Input, Modal } from 'antd';
import { PASSWORD_REGEX, PHONE_REGEX } from '@/constants';
import { fetchSalt, restPassword, sendCaptcha, verifyCaptcha } from '@/services/LoginController';
import cryptoStr from '@/utils/cryptojs';

interface FindPwdModalProps {
  onCancel: () => void;
}

function isCanVerify(mobile: string, code: string) {
  return PHONE_REGEX.test(mobile) && code.length === 4;
}

function isCanCommit(mobile: string, pwd: string) {
  return PHONE_REGEX.test(mobile) && PASSWORD_REGEX.test(pwd);
}

const FindPwdForm: FC<FindPwdModalProps> = ({ onCancel }) => {
  const [canVerify, setCanVerify] = useState(false);
  const [canCommit, setCanCommit] = useState(false);
  const [mobile, setMobile] = useState<string>('');
  const [verifiedCode, setVerifiedCode] = useState(false);
  const [mobileError, setMobileError] = useState(false);
  const [pwd, setPwd] = useState('');
  const [code, setCode] = useState('');
  const [salt, setSalt] = useState('');
  const [showDialog, setShowDialog] = useState(false);

  const handleMobileChange = (e: { target: { value: any; }; }) => {
    const mobile = e.target.value;
    setMobile(mobile);

    if (mobile && !PHONE_REGEX.test(mobile)) {
      setMobileError(true);
      return;
    }

    setMobileError(false);
  };

  const handleCodeChange = (e) => {
    let code = e.target.value;
    setCode(code);
  };

  const handleGetCaptcha = () => {
    if (!mobile || !PHONE_REGEX.test(mobile)) {
      return;
    }

    // @ts-ignore
    sendCaptcha(mobile).then((res) => {
      console.log(mobile, '发送验证码', res ? '成功' : '失败');
    });
  };

  const handlePwdChange = (e) => {
    setPwd(e.target.value);
  };

  const handleVerify = () => {
    if (!isCanVerify(mobile, code)) {
      return;
    }
    verifyCaptcha(mobile, code).then((res) => {
      if (!res) {
        return;
      }
      setVerifiedCode(true);

      fetchSalt().then((res) => {
        if (!res) {
          return;
        }
        setSalt(res);
      });
    });
  };

  const handleCommit = () => {
    if (!isCanCommit(mobile, pwd)) {
      return;
    }

    const password = cryptoStr.aesEncrypt(pwd, salt);
    restPassword(password).then((r) => {
      if (!r) {
        return;
      }

      setShowDialog(true);
    });
  };

  //监听输入变化，设置是否能提交状态
  useEffect(() => {
    setCanVerify(isCanVerify(mobile, code));
    setCanCommit(isCanCommit(mobile, pwd));
  }, [mobile, code, pwd]);

  return (<Form>

    <Flex vertical align={'center'} justify={'flex-start'} style={{ padding: '48px 0 58px 0' }} gap={12}>
      <div className={'row-center-block'}>
        {!verifiedCode &&
          <div className={'text24 weight font-pf color-1a'}>忘记密码</div>
        }
        {verifiedCode &&
          <div className={'text24 weight font-pf color-1a'}>设置新密码</div>
        }
      </div>
      <div className={'row-center-block margin-top-12'}>
        {!verifiedCode &&
          <Input onChange={handleMobileChange} addonBefore="+86" size={'large'}
                 status={mobileError ? 'error' : ''} allowClear showCount
                 placeholder={'请输入注册手机号码'} maxLength={11} className={'login-input'} />
        }
        {verifiedCode &&
          <div className={'text16 font-pf color-26'} style={{
            marginLeft: '-8px', marginRight: '-8px',
          }}>请设置8-16位新密码，须为数字与字母组合</div>
        }
      </div>
      <Flex vertical align={'center'} justify={'center'} style={{ width: 304, marginBottom: -24 }}>
        {!verifiedCode &&
          <ProFormCaptcha
            className={'captcha-input'}
            allowClear
            fieldProps={{ size: 'large', maxLength: 4 }}
            captchaProps={{ size: 'large', disabled: !PHONE_REGEX.test(mobile) }}
            // 手机号的 name，onGetCaptcha 会注入这个值
            phoneName="phone"
            name="captcha"
            rules={[{ required: true, message: '请输入验证码' }]}
            placeholder="请输入验证码"
            onChange={handleCodeChange}
            onGetCaptcha={async () => handleGetCaptcha()}
          />
        }
        {verifiedCode &&
          <Input.Password placeholder="设置新密码" size={'large'} onChange={handlePwdChange}
                          className={'margin-bottom-24'} />
        }

      </Flex>
      <div className={'row-center-block'}>
        {!verifiedCode &&
          <Button className={'login-btn'} type="text" disabled={!canVerify}
                  onClick={() => handleVerify()}>
            <div className={'text16 font-pf weight color-w'}>下一步</div>
          </Button>
        }
        {verifiedCode &&
          <Button className={'login-btn'} type="text" disabled={!canCommit} onClick={handleCommit}>
            <div className={'text16 font-pf weight color-w'}>确认并提交</div>
          </Button>
        }
      </div>
      <div className={'row-center-block'}>
        <div className={'text16 font-pf color-2d margin-left-12'} style={{ cursor: 'pointer' }} onClick={onCancel}>
          返回登录
        </div>
      </div>

    </Flex>

    <Modal title="" open={showDialog} centered mask={true} closable={false}
           footer={null} width={444}>
      <div className={'dialog-block'}>
        <p className={'text24 font-pf weight color-26'}>设置成功</p>
        <p className={'text16 font-pf color-26'}>新密码设置成功，请返回使用新密码重新登录</p>
        <div className={'dialog-operate'}>
          <div className={'text16 font-pf color-2d'} onClick={onCancel}>返回登录</div>
        </div>
      </div>

    </Modal>
  </Form>);
};

export default FindPwdForm;
