import '@/pages/Login/index.less';
import { Button, Flex, Form, Input, notification, Tooltip, Space } from 'antd';
import { ProFormCaptcha } from '@ant-design/pro-form';
import { PHONE_REGEX, REGISTER_INVITE_CODE, USER_INFO } from '@/constants';
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { fetchSalt, sendCaptcha, register } from '@/services/LoginController';
import { ActionType } from '@/components/Home/LoginModal';
import { recordClickBtnTrack, recordLeavePageTrack } from '@/utils/trackingUtils';
import { TRACK_EVENT_BTN, TRACK_EVENT_PAGE } from '@/constants/TrackingConstants';
import CorpConfirmModal from './CorpConfirmModal';
import './RegisterForm.less';
import { getUserInfo } from '@/utils/utils';
import { QuestionCircleOutlined } from '@ant-design/icons';

// 注册表单组件的props
interface RegisterFormProps {
  changeAction: (action: ActionType) => void;
  showUnregisteredTip?: boolean;
}

// 判断是否可以提交
function isCanCommit(mobile: string | undefined, code: string | undefined, corpName: string | undefined): boolean {
  return Boolean(mobile &&
    PHONE_REGEX.test(mobile) &&
    code &&
    code.length === 4 &&
    corpName &&
    corpName !== '');
}

// 注册表单组件
const RegisterForm: React.FC<RegisterFormProps> = ({ changeAction, showUnregisteredTip = false }) => {
  const [form] = Form.useForm();
  const [salt, setSalt] = useState('');
  const [canCommit, setCanCommit] = useState(false);
  const [showCorpConfirm, setShowCorpConfirm] = useState(false);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const userInfo = getUserInfo();
  const location = useLocation();

  // 监听表单值变化
  const mobile = Form.useWatch('mobile', form);
  const code = Form.useWatch('code', form);
  const corpName = Form.useWatch('corpName', form);

  // 邀请码
  const [inviteCode, setInviteCode] = useState<string|null>();

  // 监听表单值变化，设置是否能提交状态
  useEffect(() => {
    setCanCommit(isCanCommit(mobile, code, corpName));
  }, [mobile, code, corpName]);

  // 获取验证码
  const handleGetCaptcha = async (mobile: string) => {
    if (!mobile || !PHONE_REGEX.test(mobile)) {
      return;
    }

    // 记录点击获取验证码按钮
    recordClickBtnTrack(TRACK_EVENT_BTN.GET_SMS_BTN.label, { mobile: mobile });
    sendCaptcha(mobile);
  };

  // 执行注册
  const handleRegister = async (values: any) => {
    const { mobile, code, corpName, nickName } = values;

    // 记录点击注册按钮
    recordClickBtnTrack(TRACK_EVENT_BTN.REGISTER_BTN.label, {
      mobile: mobile,
      salt: salt,
    });

    setLoading(true);
    try {
      // 执行注册操作，若成功则提示注册成功，否则提示注册失败，并且添加至 localStorage
      const result = await register(mobile, code, corpName, nickName, inviteCode);
      if (result) {
        notification.success({ message: '注册成功' });
        // 注册成功
        localStorage.setItem(USER_INFO, JSON.stringify(result));
        // 显示企业信息确认弹窗
        setShowCorpConfirm(true);

        //清除邀请码
        setInviteCode(null);
        sessionStorage.removeItem(REGISTER_INVITE_CODE);

      } else {
        notification.error({ message: '注册失败' });
      }
    } finally {
      setLoading(false);
    }
  };

  // 处理企业信息确认
  const handleCorpConfirm = (corpName: string) => {
    setTimeout(() => {
      let path = '/upload';
      if (userInfo?.roleType === 'ADMIN') {
        path = '/cloth-mng';
      } else if (userInfo?.roleType === 'DISTRIBUTOR') {
        path = '/distributor/customers';
      } else if (!userInfo?.imagePoint || userInfo?.imagePoint <= 0 || !userInfo?.lastLoginTime) {
        path = '/experience';
      } else if (sessionStorage.getItem('preset.face')) {
        path = '/creation';
      }

      // 上报埋点信息(登录成功后的数据需要进行 userId 更新处理)
      recordLeavePageTrack(TRACK_EVENT_PAGE.HOME_PAGE.label, corpName);

      // 关闭企业信息确认弹窗
      setShowCorpConfirm(false);

      // 跳转地址
      navigate(path);
    }, 1000);


  };

  // 获取盐值
  useEffect(() => {
    async function init() {
      const value = await fetchSalt();
      if (value) {
        setSalt(value);
      }
    }
    init();

    // 禁止页面滚动 
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  useEffect(()=>{
    // 获取邀请码
    const searchParams = new URLSearchParams(location.search);
    let code = searchParams.get('ic');
    if (code) {
      setInviteCode(code);
    } else {
      code = sessionStorage.getItem(REGISTER_INVITE_CODE);
      if (code){
        setInviteCode(code);
      }
    }
  }, [location]);

  return (
    <>
      <Form
        form={form}
        onFinish={handleRegister}
        className="register-form"
        validateTrigger={['onChange', 'onBlur', 'onSubmit']}
      >
        <Flex vertical align={'center'} justify={'flex-start'} className="form-content" style={{ padding: '48px 0 58px 0' }} gap={12}>
          <div className={'text24 weight font-pf color-1a'}>
            注册账号
          </div>

          {showUnregisteredTip && (
            <div className={'text14 font-pf'} style={{ marginBottom: '-8px', color: '#ff4d4f' }}>
              该手机号未注册，请完成注册
            </div>
          )}

          <div className={'row-center-block'}>
            <span className="required-mark">*</span>
            <Form.Item
              name="corpName"
              rules={[{ required: true, message: '请输入企业名称' }]}
              style={{ marginBottom: 0, width: '100%' }}
            >
              <Input 
                size={'large'} 
                placeholder="请输入企业名称" 
                suffix={
                  <Tooltip
                    title="请输入完整的企业名称"
                    placement="topRight"
                    overlayClassName="corp-name-tooltip"
                  >
                    <QuestionCircleOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
                  </Tooltip>
                }
              />
            </Form.Item>
            <div className="corp-name-tip" style={{ position: 'absolute', right: '-155px', top: '50%', transform: 'translateY(-50%)', color: '#9E9E9E', fontSize: '14px' }}>
              (请输入完整的企业名称)
            </div>
          </div>

          <div className={'row-center-block'}>
            <span className="required-mark">*</span>
            <Form.Item
              name="mobile"
              rules={[
                { required: true, message: '请输入手机号码' },
                { pattern: PHONE_REGEX, message: '请输入正确的手机号码' }
              ]}
              style={{ marginBottom: 0, width: '100%' }}
            >
              <Input
                addonBefore="+86"
                size={'large'}
                allowClear
                showCount
                placeholder={'请输入手机号码'}
                maxLength={11}
              />
            </Form.Item>
          </div>

          <div className={'row-center-block'}>
            <Form.Item
              name="nickName"
              style={{ marginBottom: 0, width: '100%' }}
            >
              <Input size={'large'} placeholder="请输入昵称（非必填）" />
            </Form.Item>
          </div>

          <div className={'row-center-block'} style={{ height: '38px' }}>
            <span className="required-mark">*</span>
            <Form.Item
              name="code"
              rules={[{ required: true, message: '请输入验证码' }]}
              style={{ marginBottom: 0, width: '100%', height: '38px' }}
            >
              <ProFormCaptcha
                allowClear
                fieldProps={{ size: 'large', maxLength: 4 }}
                captchaProps={{ size: 'large' }}
                phoneName="mobile"
                placeholder="请输入验证码"
                onGetCaptcha={async () => {
                  const mobile = form.getFieldValue('mobile');
                  await handleGetCaptcha(mobile);
                }}
              />
            </Form.Item>
          </div>

          <Flex justify={'center'} className="btn-container">
            <Button 
              className={'login-btn'} 
              type="primary" 
              htmlType="submit" 
              disabled={!canCommit || loading}
              loading={loading}
            >
              <div className={'text14 font-pf weight color-w'}>注册</div>
            </Button>
          </Flex>

          <div className="agreement-container">
            <span className={'text12 font-pf color-9e'}>
              注册即表示已阅读并同意
              <Space size={0} className="agreement-links">
                <a target={'_blank'} href={'user_service.html'} className={'color-primary'}>《用户服务协议》</a>
                和
                <a target={'_blank'} href={'privacy_agreement.html'} className={'color-primary'}>《隐私政策》</a>
              </Space>
            </span>
          </div>

          <div className="login-entry">
            <span className={'text12 font-pf color-primary'}>已有账号？</span>
            <a
              className={'text12 font-pf color-primary pointer'}
              onClick={() => {
                window.history.pushState({}, '', window.location.pathname);
                changeAction('login');
              }}
            >
              立即登录 &gt;
            </a>
          </div>
        </Flex>
      </Form>
      <CorpConfirmModal
        open={showCorpConfirm}
        onCancel={() => setShowCorpConfirm(false)}
        onConfirm={handleCorpConfirm}
      />
    </>
  );
};

export default RegisterForm;
