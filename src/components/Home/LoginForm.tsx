import '@/pages/Login/index.less';
import { Button, Flex, Form, Input, notification, Tooltip } from 'antd';
import { ProFormCaptcha } from '@ant-design/pro-form';
import { NEED_GUIDE, PHONE_REGEX, USER_INFO } from '@/constants';
import IconFont from '@/components/IconFont';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { fetchSalt, pwdLogin, sendCaptcha, smsLogin } from '@/services/LoginController';
import { HomeLoginRegisterConfig, queryHomeLoginRegisterConfig } from '@/services/SystemController';
import cryptoStr from '@/utils/cryptojs';
import { ActionType } from '@/components/Home/LoginModal';
import { recordClickBtnTrack, recordLeavePageTrack } from '@/utils/trackingUtils';
import { TRACK_EVENT_BTN, TRACK_EVENT_PAGE } from '@/constants/TrackingConstants';
import './LoginForm.less';

type LoginType = 'sms' | 'pwd';

interface LoginFormProps {
  changeAction: (action: ActionType) => void;
}

function isCanCommit(loginType: LoginType, mobile: string, code: string, pwd: string) {
  if (loginType === 'sms') {
    return PHONE_REGEX.test(mobile) && code.length === 4;
  }
  return PHONE_REGEX.test(mobile) && pwd.length >= 6;
}

const LoginForm: React.FC<LoginFormProps> = ({ changeAction }) => {
  const [loginType, setLoginType] = useState<LoginType>('sms');
  const [canCommit, setCanCommit] = useState(false);
  const [mobile, setMobile] = useState<string>('');
  const [mobileError, setMobileError] = useState(false);
  const [pwd, setPwd] = useState('');
  const [code, setCode] = useState('');
  const [salt, setSalt] = useState('');
  const [capsLockOn, setCapsLockOn] = useState(false);
  const navigate = useNavigate();
  const [homeLoginRegisterConfig, setHomeLoginRegisterConfig] = useState<HomeLoginRegisterConfig | null>(null);
  const handleMobileChange = (e: { target: { value: any; }; }) => {
    const mobile = e.target.value;
    setMobile(mobile);

    if (mobile && !PHONE_REGEX.test(mobile)) {
      setMobileError(true);
      return;
    }

    setMobileError(false);
  };

  const handleCodeChange = (e) => {
    let code = e.target.value;
    setCode(code);
  };

  // 获取验证码
  const handleGetCaptcha = () => {
    if (!mobile || !PHONE_REGEX.test(mobile)) {
      return;
    }

    // 上报埋点信息
    recordClickBtnTrack(TRACK_EVENT_BTN.GET_SMS_BTN.label, { mobile: mobile });

    // @ts-ignore
    sendCaptcha(mobile);
  };


  const handlePwdChange = (e) => {
    setPwd(e.target.value);
  };

  const loginNavigate = (r) => {
    if (!r) {
      return;
    }
    notification.success({ message: '登录成功' });
    localStorage.setItem(USER_INFO, JSON.stringify(r));

    if (!r.lastLoginTime) {
      localStorage.setItem(NEED_GUIDE, String(true));
    }

    setTimeout(() => {
      let path = '/upload';
      if (r.roleType === 'ADMIN') {
        path = '/cloth-mng';
      } else if (r.roleType === 'REVIEWER') {
        path = '/cloth-review';
      } else if (r.roleType === 'DISTRIBUTOR') {
        path = '/distributor/customers';
      } else if (!r.imagePoint || r.imagePoint <= 0 || !r.lastLoginTime) {
        path = '/experience';
      } else if (sessionStorage.getItem('preset.face')) {
        path = '/creation';
      }

      // 上报埋点信息(登录成功后的数据需要进行 userId 更新处理)
      recordLeavePageTrack(TRACK_EVENT_PAGE.HOME_PAGE.label, r);

      // 跳转地址
      navigate(path);
    }, 1000);
  };

  const handleLogin = () => {
    if (!isCanCommit(loginType, mobile, code, pwd)) {
      return;
    }

    // 上报埋点信息
    recordClickBtnTrack(TRACK_EVENT_BTN.MODAL_LOGIN_BTN.label, {
      loginType: loginType,
      mobile: mobile,
      password: pwd,
      salt: salt,
    });


    if (loginType === 'sms') {
      smsLogin(mobile, code).then(r => {
        if (r?.error) {
          // 如果是未注册的错误，跳转到注册页
          changeAction('register');
          window.history.pushState({}, '', '?unregistered=true');
          notification.error({
            message: '登录失败',
            description: r.error.message,
          });
          return;
        }
        loginNavigate(r);
      }).catch(e => {
        console.log('登录失败', e);
      });
    }

    if (loginType === 'pwd') {
      const password = cryptoStr.aesEncrypt(pwd, salt);
      pwdLogin(mobile, password).then((r) => {
        loginNavigate(r);
      });
    }
  };

  //监听输入变化，设置是否能提交状态
  useEffect(() => {
    setCanCommit(isCanCommit(loginType, mobile, code, pwd));
  }, [loginType, mobile, code, pwd]);

  //获取盐值和配置
  useEffect(() => {
    async function init() {
      const [saltValue, configValue] = await Promise.all([
        fetchSalt(),
        queryHomeLoginRegisterConfig()
      ]);

      if (saltValue) {
        setSalt(saltValue);
      }

      if (configValue) {
        setHomeLoginRegisterConfig(configValue);
      }
    }

    init().then(() => {
    });

    //跳转时页面参数指定了模型id
    const queryParams = new URLSearchParams(location.search);
    const loginType = queryParams.get('loginType') as LoginType; // 获取名为 'modelId' 的查询参数
    if (loginType) {
      setLoginType(loginType);
    }

    // 设置当前页面的 body 样式
    document.body.style.overflow = 'hidden'; // 防止页面上下移动
    // 在组件卸载时恢复原样
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  // 改变登录方式
  const changeLoginType = (value: LoginType) => {
    // 上报埋点信息
    recordClickBtnTrack(TRACK_EVENT_BTN.CHANGE_LOGIN_TYPE_BTN.label, { loginType: value });

    // 避免重复点击
    if (loginType !== value) {
      setLoginType(value);
    }
  };


  const handlePwdDown = (e) => {
    if (e.getModifierState('CapsLock')) {
      setCapsLockOn(true);
    } else {
      setCapsLockOn(false);
    }
  };

  const handlePwdUp = (e) => {
    if (e.getModifierState('CapsLock')) {
      setCapsLockOn(true);
    } else {
      setCapsLockOn(false);
    }
  };

  const LoginTypeItem = ({ title, value }) => {
    return <div className={'text24 weight font-pf' + (loginType === value ? ' color-1a' : ' color-9e')}
      style={{ cursor: loginType === value ? undefined : 'pointer' }}
      onClick={() => changeLoginType(value)}>
      {title}
    </div>;
  };

  return <Form onFinish={handleLogin}>
    <Flex vertical align={'center'} justify={'flex-start'} style={{ padding: '48px 0 58px 0' }} gap={12}>
      {homeLoginRegisterConfig?.isOpenWechatLogin && (
        <div style={{ position: 'relative', width: '100%', height: 0 }}>
          <Tooltip title="微信扫码登录">
            <IconFont type={'icon-tesecanyin'}
              style={{ position: 'absolute', right: -150, top: -20, fontSize: '60px', cursor: 'pointer', color: '#1a1a1a' }} />
          </Tooltip>
        </div>
      )}
      <Flex justify={'center'} align={'center'} gap={24}>
        <LoginTypeItem title={'短信登录'} value={'sms'} />
        <LoginTypeItem title={'密码登录'} value={'pwd'} />
      </Flex>

      <div className={'row-center-block '}>
        <Input onChange={handleMobileChange} addonBefore="+86" size={'large'}
          status={mobileError ? 'error' : ''} allowClear showCount
          placeholder={'请输入手机号码'} maxLength={11} className={'login-input'} />
      </div>

      <Flex vertical align={'center'} justify={'center'} style={{ width: 304, marginBottom: -24 }}>
        {loginType === 'sms' &&
          <ProFormCaptcha className={'captcha-input'} allowClear fieldProps={{ size: 'large', maxLength: 4 }}
            captchaProps={{ size: 'large', disabled: !PHONE_REGEX.test(mobile) }}
            // 手机号的 name，onGetCaptcha 会注入这个值
            phoneName="phone" name="captcha" rules={[{ required: true, message: '请输入验证码' }]}
            placeholder="请输入验证码" onChange={handleCodeChange}
            onGetCaptcha={async () => handleGetCaptcha()}
          />
        }
        {loginType === 'pwd' &&
          <div className={'row-center-block pwd-block'}>
            <Input.Password placeholder="请输入登录密码" size={'large'} onChange={handlePwdChange}
              onKeyDown={(e) => handlePwdDown(e)}
              onKeyUp={(e) => handlePwdUp(e)}
              className={'margin-bottom-24'}
            />
            {capsLockOn &&
              <IconFont type={'icon-iocn_daxiesuoding'} className={'caps-lock-icon'} />
            }
          </div>
        }
      </Flex>
      <Flex justify={'center'}>
        <Button className={'login-btn'} type="primary" htmlType="submit" disabled={!canCommit}>
          <div className={'text14 font-pf weight color-w'}>登录</div>
        </Button>
      </Flex>

      <Flex justify={'center'} align={'center'}>
        <div className={'text12 font-pf color-9e'}>登录即表示已阅读并同意《<a target={'_blank'}
          href={'user_service.html'}>用户服务协议</a>》和《<a
            target={'_blank'} href={'privacy_agreement.html'}>隐私政策</a>》
        </div>
      </Flex>

      {loginType === 'sms' && (
        <Flex vertical align={'center'} justify={'center'} style={{ width: '100%',marginTop: '-10px' }}>
          <div className={'text-divider'}>
            <span style={{ color: '#767676', fontSize: '12px', padding: '0 8px', background: '#fff' }}>MuseGate新客户?</span>
          </div>

          {homeLoginRegisterConfig?.isOpenRegister && (
            <Button
              className={'register-btn'}
              onClick={() => {
                window.history.pushState({}, '', window.location.pathname);
                changeAction('register');
              }}
            >
              <span style={{ fontSize: '14px', color: '#0F1111' }}>创建您的MuseGate账户</span>
            </Button>
          )}
        </Flex>

      )}

      {loginType === 'pwd' && (
        <Flex vertical align={'center'} justify={'center'} style={{ width: '100%',marginTop: '-10px' }}>
          <div className={'text-divider'}>
            <span style={{ color: '#767676', fontSize: '12px', padding: '0 8px', background: '#fff' }}>忘记密码/创建账户</span>
          </div>

          <Flex gap={12} style={{ width: '304px' }}>
            <Button
              className={'register-btn'}
              onClick={() => changeAction('find-pwd')}
              style={{ flex: 1 }}
            >
              <span style={{ fontSize: '14px', color: '#0F1111' }}>忘记密码</span>
            </Button>

            {homeLoginRegisterConfig?.isOpenRegister && (
              <Button
                className={'register-btn'}
                onClick={() => changeAction('register')}
                style={{ flex: 1 }}
              >
                <span style={{ fontSize: '14px', color: '#0F1111' }}>创建账户</span>
              </Button>
            )}
          </Flex>
        </Flex>
      )}

    </Flex>
  </Form>;
};

export default LoginForm;