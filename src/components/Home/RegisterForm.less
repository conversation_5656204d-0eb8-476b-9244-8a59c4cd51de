.register-form {
  .ant-form-item {
    margin-bottom: 0;
    
    .ant-form-item-explain-error {
      display: none;
    }
    
    .ant-input-status-error {
      border-color: #ff4d4f;
    }
  }

  // 处理 ProFormCaptcha 组件的验证状态
  .ant-pro-form-captcha {
    .ant-input-affix-wrapper-status-error {
      border-color: #ff4d4f;
    }
    
    .ant-input-status-error {
      border-color: #ff4d4f;
    }
  }
  
  /* 响应式调整 */
  .row-center-block {
    width: 100%;
    max-width: 400px;
    position: relative;
    
    /* 企业名称提示 */
    .right-tip {
      position: absolute;
      right: -155px;
      top: 50%;
      transform: translateY(-50%);
      color: #9E9E9E;
      font-size: 14px;
      white-space: nowrap;
    }
    
    /* 必填项标记 */
    .required-mark {
      position: absolute;
      left: -16px;
      top: 50%;
      transform: translateY(-50%);
      color: red;
    }
    
    /* 企业名称右侧提示 */
    .corp-name-tip {
      position: absolute;
      right: -155px;
      top: 50%;
      transform: translateY(-50%);
      color: #9E9E9E;
      font-size: 14px;
      white-space: nowrap;
    }
  }
  
  /* 按钮容器 */
  .btn-container {
    margin-top: 8px;
    width: 100%;
    max-width: 400px;
  }
  
  /* 协议容器 */
  .agreement-container {
    margin-top: 12px;
    text-align: center;
    width: 100%;
    max-width: 400px;
    
    .agreement-links {
      display: inline-flex;
      flex-wrap: wrap;
      justify-content: center;
    }
  }
  
  /* 登录入口 */
  .login-entry {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    max-width: 400px;
    
    a {
      margin-left: 4px;
    }
  }
}

/* 自定义企业名称提示气泡 */
.corp-name-tooltip {
  .ant-tooltip-inner {
    font-size: 12px;
    padding: 4px 8px;
  }
  
  .ant-tooltip-arrow {
    display: none;
  }
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .register-form {
    .row-center-block {
      max-width: 100%;
      
      /* 处理企业名称右侧提示信息 */
      .right-tip {
        position: static !important;
        transform: none !important;
        display: block;
        text-align: right;
        margin-top: 4px;
        font-size: 12px;
        color: #9E9E9E;
      }
      
      /* 必填项标记调整 */
      .required-mark {
        left: -12px;
      }
      
      /* 隐藏企业名称右侧提示 */
      .corp-name-tip {
        display: none;
      }
    }
    
    /* 调整表单整体布局 */
    .form-content {
      padding: 32px 16px 42px 16px !important;
    }
    
    /* 调整注册按钮宽度 */
    .login-btn {
      min-width: 200px;
      width: 80%;
    }
    
    /* 调整协议相关样式 */
    .agreement-container {
      width: 90%;
      font-size: 12px;
      
      .agreement-links {
        margin-top: 2px;
      }
    }
    
    /* 调整登录入口位置 */
    .login-entry {
      width: 90%;
      margin-top: 12px;
    }
  }
}

/* 手机布局 */
@media screen and (max-width: 480px) {
  .register-form {
    /* 继续缩小内边距 */
    .form-content {
      padding: 24px 12px 32px 12px !important;
      gap: 8px !important;
    }
    
    /* 调整按钮到更窄的宽度 */
    .login-btn {
      min-width: 180px;
      width: 90%;
    }
    
    .row-center-block {
      /* 在小屏幕上调整必填项标记 */
      .required-mark {
        left: -10px;
      }
    }
    
    /* 在移动端上，隐藏Tooltip */
    .corp-name-tooltip {
      display: none !important;
    }
    
    /* 调整协议样式 */
    .agreement-container {
      font-size: 11px;
      
      .agreement-links {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        margin-top: 4px;
        gap: 2px;
        
        a {
          font-size: 11px;
        }
      }
    }
    
    /* 调整登录入口样式 */
    .login-entry {
      margin-top: 10px;
      justify-content: center;
      
      span, a {
        font-size: 11px;
      }
    }
  }
} 