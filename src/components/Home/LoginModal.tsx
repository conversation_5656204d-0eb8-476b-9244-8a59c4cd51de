import './LoginModal.less';
import '@/app.less';
import React, { FC, useState, useEffect } from 'react';
import { Flex, Modal, Tooltip } from 'antd';
import { BRAND, LOGO } from '@/constants';
import LoginForm from '@/components/Home/LoginForm';
import FindPwdForm from '@/components/Home/FindPwdForm';
import IconFont from '@/components/IconFont';
import { recordClickBtnTrack } from '@/utils/trackingUtils';
import { TRACK_EVENT_BTN } from '@/constants/TrackingConstants';
import RegisterForm from './RegisterForm';

export type ActionType = 'login' | 'find-pwd' | 'register';

interface LoginModalProps {
  onCancel: () => void;
  actionType?: ActionType;
}

const LoginModal: FC<LoginModalProps> = ({ onCancel, actionType }) => {
  const [action, setAction] = useState<ActionType>(actionType || 'login');
  const [modalWidth, setModalWidth] = useState(694);

  // 监听窗口大小变化，调整弹窗宽度
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth <= 768) {
        setModalWidth(Math.min(window.innerWidth * 0.9, 694));
      } else {
        setModalWidth(694);
      }
    };

    handleResize(); // 初始加载时执行一次
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const forgetPwd = (action: ActionType) => {
    // 上报埋点信息
    recordClickBtnTrack(TRACK_EVENT_BTN.FORGET_PWD_BTN.label);

    // 忘记密码
    setAction(action);
  };

  return (
    <Modal 
      open={true} 
      onCancel={onCancel} 
      footer={null} 
      width={modalWidth} 
      closeIcon={null} 
      centered
      className="login-modal"
    >
      <Flex className={'login-form-container'}>
        <Flex className={'login-logo'} align={'center'} justify={'center'}>
          <img src={LOGO} alt={'logo'} width={'96'} height={'96'} />
          <img src={BRAND} alt={'logo'} width={'318'} height={'72'} />
        </Flex>

        <div 
          style={{ position: 'absolute', right: -64, top: -24 }} 
          className="close-button"
          onClick={onCancel}
        >
          <IconFont type={'icon-lujing'} style={{ fontSize: 24 }} className={'color-w pointer'} />
        </div>

        {action === 'login' &&
          <LoginForm changeAction={action => forgetPwd(action)} />
        }

        {action === 'find-pwd' &&
          <FindPwdForm onCancel={() => setAction('login')} />
        }

        {action === 'register' &&
          <RegisterForm 
            changeAction={action => forgetPwd(action)} 
            showUnregisteredTip={window.location.search.includes('unregistered=true')}
          />
        }

      </Flex>

    </Modal>);
};

export default LoginModal;