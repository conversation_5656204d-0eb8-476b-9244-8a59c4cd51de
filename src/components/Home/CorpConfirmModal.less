.corp-confirm-modal {
  .ant-modal-content {
    .ant-modal-body {
      padding: 20px;
    }
    
    .ant-modal-header {
      padding: 16px 20px;
    }
    
    .ant-modal-footer {
      padding: 16px 20px;
    }
  }

  .ant-btn {
    &.ant-btn-loading {
      opacity: 0.8;
    }
  }
  
  /* 表单紧凑样式 */
  .compact-form {
    .ant-form-item {
      margin-bottom: 8px;
    }
  }
  
  /* 提示文字 */
  .search-tip {
    display: block;
    text-align: center;
    font-size: 12px;
    margin-bottom: 8px;
  }
  
  /* 表格容器 */
  .table-container {
    margin-top: 4px;
  }
  
  /* 空数据样式 */
  .empty-content {
    margin: 16px 0;
    
    .ant-empty-description {
      font-size: 12px;
    }
  }
  
  /* 企业表格 */
  .corp-table {
    .corp-name-cell {
      font-weight: 500;
      color: #333;
    }
    
    .select-button {
      padding: 2px 8px;
    }
    
    .ant-table-cell {
      vertical-align: middle;
      padding: 8px;
    }
    
    .ant-table-thead > tr > th {
      padding: 8px;
    }
    
    .ant-table-row {
      transition: background-color 0.3s;
      
      &.ant-table-row-selected {
        background-color: #e6f7ff;
      }
      
      &:hover {
        background-color: #f5f5f5;
      }
    }
    
    /* 减少表格分页器的边距 */
    .ant-table-pagination {
      margin: 12px 0 0 0;
    }
  }
  
  /* 搜索图标 */
  .search-icon {
    &:hover {
      color: #40a9ff;
    }
  }
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .corp-confirm-modal {
    .ant-modal-content {
      .ant-modal-body {
        padding: 12px;
      }
      
      .ant-modal-header {
        padding: 12px;
      }
      
      .ant-modal-footer {
        padding: 12px;
        margin-top: -8px;
      }
    }
    
    .ant-table {
      font-size: 13px;
      
      .ant-table-cell {
        padding: 6px 4px;
      }
      
      .ant-pagination {
        margin: 8px 0 0 0;
        
        .ant-pagination-item {
          min-width: 28px;
          height: 28px;
          line-height: 26px;
        }
      }
    }
    
    /* 调整表单布局 */
    .corp-form-container {
      flex-direction: column;
      
      .ant-form-item {
        width: 100% !important;
        margin-bottom: 4px !important;
      }
    }
    
    /* 搜索提示 */
    .search-tip {
      margin-bottom: 6px;
      font-size: 11px;
    }
    
    /* 企业表格 */
    .corp-table {
      .select-button {
        padding: 0 4px;
      }
      
      .ant-table-tbody > tr > td {
        overflow-wrap: break-word;
        word-wrap: break-word;
      }
    }
  }
}

/* 移动端布局 */
@media screen and (max-width: 480px) {
  .corp-confirm-modal {
    .ant-modal-header {
      padding: 10px 12px;
    }
    
    .ant-modal-content {
      .ant-modal-body {
        padding: 10px;
      }
      
      .ant-modal-footer {
        padding: 10px;
      }
    }
    
    .ant-table {
      font-size: 12px;
      
      /* 减少表格内部间距 */
      .ant-table-cell {
        padding: 5px 2px;
      }
      
      /* 调整分页器 */
      .ant-pagination {
        margin: 6px 0 0 0;
        text-align: center;
        
        .ant-pagination-prev, 
        .ant-pagination-next {
          min-width: 24px;
          height: 24px;
          line-height: 24px;
        }
        
        .ant-pagination-item {
          min-width: 24px;
          height: 24px;
          line-height: 22px;
        }
      }
    }
    
    /* 极小屏幕下的表格样式 */
    .corp-table {
      .corp-name-cell {
        display: inline-block;
        max-width: 160px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .select-button {
        padding: 0;
        margin: 0;
        height: auto;
        line-height: 1.5;
      }
      
      .ant-table-thead > tr > th {
        padding: 5px 2px;
        font-size: 12px;
      }
    }
    
    /* 空数据样式 */
    .empty-content {
      margin: 10px 0;
      
      .ant-empty-image {
        height: 30px;
      }
    }
  }
}
