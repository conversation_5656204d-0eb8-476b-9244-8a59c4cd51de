.login-form-container {
  position: relative;
  width: 100%;
  height: 390px;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  overflow-y: initial;
}

.login-logo {
  position: absolute;
  top: -137px;
  width: 100%;
  padding: 12px;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .login-modal {
    .ant-modal-content {
      padding: 16px;
    }
  }
  
  .login-form-container {
    height: auto;
    min-height: 360px;
    padding-bottom: 16px;
  }

  .login-logo {
    top: -100px;
    padding: 8px;
    
    img {
      /* Logo大小缩小 */
      &:first-child {
        width: 72px !important;
        height: 72px !important;
      }
      
      &:last-child {
        width: 240px !important;
        height: 54px !important;
      }
    }
  }
  
  .close-button {
    right: -20px !important;
    top: -12px !important;
  }
}

/* 手机布局 */
@media screen and (max-width: 480px) {
  .login-logo {
    top: -80px;
    
    img {
      /* Logo大小进一步缩小 */
      &:first-child {
        width: 54px !important;
        height: 54px !important;
      }
      
      &:last-child {
        width: 180px !important;
        height: 40px !important;
      }
    }
  }
  
  .close-button {
    right: -12px !important;
    top: -12px !important;
  }
}
