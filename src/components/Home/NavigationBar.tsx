import { Flex, MenuProps } from 'antd';
import { LOGO_BRAND, NAVI_TYPE, NAVI_TYPE_CHANGE_EVENT, USER_INFO } from '@/constants';
import UserIconBlock from '@/layout/UserIconBlock';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { HiddenInfo } from '@/pages/Home';
import { UserVO } from '@/services/UserController';
import { getUserInfo, isProdEnv, isValidJsonObject } from '@/utils/utils';
import { useLocation, useNavigate } from 'react-router-dom';
import LoginModal from '@/components/Home/LoginModal';
import { loginStatus } from '@/services/LoginController';
import IconFont from '@/components/IconFont';
import { Dropdown } from 'antd/lib';
import { queryMenusCfg } from '@/services/SystemController';
import RightContentRender from '@/layout/RightContentRender';
import { isDesktop } from 'react-device-detect';


export interface NavigationBarProps {
  needBorder?: boolean;
  showMenusByWhitelist?: Set<string>;
}

export interface NavigationBarRef {
  gotoStart: () => void;
  checkLogin: () => void;
  gotoRegister: () => void;
}

const NavigationBar = forwardRef<NavigationBarRef, NavigationBarProps>(({ needBorder = false, showMenusByWhitelist }, ref) => {
  const userInfo: UserVO | null = getUserInfo();
  const navigate = useNavigate();
  const location = useLocation();
  const [isScrolled, setIsScrolled] = useState(false);
  const [showLogin, setShowLogin] = useState(false);
  const [showRegister, setShowRegister] = useState(false);
  const [currentNaviType, setCurrentNaviType] = useState<string | null>('HOME');
  const [showBusiness, setShowBusiness] = useState(false);

  useImperativeHandle(ref, () => ({
    gotoStart: () => gotoStart(),
    checkLogin: () => checkLogin(),
    gotoRegister: () => gotoRegister(),
  }));

  useEffect(() => {
    const handleScroll = () => {
      // 检查滚动距离
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    const handleNaviTypeChange = () => {
      setCurrentNaviType(sessionStorage.getItem(NAVI_TYPE));
    };

    if (userInfo && userInfo.roleType === 'ADMIN') {
      if (isProdEnv()) {
        queryMenusCfg().then(res => {
          if (typeof res === 'string' && isValidJsonObject(res)) {
            let cfg = JSON.parse(res);
            setShowBusiness(cfg && cfg['/business-mng'] && cfg['/business-mng'].includes(userInfo.id));
          }
        });
      } else {
        setShowBusiness(true);
      }
    }

    window.addEventListener('scroll', handleScroll);
    window.addEventListener(NAVI_TYPE_CHANGE_EVENT, handleNaviTypeChange);
    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener(NAVI_TYPE_CHANGE_EVENT, handleNaviTypeChange);
    };
  }, []);

  const gotoStart = () => {
    let path = '/creation';
    if (userInfo) {
      if (userInfo.roleType === 'ADMIN') {
        path = '/cloth-mng';
      } else if (userInfo.roleType === 'REVIEWER') {
        path = '/cloth-review';
      } else if (userInfo.roleType === 'DISTRIBUTOR') {
        path = '/distributor/customers';
      } else if (!userInfo.imagePoint || userInfo.imagePoint <= 0 || !userInfo.lastLoginTime) {
        path = '/experience';
      }
    }

    goto(path, true);
  };

  const goto = (path: string, needLogin: boolean) => {
    const current = location.pathname;
    if (current === path) {
      return;
    }
    if (needLogin && !userInfo) {
      setShowLogin(true);
    } else {
      navigate(path);
    }

  };

  const checkLogin = async () => {
    if (!userInfo || await loginStatus() === 'NONE') {
      localStorage.removeItem(USER_INFO);
      setShowLogin(true);
    }
  };

  const gotoRegister = async () => {
    localStorage.removeItem(USER_INFO);
    setShowRegister(true);
  };

  const NavigateMenu = ({ title, path, needLogin = false, naviType = 'HOME', naviByPath = false, subMenu = false }) => {
    const selected = naviByPath ? location.pathname === path : currentNaviType === naviType;
    return <Flex className={'home-navigation-menu pointer' + (selected ? ' home-navigation-menu-selected' : '')}
      justify={'center'} align={'center'} onClick={() => subMenu ? null : goto(path, needLogin)}>
      <div className={'text18 weight' + (selected ? ' color-1a' : ' color-b-4')}
        style={{ position: 'relative' }}>
        <div style={{ zIndex: 2, opacity: 1, position: 'relative' }}>
          {title}
        </div>

        <HiddenInfo />
      </div>
    </Flex>;
  };

  const buildDropdownMenu = (path, title, disabled = false) => {
    return { key: path, label: <a onClick={() => navigate(path)}> {title}</a>, disabled };
  };

  let items: MenuProps['items'] = [
    buildDropdownMenu('/create-video', '创作视频'),
    buildDropdownMenu('/window-video', '橱窗AI视频'),
    buildDropdownMenu('/', '视频混剪（敬请期待）', true),
  ];

  const VideoTitle = () => <Dropdown menu={{ items }}>
    <Flex justify={'center'} align={'center'} gap={4}>
      <div>AI视频</div>
      <IconFont type={'icon-a-jiantou1x'} style={{ fontSize: 16 }} />
    </Flex>
  </Dropdown>;


  return <Flex justify={'space-between'} align={'center'}
    className={'home-main-title' + ((isScrolled || needBorder) ? ' home-main-title-scrolled' : '')}>
    {/*logo区域*/}
    <Flex justify={'space-between'} align={'center'} gap={isDesktop ? 100 : 10}>
      <img alt={'logo'} src={LOGO_BRAND} width={126} height={28} />

      <Flex gap={isDesktop ? 24 : 8} justify={'flex-start'} className={'width-100'}>
        <NavigateMenu title={'首页'} path={'/'} naviType={'HOME'} naviByPath={true} />
        {userInfo?.roleType && !['ADMIN', 'REVIEWER'].includes(userInfo?.roleType) &&
          <>
            <NavigateMenu title={'模特图'} path={'/creation'} naviType={'IMAGE'} needLogin={true} />
            <NavigateMenu title={<VideoTitle />} path={'/create-video'} naviType={'VIDEO'} needLogin={true} />
            {userInfo?.roleType === 'OPERATOR' &&
              <NavigateMenu title={'服装设计'} path={'/logo-combine'} naviType={'DESIGN'} needLogin={true} />
            }
            {userInfo?.roleType === 'DISTRIBUTOR' &&
              <NavigateMenu title={'经营管理'} path={'/distributor/customers'} naviType={'DIS-MANAGE'} needLogin={true} />
            }
            <NavigateMenu title={'使用教程'} path={'/tutorial'} naviType={'TUTORIAL'} naviByPath={true} />
          </>
        }
        {userInfo?.roleType === 'REVIEWER' &&
          <>
            <NavigateMenu title={'管理平台'} path={'/cloth-review'} naviType={'IMAGE'} needLogin={true} />
          </>
        }
        {userInfo?.roleType === 'ADMIN' &&
          <>
            <NavigateMenu title={'管理平台'} path={'/cloth-mng'} naviType={'MANAGE'} needLogin={true} />
            {showBusiness &&
              <NavigateMenu title={'业务经营'} path={'/delivery-stats'} naviType={'BUSINESS'} needLogin={true} />
            }

            {(!(showMenusByWhitelist && showMenusByWhitelist.size > 0 && !showMenusByWhitelist.has('/abtest'))) &&
              <NavigateMenu title={'开发工具'} path={'/abtest'} naviType={'DEVELOP'} needLogin={true} />
            }
          </>
        }
      </Flex>
    </Flex>

    <Flex gap={16}>
      {userInfo &&
        <>
          <RightContentRender />
          <UserIconBlock />
        </>
      }
      {!userInfo &&
        <div className={'home-main-login-btn pointer'} onClick={() => setShowLogin(true)}>登录</div>
      }

      {showLogin &&
        <LoginModal onCancel={() => setShowLogin(false)} />
      }

      {showRegister &&
        <LoginModal
          actionType={'register'}
          onCancel={() => setShowRegister(false)} />
      }
    </Flex>
  </Flex>;
});

export default NavigationBar;