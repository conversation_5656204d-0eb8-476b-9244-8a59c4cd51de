import React from 'react';
import LazyLoad from 'react-lazyload';

interface LazyImageProps {
  src: string;
  alt: string;
  height?: number;
  offset?: number;
  className?: string;
  onClick?: () => void;
}

const LazyImage: React.FC<LazyImageProps> = ({ src, alt, height = 0, offset = 100, className = '', onClick }) => {
  return (
    <LazyLoad height={height} offset={offset} once>
      <img alt={alt} src={src} className={className} onClick={onClick} />
    </LazyLoad>
  );
};

export default LazyImage;