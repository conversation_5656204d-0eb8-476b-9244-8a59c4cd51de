import React, { useEffect, useState } from 'react';
import { Modal, Button, Checkbox, message } from 'antd';
import './index.less';

interface DisclaimerModalProps {
  visible: boolean;
  onClose: () => void;
  onAgree: () => void;
}

const DisclaimerModal: React.FC<DisclaimerModalProps> = ({ visible, onClose, onAgree }) => {
  const [loading, setLoading] = useState(true);
  const [content, setContent] = useState('');
  const [agreed, setAgreed] = useState(false);

  useEffect(() => {
    if (visible) {
      setLoading(true);
      // 加载免责声明HTML内容
      fetch('/disclaimer.html')
        .then(response => response.text())
        .then(html => {
          // 提取body内容
          const bodyContent = html.match(/<body[^>]*>([\s\S]*)<\/body>/i)?.[1] || html;
          setContent(bodyContent);
          setLoading(false);
        })
        .catch(error => {
          console.error('加载免责声明失败:', error);
          setContent('<p>加载免责声明内容失败，请刷新重试。</p>');
          setLoading(false);
        });
    }
  }, [visible]);

  const handleAgree = () => {
    if (!agreed) {
      message.warning('请先阅读并同意免责声明');
      return;
    }
    onAgree();
    onClose();
  };

  return (
    <Modal
      title="免责声明"
      open={visible}
      width={800}
      centered
      closable={false}
      maskClosable={false}
      footer={[
        <div key="footer" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Checkbox checked={agreed} onChange={e => setAgreed(e.target.checked)}>
            我已阅读并同意以上条款
          </Checkbox>
          <div>
            <Button onClick={onClose} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button type="primary" onClick={handleAgree}>
              确认
            </Button>
          </div>
        </div>,
      ]}
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '20px 0' }}>加载中...</div>
      ) : (
        <div
          className="disclaimer-content"
          dangerouslySetInnerHTML={{ __html: content }}
        />
      )}
    </Modal>
  );
};

export default DisclaimerModal;