import { ElementConfigWithBlobs } from '@/services/ElementController';
import { Alert, Flex, message, Modal, Radio, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { queryAllMaster } from '@/services/UserController';
import { assignElementModelToUser, MaterialModelWithBlogs } from '@/services/MaterialModelController';
import { getUserInfo, isProdEnv } from '@/utils/utils';
import { getDistributorCustomerByPage, queryCustomerMusePoint } from '@/services/DistributorController';
import { queryImagePointByUserId } from '@/services/PointController';

function AssignModel2User(props: {
  onCancel: () => void,
  onOk: () => void,
  assignElementConfig?: ElementConfigWithBlobs,
  assignElementLora?: MaterialModelWithBlogs,
  materialType: string,
}) {

  //转交客户
  const [assignUserId, setAssignUserId] = useState<number | null>(null);
  const [assignElementExclusive, setAssignElementExclusive] = useState<boolean | null>(true);
  const [free, setFree] = useState(false);
  //需要扣除的muse点
  const [musePoint, setMusePoint] = useState<number>();
  //选中的目标商家的muse点余额
  const [assignUserImgPoint, setAssignUserImgPoint] = useState<number>();
  const [loraModelId, setLoraModelId] = useState<number>();
  const [modelName, setModelName] = useState<string>();

  //客户列表
  const [masterOptions, setMasterOptions] = useState<Array<any>>([]);

  //原始模型归属的商家id
  const [originalUserId, setOriginalUserId] = useState<number>();
  const [originalUserNick, setOriginalUserNick] = useState<string>();

  const materialType = props.materialType;

  const userInfo = getUserInfo();

  console.log('AssignModel2User', props);

  useEffect(() => {

    //获取渠道商的客户列表
    if (userInfo && userInfo?.roleType === 'DISTRIBUTOR') {
      getDistributorCustomerByPage({
        pageNum: 1,
        pageSize: 9999999,
        orderBy: 'id desc',

      }).then(res => {
        if (res && res.list) {
          //@ts-ignore 追加一个key字段，避免警告
          let newData = res.list.map(item => ({ label: item.nickName, value: item.id }));
          setMasterOptions(newData);
        } else {
          setMasterOptions([]);
        }
      });

      //后台管理员，获取所有主账号
    } else if (userInfo?.roleType === 'ADMIN' || userInfo?.roleType === 'OPERATOR') {

      queryAllMaster(['MERCHANT', 'OPERATOR', 'ADMIN', 'DISTRIBUTOR']).then(res => {
        if (res && Array.isArray(res)) {
          const masterOptions = [];
          res.forEach(item => {
            // @ts-ignore
            masterOptions.push({
              label: item.nickName + (item.corpName ? '@' + item.corpName : ''),
              value: item.id,
            });
          });

          setMasterOptions(masterOptions);
        }
      });
    }

    if (props.assignElementLora) {
      setLoraModelId(props.assignElementLora.id);
      setModelName(props.assignElementLora.name);
      setOriginalUserId(props.assignElementLora.userId);
      setOriginalUserNick(props.assignElementLora.userNick);

      setAssignUserId(props.assignElementLora.userId);

    } else if (props.assignElementConfig) {
      setLoraModelId(props.assignElementConfig.loraModelId);
      setModelName(props.assignElementConfig.name);
      setOriginalUserId(props.assignElementConfig.userId);
      setOriginalUserNick(props.assignElementConfig.userNick);

      setAssignUserId(props.assignElementConfig.userId || null);

    } else {
      throw new Error('assignElementLora和assignElementConfig不可同时为空');
    }

    return () => {
      setAssignUserId(null);
      setAssignElementExclusive(true);
      setFree(false);
      setMusePoint(0);
    }

  }, []);

  useEffect(() => {
    if (free) {
      setMusePoint(0);
    } else {
      if (materialType) {
        if (materialType === 'face') {
          setMusePoint(1200);

        } else if (materialType === 'scene') {
          setMusePoint(assignElementExclusive ? 200 : 40);

        } else {
          throw new Error('未知的素材类型' + materialType);
        }
      }

    }

  }, [assignElementExclusive, free, materialType])


  useEffect(() => {
    if (!assignUserId || Number(assignUserId) < 1) {
      return;
    }

    //渠道商代用户上传服装，此时查询代用户积分
    if (userInfo && userInfo.roleType === 'DISTRIBUTOR') {
      queryCustomerMusePoint({ customerMasterId: Number(assignUserId) }).then(res => {
        if (res && res.imagePoint != null) {
          setAssignUserImgPoint(res.imagePoint);
        }
      });

    } else if (userInfo?.roleType === 'ADMIN') {
      queryImagePointByUserId(Number(assignUserId)).then(res => {
        if (res && res.imagePoint != null) {
          setAssignUserImgPoint(res.imagePoint);
        }
      });
    }
  }, [assignUserId, userInfo]);


  function okDisabled() {
    console.log('okDisabled', assignUserId, musePoint, assignUserImgPoint);
    return !assignUserId || (musePoint != null && assignUserImgPoint != null && musePoint > assignUserImgPoint);
  }

  return (
    <Modal open={true}
      title={'设置指定用户'}
      onCancel={props.onCancel}
      onOk={() => {
        assignElementModelToUser(loraModelId, Number(assignUserId), assignElementExclusive, free).then(res => {
          if (res) {
            message.success('转移成功');
            if (props.onOk) {
              props.onOk();
            }
          } else {
            message.error('转移失败');
          }
        });
      }}
      okButtonProps={{ disabled: okDisabled() }}
    >
      <Flex vertical gap={8}>
        {userInfo?.roleType === 'ADMIN' && originalUserId && originalUserNick &&
          <Alert message={'当前模型已归属于商家"' + originalUserNick + '"，请谨慎操作！！！'}
            type={'error'} />
        }
        <div>目标模型：{modelName}</div>
        <Flex align={'center'}>
          <span>指定商家：</span>
          <Select options={masterOptions}
            style={{ width: '70%' }}
            showSearch
            optionFilterProp="label"
            defaultActiveFirstOption={true}
            value={assignUserId}
            onChange={(e) => setAssignUserId(e)} />
        </Flex>

        <Flex gap={8} align={'center'}>
          <span>是否专属:</span>
          <Radio.Group
            value={assignElementExclusive}
            buttonStyle="solid"
            onChange={e => {
              setAssignElementExclusive(e.target.value);
            }}>
            <Radio.Button value={true}>专属</Radio.Button>
            {(materialType === 'scene' || [100155].includes(userInfo?.id || 0)) && <Radio.Button value={false}>公开</Radio.Button>}
          </Radio.Group>
        </Flex>

        {userInfo?.roleType === 'ADMIN' &&
          <Flex gap={8} align={'center'}>
            <span>是否收费:</span>
            <Radio.Group
              value={free}
              buttonStyle="solid"
              onChange={e => {
                setFree(e.target.value);
              }}>
              <Radio.Button value={false}>收费</Radio.Button>
              <Radio.Button value={true} disabled={(![100156,100152,100155].includes(userInfo?.id) && isProdEnv())}>免费</Radio.Button>
            </Radio.Group>
          </Flex>
        }

        <div style={{ color: 'red' }}>{free ? '注意：模型将免费转交给商家' : `注意：模型指定给商户后，将自动扣除该商户名下${musePoint}缪斯点`}</div>

        {!free && musePoint != null && assignUserImgPoint != null &&
          <span>目标商家剩余缪斯点：{assignUserImgPoint}，<span style={{ color: assignUserImgPoint > musePoint ? '' : 'red' }}>{assignUserImgPoint > musePoint ? '余额充足' : '余额不足，请联系商家先充值'}</span></span>
        }

      </Flex>
    </Modal>
  );
}

export default AssignModel2User;