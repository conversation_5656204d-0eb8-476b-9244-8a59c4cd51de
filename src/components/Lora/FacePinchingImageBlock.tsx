import React, { FC, useEffect, useState } from 'react';
import { Flex, Tooltip } from 'antd';
import { getImageName } from '@/utils/utils';
import { CreativeVO, isProcessing } from '@/services/CreativeController';
import { chunkArray } from '@/utils/ArrayUtils';
import ImgPreview from '@/components/ImgPreview';
import { CheckCircleFilled, CheckCircleOutlined } from '@ant-design/icons';

interface FacePinchingImageBlockProps {
  task: CreativeVO;
  onChange: (selected: string[]) => void;
}

/**
 * 对图片 URL 按 FP 图片索引进行排序
 * @param a 图片 URL A
 * @param b 图片 URL B
 */
export const sortFpImages = (a: string, b: string): number => {
  const indexA = getFpImgIndex(a);
  const indexB = getFpImgIndex(b);
  return indexA - indexB;
};

export const getFpImgIndex = (url: string) => {
  const imageName = getImageName(url);
  const split = imageName.split('_');
  if (split.length < 3) {
    return 0;
  }
  return Number(split[2]);
};

const FacePinchingImageBlock: FC<FacePinchingImageBlockProps> = ({ task, onChange }) => {
  const [images, setImages] = useState<string[]>([]);
  const [data, setData] = useState<string[][] | undefined>();
  const [selected, setSelected] = useState<string[]>([]);
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null);

  useEffect(() => {
    if (!task) return;
    const resultImages = task.resultImages?.sort(sortFpImages);
    if (resultImages) {
      setImages(resultImages);
      setData(chunkArray(resultImages, 3));
    }
  }, [task]);

  if (!task || isProcessing(task) || !task?.resultImages) {
    return null;
  }

  const addSelected = (url: string, arr: string[] | null = null) => {
    const list = arr ? arr : data?.find(e => e.includes(url));
    let target = selected.filter(e => !list?.includes(e));
    target.push(url);
    setSelected(target);
    onChange(target);
  };

  return (
    <Flex gap={8} wrap={'wrap'} style={{ padding: 8, border: '1px solid #D9E1FF', borderRadius: 8 }}>
      {data && data.map((e, index) =>
        <Flex gap={8} key={index} style={{ padding: 8, border: '1px solid #D9E1FF', borderRadius: 8 }}>
          {e.map((sub, subIdx) =>
            <div key={subIdx} style={{ position: 'relative' }}>
              <img src={sub} style={{ width: 150 }} className={'pointer'} alt={''}
                   onClick={() => setPreviewImageUrl(sub)} />
              <div style={{ position: 'absolute', top: 8, right: 8 }} onClick={() => addSelected(sub, e)}>
                {selected.includes(sub) &&
                  <CheckCircleFilled style={{ fontSize: 20 }} className={'pointer color-brand'} />
                }
                {!selected.includes(sub) &&
                  <Tooltip title={'点击选择当前图片'}>
                    <CheckCircleOutlined style={{ fontSize: 20 }} className={'pointer color-a0'} />
                  </Tooltip>
                }
              </div>
            </div>,
          )}
        </Flex>,
      )}

      {previewImageUrl &&
        <ImgPreview
          previewVisible={true}
          previewImgs={images}
          previewIdx={images.findIndex((e) => e === previewImageUrl)}
          handleCancelPreview={() => setPreviewImageUrl(null)}
          previewImage={previewImageUrl || ''}
          needWatermark={false}
          needSwitch={true}
          isEnableGou={(previewImage) => selected.includes(previewImage)}
          isBlueGou={true}
          imageClick={addSelected}
        />
      }
    </Flex>
  );
};

export default FacePinchingImageBlock;