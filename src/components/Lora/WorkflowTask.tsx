import { USER_INFO } from "@/constants";
import { User<PERSON> } from "@/services/UserController";
import { WorkflowTaskVO } from "@/services/WorkflowTaskController";
import { EditOutlined } from "@ant-design/icons";
import { Flex, Tooltip } from "antd";

interface MaterialModelProps {
  id: number;
  status: string;
  tasks?: WorkflowTaskVO[];
  promptEngineerNick?: string;
  extInfo?: { testImageFinished?: string, reviewerId?: number, disableTime?: string, disableReason?: string } & Map<string, string>;
}

export function ReviewTaskStatus({
    model,
    users,
    type
}: {
    model: MaterialModelProps | undefined;
    users: UserVO[];
    type: 'initial_review' | 'again_review';
}) {
    const reviewTask = model?.tasks?.find(e => e.type === type);
    return (
        <div className={'lora-detail-pair'}>
            <div className="font-pf text14 weight">
                {type === 'initial_review' ? '初审' : '复审'}
                <span className={'text12 color-a0 normal margin-left-4'}>
                    {users.find((user) => user.id == reviewTask?.operatorId)?.nickName}
                </span>
            </div>
            <div className="font-pf text12">
            {(() => {
                switch(reviewTask?.status) {
                case 'COMPLETED':
                    return <span>{reviewTask?.modifyTime} <span style={{color: '#52c41a'}}>完成</span></span>;
                case 'FAILED':
                    return <span>{reviewTask?.modifyTime} <span style={{color: '#ff4d4f'}}>不通过</span></span>;
                default:
                    return <span style={{color: '#666'}}>未完成</span>;
            }
            })()}
        </div>
        {reviewTask?.status === 'FAILED' && (
            <Flex vertical>
                {reviewTask?.meta?.reasons?.map((item, index) => (
                    <div key={index + '' + reviewTask.id} className="font-pf text12">
                        {item}
                    </div>
                ))}
            </Flex>
        )}
    </div>
    );
}

export function ReviewStatus({
    model,
    users,
}: {
    model: MaterialModelProps | undefined;
    users: UserVO[];
}) {
    return (
        <div className={'lora-detail-pair'}>
            <div className="font-pf text14 weight">
                审核
                <span className={'text12 color-a0 normal margin-left-4'}>
                    {users.find((user) => user.id === model?.extInfo?.reviewerId)?.nickName}
                </span>
            </div>
            <div className="font-pf text12">
            {(() => {
                switch(model?.status) {
                case 'ENABLED':
                    return <span style={{color: '#52c41a'}}>完成</span>;
                case 'DISABLED':
                    return <span>{model?.extInfo?.disableTime} <span style={{color: '#ff4d4f'}}>不通过</span></span>;
                default:
                    return <span style={{color: '#666'}}>未完成</span>;
            }
            })()}
        </div>
        <Flex vertical>
            {model?.extInfo?.disableReason && (
                <div className="font-pf text12">
                    {model?.extInfo?.disableReason}
                </div>
            )}
        </Flex>
    </div>
    );
}

export function ReviewStatusText({item}: {item: MaterialModelProps}) {
    const statusTexts: string[] = [];
    if (item?.tasks?.find((e) => e.type === 'initial_review')?.status === 'COMPLETED') {
      statusTexts.push('初审通过');
    }
    if (item?.tasks?.find((e) => e.type === 'initial_review')?.status === 'FAILED') {
      statusTexts.push('初审不通过');
    }
    if (item?.tasks?.find((e) => e.type === 'again_review')?.status === 'COMPLETED') {
      statusTexts.push('复审通过');
    }
    if (item?.tasks?.find((e) => e.type === 'again_review')?.status === 'FAILED') {
      statusTexts.push('复审不通过');
    }
    return statusTexts.length > 0 ? (<>
      <div
        className={
          'models-training-title'
        }
      >
        {statusTexts.join('-')}
      </div>
    </>) : null;
}

export function TestImageStatus({item}: {
    item: MaterialModelProps | undefined;
}) {
    const testImageFinished = item?.extInfo?.testImageFinished === 'Y';
    return (
        <div className={'lora-detail-pair'}>
            <div className="font-pf text14 weight">
                出图
            </div>
            <div className="font-pf text12">
                {testImageFinished ? (
                    <span><span style={{color: '#52c41a'}}>完成</span></span>
                ) : (
                    <span style={{color: '#666'}}>未完成</span>
                )}
            </div>
        </div>
    );
}

export function ReviewerNameText({item, users}: {item: MaterialModelProps, users: Array<UserVO>}) {
    const task = (task: {type?: string}) => (item.status === 'IN_TRAINING' && task.type === 'initial_review') || (item.status !== 'IN_TRAINING' && task.type === 'again_review');
    return <div className="loras-image-card-info">
        <span style={{ color: 'black' }}>审核员：</span>
        <a>{users.find((o) => o.id == item.tasks?.find((e) => task(e))?.operatorId)?.nickName}</a>
    </div>
}

export function PromptUserNameText({item, users}: {item: MaterialModelProps, users: Array<UserVO>}) {
    const task = (task: {type?: string}) => (item.status === 'IN_TRAINING' && task.type === 'prompt_user_review') || (item.status !== 'IN_TRAINING' && task.type === 'prompt_user_again_review');
    return <div className="loras-image-card-info">
        <span style={{ color: 'black' }}>工程师：</span>
        <a>{users.find((o) => o.id == item.tasks?.find((e) => task(e))?.operatorId)?.nickName}</a>
    </div>
}

export function ReviewerNick({ item, users, onclick }: {
  item: MaterialModelProps,
  users: Array<UserVO>,
  onclick: () => void
}) {
  const userInfo = localStorage.getItem(USER_INFO);
  const allow = userInfo ? JSON.parse(userInfo).id === 100156 : false;
  return <div className="loras-image-card-info">
    <span style={{ color: 'black' }}>审核员：</span>
    <a>{users.find((o) => o.id == item.extInfo?.reviewerId)?.nickName}</a>
    {allow &&
      <Tooltip title={'变更审核员'}>
        <span style={{
          marginLeft: 6,
          color: '#aaa',
          cursor: 'pointer',
          fontSize: 16,
          verticalAlign: 'middle',
        }}>
            <EditOutlined onClick={onclick} />
        </span>
      </Tooltip>
    }
  </div>;
}

export function PromptEngineerNick({item}: {item: MaterialModelProps}) {
    return <div className="loras-image-card-info">
        <span style={{ color: 'black' }}>工程师：</span>
        <a>{item.promptEngineerNick}</a>
    </div>
}