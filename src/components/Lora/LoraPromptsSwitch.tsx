import React, {FC} from 'react';
import {MaterialType} from '@/services/MaterialInfoController';
import SystemConfigSwitch from '@/components/Common/SystemConfigSwitch';

interface LoraPromptsSwitchProps {
	value: string;
	tag?: string;
	materialType?: MaterialType;
	onChange: (value: string, tag?: string) => void;
}

export const formatPrompt = (prompt) => {
	if (!prompt) return '';

	let newVar = prompt?.replace(/\\n/g, '\n') || '';
	newVar = newVar.replaceAll('\\\'', '\'');
	newVar = newVar.replaceAll('\\\"', '\"');
	return newVar;
};

export const LabelTypeItems =
	{
		'cloth': [
			{
				label: '默认打标',
				key: 'TRAIN_LABEL_CLOTH_PROMPT',
				tag: 'default',
			},
			{
				label: '精准打标',
				key: 'TRAIN_LABEL_CLOTH_DETAILS_PROMPT',
				tag: 'details',
			},
			{
				label: '极简打标',
				key: 'TRAIN_LABEL_CLOTH_MINI_PROMPT',
				tag: 'mini',
			},
		],
		'scene': [
			{
				label: '默认打标',
				key: 'TRAIN_LABEL_SCENE_PROMPT',
				tag: 'default',
			},
		],
		'face': [
			{
				label: '默认打标',
				key: 'TRAIN_LABEL_FACE_PROMPT',
				tag: 'default',
			},
			{
				label: '极简打标',
				key: 'TRAIN_LABEL_FACE_MINI_PROMPT',
				tag: 'mini',
			},
		],
	};

const LoraPromptsSwitch: FC<LoraPromptsSwitchProps> = ({value, tag, materialType = 'cloth', onChange}) =>
	<SystemConfigSwitch value={value} onChange={onChange} tag={tag} keyLabels={LabelTypeItems[materialType]}/>;


export default LoraPromptsSwitch;