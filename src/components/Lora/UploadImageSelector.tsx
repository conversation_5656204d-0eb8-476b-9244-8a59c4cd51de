import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Card, Flex, message, Modal, } from 'antd';
import IconFont from '@/components/IconFont';
import {
  getAllUploadImages,
  getTrainDetail,
} from '@/services/MaterialModelController';
import { MinusOutlined, PlusOutlined} from '@ant-design/icons';
import ImgPreview from '@/components/ImgPreview';

interface ImageSelectorProps {
  modelId?: number;
  value?: Array<string>;
  maxChoose?: number;
  onChange: (value: Array<string>) => void;
  onCancel: () => void;
}

const UploadImageSelector: React.FC<ImageSelectorProps> = ({ modelId, value = [], maxChoose = 3, onChange, onCancel }) => {
  const [selected, setSelected] = useState<string[]>(value);
  const [allImages, setAllImages] = useState<string[]>([]);
  const [previewImage, setPreviewImage] = useState<null | string>(null);
  const [previewImgList, setPreviewImgList] = useState< string[]>([]);

  useEffect(() => {
    if (modelId) {
      getTrainDetail({id: modelId}).then(res => {
        if(!res) return;
        setAllImages(getAllUploadImages(res));
      });
    }
  }, []);
  const addImage = (image: string) => {
    if (selected.length  >= maxChoose) {
      message.warning(`最多能选择${maxChoose}张图片`);
      return;
    }

    const newValue = [...selected, image];
    setSelected(newValue);
  };

  const delImage = (image: string) => {
    const newValue = selected.filter(e => e !== image);
    setSelected(newValue);
  };
  const ImageCard = ({ image, width }) => {
    return (<>
      <div>
        <img src={image} width={width} alt={''} onClick={() => {
          setPreviewImage(image);
          setPreviewImgList( allImages);
        }} style={{ cursor: 'pointer' }} />
      </div>
    </>);
  };
  const handleFinish = () => {
    if (selected.length === 0) {
      message.error('请选择至少一张图片');
      return;
    }

    onChange(selected);
  };

  return (
    <Modal open={true} onCancel={onCancel} onOk={handleFinish}
           width={'calc(100vw * 0.96 + 10px)'} centered>

      <Flex gap={8} vertical style={{ height: 'calc(100vh - 100px)', overflowY: 'auto' }}>

        <div className={'text16 weight'}>已选择: {selected.length}/{maxChoose}</div>

        <Card style={{ background: '#F5F5F5' }} styles={{ body: { padding: 0 } }}>
          <Flex gap={8} style={{ position: 'relative', width: '100%' }} wrap={'wrap'}>
            <>
              {selected.length > 0 && selected.map((image, index) => (
                <Flex vertical key={index} align={'center'} gap={4}
                      style={{
                        border: '1px dashed #e8e8e8',
                        borderRadius: 8,
                        padding: 4,
                        position: 'relative',
                        height: 'auto',
                      }}>
                  <ImageCard key={index} image={image} width={80} />
                  <Button size={'small'} onClick={() => delImage(image)} block icon={<MinusOutlined />}>删除</Button>
                  <div style={{ position: 'absolute', right: 8, top: 8 }}>
                    <IconFont type={'icon-gou-lan'} style={{ fontSize: 16, color: '#366EF4' }} />
                  </div>
                </Flex>
              ))}

              {(selected.length <= 0) &&
                <Flex vertical gap={8} align={'center'} justify={'center'} className={'width-100'}
                      style={{ padding: 16 }}>
                  <IconFont type={'icon-kongzhuangtai'} style={{ fontSize: 32, color: '#000000' }} />
                  <div className={'text16 font-pf color-b'}>未选择</div>
                </Flex>
              }
            </>
          </Flex>
        </Card>

        <div className={'text16 weight'}>请选择:</div>

        <Flex gap={8} wrap>
          {allImages.length > 0 && allImages.map((image, index) => (
            <Flex vertical key={index} align={'center'} gap={4}
                  style={{
                    border: previewImage === image ? '2px solid #366EF4' : '1px dashed #e8e8e8',
                    borderRadius: 8, padding: 4, position: 'relative',
                  }}>
              <ImageCard image={image} width={120} />

              {!selected.includes(image) &&
                <Button size={'small'} onClick={() => addImage(image)} block icon={<PlusOutlined />}>添加</Button>
              }

              {selected.includes(image) &&
                <Button size={'small'} onClick={() => delImage(image)} block icon={<MinusOutlined />}>删除</Button>
              }

              {selected.includes(image) &&
                <div style={{ position: 'absolute', right: 8, top: 8 }}>
                  <IconFont type={'icon-gou-lan'} style={{ fontSize: 24, color: '#366EF4' }} />
                </div>
              }
            </Flex>
          ))}
        </Flex>
      </Flex>

      {previewImage !== null && (
        <ImgPreview
          previewVisible={!!previewImage}
          handleCancelPreview={() => {
            setPreviewImage(null);
            setPreviewImgList([]);
          }}
          previewImage={previewImage}
          needSwitch={!!previewImgList}
          previewIdx={previewImgList.findIndex(item => item === previewImage)}
          previewImgs={previewImgList}
          showTools={false}
        />
      )}
    </Modal>
  );
};

export default UploadImageSelector;