import React, { useEffect, useRef, useState } from 'react';
import {
  assignPlatformOperator,
  confirmTrainLora,
  cutoutAgain,
  FileVO, getAllUploadImages,
  getTrainDetail,
  ModelTrainDetailVO,
  updateLabelFiles,
} from '@/services/MaterialModelController';
import { Button, Col, Flex, Form, Input, InputNumber, message, Modal, Radio, Row, Select, Tooltip } from 'antd';
import IconFont from '@/components/IconFont';
import { batchDownload, uploadFile } from '@/services/FileController';
import { download, getUserInfo, isProdEnv } from '@/utils/utils';
import { RollbackOutlined } from '@ant-design/icons';
import ImgPreview from '@/components/ImgPreview';
import TextArea from 'antd/lib/input/TextArea';
import LoraPromptsSwitch from '@/components/Lora/LoraPromptsSwitch';
import SystemConfigSwitch from '@/components/Common/SystemConfigSwitch';
import { queryConfigByKeys } from '@/services/SystemController';

interface TrainDetailProps {
  id: number;
  onComplete: () => void;
}

const LoraTrainDetail: React.FC<TrainDetailProps> = ({ id, onComplete }) => {
  const userInfo = getUserInfo();
  const DEFAULT_TEST_NUM = 1;

  const [previewImage, setPreviewImage] = useState('');

  //训练详情页
  const [showTrainDetailModal, setShowTrainDetailModal] = useState(true);
  const [selectedTrainModelDetail, setSelectedTrainModelDetail] = useState<ModelTrainDetailVO>();

  const [groupedLabelFiles, setGroupedLabelFiles] = useState<Map<string, FileVO[]>>(new Map());
  const [showCutout, setShowCutout] = useState(false);
  const [showSplitTags, setShowSplitTags] = useState(false);
  const [showOriginal, setShowOriginal] = useState(true);

  //更新打标词
  const [updateTxtBtnEnabled, setUpdateTxtBtnEnabled] = useState(false);
  const [updateTxtFiles, setUpdateTxtFiles] = useState<Map<string, string>>(new Map());

  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewIdx, setPreviewIdx] = useState();
  const [previewImgs, setPreviewImgs] = useState();

  //指定平台运营
  const [relatedOperatorMobile, setRelatedOperatorMobile] = useState<string>('');
  const [allOperators, setAllOperators] = useState<any[]>([]);

  //打标图片编辑
  const [labelImgFiles, setLabelImgFiles] = useState<Map<string, {
    filePath: string,
    url: string,
    deleted: boolean
  }>>(new Map());
  const labelImgInputRefs = useRef({});

  //替换打标词输入框
  const replaceLabelTagsInputRefs = useRef([]);

  //关联的测试脸和场景，训练完成后自动生成图片
  const [relatedTestFaces, setRelatedTestFaces] = useState<number[]>();
  const [relatedTestScenes, setRelatedTestScenes] = useState<number[]>();
  const [relatedTestNum, setRelatedTestNum] = useState(0);
  const [testClothCollocation, setTestClothCollocation] = useState({});

  //是否需要重新抠图
  const [needCutoutAgain, setNeedCutoutAgain] = useState(false);
  const [cutoutKeyword, setCutoutKeyword] = useState('');
  const [loraTrainType, setLoraTrainType] = useState('flux');

  const [canShowTrainParams, setCanShowTrainParams] = useState(false);

  const [captionPrompt, setCaptionPrompt] = useState<string>('');
  const [waterMarkDesc, setWaterMarkDesc] = useState<string>('');
  const [waterMarkDescType, setWaterMarkDescType] = useState<string>('none');
  const [waterMarkDescPos, setWaterMarkDescPos] = useState<string>();

  //是否需要预处理被裁剪的人脸 Y|N
  const [preprocessCensoredFace, setPreprocessCensoredFace] = useState<string>('N');
  const [noshowFace, setNoshowFace] = useState('N')

  //重新抠图时，是否重新进行图片分析预处理
  const [prepareViewAgainWhenCutoutAgain, setPrepareViewAgainWhenCutoutAgain] = useState(false);
  const [imageSize, setImageSize] = useState('1024');
  const [trainRepeatTimes, setTrainRepeatTimes] = useState<number>(15);

  const defaultTrainParams = {
    lr: 0.00020,
    contentOrStyle: 'content',
    rank: 32,
    alpha: 16,
    dropout: 0.20,
    trainExtInfo: selectedTrainModelDetail?.clothLoraTrainDetail?.trainExtInfo,
  };

  const [trainParams, setTrainParams] = useState(defaultTrainParams);

  //是否显示确认训练的弹窗
  const [showConfirmLoraModal, setShowConfirmLoraModal] = useState(false);

  const [maxTrainStep, setMaxTrainStep] = useState<number>();

  const [confirmLoraForm] = Form.useForm();

  const defaultFluxSteps = 2500;

  const [downloading, setDownloading] = useState(false);

  useEffect(() => {
    if (relatedTestFaces && relatedTestFaces?.length > 0 && relatedTestScenes && relatedTestScenes?.length > 0) {
      setRelatedTestNum(DEFAULT_TEST_NUM);
    } else {
      setRelatedTestNum(0);
    }
  }, [relatedTestFaces, relatedTestScenes]);

  useEffect(() => {
    setLoraTrainType('flux');
    setMaxTrainStep(defaultFluxSteps);
    setTrainParams({
      ...defaultTrainParams,
      contentOrStyle: getDefaultContentOrStyle(),
    });

  }, [showConfirmLoraModal]);

  useEffect(() => {
    if (waterMarkDescType === 'none') {
      setWaterMarkDescPos('');
      setWaterMarkDesc('');
    } else if (waterMarkDescType && waterMarkDescPos) {
      setWaterMarkDesc(waterMarkDescType + ' in the ' + waterMarkDescPos + ' corner');
    }
  }, [waterMarkDescType, waterMarkDescPos]);

  useEffect(() => {
    getTrainDetail({ id: id }).then(res => {
      if (res) {
        setSelectedTrainModelDetail(res);
        if (res.labelRetFiles) {
          setGroupedLabelFiles(groupFilesByFileDir(res.labelRetFiles));
        }
        setShowTrainDetailModal(true);
        setShowCutout(false);
        setShowSplitTags(false);
        setRelatedTestFaces(res.clothLoraTrainDetail.testFaces || undefined);
        setRelatedTestScenes(res.clothLoraTrainDetail.testScenes || undefined);
        setMaxTrainStep(defaultFluxSteps);
        setCaptionPrompt(res?.clothLoraTrainDetail?.captionPrompt?.replace(/\\n/g, '\n').replace(/\\"/g, '"') || '');
        setNoshowFace(res?.clothLoraTrainDetail?.noshowFace || 'N');
        setTrainRepeatTimes(res?.clothLoraTrainDetail?.repeatTimes || (res.clothLoraTrainDetail.materialType === 'face' ? 15 : 5));
        replaceLabelTagsInputRefs.current = [];

      } else {
        setSelectedTrainModelDetail(undefined);
        setShowTrainDetailModal(false);
      }
    });

    if (!isProdEnv()){
      setCanShowTrainParams(true);
    } else {
      queryConfigByKeys(['SHOW_TRAIN_PARAMS_WHITELIST']).then(res=>{
        if(!res) return;
        setCanShowTrainParams(JSON.parse(res['SHOW_TRAIN_PARAMS_WHITELIST']).includes(userInfo?.id || 0));
      })
    }
  }, []);

  useEffect(() => {
    if (!showTrainDetailModal && onComplete) {
      onComplete();
    }
  }, [showTrainDetailModal]);

  const getDefaultContentOrStyle = () => {
    // if (selectedTrainModelDetail?.clothLoraTrainDetail?.materialType === 'scene') {
    //   return 'style';
    // }
    return 'content';
  };

  const onUpdateTxtFiles = async () => {
    setUpdateTxtBtnEnabled(false);

    let labelFileEditItems = [];

    for (const [filePath, content] of updateTxtFiles) {
      // @ts-ignore
      labelFileEditItems.push({
        filePath: filePath,
        updateTextContent: content,
        deleted: false,
      });
    }

    for (const [filePath, item] of labelImgFiles) {
      // @ts-ignore
      labelFileEditItems.push({
        filePath: filePath,
        updateImgUrl: item.url,
        deleted: item.deleted,
      });
    }

    if (labelFileEditItems) {
      updateLabelFiles({
        labelId: selectedTrainModelDetail?.clothLoraTrainDetail && selectedTrainModelDetail?.clothLoraTrainDetail.label ? selectedTrainModelDetail?.clothLoraTrainDetail.label.taskId : null,
        items: labelFileEditItems,

      }).then(res => {
        if (res) {
          message.success('更新成功', 3);
          setUpdateTxtBtnEnabled(false);
        } else {
          message.error('更新失败');
          setUpdateTxtBtnEnabled(true);
        }
      });
    }
  };

  function groupFilesByFileDir(labelRetFiles: FileVO[]): Map<string, FileVO[]> {
    const groupedFiles = new Map<string, FileVO[]>();

    for (const file of labelRetFiles) {
      if (!groupedFiles.has(file.fileDir)) {
        groupedFiles.set(file.fileDir, []);
      }
      groupedFiles.get(file.fileDir)!.push(file);
    }

    return groupedFiles;
  }

  function getLabelRetDirShowName(fileDir: string) {
    if (fileDir.includes('half')) {
      return '半身图';
    } else if (fileDir.includes('full')) {
      return '全身图';
    } else {
      return '特征';
    }
  }

  function loraConfirmed() {
    return loraConfirmedByModel(selectedTrainModelDetail);
  }

  function loraConfirmedByModel(model: any) {

    if (model && model.clothLoraTrainDetail && model.clothLoraTrainDetail['loraConfirmed'] === 'Y') {
      return true;
    }

    return !!(model && model.extInfo && model.extInfo['loraConfirmed'] === 'Y');
  }

  function loraTrainDetailOkText() {

    if (selectedTrainModelDetail?.status === 'ENABLED') {
      return 'lora训练完成';
    }

    if (selectedTrainModelDetail?.status === 'TESTING') {
      return '内部测试中';
    }

    if (findLabelFailTxtFileName().length > 0) {
      return '需要手工处理打标失败文件';
    }

    return loraConfirmed() ? '正在训练lora' : '设置lora参数';
  }


  const handlePreviewUrl = (url, idx = -1, imgList: string[] | [] = []) => {
    setPreviewImage(url);

    if (idx >= 0) {
      //@ts-ignore
      setPreviewIdx(idx);
      //@ts-ignore
      setPreviewImgs(imgList);
    } else {
      setPreviewIdx(undefined);
      setPreviewImgs(undefined);
    }

    setPreviewVisible(true);
  };

  const getServerUrl = (key) => {
    if (!selectedTrainModelDetail || !selectedTrainModelDetail?.clothLoraTrainDetail) return '';

    const target = selectedTrainModelDetail?.clothLoraTrainDetail[key];
    return target && target.serverUrl ? target.serverUrl : '';
  };

  function loraConfirmedTime() {

    if (selectedTrainModelDetail && selectedTrainModelDetail.clothLoraTrainDetail && selectedTrainModelDetail.clothLoraTrainDetail['loraConfirmedTime']) {
      return selectedTrainModelDetail.clothLoraTrainDetail['loraConfirmedTime'];
    }

    if (selectedTrainModelDetail && selectedTrainModelDetail.extInfo && selectedTrainModelDetail.extInfo['loraConfirmedTime']) {
      return selectedTrainModelDetail.extInfo['loraConfirmedTime'];
    }

    return '';
  }

  function findLabelFailTxtFileName() {
    let ret: string[] = [];
    if (selectedTrainModelDetail && selectedTrainModelDetail?.labelRetFiles) {
      for (const f of selectedTrainModelDetail?.labelRetFiles) {
        let emptyTextContent = !f.fileDir.endsWith('/label') && (!f.textContent || f.textContent === '' || f.textContent.toLowerCase().includes('error'));

        if (f.type === 'text' && (emptyTextContent || f.textContent.toLowerCase().includes('a garment'))) {
          ret.push(f.fileName);
        }
      }
    }
    return ret;
  }

  const handleCancelPreview = () => {
    setPreviewVisible(false);
  };

  function getAllUploadImgs() {
    return getAllUploadImages(selectedTrainModelDetail);
  }

  function onLabelTextChange(e: React.ChangeEvent<HTMLTextAreaElement>, f: FileVO) {
    const newMap = new Map(updateTxtFiles);
    const key = f.fileDir + '/' + f.fileName;
    newMap.set(key, e.target.value);
    setUpdateTxtFiles(newMap);

    setUpdateTxtBtnEnabled(true);
  }

  function getTxtFileColorByFile(f: FileVO) {
    let filePath = f.fileDir + '/' + f.fileName;
    if (updateTxtFiles.has(filePath)) {
      return 'blue';
    }
    return '';
  }

  function setReplaceInputRef(index: number, el: HTMLInputElement | null) {
    // @ts-ignore
    return replaceLabelTagsInputRefs.current[index] = el;
  }

  function replaceTag(str: string, from: string, to: string): string {
    // 按逗号拆分字符串，并去除每个标签词的前后空格
    const tags = str.split(',').map(tag => tag.trim());

    // 检查是否包含 from 标签词
    const containsFrom = tags.some(tag => tag === from);

    // 如果不包含 from 标签词，直接返回原始字符串
    if (!containsFrom) {
      return str;
    }

    // 遍历标签词数组，替换包含 from 的标签词
    const replacedTags = tags.map(tag => {
      if (tag === from) {
        return to;
      }
      return tag;
    });

    // 将替换后的标签词数组重新用逗号拼接成新的字符串
    return replacedTags.join(', ');
  }

  function handleLabelImgChange(file: File, f: FileVO) {
    if (file) {
      uploadFile(file).then(res => {
        if (res) {
          let filePath = f.fileDir + '/' + f.fileName;
          const newLabelImgFiles = new Map(labelImgFiles);
          newLabelImgFiles.set(filePath, {
            filePath: f.fileDir + '/' + f.fileName,
            url: res,
            deleted: false,
          });
          setLabelImgFiles(newLabelImgFiles);
          setUpdateTxtBtnEnabled(true);
        }
      });
    }
  }

  function getImgFileColorByFile(f: FileVO) {
    let filePath = f.fileDir + '/' + f.fileName;
    if (labelImgFiles.has(filePath)) {
      if (labelImgFiles.get(filePath)?.deleted === true) {
        return 'red';
      }
      return 'blue';
    }
    return '';
  }


  function onConfirmLora() {
    if (selectedTrainModelDetail) {
      let trainExtInfo = trainParams['trainExtInfo'].toString();
      trainExtInfo = trainExtInfo && trainExtInfo.length > 0 ? JSON.parse(trainExtInfo) : {};

      confirmTrainLora({
        id: selectedTrainModelDetail.id,
        maxTrainStep: maxTrainStep,
        // testFaces: relatedTestFaces,
        // testScenes: relatedTestScenes,
        // testNum: relatedTestNum,
        // testClothCollocation,
        loraType: loraTrainType,

        lr: trainParams['lr'].toString(),
        contentOrStyle: trainParams['contentOrStyle'],
        rank: trainParams['rank'].toString(),
        alpha: trainParams['alpha'].toString(),
        dropout: trainParams['dropout'].toString(),
        trainExtInfo: canShowTrainParams? trainExtInfo : null,

      }).then(res => {
        if (res) {
          setShowTrainDetailModal(false);
          setSelectedTrainModelDetail(undefined);
          setShowConfirmLoraModal(false);
          message.info('提交成功，将继续训练');
        }
      });
    }
  }

  function onCutoutAgain() {
    if (selectedTrainModelDetail && needCutoutAgain) {
      cutoutAgain({
        id: selectedTrainModelDetail.id,
        cutoutKeyword: cutoutKeyword || '',
        captionPrompt,
        prepareViewAgainWhenCutoutAgain: prepareViewAgainWhenCutoutAgain ? prepareViewAgainWhenCutoutAgain : preprocessCensoredFace === 'N',
        imageSize,
        waterMarkDesc,
        trainRepeatTimes,
        preprocessCensoredFace,
        noshowFace: selectedTrainModelDetail?.clothLoraTrainDetail?.materialType === 'scene'? noshowFace: null,
      }).then(res => {
        if (res) {
          setShowTrainDetailModal(false);
          setSelectedTrainModelDetail(undefined);
          setShowConfirmLoraModal(false);
          setNeedCutoutAgain(false);

          message.info('提交成功，将重新抠图打标');
        }
      });
    }
  }


  return <>

    {/*查看lora训练详情，看抠图、打标结果，确认推进lora训练，浮层弹窗页面*/}

    {showTrainDetailModal &&
      <Modal
        open={showTrainDetailModal}
        centered={true}
        onCancel={() => {
          setShowTrainDetailModal(false);
        }}
        width={'auto'}
        closable={false}
        zIndex={1200}
        footer={[
          <Button key="cancel" style={{ marginRight: 8 }}
                  onClick={() => {
                    setShowTrainDetailModal(false);
                    setUpdateTxtBtnEnabled(false);
                  }}>
            取消
          </Button>,

          <Flex key={'operator'} style={{ display: 'none', margin: '0 10px', alignItems: 'center' }}>
            <div>负责人：</div>
            <Select style={{ width: '80px' }}
                    value={relatedOperatorMobile}
                    onChange={value => {
                      setRelatedOperatorMobile(value);
                      assignPlatformOperator({ id: selectedTrainModelDetail?.id, operatorMobile: value }).then(res => {
                        if (res) {
                          message.success('设置成功');
                        } else {
                          message.error('操作失败');
                        }
                      });
                    }}>
              {allOperators.length > 0 && allOperators.map((item) => (
                <Select.Option key={item.id} value={item.mobile}>{item.nickName}</Select.Option>
              ))}
            </Select>
          </Flex>,

          <Button key="updateTxt" disabled={!updateTxtBtnEnabled} type="primary" onClick={onUpdateTxtFiles}>
            提交打标变更
          </Button>,

          <Button
            key="submit"
            type="primary"
            onClick={() => {
              setShowConfirmLoraModal(true);
              if (selectedTrainModelDetail?.merchantPreference && selectedTrainModelDetail.merchantPreference.preferences && selectedTrainModelDetail.merchantPreference.preferences.length > 0) {
                setTestClothCollocation(selectedTrainModelDetail.merchantPreference.preferences[0].clothCollocation);
              }
            }}
            disabled={loraConfirmed() || !selectedTrainModelDetail?.labelRetFiles || updateTxtBtnEnabled || findLabelFailTxtFileName().length > 0}
          >
            {loraTrainDetailOkText()}
          </Button>,
        ]}
        styles={{
          mask: {
            backgroundColor: 'rgba(0,0,0,0.4)',
          },
        }}
      >
        <div className="lora-detail-content">
          <div className={'lora-detail-left'}>
            <div className={'lora-detail-left-inner'}>
              <div className={'lora-left-inner-top'}>
                <div className="font-pf text14 weight">模型</div>
                <img alt="cloth model"
                     style={{ cursor: 'pointer' }}
                     onClick={() => handlePreviewUrl(selectedTrainModelDetail?.showImage)}
                     src={selectedTrainModelDetail?.showImage} />
                <div className="font-pf text14">{selectedTrainModelDetail?.name}</div>
              </div>

              <div className={'lora-detail-pair'}>
                <Flex align={'center'} className="font-pf text14 weight">
                  操作人
                  <div className={"text12" + (selectedTrainModelDetail?.clothLoraTrainDetail?.watermarkOrigin? "":" color-error")}>
                    ({selectedTrainModelDetail?.clothLoraTrainDetail?.watermarkOrigin?'已标水印':'未标水印'})
                    {selectedTrainModelDetail?.clothLoraTrainDetail?.preprocessCensoredFace === 'Y'?'  (白头)':''}
                  </div>
                </Flex>
                <div className="font-pf text12">{selectedTrainModelDetail?.operatorNick}</div>
              </div>
              <div className={'lora-detail-pair'}>
                <div className="font-pf text14 weight">创建时间</div>
                <div className="font-pf text12">{selectedTrainModelDetail?.createTime}</div>
              </div>

              {selectedTrainModelDetail?.clothLoraTrainDetail?.prepareView && (
                <div className={'lora-detail-pair'}>
                  <div className="font-pf text14 weight">预处理
                    <span className={'text10'} style={{ marginLeft : 4 }}>
                      {selectedTrainModelDetail?.clothLoraTrainDetail?.prepareView?.taskId}
                    </span>
                    <span className={'text10 color-a0 normal margin-left-4'}>{getServerUrl('prepareView')}</span>
                  </div>
                  <div className="font-pf text12">
                    {selectedTrainModelDetail?.clothLoraTrainDetail?.prepareView?.status === 'COMPLETED'
                      ? (<>{selectedTrainModelDetail?.prepareViewFinishTime} <span
                        style={{ color: '#52c41a' }}>完成</span></>)
                      : <span style={{ color: '#666' }}>未完成</span>}
                  </div>
                </div>
              )}

              {selectedTrainModelDetail?.cutoutFiles &&
                <div className={'lora-detail-pair'}>
                  <div className="font-pf text14 weight">抠图
                    <span className={'text10'} style={{ marginLeft : 4 }}>
                      {selectedTrainModelDetail?.clothLoraTrainDetail?.cutout?.taskId}
                    </span>
                    <span className={'text10 color-a0 normal margin-left-4'}>{getServerUrl('cutout')}</span>
                  </div>
                  <div className="font-pf text12">
                    {selectedTrainModelDetail?.cutoutFiles && selectedTrainModelDetail?.cutoutFiles?.length > 0
                      ? (<>{selectedTrainModelDetail?.cutoutFinishTime} <span
                        style={{ color: '#52c41a' }}>完成</span></>)
                      : <span style={{ color: '#666' }}>未完成</span>}
                  </div>
                </div>
              }

              <div className={'lora-detail-pair'}>
                <div className="font-pf text14 weight">打标
                  <span className={'text10'} style={{ marginLeft : 4 }}>
                    {selectedTrainModelDetail?.clothLoraTrainDetail?.label?.taskId}
                  </span>
                  <span className={'text10 color-a0 normal margin-left-4'}>{getServerUrl('label')}</span>
                </div>
                <div className="font-pf text12">
                  {selectedTrainModelDetail?.labelRetFiles && selectedTrainModelDetail?.labelRetFiles?.length > 0
                    ? (<>{selectedTrainModelDetail?.labelFinishTime} <span style={{ color: '#52c41a' }}>完成</span></>)
                    : <span style={{ color: '#666' }}>未完成</span>}
                </div>
              </div>

              <div className={'lora-detail-pair'}>
                <div className="font-pf text14 weight">训练
                  <span className={'text10'} style={{ marginLeft : 4 }}>
                    {selectedTrainModelDetail?.clothLoraTrainDetail?.lora?.taskId}
                  </span>
                  <span className={'text10 color-a0 normal margin-left-4'}>{getServerUrl('lora')}</span>
                </div>
                {selectedTrainModelDetail && !selectedTrainModelDetail?.loraStatus &&
                  <div className="font-pf text12"><span style={{ color: '#666' }}>状态：未开始</span></div>
                }
                {loraConfirmed() && <>
                  <div className="font-pf text12" style={{ margin: '3px 0' }}>{loraConfirmedTime()}<span
                    style={{ color: '#52c41a', marginLeft: '8px' }}>已确认</span></div>
                  <div style={{
                    backgroundColor: '#f9f9f9',
                    border: '1px solid #f0f0f0',
                    borderRadius: '2px',
                    padding: '3px 6px',
                    marginBottom: '2px',
                    fontSize: '12px',
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gridGap: '2px 8px',
                  }}>
                    <div><b>类型:</b> {selectedTrainModelDetail?.clothLoraTrainDetail?.loraType || 'sdxl'}</div>
                    <div><b>次数:</b> {selectedTrainModelDetail?.clothLoraTrainDetail?.maxTrainStep || 4000}</div>

                    {selectedTrainModelDetail?.clothLoraTrainDetail?.lr &&
                      <div><b>学习率:</b> {selectedTrainModelDetail?.clothLoraTrainDetail?.lr}</div>
                    }
                    {selectedTrainModelDetail?.clothLoraTrainDetail?.contentOrStyle &&
                      <div><b>内容:</b> {selectedTrainModelDetail?.clothLoraTrainDetail?.contentOrStyle}</div>
                    }

                    {selectedTrainModelDetail?.clothLoraTrainDetail?.rank &&
                      <div><b>Rank:</b> {selectedTrainModelDetail?.clothLoraTrainDetail?.rank}</div>
                    }
                    {selectedTrainModelDetail?.clothLoraTrainDetail?.alpha &&
                      <div><b>Alpha:</b> {selectedTrainModelDetail?.clothLoraTrainDetail?.alpha}</div>
                    }

                    {selectedTrainModelDetail?.clothLoraTrainDetail?.dropout &&
                      <div><b>Dropout:</b> {selectedTrainModelDetail?.clothLoraTrainDetail?.dropout}</div>
                    }
                    {selectedTrainModelDetail?.clothLoraTrainDetail?.resolution &&
                      <div><b>Res:</b> {selectedTrainModelDetail?.clothLoraTrainDetail?.resolution}</div>
                    }
                  </div>
                </>}

                {selectedTrainModelDetail && selectedTrainModelDetail?.loraStatus === 'QUEUED' &&
                  <div className="font-pf text12">
                    <span style={{ color: '#1890ff' }}>状态：排队中</span>
                  </div>
                }
                {selectedTrainModelDetail && selectedTrainModelDetail?.loraStatus === 'RUNNING' && (
                  <>
                    <div className="font-pf text12">{selectedTrainModelDetail?.loraStartTime}<span
                      style={{ color: '#fa8c16', marginLeft: '8px' }}>开始</span></div>
                    <div className="font-pf text12" style={{ marginTop: 4 }}><span
                      style={{ color: '#fa8c16' }}>任务状态：运行中</span></div>
                  </>
                )}
                {selectedTrainModelDetail && selectedTrainModelDetail?.loraStatus === 'COMPLETED' &&
                  <>
                    <div className="font-pf text12">{selectedTrainModelDetail?.loraStartTime}<span
                      style={{ color: '#52c41a', marginLeft: '8px' }}>开始</span></div>
                    <div className="font-pf text12"
                         style={{ marginTop: 4 }}>{selectedTrainModelDetail?.loraFinishTime}<span
                      style={{ color: '#52c41a', marginLeft: '8px' }}>完成</span></div>
                    <div className="font-pf text12" style={{ marginTop: 4 }}><span
                      style={{ color: '#52c41a' }}>状态：已完成</span></div>
                  </>
                }
              </div>

              <div className={'lora-detail-pair'}>
                <div className="font-pf text14 weight">交付时间</div>
                <div
                  className="font-pf text12">{selectedTrainModelDetail && selectedTrainModelDetail?.extInfo ? selectedTrainModelDetail?.extInfo['deliveryTime'] : ''}</div>
              </div>
              <div className={'lora-detail-pair lora-detail-pair-row'}>
                <div className="font-pf text14 weight">模型ID</div>
                <div className="font-pf text12">{selectedTrainModelDetail?.id}</div>
              </div>
            </div>
          </div>
          <div className="lora-detail-right">
            <Flex align={'baseline'}>
              <div className="font-pf text14 weight"
                   style={{ marginTop: 10, cursor: 'pointer', display: 'flex', alignItems: 'center' }} onClick={() => {
                navigator.clipboard.writeText(selectedTrainModelDetail?.clothLoraTrainDetail?.clothDir).then(() => {
                  message.info('目录已复制到剪贴板');
                });
              }}>
                原图<span style={{
                fontSize: 12,
                textDecoration: 'underline',
              }}>{selectedTrainModelDetail?.clothLoraTrainDetail?.clothDir}</span>
                <IconFont type={'icon-icon_fuzhi'} style={{ fontSize: 18, marginLeft: 5 }} />
              </div>

              <Button type={'default'} size={'small'} style={{ borderRadius: 8, fontSize: 12, marginLeft: 8 }}
                      onClick={() => setShowOriginal(!showOriginal)}>{showOriginal ? '隐藏' : '展开'}</Button>

              <Button type={'default'} size={'small'}
                      loading={downloading}
                      style={{ borderRadius: 8, fontSize: 12, marginLeft: 8 }}
                      onClick={() => {
                        setDownloading(true);

                        let urls = getAllUploadImgs();
                        batchDownload(urls, selectedTrainModelDetail?.name + '_' + new Date().getTime()).then(res => {
                          if (res) {
                            download(res);
                            message.success('下载完成');
                          }
                        }).finally(() => setDownloading(false));

                      }}>批量下载原图
              </Button>

            </Flex>

            <div className="lora-img-list">
              {showOriginal && getAllUploadImgs().map((f: string, index: number) => (
                <div key={index}
                     className="lora-detail-img-item">
                  <img src={f} alt={''}
                       onClick={() => handlePreviewUrl(f, index, getAllUploadImgs())} />
                </div>
              ))}
            </div>

            {/*抠图结果*/}
            {selectedTrainModelDetail?.cutoutFiles && selectedTrainModelDetail?.cutoutFiles?.length > 0 && (
              <>
                <Flex align={'baseline'}>
                  <div className="font-pf text14 weight"
                       style={{ marginTop: 10, cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                       onClick={() => {
                         navigator.clipboard.writeText(selectedTrainModelDetail?.clothLoraTrainDetail?.clothDir).then(() => {
                           message.info('目录已复制到剪贴板');
                         });
                       }}
                  >
                    <span>抠图结果：</span>
                    <span style={{
                      fontSize: 12,
                      textDecoration: 'underline',
                    }}>{selectedTrainModelDetail?.clothLoraTrainDetail?.cutoutRetDir}</span>
                    <IconFont type={'icon-icon_fuzhi'} style={{ fontSize: 18, marginLeft: 5, marginRight: 5 }} />
                  </div>
                  <Button type={'default'} size={'small'} style={{ borderRadius: 8, fontSize: 12 }}
                          onClick={() => setShowCutout(!showCutout)}>{showCutout ? '隐藏' : '展开'}</Button>
                </Flex>

                <div className="lora-img-list">
                  {showCutout && selectedTrainModelDetail && selectedTrainModelDetail?.cutoutFiles && selectedTrainModelDetail?.cutoutFiles.map((f: FileVO, index: number) => {
                    if (f.type === 'img') {
                      return (
                        <div key={index} className="lora-detail-img-item">
                          <img
                            src={f.imgUrl}
                            alt={f.fileName}
                            onClick={() => handlePreviewUrl(f.imgUrl, index, selectedTrainModelDetail?.cutoutFiles.map((f: FileVO) => (f.imgUrl)))}
                          />
                          <div style={{ maxWidth: 160, fontSize: 12 }}>{f.fileName}</div>
                        </div>
                      );
                    } else {
                      return (
                        <div key={index} className="lora-detail-img-item">
                          <textarea
                            value={updateTxtFiles.get(f.fileDir + '/' + f.fileName) || f.textContent}
                            style={{ fontSize: 12 }}
                            disabled={true}
                            onChange={e => onLabelTextChange(e, f)}
                          />
                          <div
                            style={{
                              maxWidth: 160,
                              fontSize: 12,
                              color: getTxtFileColorByFile(f),
                            }}
                          >
                            {f.fileName}
                          </div>
                        </div>
                      );
                    }
                  })}
                </div>
              </>
            )}

            {selectedTrainModelDetail && selectedTrainModelDetail?.splitTags &&
              <>
                <Flex align={'baseline'}>
                  <div className="font-pf text14 weight"
                       style={{ display: 'flex', alignItems: 'center', marginTop: 8 }}>
                    <span style={{ color: 'black' }}>全局快速替换打标词：</span>
                  </div>
                  <Button type={'default'} size={'small'} style={{ borderRadius: 8, fontSize: 12 }}
                          onClick={() => setShowSplitTags(!showSplitTags)}>{showSplitTags ? '隐藏' : '展开'}</Button>
                </Flex>
                {showSplitTags &&
                  <div style={{
                    border: '1px solid #d9d9d9',
                    padding: '16px',
                    backgroundColor: '#f5f5f5',
                    borderRadius: '8px',
                  }}>
                    <h4>全局快速替换打标词</h4>
                    <Row gutter={[16, 16]} style={{ display: 'flex', flexWrap: 'wrap' }}>
                      {selectedTrainModelDetail && selectedTrainModelDetail.splitTags.map((tag, index) => (
                        <Col
                          key={index}
                          style={{
                            flex: '1 1 auto', // 使列宽度自适应
                            borderRight: index !== selectedTrainModelDetail.splitTags.length - 1 ? '1px solid #d9d9d9' : 'none',
                            textAlign: 'center',
                            minWidth: '150px',
                            maxWidth: '250px',
                          }}
                        >
                          <div style={{
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'space-between',
                            height: '100%',
                          }}>
                            {/* 第一行：展示所有原始的 tag */}
                            <div style={{ padding: '8px', borderBottom: '1px solid #d9d9d9' }}>
                              <span>{tag}</span>
                            </div>

                            {/* 第二行：Input 输入框 */}
                            <div style={{ padding: '8px', borderBottom: '1px solid #d9d9d9' }}>
                              <input defaultValue={''} style={{ width: '100%' }} placeholder={`替换 ${tag}`}
                                     ref={el => setReplaceInputRef(index, el)} />
                            </div>

                            {/* 第三行：确定替换按钮 */}
                            <div style={{ padding: '8px' }}>
                              <Button style={{ width: '100%' }}
                                      onClick={() => {
                                        //@ts-ignore
                                        const inputValue = replaceLabelTagsInputRefs.current[index].value;
                                        if (inputValue) {
                                          const newMap = new Map(updateTxtFiles);
                                          for (const f of selectedTrainModelDetail?.labelRetFiles) {
                                            if (f.textContent && !f.fileDir.endsWith('label')) {
                                              const key = f.fileDir + '/' + f.fileName;
                                              let oldVal = updateTxtFiles.get(key) || f.textContent;
                                              let newVal = replaceTag(oldVal, selectedTrainModelDetail.splitTags[index], inputValue);
                                              if (newVal !== oldVal) {
                                                newMap.set(key, newVal);
                                              }
                                            }
                                          }

                                          setUpdateTxtFiles(newMap);
                                          setUpdateTxtBtnEnabled(true);
                                        }

                                      }}>全局替换
                              </Button>
                            </div>
                          </div>
                        </Col>
                      ))}
                    </Row>
                  </div>
                }
              </>
            }

            {/*打标结果*/}
            {selectedTrainModelDetail && selectedTrainModelDetail?.labelRetFiles && groupedLabelFiles && Array.from(groupedLabelFiles.entries()).map(([fileDir, files]) => (
              <>
                <div className="font-pf text14 weight"
                     style={{ marginTop: 10, cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                     onClick={() => {
                       navigator.clipboard.writeText(fileDir).then(() => {
                         message.info('目录已复制到剪贴板');
                       });
                     }}>
                  <span style={{ color: 'blue' }}>{getLabelRetDirShowName(fileDir)}打标结果：</span><span
                  style={{ fontSize: 12, textDecoration: 'underline' }}>{fileDir}</span>
                  <IconFont type={'icon-icon_fuzhi'} style={{ fontSize: 18, marginLeft: 5 }} />
                </div>

                {findLabelFailTxtFileName().length > 0 &&
                  <div style={{ color: 'red', fontSize: 16, fontWeight: 500 }}>
                    请手工更新打标失败文件后，提交打标变更：{findLabelFailTxtFileName().join(', ')}
                  </div>
                }

                <div className="lora-img-list">
                  {files.sort((a, b) => a.fileName.localeCompare(b.fileName)).map((f: FileVO, index: number) => (
                    <div key={index} className="label-ret-item"
                         onDrop={(event) => {
                           event.preventDefault();
                           //只响应图片控件的拖拽事件
                           if (f.type !== 'img') {
                             return;
                           }
                           let filePath = f.fileDir + '/' + f.fileName;
                           let deleted = labelImgFiles.get(filePath)?.deleted;
                           if (deleted) {
                             message.error('该图片已被删除，无法替换');
                           } else {
                             const file = event.dataTransfer.files[0];
                             handleLabelImgChange(file, f);
                           }
                         }}
                         onDragOver={(e) => {
                           e.preventDefault();
                           if (f.type !== 'img') {
                             return;
                           }
                         }}>
                      {(() => {
                        // 如果是图片，则显示图片
                        if (f.type === 'img') {
                          let filePath = f.fileDir + '/' + f.fileName;

                          // 如果文件被删除，则不显示
                          let deleted = labelImgFiles.get(filePath)?.deleted;

                          return <>
                            <div style={{ position: 'relative' }}>
                              <img
                                src={labelImgFiles.get(filePath)?.url ? labelImgFiles.get(filePath)?.url : f.imgUrl}
                                alt={''}
                                onClick={() => handlePreviewUrl(labelImgFiles.get(filePath)?.url ? labelImgFiles.get(filePath)?.url : f.imgUrl)} />

                              {/*上传新的打标图片*/}
                              <Tooltip title={'点击或拖拽至此，替换图片'}>
                                <IconFont type={'icon-icon_qiehuan'}
                                          style={{
                                            position: 'absolute',
                                            top: 0,
                                            right: 0,
                                            cursor: 'pointer',
                                            fontSize: 24,
                                          }}
                                          onClick={() => {
                                            if (deleted) {
                                              message.error('该图片已被删除，无法替换');
                                            } else {
                                              labelImgInputRefs.current[filePath].click();
                                            }
                                          }} />
                              </Tooltip>

                              {/*删除打标图片*/}
                              {!deleted && (
                                <Tooltip title={'删除图片'} placement="bottom">
                                  <IconFont
                                    style={{ position: 'absolute', top: 36, right: 0, cursor: 'pointer', fontSize: 24 }}
                                    type={'icon-icon_shanchu'}
                                    onClick={() => {
                                      const newLabelImgFiles = new Map(labelImgFiles);
                                      newLabelImgFiles.set(filePath, {
                                        filePath: f.fileDir + '/' + f.fileName,
                                        url: '',
                                        deleted: true,
                                      });
                                      setLabelImgFiles(newLabelImgFiles);
                                      setUpdateTxtBtnEnabled(true);
                                    }} />
                                </Tooltip>
                              )}

                              {/*恢复删除的图片*/}
                              {deleted && (
                                <Tooltip title={'撤销删除'} placement="bottom">
                                  <RollbackOutlined
                                    style={{
                                      position: 'absolute',
                                      top: 36,
                                      right: 4,
                                      cursor: 'pointer',
                                      fontSize: 18,
                                      color: 'red',
                                    }}
                                    onClick={() => {
                                      const newLabelImgFiles = new Map(labelImgFiles);
                                      newLabelImgFiles.delete(filePath);
                                      setLabelImgFiles(newLabelImgFiles);
                                      setUpdateTxtBtnEnabled(true);
                                    }} />
                                </Tooltip>
                              )}

                              <input
                                type="file"
                                ref={(el) => labelImgInputRefs.current[filePath] = el}
                                onChange={(e) => {
                                  // @ts-ignore
                                  const file = e.target.files[0];
                                  handleLabelImgChange(file, f);
                                }}
                                accept="image/jpeg,image/jpg"
                                style={{ display: 'none' }}
                              />
                            </div>

                            {deleted && <div style={{ maxWidth: 160, fontSize: 12, color: getImgFileColorByFile(f) }}>
                              <del>{f.fileName}</del>
                            </div>}
                            {!deleted && <div style={{
                              maxWidth: 160,
                              fontSize: 12,
                              color: getImgFileColorByFile(f),
                            }}>{f.fileName}</div>}

                          </>;

                          // txt文本
                        } else {
                          return <>
                              <textarea value={updateTxtFiles.get(f.fileDir + '/' + f.fileName) || f.textContent}
                                        style={{ fontSize: 12 }}
                                        onChange={e => onLabelTextChange(e, f)}
                              />
                            <div style={{
                              maxWidth: 160,
                              fontSize: 12,
                              color: getTxtFileColorByFile(f),
                            }}>{f.fileName}</div>
                          </>;
                        }
                      })()}

                    </div>
                  ))}

                </div>

              </>

            ))}
          </div>
        </div>
      </Modal>

    }


    {/* 图片预览 */}
    <ImgPreview
      previewVisible={previewVisible}
      handleCancelPreview={handleCancelPreview}
      previewImage={previewImage}
      needSwitch={!!previewImgs}
      previewIdx={previewIdx}
      previewImgs={previewImgs}
      needWatermark={false}
    />


    {showConfirmLoraModal &&
      <Modal open={showConfirmLoraModal} width={800} centered
             title={'设置lora训练参数'}
             onCancel={() => {
               setShowConfirmLoraModal(false);
             }}
             zIndex={1210}
             okText={needCutoutAgain ? '重新抠图打标' : '提交lora训练'}
             okButtonProps={{ hidden: loraConfirmed() || !selectedTrainModelDetail?.labelRetFiles }}
             onOk={() => {
               confirmLoraForm.validateFields()
                 .then(() => {
                   //重新抠图
                   if (needCutoutAgain) {
                     onCutoutAgain();

                   } else {
                     onConfirmLora();
                   }
                 })
                 .catch(info => {
                   console.log('Validate Failed:', info);
                 });
             }}>

        <Form layout="vertical" style={{ paddingLeft: 12 }} form={confirmLoraForm}>
          <Form.Item
            label="是否需要重新抠图打标："
            name="needCutoutAgain"
            style={{ width: '100%' }}
            initialValue={false}
          >
            <Radio.Group value={needCutoutAgain} onChange={e => setNeedCutoutAgain(e.target.value)}>
              <Radio value={false}>不用，直接训练</Radio>
              <Radio value={true}>重新抠图打标</Radio>
            </Radio.Group>
          </Form.Item>

          {needCutoutAgain && <>
            <Form.Item label="抠图关键词（可空）："
                       name="cutoutKeyword"
                       style={{ width: '100%' }}>
              <Input style={{ width: '100%' }}
                     value={cutoutKeyword}
                     onChange={(e) => {
                       setCutoutKeyword(e.target.value);
                     }} />
            </Form.Item>

            <Form.Item label="是否需要重新分析图片："
                       name="prepareViewAgainWhenCutoutAgain"
                       style={{ width: '100%' }}>
              <Radio.Group value={prepareViewAgainWhenCutoutAgain} defaultValue={false}
                           onChange={e => setPrepareViewAgainWhenCutoutAgain(e.target.value)}>
                <Radio value={false}>不用，直接抠图（默认）</Radio>
                <Radio value={true}>重新分析图片，再抠图</Radio>
              </Radio.Group>
            </Form.Item>

            <Flex align={'flex-start'} justify={'center'}>
              <Form.Item label="抠图样本保存尺寸："
                         name="imageSize"
                         style={{ width: '50%' }}>
                <Input style={{ width: '50%' }}
                       defaultValue={'1024'}
                       value={imageSize}
                       onChange={(e) => {
                         setImageSize(e.target.value);
                       }} />
              </Form.Item>

              <Form.Item label="训练重复次数：" name="trainRepeatTimes" style={{ width: '50%' }}>
                <InputNumber style={{ width: '50%' }} defaultValue={trainRepeatTimes} value={trainRepeatTimes}
                             onChange={e => setTrainRepeatTimes(e || 15)} />
              </Form.Item>

            </Flex>

            <Form.Item label={<>打标prompt：<LoraPromptsSwitch value={captionPrompt} onChange={setCaptionPrompt}
                                                              materialType={selectedTrainModelDetail?.clothLoraTrainDetail?.materialType} /></>}
                       name="captionPrompt" valuePropName={'captionPrompt'} style={{ width: '100%' }}>
              <TextArea
                rows={10}
                style={{ width: '100%' }}
                value={captionPrompt}
                onChange={(e) => {
                  setCaptionPrompt(e.target.value);
                }} />
            </Form.Item>

            <Form.Item label="水印描述：（如果图片带水印，需要填写）"
                       name="waterMarkDesc"
                       style={{ width: '100%' }}>
              <Radio.Group onChange={e => setWaterMarkDescType(e.target.value)}
                           value={waterMarkDescType}
                           optionType={'button'}
                           options={[{ label: '没有水印', value: 'none' }, {
                             label: 'logo水印和文字水印',
                             value: 'logo and text',
                           }, { label: '仅logo水印', value: 'logo' }, { label: '仅文字水印', value: 'text' }]} />

              {waterMarkDescType !== 'none' &&
                <>
                  <span>水印位置</span>
                  <Radio.Group onChange={e => setWaterMarkDescPos(e.target.value)}
                               value={waterMarkDescPos}
                               optionType={'button'}
                               options={[{ label: '右上角', value: 'top right' }, {
                                 label: '左上角',
                                 value: 'top left',
                               }, { label: '右下角', value: 'bottom right' }, {
                                 label: '左下角',
                                 value: 'bottom left',
                               }]} />

                  <span>水印prompt</span>
                  <Input
                    allowClear
                    style={{ width: '100%' }}
                    value={waterMarkDesc}
                    onChange={(e) => {
                      setWaterMarkDesc(e.target.value);
                    }} />
                </>
              }

              <Radio.Group
                value={preprocessCensoredFace}
                options={[{ label: '不进行白头预处理', value: 'N' }, { label: '进行白头预处理', value: 'Y' }]}
                optionType="button" buttonStyle="solid" onChange={e => setPreprocessCensoredFace(e.target.value)} />

              {selectedTrainModelDetail?.clothLoraTrainDetail?.materialType === 'scene' &&
                <Flex gap={12} style={{marginTop:12}}>
                  <Radio.Group
                    value={noshowFace}
                    options={[{ label: '生成图片展示脸部', value: 'N' }, { label: '生成图片不展示脸部', value: 'Y' }]}
                    optionType="button" onChange={e => setNoshowFace(e.target.value)} />
                </Flex>
              }

            </Form.Item>


          </>}

          {!needCutoutAgain && canShowTrainParams && <>

            <Row style={{ marginTop: -4 }}>
              <Col span={4}>
                <Form.Item label="训练类型：" style={{ marginTop: -12 }}
                           rules={[{ required: true, message: '请上传展示图片' }]}>
                  <Radio.Group value={loraTrainType}
                               onChange={e => {
                                 setLoraTrainType(e.target.value);
                                 setMaxTrainStep(e.target.value === 'flux' ? defaultFluxSteps : 4000);
                               }}>
                    <Radio.Button value={'flux'}>FLUX</Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item label="训练步数：" rules={[{ required: true, message: '请指定步数' }]}
                           style={{ marginTop: -12 }}>
                  <InputNumber min={1} precision={0} style={{ width: '60%' }}
                               value={maxTrainStep}
                               onChange={(e) => {
                                 setMaxTrainStep(e || defaultFluxSteps);
                               }}
                  />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item label="学习率：" rules={[{ required: true }]}
                           style={{ marginTop: -12 }}>
                  <InputNumber min={0.00005} max={0.0005} style={{ width: '80%' }}
                               value={trainParams['lr']}
                               onChange={(e) => {
                                 setTrainParams({ ...trainParams, lr: e || 0.00020 });
                               }}
                  />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item label="学习内容：" rules={[{ required: true }]}
                           style={{ marginTop: -12 }}>
                  <Select style={{ width: '60%' }}
                          options={[{ label: '内容', value: 'content' }, { label: '风格', value: 'style' }]}
                          value={trainParams['contentOrStyle']}
                          onChange={(e) => {
                            setTrainParams({ ...trainParams, contentOrStyle: e || 'content' });
                          }}
                  />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item label="rank：" rules={[{ required: true }]}
                           style={{ marginTop: -12 }}>
                  <InputNumber min={8} max={256} precision={0} style={{ width: '50%' }}
                               value={trainParams['rank']}
                               onChange={(e) => {
                                 setTrainParams({ ...trainParams, rank: e || 32 });
                               }}
                  />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item label="alpha：" rules={[{ required: true }]}
                           style={{ marginTop: -12 }}>
                  <InputNumber min={1} max={256} precision={0} style={{ width: '50%' }}
                               value={trainParams['alpha']}
                               onChange={(e) => {
                                 setTrainParams({ ...trainParams, alpha: e || 16 });
                               }}
                  />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item label="dropout：" rules={[{ required: true }]}
                           style={{ marginTop: -12 }}>
                  <InputNumber min={0.01} max={0.3} style={{ width: '50%' }}
                               value={trainParams['dropout']}
                               onChange={(e) => {
                                 setTrainParams({ ...trainParams, dropout: e || 0.2 });
                               }}
                  />
                </Form.Item>
              </Col>
              <Col span={16}>
                <Form.Item label={<>分层设置：
                  <SystemConfigSwitch value={trainParams['trainExtInfo']}
                                      keyLabels={[
                                        { key: 'NONE', label: '不分层' },
                                        {
                                          key: `LORA_TRAIN_EXT_INFO.${selectedTrainModelDetail?.clothLoraTrainDetail?.materialType}`,
                                          label: '默认分层',
                                        }]}
                                      onChange={(value) => setTrainParams({
                                        ...trainParams,
                                        trainExtInfo: value,
                                      })} />
                </>} rules={[{ required: false }]} style={{ marginTop: -12 }}>
                  <TextArea rows={1} style={{ width: '100%' }} value={trainParams['trainExtInfo']}
                            onChange={(e) => {
                              setTrainParams({ ...trainParams, trainExtInfo: e.target.value });
                            }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>}

        </Form>
      </Modal>
    }


  </>;
};

export default LoraTrainDetail;