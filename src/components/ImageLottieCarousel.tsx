// src/components/ImageLottieCarousel.tsx
import React, { useEffect, useState, useRef } from 'react';
import Lottie from 'lottie-react';
import './ImageLottieCarousel.less';

interface ImageLottieCarouselProps {
    scanAnimation: any;
    images: string[];
}

const ImageLottieCarousel: React.FC<ImageLottieCarouselProps> = ({ images, scanAnimation }) => {
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const imageCount = images ? images.length : 0;
    const lottieRef = useRef<any>(null);

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentImageIndex((prevIndex) => (prevIndex + 1) % imageCount);
        }, 1000);

        return () => clearInterval(timer);
    }, [imageCount]);

  return (
        <div className="carousel-container">
            <img
                src={images[currentImageIndex]}
                alt={`Image ${currentImageIndex}`}
                className="carousel-image"
            />
            <div className="lottie-overlay">
                <Lottie
                    lottieRef={lottieRef}
                    animationData={scanAnimation}
                    loop={true}
                    autoplay={true}
                    speed={1}
                />
            </div>
            <div className="carousel-caption">正在深度学习</div>
        </div>
    );
};

export default ImageLottieCarousel;