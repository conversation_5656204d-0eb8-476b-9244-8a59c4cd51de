import { Button, Flex, Modal } from 'antd';
import React from 'react';
import IconFont from '@/components/IconFont';
import { download, getUserInfo } from '@/utils/utils';
import { CreativeVO, monitorImgDownload, VideoClipTask } from '@/services/CreativeController';
import WatermarkVideo from '@/components/WatermarkVideo';
import UserFavorButton from '@/components/Favor/UserFavorButton';

interface VideoPreviewProps {
  open?: boolean,
  src: string,
  batch?: CreativeVO,
  width?: number | string,
  showDownload?: boolean,
  needWatermark?: boolean,
  onClose: () => void,
  showTaskDetail?: boolean,
  indexInBatch?: number,
  destroyOnClose?: boolean,
}

const VideoPreview: React.FC<VideoPreviewProps> = ({
                                                     open = true,
                                                     src,
                                                     batch,
                                                     width,
                                                     showDownload = false,
                                                     needWatermark = true,
                                                     showTaskDetail = false,
                                                     onClose,
                                                     indexInBatch,
                                                     destroyOnClose = false
                                                   }) => {

  if (!src) return null;

  const videoClipGenTask = showTaskDetail && batch != null && batch.videoClipGenTasks != null ? batch?.videoClipGenTasks?.find((item: VideoClipTask) => item?.ossVideoUrl === src) : null;

  const handleDownload = (path = '') => {
    console.log('batch', batch);
    let fileName = '';
    if (batch && batch.title) {
      fileName = batch.title + '.mp4';
    }
    console.log('handleDownload',fileName);
    download(path, fileName);
    if (batch && Number(batch?.id) > 0 && path) {
      monitorImgDownload(Number(batch?.id), path);
    }
  };

  return (
    <Modal className="img-preview-modal" open={open} onCancel={onClose} footer={null} closable={false} destroyOnClose={destroyOnClose}
           maskClosable={false} style={{
      top: 16, display: 'flex', justifyContent: 'center', alignItems: 'center',
    }}>
      <Flex vertical justify={'space-between'} gap={16} className={'img-detail-container'}>
        <Flex style={{ position: 'relative' }} align={'center'}
              className={showDownload ? 'img-detail-img' : 'img-detail-img-no-tools'}>

          <WatermarkVideo src={src} autoPlay={true} />
        </Flex>
        <div style={{display: 'flex', justifyContent: 'flex-end', gap: 16 }}>
          {getUserInfo()?.roleType !== 'ADMIN' && batch?.id && indexInBatch !== undefined && indexInBatch >= 0 &&
            <Flex gap={4} className={'img-detail-repair-block img-detail-btn'}>
              <UserFavorButton favorType={'VIDEO'} itemId={batch.id} imageIndex={indexInBatch} style={{ fontSize: 24}} />
              <div className={'text16 font-pf color-1a'}>收藏</div>
            </Flex>
          }

          {showDownload &&
            <Flex gap={8} align={'center'} justify={'flex-end'}>

              {showTaskDetail && videoClipGenTask &&
                <Flex gap={4} wrap={'wrap'} vertical={true} align={'start'}>
                  <div>{videoClipGenTask?.prompt} / {videoClipGenTask?.duration}秒 (ID: {videoClipGenTask?.taskId})</div>
                  <div style={{ fontSize: 12 }}>{videoClipGenTask?.startTime} ~ {videoClipGenTask?.endTime}</div>
                </Flex>
              }

              <Button className={'download-btn detail-image-download img-detail-btn'}
                      type="text"
                      onClick={() => handleDownload(src)}
                      icon={<IconFont type={'icon-icon-download'} style={{ fontSize: 24, color: '#FFFFFF' }} />}
              >
                <div className={'text14 weight font-pf color-w'}>下载</div>
              </Button>
            </Flex>
          }
        </div>

        <div style={{ position: 'absolute', right: -48, top: 0, cursor: 'pointer' }} onClick={onClose}>
          <IconFont type={'icon-lujing'} style={{ fontSize: 24, color: '#FFFFFF' }} />
        </div>
      </Flex>
    </Modal>
  );
};

export default VideoPreview;