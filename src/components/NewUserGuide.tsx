import React, { Ref, useState } from 'react';
import { Tour, TourProps } from 'antd';
import './NewUserGuide.less';
import { NEED_GUIDE } from '@/constants';

interface NewUserGuideProps {
  visible: boolean;
  step1Ref: Ref<any>;
  step2Ref: Ref<any>;
  step3Ref: Ref<any>;
  handleFinish?: () => void;
}

const NewUserGuide: React.FC<NewUserGuideProps> = ({ visible, step1Ref, step2Ref, step3Ref, handleFinish }) => {
  const [open, setOpen] = useState(true);

  if (!visible) {
    return null;
  }
  const steps: TourProps['steps'] = [
    {
      title: (<div className={'text20 weight font-pf color-1a'}>选择你想创作的服装</div>),
      description: (
        <div className={'text16 font-pf color-72 tour-desc'}>你可以选择我们准备好的服装进行体验</div>),
      placement: 'left',
      target: () => step1Ref?.current,
      nextButtonProps: { className: 'tour-btn' },
    },
    {
      title: (<div className={'text20 weight font-pf color-1a'}>选择你需要的模特和背景</div>),
      description: (
        <div className={'text16 font-pf color-72 tour-desc'}>选择你需要的模特、背景、图片尺寸等</div>),
      placement: 'left',
      target: () => step2Ref?.current,
      prevButtonProps: { className: 'tour-btn' },
      nextButtonProps: { className: 'tour-btn' },
    },
    {
      title: (<div className={'text20 weight font-pf color-1a'}>点击开始创作</div>),
      description: (
        <div className={'text16 font-pf color-72 tour-desc'}>你可以免费创作50张图片。</div>),
      placement: 'left',
      target: () => step3Ref?.current,
      prevButtonProps: { className: 'tour-btn' },
      nextButtonProps: {
        className: 'tour-btn',
        children: '开始体验',
        onClick: () => {
          //滚动回选择服装
          step1Ref?.current.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest',
          });

          localStorage.setItem(NEED_GUIDE, String(false));

          if (handleFinish) {
            handleFinish();
          }
        },
      },
    },
  ];

  return <div className={'tour-container'}>
    <Tour closable={false} open={open} onClose={() => setOpen(false)} steps={steps}
          indicatorsRender={(current, total) => (
            <span className={'text16 font-pf color-72 tour-indicator'}>{current + 1}/{total}</span>
          )} />
  </div>;
};

export default NewUserGuide;