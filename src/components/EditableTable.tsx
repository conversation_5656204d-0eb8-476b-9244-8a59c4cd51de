import { Form, Input, InputNumber, TableProps } from 'antd';
import React from 'react';

export interface EditableCellProps extends React.HTMLAttributes<HTMLElement> {
  editing: boolean;
  dataIndex: string;
  title: any;
  inputType: 'number' | 'text';
  inputBefore?: any;
  hidden?: boolean;
  record: any;
  required?: boolean;
  index: number;
}

// @ts-ignore
export const mergedColumns: TableProps['columns'] = (columns: Array<any>) => {
  return columns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: any) => ({
        record,
        inputType: col.inputType ? col.inputType : 'text',
        dataIndex: col.dataIndex,
        title: col.title,
        inputBefore: col.inputBefore,
        editing: record.isEdit,
        hidden: col.hidden,
        required: col.required,
      }),
    };
  });
};

export const EditableCell: React.FC<React.PropsWithChildren<EditableCellProps>> = ({
                                                                                     editing,
                                                                                     dataIndex,
                                                                                     title,
                                                                                     inputType,
                                                                                     inputBefore,
                                                                                     hidden = false,
                                                                                     record,
                                                                                     index,
                                                                                     children,
                                                                                     required = true,
                                                                                     ...restProps
                                                                                   }) => {
  const inputNode = inputType === 'number' ? <InputNumber /> : <Input addonBefore={inputBefore ? inputBefore : null} />;

  return (
    <td {...restProps}>
      {editing ? (
        <Form.Item
          name={dataIndex}
          style={{ margin: 0 }}
          rules={[
            {
              required: required,
              message: `请输入${title}!`,
            },
          ]}
          hidden={hidden}
        >
          {inputNode}
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};