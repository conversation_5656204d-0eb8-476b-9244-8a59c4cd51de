.flow-container {
  position: relative;
  margin: 16px;
  width: calc(100% - 32px);
  height: 112px;
  border-radius: 8px;
  display: flex;
  flex-direction: row;
  padding: 16px;
  background: linear-gradient(180deg, #FBF7FF 0%, rgba(253, 252, 255, 0.3809) 41%, rgba(255, 255, 255, 0) 100%), #FFFFFF;
  border: 2px solid #FFFFFF;
  box-shadow: 8px 4px 23px 0px rgba(141, 149, 172, 0.2);
}

.flow-inner {
  width: calc(100% - 20px);
  height: 80px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  padding: 0px;
  gap: 8px;
  z-index: 0;
}

.step1,
.step2,
.step3 {
  position: relative;
  width: calc((100% - 16px) / 3);
  height: 80px;
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #333;
  background: linear-gradient(0deg, #F0EDF4, #F0EDF4), #F0EDF4;
  transition: background-color 0.3s ease;
  overflow: hidden;
}

.step1 {
  clip-path: polygon(calc(100% - 26px) 0, 100% 50%, calc(100% - 26px) 100%, 0 100%,0 0);
}

.step2 {
  clip-path: polygon(calc(100% - 26px) 0, 100% 50%, calc(100% - 26px) 100%, 0 100%, 26px 50%, 0 0);
}

.step3 {
  clip-path: polygon(calc(100% - 26px) 0, calc(100% - 26px) 100%,0 100%,26px 50%, 0 0);
}

.step-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  flex-grow: 1;
  gap: 12px;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
}

.step-title-desc {
  height: 52px;
  display: flex;
  flex-direction: column;
  align-items: start;
  padding: 0px;
  gap: 4px;
}

.step-title {
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: 0px;
  color: #5F4D75;
}

.step-desc {
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;
  color: rgba(95, 77, 117, 0.6);
}

.active-step {
  background: linear-gradient(90deg, #D2D8FF 0%, #E8D2FF 100%), #F0EDF4;

  .step-title {
    color: #5425A6;
  }

  .step-desc{
    color: #5425A6;
  }
}

.collapse-container {
  position: absolute;
  right: 16px;
  top: 16px;

  width: 24px;
  height: 24px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2.67px;
  box-sizing: border-box;
  border: 0.67px solid #D8D8D8;
  z-index: 1;
}