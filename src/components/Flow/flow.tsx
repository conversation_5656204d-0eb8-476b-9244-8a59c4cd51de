import React, { useEffect, useState } from 'react';
import './flow.less';
import IconFont from '@/components/IconFont';
import { EVENT_TOGGLE_FLOW_STEPS } from '@/constants';
import { queryUserProfileByKey, setUserProfileByKey } from '@/services/UserController';

interface ToggleFlowProps {
  activeStep: number;
  onCollapseAction?: (show: boolean) => void;
}

const Flow: React.FC<ToggleFlowProps> = ({ activeStep, onCollapseAction }) => {
  const [showFlowSteps, setShowFlowSteps] = useState(false);

  useEffect(() => {
    queryUserProfileByKey('showFlowSteps').then(res => {
      let status = !(res && res.profileVal === 'N');
      setShowFlowSteps(status);
      if (onCollapseAction) {
        onCollapseAction(status);
      }
    });

    // 定义事件处理函数
    const handleToggleFlowSteps = (event) => {
      setShowFlowSteps(event.detail.showFlowSteps);
      if (onCollapseAction) {
        onCollapseAction(event.detail.showFlowSteps);
      }
    };

    // 添加事件监听器
    window.addEventListener(EVENT_TOGGLE_FLOW_STEPS, handleToggleFlowSteps);

    // 清理事件监听器
    return () => {
      window.removeEventListener(EVENT_TOGGLE_FLOW_STEPS, handleToggleFlowSteps);
    };
  }, []);

  function onCollapse() {
    const customEvent = new CustomEvent(EVENT_TOGGLE_FLOW_STEPS, {
      detail: { showFlowSteps: false },
    });
    window.dispatchEvent(customEvent);

    setUserProfileByKey({
      key: 'showFlowSteps',
      value: 'N',
    });

    if (onCollapseAction) {
      onCollapseAction(false);
    }
  }

  return (
    showFlowSteps && (
      <div className="flow-container">
        <div className="flow-inner">
          <div className={`step1 ${activeStep === 1 ? 'active-step' : ''}`}>
            <div className="step-content">
              <div className="step-number" style={{ marginLeft: 16 }}>
                <IconFont type={'icon-a-rongqi312'} style={{ fontSize: 40 }} />
              </div>
              <div className={'step-title-desc'}>
                <div className={'step-title'}>上传服装图片</div>
                <div className={'step-desc'}>上传服装多角度的照片</div>
              </div>
            </div>
          </div>
          <div className={`step2 ${activeStep === 2 ? 'active-step' : ''}`}>
            <div className="step-content">
              <div className="step-number" style={{ marginLeft: 42 }}>
                <IconFont type={'icon-a-rongqi311'} style={{ fontSize: 40 }} />
              </div>
              <div className={'step-title-desc'}>
                <div className={'step-title'}>MuseGate 深度学习服装</div>
                <div className={'step-desc'}>服装学习时长约24小时</div>
              </div>
            </div>
          </div>
          <div className={`step3 ${activeStep === 3 ? 'active-step' : ''}`}>
            <div className="step-content">
              <div className="step-number" style={{ marginLeft: 42 }}>
                <IconFont type={'icon-a-rongqi310'} style={{ fontSize: 40 }} />
              </div>
              <div className={'step-title-desc'}>
                <div className={'step-title'}>去创作</div>
                <div className={'step-desc'}>创作多姿势多场景的图片</div>
              </div>
            </div>
          </div>
        </div>
        <div className={'collapse-container'} onClick={onCollapse}>
          <IconFont type={'icon-shouqi'} style={{ fontSize: 24 }} />
        </div>
      </div>
    )
  );
};

export default Flow;