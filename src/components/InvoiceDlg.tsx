import React, { useState } from 'react';
import { Modal, Form, Input, Radio, Button, message, Flex } from 'antd';
import IconFont from '@/components/IconFont';
import './InvoiceDlg.less';

interface InvoiceDlgProps<T> {
  visible: boolean;
  title: string;
  confirmText: string;
  onCancel: () => void;
  onConfirm: (data: T) => void;
  formData: T | undefined;
}

const InvoiceDlg = <T,>({ visible, formData, title, confirmText, onCancel, onConfirm }: InvoiceDlgProps<T>) => {
  const [form] = Form.useForm();
  if (formData) {
    form.setFieldsValue(formData);
  }

  const [subjectType, setSubjectType] = useState('企业');

  const handleSubjectTypeChange = e => {
    setSubjectType(e.target.value);
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      console.log('Received values of form: ', values);
      onConfirm({ ...formData, ...values } as T);
    } catch (errorInfo) {
      console.error('Validate Failed:', errorInfo);
    }
  };

  return (
    <Modal
      title={
        <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#333' }}>
          {title}
        </div>
      }
      open={visible}
      centered={true}
      onCancel={onCancel}
      closable={false}
      width={564}
      styles={{
        mask: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          backdropFilter: 'blur(10px)'
        }
      }}
      footer={[
        <Button key="cancel" onClick={onCancel} style={{ width: 158, height: 38, borderRadius: 8, marginRight: 8 }}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleOk} className={'invoice-confirm-btn'}>
          {confirmText}
        </Button>
      ]}
    >
      <Flex vertical gap={'24px'} style={{ width: '100%' }} className={'invoice-dlg'}>
        <div className={'invoice-warning-row'}>
          <IconFont type={'icon-Union'} style={{ fontSize: 17, color: '#0052D9' }} />
          <span className={'invoice-warning-text'}>请确认开票信息是否正确，信息错误将导致收到的发票无法抵扣。</span>
        </div>
        <Form form={form} layout="horizontal" className={'invoice-form'} initialValues={{ invoiceType: '普通', subjectType: '企业' }}>
          <Form.Item name="invoiceType" label="发票类型">
            <Radio.Group defaultValue="普通">
              <Radio value="普通">普通</Radio>
            </Radio.Group>
          </Form.Item>
          {form.getFieldValue('orderAmount') && (
            <Form.Item name="orderAmount" label="发票金额">
              <span>{form.getFieldValue('orderAmount')} 元</span>
            </Form.Item>
          )}
          <Form.Item name="subjectType" label="主体类型" hidden={true}>
            <Radio.Group onChange={handleSubjectTypeChange}>
              <Radio value="企业">企业</Radio>
              <Radio value="个人">个人</Radio>
            </Radio.Group>
          </Form.Item>
          {subjectType === '企业' && (
            <>
              <Form.Item
                name="subjectName"
                label="发票抬头"
                rules={[
                  { required: true },
                  {
                    validator: (_, value) => {
                      if (value && value.includes(' ')) {
                        return Promise.reject('发票抬头不能包含空格');
                      }
                      return Promise.resolve();
                    }
                  }
                ]}
              >
                <Input maxLength={100} showCount placeholder={'请输入'} />
              </Form.Item>
              <Form.Item
                name="creditCode"
                label="统一社会信用码"
                rules={[
                  { required: true },
                  {
                    validator: (_, value) => {
                      if (value && value.includes(' ')) {
                        return Promise.reject('统一社会信用码不能包含空格');
                      }
                      return Promise.resolve();
                    }
                  }
                ]}
              >
                <Input maxLength={20} showCount placeholder={'请输入'} />
              </Form.Item>
              <Form.Item name="businessAddress" label="营业地址">
                <Input maxLength={80} showCount placeholder={'请输入'} />
              </Form.Item>
              <Form.Item name="businessPhone" label="营业电话">
                <Input maxLength={15} showCount placeholder={'请输入'} />
              </Form.Item>
              <Form.Item name="bankName" label="开户银行">
                <Input maxLength={70} showCount placeholder={'请输入'} />
              </Form.Item>
              <Form.Item name="bankAccount" label="开户银行账号">
                <Input maxLength={25} showCount placeholder={'请输入'} />
              </Form.Item>
            </>
          )}
        </Form>
      </Flex>
    </Modal>
  );
};

export default InvoiceDlg;
