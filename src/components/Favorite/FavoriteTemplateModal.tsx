import React, { useEffect, useState } from 'react';
import { Modal, Flex, Pagination, Button, message, Carousel, Input, Tooltip, Popconfirm } from 'antd';
import { queryFavorElements, ElementConfig } from '@/services/ElementController';
import { queryByPage as queryFixedCreativeTemplates, FixedCreativeTemplateVO, updateById, deleteById } from '@/services/FixedCreativeTemplateController';
import IconFont from '@/components/IconFont';
import { LeftOutlined, RightOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { bodyTypeOptions, positionOptions } from '@/components/Operate/ABTest/ParamsSelectList';
import './FavoriteTemplateModal.less';

interface FavoriteTemplateModalProps {
  open: boolean;
  onCancel: () => void;
  onSelect: (templates: { imageUrl: string, isSelected: boolean, referenceConfig?: any, backTags?: string, backExtTags?: string, loraId?: number, loraPath?: string }[]) => void;
}

const FavoriteTemplateModal: React.FC<FavoriteTemplateModalProps> = ({
  open,
  onCancel,
  onSelect
}) => {
  const [loading, setLoading] = useState(false);
  const [favoriteList, setFavoriteList] = useState<FixedCreativeTemplateVO[]>([]);
  const [selectedItems, setSelectedItems] = useState<FixedCreativeTemplateVO[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(27);
  const [carouselHoverSide, setCarouselHoverSide] = useState<'left' | 'right' | null>(null);
  
  // 添加轮播图当前索引状态
  const [currentSlide, setCurrentSlide] = useState(0);

  // 添加编辑相关状态
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<FixedCreativeTemplateVO | null>(null);
  const [editTemplateName, setEditTemplateName] = useState('');
  // 添加待删除图片索引集合
  const [pendingDeleteIndexes, setPendingDeleteIndexes] = useState<Set<number>>(new Set());

  // 修改为支持确认按钮的模式
  const maxChoose = 1;
  const showConfirmFooter = true;

  // 获取收藏的模板数据
  const fetchFavoriteTemplates = async () => {
    setLoading(true);
    try {
      // 获取当前用户信息
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      
      const query = {
        pageNum: page,
        pageSize: pageSize,
        userId: userInfo.userId || userInfo.id // 添加userId参数，只查询当前用户的收藏模板
      };
      
      const res = await queryFixedCreativeTemplates(query);
      if (res) {
        setFavoriteList(res.list || []);
        setTotal(res.totalCount || 0);
        
        // 如果是第一页且有数据，默认选中第一条记录
        if (page === 1 && res.list && res.list.length > 0) {
          setSelectedItems([res.list[0]]);
        }
      }
    } catch (error) {
      console.error('获取收藏模板失败:', error);
      message.error('获取收藏模板失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理模板选择
  const handleChange = (item: FixedCreativeTemplateVO) => {
    let newItems = selectedItems || [];

    //单选模式：如果点击的是已选中的项，则取消选中；否则选中该项
    if (newItems.some(e => e.id === item.id)) {
      newItems = []; // 取消选中
    } else {
      newItems = [item]; // 选中该项
    }
    
    setSelectedItems(newItems);
    // 重置轮播图索引
    setCurrentSlide(0);
  };

  // 处理页码变化
  const handlePageChange = (newPage: number, newPageSize?: number) => {
    setPage(newPage);
    if (newPageSize) {
      setPageSize(newPageSize);
    }
    // 切换页面时清空选中状态
    setSelectedItems([]);
  };

  // 确认选择
  const handleConfirm = () => {
    if (selectedItems.length === 0) {
      message.warning('请先选择一个模板');
      return;
    }

    // 将FixedCreativeTemplateVO转换为参考图格式
    const templates = selectedItems.flatMap(item => {
      // 确保referenceInfoList是数组
      const refList = Array.isArray(item.referenceInfoList) ? item.referenceInfoList : [];
      return refList.map(ref => ({
        imageUrl: ref.imageUrl,
        isSelected: true,
        referenceConfig: ref.referenceConfig || {},
        backTags: ref.backTags || '',
        backExtTags: ref.backExtTags || '',
        loraId: ref.loraId || 0,
        loraPath: ref.loraPath || (ref.referenceConfig?.loraPath || '') // 从referenceConfig中获取loraPath
      }));
    });
    
    onSelect(templates);
    onCancel();
  };

  // 取消选择
  const handleCancel = () => {
    setSelectedItems([]);
    onCancel();
  };

  // 处理编辑模板
  const handleEditTemplate = (template: FixedCreativeTemplateVO, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发模板选择
    setEditingTemplate(template);
    setEditTemplateName(template.templateName);
    setPendingDeleteIndexes(new Set()); // 重置待删除状态
    setShowEditModal(true);
  };

  // 确认编辑
  const handleConfirmEdit = async () => {
    if (!editTemplateName.trim()) {
      message.error('请输入模板名称');
      return;
    }

    if (!editingTemplate) {
      return;
    }

    // 过滤掉待删除的图片
    const filteredReferenceInfoList = editingTemplate.referenceInfoList.filter((_, index) => 
      !pendingDeleteIndexes.has(index)
    );

    if (filteredReferenceInfoList.length === 0) {
      message.error('至少需要保留一张参考图');
      return;
    }

    try {
      // 调用更新API，包含模板名称和更新后的referenceInfoList
      const updateData = {
        id: editingTemplate.id,
        templateName: editTemplateName.trim(),
        referenceInfoList: filteredReferenceInfoList
      };

      await updateById(updateData);
      
      message.success('模板更新成功');
      setShowEditModal(false);
      setEditingTemplate(null);
      setEditTemplateName('');
      setPendingDeleteIndexes(new Set()); // 重置待删除状态
      
      // 刷新列表
      fetchFavoriteTemplates();
    } catch (error) {
      console.error('更新模板失败:', error);
      message.error('更新模板失败，请重试');
    }
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setShowEditModal(false);
    setEditingTemplate(null);
    setEditTemplateName('');
    setPendingDeleteIndexes(new Set()); // 重置待删除状态
  };

  // 切换参考图的删除状态
  const handleToggleDeleteReference = (index: number) => {
    const newPendingDeleteIndexes = new Set(pendingDeleteIndexes);
    
    if (newPendingDeleteIndexes.has(index)) {
      // 如果已经标记为待删除，则取消标记
      newPendingDeleteIndexes.delete(index);
    } else {
      // 如果未标记，则标记为待删除
      newPendingDeleteIndexes.add(index);
    }
    
    setPendingDeleteIndexes(newPendingDeleteIndexes);
  };

  // 删除整个模板
  const handleDeleteTemplate = async (template: FixedCreativeTemplateVO) => {
    try {
      await deleteById({ id: template.id });
      message.success('模板删除成功');
      
      // 如果删除的是当前选中的模板，清空选中状态
      if (selectedItems.some(item => item.id === template.id)) {
        setSelectedItems([]);
      }
      
      // 刷新列表
      fetchFavoriteTemplates();
    } catch (error) {
      console.error('删除模板失败:', error);
      message.error('删除模板失败，请重试');
    }
  };

  // 获取模板的展示图片列表（用于左侧轮播图）
  function getTemplateShowImgs(item: FixedCreativeTemplateVO): string[] {
    try {
      // 返回referenceInfoList中的所有图片URL
      const refList = Array.isArray(item.referenceInfoList) ? item.referenceInfoList : [];
      return refList.map(ref => ref.imageUrl);
    } catch (e) {
      console.error('获取模板图片失败:', e);
      return [];
    }
  }

  // 获取模板的第一张图片作为封面（用于右侧列表展示）
  function getTemplateCoverImage(item: FixedCreativeTemplateVO): string {
    try {
      const refList = Array.isArray(item.referenceInfoList) ? item.referenceInfoList : [];
      return refList.length > 0 ? refList[0].imageUrl : '';
    } catch (e) {
      console.error('获取模板封面失败:', e);
      return '';
    }
  }

  // 自定义箭头组件
  const CustomArrow = ({ direction, onClick = undefined }) => {
    const ArrowIcon = direction === 'prev' ? LeftOutlined : RightOutlined;

    return (
      <div
        className={`carousel-arrow ${direction}`}
        style={{
          width: '32px',
          height: '32px',
          borderRadius: '50%',
          backgroundColor: 'rgba(0, 0, 0, 0.4)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#fff',
          cursor: 'pointer',
          fontSize: '20px',
          position: 'absolute',
          top: '50%',
          transform: 'translateY(-50%)',
          zIndex: 2,
          opacity: 1,
          transition: 'opacity 0.3s',
          ...(direction === 'prev'
            ? { left: '8px' }
            : { right: '8px' }),
        }}
        onClick={onClick}
      >
        <ArrowIcon />
      </div>
    );
  };

  useEffect(() => {
    if (open) {
      fetchFavoriteTemplates();
    }
  }, [open, page, pageSize]);

  useEffect(() => {
    if (open) {
      // 只在弹窗打开时重置页码，不清空选中状态（因为fetchFavoriteTemplates会处理默认选中）
      setPage(1);
    }
  }, [open]);

  return (
    <>
      <Modal
        open={open}
        closable={false}
        className="favorite-template-modal"
        footer={
          showConfirmFooter ? (
            <Flex gap={16} justify="end">
              <Button  onClick={handleCancel} >
                取消
              </Button>
              <Button
                type="primary"
                onClick={handleConfirm}
                className="ok-btn"
                disabled={selectedItems.length === 0}
              >
                确定
              </Button>
            </Flex>
          ) : null
        }
        title="我收藏的模板"
        onCancel={handleCancel}
        width={1250}
        centered
        styles={{
          header: { display: 'flex', alignItems: 'center', justifyContent: 'center' },
          wrapper: { borderRadius: '24px' },
        }}
      >
        <Flex vertical gap={8} className={'element-modal-wrapper'} justify={'space-between'}>

          {/*下方列表区*/}
          <Flex gap={16} align={'flex-start'} justify={'flex-start'} className={'element-modal-img-block'}>

            {/*下方&左侧轮播图-空白-没选择场景*/}
            {(!selectedItems || selectedItems.length < 1) &&
              <div className={'element-carousel-empty-container'}>
                <Flex vertical={true} align="center" justify="center" style={{ width: 396, height: 528, gap: 8 }}>
                  <IconFont type={'icon-lujing5'} style={{ fontSize: 24 }} />
                  <span className={'element-carousel-empty-title'}>暂未选择</span>
                  <span className={'element-carousel-empty-content'}>右侧选择模板后展示该模板AI生成图效果</span>
                </Flex>
              </div>
            }

            {/*下方&左侧轮播图-空白-选择了没有配置轮播图的场景*/}
            {(selectedItems?.length >= 1 && getTemplateShowImgs(selectedItems[selectedItems.length - 1]).length === 0) &&
              <div className={'element-carousel-empty-container'}>
                <Flex vertical={true} align="center" justify="center" style={{ width: 396, height: 528, gap: 8 }}>
                  <IconFont type={'icon-lujing5'} style={{ fontSize: 24 }} />
                  <span className={'element-carousel-empty-title'}>暂无示例图片，去出图试试吧~</span>
                </Flex>
              </div>
            }

            {/*下方&左侧轮播图-有内容*/}
            {selectedItems?.length >= 1 && getTemplateShowImgs(selectedItems[selectedItems.length - 1]).length > 0 &&
              <Flex vertical={true} align={'flex-start'} justify={'flex-start'} className={'element-carousel-container'}>

                <div
                  className={`custom-carousel-container ${carouselHoverSide ? `hover-${carouselHoverSide}` : ''}`}
                  onMouseMove={(e) => {
                    const { clientX, currentTarget } = e;
                    const { left, width } = currentTarget.getBoundingClientRect();
                    const mousePosition = clientX - left;

                    if (mousePosition < width / 2) {
                      setCarouselHoverSide('left');
                    } else {
                      setCarouselHoverSide('right');
                    }
                  }}
                  onMouseLeave={() => setCarouselHoverSide(null)}
                  onClick={(e) => {
                    const { clientX, currentTarget } = e;
                    const { left, width } = currentTarget.getBoundingClientRect();
                    const clickPosition = clientX - left;

                    if (clickPosition < width / 2) {
                      const prevArrow = currentTarget.querySelector('.carousel-arrow.prev');
                      if (prevArrow) {
                        (prevArrow as HTMLElement).click();
                      }
                    } else {
                      const nextArrow = currentTarget.querySelector('.carousel-arrow.next');
                      if (nextArrow) {
                        (nextArrow as HTMLElement).click();
                      }
                    }
                  }}
                >
                  <Carousel
                    fade={true}
                    autoplay={true}
                    arrows={true}
                    prevArrow={<CustomArrow direction="prev" />}
                    nextArrow={<CustomArrow direction="next" />}
                    autoplaySpeed={1500}
                    infinite={true}
                    dots={false}
                    beforeChange={(from, to) => setCurrentSlide(to)}
                    className="custom-carousel"
                  >
                    {getTemplateShowImgs(selectedItems[selectedItems.length - 1]).map((url, index) => (
                      <div key={index} style={{ borderRadius: 8, position: 'relative' }}>
                        <img src={url} alt={`style-${index}`} className={'element-carousel-img'} />
                      </div>
                    ))}
                  </Carousel>
                  
                  {/* 显示数字计数器 */}
                  {getTemplateShowImgs(selectedItems[selectedItems.length - 1]).length > 1 && (
                    <div
                      style={{
                        position: 'absolute',
                        bottom: '-20px',
                        right: '12px',
                        background: 'rgba(0, 0, 0, 0.7)',
                        color: 'white',
                        padding: '4px 8px',
                        borderRadius: '12px',
                        fontSize: '12px',
                        fontWeight: '500',
                        zIndex: 4,
                        boxShadow: '0 2px 6px rgba(0, 0, 0, 0.3)',
                        backdropFilter: 'blur(4px)'
                      }}
                    >
                      {currentSlide + 1} / {getTemplateShowImgs(selectedItems[selectedItems.length - 1]).length}
                    </div>
                  )}
                </div>

                <div className={'carousel-config-name'}>{selectedItems[selectedItems.length - 1].templateName}</div>

                <Flex align={'center'} justify={'flex-end'} gap={8} wrap={'wrap'}>
                  <div className={'color-w text10'}>收藏模板</div>
                </Flex>
              </Flex>
            }

            {/*下方&右侧的场景、模特列表*/}
            <Flex gap={12} wrap={'wrap'} align={'flex-start'} justify={'flex-start'}>
              {loading ? (
                <Flex justify="center" align="center" style={{ width: '100%', height: 400 }}>
                  <IconFont type="icon-loading" className="loading-icon" style={{ fontSize: 24 }} />
                  <span className="text14 color-96 margin-left-8">加载中...</span>
                </Flex>
              ) : favoriteList.length === 0 ? (
                <Flex vertical justify="center" align="center" style={{ width: '100%', height: 400 }} gap={16}>
                  <IconFont type="icon-empty" style={{ fontSize: 48, color: '#d9d9d9' }} />
                  <span className="text16 color-96">暂无收藏的模板</span>
                  <span className="text14 color-96">去收藏一些模板吧</span>
                </Flex>
              ) : (
                favoriteList.map((item) => (
                  <div
                    key={item.id}
                    style={{ position: 'relative', overflow: 'hidden' }}
                    onClick={() => handleChange(item)}
                    className={'work-item element-modal-item' + (selectedItems && selectedItems.length > 0 && selectedItems.some(e => e.id === item.id) ? ' work-item-selected' : '')}
                  >
                    <div className={'element-image-wrapper element-image-wrapper-34'}>
                      <img alt="img" src={getTemplateCoverImage(item)} className={'element-image-item'} />

                      {/* 编辑按钮 */}
                      <Tooltip title="编辑模板" placement="top">
                        <div 
                          className="template-edit-btn"
                          onClick={(e) => handleEditTemplate(item, e)}
                          style={{
                            position: 'absolute',
                            top: '4px',
                            right: '28px',
                            width: '20px',
                            height: '20px',
                            borderRadius: '4px',
                            backgroundColor: 'rgba(0, 0, 0, 0.7)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            cursor: 'pointer',
                            zIndex: 2,
                            transition: 'all 0.2s ease',
                            opacity: 0.8
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.opacity = '1';
                            e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.opacity = '0.8';
                            e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                          }}
                        >
                          <EditOutlined style={{ fontSize: 10, color: '#fff' }} />
                        </div>
                      </Tooltip>

                      {/* 删除按钮 */}
                      <Popconfirm
                        title="删除模板"
                        description={`确定要删除模板"${item.templateName}"吗？删除后无法恢复。`}
                        onConfirm={() => handleDeleteTemplate(item)}
                        okText="确认删除"
                        cancelText="取消"
                        placement="topRight"
                        okButtonProps={{ danger: true }}
                      >
                        <Tooltip title="删除模板" placement="top">
                          <div 
                            className="template-delete-btn"
                            onClick={(e) => e.stopPropagation()}
                            style={{
                              position: 'absolute',
                              top: '4px',
                              right: '4px',
                              width: '20px',
                              height: '20px',
                              borderRadius: '4px',
                              backgroundColor: 'rgba(255, 77, 79, 0.8)',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              cursor: 'pointer',
                              zIndex: 2,
                              transition: 'all 0.2s ease',
                              opacity: 0.8
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.opacity = '1';
                              e.currentTarget.style.backgroundColor = 'rgba(255, 77, 79, 1)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.opacity = '0.8';
                              e.currentTarget.style.backgroundColor = 'rgba(255, 77, 79, 0.8)';
                            }}
                          >
                            <DeleteOutlined style={{ fontSize: 10, color: '#fff' }} />
                          </div>
                        </Tooltip>
                      </Popconfirm>

                      {maxChoose > 1 && selectedItems && selectedItems.length > 0 && selectedItems.some(e => e.id === item.id) &&
                        <div style={{ position: 'absolute', right: 4, top: 4 }}>
                          <IconFont type={'icon-gou-lan'} style={{ fontSize: 32, color: '#366EF4' }} />
                        </div>
                      }
                    </div>
                    <div className={'element-block-item-name text14 font-pf color-n margin-top-4 margin-bottom-4 text-center'}>
                      {item.templateName}
                    </div>
                  </div>
                ))
              )}
            </Flex>

          </Flex>
          {total > 0 && (
            <div className={'stick-bottom-pagination'}>
              <Pagination
                current={page}
                pageSize={pageSize}
                total={total}
                onChange={handlePageChange}
                showTotal={(total) => `共 ${total} 个模板`}
                showSizeChanger
                pageSizeOptions={[27, 54, 81]}
                showQuickJumper
                style={{ marginTop: '16px', textAlign: 'center' }}
              />
            </div>
          )}
        </Flex>
      </Modal>

      {/* 编辑模板弹窗 - 复用新增弹窗的设计 */}
      {showEditModal && (
        <Modal
          open={showEditModal}
          title="编辑收藏模板"
          onCancel={handleCancelEdit}
          onOk={handleConfirmEdit}
          okText="保存"
          cancelText="取消"
          width={800}
          centered
          className="edit-template-modal"
        >
          <Flex vertical gap={16}>
            {/* 模板名称输入框 */}
            <div>
              <div className="text16 font-pf color-n weight margin-bottom-8">模板名称</div>
              <Input
                placeholder="请输入模板名称"
                value={editTemplateName}
                onChange={(e) => setEditTemplateName(e.target.value)}
                maxLength={20}
                showCount
              />
            </div>

            {/* 收藏的图片展示 */}
            {editingTemplate && (
              <div>
                <div className="text16 font-pf color-n weight margin-bottom-8">
                  收藏姿势 ({editingTemplate.referenceInfoList?.length || 0})
                  {pendingDeleteIndexes.size > 0 && (
                    <span style={{
                      color: '#ff4d4f',
                      marginLeft: '8px',
                      fontSize: '14px',
                      fontWeight: 'normal'
                    }}>
                      (待删除 {pendingDeleteIndexes.size} 张)
                    </span>
                  )}
                </div>
                
                {/* 操作提示 */}
                <div style={{ 
                  marginBottom: '8px', 
                  padding: '8px 12px', 
                  backgroundColor: '#f6f8fa', 
                  borderRadius: '6px',
                  fontSize: '12px',
                  color: '#666'
                }}>
                  💡 点击图片或删除按钮可标记/取消删除，保存时将删除标记的图片
                </div>

                <div style={{ 
                  display: 'flex', 
                  flexWrap: 'wrap', 
                  gap: '12px',
                  maxHeight: '400px',
                  overflowY: 'auto',
                  padding: '8px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '6px',
                  backgroundColor: '#fafafa'
                }}>
                  {(!editingTemplate.referenceInfoList || editingTemplate.referenceInfoList.length === 0) ? (
                    <div style={{ 
                      width: '100%', 
                      textAlign: 'center', 
                      padding: '40px 0',
                      color: '#999'
                    }}>
                      暂无参考图
                    </div>
                  ) : (
                    editingTemplate.referenceInfoList.map((ref, index) => {
                      const isPendingDelete = pendingDeleteIndexes.has(index);
                      
                      return (
                        <div 
                          key={index} 
                          className={`reference-image-item ${isPendingDelete ? 'pending-delete' : ''}`}
                          onClick={() => handleToggleDeleteReference(index)}
                          style={{
                            position: 'relative',
                            display: 'inline-block',
                            cursor: 'pointer',
                            borderRadius: '6px',
                            overflow: 'hidden',
                            transition: 'all 0.3s ease'
                          }}
                        >
                          <img
                            src={ref.imageUrl}
                            alt={`参考图${index + 1}`}
                            width={120}
                            height={120}
                            className="reference-image"
                            style={{ 
                              objectFit: 'cover',
                              borderRadius: '6px',
                              border: isPendingDelete ? '2px solid #ff4d4f' : '1px solid #d9d9d9',
                              display: 'block',
                              opacity: isPendingDelete ? 0.3 : 1,
                              filter: isPendingDelete ? 'grayscale(100%)' : 'none',
                              transition: 'all 0.3s ease'
                            }}
                          />
                          
                          {/* 删除状态标识 - 使用内联样式确保生效 */}
                          {isPendingDelete && (
                            <div 
                              style={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                right: 0,
                                bottom: 0,
                                width: '100%',
                                height: '100%',
                                backgroundColor: 'rgba(255, 77, 79, 0.85)',
                                color: '#fff',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '13px',
                                fontWeight: 'bold',
                                pointerEvents: 'none',
                                zIndex: 10,
                                borderRadius: '6px',
                                textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)',
                                userSelect: 'none'
                              }}
                            >
                              待删除
                            </div>
                          )}
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
            )}
          </Flex>
        </Modal>
      )}
    </>
  );
};

export default FavoriteTemplateModal; 