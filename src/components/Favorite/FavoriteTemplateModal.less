.favorite-template-modal {
  .ant-modal-content {
    border-radius: 12px;
  }

  .favorite-template-content {
    min-height: 500px;

    .selection-hint {
      padding: 8px 16px;
      background: #f5f6f9;
      border-radius: 8px;
      text-align: center;
    }

    .template-list-container {
      min-height: 400px;

      .loading-icon {
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        from {
          transform: rotate(0deg);
        }

        to {
          transform: rotate(360deg);
        }
      }

      .template-item {
        width: 140px;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 8px;
        overflow: hidden;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        &.template-item-selected {
          border: 2px solid #366EF4;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(54, 110, 244, 0.2);
        }

        .template-image-wrapper {
          position: relative;
          width: 140px;
          height: 186px;
          border-radius: 8px;
          overflow: hidden;

          .template-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .selection-indicator {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .template-checkbox {
            position: absolute;
            bottom: 8px;
            right: 8px;

            .ant-checkbox {
              .ant-checkbox-inner {
                border-radius: 4px;
                background: rgba(255, 255, 255, 0.9);
              }

              &.ant-checkbox-checked {
                .ant-checkbox-inner {
                  background: #366EF4;
                  border-color: #366EF4;
                }
              }
            }
          }
        }

        .template-name {
          padding: 8px 4px;
          line-height: 1.4;
          word-break: break-all;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      padding-top: 16px;
      border-top: 1px solid #f0f0f0;

      .ant-pagination {
        .ant-pagination-total-text {
          color: #666;
        }
      }
    }
  }

  .element-selector-modal {
    .cancel-btn {
      width: 212px;
      height: 38px;
      border-radius: 8px;
      opacity: 1;

      /* 自动布局 */
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 8px 32px;
      gap: 8px;

      background: #FFFFFF;

      box-sizing: border-box;
      /* 中性色/N3-边框、背板 */
      border: 1px solid #E1E3EB;
    }

    .ok-btn {
      width: 212px;
      height: 38px;
      border-radius: 8px;
      opacity: 1;

      /* 自动布局 */
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 8px 32px;
      gap: 8px;

      z-index: 1;
    }

    .carousel-title {
      position: absolute;
      left: 8px;
      top: 8px;
      width: 72px;
      height: 28px;
      border-radius: 4px;
      opacity: 1;

      /* 自动布局 */
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 4px 8px;
      gap: 4px;

      background: rgba(0, 0, 0, 0.6);

      /* body/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: 0px;

      font-variation-settings: "opsz" auto;
      /* 白色 */
      color: #FFFFFF;
    }

    /* 轮播图，自定义 dots 样式 */

    .custom-dots {
      li {
        width: 8px;
        height: 8px;
        opacity: 1;
        position: relative;
        display: flex;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 50%;

        &.slick-active {
          background: rgba(255, 255, 255, 1.0);

          &::after {
            display: none;
          }
        }
      }
    }

    .custom-dots li.slick-active {
      width: 8px;
      height: 8px;
      opacity: 1;

      /* 自动布局 */
      display: flex;
      background: rgba(255, 255, 255, 1.0);
      border-radius: 50%;
      /* 设置为圆形 */

      ::after {
        display: none;
      }
    }

    .carousel-config-name {
      margin-top: 8px;

      font-family: PingFang SC;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      text-align: center;
      letter-spacing: 0px;

      font-variation-settings: "opsz" auto;
      /* 中性色/N8-主文字 */
      color: #1A1B1D;
    }
  }

  .element-modal-wrapper {
    padding: 8px;

    .element-modal-img-block {
      max-height: calc(148px * 3 + 40px * 3);
      overflow-y: auto;

      /* 添加相对定位，作为固定轮播图的参考容器 */
      position: relative;
    }
  }

  /* 添加轮播图容器的固定样式 */
  .element-carousel-empty-container {
    width: 396px;
    height: @element-carousel-height;
    opacity: 1;

    /* 自动布局 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0px;
    gap: 8px;
    z-index: 0;
    background-color: #F5F6F9;
    border-radius: 8px;

    /* 添加固定位置样式，与有内容的轮播图保持一致 */
    position: sticky;
    top: 8px;
    //align-self: flex-start;
  }

  .element-carousel-empty-title {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: normal;
    line-height: 20px;
    text-align: center;
    letter-spacing: 0px;
    color: #727375;
    margin-top: 8px;
  }

  .element-carousel-empty-content {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: normal;
    line-height: 20px;
    text-align: center;
    letter-spacing: 0px;

    font-variation-settings: "opsz" auto;
    /* 品牌色/N6-hover */
    color: #366EF4;

    z-index: 2;
  }

  /* 添加轮播图容器的固定样式 */
  .element-carousel-container {
    position: sticky;
    top: 8px;
    align-self: flex-start;
    height: fit-content;
  }

  .element-model-btn-look {
    background: radial-gradient(50% 50% at 50% 50%, #B7B6FF 0%, rgba(228, 228, 255, 0.1) 100%) !important;
    box-sizing: border-box !important;
    border: 1px solid #FFFFFF !important;
    backdrop-filter: blur(10px) !important;
    color: #873BFA !important;
    font-weight: 500 !important;
  }

  .element-model-btn-real-scene-shooting {
    background: radial-gradient(50% 50% at 50% 50%, #E5F6FF 1%, rgba(229, 246, 255, 0.1) 100%) !important;
    ;
    box-sizing: border-box !important;
    border: 1px solid #FFFFFF !important;
    backdrop-filter: blur(31px) !important;
    color: #00BDFF !important;
    font-weight: 500 !important;
  }

  .element-model-btn-model-show {
    background: radial-gradient(50% 50% at 50% 50%, #DCFFEE 0%, rgba(220, 255, 238, 0.1) 100%) !important;
    box-sizing: border-box !important;
    border: 1px solid #FFFFFF !important;
    backdrop-filter: blur(31px) !important;
    color: #27C165 !important;
    font-weight: 500 !important;
  }

  .custom-carousel-container {
    position: relative;
    width: 396px;
    height: @element-carousel-height;
    cursor: pointer;

    .custom-carousel {
      width: 396px;
      height: @element-carousel-height;
      /* 添加固定位置样式 */
      position: sticky;
      top: 8px;
    }

    /* 箭头样式 */
    .carousel-arrow {
      opacity: 1 !important;
      transition: all 0.3s ease;
      background-color: rgba(0, 0, 0, 0.6) !important;
    }

    &.hover-left {
      .carousel-arrow.prev {
        background-color: #366EF4 !important;
      }
    }

    &.hover-right {
      .carousel-arrow.next {
        background-color: #366EF4 !important;
      }
    }
  }

  .element-carousel-img {
    width: 396px;
    height: @element-carousel-height;
    object-fit: contain;
    border-radius: 8px;
  }

  .loading-icon {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  // 模板操作按钮样式
  .template-edit-btn,
  .template-delete-btn {
    &:hover {
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  // 确保按钮在悬停时有足够的对比度
  .template-edit-btn:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .template-delete-btn:hover {
    box-shadow: 0 2px 4px rgba(255, 77, 79, 0.4);
  }

  // 编辑模板相关样式
  .edit-template-modal {
    .reference-image-item {
      position: relative;
      display: inline-block;
      cursor: pointer;
      transition: all 0.3s ease;
      border-radius: 6px;
      overflow: hidden;

      &:hover {
        transform: scale(1.02);
      }

      &.pending-delete {
        .reference-image {
          opacity: 0.5;
          filter: grayscale(100%);
          border: 2px solid #ff4d4f !important;
        }

                 .delete-status-overlay {
           position: absolute;
           top: 0;
           left: 0;
           right: 0;
           bottom: 0;
           width: 100%;
           height: 100%;
           background: rgba(255, 77, 79, 0.85);
           color: #fff;
           display: flex;
           align-items: center;
           justify-content: center;
           font-size: 13px;
           font-weight: bold;
           pointer-events: none;
           z-index: 10;
           border-radius: 6px;
           text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
         }
      }

      .reference-image {
        display: block;
        border-radius: 6px;
        transition: all 0.3s ease;
      }
    }

    .pending-delete-hint {
      color: #ff4d4f;
      margin-left: 8px;
      font-size: 14px;
      font-weight: normal;
    }
  }
}

// 响应式设计
@media (max-width: 1400px) {
  .favorite-template-modal {
    .template-item {
      width: 120px;

      .template-image-wrapper {
        width: 120px;
        height: 160px;
      }
    }
  }
}

@media (max-width: 1200px) {
  .favorite-template-modal {
    .template-item {
      width: 110px;

      .template-image-wrapper {
        width: 110px;
        height: 146px;
      }
    }
  }
}

@element-carousel-height: 504px;