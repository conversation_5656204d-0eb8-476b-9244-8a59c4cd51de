import React, { useEffect, useState } from 'react';
import { Upload, Select, Button, Space, Flex, Image, message, Form, Input } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { UploadFile, UploadChangeParam } from 'antd/lib/upload/interface';
import { UPLOAD_URL } from '@/constants';
import { getClothColors, getElementTypes, SceneType } from '@/services/ElementController';
import { FileType, getBase64 } from '@/utils/utils';

import './UploadWithTags.less';

const uploadButton = (
  <div>
    <PlusOutlined />
    <div style={{ marginTop: 8 }}>Upload</div>
  </div>
);

export interface UploadFileTagItem {
  id?: number;
  //图片缓存（只有一个）
  fileList?: UploadFile[];
  //颜色
  clothColor?: string;
  //图片
  imgUrl?: string;
  //款式列表
  clothStyleIds?: [];
  //人脸列表
  faceIds?: [];
}

export interface UploadFilesModel {
  files: UploadFileTagItem[];
  deletedFiles?: UploadFileTagItem[];
}

interface UploadWithTagsProps {
  value?: UploadFilesModel;
  onChange?: (value: UploadFilesModel) => void;
}

const UploadWithTags: React.FC<UploadWithTagsProps> = ({ value , onChange }) => {

  const [colorTypes, setColorTypes] = useState<string[]>([]);
  const [logoLocationTypes, setLogoLocationTypes] = useState<SceneType[]>([]);
  const [clothStyleTypes, setClothStyleTypes] = useState<SceneType[]>([]);

  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');

  useEffect(() => {
    getClothColors().then(res => {
      if (res) {
        setColorTypes(res);
      }
    });

  }, []);

  const handleChange = (info: UploadChangeParam<UploadFile>, index: number) => {

    console.log('handle img change:', info);

    // @ts-ignore
    const newValue = {files: value?.files || [], deletedFiles: value?.deletedFiles || []};
    // @ts-ignore
    newValue.files[index] = { ...newValue.files[index], fileList: info.fileList, imgUrl: '' };
    onChange?.(newValue);
  };

  const onSelectColor = (clothColor: string, index: number) => {
    const newValue = {files: value?.files || [], deletedFiles: value?.deletedFiles || []};
    // @ts-ignore
    newValue.files[index] = { ...newValue.files[index], clothColor };
    // @ts-ignore
    onChange?.(newValue);
  };

  const addUpload = () => {
    const newValue = {files: value?.files || [], deletedFiles: value?.deletedFiles || []};
    // @ts-ignore
    newValue.files.push({ fileList: [] });
    // @ts-ignore
    onChange?.(newValue);
  };

  const removeUpload = (index: number) => {
    const newValue = {files: value?.files || [], deletedFiles: value?.deletedFiles || []};

    // @ts-ignore
    newValue.files = value.files.filter((_, i) => i !== index);
    // @ts-ignore
    if (value.files[index].id) {
      // @ts-ignore
      newValue.deletedFiles.push(value.files[index]);
    }
    // @ts-ignore
    onChange?.(newValue);
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
  };

  return (

    <Flex style={{flexWrap: 'wrap', gap: 8}}>

      <Button size={'small'} style={{width: 80}} type="dashed" onClick={addUpload} block icon={<PlusOutlined />}>
        增加
      </Button>

      {value != null && value.files && value.files.map((item, index) => (
        <Flex key={index} vertical={true} align="center" gap={8} style={{border: '1px dashed #e8e8e8', borderRadius: 8, padding: 4}}>
          <Input value={item.id} hidden/>

          <Upload
            action={UPLOAD_URL}
            listType="picture-card"
            fileList={item.fileList}
            onPreview={handlePreview}
            style={{ padding: 0 }}
            onChange={info => handleChange(info, index)}
          >
            {
              item.imgUrl
              ? (<img src={item.imgUrl} alt="uploaded file" style={{ width: '100%', height: '100%', objectFit: 'cover' }} />)
              : (item.fileList && item.fileList.length > 0 ? null : uploadButton)
            }
          </Upload>
          <Select
            placeholder="选择颜色"
            value={item.clothColor}
            style={{ width: 100, height: 25 }}
            onChange={tag => onSelectColor(tag, index)}
          >
            {colorTypes?.map(s => (
              <Select.Option key={s} value={s}>
                <span className="color-block" style={{ backgroundColor: s }}></span>
              </Select.Option>
            ))}
          </Select>
          <Button style={{ padding: 0, margin: -5 }} size={'small'} type="link" onClick={() => removeUpload(index)}>
            删除
          </Button>
        </Flex>
      ))}

      {previewImage && (
        <Image
          wrapperStyle={{ display: 'none' }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            afterOpenChange: (visible) => !visible && setPreviewImage(''),
          }}
          src={previewImage}
        />
      )}
    </Flex>
  );
};

export default UploadWithTags;