import { requestGet, requestPost } from './request';

interface WxPayQRCode {
  codeUrl: string;
  orderNo: string;
}

interface WxPayQueryRet {
  tradeState: string;
  desc: string;
}

export async function createWeChatPayQRCode(payload){
  return await requestPost<WxPayQRCode>('/wx/pay/qrcode', payload);
}

export async function queryWeChatPayResult(orderNo: string){
  return await requestPost<WxPayQueryRet>('/wx/pay/query', {orderNo: orderNo});
}

export async function createAlipayPayQRCode(payload){
  return await requestPost<WxPayQRCode>('/alipay/qrcode', payload);
}

export async function queryAlipayPayResult(orderNo: string){
  return await requestPost<WxPayQueryRet>('/alipay/query', {orderNo: orderNo});
}