import {requestPost} from './request';

export interface TagVO {
  /** 标签id */
  id?: number;

  /** 标签类型 */
  type?: string;

  /** 标签名称 */
  name?: string;

  /** 是否默认选中，0不选中、1默认选中 */
  defChecked?: boolean;

  /** 创建时间 */
  createTime?: string;

  /** 修改时间 */
  modifyTime?: string;

  /** 标签详情 */
  detail?: string;
}

export enum TagType {
  FACE_FIX = 'FACE_FIX',
  MODEL_DESC = 'MODEL_DESC',
  POSE = 'POSE',
  FACIAL_EXPRESSION = 'FACIAL_EXPRESSION',
  FACIAL_FEATURES = 'FACIAL_FEATURES',
  HAIR = 'HAIR',
  NEGATIVE = 'NEGATIVE',
  MERCHANT_MEMO = 'MERCHANT_MEMO',
  IMG_BACKGROUND = 'IMG_BACKGROUND',
  CAMERA_VIEW='CAMERA_VIEW',
  PHOTO_STYLE='PHOTO_STYLE',
  CLOTH_DESC = 'CLOTH_DESC',
  LOGO_MODEL_DESC = 'LOGO_MODEL_DESC',
  FACE_AGE_DESC = 'FACE_AGE_DESC',
}

export async function queryTagsList(payload) {
  return await requestPost<Array<TagVO>>('/tags/queryList', payload);
}

export async function createTag(payload) {
  return await requestPost<number>('/tags/create', payload);
}

export async function deleteTagById(id: number) {
  return await requestPost<number>('/tags/deleteById', {id: id});
}

export async function updateTag(payload) {
  return await requestPost<number>('/tags/updateById', payload);
}