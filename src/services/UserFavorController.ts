import { requestGet, requestPost } from '@/services/request';
import FavorDetailModal from '@/components/Favor/FavorDetailModal';
import { MaterialModel } from '@/services/MaterialModelController';

export type UserFavorType = 'MATERIAL' | 'ELEMENT' | 'IMAGE' | 'VIDEO';

export interface UserFavorVO {
  id: number;
  type: UserFavorType;
  itemId: number;
  modelId: number;
  extInfo: [];
}

export interface FavorCreationModel {
  modelId: number;
  modelName: string;
  type: UserFavorType;
  showImage: string;
  imageCount: number;
}

export interface FavorImageModel {
  batchId: number;
  index: number;
  type: UserFavorType;
  image: string;
}

export interface FavorImageDetail {
  modelId: number;
  modelName: string;
  type: UserFavorType;
  images: Array<FavorImageModel>;
}

export const isCreationType = (type: UserFavorType) => {
  if (!type) {
    return false;
  }
  return type === 'IMAGE' || type === 'VIDEO';
};

export async function addFavor(payload) {
  return await requestPost<UserFavorVO>('/userFavor/addFavor', payload);
}

export async function removeFavor(payload) {
  return await requestPost<UserFavorVO>('/userFavor/removeFavor', payload);
}

export async function getAllFavorInfoWithBlobs() {
  return await requestGet<Array<UserFavorVO>>(
    '/userFavor/getAllFavorInfoWithBlobs',
    {},
  );
}

export async function queryFavorImg4ModelByPage(payload: any) {
  return await requestPost<API.PageInfo<FavorCreationModel>>(
    '/userFavor/queryFavorImg4ModelByPage',
    payload,
  );
}

export async function queryFavorDetail(payload) {
  return await requestPost<FavorImageDetail>(
    '/userFavor/queryFavorDetail',
    payload,
  );
}

export async function queryFavorImgWithoutModelId(payload) {
  return await requestPost<Array<FavorImageModel>>(
    `/userFavor/queryFavorImgWithoutModelId`,
    payload,
  );
}

export async function getModels4Favor() {
  return await requestGet<Array<MaterialModel>>(`/userFavor/getModels4Favor`);
}

export class getFavorImgDetail {}