import { requestGet, requestPost } from './request';

export async function sendCaptcha(mobile: string) {
  return await requestPost<any>('/sms/sendCaptcha', { mobile });
}

export async function verifyCaptcha(mobile: string, code: string) {
  return await requestPost<any>('/sms/verifyCaptcha', { mobile, code });
}

export async function logout() {
  return await requestPost<any>('/login/logout', {});
}

export async function fetchSalt() {
  return await requestPost<any>('/login/fetchSalt', {});
}

export async function smsLogin(mobile: string, code: string) {
  return await requestPost<any>('/login/sms', { mobile, code });
}

export async function pwdLogin(mobile: string, pwd: string) {
  return await requestPost<any>('/login/pswd', { loginId: mobile, pswd: pwd });
}

export async function restPassword(newPassword: string) {
  return await requestPost<any>('/login/restPassword', { newPassword });
}

export async function backRestPassword(userId: number, newPassword: string) {
  return await requestPost<any>('/login/back/restPassword', { userId, newPassword });
}

export async function loginStatus() {
  return await requestGet<string>('/login/status');
}

export async function register(mobile: string, code: string, corpName: string, nickName: string, inviteCode?: string|null) {
  return await requestPost<any>('/login/register', { mobile, code, corpName, nickName, inviteCode });
}