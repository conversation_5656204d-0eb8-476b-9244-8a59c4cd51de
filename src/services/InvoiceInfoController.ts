import { requestPost } from '@/services/request';

export interface InvoiceTitleVO {
    // id
    id?: number;

    // 关联的主用户id
    masterUserId?: number;

    // 关联的操作员用户id
    operatorUserId?: number;

    // 发票类型，普票｜专票
    invoiceType?: string;

    // 发票抬头类型，个人｜企业
    subjectType?: string;

    // 发票抬头
    subjectName?: string;

    // 统一社会信用代码
    creditCode?: string;

    // 办公地址
    businessAddress?: string;

    // 办公电话
    businessPhone?: string;

    // 开户银行
    bankName?: string;

    // 银行账号
    bankAccount?: string;

    // 扩展信息
    extInfo?: string;

    // 创建时间
    createTime?: string;

    // 修改时间
    modifyTime?: string;
}

export interface InvoiceApplyForm extends InvoiceTitleVO {
    orderId: number;
    orderAmount: number;
}

export interface InvoiceInfoVO {
    /** id */
    id?: number;
  
    /** 关联的主用户id */
    masterUserId?: number;
  
    /** 关联的主用户昵称快照 */
    masterUserNick?: string;
  
    /** 关联的主用户登录账号快照 */
    masterUserLoginId?: string;
  
    /** 关联的操作员用户id */
    operatorUserId?: number;
  
    /** 关联的操作员用户昵称快照 */
    operatorUserNick?: string;
  
    /** 关联的操作员用户登录账号快照 */
    operatorUserLoginId?: string;
  
    /** 发票类型，普票｜专票 */
    invoiceType?: string;
  
    /** 发票抬头类型，个人｜企业 */
    subjectType?: string;
  
    /** 发票抬头 */
    subjectName?: string;
  
    /** 统一社会信用代码 */
    creditCode?: string;
  
    /** 办公地址 */
    businessAddress?: string;
  
    /** 办公电话 */
    businessPhone?: string;
  
    /** 开户银行 */
    bankName?: string;
  
    /** 银行账号 */
    bankAccount?: string;
  
    /** 发票状态code */
    status?: string;

    //发票状态名称
    statusName?: string;
  
    /** 申请时间 */
    applyTime?: string;
  
    /** 完成时间 */
    finishTime?: string;
  
    /** 发票号 */
    invoiceNo?: string;
  
    /** 不含税发票金额 */
    amountNoTax?: string;
  
    /** 税率，小数形式 */
    taxRate?: string;
  
    /** 税额 */
    taxAmount?: string;
  
    /** 含税发票金额 */
    amountWithTax?: string;
  
    /** 外部发票平台名称 */
    invoiceTaskThirdPlatform?: string;
  
    /** 外部发票任务id */
    invoiceTaskThirdReqId?: string;
  
    /** 发票任务详情 */
    invoiceTaskDetail?: string;
  
    /** 发票文件下载地址 */
    invoiceDownloadUrl?: string;
  
    /** 备注 */
    memo?: string;
  
    /** 扩展信息 */
    extInfo?: string;
  
    /** 创建时间 */
    createTime?: string;
  
    /** 修改时间 */
    modifyTime?: string;

    //内部发票号码
    innerInvoiceNo?: string;

    //税务局红冲票号码
    negativeInvoiceNo?: string;

    //红冲发票详情
    negativeInvoiceDetail?: string;
}

//申请发票
export async function applyInvoice(data) {
    return await requestPost<Number>('/invoiceInfo/create', data);
}

//申请红冲
export async function applyReverseInvoice(data) {
    return await requestPost<any>('/invoiceInfo/reverse', data);
}

//申请发票失败（一般是用户填写有误，后台手工处理后发起重试）
export async function retryApplyInvoice(data) {
    return await requestPost<boolean>('/invoiceInfo/retry', data);
}

export async function updateInvoiceTitle(data) {
    //更新成功时返回的是{success: true}
    return await requestPost<any>('/invoiceTitle/updateById', data);
}

export async function queryMerchantInvoiceTitle() {
    return await requestPost<InvoiceTitleVO>('/invoiceTitle/queryMerchantInvoiceTitle', {});
}

export async function queryInvoiceList(payload){
    return await requestPost<Array<InvoiceInfoVO>>('/invoiceInfo/queryList', payload);
}


export async function updateInvoiceInfo(payload){
    return await requestPost<boolean>('/invoiceInfo/updateById', payload);
}