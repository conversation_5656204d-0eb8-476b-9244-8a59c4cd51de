import { requestGet, requestPost } from '@/services/request';
import { ElementConfig } from '@/services/ElementController';
import { ClothCollocation } from '@/services/MaterialModelController';

export interface ShowCaseVO {
  id: number;
  name: string;
  type: string;
  topped: boolean;
  mainUrl: string;
  showImage: string;
  faceId: number;
  sceneId: number;
  modelId: number;
  modelUrl: string;
  order: number;
  tags: Array<string>;
  memo: string;
  createTime: string;
  modifyTime: string;

  face: ElementConfig;
  scene: ElementConfig;
}

export interface MiniElement {
  id: number;
  name: string;
  url: string;
}

export interface ShowCaseDetail {
  type: string;
  topped: boolean;
  mainUrl: string;
  showImage: string;
  face: MiniElement;
  scene: MiniElement;
  modelUrl: string;
  modelMiniUrl: string;
  tags: Array<string>;
  memo?: string;
  clothCollocation: ClothCollocation;
}

export async function queryAllShowCase() {
  return await requestGet<Array<ShowCaseVO>>('/showCase/findAll');
}

export async function queryIndexShowCase() {
  return await requestGet<Array<ShowCaseDetail>>('/showCase/queryIndex');
}

export async function createShowCase(payload) {
  return await requestPost<boolean>('/showCase/create', payload);
}

export async function updateShowCase(payload) {
  return await requestPost<boolean>('/showCase/updateById', payload);
}

export async function deleteShowCase(id) {
  return await requestPost<boolean>('/showCase/deleteById', { id });
}