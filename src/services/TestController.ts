import { requestGet, requestPost } from '@/services/request';

type TestStatus = 'DISABLED' | 'ENABLED' | 'PROCESSING' | 'COMPARING' | 'FINISHED';
type GroupType = 'EXPERIMENTAL' | 'CONTROL';

export interface TestPlan {
  id: number;
  name: string;
  description: string;
  status: TestStatus;
  statusName: string;
  items: Array<TestItem>;
  userNick: string;
  memo: string;
}

export interface TestItem {
  idx: number;
  id: number;
  name: string;
  status: TestStatus;
  statusName: string;
  roundsNum: number;
  groups: Array<TestItemGroup>;
  comparisonParams: Array<ComparisonParam>;
  planId: number;
  sharedParams: {
    loraId: number | null;
    configs: Record<string, any>;
    bodyType: string | null;
    position: string | null;
    proportion: string | null;
    imageNum: number | null;
    enableAntiBlurLora: boolean;
    enableNewModel: boolean;
    [key: string]: any;
  };
}

export interface TestItemGroup {
  roundsNum: number;
  positiveNum: number;
  negativeNum: number;
  groupType: GroupType;
  comparisonParam: ComparisonParam;
  results: Array<TestResult>;
  batchId: number;
  extInfo: JSON;
}

export interface TestResult {
  caseId: null;
  id: number;
  itemId: number;
  groupId: number;
  roundId: number;
  imageUrl: string;
  status: TestStatus;
  taskId: number;
  batchId: number;
  score: boolean;
  extInfo: JSON;
}

export interface ComparisonParam {
  key: string;
  title: string;
  options?: Array<string>;
}

// 相似度常量
export const SIMILARITY_TYPE = {
  // 服装相似度
  materialSimilarity: 'materialSimilarity',
  // 模特相似度
  faceSimilarity: 'faceSimilarity',
  // 场景相似度
  sceneSimilarity: 'sceneSimilarity',
}

export const COMPARISON_PARAMS: Array<ComparisonParam> = [
  { key: 'enableNewModel', title: '是否使用新模型', options: ['true', 'false'] },
];

export const queryTestPlanByPage = async (payload) => {
  return await requestPost<API.PageInfo<TestPlan>>('/testPlan/queryByPage', payload);
};

export const createTestPlan = async (payload) => {
  return await requestPost<number>('/testPlan/create', payload);
};

export const updateTestPlan = async (payload) => {
  return await requestPost<number>('/testPlan/updateById', payload);
};

export const queryTestResult = async (itemId) => {
  return await requestPost<Array<TestItemGroup>>('/testPlan/queryTestResult', { itemId });
};

export const scoringTestResult = async (resultId, isDraw, score) => {
  return await requestPost<boolean>('/testPlan/scoring', { resultId, isDraw, score });
};

export const enableTestPlan = async (id, enable) => {
  return await requestPost<boolean>('/testPlan/enable', { id, enable });
};

export const beginTestPlan = async (planId, itemId) => {
  return await requestPost<boolean>(`/testPlan/begin`, { planId, itemId });
};

export const deleteTestItemById = async (id) => {
  return await requestPost<boolean>(`/testPlan/deleteTestItemById/${id}`);
};

export const getById = async (id) => {
  return await requestGet<boolean>(`/testPlan/getById/${id}`);
};

export const analysisRatio = async (id, type) => {
  return await requestPost<boolean>(`/testPlan/resultAnalysisRatio`, { id, type });
};

// 更新测试结果的扩展信息
export const updateExtInfo = async (id: number, extInfoLabel: string, extInfoValue: any) => {
  return await requestPost<boolean>(`/testPlan/updateExtInfo`, { id, extInfoLabel, extInfoValue });
};

// 扩展信息分析
export const extInfoAnalysisRatio = async (id) => {
  return await requestPost<boolean>(`/testPlan/extInfoAnalysisRatio`, { id });
};