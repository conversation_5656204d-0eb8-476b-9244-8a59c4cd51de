import { requestGet, requestPost } from '@/services/request';

export interface WorkScheduleVO {
  id: number;
  userId: number;
  startTime: string;
  endTime: string;
  userName?: string; // Additional field for displaying user name
}

export interface WorkScheduleQuery {
  pageNum?: number;
  pageSize?: number;
  userId?: number;
  startTime?: string;
  endTime?: string;
}

/**
 * 分页查询工作排班
 * @param payload 查询参数
 * @returns 分页数据
 */
export async function getPageWorkSchedule(payload: WorkScheduleQuery) {
  return await requestPost<API.PageInfo<WorkScheduleVO>>('/workSchedule/queryByPage', payload);
}

/**
 * 创建工作排班
 * @param payload 排班数据
 * @returns 新创建的排班
 */
export async function createWorkSchedule(payload: Omit<WorkScheduleVO, 'id'>) {
  return await requestPost<WorkScheduleVO>('/workSchedule/create', payload);
}

/**
 * 批量创建工作排班（多人员）
 * @param schedules 排班数据，包含多个用户ID
 * @returns 创建结果
 */
export async function batchCreateWorkSchedule(schedules: Array<Partial<WorkScheduleVO>>) {
  return await requestPost<boolean>('/workSchedule/batchCreate', { list: schedules });
}
/**
 * 更新工作排班
 * @param payload 排班数据（必须包含id）
 * @returns 更新结果
 */
export async function updateWorkSchedule(payload: WorkScheduleVO) {
  return await requestPost<boolean>('/workSchedule/updateById', payload);
}

/**
 * 删除工作排班
 * @param id 排班ID
 * @returns 删除结果
 */
export async function deleteWorkSchedule(id: number) {
  return await requestPost<boolean>('/workSchedule/deleteById', { id });
}

/**
 * 根据ID获取工作排班详情
 * @param id 排班ID
 * @returns 排班详情
 */
export async function getWorkScheduleById(id: number) {
  return await requestGet<WorkScheduleVO>(`/workSchedule/getById/${id}`);
}

/**
 * 批量查询工作排班
 * @param query 查询条件
 * @returns 排班列表
 */
export async function queryWorkScheduleList(query: Partial<WorkScheduleQuery>) {
  return await requestPost<WorkScheduleVO[]>('/workSchedule/queryList', query);
}

/**
 * 批量删除工作排班
 * @param ids 排班ID数组
 * @returns 删除结果
 */
export async function batchDeleteWorkSchedule(ids: number[]) {
  const schedules = ids.map(id => ({ id: id } as WorkScheduleVO));
  return await requestPost<boolean>('/workSchedule/batchDelete', { list: schedules });
}
