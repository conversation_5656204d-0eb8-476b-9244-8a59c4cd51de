import { CreativeVO } from '@/services/CreativeController';
import { ElementConfig } from '@/services/ElementController';
import { MerchantPreference } from '@/services/SystemController';
import { UserVO } from '@/services/UserController';
import { requestGet, requestPost } from './request';
import { WorkflowTaskVO } from "@/services/WorkflowTaskController";

export interface ClothColorDetail {
  index: number;
  name: string;
  desc: string;
  showImg: string;
  value?: string;
  enable: boolean;
}

export interface MaterialModel {
  id: number;
  name: string;
  type: string;
  showImage: string;
  mainType: string;
  mainId?: number;
  createTime: string;
  modifyTime: string;
  operatorNick: string;
  operatorId: number;
  userId: number;
  status: string;
  loraName?: string;
  clothLoraTrainDetail: any;
  materialInfoId: number;
  modelPoint?: number;
  extInfo?: { reviewerId?: number; memo?: string; [key: string]: any };
  relatedDistributorCorpName?: string;
  paidCustomer: boolean;
  vipCustomer: boolean;
  lowerBody: boolean;
  halfBody: string;
  hasBackView: boolean;
  version: string;
  clothCollocation?: ClothCollocation;
  //用户选择的服装分类（上装/下装等）
  clothType: string;
  //用户选择的服装分类（上装/下装等）
  clothTypeDesc: string;
  colorList: Array<ClothColorDetail>;
  clothTypeConfigs?: Array<any>;

  //关联的元素（模特/场景）
  relatedElementVO: ElementConfig;

  // 超时时间
  timeoutHours?: string;
}

export interface MaterialModelWithBlogs extends MaterialModel {
  userNick: string;
  userCorpName: string;
  userRole: string;
  tags?: string;
  extTags?: string;
  opVersion: number;
  merchantPreference: MerchantPreference;
  subNeedConfirmCnt?: number;
  subTestingCnt?: number;
  subTotal?:number;
}

export interface DistributorClothLoraModelVO extends MaterialModelWithBlogs {
  relatedDistributorInfo: RelatedDistributorInfo;
}

export interface RelatedDistributorInfo {
  distributorMasterUserId: number;

  // 运营
  distributorOperatorUserId: number;
  distributorOperatorNickName: string;
  distributorOperatorMobile: string;

  // 销售
  distributorSalesUserId: number;
  distributorSalesNickName: string;
  distributorSalesMobile: string;
}

export interface ModelTrainDetailVO extends MaterialModelWithBlogs {
  cutoutFiles: FileVO[];
  labelRetFiles: FileVO[];
  materialDetail: any;
  prepareViewFinishTime: string;
  cutoutFinishTime: string;
  labelFinishTime: string;
  loraFinishTime: string;
  loraStatus: string;
  loraStartTime: string;
  splitTags: string[];
  detailGarmentTypes: string[];
  tasks: WorkflowTaskVO[];
}

export interface FileVO {
  fileDir: string;
  type: string;
  fileName: string;
  textContent: string;
  imgUrl: string;
}

export interface ExtInfo {
  id: number;
  isManualReplacement?: number;
  isLoraSystemReload?: boolean;
}

export interface SyncImageCase {
  //图片路径(新路径)
  filePath: string;
  //图片url（新地址）
  url: string;
  //服装模型id
  id: number;
  //文件路径(旧路径)
  fileDir: string;
  //文件名称(旧文件名称)
  fileName: string;
  //图片url（旧地址）
  imgUrl: string;
  //文本内容
  textContent: string;
  //图片类型
  type: string;
}

export interface QueuedModelData {
  queuedPreProcess : number;
  queuedTrain : number
}

export type SubTrainingStatus =
  | 'COMPLETED'
  | 'FAILED'
  | 'NONE'
  | 'QUEUED'
  | 'RUNNING'
  | 'UNKNOWN'
  | null;

export function getSubTrainStatusDesc(status: SubTrainingStatus): string {
  if (status === null) {
    return '未开始';
  }

  switch (status) {
    case 'COMPLETED':
      return '已完成';
    case 'FAILED':
      return '失败';
    case 'NONE':
      return '未开始';
    case 'QUEUED':
      return '排队中';
    case 'RUNNING':
      return '运行中';
    case 'UNKNOWN':
      return '未知';
    default:
      // 使用类型断言确保覆盖所有枚举值
      const exhaustiveCheck: never = status;
      return '未知';
  }
}

export const isDemoTag = (item: MaterialModel) => {
  return item && item.extInfo && item.extInfo['demoTag'] && item.extInfo['demoTag'] === 'Y';
};

export const AllModelStatus = [
  { label: '训练中', value: 'IN_TRAINING' },
  { label: '审核中', value: 'TESTING' },
  { label: '审核通过', value: 'ENABLED' },
  { label: '审核不通过', value: 'DISABLED' },
];

export const MODEL_LABEL_TYPES = [
  { label: '默认', value: 'default' },
  { label: '精准', value: 'details' },
  { label: '极简', value: 'mini' },
];

export interface ClothCollocation {
  /** 鞋子配饰描述，如果有多个值用逗号连接 */
  shoe: string | null;
  /** 上装配饰描述，如果有多个值用逗号连接 */
  tops: string | null;
  /** 下装配饰描述，如果有多个值用逗号连接 */
  bottoms: string | null;
  /** 其他配饰描述，如果有多个值用逗号连接 */
  others: string | null;
  /** 道具描述，如果有多个值用逗号连接 */
  props: string | null;
}

export interface UpdateReviewerRequest {
  id: number;
  reviewerId: number | undefined;
}

export const emptyClothCollocation: ClothCollocation = {
  shoe: null,
  tops: null,
  bottoms: null,
  others: null,
  props: null,
};

export  const getAllUploadImages = (model: ModelTrainDetailVO | undefined) => {
  let ret: string[] = [];
  if(!model?.materialDetail){
    return ret;
  }

  let materialDetail = model.materialDetail;
  if ( materialDetail?.imgUrls) {
    ret = ret.concat(materialDetail?.imgUrls);
  } else {
    if ( materialDetail?.fullShotImgList) {
      ret = ret.concat(materialDetail.fullShotImgList.map(item => item.imgUrl));
    }
    if ( materialDetail?.detailShotImgList) {
      ret = ret.concat(materialDetail.detailShotImgList.map(item => item.imgUrl));
    }
    if ( materialDetail?.moreImgList) {
      ret = ret.concat(materialDetail.moreImgList.map(item => item.imgUrl));
    }
  }

  return ret;
}


//red/green/gray/None/undefined
export function getClothMarkedColor(item: MaterialModel) {
  return item.extInfo && item.extInfo['clothMark'] && JSON.parse(item.extInfo['clothMark'])['markedColor'];
}

export function isClothMarkedColor(item: MaterialModel) {
  return getClothMarkedColor(item) && getClothMarkedColor(item) !== 'None';
}

export function getClothMarkIcon(item: MaterialModel) {
  if (isClothMarkedColor(item)) {
    return clothMarkFlags.find(c => c.key === getClothMarkedColor(item))?.icon || '';
  }
  return '';
}

export function getClothMarkIconByCreative(item: CreativeVO) {
  if (item?.modelMarkedColor && item?.modelMarkedColor !== 'None') {
    return clothMarkFlags.find(c => c.key === item.modelMarkedColor)?.icon || '';
  }
  return '';
}

export const clothMarkFlags = [
  {
    key: 'red',
    name: '红色',
    icon: 'icon-hongseqizi',
    top: 8,
  },
  {
    key: 'green',
    name: '绿色',
    icon: 'icon-lvseqizi',
    top: 32,
  },
  {
    key: 'gray',
    name: '灰色',
    icon: 'icon-huiseqizi',
    top: 56,
  },
];

export async function getTrainDetail(payload) {
  return await requestPost<ModelTrainDetailVO>('/materialModel/getTrainDetail', payload);
}

export async function queryMaterialModelList(payload) {
  return await requestPost<Array<MaterialModel>>('/materialModel/queryList', payload);
}

export async function getMaterialModelById(id: number) {
  return await requestGet<MaterialModelWithBlogs>(`/materialModel/getById/${id}`);
}

export async function queryMaterialModelByPage(payload) {
  return await requestPost<API.PageInfo<MaterialModel>>('/materialModel/queryByPage', payload);
}

export async function queryMaterialModelListWithBlogs(payload) {
  return await requestPost<API.PageInfo<MaterialModelWithBlogs>>('/materialModel/queryListWithBlogs', payload);
}

export async function queryAllModelCreators() {
  return await requestPost<Array<UserVO>>('/materialModel/queryAllModelCreators', {});
}

export async function updateMaterialModel(data) {
  return await requestPost<boolean>('/materialModel/updateById', data);
}

export async function confirmTrainLora(payload) {
  return await requestPost<boolean>('/materialModel/confirmTrainLora', payload);
}

export async function cutoutAgain(payload) {
  return await requestPost<boolean>('/materialModel/cutoutAgain', payload);
}

export async function addSystemModel(payload) {
  return await requestPost<boolean>('/materialModel/add/system', payload);
}

export async function delModel(id: number) {
  return await requestPost<boolean>('/materialModel/deleteById', { id });
}

export async function querySystemModelList(payload) {
  const { isOwner, ...params } = payload;
  return await requestPost<API.PageInfo<MaterialModel>>('/materialModel/querySystemList', params);
}

export async function updateLabelFiles(payload) {
  return await requestPost<boolean>('/materialModel/updateLabelFiles', payload);
}

export async function assignLoraTo(modelId, userId) {
  return await requestPost<boolean>('/materialModel/assignTo', { modelId, userId });
}

export async function assignElementModelToUser(modelId, userId, exclusive, free) {
  return await requestPost<boolean>('/materialModel/assignElementModelToUser', { modelId, userId, exclusive, free });
}

export async function assignPlatformOperator(payload) {
  return await requestPost<boolean>('/materialModel/assignPlatformOperator', payload);
}

export async function changeExampleImages(payload) {
  return await requestPost<boolean>('/materialModel/changeExampleImages', payload);
}

export async function clearExampleImages(modelId) {
  return await requestPost<boolean>('/materialModel/clearExampleImages', { modelId });
}

export async function queryModelDetailShowImage(modelId) {
  return await requestPost<string>('/materialModel/queryDetailShowImage', { modelId }, {}, false);
}

export async function cloneLora(modelId, fullCopy = false) {
  return await requestPost<boolean>('/materialModel/cloneLora', { modelId, fullCopy });
}

export async function markCloth(modelId, markKey, markValue) {
  return await requestPost<boolean>('/materialModel/markCloth', { modelId, markKey, markValue });
}

export async function batchModifyGarment(ids: string) {
  return await requestPost<boolean>('/materialModel/batchModifyGarment', { ids });
}

export async function supplyColor(ids: string) {
  return await requestPost<boolean>('/materialModel/supplyColor', { ids });
}

export async function batchUploadOss(ids: string) {
  return await requestPost<boolean>('/materialModel/batchUploadOss', { ids });
}

export async function batchAutoDelivery(ids: string) {
  return await requestPost<boolean>('/materialModel/batchAutoDelivery', { ids });
}

export async function batchAutoCreateImages(ids: string) {
  return await requestPost<boolean>('/materialModel/batchAutoCreateImages', { ids });
}

export async function batchTrainFolderSync(ids: string) {
  return await requestPost<boolean>('/materialModel/batchTrainFolderSync', { ids });
}

export async function fetchWorkflowByComfyuiTaskId(taskId: number){
  return await requestPost<string>('/materialModel/fetchWorkflowByComfyuiTaskId', { taskId });
}

export async function batchCreateTestImages(ids: string) {
  return await requestPost<boolean>('/materialModel/batchCreateTestImages', { ids });
}

export async function batchRelabelLora(ids: string) {
  return await requestPost<boolean>('/materialModel/batchRelabelLora', { ids });
}

export async function batchRetrainLora(ids: string) {
  return await requestPost<boolean>('/materialModel/batchRetrainLora', { ids });
}

export async function batchRetrainAndAutoComplete(ids: string) {
  return await requestPost<boolean>('/materialModel/batchRetrainAndAutoComplete', { ids });
}

export async function batchInitTrainedModel(ids: string) {
  return await requestPost<boolean>('/materialModel/batchInitTrainedModel', { ids });
}

export async function updateColorImage(payload) {
  return await requestPost<boolean>('/materialModel/updateColorImage', payload);
}

export async function enableColor(payload) {
  return await requestPost<boolean>('/materialModel/enableColor', payload);
}

// 更新扩展信息
export async function updateExtInfo(payload: ExtInfo) {
  return await requestPost<boolean>('/materialModel/updateExtInfo', payload);
}

// 更新审核员
export async function updateModelReviewer(payload: UpdateReviewerRequest) {
  return await requestPost<boolean>('/materialModel/updateReviewer', payload);
}

// 更新扩展信息
export async function syncToImageCase(payload: SyncImageCase) {
  return await requestPost<boolean>('/materialModel/syncToImageCase', payload);
}

export async function retrainModel(modelId: number, labelType?: string | null, cut4ScaleUp?: string | null) {
  return await requestPost<boolean>('/materialModel/retrainLora', { modelId, labelType, cut4ScaleUp });
}

export async function relabelModel(modelId: number, labelType?: string | null, cut4ScaleUp?: string | null, preprocessCensoredFace?: string | null) {
  return await requestPost<boolean>('/materialModel/relabelLora', { modelId, labelType, cut4ScaleUp, preprocessCensoredFace });
}

export async function modifyModelName(id: number, name: string) {
  return await requestPost<boolean>('/materialModel/modifyModelName', { id, name });
}

export async function copyModelToSystem(modelId: number) {
  return await requestPost<boolean>('/materialModel/copyToSystem', { modelId });
}

export async function confirmCanDeliver(loraId: number) {
  return await requestPost<boolean>('/materialModel/confirmCanDeliver', { loraId });
}

export async function addLora2Vip(loraId: number) {
  return await requestPost<boolean>('/materialModel/addLora2Vip', { loraId });
}

export async function addModelDemoTag(id) {
  return await requestPost<boolean>('/materialModel/addDemoTag', { id });
}

export async function deliverModel(payload: { id: number}) {
  return await requestPost<boolean>('/materialModel/deliver', payload);
}

export async function updateModelExperimental(id: number, experimental: boolean) {
  return await requestPost<boolean>('/materialModel/updateExperimental', { id, experimental });
}

export async function queryMaterialSubIds(id: number) {
  return await requestPost<Array<number>>('/materialModel/querySubIds', { id });
}

export async function statsQueuedModel() {
  return await requestGet<QueuedModelData>('/materialModel/statsQueuedModel');
}

// 获取同步状态的颜色
export function getSyncStatusColor(status: string) {
  if (status === '无需同步') {
    return '#52c41a'; // 绿色
  }
  if (status === '待同步') {
    return '#1677ff'; // 蓝色
  }
  return '';
}