import { requestGet, requestPost } from '@/services/request';
import { SalesType } from '@/services/OrganizationController';
import { CUSTOM_ROLE } from '@/services/UserController';
import { PrincipalType } from '@/services/AssessmentController';

/**
 * key account sales, 大客户销售
 *
 * industrial cluster sales , 产业带销售
 *
 */
export type SettleType = 'AGENT_A' | 'AGENT_B' | 'SALE_KA' | 'SALE_LEADER' | 'SALE_SUB' | 'SALE_DEPT';
// ======== keys =========
export const DEFAULT_SETTLEMENT_CONFIGS = 'DEFAULT_SETTLEMENT_CONFIGS';

export const KEY_IS_NEW_ORDER = 'renewedOrder';
/** 渠道结算开始日期, 包含, yyyy-MM-dd */
export const KEY_SETTLE_START_DATE = "settleStartDate";
/** 渠道结算结束日期, 包含, yyyy-MM-dd */
export const KEY_SETTLE_END_DATE = "settleEndDate";

export const SettleTypeMap: Record<SettleType, string> = {
  'AGENT_A': '代理A级',
  'AGENT_B': '代理B级',
  'SALE_KA': '大客户销售',
  'SALE_LEADER': '直营销售Leader',
  'SALE_SUB': '直营销售',
  'SALE_DEPT': '直营销售Leader下属渠道',
};

/** 订单结算状态 */
export type OrderSettlementStatus = 'INIT' | 'TRADE_CLOSED' | 'PENDING_SETTLE' | 'SETTLING' | 'SUCCESS' | 'FAIL';

export const orderSettlementStatusMap: Record<OrderSettlementStatus, string> = {
  INIT: '初始化',
  TRADE_CLOSED: '交易关闭',
  PENDING_SETTLE: '带结算',
  SETTLING: '结算中',
  SUCCESS: '已结算',
  FAIL: '失败',
};

/** 订单结算类型 */
export type OrderSettlementType = 'COMMISSION' | 'COMMISSION_RELATED';

export const orderSettleTypeMap: Record<OrderSettlementType, {code: string, desc: string}> = {
  COMMISSION: {code: 'COMMISSION', desc: '分佣'},
  COMMISSION_RELATED: {code: 'COMMISSION_RELATED', desc: '抽成' },
};

export interface SettleConfigModel {
  /** 结算类型 */
  settleType: SettleType;
  /** 是否直营 */
  salesType: string;
  /** 结算时间 -- 距离月底的天数 */
  calculationDate?: number;
  /** 新订单费率 */
  newOrderCommRate: number;
  /** 续费订单费率 */
  renewOrderCommRate: number;
  /** 是否抽成 */
  hasBeneficiary?: boolean;
  /** 抽成对象 */
  beneficiaryTargetId?: number;
  /** 签约首年的抽成费率 */
  initYrRate?: number;
  /** 签约非首年的抽成费率 */
  subseqYrsRate?: number;
  /** 备注 */
  memo?: string;
}

export interface DistributorBasicVO {
  /** 销售/渠道商 类型 */
  salesType: SalesType;
  /** 主体类型 */
  principalType: PrincipalType;
  /** 主体id */
  principalId: number;
  /** 主体名称 */
  principalName: string;
  /** 组织内角色 */
  customRole: CUSTOM_ROLE;
  /** 主组织id */
  masterCorpId: number;
  /** 主组织名称 */
  masterCorpName: string;
  /** 主账号id */
  channelAdminId: number;
  /** 主账号昵称 */
  channelAdminNickName: string;
  /** 子组织 销售/渠道商 类型 */
  subSalesType: SalesType;
  /** 子组织id */
  subCorpId: number;
  /** 子组织名称 */
  subCorpName: string;
  /** 子组织管理员id */
  subChannelAdminId: number;
  /** 子组织管理员昵称 */
  subChannelAdminNickName: string;
  /** 签约时间 */
  contractDate: string;
}

export interface DistributorOrgTree {
  orgId: number;
  orgName: string;
  salesType: string;
  memberMap?: Map<string, Array<{userId: number, nickName: string}>>;
  children?: DistributorOrgTree[];
}


/** 渠道商结算配置 */
export interface DistributorSettleConfigVO extends DistributorBasicVO{
  /** 结算策略类型 */
  settleConfig: SettleConfigModel;
}

/** 百分比转换为小数的辅助函数，带有校验逻辑 */
export const percentToDecimal = (value) => {
  // 检查值是否存在且为数字
  if (value === null || value === undefined || isNaN(Number(value))) {
    return 0;
  }
  // 将百分比转换为小数，并确保结果在0-1之间
  const result = Number(value) / 100;
  return Math.max(0, Math.min(1, result)); // 确保在有效范围内
};

/** 小数转换为百分比的辅助函数，带有校验逻辑 */
export const decimalToPercent = (value) => {
  // 检查值是否存在且为数字
  if (value === null || value === undefined || isNaN(Number(value))) {
    return 0;
  }
  // 将小数转换为百分比，并确保结果在0-100之间
  const result = Number(value) * 100;
  return Math.max(0, Math.min(100, result)); // 确保在有效范围内
};

/**
 * 元和万元转换, 只保留两位小数, 向下取整
 * @param value 
 * @returns 
 */
export const convertToWan = (value) => {
  // 检查值是否存在且为数字
  if (value === null || value === undefined || isNaN(Number(value))) {
    return 0;
  }
  return Math.floor(value / 10000 * 100) / 100;
};

/**
 * 万元和元转换
 * @param value 
 * @returns 
 */
export const convertToYuan = (value) => {
  // 检查值是否存在且为数字
  if (value === null || value === undefined || isNaN(Number(value))) {
    return 0;
  }
  return value * 10000;
};

// 去尾法保留 n 位小数
/**
 * 去尾法保留 n 位小数（向下取整到指定小数位）
 * @param value 要处理的数值
 * @param decimals 要保留的小数位数，默认为2
 * @returns 去尾后的数值
 */
export const truncateToDecimals = (value: number, decimals: number = 2): number => {
  // 检查值是否存在且为数字
  if (value === null || value === undefined || isNaN(Number(value))) {
    return 0;
  }
  
  // 检查小数位数是否有效
  if (decimals < 0 || !Number.isInteger(decimals)) {
    throw new Error('小数位数必须是非负整数');
  }
  
  // 使用 Math.floor 实现去尾法
  const multiplier = Math.pow(10, decimals);
  return Math.floor(Number(value) * multiplier) / multiplier;
};

/**
 * 去尾法保留 n 位小数并返回字符串格式
 * @param value 要处理的数值
 * @param decimals 要保留的小数位数，默认为2
 * @returns 去尾后的字符串
 */
export const truncateToDecimalsString = (value: number, decimals: number = 2): string => {
  const truncatedValue = truncateToDecimals(value, decimals);
  return truncatedValue.toFixed(decimals);
};

export async function modifyDistributorSettlementConfig(params) {
  return await requestPost<boolean>('/distributorSettlement/modifyDistributorSettlementConfig', params);
}

/** 获取渠道组织树 */
export async function getDistributorOrgTrees() {
  return await requestGet<DistributorOrgTree[]>('/distributorSettlement/getDistributorOrgTrees');
}