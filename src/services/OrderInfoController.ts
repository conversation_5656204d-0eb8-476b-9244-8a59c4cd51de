import { requestGet, requestPost } from './request';

export interface PricePlan {
    // 价格方案编码
    code: string;

    // 价格方案名称
    name: string;

    // 价格方案金额
    amount: string;

    // Muse点数
    musePoint: number;

    // 赠送的创作图片次数
    creativeImgCountGave: number;

    // 赠送的创作图片的价值金额
    creativeImgAmountGave: string;

    // 赠送的定制模特数量和价值金额
    customModelCountGave: number;
    customModelAmountGave: string;

    // 赠送的定制场景数量和价值金额
    customSceneCountGave: number;
    customSceneAmountGave: string;

    // 赠送的短视频数量和价值金额
    shortVideoCountGave: number;
    shortVideoAmountGave: string;

    // 预计衣服套数
    clothCount: number;

    // 每套衣服的创作图片次数
    creativeImgCountPerCloth: number;

    // 每套衣服的精选示例图片数
    exampleImgCountPerCloth: number;

    // 最大创作图片数
    maxCreativeImgCount: number;

    // 赠送价值总金额
    totalGaveAmount: string;
}

export interface OrderInfoVO {
    id: number;
    orderNo: string;
    masterUserId: number;
    masterUserNick: string;
    masterUserLoginId: string;
    operatorUserId: number;
    operatorUserNick: string;
    operatorUserLoginId: string;
    originalAmount: string;
    payAmount: string;
    payDetail?: string;
    orderStatus: string;
    productCode: string;
    productName: string;
    productDetail?: string;
    finishTime?: string;
    expireTime?: string;
    extInfo?: string;
    createTime: string;
    modifyTime: string;
    masterCorpName: string;
    distributorMasterUserId: number;
    distributorCorpName: string;

    invoiceStatus?: string;
    invoiceStatusName?: string;
    invoiceFileUrl?: string;
    distributorSalesUserId?: number;
    distributorSalesNickName?: string;

    distributorSalesOrgId?: number;
    distributorSalesOrgName?: string;

    distributorSalesOrgIdFullPath?: number[];
    distributorCorpOrgId?: number;

    //wx/alipay/offlinePayment
    payType?: string;
}

export enum PricePlanCode {
    PLAN_FLAGSHIP = 'PLAN_FLAGSHIP',
    PLAN_PREMIUM = 'PLAN_PREMIUM',
    PLAN_BASIC = 'PLAN_BASIC',
    PLAN_NEWBIE = 'PLAN_NEWBIE',
    PLAN_CUSTOM = 'PLAN_CUSTOM',
}

export const PricePlanDescriptions: { [key in PricePlanCode]: string } = {
    [PricePlanCode.PLAN_FLAGSHIP]: '黄金包',
    [PricePlanCode.PLAN_PREMIUM]: '白银包',
    [PricePlanCode.PLAN_BASIC]: '青铜包',
    [PricePlanCode.PLAN_NEWBIE]: '体验包',
    [PricePlanCode.PLAN_CUSTOM]: '定制包'
};

export interface MerchantTopupVO {
    /** id */
    id: number;

    /** 订单号 */
    orderNo: string;

    /** 产品码 */
    productCode: string;

    /** 产品名称 */
    productName: string;

    /** 订单支付金额 */
    payAmount: number;

    // 支付类型,wx/alipay
    payType?: string;

    /** 订单时间 */
    createTime: string;

    /** 过期时间 */
    expireTime: string;

    /** 关联的操作员用户登录账号快照 */
    operatorUserLoginId: string;

    masterUserNickName: string;
    masterLoginId: string;

    masterCorpName: string;

    /** 是否主账号 */
    masterUser: boolean;

    /** 开票状态 */
    invoiceStatus: string;

    /**
     * 发票状态名称
     */
    invoiceStatusName: string;

    /** 发票文件url */
    invoiceFileUrl: string;
}

export const getPricePlanDescription = (code: string): string => {
    return PricePlanDescriptions[code as PricePlanCode] || code;
};

export async function getPricePlan(){
    return await requestGet<PricePlan[]>('/orderInfo/getPricePlan');
}

export async function checkIfCanShowNewbiePlan(){
    return await requestGet<boolean>('/orderInfo/checkIfCanShowNewbiePlan');
}

export async function checkIfShowTopup(){
    return await requestGet<boolean>('/orderInfo/checkIfShowTopup');
}

//单个查询订单
export async function getById(id){
    return await requestGet<OrderInfoVO>(`/orderInfo/getById/${id}`);
}

//批量查询订单
export async function queryOrderInfoList(query){
    return await requestPost<OrderInfoVO[]>(`/orderInfo/queryList`, query);
}

export async function queryOrderInfoByPage(query){
    return await requestPost<API.PageInfo<OrderInfoVO>>(`/orderInfo/queryByPage`, query);
}

export async function queryMerchantTopupByPage(query){
    return await requestPost<API.PageInfo<MerchantTopupVO>>(`/orderInfo/queryMerchantTopupByPage`, query);
}