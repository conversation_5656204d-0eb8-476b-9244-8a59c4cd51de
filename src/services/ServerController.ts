import { requestGet, requestPost } from '@/services/request';
import React from 'react';

type ServerStatus = 'ENABLE' | 'DISABLE' | 'BUSY' | 'UNUSABLE';

export const AllServerTypes = [
  { key: 'IMAGE_GENERATE', value: 'IMAGE_GENERATE', label: '图片/视频服务' },
  { key: 'LORA_TRAIN', value: 'LORA_TRAIN', label: '模型训练' },
  { key: 'LORA_PRE_PROCESS', value: 'LORA_PRE_PROCESS', label: '训练前置处理' },
  { key: 'FILE_SERVICE', value: 'FILE_SERVICE', label: '文件服务' },
  { key: 'REMOVE_BG', value: 'REMOVE_BG', label: '去除背景' },
  { key: 'PICTURE_MATTING', value: 'PICTURE_MATTING', label: '图片抠图' },
];

export interface PipelineVO {
  key: React.Key;
  id: number;
  name: string;
  memo: string;
  userRelation: JSON;
  servers: Array<ServerVO>;
  createTime: string;
}

export interface ServerVO {
  key: React.Key;
  id: number;
  name: string;
  level: number;
  config: string;
  intranetAddress: string;
  parentId: number;
  pipelineId: number;
  type: string;
  typeName: string;
  status: ServerStatus;
  realtimeStatus: ServerStatus;
  realtimeTaskId: number;
  statusName: string;
  realtimeStatusName: string;
  children?: Array<ServerVO> | null;
  ports?: Array<ServerVO> | null;
  createTime: string;
  modifyTime: string;
  isEdit?: boolean;
  deviceId: string;
}

export interface MachineRoom {
  id: string;
  name: string;
  machines: Array<MachineInfo>;
}

export interface MachineInfo {
  id: string;
  name: string;
  publicAddress: string;
  internalAddress?: string;
  status: ServerStatus;
  ports: Array<MachinePort>;
}

export interface MachinePort {
  id: string;
  port: number;
  type: string;
  status: ServerStatus;
}

export async function queryAllPipeline() {
  return await requestGet<Array<PipelineVO>>('/pipeline/all');
}

export async function addPipeline(payload) {
  return await requestPost<boolean>('/pipeline/create', payload);
}

export async function updPipeline(payload) {
  return await requestPost<boolean>('/pipeline/updateById', payload);
}

export async function delPipeline(id) {
  return await requestPost<boolean>('/pipeline/deleteById', { id });
}

export async function addServer(payload) {
  return await requestPost<boolean>('/server/create', payload);
}

export async function updServer(payload) {
  return await requestPost<boolean>('/server/updateById', payload);
}

export async function delServer(id) {
  return await requestPost<boolean>('/server/deleteById', { id });
}

export async function testServer(id) {
  return await requestPost<ServerVO>('/server/test', { id });
}

export async function fetchServerRunLog(id) {
  return await requestPost<string>('/server/fetchRunLog', { id });
}

export async function restartServerPort(id) {
  return await requestPost<boolean>('/server/restartPort', { id });
}

export async function restartServer(id, type) {
  return await requestPost<boolean>('/server/restartServer', { id, type });
}

export async function updateCreativeNode(id) {
  return await requestPost<boolean>('/server/updateCreativeNode', { id });
}

export async function fetchAllFileServers() {
  return await requestGet<ServerVO[]>('/server/fetchAllFileServers');
}
