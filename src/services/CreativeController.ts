import { getTaskIdByImage } from '@/utils/utils';
import { fetchGet, fetchPost, fetchPostDownload, requestGet, requestPost, requestPostNoCareResult } from './request';
import { MaterialModel } from '@/services/MaterialModelController';
import RepairDetailGuideBlock from '@/components/Guide/RepairDetailGuideBlock';
import BasicReplaceGuideBlock from '@/components/Guide/BasicReplace/BasicReplaceGuideBlock';
import { MerchantPreferenceVO, PreferenceType } from '@/components/Creative/MerchantPreferenceBlock';
import ClothingSwapExampleContent from '@/components/Guide/ClothingSwapExampleContent';
import ClothingSwapGuideBlock from '@/components/Guide/ClothingSwapGuideBlock';

export type CreativeStatus = 'INIT' | 'QUEUE' | 'PROCESSING' | 'FINISHED' | 'FAILED' | 'MOCK';
export type ElementKey = 'FACE' | 'SCENE' | 'CLOTH_STYLE' | 'CLOTH_COLOR' | 'REFER' | 'LOGO_POSITION';
export type LoraType = 'SYSTEM' | 'CUSTOM';
export type BizType = 'ALL' | 'LOOK' | 'REAL-SCENE-SHOOTING' | 'MODEL-SHOW' | 'NORMAL';
export type CreativeType =
  'CREATE_IMAGE'
  | 'REPAIR_HANDS'
  | 'LOGO_COMBINE'
  | 'IMAGE_UPSCALE'
  | 'CREATE_VIDEO'
  | 'FIX_VIDEO_FACE'
  | 'PARTIAL_REDRAW'
  | 'FACE_SCENE_SWITCH'
  | 'TRYON'
  | 'REMOVE_WRINKLE'
  | 'REPAIR_DETAIL'
  | 'ERASE_BRUSH'
  | 'PICTURE_MATTING'
  | 'BASIC_CHANGING_CLOTHES'
  | 'POSE_SAMPLE_DIAGRAM'
  | 'FACE_PINCHING'
  | 'FIXED_POSTURE_CREATION'
  | 'CLOTHING_SWAP'
  | 'CLOTH_AUTO_SEGMENT'
  | 'CLOTH_RECOLOR'
  ;

export const ALLCreativeType: Array<{ key: string, value: CreativeType; label: string, type: string }> = [
  {
    key: 'CREATE_IMAGE',
    value: 'CREATE_IMAGE',
    label: '服装模特图',
    type: 'IMAGE',
  },
  {
    key: 'LOGO_COMBINE',
    value: 'LOGO_COMBINE',
    label: '印花上身',
    type: 'DESIGN',
  },
  {
    key: 'REPAIR_HANDS',
    value: 'REPAIR_HANDS',
    label: '手部修复',
    type: 'IMAGE',
  },
  {
    key: 'CREATE_VIDEO',
    value: 'CREATE_VIDEO',
    label: '图生视频',
    type: 'VIDEO',
  },
  {
    key: 'FIX_VIDEO_FACE',
    value: 'FIX_VIDEO_FACE',
    label: '视频修脸',
    type: 'VIDEO',
  },
  {
    key: 'FACE_SCENE_SWITCH',
    value: 'FACE_SCENE_SWITCH',
    label: '模特换头',
    type: 'IMAGE',
  }, {
    key: 'PARTIAL_REDRAW',
    value: 'PARTIAL_REDRAW',
    label: '局部重绘',
    type: 'IMAGE',
  }, {
    key: 'TRYON',
    value: 'TRYON',
    label: '虚拟试衣',
    type: 'IMAGE',
  },
  {
    key: 'REMOVE_WRINKLE',
    value: 'REMOVE_WRINKLE',
    label: '衣服去皱',
    type: 'IMAGE',
  },
  {
    key: 'REPAIR_DETAIL',
    value: 'REPAIR_DETAIL',
    label: '细节修补',
    type: 'IMAGE',
  },
  {
    key: 'ERASE_BRUSH',
    value: 'ERASE_BRUSH',
    label: '消除笔',
    type: 'IMAGE',
  },
  {
    key: 'BASIC_CHANGING_CLOTHES',
    value: 'BASIC_CHANGING_CLOTHES',
    label: '基础款换衣',
    type: 'IMAGE',
  },
  {
    key: 'POSE_SAMPLE_DIAGRAM',
    value: 'POSE_SAMPLE_DIAGRAM',
    label: '姿势示例图',
    type: 'IMAGE',
  },
  {
    key: 'FACE_PINCHING',
    value: 'FACE_PINCHING',
    label: '模特捏脸',
    type: 'IMAGE',
  },
  {
    key: 'IMAGE_UPSCALE',
    value: 'IMAGE_UPSCALE',
    label: '2倍放大',
    type: 'IMAGE',
  },
  {
    key: 'FIXED_POSTURE_CREATION',
    value: 'FIXED_POSTURE_CREATION',
    label: '固定姿势创作',
    type: 'IMAGE',
  },
  {
    key: 'CLOTHING_SWAP',
    value: 'CLOTHING_SWAP',
    label: '单图换装',
    type: 'IMAGE',
  },
  {
    key: 'CLOTH_RECOLOR',
    value: 'CLOTH_RECOLOR',
    label: '服装换色',
    type: 'IMAGE',
  },
];

export const ALLCreativeStatus: Array<{ value: CreativeStatus; label: string }> = [
  {
    value: 'INIT',
    label: '待开始',
  },
  {
    value: 'QUEUE',
    label: '排队中',
  },
  {
    value: 'PROCESSING',
    label: '生成中',
  },
  {
    value: 'FINISHED',
    label: '已完成',
  },
  {
    value: 'FAILED',
    label: '生成失败',
  },
];

export const ALLBizTypes: Array<{
  key: BizType,
  value: BizType;
  label: string;
  iconType?: string;
  includesTypes?: Array<string>;
  excludesTypes?: Array<string>
}> = [
  {
    key: 'ALL',
    value: 'ALL',
    label: '全部场景',
  },
  {
    key: 'LOOK',
    value: 'LOOK',
    label: '棚拍Look图',
    iconType: 'icon-icon_looktu',
    includesTypes: ['pure background'],
  },
  {
    key: 'REAL-SCENE-SHOOTING',
    value: 'REAL-SCENE-SHOOTING',
    label: '实景拍摄',
    iconType: 'icon-icon_huwaipaishe',
    excludesTypes: ['pure background', 'show'],
  },
  {
    key: 'MODEL-SHOW',
    value: 'MODEL-SHOW',
    label: 'T台秀场',
    iconType: 'icon-icon_motexiuchang',
    includesTypes: ['show'],
  },
];

// 单行类型校验（自动补全提示可用）
export const isValidCreativeType = (s: string): s is CreativeType =>
  ['CREATE_IMAGE', 'REPAIR_HANDS', 'LOGO_COMBINE', 'IMAGE_UPSCALE', 'CREATE_VIDEO', 'FIX_VIDEO_FACE', 'PARTIAL_REDRAW', 'FACE_SCENE_SWITCH', 'TRYON', 'REMOVE_WRINKLE', 'REPAIR_DETAIL', 'ERASE_BRUSH'].includes(s as CreativeType);

export const getCreativeTypeName = (type: CreativeType, bizType: any = null) => {
  const find = ALLCreativeType.find((item) => item.value === type);
  if (bizType === 'LOOK' && find?.value === 'CREATE_IMAGE') {
    return 'LOOK图';
  }
  return find?.label;
};

export interface QueuedCreativeData {
  merchantCnt: number;
  systemCnt: number;
  backUserCnt: number;
}

export interface StatsUserQueuedCreativeDO {
  userId: number;
  nickName: string;
  total: number;
  queueSize: number;
  processingSize: number;
  minCreateTime: string;
}

// 获取创作状态名称
export const getCreativeStatusName = (status: CreativeStatus) => {
  const find = ALLCreativeStatus.find((item) => item.value === status);
  return find?.label;
};

// 是否是示例创作是否是示例创作
export const isExampleImage = (item: CreativeVO) => {
  return item && item.extInfo && item.extInfo['bizTag'] && item.extInfo['bizTag'] === 'exampleImages';
};

// 是否是处理初始化
export const isRefineInit = (item: CreativeVO) => {
  return item && item.extInfo && item.extInfo['refineStatus'] === 'INIT';
};

// 是否是处理完成
export const isRefineCompleted = (item: CreativeVO) => {
  return item && item.extInfo && item.extInfo['refineStatus'] === 'COMPLETED';
};

// 是否是处理中
export const isProcessing = (item: CreativeVO | null | undefined) => {
  return item && item.status !== 'FINISHED' && item.status !== 'FAILED';
};

// 是否是批量点赞创作
export const isBatchLike = (item: CreativeVO, isLike: boolean) => {
  if (!item || !item.extInfo || !item.extInfo['like']) {
    return false;
  }

  const likeTag = isLike ? 'LIKE' : 'DISLIKE';

  return Object.values(item.extInfo['like']).some(value => likeTag === value);
};

// 是否是点赞创作
export const isImageLike = (item: CreativeVO, isLike: boolean, imageUrl: string, idx: number) => {
  if (!item || !item.extInfo || !item.extInfo['like']) {
    return false;
  }

  const likeTag = isLike ? 'LIKE' : 'DISLIKE';
  let id = getTaskIdByImage(imageUrl);
  if (!id) id = idx;

  return item.extInfo['like'][id] === likeTag;
};

// 是否是demo创作
export const isDemoTag = (item: CreativeVO) => {
  return item && item.extInfo && item.extInfo['demoTag'] && item.extInfo['demoTag'] === 'Y';
};

/**
 * 是否能进行图片放大
 * @param imageUrl 图片地址
 */
export const canImageUpscale = (imageUrl: string | null) => {
  return imageUrl && imageUrl.indexOf('i_upscale') < 0;
};

export interface CreativeVO {
  id: number;
  title: string;
  modelId: number;
  showImage: string;
  status: CreativeStatus;
  type: CreativeType;
  extInfo: [];
  createTime: string;
  resultImages?: Array<string>;
  videoClipGenTasks?: Array<VideoClipTask>;
  batchCnt: number;
  imageProportion: string;
  imageProportionName: string;
  operatorNick: string;
  userNick?: string;
  modelName: string;
  modelShowImg: string;
  sceneName: string;
  faceName: string;
  modelMarkedColor: string;
  syncStatus: number;
  reSyncCount: number;
  syncSuccessArray?: Array<number>;
  creativeTasksList: Array<CreativeTaskVO>;
  bizType: BizType;
  timeSecs4Video?: number;
  canShowChangeImg4VideoBatch?: boolean;
  deliveryName?: string;
}

export interface CreativeActiveAndToday {
  active: CreativeVO;
  todayList: Array<CreativeVO>;
}

export interface CreativeTaskVO {
  status: string;
  id: number;
  resultImages?: Array<string>;
  imageProportion: string;
  extInfo: {
    referenceOriginalImage: string;
    resultFaceImage: string;
    resultMaskImage: string;
  };
}

export interface VideoClipTask {
  // creative batch id
  id: number | null;

  //index: 0~3
  index: number | null;

  //tmp video url from kling
  outVideoUrl: string;

  //oss video url
  ossVideoUrl: string;

  //auto gen prompt
  prompt?: string;

  //auto gen duration
  duration?: number | null;

  //auto gen taskId
  taskId?: number | null;

  //auto gen task status
  taskStatus?: string;

  //auto gen outside task id (e.g., kling taskId)
  outTaskId?: string;

  //auto gen outside task platform name (e.g., Kling)
  outTaskPlatform?: string;

  startTime?: string;

  endTime?: string;
}

export const CreateGuideBlockMap = {
  REPAIR_DETAIL: RepairDetailGuideBlock,
  BASIC_CHANGING_CLOTHES: BasicReplaceGuideBlock,
  CLOTHING_SWAP: ClothingSwapGuideBlock,
};

// 创建创作
export async function startCreative(data) {
  return await requestPost<CreativeVO>('/creative/create', data);
}

// 固定姿势创作
export async function fixedPostureCreation(data) {
  return await requestPost<CreativeVO>('/creative/fixedPosture/create', data);
}

// 设置创作失败
export async function setCreativeToFail(id: number) {
  return await requestPost<boolean>('/creative/setToFail', { id });
}

export async function queryActiveAndTodayCreative(types: Array<CreativeType>, loraType: LoraType | null = null, bizType: BizType | null | undefined = 'ALL', isSelectTask: boolean = false) {
  return await requestPost<CreativeActiveAndToday>('/creative/queryActiveAndTodayList', {
    types,
    loraType,
    bizType,
    isSelectTask,
  });
}

export async function queryCreativeById(id: number) {
  return await requestGet<CreativeVO>(`/creative/getById/${id}`);
}

export async function getCreativeBatchByIdWithTask(id: number) {
  return await requestGet<CreativeVO>(`/creative/getById/task/${id}`);
}

export async function queryCreativeAndSync(id: number) {
  return await requestGet<CreativeVO>(`/creative/getAndSync/${id}`);
}

export async function batchQueryCreative(ids: number[], isSelectTask: boolean = false) {
  return await requestPost<Array<CreativeVO>>('/creative/batchQueryAndSync', { ids, isSelectTask });
}

export async function queryCreativesByPage(payload) {
  return await requestPost<API.PageInfo<CreativeVO>>('/creative/queryByPage', payload);
}

export async function cancelCreativeQueue(id: number) {
  return await requestPost<boolean>('/creative/cancel', { id });
}

export async function clearCreative(types: Array<CreativeType>, loraType: LoraType | null = null) {
  return await requestPost<boolean>('/creative/clear', { types, loraType });
}

export async function fetchMagicPrompt(imageName: string) {
  return await requestPost<string>('/creative/fetchPrompt', { imageName });
}

export async function fetchWorkflow(taskId: number) {
  return await requestPost<string>('/creative/fetchWorkflow', { taskId });
}

export async function queryModels4HistoryTasks() {
  return await requestPost<Array<MaterialModel>>('/creative/queryModels4HistoryTasks', {});
}

export async function imageLike(taskId: number, batchId: number | undefined, like: boolean, imageUrl) {
  return await requestPost<boolean>('/creative/imageLike', { taskId, batchId, like, imageUrl});
}

//图片下载打点
export async function monitorImgDownload(batchId: number, imgUrl: string) {
  return await requestPost<boolean>('/creative/monitorImgDownload', { batchId, imgUrl });
}

export async function fetchZipUrl(batchId: number) {
  return await requestPost<string>('/creative/fetchZipUrl', { batchId });
}

export async function fetchTaskInfo(taskId: number) {
  return await requestPost<CreativeTaskVO>('/creative/fetchTaskInfo', { taskId });
}

export async function fetchBatchByTask(taskId: number) {
  return await requestPost<CreativeVO>('/creative/fetchBatchByTask', { taskId });
}

export async function queryExampleImages(modelId: number) {
  return await requestPost<Array<string>>('/creative/queryExampleImages', { modelId });
}

export async function fetchTaskImage(taskId: number) {
  return await fetchGet(`/creative/fetchTaskImage/${taskId}`);
}

export async function repairHands(form) {
  return await fetchPost('/creative/repairHands', form);
}

export async function partialRedraw(form) {
  return await fetchPost('/creative/partialRedraw', form);
}

export async function imageUpscale(taskId: number | null, upscaleImage: string, imageSource: string | null = null) {
  return await requestPost<boolean>('/creative/imageUpscale', { taskId, upscaleImage, imageSource });
}

export async function deleteCreative(id) {
  return await requestPost('/creative/deleteById', { id });
}

export async function startLogoCombine(form) {
  return await fetchPost<CreativeVO>('/creative/logoCombine', form);
}

export async function startCreateVideo(form) {
  return await requestPost<CreativeVO>('/creative/createVideo', form);
}

export async function fixVideoFace(payload) {
  return await requestPost<CreativeVO>('/creative/fixVideoFace', payload);
}

export async function removeBg(file, type = 1) {
  const formData = new FormData();
  formData.append('image', file);
  formData.append('type', type + '');
  return await fetchPostDownload('/creative/removeBg', formData);
}

export async function uploadCreativeVideo(payload) {
  return await requestPost<boolean>('/creative/uploadVideo', payload);
}

export async function apply2GenVideoClip(payload) {
  return await requestPost<boolean>('/creative/apply2GenVideoClip', payload);
}

export async function removeFixFaceVideo(id: number, index: number) {
  return await requestPost<boolean>('/creative/removeFixFace', { id, index });
}

export async function assignVideoOperator(id: number, mobile: string) {
  return await requestPost<boolean>('/creative/assignVideoOperator', { id, mobile });
}

export async function queryNeedProcessCreativeCnt() {
  return await requestGet<number>('/creative/queryNeedProcessCnt');
}

export async function applyRefineExample(id: number) {
  return await requestPost<boolean>('/creative/applyRefine', { id });
}

export async function completeRefineExample(id: number) {
  return await requestPost<boolean>('/creative/completeRefine', { id });
}

export async function changeTempVideo(id: number, index: number, videoUrl: string | null) {
  return await requestPost<boolean>('/creative/changeTempVideo', { id, index, videoUrl });
}

export async function resetProcessing(id: number) {
  return await requestPost<boolean>('/creative/resetProcessing', { id });
}

export async function downloadAllImages(id: number, imageUrls?: string[]) {
  return await requestPost<string>('/creative/downloadAll', { id, imageUrls });
}

export async function assignCreativeTo(batchId, userId) {
  return await requestPost<boolean>('/creative/assignTo', { batchId, userId });
}

export async function queryImagesByElementWithPage(payload) {
  return await requestPost<API.PageInfo<string>>('/creative/queryImagesByElementWithPage', payload);
}

export async function queryImagesByElementWithPageFormTask(payload) {
  return await requestPost<API.PageInfo<string>>('/creative/queryImagesByElementWithPageFormTask', payload);
}

export async function queryImagesByElement(elementId, limit, userId, testFlag) {
  return await requestPost<Array<string>>('/creative/queryImagesByElement', { elementId, limit, userId, testFlag });
}

export async function swapFace(data) {
  return await requestPost<CreativeVO>('/creative/swapFace', data);
}

export async function faceSceneSwitch(data) {
  return await requestPost<CreativeVO>('/creative/faceSceneSwitch', data);
}

export async function fetchBatchStyleImagesMap(id) {
  return await requestPost<Record<string, string>>('/creative/fetchStyleImagesMap', { id });
}

export async function createTryonTask(payload) {
  return await requestPost<number>('/creative/createTryonTask', payload);
}

export async function createTryonRefinerTask(payload) {
  return await requestPost<number>('/creative/createTryonRefinerTask', payload);
}

export async function queryTryonTask(payload) {
  return await requestPost<any>('/creative/queryTryonTask', payload);
}

export async function queryTryonTasksByPage(payload) {
  return await requestPost<API.PageInfo<any>>('/creative/queryTryonTasksByPage', payload);
}

export async function queryAllMerchantPreference(type: PreferenceType) {
  return await requestGet<Array<MerchantPreferenceVO>>(`/creative/queryAllMerchantPreference/${type}`);
}

export async function addMerchantPreference(query) {
  return await requestPost<Array<MerchantPreferenceVO>>('/creative/addMerchantPreference', query);
}

export async function updateMerchantPreference(query) {
  return await requestPost<Array<MerchantPreferenceVO>>('/creative/updateMerchantPreference', query);
}

export async function deleteMerchantPreference(query) {
  return await requestPost<Array<MerchantPreferenceVO>>('/creative/deleteMerchantPreference', query);
}

export async function applyRemoveWrinkle(payload) {
  return await requestPost<CreativeVO>('/creative/applyRemoveWrinkle', payload);
}

export async function repairDetail(payload) {
  return await requestPost<CreativeVO>('/creative/repairDetail', payload);
}

export async function clothingSwap(payload) {
  return await requestPost<CreativeVO>('/creative/clothingSwap', payload);
}

export async function eraseBrush(payload) {
  return await requestPost<CreativeVO>('/creative/eraseBrush', payload);
}

export async function clothImgAutoSeg(payload) {
  return await requestPost<CreativeVO>('/creative/clothImgAutoSeg', payload);
}

export async function clothRecolor(payload) {
  return await requestPost<CreativeVO>('/creative/clothRecolor', payload);
}

export async function facePinching(payload) {
  return await requestPost<CreativeVO>('/creative/facePinching', payload);
}

export async function queryCreateImageCnt(modelId) {
  return await requestPost<number>('/creative/queryCreateImageCnt', { modelId });
}

// 图片抠图
export async function pictureMatting(imageUrl: string, type: string, prompt?: string) {
  return await requestPostNoCareResult<CreativeVO>('/creative/pictureMatting', {
    clotheImage: imageUrl,
    clotheType: type,
    prompt: prompt,
  });
}

// 基础款换衣
export async function basicChangingClothes(payload) {
  return await requestPost<CreativeVO>('/creative/basicChangingClothes', payload);
}

export async function addCreativeDemoTag(id) {
  return await requestPost<boolean>('/creative/addDemoTag', { id });
}

// 创建姿势示例图
export async function poseSampleDiagram(payload) {
  return await requestPost<CreativeVO>('/creative/poseSampleDiagram', payload);
}

export async function fetchBatchClothDetailImage(id: number) {
  return await requestGet<string>(`/creative/fetchBatchClothDetailImage/${id}`);
}

// 更新视频任务的图片
export async function updateOriginalImg4Video(payload) {
  return await requestPost('/creative/updateOriginalImg4Video', payload);
}

export async function statsQueuedCreative() {
  return await requestGet<QueuedCreativeData>('/creative/statsQueuedCreative');
}

export async function statsCustomerQueuedCreative() {
  return await requestGet<StatsUserQueuedCreativeDO[]>('/creative/statsCustomerQueuedCreative');
}

export async function statsBackUserQueuedCreative() {
  return await requestGet<StatsUserQueuedCreativeDO[]>('/creative/statsBackUserQueuedCreative');
}

