import { request } from '@umijs/max';
import { getFullUrl, fail, requestPost } from './request';

/**
 * 上传文件到服务器
 * @param url 服务器的URL
 * @param file 要上传的文件
 * @returns Promise，成功时返回服务器响应的URL，失败时返回错误通知
 */
export async function uploadFile(file: File, pro: boolean = false): Promise<string> {

    // 创建 FormData 对象并添加文件
    const formData = new FormData();
    formData.append('file', file);

    // 构造完整的 URL
    const fullUrl = pro ? getFullUrl('/file/uploadPro') : getFullUrl('/file/upload');

    // 发送 POST 请求上传文件
    return request<API.Result>(fullUrl, {
        method: 'POST',
        data: formData,
        // 不要手动设置 Content-Type，浏览器会自动设置，包括正确的 boundary 参数
        // @ts-ignore
        headers: { 'Content-Type': undefined }
    }).then((response) => {

        // @ts-ignore
        if (!response.success) {
            // 如果请求不成功，调用 fail 函数处理错误
            fail(response, '/file/upload', false);
            // 抛出错误以便可以捕获
            throw new Error('File upload failed');
        }

        return response.data as string;

    }).catch((error) => {
        console.log('文件上传失败，请刷新页面后重试:' + error.message);
        return Promise.reject(error);
    });
}

export async function batchDownload(imageUrls: string[], fileName: string|undefined){
    return await requestPost<string>('/file/batchDownload', {fileUrls: imageUrls, fileName: fileName});
}

export async function uploadOss2Input(ossUrl: string) {
    return await requestPost<boolean>('/file/uploadOss2Input', {ossUrl});
}

export async function batchUploadOss2Input(ossUrls: string[]) {
    return await requestPost<boolean>('/file/batchUploadOss2Input', {ossUrls});
}

export async function syncFile(path: string) {
    return await requestPost<boolean>('/file/syncFile', {path});
}