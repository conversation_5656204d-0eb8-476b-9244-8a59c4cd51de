import { requestGet, requestPost } from '@/services/request';

export interface CodeVO {
    id?: number;
    code?: string;
    codeType: string;
    codeStatus: string;
    codeInfo?: any;
    relatedUserId?: number;
    relatedUserName?: string;
    creatorId?: number;
    creatorMasterId?: number;
    modifierId?: number;
    extInfo?: string;
    createTime?: string;
    modifyTime?: string;
}

// 创建推广注册码
export async function createRegisterPromotionCode(params) {
    return await requestPost<number>('/code/createRegisterPromotionCode', params);
}

// 分页查询推广注册码
export async function queryCodeByPage(params) {
    return await requestPost<API.PageInfo<CodeVO>>('/code/queryByPage', params);
}

// 更新推广注册码
export async function updateCode(params) {
    return requestPost('/code/updateById', params);
}

//批量查询推广注册码
export async function queryCodeList(params) {
    return requestPost<Array<CodeVO>>('/code/queryList', params);
}