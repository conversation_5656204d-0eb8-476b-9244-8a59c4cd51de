import { fetchPostDownload, requestGet, requestPost, requestPostNoCareResult } from './request';
import { OperatorVO } from '@/services/UserController';
import { ClothCollocation } from '@/services/MaterialModelController';
import { MachineRoom } from '@/services/ServerController';

export interface SystemVO {
  id: number;
  confKey: string;
  confValue: string;
  memo: string;
  status: string;
}

export interface OSSToken {
  accessKeyId: string;
  accessKeySecret: string;
  expiration: string;
  securityToken: string;
  bucketName: string;
  region: string;
}

export interface MerchantPreference {
  userId: number;
  nickName: string;
  memo: string;
  faces: Array<string>;
  scenes: Array<string>;
  clothCollocation: ClothCollocation;
  preferences: Array<MerchantSubPreference>;
  creativePreferences: Array<MerchantSubPreference>;
}

export interface MerchantSubPreference {
  type: string;
  tags: Array<string>;
  memo: string;
  faces: Array<number>;
  scenes: Array<number>;
  clothCollocation: ClothCollocation;
  enableAutoCreative: boolean;
  imageNum: number;
  imageProportion: string;
}

export interface ExperienceModelOpenDetail {
  id: number;
  faces: Array<number>;
  scenes: Array<number>;
}

// 图片案例同步配置
export interface ImageCaseSyncConfig {
  // BadCase
  badCase: {
    targetServer: string,
    targetPath: string,
  },
  // goodCase
  goodCase: {
    targetServer: string,
    targetPath: string,
  },
  // manualReplacement
  manualReplacement: {
    targetServer: string,
    targetPath: string,
  },
  // lowQuality
  lowQuality: {
    targetServer: string,
    targetPath: string,
  }
}

// 首页登录注册配置
export interface HomeLoginRegisterConfig {
  isOpenRegister: boolean;
  isOpenWechatLogin: boolean;
}

export async function queryAllSys() {
  return await requestGet<Array<SystemVO>>('/sys/all');
}

export async function updateSys(payload) {
  return await requestPost<boolean>('/sys/update', payload);
}

export async function addSys(payload) {
  return await requestPost<boolean>('/sys/add', payload);
}

export async function delSys(id) {
  return await requestPost<boolean>('/sys/delete', { id });
}

export async function fetchOSSToken() {
  return await requestGet<OSSToken>('/sys/oss/fetchToken');
}

export async function downloadOss(url: string) {
  const formData = new FormData();
  formData.append('url', url);
  return await fetchPostDownload('/sys/oss/download', formData);
}

export async function resizeShowImage(payload) {
  return await requestPost<boolean>('/sys/resize/showImage', payload);
}

export async function queryAllOperators() {
  return await requestGet<Array<OperatorVO>>('/sys/queryOperators');
}

export async function queryModelReplaceKey() {
  return await requestGet<string>('/sys/queryModelReplaceKey');
}

export async function canPublishComfyuiWorkflow(){
  return await requestGet<boolean>('/sys/canPublishComfyuiWorkflow');
}

export async function updateMerchantPreference(payload) {
  return await requestPost<boolean>('/sys/updateMerchantPreference', payload);
}

export async function queryAllMerchantPreference() {
  return await requestGet<Array<MerchantPreference>>('/sys/queryAllMerchantPreference');
}

export async function queryMerchantPreference(userId: number) {
  return await requestPost<MerchantPreference>('/sys/queryMerchantPreference', { userId });
}

export async function queryMerchantConfigs() {
  return await requestGet<any>('/sys/queryMerchantConfigs');
}

export async function queryCreativePreference() {
  return await requestGet<Array<MerchantSubPreference>>('/sys/queryCreativePreference');
}

export async function queryMenusCfg() {
  return await requestGet<string>('/sys/queryMenusCfg');
}

export async function queryAllExperienceModelOpenCfg() {
  return await requestGet<Array<ExperienceModelOpenDetail>>('/sys/queryAllExperienceModelOpenCfg');
}

export async function queryExperienceModelOpenCfgById(id) {
  return await requestPost<ExperienceModelOpenDetail>('/sys/queryExperienceModelOpenCfgById', { id });
}

export async function modifyExperienceModelOpenCfg(payload) {
  return await requestPost<ExperienceModelOpenDetail>('/sys/modifyExperienceModelOpenCfg', payload);
}

/**
 * 埋点事件上报
 * @param requestBody 请求体参数
 */
export async function record(requestBody: {} | undefined) {
  return await requestPostNoCareResult<boolean>(`/eventTrackingRecord/record`, requestBody);
}

// 获取图片同步配置
export async function queryImageCaseSyncConfig() {
  return await requestGet<string>('/sys/queryImageCaseSyncConfig');
}

export async function queryConfigByKeys(keys: string[]) {
  return await requestPost<any>('/sys/queryConfigByKeys', { keys });
}

export async function queryLoraVipCfg() {
  return await requestGet<number[]>('/sys/queryLoraVipCfg');
}

export async function modifyDeviceInfo(rooms) {
  return await requestPost<boolean>('/sys/modifyDeviceInfo', { rooms });
}

export async function checkCanShowProportion(payload) {
  return await requestPost<boolean>('/sys/checkCanShowProportion', payload);
}

export async function queryDeviceInfo() {
  return await requestGet<MachineRoom[]>('/sys/queryDeviceInfo');
}

export async function queryHomeLoginRegisterConfig() {
  return await requestGet<HomeLoginRegisterConfig>('/sys/queryHomeLoginRegisterConfig');
}