import {requestGet, requestPost} from './request';


export interface TrainParam {
    id?: number;
    key: string;
    trainResolution: string;
    contentOrStyle: string;
    loraRank: number;
    alpha: number;
    trainStep: number;
    lr: number;
    dropout: number;
    relatedLoraModelName?: string;
    relatedLoraModelId?: number;
}

export interface TrainPlanForm {
    id?: number;
    planName: string;
    clothingId: number;
    clothingName: string;
    remarks: string;
    trainParams: TrainParam[];
    faceModels: number[];
    scenes: number[];
    sizes: string[];
    imagesPerCombination: number;
}

export interface TrainPlanVO extends TrainPlanForm {
    createTime: string;
    creatorUserName: string;
    trainParams: TrainParam[];
}

export interface BatchParams {
    trainResolutions: string[];
    contentOrStyles: string[];
    ranks: number[];
    alphas: number[];
    trainSteps: number[];
    lrs: number[];
    dropouts: number[];
}

export async function createTrainPlan(payload) {
    return await requestPost<Number>('/trainPlan/create', payload);
}

export async function queryAllTrainPlans(payload){
    return await requestPost<TrainPlanVO[]>('/trainPlan/findAll', payload);
}