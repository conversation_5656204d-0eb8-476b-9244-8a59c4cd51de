import { requestGet, requestPost } from './request';

export type ClothType = {
  key: string;
  desc: string;
};

export type MaterialType = 'cloth' | 'face' | 'scene';

export const ClothTypes: Record<string, ClothType> = {
  Tops: { key: 'Tops', desc: '上装' },
  Bottoms: { key: 'Bottoms', desc: '下装' },
  TwoPiece: { key: 'TwoPiece', desc: '上下装' },
  SwimSuit: { key: 'SwimSuit', desc: '泳装' },
  OnePiece: { key: 'OnePiece', desc: '连体衣' },
};

export interface MaterialInfoVO {
  /**
   * id
   */
  id: number;

  /**
   * 名称
   */
  name: string;

  /**
   * 类型
   */
  type: string;

  /**
   * 子类型
   */
  subType: string;

  /**
   * 扩展信息，是否生成背面照等
   */
  extInfo: Record<string, any>;

  /**
   * 归属主账号id
   */
  userId: number;

  /**
   * 操作者id
   */
  operatorId: number;

  /**
   * 创建时间
   */
  createTime: string; // 使用 ISO 格式的字符串

  /**
   * 修改时间
   */
  modifyTime: string; // 使用 ISO 格式的字符串

  /**
   * 素材详情
   */
  materialDetail: ClothMaterialDetail;
}

export interface ClothMaterialDetail {
  fullShotImgList?: ClothMaterialImg[];
  detailShotImgList?: ClothMaterialImg[];
  moreImgList?: ClothMaterialImg[];

  imgUrls?: string[];
}

export interface ClothMaterialImg {
  imgUrl: string;
  viewTags: string;
}

export const getClothDescByKey = (key: string): string => ClothTypes[key]?.desc;

export const getSexTypeDescByKey = (sex: string) => {
  switch (sex) {
    case 'male':
      return '男';
    case 'female':
      return '女';
    case 'unisex':
      return '男女通用';
    default:
      throw new Error('不支持的字段：' + sex);
  }
}

// 定义fullExamCfg和detailExamCfg数组中每个对象的类型
export interface ExamCfgItem {
  title: string;
  exampleImgUrl: Map<string, string>;
  exampleTitle: string;
  viewTags: string;
}

// 定义整个JSON数据的类型
export interface ExamConfig {
  fullExamCfg: ExamCfgItem[];
  detailUpperExamCfg: ExamCfgItem[];
  detailLowerExamCfg: ExamCfgItem[];
  moreExamCfg: ExamCfgItem[];
}

export async function getClothExamCfg() {
  return await requestGet<ExamConfig>('/materialInfo/clothExamCfg');
}

export async function getMaxColorNumberCfg() {
  return await requestPost<Map<string, number>>('/materialInfo/getMaxColorNumberCfg');
}

export async function getMaterialInfoById(id: number) {
  return await requestGet<MaterialInfoVO>(`/materialInfo/getById/${id}`);
}

// 上传素材
export async function uploadMaterial(data) {
  return await requestPost<number>('/materialInfo/create', data);
}

export async function uploadCommonMaterialInfo(data) {
  return await requestPost<number>('/materialInfo/uploadCommonMaterialInfo', data);
}

export async function updateMaterialById(data) {
  return await requestPost<boolean>('/materialInfo/updateById', data);
}

export async function checkMaterialNameExists(name: string) {
  return await requestPost<boolean>('/materialInfo/checkMaterialNameExists', { name });
}