import { requestGet, requestPost, getFullUrl } from '@/services/request';
import { UserOptionVO, UserVO } from './UserController';
import { MaterialModel } from './MaterialModelController';

export interface DeliveryVO {
  /** 日期 */
  date: string;
  /** 总数 */
  total: number;
  /** 自动交付数 */
  autoDelivery: number;
  /** vip交付情况 */
  vip: DeliveryItem;
  /** 普通用户交付情况 */
  normal: DeliveryItem;
}

export interface PageInfo<T> {
  list: T[];
  totalCount: number;
  hasNextPage?: boolean;
}

export interface DeliveryItem {
  todo: number;
  in24: number;
  in48: number;
  other: number;
  unknown: number;
  total: number;
}

export interface StatsUserPointVO {
  /** 记录ID */
  id?: number;
  /** 用户ID（0表示所有用户汇总数据） */
  userId?: number;
  /** 用户名称 */
  nickName?: string;
  /** 所属渠道商 */
  distributorCorpName?: string;
  /** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
  statsType?: string;
  /** 统计日期下限 */
  statsDateLower?: string;
  /** 统计日期上限 */
  statsDateUpper?: string;
  /** 统计日期，格式根据统计类型不同 */
  statsDate?: string;
  /** 消耗的算力点 */
  pointConsumed?: number;
  /** 消耗的赠送点 */
  givePointConsumed?: number;
  /** 消耗的体验点 */
  expPointConsumed?: number;
  /** 消耗的套内点 */
  modelPointConsumed?: number;
  /** 充值金额 */
  rechargeAmount?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改时间 */
  modifyTime?: string;
}

export interface StatsClothesInfoVO {
  /** id */
  id?: number;
  /** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
  statsType?: string;
  /** 统计日期: 格式为yyyy-MM-dd */
  statsDate?: string;
  /** vip 用户上传服装 套数 */
  vipClothesCount?: number;
  /** 自动训练服装 套数 */
  autoTrainCount?: number;
  /** 人工交付服装 套数 */
  manualDeliveryCount?: number;
  /** 自动训练+交付 套数 */
  autoTrainAndDeliveryCount?: number;
  /** 二次抠图（手动上传图片+系统级抠图） 套数 */
  retryMattingCount?: number;
  /** 更新提示词 套数 */
  updatePromptCount?: number;
  /** 克隆服装 套数 */
  copyCount?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改时间 */
  modifyTime?: string;
  /** 扩展字段 */
  extInfo?: Object;
}

export interface StatsUserOperateVO {
  /** id */
  id?: number;
  /** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
  statsType?: string;
  /** 统计日期: 格式为yyyy-MM-dd */
  statsDate?: string;
  /** 用户 id */
  userId?: number;
  /** 服装 id */
  materialId?: number;
  /** 用户类型（主账户：MASTER  子账号：SUB） */
  userType?: string;
  /** 用户出图量 */
  createCount?: number;
  /** 图片下载量 */
  downloadCount?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  modifyTime?: string;
  /** 扩展字段 */
  extInfo?: {
    loginId?: string;
    nickname?: string;
    modelName?: string;
    showImage?: string;
  };
}

/**
 * 服装负责人统计
 */
export interface StatsMaterialOwnerVO {
  /** id */
  id?: number;
  /** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
  statsType?: string;
  /** 统计日期: 格式为yyyy-MM-dd */
  statsDate?: string;
  /** 用户 id（为 0 时则是汇总） */
  userId?: number;
  /** 用户名称 */
  nickname?: string;
  /** 交付数量 */
  deliveryCount?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改时间 */
  modifyTime?: string;
  /** 扩展字段 */
  extInfo?: Object;
  /** 服装列表 */
  materialList?: Array<{
    /** 服装 id */
    id?: number;
    /** 服装名称 */
    name?: string;
    /** 服装展示图 */
    showImage?: string;
    /** 扩展字段 */
    extInfo?: {
      /** 交付时间 */
      deliveryTime?: string;
    };
  }>;
}

/**
 * 销售指标统计
 */
export interface StatsSaleIndicatorsVO {
  /** id */
  id?: number;
  /** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
  statsType?: string;
  /** 统计日期: 格式为yyyy-MM-dd */
  statsDate?: string;
  /** 用户 id（为 0 时则是汇总） */
  userId?: number;
  /** 用户名称 */
  nickname?: string;
  /** 父级ID（渠道ID，默认为0表示顶级渠道） */
  parentId?: number;
  /** 名称（渠道/销售/运营） */
  name?: string;
  /** 服装体验量 */
  clothesExpCount?: number;
  /** 客户转换量（新签3999以上） */
  customerConversionCount?: number;
  /** 客户消耗点数 */
  customerConsumptionPoints?: number;
  /** 活跃客户率 */
  customerActivityRate?: string;
  /** 客户复购率 */
  customerRepurchaseRate?: string;
  /** 定制模特数量 */
  customModelCustomers?: string;
  /** 定制场景数量 */
  customSceneCustomers?: string;
  /** 大于60天未充值的客户 */
  customerProtectionMetrics?: number;
  /** 销售出图数量 */
  createCount?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改时间 */
  modifyTime?: string;
  /** 扩展字段 */
  extInfo?: Object;
  /** 销售记录列表 */
  saleRecords?: Array<{
    /** 订单 id */
    id?: number;
    /** 订单编号 */
    orderNo?: string;
    /** 订单金额 */
    amount?: number;
    /** 客户名称 */
    customerName?: string;
    /** 订单日期 */
    orderDate?: string;
    /** 扩展字段 */
    extInfo?: {
      /** 订单状态 */
      status?: string;
      /** 支付方式 */
      paymentMethod?: string;
    };
  }>;
  /** 日期销售数据列表 */
  dateCountList?: Array<{
    /** 日期 */
    date: string;
    /** 销售额 */
    amount: number;
    /** 订单数 */
    count: number;
  }>;
  /** 下级销售人员列表 */
  children?: StatsSaleIndicatorsVO[];
}

/**
 * 运营指标统计
 */
export interface StatsOperateIndicatorsVO {
  /** id */
  id?: number;
  /** 统计类型：DAILY/WEEKLY/MONTHLY/QUARTERLY/TOTAL */
  statsType?: string;
  /** 统计日期: 格式为yyyy-MM-dd */
  statsDate?: string;
  /** 用户 id（渠道/销售/运营） */
  userId?: number;
  /** 名称（渠道/销售/运营） */
  name?: string;
  /** 客户转换量（新签 3999 以上） */
  customerConversionCount?: number;
  /** 客户消耗点数 */
  customerConsumptionPoints?: number;
  /** 平均消耗点数 */
  customerConsumptionPointsAvg?: string;
  /** 活跃客户率 */
  customerActivityRate?: string;
  /** 客户复购率 */
  customerRepurchaseRate?: string;
  /** 定制模特数量 */
  customModelCustomers?: string;
  /** 定制场景数量 */
  customSceneCustomers?: string;
  /** 大于 60 天未充值的客户 */
  customerProtectionMetrics?: number;
  /** 交付服装量 */
  deliveryClothingCount?: number;
  /** 审核服装量 */
  approveClothingCount?: number;
  /** 审核错误率 */
  approveErrorRate?: string;
  /** 服装返点率 */
  garmentRebateRate?: string;
  /** 客户投诉率 */
  customerComplaintRate?: string;
  /** 创建时间 */
  createTime?: string;
  /** 修改时间 */
  modifyTime?: string;
  /** 扩展字段 */
  extInfo?: {
    customModelCustomersDenominator?: number;
    customModelCustomersMolecular?: number;
    customSceneCustomersDenominator?: number;
    customSceneCustomersMolecular?: number;
    customerActivityRateDenominator?: number;
    customerActivityRateMolecular?: number;
    customerRepurchaseRateDenominator?: number;
    customerRepurchaseRateMolecular?: number;
    garmentRebateRateDenominator?: number;
    garmentRebateRateMolecular?: number;
  };
  customerTotalCount: number;
  customerUploadMaterialCount: number;
  videoCount: number;
  videoCountAvg: string;
  /** 下级运营人员列表 */
  children?: StatsOperateIndicatorsVO[];
  /** 角色类型：ENGINEER/REVIEWER */
  roleType?: string;
}

/**
 * 预警信息统计
 */
export interface StatsWarningInfoVO {
  /** id */
  id?: number;
  /** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
  statsType?: string;
  /** 统计日期: 格式为yyyy-MM-dd */
  statsDate?: string;
  /** 周内不消耗客户 */
  weeklyNoConsumptionRate?: string;
  /** 月内不消耗客户 */
  monthlyNoConsumptionRate?: string;
  /** 用户退款率大于百分5x的客户数量 */
  customerRefundRateCount?: number;
  /** 交付超过 24 小时的服装量 */
  deliveryTimeoutCount?: number;
  /** 客户余额预警（缪斯点小于 2000 或 小于累计充值金额为基础的30%） */
  customerBalanceAlertCount?: number;
  /** 客户入库时间超过 60 天未转化数量 */
  customerNotConvertCount?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改时间 */
  modifyTime?: string;
  /** 扩展字段 */
  extInfo?: Object;
  /** 周内未消费用户列表 */
  weeklyUserList?: Array<UserVO>;
  /** 月内未消费用户列表 */
  monthlyUserList?: Array<UserVO>;
  /** 用户退点列表 */
  customerRefundUser?: Array<UserVO>;
  /** 交付超时服装列表 */
  deliveryTimeoutMaterial?: Array<MaterialModel>;
  /** 用户余额预警信息 */
  balanceWarningUser?: Array<UserOptionVO>;
  /** 60天内未转换用户 */
  sixtyDaysNoConvertUserList?: Array<UserVO>;
}

// 用户创作数据接口
export interface UserCreativeData {
  /** 最近创作数量 */
  recentCreateCount?: number;
  /** 最常用服装名称 */
  mostUsedClothName?: string;

  /** 其他数据字段 */
  [key: string]: any;
}

export async function statsDelivery(startDate: string | null, endDate: string | null) {
  return await requestPost<Array<DeliveryVO>>('/data/delivery', { startDate, endDate });
}

// 用户积分统计
export async function statsUserPoint(payload: any) {
  return await requestPost<Array<StatsUserPointVO>>('/statsUserPoint/queryByPage', payload);
}

// 服装信息统计
export async function statsClothesInfo(payload: any) {
  return await requestPost<Array<StatsClothesInfoVO>>('/statsClothesInfo/queryByPage', payload);
}

// 用户操作统计
export async function statsUserOperate(payload: any) {
  return await requestPost<Array<StatsUserOperateVO>>('/statsUserOperate/queryByPage', payload);
}

// 获取用户创作数据统计
export async function getUserCreativeData(userId: number, isParent: boolean) {
  return await requestGet<Map<string, any>>('/statsUserOperate/getUserCreativeData', { userId, isParent });
}

// 服装负责人统计
export async function statsMaterialOwner(payload: any) {
  return await requestPost<Array<StatsMaterialOwnerVO>>('/statsMaterialOwner/queryByPage', payload);
}

// 销售指标统计
export async function statsSaleIndicators(payload: any) {
  return await requestPost<Array<StatsSaleIndicatorsVO>>('/statsSaleIndicators/queryByPage', payload);
}

// 运营指标统计
export async function statsOperateIndicators(payload: any) {
  return await requestPost<Array<StatsOperateIndicatorsVO>>('/statsOperationIndicators/queryByPage', payload);
}

// 下载运营指标数据
export async function download(payload: any) {
  const response = await fetch(getFullUrl('/statsOperationIndicators/download'), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payload),
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.blob();
}

// 预警信息统计
export async function statsWarningInfo(payload: any) {
  return await requestPost<Array<StatsWarningInfoVO>>('/statsWarningInfo/queryByPage', payload);
}

// 获取预警详细信息
export async function getWarningInfo(id: number) {
  return await requestGet<StatsWarningInfoVO>(`/statsWarningInfo/getWarningInfo/${id}`);
}

