import { requestPost, requestGet } from './request';
import { DistributorBasicVO } from '@/services/SettlementController';

type DistributorSettleStatus = 'PENDING_SETTLE' | 'SETTLE_FINISH';

export interface DistributorSettlementVO {
  /** id */
  id: number;

  /** 主体类型 */
  principalType: string;

  /** 主体id */
  principalId: number;

  /** 结算id，前8位是YYYYMMDD */
  settleId: string;

  /**状态，0待结算，1已结算*/
  status: DistributorSettleStatus;
  statusName: string;

  /** 结算类型，1系统结算，2手工结算 */
  settleType: number;

  /** 总金额 */
  totalAmount: number;

  /** 结算金额，结算给渠道商的总金额 */
  settleAmount: number;

  /** 结算订单笔数 */
  orderNum: number;

  /** 外部业务单号，如银行流水号 */
  outBizNo: string;

  /** 渠道商实体id */
  distributorCorpId: number;

  /** 渠道商实体名称 */
  distributorCorpName: string;

  /** 结算日期 */
  settleTime: string;

  /** 扩展信息 */
  extInfo: string;

  /** 创建时间 */
  createTime: string;

  /** 修改时间 */
  modifyTime: string;

  /** 基础信息 */
  basicInfo: DistributorBasicVO;
}


export interface SystemConfigVO {
  id: number;
  confKey: string;
  confValue: string;
  confValueNext: string;
  status: any;
  effectTime: string;
  memo?: string;
  operatorId: number;
  createTime: string;
  modifyTime: string;
}

export interface DistributorSettleRateModel {
  distributorSettleRateList: DistributorSettleRateItem[];
}

export interface DistributorSettleRateItem {
  corpId: number;
  corpName: string;
  masterUserId: number;
  masterUserName: number;
  settleRate: SystemConfigVO;
}

export const settleStatusMap : Record<DistributorSettleStatus, {code: number, text: string, status: string, color: string }> = {
  PENDING_SETTLE: {code: 0, text: '未结算', status: 'warning', color: '#FF951C' },
  SETTLE_FINISH: { code: 1, text: '已结算', status: 'success', color: '#00B578' },
};
/** 结算管理分页查询 **/

export async function queryDistributorSettlementListByPage(payload) {
  //ProTable的参数转换 current -> pageNum
  return await requestPost<API.PageInfo<DistributorSettlementVO>>(`/distributorSettlement/queryByPage`, {...payload, pageNum: payload.current || 1});
}

// 费率查询
export async function queryAllDistributorSettleRateConfigs() {
  return await requestGet<DistributorSettleRateModel>(`/distributorSettlement/queryAllDistributorSettleRateConfigs`);
}

// 调整费率
export async function updateServiceRate(payload) {
  return await requestPost<boolean>(`/distributorSettlement/updateServiceRate`, payload);
}

// 手动结算
export async function manualSettle(id: string, bizNo: string) {
  return await requestPost<boolean>(`/distributorSettlement/manualSettle`, {id, outBizNo: bizNo});
}

// 对账单查询
export async function queryOrderSettlementsByPage(payload: any) {
  return await requestPost<API.PageInfo<any>>(`/orderSettlement/queryByPage`, {...payload, pageNum: payload.current || 1});
}

// 查询所有结算主体基础信息
export async function queryAllDistributorPrincipalBasicInfo() {
  return await requestGet<Array<DistributorBasicVO>>(`/distributorSettlement/queryAllDistributorPrincipalBasicInfo`);
}