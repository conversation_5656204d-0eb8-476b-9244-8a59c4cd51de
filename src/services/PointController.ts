import { requestGet, requestPost } from './request';
import { CreativeType } from '@/services/CreativeController';
import { EXPERIENCE_POINT, IMAGE_POINT, IMAGE_POINT_CHANGE_EVENT } from '@/constants';

export interface UserPoint {
  //muse点
  imagePoint: number;
  //赠送点
  givePoint: number;
  experiencePoint: number;
}

export interface PredictVO {
  /** 服装套餐内图片数量 */
  modelPoint?: number;
  /** 赠送图片张数 */
  givePoint?: number;
  /** 缪斯点数,已经除1000 */
  musePoint?: number;
  /** 当前服装套餐内图片数量 */
  currentModelPoint: number;
  /** 当前赠送图片张数 */
  currentGivePoint?: number;
  /** 当前缪斯点数 */
  currentMusePoint?: number;
  /** 是否需要充值 */
  needTopup?: boolean;
}

export const updatePoint = () => {
  queryImagePoint().then(
    (result) => {
      if (result) {
        localStorage.setItem(IMAGE_POINT, result.imagePoint.toString());
        localStorage.setItem(EXPERIENCE_POINT, result.experiencePoint.toString());
        window.dispatchEvent(new Event(IMAGE_POINT_CHANGE_EVENT));
      }
    },
  );
};

export async function queryImagePoint() {
  return await requestGet<UserPoint>('/point/queryByImage');
}

export async function queryImagePointByUserId(userId) {
  return await requestPost<UserPoint>('/point/queryByUserId', { userId });
}

export async function predict(creativeType: CreativeType = 'CREATE_IMAGE', imageNum: number, modelId: null | number = null, isUpload: boolean = false, proportionType: string | null = null) {
  return await requestPost<PredictVO>('/point/predict', { modelId, imageNum, creativeType, isUpload, proportionType });
}

export async function predict4Video(payload) {
  return await requestPost<PredictVO>('/point/predict', {...payload, creativeType: 'CREATE_VIDEO'});
}