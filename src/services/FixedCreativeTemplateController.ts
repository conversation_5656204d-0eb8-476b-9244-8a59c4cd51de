import { requestPost } from '@/services/request';

export interface FixedCreativeTemplateVO {
  id: number;
  userId: number;
  templateName: string;
  templateList: string;
  createTime: string;
  modifyTime: string;
  extInfo: string;
  referenceInfoList: Array<{
    /** 参考图图片 */
    imageUrl: string;
    /** 参考图配置 */
    referenceConfig: any;
    /** 背景标签 */
    backTags: string;
    /** 背景扩展标签 */
    backExtTags: string;
    /** 场景ID，用作loraId */
    loraId: number;
    /** lora路径 */
    loraPath?: string;
  }>;
}

// 创建固定姿势模板
export async function create(params) {
  return await requestPost<number>('/fixedCreativeTemplate/create', params);
}

// 分页查询固定姿势模板
export async function queryByPage(params) {
  return await requestPost<API.PageInfo<FixedCreativeTemplateVO>>('/fixedCreativeTemplate/queryByPage', params);
}

// 更新固定姿势模板
export async function updateById(params) {
  return requestPost('/fixedCreativeTemplate/updateById', params);
}

//批量查询固定姿势模板
export async function queryList(params) {
  return requestPost<Array<FixedCreativeTemplateVO>>('/fixedCreativeTemplate/queryList', params);
}

//删除固定姿势模板
export async function deleteById(params) {
  return requestPost<number>('/fixedCreativeTemplate/deleteById', params);
}