import { requestGet, requestPost } from '@/services/request';

// 图片操作对象
export interface ImageOperateVO {
  originalImageUrl?: string;
  originalImageBase64?: string;
  maskImageUrl?: string;
  maskImageBase64?: string;
  foregroundImageUrl?: string;
  foregroundImageBase64?: string;
}

// 图片操作输出对象
export interface ImageOperateOutputModal {
  taskId: string;
  taskStatus: string;
  submitTime: string;
  scheduledTime: string;
  endTime: string;
  imageUrl: string;
  outputImageUrl: string;
}

// 创建图片擦除任务
export async function createImageErase(serverType: string, imageOperateVO: ImageOperateVO) {
  return await requestPost<string>(`/imageOperate/erase/${serverType}`, imageOperateVO);
}

// 查询图片擦除任务
export async function queryImageErase(serverType: string, taskId: string) {
  return await requestGet<ImageOperateOutputModal>(`/imageOperate/erase/query/${serverType}`, { taskId });
}