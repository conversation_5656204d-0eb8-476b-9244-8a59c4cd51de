import { requestPost } from '@/services/request';

export interface BadCaseTag {
  id: number;
  title: string;
  checked: boolean;
}

export interface ImageCase {
  id: number;
  url: string;
  miniUrl: string;
  tagDetails: Array<BadCaseTag>;
}

export async function queryImageBadCaseTags(imageUrl: string) {
  return await requestPost<Array<BadCaseTag>>('/imageCase/badCaseTags', { imageUrl });
}

export async function addImageBadCaseTag(imageUrl: string, taskId: number, tagId: number, isAdd: boolean, id: number, type: string) {
  return await requestPost<boolean>('/imageCase/addBadCaseTag', { imageUrl, taskId, tagId, isAdd, id, type });
}

export async function queryImageCaseByPage(payload) {
  return await requestPost<API.PageInfo<ImageCase>>('/imageCase/queryByPage', payload);
}