import { requestGet, requestPost } from '@/services/request';

export interface WorkflowTaskVO {
  id?: number;
  bizId?: number;
  type?: string;
  operatorId?: number;
  status?: string;
  createTime?: string;
  modifyTime?: string;
  meta?: { reasons?: string[] };
}

export interface WorkflowTaskQuery {
  pageNum?: number;
  pageSize?: number;
  bizId?: number;
  type?: string;
  operatorId?: number;
  status?: string;
}

/**
 * 根据ID获取工作流任务详情
 * @param id 任务ID
 * @returns 任务详情
 */
export async function getWorkflowTaskById(id: number) {
  return await requestGet<WorkflowTaskVO>(`/workflowTask/getById/${id}`);
}

/**
 * 创建工作流任务
 * @param payload 任务数据
 * @returns 新创建的任务ID
 */
export async function createWorkflowTask(payload: Omit<WorkflowTaskVO, 'id'>) {
  return await requestPost<number>('/workflowTask/create', payload);
}

/**
 * 更新工作流任务
 * @param payload 任务数据（必须包含id）
 * @returns 更新结果
 */
export async function updateWorkflowTask(payload: WorkflowTaskVO) {
  return await requestPost<boolean>('/workflowTask/updateById', payload);
}

/**
 * 更新工作流任务
 * @param payload 任务数据
 * @returns 更新结果
 */
export async function updateWorkflowTaskByBizId(payload: WorkflowTaskVO) {
  return await requestPost<boolean>('/workflowTask/updateByBizId', payload);
}

/**
 * 删除工作流任务
 * @param id 任务ID
 * @returns 删除结果
 */
export async function deleteWorkflowTask(id: number) {
  return await requestPost<boolean>('/workflowTask/deleteById', { id });
}

/**
 * 批量查询工作流任务
 * @param query 查询条件
 * @returns 任务列表
 */
export async function queryWorkflowTaskList(query: Partial<WorkflowTaskQuery>) {
  return await requestPost<WorkflowTaskVO[]>('/workflowTask/queryList', query);
}

/**
 * 批量查询工作流任务
 * @param query 查询条件
 * @returns 任务列表
 */
export async function queryWorkflowTaskGroup(query: Partial<WorkflowTaskQuery>) {
  return await requestPost<Map<number, WorkflowTaskVO[]>>('/workflowTask/queryGroup', query);
}

/**
 * 分页查询工作流任务
 * @param query 查询参数
 * @returns 分页数据
 */
export async function getPageWorkflowTask(query: WorkflowTaskQuery) {
  return await requestPost<API.PageInfo<WorkflowTaskVO>>('/workflowTask/queryByPage', query);
}
