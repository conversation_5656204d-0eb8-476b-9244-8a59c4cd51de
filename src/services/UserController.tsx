import { requestGet, requestPost } from '@/services/request';

export type USER_TYPE = 'MASTER' | 'SUB';
export type USER_STATUS = 'ENABLED' | 'DISABLED' | 'UNDER_REVIEW' | 'REJECT';
export type ROLE_TYPE = 'MERCHANT' | 'ADMIN' | 'OPERATOR' | 'DISTRIBUTOR' | 'SYSTEM' | 'DEMO_ACCOUNT' | 'REVIEWER';
export type CUSTOM_ROLE = 'CHANNEL_ADMIN' | 'SECOND_CHANNEL_ADMIN' | 'OPS_MEMBER' | 'SALES_MEMBER';

export const RoleTypesItems = [
  { key: 'MERCHANT', value: 'MERCHANT', label: '客户' },
  { key: 'DISTRIBUTOR', value: 'DISTRIBUTOR', label: '销售/渠道商' },
  { key: 'OPERATOR', value: 'OPERATOR', label: '运营' },
  { key: 'REVIEWER', value: 'REVIEWER', label: '审核员' },
  { key: 'ADMIN', value: 'ADMIN', label: '管理员' },
  { key: 'DEMO_ACCOUNT', value: 'DEMO_ACCOUNT', label: '演示账号' },
  { key: 'SYSTEM', value: 'SYSTEM', label: '系统调度' },
 ]


export const CustomRoleMap: Record<CUSTOM_ROLE, string> = {
  CHANNEL_ADMIN: '渠道管理员',
  SECOND_CHANNEL_ADMIN: '二级渠道管理员',
  OPS_MEMBER: '运营成员',
  SALES_MEMBER: '销售成员',
}

export interface UserVO {
  id: number;
  masterId: number;
  masterLoginId: string;
  masterNick: string;
  nickName: string;
  mobile: string;
  createTime: string;
  modifyTime: string;
  lastLoginTime: string;
  userType: USER_TYPE;
  roleType: ROLE_TYPE;
  customRole?: string;
  customRoleName?: string;
  customRoleId?: number;
  corpName: string;
  corpOrgId?: number;
  deptOrgId?: number;
  deptOrgName?: string;
  status: USER_STATUS;
  imagePoint: number;
  givePoint: number;
  experiencePoint: number;
  organizationVO?: {
    extInfo?: {
      status?: string;
      name?: string;
      creditCode?: string;
      operName?: string;
    }
  };
  memo?: string;
  promptUserId?: number;
  //yyyyMMDD
  lastVisitDate?: string;
  profiles: Record<string, UserProfile>;

  relatedDistributorCorpId?: number;
  relatedDistributorCorpName?: string;
  relatedSalesUserId?: number;
  relatedSalesUserName?: string;
  // 用户退点率
  refundRate?: string;

  inviteRegister?: string;
  relatedAccounts?: RelatedAccounts;
  contractDate?: string;
}

export interface UserProfile {
  id: number;
  uid: number;
  profileKey: string;
  profileVal: string;
  modifyTime: string;
}

export interface UserOptionVO {
  id: number;
  nickName: string;
  point: number;
}

export interface OperatorVO {
  mobile: string;
  nickName: string;
}

export interface RelatedAccounts {
  relatedMerchantAccount?: UserVO;
  relatedDemoAccount?: UserVO;
}

export async function createUserProfile(payload) {
  return await requestPost<UserProfile>('/userProfile/create', payload);
}

export async function queryUserProfileList(payload) {
  return await requestPost<UserProfile>('/userProfile/queryList', payload);
}

export async function queryUserProfileByKey(key: string) {
  return await requestPost<UserProfile>('/userProfile/queryUserProfileByKey', { key: key });
}

export async function setUserProfileByKey(payload) {
  return await requestPost<boolean>('/userProfile/setUserProfileByKey', payload);
}

export async function getPageUser(payload) {
  return await requestPost<API.PageInfo<UserVO>>('/user/getPageUser', payload);
}

export async function createSubUser(payload) {
  return await requestPost<number>('/user/sub/create', payload);
}

export async function updateSubUser(payload) {
  return await requestPost<number>('/user/sub/update', payload);
}

export async function enableSubUser(uid: number) {
  return await requestPost<number>('/user/sub/enable', { userId: uid });
}

export async function disableSubUser(uid: number) {
  return await requestPost<number>('/user/sub/disable', { userId: uid });
}

export async function deleteUser(uid: number) {
  return await requestPost<number>('/user/deleteById', { id: uid });
}

export async function createUser(payload) {
  return await requestPost<number>('/user/create', payload);
}

export async function updateUser(payload) {
  return await requestPost<number>('/user/update/back', payload);
}

export async function topupPoint(userId: number, imagePoint: number, experiencePoint: number, givePoint: number) {
  return await requestPost<boolean>('/point/topupByImage', { userId, imagePoint, experiencePoint, givePoint });
}

export async function givePoint(userId: number, experiencePoint: number, givePoint: number) {
  return await requestPost<boolean>('/point/give', { userId, experiencePoint, givePoint });
}

export async function adjustMuse(userId: number, musePoint: number, memo: string) {
  return await requestPost<boolean>('/point/adjustMuse', { userId, musePoint, memo });
}

export async function queryAllMaster(roleTypes: null | Array<ROLE_TYPE> = null) {
  return await requestPost<Array<UserVO>>('/user/allMaster', { roleTypes });
}

export async function allByRoleTypes(roleTypes: null | Array<ROLE_TYPE> = null) {
  return await requestPost<Array<UserVO>>('/user/allByRoleTypes', { roleTypes });
}

export async function queryAllSubByUserId(userId: number) {
  return await requestPost<Array<UserVO>>(`/user/querySub/${userId}`);
}


export async function reviewUser(payload) {
  return await requestPost<boolean>('/user/review', payload);
}

export async function assignCustomer2Distributor(payload) {
  return await requestPost<boolean>('/user/assignCustomer2Distributor', payload);
}

export async function getUserCount(payload) {
  return await requestPost<number>('/user/getUserCount', payload);
}

export async function isVipUser() {
  return await requestGet<boolean>('/user/isVip');
}

export async function isTrialAccount() {
  return await requestGet<boolean>('/user/isTrialAccount');
}

export async function topup2User(payload) {
  return await requestPost<number>('/orderInfo/topup2User', payload);
}

export async function genTestMobile(){
  return await requestPost<string>('/user/genTestMobile', {});
}

export async function queryRelatedAccounts(){
  return await requestPost<RelatedAccounts>('/user/queryRelatedAccounts', {});
}

export async function modifyContractDate(userId: number, contractDate: string) {
  return await requestPost<boolean>('/principalInfo/modifyContractDate', {userId, contractDate});
}