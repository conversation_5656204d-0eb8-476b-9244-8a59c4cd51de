import { requestGet, requestPost } from '@/services/request';
import { UserOptionVO, UserVO } from '@/services/UserController';
import { DistributorClothLoraModelVO, MaterialModelWithBlogs } from '@/services/MaterialModelController';
import { CreativeVO } from '@/services/CreativeController';
import { OrganizationVO } from '@/services/OrganizationController';
import { UserPointUsageInfoVO } from '@/services/UserPointLogController';
import { MerchantTopupVO } from '@/services/OrderInfoController';
import { UserPoint } from '@/services/PointController';

export interface DistributorMerchantVO extends UserVO {
  distributorMasterUserId: number;

  distributorOperatorUserId: number;
  distributorOperatorNickName: string;
  distributorOperatorMobile: string;

  distributorSalesUserId: number;
  distributorSalesNickName: string;
  distributorSalesMobile: string;
}

export interface CustomRoleVO {
  code: string;
  name: string;
}

export interface DistributorCustomerPointUsageVO extends UserPointUsageInfoVO {
  distributorOperatorUserId: number;
  distributorOperatorNickName: string;
  distributorOperatorMobile: string;

  distributorSalesUserId: number;
  distributorSalesNickName: string;
  distributorSalesMobile: string;
}

export interface DistributorCustomerTopupVO extends MerchantTopupVO {
  distributorOperatorUserId: number;
  distributorOperatorNickName: string;
  distributorOperatorMobile: string;

  distributorSalesUserId: number;
  distributorSalesNickName: string;
  distributorSalesMobile: string;
}

export interface DistributorPerformanceQueryUserOptionsVO {
  relatedCustomers: UserOptionVO[];
  relatedOperators: UserOptionVO[];
  relatedSales: UserOptionVO[];
}

export interface DistributorCustomerTopupSummary {
  // 总充值客户数
  totalTopupCustomerCount: number;
  // 总充值次数
  totalTopupCount: number;
  // 总充值金额
  totalAmount: string; // BigDecimal in Java typically maps to string in TypeScript

  // 员工充值业绩统计
  topupStaffItems: DistributorCustomerTopupStaffSummary[];
}

export interface DistributorCustomerTopupStaffSummary {
  // 员工id
  staffId: number;
  // 员工名称
  staffName: string;

  // 员工客户-总充值客户数
  totalTopupCustomerCount: number;
  // 员工客户-总充值次数
  totalTopupCount: number;
  // 员工客户-总充值金额
  totalAmount: string; // 同样处理 BigDecimal
}

//创建渠道商员工账号
export async function createStaff(payload) {
  return await requestPost<number>('/distributor/createStaff', payload);
}

//更新渠道商员工账号
export async function updateStaff(payload) {
  return await requestPost<boolean>('/distributor/updateStaff', payload);
}

//创建渠道商客户账号
export async function createDistributorCustomer(payload) {
  return await requestPost<number>('/distributor/createCustomer', payload);
}

//编辑渠道商客户账号
export async function updateDistributorCustomer(payload) {
  return await requestPost<boolean>('/distributor/updateCustomer', payload);
}

//编辑渠道商客户账号
export async function deleteDistributorCustomer(userId: number) {
  return await requestPost<boolean>('/distributor/deleteCustomer', { userId: userId });
}

export async function getDistributorCustomerByPage(payload) {
  return await requestPost<API.PageInfo<DistributorMerchantVO>>('/distributor/queryCustomersByPage', payload);
}

export async function queryAllDistributorSales(payload) {
  return await requestPost<UserVO[]>('/distributor/queryAllSales', payload);
}

export async function queryAllDistributorOperators(payload) {
  return await requestPost<UserVO[]>('/distributor/queryAllOperators', payload);
}

export async function queryAllStaffs(payload) {
  return await requestPost<UserVO[]>('/distributor/queryAllStaffs', payload);
}

//获取渠道商模型列表
export async function queryLoras4Distributor(payload) {
  return await requestPost<API.PageInfo<DistributorClothLoraModelVO>>('/distributor/queryLoraListByPage', payload);
}

export async function queryDistributorCreativeRecordsByPage(payload) {
  return await requestPost<API.PageInfo<CreativeVO>>('/distributor/queryCreativeRecordsByPage', payload);
}

export async function createDept(payload) {
  return await requestPost<number>('/distributor/createDept', payload);
}

export async function getOrganizationTreeByUserId() {
  return await requestPost<OrganizationVO>('/distributor/getOrganizationTreeByUserId', {});
}

export async function updateDept(payload) {
  return await requestPost<boolean>('/distributor/updateDept', payload);
}

export async function deleteDept(payload) {
  return await requestPost<boolean>('/distributor/deleteDept', payload);
}

export async function queryAllCorpRoles(payload) {
  return await requestPost<CustomRoleVO[]>('/distributor/queryAllCorpRoles', payload);
}

export async function queryDeptUsersIncludeSubByPage(payload) {
  return await requestPost<API.PageInfo<UserVO>>('/distributor/queryDeptUsersIncludeSubByPage', payload);
}

export async function queryUsagePerformanceByPage(payload) {
  return await requestPost<API.PageInfo<DistributorCustomerPointUsageVO>>('/distributor/queryUsagePerformanceByPage', payload);
}

export async function queryCustomerTopupByPage(payload) {
  return await requestPost<API.PageInfo<DistributorCustomerTopupVO>>('/distributor/queryCustomerTopupByPage', payload);
}

export async function queryTopupSummary(payload) {
  return await requestPost<DistributorCustomerTopupSummary>('/distributor/queryTopupSummary', payload);
}

export async function queryUserOptions4TopupPerformance(payload) {
  return await requestPost<DistributorPerformanceQueryUserOptionsVO>('/distributor/queryUserOptions4TopupPerformance', payload);
}

export async function queryUserOptions4UsagePerformance(payload) {
  return await requestPost<DistributorPerformanceQueryUserOptionsVO>('/distributor/queryUserOptions4UsagePerformance', payload);
}

export async function queryCustomerMusePoint(payload) {
  return await requestPost<UserPoint>('/distributor/queryCustomerMusePoint', payload);
}

export async function queryCustomersOptionsByDistributorMasterId(distributorMasterId: number) {
  return await requestPost<UserOptionVO[]>('/distributor/queryCustomersOptionsByDistributorMasterId', { distributorMasterId: distributorMasterId });
}

/**
 * 查询组织下的直接关联账号, 除了运营 (所有需要结算的账号)
 * @param orgId
 * @returns
 */
export async function queryDirectSubAccounts(orgId: number) {
  return await requestPost<UserVO[]>('/distributor/queryDirectSubAccounts', {orgId: orgId});
}