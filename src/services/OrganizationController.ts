import { requestGet, requestPost } from "./request";

export type SalesType = string | 'AGENT_NORMAL' | 'AGENT_EXCLUSIVE' | 'DIRECT';

export const salesTypeMap = {
  AGENT_NORMAL: {code: 'AGENT_NORMAL', desc: '外部普通', color: 'grey'},
  AGENT_EXCLUSIVE: {code: 'AGENT_EXCLUSIVE', desc: '外部独代', color: 'cyan'},
  DIRECT: {code: 'DIRECT', desc: '直营', color: 'orange'},
}

export interface OrganizationVO {
  /** 组织ID */
  id: number;

  /** 父级组织ID，根组织为0 */
  parentId?: number;

  /** 是否根结点组织，0不是，1是 */
  root?: boolean;

  /** 组织类型，DISTRIBUTOR_ORG：渠道商组织 */
  orgType?: string;

  /** 组织名称 */
  name: string;

  /** 组织标签，作为组织类型的补充，预留 */
  tags?: string;

  /** 组织层级，根组织为0，根组织下有1级组织，1级组织下有2级组织，以此类推 */
  orgLevel?: number;

  /** 主账号id */
  channelAdminId?: number;

  /** 主账号昵称 */
  channelAdminNickName?: string;

  /** 创建者主账号id */
  creatorMasterUserId?: number;

  /** 创建人角色类型，DISTRIBUTOR：渠道商 */
  creatorUserRoleType?: string;

  /** 创建人操作员账号id */
  creatorOperatorUserId?: number;

  /** 最近修改人的操作员账号id */
  modifierOperatorUserId?: number;

  /** 扩展信息 */
  extInfo?: any;

  /** 创建时间 */
  createTime?: string;

  /** 修改时间 */
  modifyTime?: string;

  /** 下级部门 */
  children?: OrganizationVO[];

  /** 销售类型 */
  salesType?: SalesType;
}

export const IS_DIRECT_TAG = 'direct';

export function findOrganizationById(data: OrganizationVO, id: number): OrganizationVO | undefined {
  if (data.id === id) {
    return data;
  }
  if (data.children) {
    for (const item of data?.children) {
      const found = findOrganizationById(item, id);
      if (found) {
        return found;
      }
    }
  }
  return undefined;
}


// 企查查信息 VO 类
export interface QiChaChaModelVO {
  keyNo: string;
  no: string;
  name: string;
  creditCode: string;
  operName: string;
  status: string;
  address: string;
  startDate: string;
}

interface QiChaChaResponse {
  pageIndex: number;
  pageSize: number;
  total: number;
  data: QiChaChaModelVO[];
}

export interface OrgTree {
  key: number;
  label: string;
  value: number;
  children?: Array<OrgTree>;
}

export const convertOrgsToTree = (data: OrganizationVO[]): OrgTree[] => {
  return data.map(item => ({
    key: item.id,
    label: item.name,
    value: item.id,
    children: item.children?.map(child => ({
      key: child.id,
      label: child.name,
      value: child.id,
    })),
  }));
};

/**
 * 企查查模糊查询
 * @param corpName 企业名称
 * @param pageIndex 页码
 */
export async function qiChaChaFuzzyQuery(corpName: string, pageIndex: number) {
  return await requestGet<QiChaChaResponse>(`/organization/qiChaCha/query`, {
    corpName,
    pageIndex
  });
}

/**
 * 更新组织扩展信息
 * @param id 组织ID
 * @param QiChaChaModelVO 企查查信息
 */
export async function updateCorpAuthInfo(id: number, userId: number | undefined, extInfo: QiChaChaModelVO) {
  return await requestPost<OrganizationVO>(`/organization/updateCorpAuthInfo`, {
    id: id,
    userId: userId,
    extInfo: extInfo
  });
}

/**
 * 查询渠道组织树列表
 */
export async function queryDistributorOrganizationTrees(){
  return await requestPost<OrganizationVO[]>(`/organization/queryDistributorOrganizationTrees`, {});
}

/** 分页查询渠道根组织 */
export async function queryRootCorpByPage(payload) {
  return await requestPost<API.PageInfo<OrganizationVO>>('/organization/queryRootCorpByPage', payload)
}


