
// Exporting the PointLogTypeEnum TypeScript enum
import { fetchPost, fetchPostDownload, fetchPostJsonDownload, requestPost } from '@/services/request';
import { ROLE_TYPE } from '@/services/UserController';
import { downloadExcel } from '@/utils/downloadUtils';

export enum PointLogTypeEnum {
  REGISTER_GIVE = 'REGISTER_GIVE',
  RECHARGE = 'RECHARGE',
  RECHARGE_MANUAL = 'RECHARGE_MANUAL',
  LORA_TRAIN = 'LORA_TRAIN',
  EXPERIENCE_CREATE = 'EXPERIENCE_CREATE',
  EXPERIENCE_RETURN = 'EXPERIENCE_RETURN',
  CREATIVE_CREATE = 'CREATIVE_CREATE',
  CREATIVE_RETURN = 'CREATIVE_RETURN',
}

// Exporting the description map for PointLogTypeEnum
export const PointLogTypeEnumDesc: { [key in PointLogTypeEnum]: string } = {
  [PointLogTypeEnum.REGISTER_GIVE]: '注册赠送',
  [PointLogTypeEnum.RECHARGE]: '充值',
  [PointLogTypeEnum.RECHARGE_MANUAL]: '人工充值',
  [PointLogTypeEnum.LORA_TRAIN]: 'Lora训练',
  [PointLogTypeEnum.EXPERIENCE_CREATE]: '体验创作',
  [PointLogTypeEnum.EXPERIENCE_RETURN]: '体验退回',
  [PointLogTypeEnum.CREATIVE_CREATE]: '图片创作',
  [PointLogTypeEnum.CREATIVE_RETURN]: '创作退回',
};

// Exporting the function to get enum by code
export const getByCode = (code: string): PointLogTypeEnum | null => {
  if (!code) {
    return null;
  }

  const enumKey = Object.keys(PointLogTypeEnum).find(
    key => PointLogTypeEnum[key as keyof typeof PointLogTypeEnum] === code
  );

  return enumKey ? PointLogTypeEnum[enumKey as keyof typeof PointLogTypeEnum] : null;
};

export interface UserPointUsageInfoVO {
  operatorNickName: string;
  pointLogId: number;
  userId: number;
  userNickName: string;
  masterUserNickName: string;
  masterLoginId: string;
  operatorUserId: number;
  operatorLoginId: string;
  operatorRole: ROLE_TYPE;
  masterUser: boolean;
  type: PointLogTypeEnum;

  // 使用场景
  usageScene: string;
  // 使用方式
  usageWay: string;

  usedPoint: number;
  usedExperiencePoint: number;
  usedGivePoint: number;
  usedModelPoint: number;

  createTime: string;
}

export async function queryPointUsageInfoByPage(query){
  return await requestPost<API.PageInfo<UserPointUsageInfoVO>>('/userPointLog/queryPointUsageInfoByPage', query);
}

export async function exportPointUsage(query){
  const res = await fetchPostJsonDownload('/userPointLog/exportPointUsage', query)
  return await res.blob();
}