import { requestGet, requestPost } from './request';

export interface PromptDictVO {
  id: number;
  word: string;
  prompt: string;
  memo: string;
  type: string;
  tags: Array<string>;
  createTime: string;
  modifyTime: string;
}

export async function queryAllPromptDict() {
  return await requestGet<Array<PromptDictVO>>('/promptDict/queryAll');
}

export async function createPromptDict(payload: PromptDictVO) {
  return await requestPost<number>('/promptDict/create', payload);
}

export async function updatePromptDict(payload: PromptDictVO) {
  return await requestPost<boolean>('/promptDict/updateById', payload);
}

export async function deletePromptDict(id) {
  return await requestPost<boolean>('/promptDict/deleteById', { id });
}

export async function querySystemCollocation() {
  return await requestGet<Array<PromptDictVO>>('/promptDict/querySystemCollocation');
}
export async function querySystemScene() {
  return await requestGet<Array<PromptDictVO>>('/promptDict/querySystemScene');
}

export async function queryGarmentList() {
  return await requestGet<Array<PromptDictVO>>('/promptDict/queryGarmentList');
}

export async function queryImageBadTags(tagName) {
  return await requestGet<Array<PromptDictVO>>(`/promptDict/queryImageByTags/${tagName}`,);
}

export async function queryDictByKeyAndTags(key: string, tagNamesList: string[][]) {
  return await requestPost<Record<string,Array<PromptDictVO>>>('/promptDict/queryByKeyAndTags',{key, tagNamesList});
}

export async function queryListByTypeAndTags(type: string, tagNames: string[]) {
  return await requestPost<Array<PromptDictVO>>('/promptDict/queryListByTypeAndTags',{type, tagNames});
}