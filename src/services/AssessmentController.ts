import { requestGet, requestPost } from '@/services/request';
import { DistributorBasicVO, DistributorSettleConfigVO, SettleConfigModel } from '@/services/SettlementController';

/** 考核计划 */
export interface AssessmentPlanVO {
  /** id */
  id: number;
  /** 主体类型 */
  principalType: PrincipalType;
  /** 主体id */
  principalId: number;
  /** 考核类型 */
  type: SalesAssessType;
  /** 考核任务状态(0: '初始化', 1: '未开始', 2: '考核中', 3: '待审核', 4: '已通过', 5: '未通过', 6: '已结束', 7: '已取消') */
  status: AssessStatus;
  /** 考核指标 */
  kpiTarget?: KPIModel;
  /** 实际完成情况 */
  kpiActual?: KPIModel;
  /** 考核计划开始日期 */
  planFromDate?: string; // format: yyyy-MM-dd
  /** 考核计划结束日期 */
  planEndDate?: string; // format: yyyy-MM-dd
  /** 扩展字段 */
  extInfo: Record<string, any>;
  /** 创建人用户id */
  creatorUserId?: number;
  /** 修改人用户id */
  modifyUserId?: number;
  /** 创建时间 */
  createTime?: string; // format: yyyy-MM-dd HH:mm:ss
  /** 修改时间 */
  modifyTime?: string; // format: yyyy-MM-dd HH:mm:ss
  /** 备注 **/
  memo?: string;
}


/**
 * 渠道商考核计划
 */
export interface DistributorEnhancedVO extends DistributorSettleConfigVO {
  /** 当前考核计划 */
  currentPlan: AssessmentPlanVO;
}

/** 主体 */
export interface PrincipalModel {
  type: PrincipalType;
  id: number;
  name: string;
}

/** 主体类型 */
export type PrincipalType = 'CORP' | 'USER' | 'SUB_CORP';

/** 主体类型 */
export const principalTypeMap: Record<PrincipalType, string> = {
  CORP: '组织',
  USER: '用户',
  SUB_CORP: '子组织'
};

/** 销售考核类型 */
export type SalesAssessType = 'AGENT_EXCLUSIVE' | 'AGENT_NORMAL' | 'AGENT_RECTIFY' | 'SALES_LEADER' | 'SALES_SUB' | 'SALES_DEPT';

export const salesAssessTypeMap: Record<SalesAssessType, string> = {
  AGENT_EXCLUSIVE: '独家代理',
  AGENT_NORMAL: '普通外部渠道',
  AGENT_RECTIFY: '外部渠道整改期',
  SALES_LEADER: '直营销售Leader',
  SALES_SUB: '直营销售',
  SALES_DEPT: '直营销售下属渠道'
};

/** 考核状态 */
export type AssessStatus = 'ASSESS_INIT' | 'ASSESS_WAITING' | 'ASSESS_ACTIVE' | 'ASSESS_REVIEWED' | 'ASSESS_PASSED' | 'ASSESS_FAILED' | 'ASSESS_FINISHED' | 'ASSESS_CANCELLED';

export const assessStatusMap: Record<AssessStatus, {code: string, desc: string, color: string}> = {
  ASSESS_INIT: {code: 'ASSESS_INIT', desc: '未启用', color: '#8c8c8c'},        // 中性灰 - 冷色调
  ASSESS_WAITING: {code: 'ASSESS_WAITING', desc: '待开始', color: '#1890ff'},   // 蓝色 - 冷色调
  ASSESS_ACTIVE: {code: 'ASSESS_ACTIVE', desc: '考核中', color: '#fa8c16'},     // 橙色 - 暖色调  
  ASSESS_PASSED: {code: 'ASSESS_PASSED', desc: '已通过', color: '#52c41a'},     // 绿色 - 成功色
  ASSESS_FAILED: {code: 'ASSESS_FAILED', desc: '未通过', color: '#ff4d4f'},     // 红色 - 失败色
  ASSESS_REVIEWED: {code: 'ASSESS_REVIEWED', desc: '已审核', color: '#faad14'}, // 金黄色 - 暖色调
  ASSESS_FINISHED: {code: 'ASSESS_FINISHED', desc: '已结束', color: '#722ed1'}, // 紫色 - 冷色调
  ASSESS_CANCELLED: {code: 'ASSESS_CANCELLED', desc: '已取消', color: '#bfbfbf'}, // 浅灰 - 中性色
};

export const DEFAULT_ASSESSMENT_CONFIGS = 'DEFAULT_ASSESSMENT_CONFIGS';

/** KPI模型 */
export interface KPIModel {
  totalSalesAmount: number;
  totalSalesCount: number;
}

export async function queryAssessmentPlanByPage(params: any) {
  return await requestPost<Array<AssessmentPlanVO>>('/assessmentPlan/queryByPage', params);
}

export async function getAllDistributorAssessment() {
  return await requestGet<Array<DistributorEnhancedVO>>('/assessmentPlan/getAllDistributorAssessment');
}

export async function modifyAssessmentPlan(params: any) {
  return await requestPost<boolean>('/assessmentPlan/modifyAssessmentPlan', params);
}

/**
 * 手动确认考核结果
 * @param planId
 * @param newSettleConfig
 */
export async function reviewAssessmentPlan(planId: number, newSettleConfig?: SettleConfigModel) {
  return await requestPost<boolean>('/assessmentPlan/reviewAssessmentPlan', { planId, newSettleConfig });
}

/**
 * 手动刷新考核计划状态
 */
export async function manualRefresh() {
  return await requestGet<boolean>('/assessmentPlan/manualRefresh');
}