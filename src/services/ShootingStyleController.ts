import { requestGet, requestPost } from './request';

export interface ShootingStyleVO {
  /** id */
  id?: number;

  /** 一级分类名称 */
  type1Name?: string;

  /** 二级分类名称 */
  type2Name?: string;

  /** 英文名称 */
  type2EnName?: string;

  /** 代表性品牌 */
  representativeBrand?: string;

  /** 经典元素 */
  classicElements?: string;

  /** 创建账号id */
  createBy?: number;

  /** 修改账号id */
  modifyBy?: number;

  /** 创建时间 */
  createTime?: string;

  /** 修改时间 */
  modifyTime?: string;

  /** 示例图片地址列表，jsonArray格式 */
  examImageUrls?: string;

  /** 风格描述 */
  styleDesc?: string;

  /** 模特类型，适合的男模特/女模特类型，男模标签列表，女模标签列表 */
  modelTags?: string;

  /** 扩展 */
  extInfo?: string;
}

// 根据id查询拍摄风格配置
export async function getShootingStyleById(id: number) {
  return await requestGet<ShootingStyleVO>(`/shootingStyle/getById/${id}`);
}

// 新建拍摄风格配置
export async function createShootingStyle(payload: ShootingStyleVO) {
  return await requestPost<number>('/shootingStyle/create', payload);
}

// 删除拍摄风格配置
export async function deleteShootingStyleById(id: number) {
  return await requestPost<any>('/shootingStyle/deleteById', { id });
}

// 更新拍摄风格配置
export async function updateShootingStyleById(payload: ShootingStyleVO) {
  return await requestPost<any>('/shootingStyle/updateById', payload);
}

// 全量查询拍摄风格配置
export async function findAllShootingStyles() {
  return await requestPost<Array<ShootingStyleVO>>('/shootingStyle/findAll', {});
}