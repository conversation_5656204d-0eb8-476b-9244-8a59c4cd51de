import { requestGet, requestPost } from '@/services/request';
import { BizType, CreativeType, ElementKey } from '@/services/CreativeController';
import { CheckboxOptionType } from 'antd';
import { CSSProperties } from 'react';
import { CONDIFS_KEYS } from '@/constants';

export type ElementConfigKey = 'FACE' | 'SCENE' | 'CLOTH_STYLE' | 'REFER' | 'LOGO_POSITION';
export type ClothType = 'Male' | 'Female' | 'Child' | 'Common' | 'Unisex';
export type AgeRange = 'ALL' | 'adult' | 'child';
export const elementConfigKeys: ElementConfigKey[] = ['FACE', 'SCENE', 'CLOTH_STYLE', 'REFER', 'LOGO_POSITION'];

export const AllClothTypes: Array<{ key: ClothType; value: ClothType; label: string, style: CSSProperties }> = [
  { key: 'Female', value: 'Female', label: '女款', style: { width: 40, padding: 0, textAlign: 'center' } },
  { key: 'Male', value: 'Male', label: '男款', style: { width: 40, padding: 0, textAlign: 'center' } },
  { key: 'Unisex', value: 'Unisex', label: '男女同款', style: { width: 70, padding: 0, textAlign: 'center' } },
  // { key: 'Child', value: 'Child', label: '童款', style: { width: 40, padding: 0, textAlign: 'center' } },
  // { key: 'Common', value: 'Common', label: '通用', style: { width: 40, padding: 0, textAlign: 'center' } },
];

export const AgeRanges: Array<{ key: AgeRange; value: AgeRange; label: string, style: CSSProperties }> = [
  { key: 'ALL', value: 'ALL', label: '全部', style: { width: 40, padding: 0, textAlign: 'center' } },
  { key: 'adult', value: 'adult', label: '成人', style: { width: 40, padding: 0, textAlign: 'center' } },
  { key: 'child', value: 'child', label: '儿童', style: { width: 40, padding: 0, textAlign: 'center' } },
];


export const AllClothTypeCheckBoxOptions: Array<CheckboxOptionType> = [
  { label: '全部', value: 'All', style: { width: 40, padding: 0, textAlign: 'center' } },
  // { label: '未设置', value: 'Unset', style: { width: 50, padding: 0, textAlign: 'center' } },
  ...AllClothTypes as Array<CheckboxOptionType>,
];

// 年龄范围【creation中使用】
export const AllAgeRanges: Array<{}> = [
  { key: 'adult', value: 'adult', label: '成人', disabled: false },
  { key: 'big-child', value: 'big-child', label: '大童', disabled: false },
  { key: 'medium-child', value: 'medium-child', label: '中童', disabled: false },
  { key: 'small-child', value: 'small-child', label: '小童', disabled: false },
];



// 服装款式【creation中使用】
export const CreationClothTypeCheckBoxOptions: Array<CheckboxOptionType> = [
  { label: '女款', value: 'Female' },
  { label: '男款', value: 'Male' },
  { label: '同款', value: 'Unisex' },
];

/**
 * 服装款式类型定义
 */
export interface ClothCategory {
  subcategory: string;
  subcategoryName: string;
  mainCategory: string;
  mainCategoryName: string;
}

/**
 * 服装款式主类别类型
 */
export interface ClothCategoryGroup {
  mainCategory: string;
  mainCategoryName: string;
  children: ClothCategoryChild[];
}

/**
 * 服装款式子类别类型
 */
export interface ClothCategoryChild {
  subcategory: string;
  subcategoryName: string;
}
export interface ElementConfig {
  id: number;
  name: string;
  configKey: ElementConfigKey;
  children: Array<ElementConfig>;
  showImage: string;
  type: string[]; //对应scene type code
  extInfo?: any;
  belong: 'SYSTEM' | 'CUSTOM';
  parentId: number;
  fixed?: boolean;
  operatorNick: string;
  deliveryName?: string;
  createTime: string;
  loraTraining: boolean;
  loraModelId?: number;
  labelFinished?: boolean;
  loraConfirmed?: boolean;
  loraTaskStatus?: string;
  loraTaskStatusDesc?: string;
  contentOrStyle?: string;
  maskLoss: boolean;

  status: string;
  version: string;
  isNew?: boolean;
  styleScene: boolean;

  //关联的运营账号id
  relatedOperatorUserId?: number;
  //关联的运营账号nick
  relatedOperatorUserNick?: string;

  //客户昵称
  userId?: number;
  userNick?: string;
  userRoleType?: string;

  //客户专属的风格场景轮播图，取样本前10张
  showImgs4PrivateStyleScene?: string[];

  privatelyOpen2UserNick?: string;
  privatelyOpen2UserId?: number;
  privatelyOpen2UserRoleType?: string;
  watermarked?: boolean;
}

export interface ElementConfigWithBlobs extends ElementConfig {
  extTags?: string;
  tags?: string;
  uploadItems: [];
}

export interface SceneType {
  code: string;
  name: string;
  empty?: boolean;
}

export interface DefaultTestCreationConfig {
  maleFaces: ElementConfigWithBlobs[];
  femaleFaces: ElementConfigWithBlobs[];
  sceneList: ElementConfigWithBlobs[];
}

export interface ClothTypeScopeItem {
  key: string;
  values: string[];
}

export interface ShowImageVO {
  // 选中的展示图
  selectedShowImage: string;
  // 展示图列表
  showImages: string[];
}

export type ProportionType =
  'ONE_ONE'
  | 'THREE_FOUR'
  | 'ONE_ONE_LG'
  | 'THREE_FOUR_LG'
  | 'THREE_FOUR_LG_N'
  | 'NINE_SIXTEEN_2K'
  | 'P_1152_1536'
  | 'P_1620_2100'
  | 'P_1200_600';

export async function queryElementById(id: number) {
  return await requestGet<ElementConfigWithBlobs>(`/element/getById/${id}`);
}

export async function batchQueryElementById(idList: number[]) {
  return await requestPost<Map<number, ElementConfigWithBlobs>>(`/element/batchQueryById`, { idList: idList });
}

export async function queryHasShowImageChildren(params: number[] | {
  idList: number[];
  configKey?: string;
  clothType?: string | null;
  ageRange?: string | null;
  positions?: string[] | null;
  bodyTypes?: string[] | null;
  clothCategory?: string[] | null;
  genderTypes?: string[] | null;
  bizType?: string;
  principalId?: number | null;
}) {
  // 如果传入的是数组，则使用旧的格式
  const requestParams = Array.isArray(params) ? { idList: params } : params;
  return await requestPost<Map<number, ElementConfigWithBlobs>>(`/element/queryHasShowImageChildren`, requestParams);
}

export async function getElementConfig(type: CreativeType, bizType: BizType = 'ALL', onlyExclusive = false, needAll = false) {
  return await requestPost<Array<ElementConfig>>('/element/getConfig', { type, bizType, onlyExclusive, needAll });
}

export async function getElementConfigByKey(key: ElementKey) {
  return await requestGet<Array<ElementConfigWithBlobs>>(`/element/getByKey/${key}`);
}

// 获取基础换装配置
export async function getBasicChangeConfig(bizType: BizType = 'ALL', onlyExclusive = false) {
  return await requestPost<Array<ElementConfig>>('/element/getBasicChangeConfig', {
    bizType,
    onlyExclusive,
    configKeys: [CONDIFS_KEYS.SCENE, CONDIFS_KEYS.FACE],
  });
}

export async function getDefaultTestCreationConfig() {
  return await requestGet<DefaultTestCreationConfig>(`/element/getDefaultTestCreationConfig`);
}

export async function addElement(data) {
  return await requestPost<boolean>('/element/create', data);
}

export async function updateElement(data) {
  return await requestPost<boolean>('/element/updateById', data);
}

export async function deleteElement(id) {
  return await requestPost<boolean>('/element/deleteById', { id });
}

export async function getElementTypes(key: ElementKey) {
  return await requestGet<Array<SceneType>>(`/element/getTypes/${key}`);
}

export async function getClothCategoryCfg() {
  return await requestGet<Array<ClothCategoryGroup>>(`/element/getClothCategoryCfg`);
}

export async function getClothTypeScopeCfg4Scene() {
  return await requestGet<Array<ClothTypeScopeItem>>(`/element/getClothTypeScopeCfg4Scene`);
}

export async function getClothColors() {
  return await requestGet<string[]>(`/element/getClothColorCfg`);
}

// 获取商户最近使用场景
export async function getMerchantRecentElement(payload) {
  const { key, type, bizType } = payload;
  if (key === undefined) {
    payload.key = 'SCENE';
  }
  if (type === undefined) {
    payload.type = 'CREATE_IMAGE';
  }
  if (bizType === undefined) {
    payload.bizType = 'ALL';
  }
  return await requestPost<Array<ElementConfig>>('/element/getMerchantRecentSceneElements', payload);
}

export async function editElementType(key, data) {
  return await requestPost<boolean>('/element/editElementType', { key, data });
}

export async function resetOrder(data) {
  return await requestPost<boolean>('/element/order/reset', data);
}

export async function queryCustomElement(data) {
  return await requestPost<API.PageInfo<ElementConfig>>('/element/queryCustomByPage', data);
}

export async function addCustomElement(data) {
  return await requestPost<ElementConfig>('/element/addCustom', data);
}

export async function fetchTaskFace(taskId) {
  return await requestPost<ElementConfig>('/element/fetchTaskFace', { taskId });
}

export async function cloneElement(id, fullCopy = false) {
  return await requestPost<boolean>('/element/clone', { id, fullCopy });
}

export async function batchCorrectType(ids: string) {
  return await requestPost<boolean>('/element/batchCorrectType', { ids });
}

export async function batchInitTrainType(ids: string) {
  return await requestPost<boolean>('/element/batchInitTrainType', { ids });
}

export async function batchCorrectStyle(ids: string) {
  return await requestPost<boolean>('/element/batchCorrectStyle', { ids });
}

export async function queryRecommendAutoGenElements(payload) {
  return await requestPost<Array<ElementConfig>>('/element/queryRecommendAutoGenElementsByCloth', payload);
}

export async function queryFavorElements(payload) {
  return await requestPost<API.PageInfo<ElementConfig>>('/element/queryFavorElements', payload);
}

// 智能推荐
export async function intelligentRecommendation(clothType: string, clothImgUrls: string[]) {
  return await requestPost<Array<ElementConfig>>('/element/intelligentRecommendation', {
    clothType,
    clothImgUrls,
  });
}

export async function queryElementByPage(payload) {
  return await requestPost<API.PageInfo<ElementConfigWithBlobs>>('/element/queryByPage', payload);
}

export async function queryPubViewByPage(payload) {
  return await requestPost<API.PageInfo<ElementConfigWithBlobs>>('/element/queryPubViewByPage', payload);
}

export async function updateElementExperimental(id: number, experimental: boolean) {
  return await requestPost<boolean>('/element/updateExperimental', { id, experimental });
}

export async function queryNeedProcessElementCnt() {
  return await requestGet<Map<string, number>>('/element/queryNeedProcessCnt');
}

// 批量设置展示图片
export async function batchSetScenePose(sceneIds: string) {
  return await requestPost<string>('/element/batchSetScenePose', { sceneIds });
}

// 查询姿势对应的姿势展示图
export async function selectShowImageByScene(sceneId: number) {
  return await requestPost<Map<number, ShowImageVO>>('/element/selectShowImageByScene', { sceneId });
}
