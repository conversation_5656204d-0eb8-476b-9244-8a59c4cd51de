import { requestPost } from '@/services/request';

export interface SalesSuccessStoriesVO {
  id: number;
  name: string;
  topped: boolean;
  customerId: number;
  customerName: string;
  batchId: number;
  modelId: number;
  modelName: string;
  modelUrl: string;
  memo: string;
  userId: number;
  operatorId: number;
  operatorNick: string;
  createTime: string;
  modifyTime: string;
  imageUrls: string[];
  extInfo: Map<string, string>;
}

export async function createSuccessStories(payload: SalesSuccessStoriesVO) {
  return await requestPost<number>('/salesSuccessStories/create', payload);
}

export async function updateSuccessStories(payload: SalesSuccessStoriesVO) {
  return await requestPost<number>('/salesSuccessStories/updateById', payload);
}

export async function deleteSuccessStories(id) {
  return await requestPost<number>('/salesSuccessStories/deleteById', {id});
}

export async function querySuccessStoriesByPage(payload) {
  return await requestPost<API.PageInfo<SalesSuccessStoriesVO>>('/salesSuccessStories/queryByPage', payload);
}
