import { request } from '@umijs/max';
import { SERVICE_URL } from '@/constants';
import { notification } from 'antd';
import { history } from '@umijs/max';

export function fail(result: API.Result, url: string, notifyError: boolean = true) {
  const { code, message } = result;
  console.log('服务端api失败:', code, message, url);
  // 登录态失败或权限不正确，直接跳转到登录页面
  if (code === 'LOGIN_EXPIRED') {
    console.log('LOGIN_EXPIRED goto login');
    history.push('/login');
    window.location.reload();
    return;
  }

  if (notifyError) {
    return notification.error({
      message: '请求异常',
      description: message,
    });
  }
}

function processResult<T>(result: API.Result, url: string, notifyError: boolean = true) {
  if (!result.success) {
    return fail(result, url, notifyError);
  }

  //这里不能改成===，因为返回的data还有undefined的情况
  if (result.data == null) {
    return { success: true } as T;
  }

  return result.data as T;
}


export function getFullUrl(url: string | undefined) {
  if (!url) {
    return SERVICE_URL;
  }

  return SERVICE_URL + (url?.startsWith('/') ? url?.substr(1, url?.length) : url);
}


export async function requestGet<T>(url: string, params = {}) {
  return await request<API.Result>(getFullUrl(url), {
    method: 'GET',
    params: { ...params },
    credentials: 'include',
  }).then((r) => {
    return processResult<T>(r, url);
  });
}


export async function requestPost<T>(url: string, data = {}, headers = {}, notifyError: boolean = true) {
  return await request<API.Result>(getFullUrl(url),
    {
      method: 'POST',
      data: { ...data },
      headers: { 'Content-Type': 'application/json', ...headers },
      credentials: 'include',
    },
  ).then((r) => {
    // 对短信登录请求做特殊处理
    if (url === '/login/sms' && !r.success && r.message?.includes('未注册')) {
      return { error: { code: r.code, message: r.message } } as T;
    }

    // 其他请求做正常处理
    return processResult<T>(r, url, notifyError);
  });
}

// 不关心请求成功与否，不抛出异常
export async function requestPostNoCareResult<T>(url: string, data = {}, headers = {}, notifyError: boolean = true) {
  return await request<API.Result>(getFullUrl(url),
    {
      method: 'POST',
      data: { ...data },
      headers: { 'Content-Type': 'application/json', ...headers },
      credentials: 'include',
    },
  ).then((r) => {
    // 如果请求失败，则弹出错误提示
    const { message, success } = r;
    if (!success) {
      notification.error({
        message: '请求异常',
        description: message,
      });
    }
    return r;
  });
}

export async function fetchGet(url: string, data = {}, headers = {}) {
  return await fetch(getFullUrl(url), data);
}

export async function fetchPost<T>(url: string, data = {}, headers = {}) {
  // @ts-ignore
  return await fetch(getFullUrl(url), { method: 'POST', body: data }).then((r) => {
    if (!r.ok) {
      throw new Error(`HTTP error! status: ${r.status}`);
    }
    return r.json();
  }).then(data => {
    // 这里的 data 就是解析后的 JSON 数据
    return processResult<T>(data, url);
  })
    .catch(error => {
      console.error('There has been a problem with your fetch operation:', error);
    });
}

export async function fetchPostDownload(url: string, data = {}, headers = {}) {
  // @ts-ignore
  return await fetch(getFullUrl(url), { method: 'POST', body: data, headers });
}

export async function fetchPostJsonDownload(url: string, data = {}, headers = {}) {
  // @ts-ignore
  return await fetch(getFullUrl(url), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
      credentials: 'include',
    }
  );
}