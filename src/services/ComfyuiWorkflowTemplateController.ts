import { requestGet, requestPost } from './request';
import { ROLE_TYPE } from '@/services/UserController';

export interface WorkflowOpenScope {
    userIds: Array<number>;

    roleTypes: Array<ROLE_TYPE>;

    logic: 'AND' | 'OR' | '';
}

export interface ComfyuiWorkflowTemplateVO {
    id: number;
    templateKey: string;
    templateDesc: string;
    version: string;
    createTime?: string;
    modifyTime?: string;
    createBy?: number;
    modifyBy?: number;
    templateData: string;
    isTest?: boolean;
    openScope?: WorkflowOpenScope;
}

export interface ComfyuiWorkflowTemplateActiveVersionVO {
    /** 主键id */
    id?: number;

    /** 模板key */
    templateKey: string;

    /** 模板描述 */
    templateDesc: string;

    /** 模板版本，如20250610.1 */
    activeVersion: string;

    /** 创建时间 */
    createTime?: string;

    /** 更新时间 */
    modifyTime?: string;

    /** 创建人id */
    createBy?: number;

    /** 修改人id */
    modifyBy?: number;
}

// 根据id查询comfyui模板
export async function getComfyuiWorkflowTemplateById(id: number) {
    return await requestGet<ComfyuiWorkflowTemplateVO>(`/comfyuiWorkflowTemplate/getById/${id}`);
}

// 新建comfyui模板
export async function createComfyuiWorkflowTemplate(template: ComfyuiWorkflowTemplateVO) {
    return await requestPost<number>('/comfyuiWorkflowTemplate/createActiveVersion', template);
}

// 删除comfyui模板
export async function deleteComfyuiWorkflowTemplateById(id: number) {
    return await requestPost<boolean>('/comfyuiWorkflowTemplate/deleteById', id);
}

// 更新comfyui模板
export async function updateComfyuiWorkflowTemplateById(template: ComfyuiWorkflowTemplateVO) {
    return await requestPost<boolean>('/comfyuiWorkflowTemplate/updateById', template);
}

// 全量查询comfyui模板
// 添加查询参数接口
export interface ComfyuiWorkflowTemplateQuery {
    id?: number;
    templateKey?: string;
    templateDesc?: string;
    version?: string;
    onlyShowTestVersion?: boolean;
    createTime?: Date;
    modifyTime?: Date;
    createBy?: number;
    modifyBy?: number;
    templateData?: string;
    pageSize: number;
    pageNum: number;
    orderBy?: string;
    needDeleted?: boolean;
}

export interface ComfyuiWorkflowTemplateOption {
  templateKey: string;
  templateDesc: string;
}


// 获取所有模板key列表（用于下拉选择）
export async function getAllTemplateKeys() {
    return await requestPost<ComfyuiWorkflowTemplateOption[]>('/comfyuiWorkflowTemplateActiveVersion/getAllTemplateKeys');
}

// 分页查询接口
export async function queryComfyuiWorkflowTemplatesByPage(query: ComfyuiWorkflowTemplateQuery) {
    return await requestPost<API.PageInfo<ComfyuiWorkflowTemplateVO>>('/comfyuiWorkflowTemplate/queryByPage', query);
}

// 查询列表接口
export async function queryComfyuiWorkflowTemplateList(query: Partial<ComfyuiWorkflowTemplateQuery>) {
    return await requestPost<ComfyuiWorkflowTemplateVO[]>('/comfyuiWorkflowTemplate/queryList', query);
}

export const activeTemplate = async (id: number) => {
    return await requestPost<boolean>('/comfyuiWorkflowTemplate/activeTemplate', { id });
}

export const deleteTemplate = async (id: number) => {
    return await requestPost<boolean>('/comfyuiWorkflowTemplate/deleteById', { id });
}