// 运行时配置
import React, { useEffect, useRef, useState } from 'react';
import { HashRouter as Router, Link, Route, Routes, useLocation } from 'react-router-dom';
import {
  EXPERIENCE_POINT,
  FREE_ICON,
  IMAGE_POINT_CHANGE_EVENT,
  LOGO_BRAND,
  NAVI_TYPE,
  NAVI_TYPE_CHANGE_EVENT,
  USER_INFO,
} from '@/constants';
import ProLayout from '@ant-design/pro-layout';
import IconFont from '@/components/IconFont';
import './layout/menuStyle.less';
import './app.less';
import UploadMaterialTotalPage from '@/pages/UploadMaterial/uploadMaterial';
import Creation from '@/pages/Creation';
import FixedPostureCreation from '@/pages/FixedPostureCreation';
import AllModelsPage from '@/pages/AllModels';
import History from '@/pages/History';
import FindPwd from '@/pages/FindPwd';
import MembersPage from '@/pages/Members';
import Lora from '@/pages/Operate/Lora';
import Face from '@/pages/Operate/Face';
import Scene from '@/pages/Operate/Scene';
import ReferPose from '@/pages/Operate/ReferPose';
import ClothStyle from '@/pages/Operate/ClothStyle';
import TagsPage from '@/pages/Operate/Tags/TagManagement';
import { UserMng } from '@/pages/Operate/UserMng';
import SettleMng from '@/pages/Operate/Settle';
import { queryImagePoint } from '@/services/PointController';
import Home from '@/pages/Home';
import Experience from '@/pages/Experience';
import OrderInfoMng from '@/pages/Operate/OrderInfo';
import ConsumeRecord from '@/pages/Operate/ConsumeRecord';
import UserCenterPage from '@/pages/UserCenter';
import CreativeRecord from '@/pages/Operate/CreativeRecord';
import DistributorTeam from '@/pages/DistributorMng/DistributorTeam';
import DistributorFaceAndSceneView from '@/pages/DistributorMng/DistributorFaceAndSceneView';
import DistributorCustomers from '@/pages/DistributorMng/DistributorCustomers';
import DistributorCustomerCreativeRecords from '@/pages/DistributorMng/DistributorCustomerCreativeRecords';
import DistributorCustomerAccountRecords from '@/pages/DistributorMng/DistributorCustomerAccountRecords';
import System from '@/pages/Operate/System';
import Server from '@/pages/Operate/Server';
import LogoCombine from '@/pages/LogoCombine';
import FaceSceneSwitch from '@/pages/FaceSceneSwitch';
import MerchantPreferenceMng from '@/pages/Operate/MerchantPreference';
import TrainPlanPage from '@/pages/Operate/Experiment/trainPlan';
import TryonPage from '@/pages/Operate/Experiment/tryon';
import InvoiceMngPage from '@/pages/Operate/OrderInfo/invoiceMng';
import {
  DatabaseFilled,
  DatabaseOutlined,
  ExperimentFilled,
  ExperimentOutlined, NodeIndexOutlined,
  PieChartFilled,
  PieChartOutlined,
  SnippetsFilled,
  SnippetsOutlined,
} from '@ant-design/icons';
import { getNavUrlByUser, isProdEnv, isValidJsonObject } from '@/utils/utils';
import CreateVideo from '@/pages/CreateVideo';
import CaptureNotice from '@/pages/UploadMaterial/CaptureNotice';
import DeliveryStats from '@/pages/Operate/Lora/deliveryStats';
import { Badge } from 'antd';
import { queryNeedProcessCreativeCnt } from '@/services/CreativeController';
import { queryMenusCfg } from '@/services/SystemController';
import TutorialPage from '@/pages/Home/Tutorial';
import NavigationBar, { NavigationBarRef } from '@/components/Home/NavigationBar';
import WindowVideo from '@/pages/Home/WindowVideo';
import VideoHistory from '@/pages/History/VideoHistory';
import ABTest from '@/pages/Operate/ABTest';
import ToolkitMenu from '@/pages/toolkit/ToolkitMenu';
import DesignHistory from '@/pages/History/ToolsHistory';
import BasicReplace from '@/pages/BasicReplace/BasicReplace';
import UserFavor from '@/pages/UserFavor';
import ClothReview from '@/pages/Operate/ClothReview';
import { useUpdateChecker } from '@/hooks/useUpdateChecker';
import WorkSchedule from './pages/Operate/WorkSchedule';
import WorkflowMng from './pages/Operate/System/workflow';
import DataStatistics from '@/pages/Operate/Stats/DataStatistics';
import SalesSuccessStoriesMng from '@/pages/DistributorMng/SalesSuccessStoriesMng';

import { queryNeedProcessElementCnt } from '@/services/ElementController';
import ClothingSwap from '@/pages/ClothingSwap';
// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
export async function getInitialState(): Promise<{ name: string }> {
  return { name: 'muse-gate' };
}

export type NaviType =
  'HOME'
  | 'IMAGE'
  | 'VIDEO'
  | 'TUTORIAL'
  | 'DISTRIBUTOR'
  | 'MANAGE'
  | 'BUSINESS'
  | 'DEVELOP'
  | 'DESIGN'
  | 'DIS-MANAGE';

const noNeedLayout = {
  '/login': <Home />,
  '/register': <Home />,
  '/find-pwd': <FindPwd />,
  '/': <Home />,
  '/home': <Home />,
  '/capture-notice': <CaptureNotice />,
  '/tutorial': <TutorialPage />,
};

// @ts-ignore
const BasicLayout: React.FC = ({ children }) => {
  // 使用更新检测 hook
  useUpdateChecker({ checkInterval: 180000 });

  const userInfo = localStorage.getItem(USER_INFO);
  const userRole = userInfo ? JSON.parse(userInfo).roleType : 'NONE';
  const userMemo = userInfo ? JSON.parse(userInfo).memo : '';

  const [menu, setMenu] = useState('/');
  const [showFree, setShowFree] = useState(false);
  const location = useLocation();

  const [needProcessCreative, setNeedProcessCreative] = useState(0);
  const [needProcessFace, setNeedProcessFace] = useState(0);
  const [needProcessScene, setNeedProcessScene] = useState(0);

  const [menuCfg, setMenuCfg] = useState<string>();
  const [hideMenus, setHideMenus] = useState<Set<string>>(new Set());

  // 白名单控制当前用户的菜单显示，在原始的菜单上进行过滤，并隐藏菜单
  const [showMenusByWhitelist, setShowMenusByWhitelist] = useState<Set<string>>(new Set());

  const navigationBarRef = useRef<NavigationBarRef>(null);

  // 检查用户是否有业务经营相关权限
  const checkBusinessPermission = (cfg: any, userInfo: any) => {
    if (!cfg || !userInfo) return false;

    const hasBusinessMngPermission = cfg['/business-mng'] && cfg['/business-mng'].includes(userInfo.id);
    const hasStatsOperatePermission = cfg['/stats-operate-indicators'] && cfg['/stats-operate-indicators'].includes(userInfo.id);

    return hasBusinessMngPermission && hasStatsOperatePermission;
  };

  useEffect(() => {
    // 如果是登录页，不需要布局
    if (noNeedLayout[location.pathname]) {
      return;
    }

    setMenu(location.pathname);
    if (userInfo && JSON.parse(userInfo).roleType === 'ADMIN') {
      queryMenusCfg().then(res => {
        if (typeof res === 'string') {
          setMenuCfg(res);
        }
      });
    }

  }, [location]); // 依赖项为 location，当 location 变化时重新运行 effect

  useEffect(() => {
    // 如果是登录页，不需要布局
    if (noNeedLayout[location.pathname]) {
      return;
    }

    if (userInfo && (JSON.parse(userInfo).roleType === 'MERCHANT' || JSON.parse(userInfo).roleType === 'OPERATOR')) {
      queryImagePoint().then((res) => {
        if (res) {
          setShowFree(res.imagePoint > 0);
          localStorage.setItem(EXPERIENCE_POINT, res.experiencePoint.toString());
        }
      });
    }

    if (userInfo && JSON.parse(userInfo).roleType === 'ADMIN') {
      queryNeedProcessCreativeCnt().then(res => {
        if (res !== undefined) {
          setNeedProcessCreative(res);
        }
      });

      queryNeedProcessElementCnt().then(res => {
        if (!res) return;
        setNeedProcessFace(res['FACE'] || 0);
        setNeedProcessScene(res['SCENE'] || 0);
      });

      queryMenusCfg().then(res => {
        if (typeof res === 'string') {
          setMenuCfg(res);
        }
      });
    }
  }, []);

  useEffect(() => {
    const handleImagePointChange = () => {
      setShowFree(Number(localStorage.getItem(EXPERIENCE_POINT)) > 0);
    };

    window.addEventListener(IMAGE_POINT_CHANGE_EVENT, handleImagePointChange);

    return () => {
      window.removeEventListener(IMAGE_POINT_CHANGE_EVENT, handleImagePointChange);
    };
  });

  useEffect(() => {
    if (menuCfg && isValidJsonObject(menuCfg) && userInfo && JSON.parse(userInfo).roleType === 'ADMIN' && isProdEnv()) {
      let cfg = JSON.parse(menuCfg);

      let hideSet = new Set<string>();
      let onlyShowSet = new Set<string>();

      Object.entries(cfg).forEach(([key, value]) => {
        //key为userId
        if (Number(key) === Number(JSON.parse(userInfo)?.id) && value !== null && Array.isArray(value)) {
          value.forEach(item => onlyShowSet.add(item));

          //key为菜单
        } else if (value !== null && Array.isArray(value) && !value.includes(Number(JSON.parse(userInfo)?.id))) {
          hideSet.add(key);
        }
      });
      setHideMenus(hideSet);
      setShowMenusByWhitelist(onlyShowSet);
    }
  }, [menuCfg, userInfo]);

  const isAdmin = userInfo && JSON.parse(userInfo).roleType === 'ADMIN';
  const isDistributor = userInfo && JSON.parse(userInfo).roleType === 'DISTRIBUTOR';
  const isMerchant = userInfo && JSON.parse(userInfo).roleType === 'MERCHANT';
  const isOperator = userInfo && JSON.parse(userInfo).roleType === 'OPERATOR';
  const isReviewer = userInfo && JSON.parse(userInfo).roleType === 'REVIEWER';

  const distributorAdminOrTl = userInfo && (JSON.parse(userInfo)?.customRole?.includes('ADMIN') || JSON.parse(userInfo)?.customRole?.includes('LEADER'));

  // 是否是虚拟商家
  const isVirtualMerchant = userInfo && JSON.parse(userInfo).memo && JSON.parse(userInfo).memo.includes('虚拟商家');


  // 如果是登录页，不需要布局
  if (noNeedLayout[location.pathname]) {
    return noNeedLayout[location.pathname];
  }

  const menuItemRender = (item) => {

    //纯logo
    if (item.onlyIcon) {
      return (
        <div>{item.icon}</div>
      );

      //正常菜单
    } else {
      return (
        <div className={'custom-menu-item' + (menu === item.key ? ' menu-item-icon-selected' : '')}>
          <Link to={item.key}>
            {item.hotIcon}
            {item.icon}
            {(!item.badgeCnt || item.badgeCnt === 0) &&
              <div>{item.name}</div>
            }
            {(item.badgeCnt !== undefined && item.badgeCnt > 0) &&
              <div>
                <Badge count={item.badgeCnt}
                       style={{ position: 'absolute', top: -28, right: 8 }}>
                  <div className={'text12 font-pf'}>{item.name}</div>
                </Badge>
              </div>
            }
          </Link>
        </div>
      );
    }

  };

  const AllMenuData = () => {
    // 获取解析后的配置用于权限校验
    const parsedMenuCfg = menuCfg && isValidJsonObject(menuCfg) ? JSON.parse(menuCfg) : null;
    const parsedUserInfo = userInfo ? JSON.parse(userInfo) : null;

    return [
      {
        path: '/',
        redirect: getNavUrlByUser(),
        naviType: 'HOME',
      },
      {
        name: '传素材',
        path: '/upload',
        icon: <IconFont
          type={menu === '/upload' || menu === '/' ? 'icon-a-chuansucai_xuanzhong1x1' : 'icon-a-chuansucai_weixuanzhong1x1'}
          style={{ fontSize: '42px', borderRadius: '8px' }} />,
        accRoles: ['MERCHANT', 'OPERATOR', 'DEMO_ACCOUNT', 'DISTRIBUTOR'],
        naviType: 'IMAGE',
      },
      {
        name: '全部资产',
        path: '/models',
        icon: <IconFont
          type={menu === '/models' ? 'icon-a-yiyourenwu_xuanzhong1x1' : 'icon-a-yiyourenwu_weixuanzhong1x1'}
          className={'menu-item-icon'} />,
        accRoles: ['MERCHANT', 'OPERATOR', 'DEMO_ACCOUNT', 'DISTRIBUTOR'],
        naviType: 'IMAGE',
      },
      {
        path: '/distributor/pub-view',
        name: '平台资产',
        icon: <IconFont
          type={menu === '/distributor/pub-view' ? 'icon-a-yiyourenwu_xuanzhong1x1' : 'icon-a-yiyourenwu_weixuanzhong1x1'}
          className={'menu-item-icon'} />,
        hideInMenu: !isDistributor,
        naviType: 'IMAGE',
      },
      {
        name: '去创作',
        path: '/creation',
        icon: <IconFont
          type={menu === '/creation' ? 'icon-a-quchuangzuo_xuanzhong1x1' : 'icon-a-quchuangzuo_weixuanzhong1x1'}
          className={'menu-item-icon'} />,
        accRoles: ['MERCHANT', 'OPERATOR', 'DEMO_ACCOUNT'],
        naviType: 'IMAGE',
      },
      {
        name: '固定姿势创作',
        path: '/fixed-posture-creation',
        icon: <IconFont
          type={menu === '/fixed-posture-creation' ? 'icon-balie--' : 'icon-balie-'}
          style={{ fontSize: 38 }} />,
        accRoles: ['DISTRIBUTOR','MERCHANT', 'OPERATOR', 'DEMO_ACCOUNT'],
        naviType: 'IMAGE',
      },
      {
        path: '/basic-replace',
        name: '基础款换衣',
        icon: <IconFont
          type={menu === '/basic-replace' ? 'icon-lishirenwu_xuanzhong' : 'icon-lishirenwu_weixuanzhong'}
          className={'menu-item-icon'} />,
        hideInMenu: isAdmin || isDistributor || isMerchant,
        accRoles: ['OPERATOR'],
        naviType: 'IMAGE',
      },
      {
        path: '/clothing-swap',
        name: '单图换衣',
        icon: <IconFont
          type={menu === '/clothing-swap' ? 'icon-lishirenwu_xuanzhong' : 'icon-lishirenwu_weixuanzhong'}
          className={'menu-item-icon'} />,
        hideInMenu: isAdmin || isDistributor || isMerchant,
        accRoles: ['OPERATOR'],
        naviType: 'IMAGE',
      },
      {
        path: '/toolkit-menu',
        name: 'AI修图',
        icon: <IconFont
          type={menu === '/toolkit-menu' ? 'icon-aixiutu_xuanzhong' : 'icon-aixiutu_weixuanzhong'}
          className={'menu-item-icon'} />,
        hideInMenu: isAdmin,
        naviType: 'IMAGE',
      },
      {
        path: '/face-scene-switch',
        name: '模特换头',
        icon: <IconFont
          type={menu === '/face-scene-switch' ? 'icon-tupianhuanlian_xuanzhong' : 'icon-tupianhuanlian_weixuanzhong'}
          className={'menu-item-icon'} />,
        accRoles: ['MERCHANT', 'OPERATOR', 'DISTRIBUTOR'],
        naviType: 'IMAGE',
      },
      {
        path: '/logo-combine',
        name: '印花上身',
        icon: <IconFont
          type={menu === '/logo-combine' ? 'icon-yinhuashangshen' : 'icon-yinhuashangshen-2'}
          className={'menu-item-icon'} />,
        hideInMenu: !(isOperator || isDistributor),
        naviType: 'DESIGN',
      },
      {
        path: '/design-history',
        name: '历史创作',
        icon: <IconFont
          type={menu === '/toolkit-history' ? 'icon-lishirenwu_xuanzhong' : 'icon-lishirenwu_weixuanzhong'}
          className={'menu-item-icon'} />,
        hideInMenu: isAdmin,
        naviType: 'DESIGN',
      },
      {
        path: '/user-favor',
        name: '我的收藏',
        icon: <IconFont
          type={menu === '/user-favor' ? 'icon-lishirenwu_xuanzhong' : 'icon-lishirenwu_weixuanzhong'}
          style={{ fontSize: '42px', background: menu === '/toolkit-menu' ? '#F5F5F5' : '', borderRadius: '8px' }} />,
        accRoles: ['OPERATOR', 'MERCHANT', 'DEMO_ACCOUNT'],
        naviType: 'IMAGE',
      },
      {
        path: '/history',
        name: '历史创作',
        icon: <IconFont
          type={menu === '/history' ? 'icon-lishirenwu_xuanzhong' : 'icon-lishirenwu_weixuanzhong'}
          className={'menu-item-icon'} />,
        accRoles: ['MERCHANT', 'OPERATOR', 'DEMO_ACCOUNT'],
        naviType: 'IMAGE',
      },
      {
        onlyIcon: true,
        path: ' ',
        name: ' ',
        icon: <div className={'divider-line'} />,
        accRoles: ['MERCHANT', 'OPERATOR', 'DEMO_ACCOUNT'],
        naviType: 'IMAGE',
      },
      {
        path: '/create-video',
        name: '图生视频',
        icon: <IconFont
          type={menu === '/create-video' ? 'icon-lishirenwu_xuanzhong2' : 'icon-lishirenwu_weixuanzhong3'}
          className={'menu-item-icon menu-item-icon-sub'} />,
        hideInMenu: isAdmin,
        naviType: 'VIDEO',
      },
      {
        path: '/video-favor',
        name: '视频收藏',
        icon: <IconFont
          type={menu === '/video-favor' ? 'icon-lishirenwu_xuanzhong' : 'icon-lishirenwu_weixuanzhong'}
          className={'menu-item-icon'} />,
        accRoles: ['OPERATOR', 'DISTRIBUTOR', 'MERCHANT', 'DEMO_ACCOUNT'],
        naviType: 'VIDEO',
      },
      {
        path: '/video-history',
        name: '历史创作',
        icon: <IconFont
          type={menu === '/video-history' ? 'icon-lishirenwu_xuanzhong' : 'icon-lishirenwu_weixuanzhong'}
          className={'menu-item-icon'} />,
        hideInMenu: isAdmin,
        naviType: 'VIDEO',
      },
      {
        path: '/window-video',
        name: '橱窗AI视频',
        icon: <IconFont
          type={menu === '/window-video' ? 'icon-lishirenwu_xuanzhong2' : 'icon-lishirenwu_weixuanzhong3'}
          className={'menu-item-icon menu-item-icon-sub'} />,
        hideInMenu: isAdmin,
        naviType: 'VIDEO',
      },
      {
        path: '/cloth-mng',
        name: '服装管理',
        icon: <IconFont
          type={menu === '/cloth-mng' ? 'icon-a-chuansucai_xuanzhong1x1' : 'icon-a-chuansucai_weixuanzhong1x1'}
          className={'menu-item-icon'} />,
        hideInMenu: !isAdmin,
        naviType: 'MANAGE',
      },
      {
        path: '/cloth-review',
        name: '素材审核',
        icon: <IconFont
          type={menu === '/cloth-review' ? 'icon-a-chuansucai_xuanzhong1x1' : 'icon-a-chuansucai_weixuanzhong1x1'}
          style={{ fontSize: '42px', background: menu === '/cloth-review' ? '#F5F5F5' : '', borderRadius: '8px' }} />,
        hideInMenu: !isReviewer,
        naviType: 'MANAGE',
      },
      {
        path: '/mng/upload',
        name: '上传训练',
        icon: <IconFont
          type={menu === '/mng/upload' ? 'icon-a-chuansucai_xuanzhong1x1' : 'icon-a-chuansucai_weixuanzhong1x1'}
          style={{
            fontSize: '42px',
            background: menu === '/mng/upload' ? '#F5F5F5' : '',
            borderRadius: '8px',
          }} className={'menu-item-icon'} />,
        hideInMenu: !isAdmin,
        naviType: 'MANAGE',
      },
      {
        path: '/face',
        name: '模特配置',
        icon: <IconFont
          type={menu === '/face' ? 'icon-a-quchuangzuo_xuanzhong1x1' : 'icon-a-quchuangzuo_weixuanzhong1x1'}
          className={'menu-item-icon'} />,
        hideInMenu: !isAdmin,
        naviType: 'MANAGE',
        badgeCnt: needProcessFace,
      },
      {
        path: '/scene',
        name: '场景配置',
        icon: <IconFont
          type={menu === '/scene' ? 'icon-a-yiyourenwu_xuanzhong1x1' : 'icon-a-yiyourenwu_weixuanzhong1x1'}
          className={'menu-item-icon'} />,
        hideInMenu: !isAdmin,
        naviType: 'MANAGE',
        badgeCnt: needProcessScene,
      },
      {
        path: '/cloth-style',
        name: '服装款式',
        icon: <IconFont
          type={menu === '/cloth-style' ? 'icon-a-yiyourenwu_xuanzhong1x1' : 'icon-a-yiyourenwu_weixuanzhong1x1'}
          className={'menu-item-icon'} />,
        hideInMenu: !isAdmin,
        naviType: 'MANAGE',
      },
      {
        path: '/refer-pose',
        name: '参考图',
        icon: <IconFont
          type={menu === '/refer-pose' ? 'icon-a-yiyourenwu_xuanzhong1x1' : 'icon-a-yiyourenwu_weixuanzhong1x1'}
          className={'menu-item-icon'} />,
        hideInMenu: !isAdmin,
        naviType: 'MANAGE',
      },
      {
        path: '/user-mng',
        name: '客户管理',
        icon: <IconFont
          type={menu === '/user-mng' ? 'icon-a-tuanduiguanli_xuanzhong1x1' : 'icon-a-tuanduiguanli_weixuanzhong1x1'}
          className={'menu-item-icon'} />,
        hideInMenu: !isAdmin,
        naviType: 'MANAGE',
      },
      {
        path: '/creative-record',
        name: '创作记录',
        icon: <IconFont
          type={menu === '/creative-record' ? 'icon-dingdan_xuanzhong' : 'icon-dingdan_weixuanzhong'}
          className={'menu-item-icon'} />,
        hideInMenu: !isAdmin,
        naviType: 'MANAGE',
        badgeCnt: needProcessCreative,
      },
      {
        path: '/tags',
        name: '标签管理',
        icon: <IconFont
          type={menu === '/tags' ? 'icon-jinrijizhang_weixuanzhong' : 'icon-jinrijizhang_weixuanzhong'}
          className={'menu-item-icon menu-item-icon-sub'} />,
        hideInMenu: !isAdmin,
        naviType: 'MANAGE',
      },
      {
        path: '/merchant-preference',
        name: '商家偏好',
        icon: (menu === '/merchant-preference' ? <SnippetsFilled style={{ fontSize: 32 }} /> :
          <SnippetsOutlined style={{ fontSize: 32 }} />),
        hideInMenu: !isAdmin,
        naviType: 'MANAGE',
      },
      {
        path: '/distributor/team',
        name: '团队管理',
        icon: <IconFont
          type={menu === '/distributor/team' ? 'icon-a-tuanduiguanli_xuanzhong1x1' : 'icon-a-tuanduiguanli_weixuanzhong1x1'}
          className={'menu-item-icon'} />,
        hideInMenu: !(isDistributor && distributorAdminOrTl),
        naviType: 'DIS-MANAGE',
      },
      {
        path: '/distributor/customers',
        name: '客户管理',
        icon: <IconFont
          type={menu === '/distributor/customers' ? 'icon-a-tuanduiguanli_xuanzhong1x1' : 'icon-a-tuanduiguanli_weixuanzhong1x1'}
          className={'menu-item-icon'} />,
        hideInMenu: !isDistributor,
        naviType: 'DIS-MANAGE',
      },
      {
        path: '/creation',
        name: '代创作',
        icon: <IconFont
          type={menu === '/creation' ? 'icon-a-quchuangzuo_xuanzhong1x1' : 'icon-a-quchuangzuo_weixuanzhong1x1'}
          className={'menu-item-icon'} />,
        hideInMenu: !isDistributor,
        naviType: 'IMAGE',
      },
      {
        path: '/experience',
        name: '免费试用',
        hotIcon: <img src={FREE_ICON} className={'hot-icon'} alt={''} />,
        icon: <IconFont
          type={menu === '/experience' ? 'icon-icon_shiyong' : 'icon-icon_shiyong'}
          className={'menu-item-icon'} />,
        hideInMenu: isAdmin,
        naviType: 'IMAGE',
      },
      {
        path: '/user-favor',
        name: '我的收藏',
        icon: <IconFont
          type={menu === '/user-favor' ? 'icon-lishirenwu_xuanzhong' : 'icon-lishirenwu_weixuanzhong'}
          style={{ fontSize: '42px', background: menu === '/toolkit-menu' ? '#F5F5F5' : '', borderRadius: '8px' }} />,
        accRoles: ['DISTRIBUTOR'],
        naviType: 'IMAGE',
      },
      {
        path: '/distributor/creative-records',
        name: '创作记录',
        icon: <IconFont
          type={menu === '/distributor/creative-records' ? 'icon-lishirenwu_xuanzhong' : 'icon-lishirenwu_weixuanzhong'}
          className={'menu-item-icon'} />,
        hideInMenu: !isDistributor,
        naviType: 'IMAGE',
      },
      {
        path: '/user-mng-biz',
        name: '客户管理',
        icon: <IconFont
          type={menu === '/user-mng-biz' ? 'icon-a-tuanduiguanli_xuanzhong1x1' : 'icon-a-tuanduiguanli_weixuanzhong1x1'}
          className={'menu-item-icon'} />,
        hideInMenu: !isAdmin || hideMenus.has('/business-mng'),
        naviType: 'BUSINESS',
      },
      {
        path: '/distributor/account-records',
        name: '业绩管理',
        icon: <IconFont
          type={menu === '/distributor/account-records' ? 'icon-dingdan_xuanzhong' : 'icon-dingdan_weixuanzhong'}
          className={'menu-item-icon'} />,
        // 渠道商除运营外，都可以查看业绩，管理员查看全部，销售查看自己
        hideInMenu: !(isDistributor) || JSON.parse(userInfo)?.customRole === 'OPS_MEMBER',
        naviType: 'DIS-MANAGE',
      },
      {
        path: '/distributor/success-stories',
        name: '成功案例',
        icon: (menu === '/distributor/success-stories' ? <SnippetsFilled style={{ fontSize: 24 }} /> :
          <SnippetsOutlined style={{ fontSize: 24 }} />) ,
        // 渠道商除运营外，都可以查看业绩，管理员查看全部，销售查看自己
        hideInMenu: !(isDistributor) || JSON.parse(userInfo)?.customRole === 'OPS_MEMBER',
        naviType: 'DIS-MANAGE',
      },
      {
        path: '/delivery-stats',
        name: '交付统计',
        icon: (menu === '/delivery-stats' ? <PieChartFilled style={{ fontSize: 24 }} /> :
          <PieChartOutlined style={{ fontSize: 24 }} />),
        hideInMenu: !isAdmin || hideMenus.has('/business-mng'),
        naviType: 'BUSINESS',
      },
      {
        path: '/order-mng',
        name: '充值记录',
        icon: <IconFont type={'icon-icon_chengben'} style={{ fontSize: 24 }} />,
        hideInMenu: !isAdmin || hideMenus.has('/business-mng'),
        naviType: 'BUSINESS',
      },
      {
        path: '/consume-record',
        name: '消费记录',
        icon: <IconFont
          type={menu === '/consume-record' ? 'icon-icon_chengben' : 'icon-icon_chengben'}
          style={{ fontSize: 24, borderRadius: '8px' }} />,
        hideInMenu: !isAdmin || hideMenus.has('/business-mng'),
        naviType: 'BUSINESS',
      },
      {
        path: '/invoice-mng',
        name: '发票管理',
        icon: <IconFont
          type={menu === '/invoice-mng' ? 'icon-icon_chengben' : 'icon-icon_chengben'}
          style={{ fontSize: 24, borderRadius: '8px' }} />,
        hideInMenu: !isAdmin || hideMenus.has('/business-mng'),
        naviType: 'BUSINESS',
      },
      {
        path: '/data-statistics',
        name: '数据统计',
        icon: <IconFont
          type={menu === '/data-statistics' || menu === '/point-stats' || menu === '/stats-clothes-info' || menu === '/admin/stats-user-operate' || menu === '/stats-user-operate' || menu === '/stats-operate-indicators' || menu === '/stats-sale-indicators' || menu === '/stats-warning-info' ? 'icon-icon_chengben' : 'icon-icon_chengben'}
          style={{ fontSize: 24, borderRadius: '8px' }} />,
        hideInMenu: !isAdmin || hideMenus.has('/business-mng'),
        naviType: 'BUSINESS',
      },
      {
        path: '/settle',
        name: '销售渠道',
        icon: <IconFont type={menu === '/settle' ? 'icon-yajin' : 'icon-yajin'} style={{ fontSize: 24 }} />,
        hideInMenu: !isAdmin || hideMenus.has('/business-mng'),
        naviType: 'BUSINESS',
      },
      {
        path: '/abtest',
        name: 'AB测试计划',
        icon: (menu === '/abtest' ? <ExperimentFilled style={{ fontSize: 24 }} /> :
          <ExperimentOutlined style={{ fontSize: 24 }} />),
        hideInMenu: !isAdmin,
        naviType: 'DEVELOP',
      },
      {
        path: '/server',
        name: '服务器',
        icon: (menu === '/server' ? <DatabaseFilled style={{ fontSize: 24 }} /> :
          <DatabaseOutlined style={{ fontSize: 24 }} />),
        hideInMenu: !isAdmin,
        naviType: 'DEVELOP',
      },
      {
        path: '/system',
        name: '系统配置',
        icon: (menu === '/system' ? <DatabaseFilled style={{ fontSize: 24 }} /> :
          <DatabaseOutlined style={{ fontSize: 24 }} />),
        hideInMenu: hideMenus.has('/system') || !isAdmin,
        naviType: 'DEVELOP',
      },
      {
        path: '/workflow',
        name: '工作流',
        icon: (menu === '/workflow' ? <NodeIndexOutlined style={{ fontSize: 24 }} /> :
          <NodeIndexOutlined style={{ fontSize: 24 }} />),
        hideInMenu: !isAdmin,
        naviType: 'DEVELOP',
      },
      {
        path: '/train',
        name: '批量训练',
        icon: (menu === '/train' ? <DatabaseFilled style={{ fontSize: 24 }} /> :
          <DatabaseOutlined style={{ fontSize: 24 }} />),
        hideInMenu: !isAdmin,
        naviType: 'DEVELOP',
      },
      {
        path: '/tryon',
        name: '单图tryon',
        icon: (menu === '/tryon' ? <DatabaseFilled style={{ fontSize: 24 }} /> :
          <DatabaseOutlined style={{ fontSize: 24 }} />),
        hideInMenu: !isAdmin,
        naviType: 'DEVELOP',
      },
      {
        path: '/work-schedule',
        name: '排班管理',
        icon: <IconFont
          type={menu === '/work-schedule' ? 'icon-a-paibanguanli_xuanzhong1x1' : 'icon-a-paibanguanli_weixuanzhong1x1'}
          className={'menu-item-icon'} />,
        hideInMenu: !isAdmin && !isReviewer,
        naviType: 'MANAGE',
      },
    ];
  };

  const getCurrentNaviType = (menuList: any[]) => {
    const path = location.pathname;

    let naviType;
    if (path === '/models') {
      naviType = 'IMAGE';
    } else if (path === '/user-info' || path === '/members' || path === '/stats-user-operate') {
      return sessionStorage.getItem(NAVI_TYPE);
    } else {
      const find = menuList.find(item => item.path === path || item.children?.some(item => item.path === path));
      naviType = find && find.naviType ? find.naviType : 'ADMIN';
    }

    window.dispatchEvent(new Event(NAVI_TYPE_CHANGE_EVENT));
    sessionStorage.setItem(NAVI_TYPE, naviType);

    return naviType;
  };

  const allMenuStaticDataList = AllMenuData();
  const currentNaviType = getCurrentNaviType(allMenuStaticDataList);

  const menuDataRender = () => {
    let menus = AllMenuData().filter(item => !item.hideInMenu && item.naviType === currentNaviType)
      .filter(item => !item.accRoles || item.accRoles.includes(userRole));
    if (showMenusByWhitelist && showMenusByWhitelist?.size > 0) {
      menus = menus.filter(item => showMenusByWhitelist.has(item.path));
    }

    return menus;
  };

  // @ts-ignore
  return (
    <ProLayout
      layout={'mix'}
      logo={LOGO_BRAND}
      title={''}
      collapsedButtonRender={false}
      siderWidth={100}
      headerRender={() => <NavigationBar needBorder={true} showMenusByWhitelist={showMenusByWhitelist}
                                         ref={navigationBarRef} />}
      menu={{ defaultOpenAll: true }}
      // collapsed={() => menuDataRender().length === 0}
      // actionsRender={() => (<RightContentRender />)}
      menuItemRender={menuItemRender}
      // @ts-ignore
      menuDataRender={menuDataRender}
      location={{ pathname: '/' }}
    >
      <Routes>
        <Route path={'/'} element={<Home />} />
        <Route path={'/creation'} element={<Creation type={'CUSTOM'} />} />
        <Route path={'/fixed-posture-creation'} element={<FixedPostureCreation type={'CUSTOM'} />} />
        <Route path={'/basic-replace'} element={<BasicReplace type={'CUSTOM'} />} />
        <Route path={'/models'} element={<AllModelsPage />} />
        <Route path={'/history'} element={<History />} />
        <Route path={'/login'} element={<Home />} />
        <Route path={'/register'} element={<Home />} />
        <Route path={'/capture-notice'} element={<CaptureNotice />} />
        <Route path={'/find-pwd'} element={<FindPwd />} />
        <Route path={'/members'} element={<MembersPage />} />
        <Route path={'/cloth-mng'} element={<Lora />} />
        <Route path={'/cloth-review'} element={<ClothReview />} />
        <Route path={'/tags'} element={<TagsPage />} />
        <Route path={'/face'} element={<Face />} />
        <Route path={'/scene'} element={<Scene />} />
        <Route path={'/cloth-style'} element={<ClothStyle />} />
        <Route path={'/refer-pose'} element={<ReferPose />} />
        <Route path={'/user-mng'} element={<UserMng />} />
        <Route path={'/user-mng-biz'} element={<UserMng naviType={'BUSINESS'} />} />
        <Route path={'/order-mng'} element={<OrderInfoMng />} />
        <Route path={'consume-record'} element={<ConsumeRecord />} />
        <Route path={'/settle'} element={<SettleMng />} />
        <Route path={'/experience'} element={<Experience />} />
        <Route path={'/home'} element={<Home />} />
        <Route path={'/user-info'} element={<UserCenterPage />} />
        <Route path={'/creative-record'} element={<CreativeRecord />} />
        <Route path={'/system'} element={<System />} />
        <Route path={'/workflow'} element={<WorkflowMng />} />
        <Route path={'/server'} element={<Server />} />
        <Route path={'/logo-combine'} element={<LogoCombine />} />
        <Route path={'/face-scene-switch'} element={<FaceSceneSwitch type={'CUSTOM'} />} />
        <Route path={'/distributor/pub-view'} element={<DistributorFaceAndSceneView />} />
        <Route path={'/distributor/team'} element={<DistributorTeam />} />
        <Route path={'/distributor/customers'} element={<DistributorCustomers />} />
        <Route path={'/distributor/creative-records'} element={<DistributorCustomerCreativeRecords />} />
        <Route path={'/distributor/account-records'} element={<DistributorCustomerAccountRecords />} />
        <Route path={'/create-video'} element={<CreateVideo />} />
        <Route path={'/delivery-stats'} element={<DeliveryStats />} />
        <Route path={'/merchant-preference'} element={<MerchantPreferenceMng />} />
        <Route path={'/upload'} element={<UploadMaterialTotalPage />} />
        <Route path={'/mng/upload'} element={<UploadMaterialTotalPage />} />
        <Route path={'/train'} element={<TrainPlanPage />} />
        <Route path={'/tryon'} element={<TryonPage />} />
        <Route path={'/invoice-mng'} element={<InvoiceMngPage />} />
        <Route path={'/tutorial'} element={<TutorialPage />} />
        <Route path={'/window-video'} element={<WindowVideo />} />
        <Route path={'/video-history'} element={<VideoHistory />} />
        <Route path={'/design-history'} element={<DesignHistory />} />
        <Route path={'/abtest'} element={<ABTest />} />
        <Route path={'/toolkit-menu'} element={<ToolkitMenu />} />
        <Route path={'/user-favor'} element={<UserFavor />} />
        <Route path={'/video-favor'} element={<UserFavor type={'VIDEO'} />} />
        <Route path={'/work-schedule'} element={<WorkSchedule />} />
        <Route path={'/data-statistics'} element={<DataStatistics />} />
        <Route path={'/point-stats'} element={<DataStatistics />} />
        <Route path={'/stats-clothes-info'} element={<DataStatistics />} />
        <Route path={'/stats-user-operate'} element={<DataStatistics />} />
        <Route path={'/admin/stats-user-operate'} element={<DataStatistics />} />
        <Route path={'/stats-warning-info'} element={<DataStatistics />} />
        <Route path={'/stats-operate-indicators'} element={<DataStatistics />} />
        <Route path={'/stats-sale-indicators'} element={<DataStatistics />} />
        <Route path={'/clothing-swap'} element={<ClothingSwap />} />
        <Route path={'/distributor/success-stories'} element={<SalesSuccessStoriesMng />} />
      </Routes>
    </ProLayout>
  );
};


// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function rootContainer(container: React.ReactNode) {
  return <Router>
    <BasicLayout />
  </Router>;
}