/**
 * animationUtils.ts
 * 通用动画样式工具类
 * 提供可复用的CSS动画样式
 * @example StyleLoraDrawer.tsx
 */
import { CSSProperties } from 'react';


// 动画类型
export enum AnimationType {
  // 淡入 
  FADE_IN = 'fadeIn',
  // 淡入项
  FADE_IN_ITEM = 'fadeInItem',
  // 右滑入
  SLIDE_IN_RIGHT = 'slideInRight',
  // 左滑入
  SLIDE_IN_LEFT = 'slideInLeft',
  // 缩放入
  ZOOM_IN = 'zoomIn',
  // 弹跳
  BOUNCE = 'bounce'
}

// 缓动函数类型
export enum EasingType {
  // 线性
  LINEAR = 'linear',
  // 平缓
  EASE = 'ease',
  // 缓入
  EASE_IN = 'ease-in',
  // 缓出
  EASE_OUT = 'ease-out',
  // 缓入缓出
  EASE_IN_OUT = 'ease-in-out'
}

// 动画配置接口
export interface AnimationConfig {
  duration?: number; // 动画持续时间（毫秒）
  delay?: number; // 动画延迟（毫秒）
  easing?: EasingType; // 缓动函数
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both'; // 动画填充模式
  iterationCount?: number | 'infinite'; // 动画重复次数
}

// 默认动画配置
const defaultConfig: AnimationConfig = {
  duration: 300,
  delay: 0,
  easing: EasingType.EASE_IN_OUT,
  fillMode: 'forwards',
  iterationCount: 1
};

/**
 * 生成动画样式
 * @param type 动画类型
 * @param config 动画配置
 * @returns React CSS样式对象
 */
export const generateAnimationStyle = (
  type: AnimationType,
  config: AnimationConfig = {}
): CSSProperties => {
  // 合并默认配置
  const finalConfig = { ...defaultConfig, ...config };
  
  return {
    animation: `${type} ${finalConfig.duration}ms ${finalConfig.easing} ${finalConfig.delay}ms ${finalConfig.fillMode} ${finalConfig.iterationCount}`,
    opacity: type.includes('fade') ? 0 : undefined, // 对于淡入动画，初始透明度为0
  };
};

/**
 * 生成带有索引延迟的动画样式（适用于列表项）
 * @param type 动画类型
 * @param index 项目索引
 * @param baseDelay 基础延迟时间（毫秒）
 * @param config 动画配置
 * @returns React CSS样式对象
 */
export const generateStaggeredAnimationStyle = (
  type: AnimationType,
  index: number,
  baseDelay: number = 50,
  config: AnimationConfig = {}
): CSSProperties => {
  return generateAnimationStyle(type, {
    ...config,
    delay: (config.delay || 0) + index * baseDelay
  });
};

/**
 * 动画关键帧定义
 * 应该在全局样式中引入这些关键帧定义
 */
export const animationKeyframes = `
  @keyframes ${AnimationType.FADE_IN} {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes ${AnimationType.FADE_IN_ITEM} {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes ${AnimationType.SLIDE_IN_RIGHT} {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes ${AnimationType.SLIDE_IN_LEFT} {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes ${AnimationType.ZOOM_IN} {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes ${AnimationType.BOUNCE} {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-20px);
    }
    60% {
      transform: translateY(-10px);
    }
  }
`; 