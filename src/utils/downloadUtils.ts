export const downloadJson = (json: string) => {
  try {
    // 创建 Blob 对象
    const blob = new Blob([json], { type: 'application/json' });

    // 创建一个临时的 URL
    const url = window.URL.createObjectURL(blob);

    // 创建一个隐藏的 <a> 元素
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = 'workflow.json'; // 文件名

    // 将 <a> 元素添加到文档中
    document.body.appendChild(a);

    // 触发下载
    a.click();

    // 清理工作
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  } catch (error) {
    console.error('下载文件时出错:', error);
  }
};

// 下载Excel文件
export const downloadExcel = (blob: Blob, filename: string = 'export.xlsx') => {
  try {
    // 创建一个临时的 URL
    const url = window.URL.createObjectURL(blob);

    // 创建一个隐藏的 <a> 元素
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename; // 文件名

    // 将 <a> 元素添加到文档中
    document.body.appendChild(a);

    // 触发下载
    a.click();

    // 清理工作
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  } catch (error) {
    console.error('下载Excel文件时出错:', error);
    throw error;
  }
};