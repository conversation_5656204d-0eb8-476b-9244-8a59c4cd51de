import { UserFavorVO, getAllFavorInfoWithBlobs, addFavor, removeFavor, UserFavorType } from '@/services/UserFavorController';

// 单例模式，全局共享收藏列表
class FavorManager {
  private static instance: FavorManager;
  private favorList: UserFavorVO[] = [];
  private listeners: Array<(favorList: UserFavorVO[]) => void> = [];
  private loading: boolean = false;

  private constructor() {
    // 私有构造函数，防止外部直接创建实例
  }

  public static getInstance(): FavorManager {
    if (!FavorManager.instance) {
      FavorManager.instance = new FavorManager();
    }
    return FavorManager.instance;
  }

  // 获取收藏列表
  public getFavorList(): UserFavorVO[] {
    return this.favorList;
  }

  // 获取加载状态
  public isLoading(): boolean {
    return this.loading;
  }

  // 添加监听器
  public addListener(listener: (favorList: UserFavorVO[]) => void): void {
    this.listeners.push(listener);
  }

  // 移除监听器
  public removeListener(listener: (favorList: UserFavorVO[]) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index !== -1) {
      this.listeners.splice(index, 1);
    }
  }

  // 通知所有监听器
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.favorList));
  }

  // 获取所有收藏信息
  public async fetchAllFavors(): Promise<void> {
    try {
      this.loading = true;
      const result = await getAllFavorInfoWithBlobs();
      if (result) {
        this.favorList = result;
        this.notifyListeners();
      }
    } catch (error) {
      console.error('Failed to fetch favorites:', error);
    } finally {
      this.loading = false;
      this.notifyListeners();
    }
  }

  // 添加收藏
  public async addUserFavor(payload: any): Promise<boolean> {
    try {
      const result = await addFavor(payload);
      
      // 检查返回结果是否存在且不是空对象
      const isSuccess = result !== null && result !== undefined;
      const isEmptyObject = isSuccess && typeof result === 'object' && Object.keys(result).length === 0;
      
      if (isSuccess && !isEmptyObject) {
        // 如果返回了实际数据对象
        if (result && typeof result === 'object' && Object.keys(result).length > 0) {
          // 查找现有记录的索引
          const index = this.favorList.findIndex(item => 
            item.type === payload.type && item.itemId === payload.itemId
          );
          
          if (index !== -1) {
            // 更新现有记录
            this.favorList[index] = result;
          } else {
            // 添加新记录
            this.favorList.push(result);
          }
          
          // 通知监听器
          this.notifyListeners();
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to add favorite:', error);
      return false;
    }
  }

  // 移除收藏
  public async removeUserFavor(payload: any): Promise<boolean> {
    try {
      const result = await removeFavor(payload);
      
      // 检查是否成功
      const isSuccess = result !== null && result !== undefined;
      
      if (isSuccess) {
        // 查找现有记录的索引
        const index = this.favorList.findIndex(item => 
          item.type === payload.type && item.itemId === payload.itemId
        );
        
        if (index !== -1) {
          // 如果返回了实际数据对象
          if (result && typeof result === 'object' && Object.keys(result).length > 0) {
            // 如果返回了结果，说明只删除了部分图片，更新现有记录
            this.favorList[index] = result;
          } else {
            // 如果返回了空对象或 { success: true }，说明整条记录被删除
            this.favorList.splice(index, 1);
          }
          // 通知监听器
          this.notifyListeners();
        }
        
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to remove favorite:', error);
      return false;
    }
  }

  // 检查是否已收藏
  public isFavorited(type: string, itemId: number): boolean {
    return this.favorList.some(item => item.type === type && item.itemId === itemId);
  }
}

// 导出单例实例
export const favorManager = FavorManager.getInstance();

// 导出一个 React Hook，用于在组件中使用 FavorManager
export function useFavorManager() {
  return favorManager;
} 