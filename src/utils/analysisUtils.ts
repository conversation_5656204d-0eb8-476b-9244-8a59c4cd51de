import { BadCaseItem, SimilarityItem, BadCaseData } from '@/types/analysis';

// 创建空的相似度数据
export const createEmptySimilarityItem = (): SimilarityItem => ({
  totalCount: 0,
  goodCount: 0,
  badCount: 0,
  notOperationCount: 0
});

// 创建空的 BadCase 数据
export const createEmptyBadCaseItem = (): BadCaseItem => ({
  totalCount: 0,
  count: 0,
  ratio: 0
});

// 创建空的 BadCase Map
export const createEmptyBadCaseMap = (abnormalTypes: string[] = []): Record<string, BadCaseItem> => {
  const emptyMap: Record<string, BadCaseItem> = {};
  abnormalTypes.forEach(type => {
    emptyMap[type] = createEmptyBadCaseItem();
  });
  return emptyMap;
};

// 空分析数据
export const emptyAnalysis = (abnormalTypes: string[] = []): BadCaseData => ({
  id: 0,
  experimentalGroupMap: createEmptyBadCaseMap(abnormalTypes),
  controlGroupMap: createEmptyBadCaseMap(abnormalTypes),
  totalMap: createEmptyBadCaseMap(abnormalTypes)
}); 