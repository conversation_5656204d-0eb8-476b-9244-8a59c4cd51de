/** 系统信息工具类 */

// 系统信息对象
interface SystemInfo {
  // 操作系统
  os: string;
  // 浏览器和操作系统的详细信息
  userAgent: string;
  // 设备类型
  deviceType: string;
  // 来源
  referrer: string;
  // 浏览器
  browser: string;
  // 屏幕宽度
  screenWidth: number;
  // 屏幕高度
  screenHeight: number;
  // 窗口宽度
  windowWidth: number;
  // 窗口高度
  windowHeight: number;
  // 页面标题
  pageTitle: string;
  // 当前页面的 URL
  currentUrl: string;
  // 上一个页面的 URL
  previousUrl: string;
  // 具体详细信息
  additionalData: object;
}

/**
 * 获取系统信息
 */
export function getSystemInfo(): SystemInfo {
  const os = navigator.platform;
  const userAgent = navigator.userAgent;
  const deviceType = getDeviceType(userAgent);
  const referrer = document.referrer || '无来源';
  const browser = getBrowserInfo(userAgent);
  const screenWidth = window.screen.width;
  const screenHeight = window.screen.height;
  const windowWidth = window.innerWidth;
  const windowHeight = window.innerHeight;
  const pageTitle = document.title;
  const currentUrl = window.location.href;
  const previousUrl = document.referrer || '无来源';
  const additionalData = {};

  return {
    os,
    userAgent,
    deviceType,
    referrer,
    browser,
    screenWidth,
    screenHeight,
    windowWidth,
    windowHeight,
    pageTitle,
    currentUrl,
    previousUrl,
    additionalData,
  };
}

/**
 * 获取设备类型
 * @param userAgent 浏览器和操作系统的详细信息
 */
function getDeviceType(userAgent: string): string {
  if (/Mobi|Android/i.test(userAgent)) {
    return 'Mobile';
  } else if (/Tablet|iPad/i.test(userAgent)) {
    return 'iPad';
  } else {
    return 'PC';
  }
}

/**
 * 获取浏览器信息
 * @param userAgent 浏览器和操作系统的详细信息
 */
function getBrowserInfo(userAgent: string): string {
  let browserName = 'Unknown Browser';

  if (userAgent.indexOf('Chrome') > -1 && userAgent.indexOf('Safari') > -1) {
    browserName = 'Chrome';
  } else if (userAgent.indexOf('Firefox') > -1) {
    browserName = 'Firefox';
  } else if (userAgent.indexOf('Safari') > -1 && userAgent.indexOf('Chrome') === -1) {
    browserName = 'Safari';
  } else if (userAgent.indexOf('Edge') > -1) {
    browserName = 'Edge';
  } else if (userAgent.indexOf('MSIE') > -1 || userAgent.indexOf('Trident') > -1) {
    browserName = 'Internet Explorer';
  }

  return browserName;
}