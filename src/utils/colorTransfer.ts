// @ts-nocheck
import cvReadyPromise from '@techstark/opencv-js';

export interface ColorTransferOptions {
  sourceImageEl: HTMLImageElement;
  maskImageEl: HTMLImageElement;
  targetColorHex: string;
  options?: {
    texturePreserve?: number;
    smartColorAdjust?: boolean;
  };
}

// provided OpenCV color transfer function
function hexToRgb(hex) {
  const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
  hex = hex.replace(shorthandRegex, function (m, r, g, b) {
    return r + r + g + g + b + b;
  });
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null;
}

// RGB到HSV转换辅助函数 (0-360, 0-100, 0-100)
function rgbToHsv(r, g, b) {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const diff = max - min;

  let h = 0;
  if (diff !== 0) {
    if (max === r) h = ((g - b) / diff) % 6;
    else if (max === g) h = (b - r) / diff + 2;
    else h = (r - g) / diff + 4;
  }
  h = h * 60;
  if (h < 0) h += 360;

  const s = max === 0 ? 0 : (diff / max) * 100;
  const v = max * 100;

  return { h, s, v };
}

// RGB到HSV转换辅助函数 (0-360, 0-255, 0-255) - 匹配Python OpenCV格式
function rgbToHsv255(r, g, b) {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const diff = max - min;

  let h = 0;
  if (diff !== 0) {
    if (max === r) h = ((g - b) / diff) % 6;
    else if (max === g) h = (b - r) / diff + 2;
    else h = (r - g) / diff + 4;
  }
  h = h * 60;
  if (h < 0) h += 360;

  // OpenCV HSV格式: H(0-179), S(0-255), V(0-255)
  // 但Python代码中直接使用了0-255的S和V，所以我们也这样做
  const s = max === 0 ? 0 : (diff / max) * 255;
  const v = max * 255;

  return { h: h / 2, s, v }; // H除以2是为了匹配OpenCV的0-179范围
}

// 自适应参数调整函数 - 完全按照Python版本实现
function adaptiveAdjustParameters(
  foregroundImageData,
  targetRgb,
  maskImageData,
) {
  console.log('🧮 开始计算自适应参数...');
  const startTime = performance.now();

  const data = foregroundImageData.data;
  const maskData = maskImageData.data;
  const width = foregroundImageData.width;
  const height = foregroundImageData.height;

  console.log(`📊 图像数据: ${width}x${height}, 总像素: ${width * height}`);

  // 收集有效区域的HSV值
  const validSValues = [];
  const validVValues = [];

  console.log('🔍 步骤1: 提取有效区域HSV值...');
  const extractStartTime = performance.now();

  // 提取有效区域的HSV值
  for (let i = 0; i < height; i++) {
    if (i % 50 === 0) {
      const progress = ((i / height) * 100).toFixed(1);
      console.log(`🔄 HSV提取进度: ${progress}% (${i}/${height} 行)`);
    }

    for (let j = 0; j < width; j++) {
      const idx = (i * width + j) * 4;
      const maskIdx = (i * width + j) * 4;

      // 检查掩膜值 (对应Python中的valid_mask > 0.1，这里用25/255 ≈ 0.1)
      if (maskData[maskIdx] > 25) {
        const r = data[idx];
        const g = data[idx + 1];
        const b = data[idx + 2];

        // 转换RGB到HSV (Python使用0-255范围)
        const hsv = rgbToHsv255(r, g, b);
        validSValues.push(hsv.s);
        validVValues.push(hsv.v);
      }
    }
  }

  console.log(
    `✅ HSV提取完成，有效像素: ${validSValues.length}, 耗时: ${(performance.now() - extractStartTime).toFixed(2)}ms`,
  );

  if (validSValues.length === 0) {
    console.log('⚠️ 没有有效的前景像素，使用默认参数');
    return { saturationStrength: 0.7, brightnessStrength: 0.7 };
  }

  // 计算原始图像的饱和度和亮度统计信息
  const origSMean =
    validSValues.reduce((sum, s) => sum + s, 0) / validSValues.length;
  const origVMean =
    validVValues.reduce((sum, v) => sum + v, 0) / validVValues.length;

  const origSStd = Math.sqrt(
    validSValues.reduce((sum, s) => sum + Math.pow(s - origSMean, 2), 0) /
      validSValues.length,
  );
  const origVStd = Math.sqrt(
    validVValues.reduce((sum, v) => sum + Math.pow(v - origVMean, 2), 0) /
      validVValues.length,
  );

  console.log(
    `📈 原始图像统计: S均值=${origSMean.toFixed(2)}, S标准差=${origSStd.toFixed(2)}, V均值=${origVMean.toFixed(2)}, V标准差=${origVStd.toFixed(2)}`,
  );

  // 计算目标颜色的HSV (使用0-255范围)
  const targetHsv = rgbToHsv255(targetRgb.r, targetRgb.g, targetRgb.b);
  const targetS = targetHsv.s;
  const targetV = targetHsv.v;

  console.log(`🎨 目标HSV: S=${targetS.toFixed(2)}, V=${targetV.toFixed(2)}`);

  // 1. 饱和度自适应调整 - 完全按照Python逻辑
  let saturationStrength;
  if (origSMean < 50) {
    // 原始图像饱和度较低
    if (targetS > 100) {
      // 目标颜色饱和度高
      saturationStrength = 0.8; // 增强饱和度调整
    } else {
      saturationStrength = 0.6; // 适中调整
    }
  } else if (origSMean > 150) {
    // 原始图像饱和度较高
    if (targetS < 80) {
      // 目标颜色饱和度低
      saturationStrength = 0.9; // 强力降低饱和度
    } else {
      saturationStrength = 0.5; // 轻微调整
    }
  } else {
    // 原始图像饱和度适中
    saturationStrength = 0.7; // 标准调整
  }

  // 根据饱和度变化幅度微调
  const sVariation = origSStd / 255.0;
  if (sVariation > 0.3) {
    // 饱和度变化大，保留更多原始特征
    saturationStrength *= 0.8;
  }

  // 2. 亮度自适应调整 - 完全按照Python逻辑
  let brightnessStrength;
  if (origVMean < 80) {
    // 原始图像较暗
    if (targetV > 150) {
      // 目标颜色较亮
      brightnessStrength = 0.6; // 适度提亮
    } else {
      brightnessStrength = 0.4; // 保持暗调
    }
  } else if (origVMean > 180) {
    // 原始图像较亮
    if (targetV < 100) {
      // 目标颜色较暗
      brightnessStrength = 0.8; // 适度压暗
    } else {
      brightnessStrength = 0.5; // 保持亮调
    }
  } else {
    // 原始图像亮度适中
    brightnessStrength = 0.7; // 标准调整
  }

  // 根据亮度对比度调整
  const vContrast = origVStd / 255.0;
  if (vContrast > 0.25) {
    // 对比度高，保留更多层次
    brightnessStrength *= 0.7;
  } else if (vContrast < 0.1) {
    // 对比度低，可以更大胆调整
    brightnessStrength *= 1.2;
  }

  // 3. 色彩和谐度优化
  // 计算目标颜色与原始颜色的差异
  const colorDistance =
    Math.abs(targetS - origSMean) + Math.abs(targetV - origVMean);
  if (colorDistance > 150) {
    // 颜色差异很大
    // 减少调整强度，避免过度变化
    saturationStrength *= 0.8;
    brightnessStrength *= 0.8;
  }

  // 限制参数范围
  saturationStrength = Math.max(0.3, Math.min(1.0, saturationStrength));
  brightnessStrength = Math.max(0.3, Math.min(1.0, brightnessStrength));

  const totalTime = performance.now() - startTime;
  console.log(`🎉 自适应参数计算完成！总耗时: ${totalTime.toFixed(2)}ms`);
  console.log(
    `📈 最终参数: 饱和度强度=${saturationStrength.toFixed(3)}, 亮度强度=${brightnessStrength.toFixed(3)}`,
  );
  console.log(
    `🔍 调整逻辑: 原S=${origSMean.toFixed(1)}, 目标S=${targetS.toFixed(1)}, 原V=${origVMean.toFixed(1)}, 目标V=${targetV.toFixed(1)}, 距离=${colorDistance.toFixed(1)}`,
  );

  return { saturationStrength, brightnessStrength };
}

// 辅助函数：将图像转换为 OpenCV Mat
const imageToMat = async (image: HTMLImageElement) => {
  const cv = await cvReadyPromise;
  const canvas = document.createElement('canvas');
  canvas.width = image.width;
  canvas.height = image.height;
  const ctx = canvas.getContext('2d');
  ctx.drawImage(image, 0, 0);
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  return cv.matFromImageData(imageData);
};

// 主要的换色处理函数 - 完全对应Python版本的process_color_transfer_pytorch
export async function processColorTransfer(sourceImageEl, maskImageEl, targetColorHex, options = {}) {
  const cv = await cvReadyPromise;

  const { texturePreserve = 0.7, smartColorAdjust = true } = options;
  let srcMat = null, maskMatGray = null, targetColorRgbMat = null, targetColorLabMat = null;
  let srcLabMat = null, srcL = null, srcA = null, srcB = null;
  let finalL = null, finalA = null, finalB = null;
  let resultLabMat = null, resultRgbMat = null;
  let meanFgLVec = null;
  let rawMaskMat = null;

  try {
    console.log('🎨 开始颜色转换处理...');
    console.log(`🎯 目标颜色: ${targetColorHex}, 纹理保留: ${texturePreserve}, 智能调色: ${smartColorAdjust}`);

    // 验证输入图像
    if (!sourceImageEl || !maskImageEl) {
      throw new Error("源图像或遮罩图像缺失");
    }

    if (!sourceImageEl.complete || !maskImageEl.complete) {
      throw new Error("图像未完全加载");
    }

    // 转换图像为 OpenCV Mat
    srcMat = await imageToMat(sourceImageEl);
    rawMaskMat = await imageToMat(maskImageEl);

    // 确保图像尺寸匹配
    if (srcMat.rows !== rawMaskMat.rows || srcMat.cols !== rawMaskMat.cols) {
      throw new Error("源图像和遮罩图像尺寸不匹配");
    }

    // 转换颜色空间
    cv.cvtColor(srcMat, srcMat, cv.COLOR_RGBA2RGB);
    maskMatGray = new cv.Mat();
    cv.cvtColor(rawMaskMat, maskMatGray, cv.COLOR_RGBA2GRAY);
    cv.threshold(maskMatGray, maskMatGray, 25, 255, cv.THRESH_BINARY); // 使用25作为阈值，对应Python中的0.1

    // 处理目标颜色
    const targetRgbObj = hexToRgb(targetColorHex);
    if (!targetRgbObj) {
      throw new Error("无效的目标颜色 HEX 值");
    }

    // 创建有效掩膜（对应Python中的valid_mask > 0.1）
    const validMask = new cv.Mat();
    cv.threshold(maskMatGray, validMask, 25, 255, cv.THRESH_BINARY);

    // 执行LAB基础换色（两种模式都使用相同的基础换色）
    console.log('🔄 步骤1: 执行LAB基础换色...');
    const baseResult = await processLabColorTransfer(srcMat, validMask, targetRgbObj, texturePreserve);

    let finalResult;
    if (smartColorAdjust) {
      console.log('🧠 步骤2: 应用智能调色增强...');
      // 智能调色模式：基于直接替换结果应用直方图均衡化等增强算法
      finalResult = await processHistogramEnhancedRecolor(
        baseResult, srcMat, targetRgbObj, validMask, texturePreserve
      );
      console.log('✅ 智能调色模式处理完成');
      // 清理基础结果
      baseResult.delete();
    } else {
      console.log('📋 使用直接替换模式结果');
      // 直接替换模式：直接使用LAB换色结果
      finalResult = baseResult;
    }

    // 合并换色结果和原始图像（只在掩膜区域应用换色）
    console.log('🔗 步骤3: 合并换色结果和原始图像...');
    const resultMat = srcMat.clone(); // 从原始图像开始

    // 只在掩膜区域复制换色结果
    finalResult.copyTo(resultMat, validMask);

    // 输出到画布
    const resultCanvas = document.createElement('canvas');
    resultCanvas.width = resultMat.cols;
    resultCanvas.height = resultMat.rows;
    cv.imshow(resultCanvas, resultMat);

    console.log('🎉 颜色转换处理完成！');

    // 清理临时资源
    [validMask, finalResult, resultMat].forEach(mat => {
      if (mat && mat.delete) mat.delete();
    });

    return resultCanvas;

  } catch (error) {
    console.error("❌ OpenCV.js 颜色转换错误:", error);
    throw error;
  } finally {
    // 清理所有 OpenCV Mat
    [srcMat, rawMaskMat, maskMatGray].forEach(mat => {
      if (mat && mat.delete) mat.delete();
    });
  }
}

// LAB基础换色处理函数 - 对应Python版本的基础LAB换色
async function processLabColorTransfer(srcMat, validMask, targetRgb, texturePreserve) {
  const cv = await cvReadyPromise;
  console.log('🔬 执行LAB基础换色...');

  let srcLabMat = null, targetColorRgbMat = null, targetColorLabMat = null;
  let labChannels = null, srcL = null, srcA = null, srcB = null;
  let newLabL = null, newLabA = null, newLabB = null;
  let resultLabMat = null, resultRgbMat = null;

  try {
    // 转换源图像到LAB空间
    srcLabMat = new cv.Mat();
    cv.cvtColor(srcMat, srcLabMat, cv.COLOR_RGB2Lab);

    // 创建目标颜色的LAB表示
    targetColorRgbMat = new cv.Mat(1, 1, cv.CV_8UC3, new cv.Scalar(targetRgb.r, targetRgb.g, targetRgb.b));
    targetColorLabMat = new cv.Mat();
    cv.cvtColor(targetColorRgbMat, targetColorLabMat, cv.COLOR_RGB2Lab);

    // 获取目标颜色的LAB值
    const targetL = targetColorLabMat.ucharPtr(0, 0)[0];
    const targetA = targetColorLabMat.ucharPtr(0, 0)[1];
    const targetB = targetColorLabMat.ucharPtr(0, 0)[2];
    console.log(`🎨 目标LAB值: L=${targetL}, A=${targetA}, B=${targetB}`);

    // 分离LAB通道
    labChannels = new cv.MatVector();
    cv.split(srcLabMat, labChannels);
    srcL = labChannels.get(0);
    srcA = labChannels.get(1);
    srcB = labChannels.get(2);

    // 计算前景区域的平均L值
    const lMeanVec = cv.mean(srcL, validMask);
    const lMean = lMeanVec[0];
    console.log(`📊 前景平均L值: ${lMean.toFixed(2)}`);

    // 创建新的LAB通道
    newLabL = srcL.clone();
    newLabA = srcA.clone();
    newLabB = srcB.clone();

    // 对有效区域应用LAB换色
    for (let i = 0; i < srcMat.rows; i++) {
      for (let j = 0; j < srcMat.cols; j++) {
        if (validMask.ucharPtr(i, j)[0] > 25) { // 有效掩膜区域
          const origL = srcL.ucharPtr(i, j)[0];

          // L通道处理：保持纹理细节
          const lDiff = origL - lMean;
          const newL = targetL + lDiff * texturePreserve;
          newLabL.ucharPtr(i, j)[0] = Math.max(0, Math.min(255, newL));

          // A和B通道直接替换为目标颜色
          newLabA.ucharPtr(i, j)[0] = targetA;
          newLabB.ucharPtr(i, j)[0] = targetB;
        }
      }
    }

    // 合并LAB通道
    resultLabMat = new cv.Mat();
    const newLabChannels = new cv.MatVector();
    newLabChannels.push_back(newLabL);
    newLabChannels.push_back(newLabA);
    newLabChannels.push_back(newLabB);
    cv.merge(newLabChannels, resultLabMat);
    newLabChannels.delete();

    // 转换回RGB
    resultRgbMat = new cv.Mat();
    cv.cvtColor(resultLabMat, resultRgbMat, cv.COLOR_Lab2RGB);

    console.log('✅ LAB基础换色完成');

    // 清理中间资源，但保留返回的resultRgbMat
    [srcLabMat, targetColorRgbMat, targetColorLabMat, labChannels,
      srcL, srcA, srcB, newLabL, newLabA, newLabB, resultLabMat].forEach(mat => {
        if (mat && mat.delete) mat.delete();
      });

    return resultRgbMat;

  } catch (error) {
    console.error('❌ LAB换色处理错误:', error);
    // 发生错误时清理所有资源
    [srcLabMat, targetColorRgbMat, targetColorLabMat, labChannels,
      srcL, srcA, srcB, newLabL, newLabA, newLabB, resultLabMat, resultRgbMat].forEach(mat => {
        if (mat && mat.delete) mat.delete();
      });
    throw error;
  }
}

// 直方图增强换色处理函数 - 对应Python版本的_histogram_enhanced_recolor
async function processHistogramEnhancedRecolor(baseResult, originalSrc, targetRgb, validMask, texturePreserve,
  // CLAHE参数
  claheClipLimit = 1.8,
  claheTileSize = 6,
  // 色度增强参数
  chromaBlend = 0.7,
  // 饱和度调整参数
  saturationLimitFactor = 1.2,
  saturationBlendFactor = 0.4,
  // 色彩混合参数
  colorBlendFactor = 0.7,
  // 亮度调整参数
  brightnessBoostLimit = 12,
  brightnessReduceLimit = 8,
  darkThreshold = 35,
  brightThreshold = 85,
  // 饱和度融合参数
  saturationFusion = 0.6,
  // 最终混合参数
  finalBlend = 0.85
) {
  const cv = await cvReadyPromise;
  console.log('🧠 执行直方图增强换色...');

  let baseLabMat = null, originalLabMat = null, baseHsvMat = null, originalHsvMat = null;
  let enhancedLabMat = null, enhancedHsvMat = null, resultMat = null;
  let labChannels = null, hsvChannels = null;

  try {
    // 转换到LAB和HSV空间
    baseLabMat = new cv.Mat();
    originalLabMat = new cv.Mat();
    baseHsvMat = new cv.Mat();
    originalHsvMat = new cv.Mat();

    cv.cvtColor(baseResult, baseLabMat, cv.COLOR_RGB2Lab);
    cv.cvtColor(originalSrc, originalLabMat, cv.COLOR_RGB2Lab);
    cv.cvtColor(baseResult, baseHsvMat, cv.COLOR_RGB2HSV);
    cv.cvtColor(originalSrc, originalHsvMat, cv.COLOR_RGB2HSV);

    // 1. 应用CLAHE增强
    console.log('📈 步骤1: 应用CLAHE增强...');
    enhancedLabMat = baseLabMat.clone();

    // 分离LAB通道
    labChannels = new cv.MatVector();
    cv.split(enhancedLabMat, labChannels);
    const lChannel = labChannels.get(0);
    const aChannel = labChannels.get(1);
    const bChannel = labChannels.get(2);

    // 对L通道应用CLAHE
    const clahe = new cv.CLAHE(claheClipLimit, new cv.Size(claheTileSize, claheTileSize));
    const enhancedL = new cv.Mat();
    clahe.apply(lChannel, enhancedL);

    // 对A、B通道应用温和的增强
    const enhancedA = new cv.Mat();
    const enhancedB = new cv.Mat();
    const chromaClahe = new cv.CLAHE(claheClipLimit * 0.7, new cv.Size(claheTileSize, claheTileSize));
    chromaClahe.apply(aChannel, enhancedA);
    chromaClahe.apply(bChannel, enhancedB);

    // 混合原始和增强的色度通道
    const blendedA = new cv.Mat();
    const blendedB = new cv.Mat();
    cv.addWeighted(enhancedA, chromaBlend, aChannel, 1 - chromaBlend, 0, blendedA);
    cv.addWeighted(enhancedB, chromaBlend, bChannel, 1 - chromaBlend, 0, blendedB);

    // 重新合并LAB通道
    const enhancedLabChannels = new cv.MatVector();
    enhancedLabChannels.push_back(enhancedL);
    enhancedLabChannels.push_back(blendedA);
    enhancedLabChannels.push_back(blendedB);
    cv.merge(enhancedLabChannels, enhancedLabMat);
    enhancedLabChannels.delete();

    // 2. HSV饱和度增强
    console.log('🌈 步骤2: HSV饱和度增强...');
    enhancedHsvMat = baseHsvMat.clone();

    hsvChannels = new cv.MatVector();
    cv.split(enhancedHsvMat, hsvChannels);
    const hChannel = hsvChannels.get(0);
    const sChannel = hsvChannels.get(1);
    const vChannel = hsvChannels.get(2);

    // 对饱和度通道应用增强
    const enhancedS = new cv.Mat();
    const sClahe = new cv.CLAHE(claheClipLimit * 0.8, new cv.Size(claheTileSize, claheTileSize));
    sClahe.apply(sChannel, enhancedS);

    // 智能饱和度调整 - 避免过度饱和
    const saturationBlend = saturationBlendFactor * (1 - texturePreserve);
    const blendedS = new cv.Mat();
    cv.addWeighted(enhancedS, saturationBlend, sChannel, 1 - saturationBlend, 0, blendedS);

    // 重新合并HSV通道
    const enhancedHsvChannels = new cv.MatVector();
    enhancedHsvChannels.push_back(hChannel);
    enhancedHsvChannels.push_back(blendedS);
    enhancedHsvChannels.push_back(vChannel);
    cv.merge(enhancedHsvChannels, enhancedHsvMat);
    enhancedHsvChannels.delete();

    // 3. 色彩平衡和智能混合
    console.log('🎨 步骤3: 色彩平衡和智能混合...');

    // 获取目标颜色的LAB值
    const targetColorMat = new cv.Mat(1, 1, cv.CV_8UC3, new cv.Scalar(targetRgb.r, targetRgb.g, targetRgb.b));
    const targetLabMat = new cv.Mat();
    cv.cvtColor(targetColorMat, targetLabMat, cv.COLOR_RGB2Lab);
    const targetL = targetLabMat.ucharPtr(0, 0)[0];
    const targetA = targetLabMat.ucharPtr(0, 0)[1];
    const targetB = targetLabMat.ucharPtr(0, 0)[2];

    // 智能色彩混合
    const blendStrength = colorBlendFactor * (1 - texturePreserve);

    // 分离增强后的LAB通道进行最终调整
    const finalLabChannels = new cv.MatVector();
    cv.split(enhancedLabMat, finalLabChannels);
    const finalL = finalLabChannels.get(0);
    const finalA = finalLabChannels.get(1);
    const finalB = finalLabChannels.get(2);

    // 对有效区域进行最终的色彩调整
    for (let i = 0; i < enhancedLabMat.rows; i++) {
      for (let j = 0; j < enhancedLabMat.cols; j++) {
        if (validMask.ucharPtr(i, j)[0] > 25) {
          const currentL = finalL.ucharPtr(i, j)[0];
          const currentA = finalA.ucharPtr(i, j)[0];
          const currentB = finalB.ucharPtr(i, j)[0];

          // 亮度微调
          let adjustedL = currentL;
          if (targetL < darkThreshold) {
            const lBoost = Math.min(brightnessBoostLimit, darkThreshold - targetL) * (1 - texturePreserve);
            adjustedL = Math.min(255, Math.max(0, currentL + lBoost));
          } else if (targetL > brightThreshold) {
            const lReduce = Math.min(brightnessReduceLimit, targetL - brightThreshold) * (1 - texturePreserve);
            adjustedL = Math.min(255, Math.max(0, currentL - lReduce));
          }

          // 色彩混合
          const newA = currentA * (1 - blendStrength) + targetA * blendStrength;
          const newB = currentB * (1 - blendStrength) + targetB * blendStrength;

          finalL.ucharPtr(i, j)[0] = adjustedL;
          finalA.ucharPtr(i, j)[0] = Math.max(0, Math.min(255, newA));
          finalB.ucharPtr(i, j)[0] = Math.max(0, Math.min(255, newB));
        }
      }
    }

    // 重新合并最终的LAB通道
    const finalLabMat = new cv.Mat();
    const finalLabChannelsVec = new cv.MatVector();
    finalLabChannelsVec.push_back(finalL);
    finalLabChannelsVec.push_back(finalA);
    finalLabChannelsVec.push_back(finalB);
    cv.merge(finalLabChannelsVec, finalLabMat);
    finalLabChannelsVec.delete();

    // 4. 融合HSV饱和度增强结果
    console.log('🔗 步骤4: 融合HSV和LAB结果...');

    // 转换LAB结果到HSV进行饱和度融合
    const labToRgbTemp = new cv.Mat();
    const labToHsvTemp = new cv.Mat();
    cv.cvtColor(finalLabMat, labToRgbTemp, cv.COLOR_Lab2RGB);
    cv.cvtColor(labToRgbTemp, labToHsvTemp, cv.COLOR_RGB2HSV);

    // 融合饱和度通道
    const fusedHsvChannels = new cv.MatVector();
    cv.split(labToHsvTemp, fusedHsvChannels);
    const fusedH = fusedHsvChannels.get(0);
    const fusedS = fusedHsvChannels.get(1);
    const fusedV = fusedHsvChannels.get(2);

    const enhancedHsvChannelsForFusion = new cv.MatVector();
    cv.split(enhancedHsvMat, enhancedHsvChannelsForFusion);
    const enhancedSForFusion = enhancedHsvChannelsForFusion.get(1);

    // 融合饱和度
    const finalS = new cv.Mat();
    cv.addWeighted(enhancedSForFusion, saturationFusion, fusedS, 1 - saturationFusion, 0, finalS);

    // 重新合并HSV
    const finalHsvChannels = new cv.MatVector();
    finalHsvChannels.push_back(fusedH);
    finalHsvChannels.push_back(finalS);
    finalHsvChannels.push_back(fusedV);
    const finalHsvMat = new cv.Mat();
    cv.merge(finalHsvChannels, finalHsvMat);
    finalHsvChannels.delete();

    // 5. 转换回RGB并最终混合
    console.log('🌈 步骤5: 转换回RGB并最终混合...');
    const enhancedRgbMat = new cv.Mat();
    cv.cvtColor(finalHsvMat, enhancedRgbMat, cv.COLOR_HSV2RGB);

    // 与原始基础结果混合
    resultMat = new cv.Mat();
    cv.addWeighted(enhancedRgbMat, finalBlend, baseResult, 1 - finalBlend, 0, resultMat);

    console.log('✅ 直方图增强换色完成');

    // 清理中间资源（不包括resultMat，因为要返回）
    [targetColorMat, targetLabMat, enhancedL, enhancedA, enhancedB, blendedA, blendedB,
      enhancedS, blendedS, finalLabMat, labToRgbTemp, labToHsvTemp, finalS, finalHsvMat, enhancedRgbMat,
      enhancedSForFusion, enhancedHsvChannelsForFusion, fusedHsvChannels].forEach(mat => {
        if (mat && mat.delete) mat.delete();
      });

    return resultMat;

  } catch (error) {
    console.error('❌ 直方图增强处理错误:', error);
    // 如果发生错误，清理resultMat
    if (resultMat && resultMat.delete) resultMat.delete();
    throw error;
  } finally {
    // 清理主要资源
    [baseLabMat, originalLabMat, baseHsvMat, originalHsvMat, enhancedLabMat, enhancedHsvMat,
      labChannels, hsvChannels].forEach(mat => {
        if (mat && mat.delete) mat.delete();
      });
  }
}