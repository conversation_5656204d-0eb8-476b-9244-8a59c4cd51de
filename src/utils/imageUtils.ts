import Compressor from 'compressorjs';
import convert from 'heic-convert';
import { uploadFile } from '@/services/FileController';
import { downloadOss } from '@/services/SystemController';
import Konva from 'konva';
import { message } from 'antd';
import Stage = Konva.Stage;

const MAX_IMAGE_SIZE = 1024 * 1024 * 1;

export const compressFile = async (file, maxSize = MAX_IMAGE_SIZE, minQualityLimit = 0.5) => {
  if (file.size < maxSize) {
    return file; // 如果文件大小已经小于maxSize，直接返回原文件
  }

  console.log('当前文件大小是', (file.size / 1024 / 1024).toFixed(2), 'MB');

  const compressImage = (file, quality) => {
    return new Promise((resolve, reject) => {
      new Compressor(file, {
        quality,
        success(result) {
          resolve(result);
        },
        error(err) {
          reject(err);
        },
      });
    });
  };

  // 初始压缩质量范围，最低不低于设定的minQualityLimit
  let minQuality = Math.max(minQualityLimit, 0.1);
  let maxQuality = 1.0;
  let compressedFile = file;

  while (minQuality <= maxQuality) {
    const midQuality = (minQuality + maxQuality) / 2;

    // 如果当前质量已经低于限制，直接返回上一次的结果
    if (midQuality < minQualityLimit) {
      console.log('达到最低质量限制:', minQualityLimit);
      return compressedFile;
    }

    compressedFile = await compressImage(file, midQuality);

    if (compressedFile.size < maxSize * 0.9) {
      minQuality = midQuality + 0.05;
    } else if (compressedFile.size > maxSize) {
      maxQuality = midQuality - 0.05;
    } else {
      break;
    }
  }

  console.log(
    '压缩后的文件大小是',
    (compressedFile.size / 1024 / 1024).toFixed(2),
    'MB',
  );
  return compressedFile;
};

export const convertHeicToJpeg = async (file: File): Promise<File> => {
  console.log('convertHeicToJpeg begin:', file.type);

  if (!file.name.match(/\.(heic|heif)$/i)) {
    return file;
  }

  try {
    // 记录文件大小（MB）
    const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
    console.log(`convertHeicToJpeg.File size: ${fileSizeMB} MB`);

    // 开始记录转换耗时
    const startTime = Date.now();

    // 读取文件并转换
    const buffer = await file.arrayBuffer();
    const converted = await convert({
      buffer: new Uint8Array(buffer),
      format: 'JPEG',
      quality: 0.9,
    });

    // 计算并记录转换耗时
    const endTime = Date.now();
    const elapsed = (endTime - startTime).toFixed(2);
    console.log(`convertHeicToJpeg.Conversion time: ${elapsed} ms`);

    // 返回转换后的 JPEG 文件
    return new File([converted], file.name.replace(/\.(heic|heif)$/i, '.jpg'), {
      type: 'image/jpeg',
    });
  } catch (error) {
    console.error('HEIC conversion failed:', error);
    throw new Error('HEIC 图片转换失败');
  }
};

export const dataURLToBlob = (dataURL) => {
  const byteString = atob(dataURL.split(',')[1]);
  const mimeString = dataURL.split(',')[0].split(':')[1].split(';')[0];
  const buffer = new ArrayBuffer(byteString.length);
  const dataArray = new Uint8Array(buffer);

  for (let i = 0; i < byteString.length; i++) {
    dataArray[i] = byteString.charCodeAt(i);
  }

  return new Blob([dataArray], { type: mimeString });
};

export const loadLocalImageFile = async (src: string) => {
  const response = await fetch(src);
  if (response) {
    const blob = await response.blob();
    return new File([blob], src, { type: 'image/png' });
  }
  return null;
};

export const convertWebpToJpg = async (file) => {
  if (!['image/webp'].includes(file.type)) {
    return file;
  }

  return await convertToJpg (file);
}

export const convertPngToJpg = async (file) => {
  if (file.type !== 'image/png') {
    return file;
  }

  return await convertToJpg (file);
};

const convertToJpg = async (file) => {
  return await new Promise((resolve) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event) => {
      const img = document.createElement('img');
      // @ts-ignore
      img.src = event.target.result;

      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // 设置canvas的宽高为图片的宽高
        canvas.width = img.width;
        canvas.height = img.height;

        // 将图片绘制到canvas上
        ctx?.drawImage(img, 0, 0);

        // 将canvas的内容转为JPG格式的Blob对象
        canvas.toBlob((blob) => {
          // @ts-ignore
          const jpgFile = new File( [blob], file.name.replace(/\.[^.]+$/, '.jpg'),
            { type: 'image/jpeg', }, );
          resolve(jpgFile);
        }, 'image/jpeg');
      };
    };
  });
}

const MAX_SIZE = 1536; // 最大尺寸

export const resizeImage = (file, maxSize = MAX_SIZE) => {
  return new Promise<File>((resolve, reject) => {
    console.log('resize before', file);
    const reader = new FileReader();
    reader.readAsDataURL(file);

    reader.onload = (e) => {
      const img = new Image();
      // @ts-ignore
      img.src = e.target.result;

      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        let { width, height } = img;

        // 判断是否需要调整尺寸
        if (width > maxSize || height > maxSize) {
          if (width > height) {
            height = (height / width) * maxSize;
            width = maxSize;
          } else {
            width = (width / height) * maxSize;
            height = maxSize;
          }
        }

        // 设置 canvas 尺寸
        canvas.width = width;
        canvas.height = height;

        console.log('resize after', width, height);

        // 绘制缩放后的图片到 canvas
        ctx?.drawImage(img, 0, 0, width, height);

        // 导出处理后的图片
        canvas.toBlob((blob) => {
          if (blob) {
            const newFile = new File([blob], file.name, { type: file.type });
            resolve(newFile);
          } else {
            reject(new Error('Failed to resize image.'));
          }
        }, file.type);
      };

      img.onerror = () => {
        reject(new Error('Failed to load image.'));
      };
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file.'));
    };
  });
};

/**
 * 图片转成JPG
 * @param file
 */
export const convertToJPG = (file) => {
  // 检查文件类型是否是图像
  if (!file.type.includes('image')) {
    throw new Error('Invalid file type. Only image files are supported.');
  }
  // console.log('convert before', file)
  return new Promise<File>((resolve, reject) => {
    // 如果已经是JPG，直接返回
    if (file.type === 'image/jpeg') {
      resolve(file);
      return;
    }
    
    // 创建一个 FileReader 来读取文件
    const reader = new FileReader();
    // 读取文件内容
    reader.readAsDataURL(file);
    reader.onload = async (e) => {
      // 检查文件是否为空
      // @ts-ignore
      if (!e.target.result) {
        reject(new Error('File is empty.'));
        return;
      }

      const img = new Image();
      // @ts-ignore
      img.src = e.target.result;

      img.onload = () => {
        // 创建一个 Canvas 元素
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;

        // 获取 Canvas 的绘图上下文
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('Failed to get canvas context.'));
          return;
        }
        // 填充背景为白色
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        // 将图片绘制到 Canvas 上
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        // 导出处理后的图片为JPG格式
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const jpgFile = new File(
                [blob],
                `${file.name.split('.')[0]}.jpg`,
                { type: 'image/jpeg' },
              );
              // console.log('convert after', jpgFile)
              resolve(jpgFile);
            } else {
              reject(new Error('Failed to convert to JPG.'));
            }
          },
          'image/jpeg',
          1,
        );
      };

      img.onerror = (error) => {
        reject(error);
      };
    };
    reader.onerror = (error) => {
      reject(error);
    };
  });
};
/**
 * 对图片缩放后重新上传
 * @param imageUrl 原图 oss url
 * @param maxSize 最长边
 */
export const resizeImageWithUpload = async (
  imageUrl: string,
  maxSize = MAX_SIZE,
): Promise<string> => {
  try {
    // 下载图片
    const response = await downloadOss(imageUrl);
    if (!response.ok) {
      throw new Error('图片下载失败');
    }
    // 将 Blob 转换为 File
    const blob = await response.blob();
    const file = new File([blob], getImageName(imageUrl), {
      type: 'image/jpg',
    });
    // 调用 resizeImage 并上传
    const newFile = await resizeImage(file, maxSize);
    const url = await uploadFile(newFile);
    // 返回 URL
    return url;
  } catch (error) {
    console.error('处理图片失败:', error);
    throw error;
  }
};

export const checkAlpha = (file: File): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    if (file.type !== 'image/png') {
      resolve(false);
      return;
    }

    // 读取文件
    const reader = new FileReader();
    // 读取文件为ArrayBuffer
    reader.readAsArrayBuffer(file);

    // 读取文件完成后，检查文件头
    reader.onload = function (e: ProgressEvent<FileReader>) {
      // 检查文件是否读取成功
      if (!e.target?.result) {
        reject(new Error('Failed to read file'));
        return;
      }

      // 检查文件头是否为PNG签名
      const view = new Uint8Array(e.target.result as ArrayBuffer, 0, 29);
      // PNG签名
      const pngSignature = [137, 80, 78, 71, 13, 10, 26, 10];

      // 检查文件头是否为PNG签名
      let isPng = true;

      // 检查文件头是否为PNG签名
      for (let i = 0; i < 8; i++) {
        if (view[i] !== pngSignature[i]) {
          isPng = false;
          break;
        }
      }

      // 若文件头不是PNG签名，则返回false
      if (!isPng) {
        resolve(false);
        return;
      }

      // 检查数据块类型是否为IHDR
      const ihdr = String.fromCharCode(view[12], view[13], view[14], view[15]);
      if (ihdr !== 'IHDR') {
        resolve(false);
        return;
      }

      // 检查颜色类型
      const colorType = view[25];
      // 若颜色类型为4或6，则返回true，否则返回false
      resolve(colorType === 4 || colorType === 6);
    };

    // 若文件读取失败，则返回false
    reader.onerror = () => reject(new Error('Failed to read file'));
  });
};

/**
 * 检测画板 Stage 是否全白
 * @param stageRef
 */
export const isStageWhite = (stageRef: React.RefObject<Stage>): boolean => {
  const stage = stageRef.current;
  if (!stage) return false;

  const layers = stage.getLayers();
  if (!layers || layers.length === 0) return true;

  // 检查最后一个layer（通常包含实际内容）
  const layer = layers[layers.length - 1];

  // 获取原生 Canvas 元素
  const canvas = layer.getCanvas()._canvas;
  if (!canvas) return false;

  const ctx = canvas.getContext('2d');
  if (!ctx) return false;

  // 获取像素数据
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;

  // 检测是否有非白色且非透明的像素
  for (let i = 0; i < data.length; i += 4) {
    const r = data[i];     // Red
    const g = data[i + 1]; // Green
    const b = data[i + 2]; // Blue
    const a = data[i + 3]; // Alpha

    // 只有非白色且不透明的像素才算"有内容"
    if (a > 0 && (r !== 255 || g !== 255 || b !== 255)) {
      return false; // 找到有内容的像素
    }
  }
  return true;
};

export const getBlobByRef = (stageRef) => {
  if (isStageWhite(stageRef)) {
    message.warning('涂抹区域为空, 请清空并重新涂抹');
    return null;
  }
  const stage = stageRef.current;
  const dataURL = stage.toDataURL({ pixelRatio: 1, quality: 1 });
  return dataURLToBlob(dataURL);
};

/**
 * 获取 <Stage /> 的绘画结果
 * @param stageRef
 * @returns 返回上传后的 JPG 格式蒙版图片 URL
 */
export const getMaskUrl = async (stageRef) => {
  if (isStageWhite(stageRef)) {
    // message.warning('涂抹区域为空, 请清空并重新涂抹');
    return '';
  }
  const stage = stageRef.current;
  const dataURL = stage.toDataURL({ pixelRatio: 1, quality: 1, mimeType: 'image/jpeg' });
  const blob = dataURLToBlob(dataURL);
  const file = new File([blob], 'mask.jpg', {type: 'image/jpeg'});
  return uploadFile(file);
};
/**
 * 根据url获取文件名
 * @param url
 */
const getImageName = (url: string) => {
  if (!url) {
    console.error('Invalid URL provided:', url);
    return ''; // 返回空字符串
  }
  // 去掉问号及之后的内容
  const urlWithoutQuery = url.split('?')[0];
  const fileName = urlWithoutQuery.split('/').pop() || '';
  if (!fileName) {
    console.error('No file name found:', url);
    return '';
  }
  return fileName;
};

/**
 * 通过 lines 数据创建离屏 canvas 并获取涂抹结果
 * @param lines 线条数据数组
 * @param canvasWidth 画布宽度，默认 560
 * @param canvasHeight 画布高度，默认 560
 * @param reversal 是否反转涂层，默认 false（白底黑涂层）
 * @returns 返回上传后的 JPG 格式蒙版图片 URL
 */
export const getMaskUrlFromLines = async (
  lines: Array<{
    points: Array<{ x: number; y: number }>;
    tool: string;
    strokeWidth: number;
    color: string;
  }>,
  canvasWidth: number = 560,
  canvasHeight: number = 560,
  reversal: boolean = false
): Promise<string> => {
  // 检查 lines 是否为空或没有有效的绘制内容
  if (!lines || lines.length === 0) {
    return '';
  }

  // 过滤出有效的绘制线条（非擦除，且有点）
  const validDrawLines = lines.filter(
    line => line.tool !== 'erase' && line.points && line.points.length > 0
  );

  if (validDrawLines.length === 0) {
    return '';
  }

  try {
    // 创建离屏 canvas
    const canvas = document.createElement('canvas');
    canvas.width = canvasWidth;
    canvas.height = canvasHeight;
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Failed to get canvas context');
    }

    // 设置背景色
    const backgroundColor = reversal ? '#000000' : '#FFFFFF';
    const strokeColor = reversal ? '#FFFFFF' : '#000000';
    
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);

    // 设置绘制样式
    ctx.strokeStyle = strokeColor;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // 绘制所有线条
    lines.forEach((line) => {
      if (!line.points || line.points.length === 0) {
        return;
      }

      // 设置线条宽度
      ctx.lineWidth = line.strokeWidth;

      // 设置合成模式
      if (line.tool === 'erase') {
        ctx.globalCompositeOperation = 'destination-out';
      } else {
        ctx.globalCompositeOperation = 'source-over';
        // 设置线条颜色为蒙版颜色
        ctx.strokeStyle = strokeColor;
      }

      // 开始绘制路径
      ctx.beginPath();
      
      // 移动到第一个点
      const firstPoint = line.points[0];
      ctx.moveTo(firstPoint.x, firstPoint.y);

      // 绘制到其他点
      for (let i = 1; i < line.points.length; i++) {
        const point = line.points[i];
        ctx.lineTo(point.x, point.y);
      }

      // 描边
      ctx.stroke();
    });

    // 重置合成模式
    ctx.globalCompositeOperation = 'source-over';

    // 由于JPEG不支持透明度，需要先创建一个白色背景的新canvas
    const finalCanvas = document.createElement('canvas');
    finalCanvas.width = canvasWidth;
    finalCanvas.height = canvasHeight;
    const finalCtx = finalCanvas.getContext('2d');
    
    if (!finalCtx) {
      throw new Error('Failed to get final canvas context');
    }
    
    // 填充白色背景
    finalCtx.fillStyle = '#FFFFFF';
    finalCtx.fillRect(0, 0, canvasWidth, canvasHeight);
    
    // 将原canvas绘制到新canvas上
    finalCtx.drawImage(canvas, 0, 0);
    
    // 转换为 DataURL (改为 JPG 格式)
    const dataURL = finalCanvas.toDataURL('image/jpeg', 1);
    
    // 转换为 Blob
    const blob = dataURLToBlob(dataURL);
    
    // 创建 File 对象 (改为 JPG 格式)
    const file = new File([blob], 'mask.jpg', { type: 'image/jpeg' });
    
    // 上传文件并返回 URL
    const uploadedUrl = await uploadFile(file);
    return uploadedUrl;
    
  } catch (error) {
    console.error('创建蒙版失败:', error);
    throw error;
  }
};

/**
 * 检查 lines 数据是否包含有效的绘制内容
 * @param lines 线条数据数组
 */
export const hasValidSmudgeContent = (
  lines: Array<{
    points: Array<{ x: number; y: number }>;
    tool: string;
    strokeWidth: number;
    color: string;
  }>
): boolean => {
  if (!lines || lines.length === 0) {
    return false;
  }

  // 检查是否有有效的绘制线条（非擦除，且有点）
  const validDrawLines = lines.filter(
    line => line.tool !== 'erase' && line.points && line.points.length > 0
  );

  return validDrawLines.length > 0;
};
