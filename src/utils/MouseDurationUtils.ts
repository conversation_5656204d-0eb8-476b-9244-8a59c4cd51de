import { recordMouseHoverTrack } from '@/utils/trackingUtils';


// 全局记录进入时间
let enterTime: number | null = null;


// 设置进入时间
const setEnterTime = (nowTime: number | null) => {
  enterTime = nowTime;
};

// 鼠标进入范围 记录进入时间
export const startMouseDuration = () => {
  setEnterTime(Date.now());
};


/**
 *  鼠标离开范围 计算相隔时间
 * @param trackEventContent 埋点内容
 * @param detailInfo 埋点具体信息
 */
export const stopMouseDuration = (trackEventContent: string, detailInfo ?: object) => {
  if (enterTime !== null) {
    // 获取时间差并转换为毫秒
    const duration = Date.now() - enterTime;


    //  上报埋点信息(大于 1 秒则进行记录)
    if (duration >= 1000) {
      recordMouseHoverTrack(trackEventContent, detailInfo, duration);
    }

    // 清除进入时间
    setEnterTime(null);
  }
};