import { favorManager } from './favorManager';

// 跟踪全局加载状态
let isInitialized = false;
let isInitializing = false;
const callbacks: Array<() => void> = [];

/**
 * 确保收藏数据已加载
 * 如果数据已加载，立即返回
 * 如果数据正在加载，将回调添加到队列
 * 如果数据未加载，开始加载并将回调添加到队列
 */
export const ensureFavorsLoaded = (callback?: () => void): boolean => {
  // 如果数据已加载，立即执行回调并返回
  if (isInitialized) {
    if (callback) {
      callback();
    }
    return true;
  }

  // 如果回调存在，添加到队列
  if (callback) {
    callbacks.push(callback);
  }

  // 如果已经在加载中，不重复加载
  if (isInitializing) {
    return false;
  }

  // 开始加载
  isInitializing = true;

  // 检查 favorManager 中是否已有数据
  if (favorManager.getFavorList().length > 0) {
    isInitialized = true;
    isInitializing = false;
    executeCallbacks();
    return true;
  }

  // 检查是否正在加载
  if (favorManager.isLoading()) {
    // 等待加载完成
    const checkInterval = setInterval(() => {
      if (!favorManager.isLoading()) {
        clearInterval(checkInterval);
        isInitialized = true;
        isInitializing = false;
        executeCallbacks();
      }
    }, 100);

    // 设置超时，避免无限等待
    setTimeout(() => {
      clearInterval(checkInterval);
      isInitialized = true;
      isInitializing = false;
      executeCallbacks();
    }, 2000);

    return false;
  }

  // 加载数据
  favorManager.fetchAllFavors().then(() => {
    isInitialized = true;
    isInitializing = false;
    executeCallbacks();
  }).catch(error => {
    console.error('Failed to fetch all favors:', error);
    isInitializing = false;
    // 即使出错也标记为已初始化，避免反复尝试
    isInitialized = true;
    executeCallbacks();
  });

  return false;
};

/**
 * 重置加载状态，用于用户登录/登出等场景
 */
export const resetFavorLoader = (): void => {
  isInitialized = false;
  isInitializing = false;
  callbacks.length = 0;
};

/**
 * 执行所有回调
 */
const executeCallbacks = (): void => {
  callbacks.forEach(callback => {
    try {
      callback();
    } catch (error) {
      console.error('Error executing favor loader callback:', error);
    }
  });
  callbacks.length = 0;
}; 