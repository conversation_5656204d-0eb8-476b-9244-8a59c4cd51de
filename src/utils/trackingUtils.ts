/**  埋点工具类  */
import { getSystemInfo } from '@/utils/systemInfoUtils';
import { getTempUserUuid, getUserInfo } from '@/utils/webStorageUtils';
import { isEmpty, isEmptyObject } from '@/utils/vertifyUtils';
import { record } from '@/services/SystemController';

/**  埋点事件类型数组  */
export const eventTypeList = [
  // 进入页面事件
  {
    label: '进入页面',
    value: 'inPage',
  },
  // 离开页面事件
  {
    label: '离开页面',
    value: 'leavePage',
  },
  // 点击按钮事件
  {
    label: '点击按钮',
    value: 'clickBtn',
  },
  // 鼠标悬浮事件
  {
    label: '鼠标悬浮',
    value: 'mouseHover',
  },
];

// 埋点事件记录对象
export interface EventTrackingRecord {
  userId: string | number | null,
  tempUserUuid: string,
  ipAddress: string,
  eventType: string,
  eventContent: string,
  pageTitle: string,
  pageUrl: string,
  userRetentionTime: number,
  os: string,
  browser: string,
  requestResource: string,
  preReferrer: string,
  additionalData: string,
}

// 获取系统信息
const systemInfo = getSystemInfo();

// 埋点事件记录对象
const eventTrackingRecord: EventTrackingRecord = {
  userId: null,
  tempUserUuid: getTempUserUuid(),
  ipAddress: '',
  eventType: '',
  eventContent: '',
  pageTitle: systemInfo.pageTitle,
  pageUrl: systemInfo.currentUrl,
  userRetentionTime: 0,
  os: systemInfo.os,
  browser: systemInfo.browser,
  requestResource: systemInfo.deviceType,
  preReferrer: systemInfo.previousUrl,
  additionalData: JSON.stringify(systemInfo),
};


/**
 * 记录进入页面埋点
 * @param eventContent 事件内容描述
 * @param detailInfo 具体信息
 */
export const recordInPageTrack = (eventContent: string, detailInfo ?: any) => {
  // 获取用户信息
  const userInfo = getUserInfo();

  // 对象赋值
  eventTrackingRecord.userId = userInfo?.id ?? null;
  eventTrackingRecord.eventType = eventTypeList[0].value;
  eventTrackingRecord.eventContent = eventContent;
  eventTrackingRecord.userRetentionTime = 0;


  // 执行数据记录
  doRecordEvent(eventTrackingRecord, detailInfo);
};

/**
 * 记录离开页面埋点
 * @param eventContent 事件内容描述
 * @param detailInfo 具体信息
 */
export const recordLeavePageTrack = (eventContent: string, detailInfo ?: any) => {
  // 获取用户信息
  const userInfo = getUserInfo();

  // 对象赋值
  eventTrackingRecord.userId = userInfo?.id ?? null;
  eventTrackingRecord.eventType = eventTypeList[1].value;
  eventTrackingRecord.eventContent = eventContent;
  eventTrackingRecord.userRetentionTime = Math.floor(Date.now() - performance.timing.navigationStart);

  // 执行数据记录
  doRecordEvent(eventTrackingRecord, detailInfo);
};

/**
 * 记录鼠标点击埋点
 * @param eventContent 事件内容描述
 * @param detailInfo 具体信息
 */
export const recordClickBtnTrack = (eventContent: string, detailInfo ?: any) => {
  // 获取用户信息
  const userInfo = getUserInfo();

  // 对象赋值
  eventTrackingRecord.userId = userInfo?.id ?? null;
  eventTrackingRecord.eventType = eventTypeList[2].value;
  eventTrackingRecord.eventContent = eventContent;
  eventTrackingRecord.userRetentionTime = 0;

  // 执行数据记录
  doRecordEvent(eventTrackingRecord, detailInfo);
};

/**
 * 记录鼠标悬浮埋点
 * @param eventContent 事件内容描述
 * @param detailInfo 具体信息
 * @param userRetentionTime 用户停留时间
 */
export const recordMouseHoverTrack = (eventContent: string, detailInfo ?: any, userRetentionTime?: number) => {
  // 获取用户信息
  const userInfo = getUserInfo();

  // 对象赋值
  eventTrackingRecord.userId = userInfo?.id ?? null;
  eventTrackingRecord.eventType = eventTypeList[3].value;
  eventTrackingRecord.eventContent = eventContent;
  eventTrackingRecord.userRetentionTime = userRetentionTime ?? 0;

  // 执行数据记录
  doRecordEvent(eventTrackingRecord, detailInfo);
};

/**
 * 执行埋点事件上报
 * @param requestParams 请求体参数
 * @param detailInfo 具体信息
 */
const doRecordEvent = (requestParams: EventTrackingRecord, detailInfo ?: any) => {
  // 若其他信息不为空则为其赋值
  if (!isEmpty(detailInfo) || !isEmptyObject(detailInfo)) {
    // 额外信息赋值
    systemInfo.additionalData = detailInfo;
    // 更新请求入参中的系统参数
    requestParams.additionalData = JSON.stringify(systemInfo);
  }

  // 执行上报操作
  record(requestParams).then(res => {
  });
};

