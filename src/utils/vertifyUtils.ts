/** 校验工具类 */

/**
 * 校验字符串是否为空
 */
export const isEmpty = (str: string | any[] | null | undefined) => {
  return str === undefined || str === null || str.length === 0;
};

/**
 * 校验是否为空对象
 */
export const isEmptyObject = (obj: object) => {
  return obj && Object.keys(obj).length === 0 && obj.constructor === Object;
};

/**
 * 校验是否为数字
 */
export const isNumeric = (str: string) => {
  return !isNaN(str as any) && !isNaN(parseFloat(str));
};

/**
 * 校验是否为手机号
 */
export const isPhoneNumber = (phoneNumber: string) => {
  return /^1[3456789]\d{9}$/.test(phoneNumber);
};

/**
 * 校验是否为邮箱
 */
export const isEmail = (email: string) => {
  return /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(email);
};

/**
 * 校验是否为url
 */
export const isUrl = (url: string) => {
  return /^(http|https):\/\/[^ "]+$/.test(url);
};
