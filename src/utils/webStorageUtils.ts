/** web 端缓存工具类 */

import { getUUIDWithoutHyphen } from '@/utils/randomUtils';
import { isEmpty } from '@/utils/vertifyUtils';
import { UserVO } from '@/services/UserController';
import { TEMP_USER_UUID, USER_INFO } from '@/constants';


/**
 * 获取临时用户 uuid
 */
export const getTempUserUuid = () => {
  // 获取临时用户 uuid
  const tempUserUuid = localStorage.getItem(TEMP_USER_UUID);

  // 判断是否为空
  if (isEmpty(tempUserUuid)) {
    return updateTempUserUuid();
  }

  // 返回 uuid
  return String(tempUserUuid);
};

/**
 * 设置临时用户 uuid
 */
export const updateTempUserUuid = () => {
  // 生成 uuid
  const uuid = getUUIDWithoutHyphen();

  // 缓存uuid信息
  localStorage.setItem(TEMP_USER_UUID, uuid);

  // 返回 uuid
  return uuid;
};

/**
 * 获取用户信息
 */
export const getUserInfo = (): UserVO | null => {
  // 获取用户信息
  const userInfo = localStorage.getItem(USER_INFO);

  // 判断是否为空
  if (userInfo) {
    return JSON.parse(userInfo) as UserVO;
  }

  // 返回 null
  return null;
};