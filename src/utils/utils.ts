import { GetProp, UploadProps } from 'antd';
import { USER_INFO } from '@/constants';
import { UserVO } from '@/services/UserController';

export function deepCopy(origin: object) {
  if (!origin) {
    return {};
  }
  return JSON.parse(JSON.stringify(origin));
}

export const download = (url: string, fileName: string | null = null) => {
  const link = document.createElement('a');
  link.href = url;
  // 文件名称
  link.download = fileName ? fileName : url.substr(url.lastIndexOf('/'), url.length);
  document.body.appendChild(link);
  link.click();  // 模拟点击
  document.body.removeChild(link);  // 下载后从文档中移除
};

export const downloadJson = (data: object | string, filename: string) => {
  // 创建数据Blob
  const blob = new Blob([typeof data === 'string' ? data : JSON.stringify(data)], {
    type: 'application/json;charset=utf-8'
  });

  // 创建虚拟链接
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);

  // 设置下载属性
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);

  // 触发下载
  link.click();

  // 清理资源
  URL.revokeObjectURL(url);
  document.body.removeChild(link);
};

export const getTaskIdByImage = (imageUrl: string) => {
  if (!imageUrl) return null;
  const imageName = getImageName(imageUrl);
  if (!imageName || !imageName.includes('_')) return null;

  const taskId = imageName.split('_')[1];
  return Number(taskId);
};

export function sleep(duration) {
  return new Promise(resolve => {
    setTimeout(resolve, duration);
  });
}

export function getTutorialIdx(materialType){
  switch (materialType) {
    case 'cloth':
      return 1;
    case 'face':
      return 6;
    case 'scene':
      return 5;
    default:
      return 1;
  }
}

export function mapToPathObject(map, parentKey = '') {
  if (!map) {
    return;
  }

  const resultObj = {};
  map.forEach((value, key) => {
    // 构建当前层的键名
    const currentKey = parentKey ? `${parentKey}.${key}` : key;
    if (value instanceof Map) {
      // 如果值是Map，递归调用此函数
      Object.assign(resultObj, mapToPathObject(value, currentKey));
    } else {
      // 否则直接在结果对象上设置值
      resultObj[currentKey] = value;
    }
  });
  return resultObj;
}


export function objectToPathObject(obj: any, parentKey = ''): Record<string, any> {
  if (!obj) {
    return obj;
  }

  const resultObj: Record<string, any> = {};
  Object.entries(obj).forEach(([key, value]) => {
    const currentKey = parentKey ? `${parentKey}.${key}` : key;
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      // 递归调用自身来处理嵌套对象
      Object.assign(resultObj, objectToPathObject(value, currentKey));
    } else {
      // 直接设置值
      resultObj[currentKey] = value;
    }
  });
  return resultObj;
}

export function pathObjectToObject(pathObject: Record<string, any>): any {
  const result = {};

  Object.keys(pathObject).forEach((key) => {
    const parts = key.split('.');
    let currentPart = result;

    // 遍历键路径的每一部分，除了最后一部分
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];

      // 如果当前部分不存在，或不是对象，则创建一个新对象
      if (!currentPart[part] || typeof currentPart[part] !== 'object') {
        currentPart[part] = {};
      }

      // 移动到下一个部分的位置
      currentPart = currentPart[part];
    }

    // 设置最后一部分的值
    const lastPart = parts[parts.length - 1];
    currentPart[lastPart] = pathObject[key];
  });

  return result;
}

export type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

export const getBase64 = (file: FileType): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

export const getImageName = (url: string) => {
  if (!url) return '';
  const fileName = url.substr(url.lastIndexOf('/') + 1);
  const questionMarkIndex = fileName.indexOf('?');
  return questionMarkIndex === -1 ? fileName : fileName.substr(0, questionMarkIndex);
};

export const getImageNameWithPath = (url: string | null | undefined) => {
  if (!url) return '';
  const fileName = url.substr(url.lastIndexOf('.com/') + 5);
  const questionMarkIndex = fileName.indexOf('?');
  return questionMarkIndex === -1 ? fileName : fileName.substr(0, questionMarkIndex);
};

export const getImageNameWithTag = (url: string, tag: string) => {
  let fileName = getImageName(url);
  fileName = fileName ? fileName : 'temp.jpg';
  1;
  const index = fileName.lastIndexOf('.');
  return fileName.substring(0, index) + '_' + tag + fileName.substring(index);
};

let timeoutGradientRef = undefined;
export const timeoutGradient = (min: number, max: number, time: number, callback: any, eachTime = null) => {
  if (timeoutGradientRef) {
    clearTimeout(timeoutGradientRef);
  }

  if (min > max) {
    return;
  }

  if (callback) {
    callback.call(min);
  }

  const times = max - min + 1;
  const stepTime = eachTime ? eachTime : time / times;

  // @ts-ignore
  timeoutGradientRef = setTimeout(() => {
    // @ts-ignore
    timeoutGradient(min + 1, max, time - stepTime, callback, stepTime);
    return () => clearTimeout(timeoutGradientRef);
  }, stepTime);
};

export const maskPhoneNumber = (phoneNumber) => {
  if (!phoneNumber) {
    return phoneNumber;
  }
  if (phoneNumber.length !== 11) {
    return phoneNumber; // 只处理长度为11的号码，其他情况返回原字符串
  }
  return phoneNumber.slice(0, 3) + '****' + phoneNumber.slice(7);
};

export const getUserInfo = (): UserVO | null => {
  const userInfo = localStorage.getItem(USER_INFO);
  if (userInfo) {
    return JSON.parse(userInfo) as UserVO;
  }
  return null;
};


export function parseArray<T>(input: string): T[] {
  try {
    const array = JSON.parse(input);
    if (Array.isArray(array)) {
      return array.map(item => item as T);
    } else {
      throw new Error('Input is not a JSON array');
    }
  } catch (error) {
    console.error('Failed to parse input:', error);
    return [];
  }
}

export const getUserRoleType = () => {
  const userInfo = getUserInfo();
  if (!userInfo) {
    return '';
  }
  return userInfo.roleType;
};

export const capitalizeFirstLetter = (str) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

export function getNavUrlByUser() {
  let userInfo = getUserInfo();

  if (userInfo != null) {
    if (userInfo.roleType === 'OPERATOR' || userInfo.roleType === 'MERCHANT') {
      return '/upload';
    } else if (userInfo.roleType === 'ADMIN') {
      return '/cloth-mng';
    } else if (userInfo.roleType === 'DISTRIBUTOR') {
      return '/distributor/customers';
    }
  }

  return '/';
}

export const isNumeric = (str: string) => {
  return /^-?\d+(\.\d+)?$/i.test(str);
};

export const formatText = (text: string | null | undefined, length) => {
  if (text && text.length > length) {
    return text.substr(0, length) + '...';
  }
  return text;
};

export const isValidJsonObject = (str: string): boolean => {
  try {
    const parsed = JSON.parse(str);
    return typeof parsed === 'object' && parsed !== null && !Array.isArray(parsed);
  } catch (error) {
    return false;
  }
};

export const isValidJsonArray = (str: string): boolean => {
  try {
    const parsed = JSON.parse(str);
    return Array.isArray(parsed);
  } catch (error) {
    return false;
  }
};

export const numberOrDefault = (value: string, defaultValue) => {
  return isNumber(value) ? Number(value) : defaultValue;
}


export const isNumber = (value: string) => {
  const n = Number(value);
  return !isNaN(n) && n >= 0 && Number.isInteger(n);
};

/**
 * 安全复制文本到剪贴板
 *
 * @param text 要复制的文本
 * @param successCallback 复制成功的回调
 * @param failCallback 复制失败的回调
 * @returns Promise<boolean> 是否复制成功
 */
export const copyToClipboard = async (
  text: string,
  successCallback?: () => void,
  failCallback?: (error: any) => void
): Promise<boolean> => {
  // 检查剪贴板API是否可用
  if (navigator && typeof navigator.clipboard !== 'undefined' && navigator.clipboard && navigator.clipboard.writeText) {
    try {
      // 使用现代剪贴板API
      await navigator.clipboard.writeText(text);
      successCallback?.();
      return true;
    } catch (error) {
      console.warn('剪贴板API调用失败，尝试使用备选方法', error);
      // 继续尝试备选方法
    }
  }

  // 备选方法1: execCommand (已被弃用但兼容性较好)
  try {
    const textArea = document.createElement('textarea');
    textArea.value = text;

    // 防止滚动到底部
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';
    textArea.style.opacity = '0';

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    const successful = document.execCommand('copy');
    document.body.removeChild(textArea);

    if (successful) {
      successCallback?.();
      return true;
    }
  } catch (err) {
    console.warn('execCommand方法复制失败', err);
  }

  // 如果所有方法都失败
  failCallback?.(new Error('当前环境不支持自动复制'));
  return false;
};

export const isProdEnv = ():boolean => {
  return process.env.NODE_ENV === 'production' || process.env.UMI_ENV === 'prod';
}

export function formatDateToCompactString(dateString: string): string {
  const date = new Date(dateString);

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.log('Invalid date string', dateString);
    return '';
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要+1
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}${month}${day}`;
}

export const formatTimeDiffSecond = (targetTime, sourceTime = null) => {
  if (!targetTime){
    return 0
  }

  let source : null | Date = null;
  if (!sourceTime){
    source = new Date()
  } else if (typeof sourceTime === 'string'){
    source = new Date(sourceTime);
  } else {
    source = sourceTime;
  }

  let target : null | Date = null;
  if (typeof targetTime === 'string'){
    target = new Date(targetTime);
  } else {
    target = targetTime;
  }

  //@ts-ignore
  return Math.floor((source.getTime() - target.getTime()) / 1000);
}

export const formatTimeDiffMinute = (targetTime, sourceTime = null) => {
  let second = formatTimeDiffSecond(targetTime, sourceTime);
  return Math.floor(second / 60) + '分' + second % 60 + '秒';
}