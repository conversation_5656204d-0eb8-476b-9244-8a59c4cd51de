import { PromptDictVO } from '@/services/PromptDictController';

export const getTypeItemsByTagsFromData = (tags:string[], typeTags:Record<string, Array<PromptDictVO>> | undefined) => {
  if (!tags || !typeTags) return [];
  tags.sort((a,b) => {return a > b ? 1 : -1})
  const key = tags.join(',')
  let typeTag = typeTags ? typeTags[key] : null;
  if(!typeTag){
    return [];
  }
  return typeTag.map(item => {return {value:item.prompt, label:item.word}});
}