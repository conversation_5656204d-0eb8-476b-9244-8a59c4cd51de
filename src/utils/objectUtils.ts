export const flattenObject = (obj, prefix = '', result = []): Array<{ key: string, value: any }> => {
  console.log('flattenObject,flattenObject', obj);
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = prefix === '' ? key : `${prefix}.${key}`;
      const value = obj[key];

      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        // 如果是普通对象，则递归调用
        flattenObject(value, newKey, result);
      } else {
        // 否则添加到结果集中
        // @ts-ignore
        result.push({ key: newKey, value });
      }
    }
  }
  return result;
};