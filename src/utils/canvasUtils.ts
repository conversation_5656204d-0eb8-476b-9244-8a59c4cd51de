import { fetchOssBlobUrl } from './ossUtils';

/**
 * 创建一个长+宽+颜色的空canvas
 * @param width
 * @param height
 * @param color
 */
export const createBlankCanvas = (width: number, height: number, color = '#FFFFFF') => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  canvas.width = width;
  canvas.height = height;
  // @ts-ignore
  ctx.fillStyle = color;
  // @ts-ignore
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  return canvas;
};

/**
 * 创建一个长+宽+内容为image的canvas
 * @param width
 * @param height
 * @param image
 */
export const createImageCanvas = (width: number, height: number, image) => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  canvas.width = width;
  canvas.height = height;

  const upscale = Math.min(width / image.width, height / image.height);

  // @ts-ignore
  ctx.scale(upscale, upscale);

  // @ts-ignore
  ctx.drawImage(image, 0, 0);

  return { canvas, ctx };
};

/**
 * 创建一个长+宽+内容为image的canvas，并加载
 * @param image
 * @param width
 * @param height
 * @param callback
 */
export const loadImageCanvas = (image: any, width: number, height: number, callback: (arg0: HTMLImageElement) => void) => {
  const { canvas } = createImageCanvas(width, height, image);
  let dataURL = canvas.toDataURL('image/png');
  const originImage = new Image();
  originImage.src = dataURL;

  originImage.onload = () => {
    if (callback) {
      callback(originImage);
    }
  };
};

/**
 * 计算mask区域信息，包括起始点x,y和高宽
 *
 * @param data
 * @param width
 * @param height
 */
export const calcMaskRegions = (data, width, height) => {
  const regions = [];
  let minX = width, minY = height, maxX = 0, maxY = 0;
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const index = (y * width + x) * 4;
      if (data[index] === 0 && data[index + 1] === 0 && data[index + 2] === 0) {
        minX = Math.min(minX, x);
        minY = Math.min(minY, y);
        maxX = Math.max(maxX, x);
        maxY = Math.max(maxY, y);
      }
    }
  }
  if (minX < maxX && minY < maxY) {
    // @ts-ignore
    regions.push({ x: minX, y: minY, width: maxX - minX, height: maxY - minY });
  }
  return regions;
};

/**
 * 计算两个mask区域重叠面积
 *
 * @param region1
 * @param region2
 */
export const calcOverlap = (region1, region2) => {
  const xOverlap = Math.max(0, Math.min(region1.x + region1.width, region2.x + region2.width) - Math.max(region1.x, region2.x));
  const yOverlap = Math.max(0, Math.min(region1.y + region1.height, region2.y + region2.height) - Math.max(region1.y, region2.y));
  const overlapArea = xOverlap * yOverlap;
  const region1Area = region1.width * region1.height;
  const region2Area = region2.width * region2.height;
  return overlapArea / Math.min(region1Area, region2Area);
};

/**
 * 四维合并图片和mask
 * @param imageUrl
 * @param maskUrl
 * @param callback
 */
export const combine4DMask = (imageUrl: string, maskUrl: string, callback: (arg0: string) => void) => {
  const maskImage = new Image();
  maskImage.src = maskUrl;

  const originImage = new Image();
  originImage.src = imageUrl;

  Promise.all([
    new Promise(resolve => maskImage.onload = resolve),
    new Promise(resolve => originImage.onload = resolve),
  ]).then(() => {
    const width = maskImage.width;
    const height = maskImage.height;
    const { canvas: imageCanvas, ctx: imageCtx } = createImageCanvas(width, height, originImage);
    const { ctx: maskCtx } = createImageCanvas(width, height, maskImage);
    if (!imageCtx || !maskCtx) return;

    const imageData = imageCtx.getImageData(0, 0, width, height).data;
    const maskData = maskCtx.getImageData(0, 0, width, height).data;

    const resultImageData = imageCtx.createImageData(width, height);
    const resultData = resultImageData.data;
    for (let i = 0; i < imageData.length; i += 4) {
      resultData[i] = imageData[i]; // Red
      resultData[i + 1] = imageData[i + 1]; // Green
      resultData[i + 2] = imageData[i + 2]; // Blue
      // resultData[i + 3] = imageData[i + 3]; // Alpha
      // resultData[i + 3] = 0; // Alpha
      resultData[i + 3] = maskData[i] === 0 && maskData[i + 1] === 0 && maskData[i + 2] === 0 ? 0 : imageData[i + 3]; // Alpha
    }

    // Put the result image data back to the canvas
    imageCtx.putImageData(resultImageData, 0, 0);

    // @ts-ignore
    let dataURL = imageCanvas.toDataURL('image/png');
    if (callback) {
      callback(dataURL);
    }
  });
};

/**
 * 合并图片和mask，mask的RGB为000时展示image内容
 * @param image
 * @param maskImage
 * @param width
 * @param height
 */
export const combineImage = (image: any, maskImage: any, width, height) => {
  const { canvas, ctx } = createImageCanvas(width, height, image);
  const { canvas: maskCanvas, ctx: maskCtx } = createImageCanvas(width, height, maskImage);

  // @ts-ignore
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;

  // @ts-ignore
  const maskImageData = maskCtx.getImageData(0, 0, maskCanvas.width, maskCanvas.height);
  const maskData = maskImageData.data;

  // @ts-ignore
  const resultImageData = ctx.createImageData(canvas.width, canvas.height);
  const resultData = resultImageData.data;

  for (let i = 0; i < data.length; i += 4) {
    if (maskData[i] === 0 && maskData[i + 1] === 0 && maskData[i + 2] === 0) {
      resultData[i] = data[i]; // Red
      resultData[i + 1] = data[i + 1]; // Green
      resultData[i + 2] = data[i + 2]; // Blue
      resultData[i + 3] = data[i + 3]; // Alpha
    } else {
      resultData[i] = 0; // Red
      resultData[i + 1] = 0; // Green
      resultData[i + 2] = 0; // Blue
      resultData[i + 3] = 0; // Alpha
    }
  }

  // @ts-ignore
  ctx.putImageData(resultImageData, 0, 0);

  // Get the data URL from the temporary canvas
  return canvas.toDataURL('image/png');
};

// 图片转线条（白底黑涂层）
export const imageToLines = (
  imageUrl: string, 
  options: {
    strokeWidth?: number;
    sampleRate?: number; // 采样率，值越大，精度越低，但性能越好
    lineColor?: string;
    threshold?: number; // RGB阈值，用于判断像素颜色
    isBlackBackground?: boolean; // 是否为黑底，true表示黑底白涂层，false表示白底黑涂层
  } = {}
): Promise<Array<{
  points: Array<{x: number, y: number}>,
  tool: string,
  strokeWidth: number,
  color: string
}>> => {
  const {
    strokeWidth = 5,
    sampleRate = 4, // 默认每4个像素采样一次
    lineColor = 'rgba(97, 179, 255, 1)',
    threshold = 50, // 默认RGB阈值
    isBlackBackground = false // 默认为白底黑涂层
  } = options;

  return new Promise(async (resolve, reject) => {
    // 检查图片URL是否有效
    if (!imageUrl) {
      reject(new Error('图片URL为空'));
      return;
    }

    // 使用fetchOssBlobUrl获取图片，防止跨域问题
    let localImageUrl: string | undefined;
    try {
      localImageUrl = await fetchOssBlobUrl(imageUrl);
      if (!localImageUrl) {
        reject(new Error('无法获取图片'));
        return;
      }
    } catch (e: unknown) {
      const error = e as Error;
      reject(new Error('获取图片时出错: ' + (error.message || '未知错误')));
      return;
    }

    const img = new Image();
    img.crossOrigin = 'Anonymous'; // 保留跨域设置，双重保障

    // 处理加载超时
    const timeoutId = setTimeout(() => {
      reject(new Error('图片加载超时'));
    }, 30000); // 30秒超时

    img.onload = () => {
      clearTimeout(timeoutId);
      
      try {
        // 创建一个canvas元素来处理图片
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          reject(new Error('无法创建Canvas上下文'));
          return;
        }

        canvas.width = img.width;
        canvas.height = img.height;
        
        // 检查图片是否有实际尺寸
        if (img.width === 0 || img.height === 0) {
          reject(new Error('图片尺寸为0，无法处理'));
          return;
        }
        
        // 绘制图片到canvas
        ctx.drawImage(img, 0, 0);
        
        // 获取图片数据
        let imageData;
        try {
          imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        } catch (e: unknown) {
          const error = e as Error;
          reject(new Error('无法获取图片数据，可能是跨域问题: ' + (error.message || '未知错误')));
          return;
        }
        
        const data = imageData.data;
        
        // 存储所有的线条
        const lines: Array<{
          points: Array<{x: number, y: number}>,
          tool: string,
          strokeWidth: number,
          color: string
        }> = [];

        // 判断像素是否为目标颜色的函数
        const isTargetColor = (r: number, g: number, b: number): boolean => {
          if (isBlackBackground) {
            // 黑底白涂层，判断是否为白色（RGB值都大于255-阈值）
            return r > (255 - threshold) && g > (255 - threshold) && b > (255 - threshold);
          } else {
            // 白底黑涂层，判断是否为黑色（RGB值都小于阈值）
            return r < threshold && g < threshold && b < threshold;
          }
        };

        // 水平方向扫描
        for (let y = 0; y < canvas.height; y += sampleRate) {
          let currentLine: Array<{x: number, y: number}> = [];
          let lineStarted = false;
          
          for (let x = 0; x < canvas.width; x += sampleRate) {
            const index = (y * canvas.width + x) * 4;
            // 获取RGB值
            const r = data[index];
            const g = data[index + 1];
            const b = data[index + 2];
            
            // 判断像素是否为目标颜色
            if (isTargetColor(r, g, b)) {
              if (!lineStarted) {
                lineStarted = true;
                currentLine = []; // 开始新线条
              }
              
              // 添加点到当前线条
              currentLine.push({ x, y });
            } else if (lineStarted) {
              // 如果当前像素不是目标颜色但之前有目标颜色像素，结束当前线条
              lineStarted = false;
              
              if (currentLine.length > 1) {
                // 只有当线条有至少两个点时才添加到lines
                lines.push({
                  points: currentLine,
                  tool: 'draw',
                  strokeWidth,
                  color: lineColor
                });
              }
            }
          }
          
          // 处理行结束但线条未结束的情况
          if (lineStarted && currentLine.length > 1) {
            lines.push({
              points: currentLine,
              tool: 'draw',
              strokeWidth,
              color: lineColor
            });
          }
        }
        
        // 垂直方向扫描
        for (let x = 0; x < canvas.width; x += sampleRate) {
          let currentLine: Array<{x: number, y: number}> = [];
          let lineStarted = false;
          
          for (let y = 0; y < canvas.height; y += sampleRate) {
            const index = (y * canvas.width + x) * 4;
            // 获取RGB值
            const r = data[index];
            const g = data[index + 1];
            const b = data[index + 2];
            
            // 判断像素是否为目标颜色
            if (isTargetColor(r, g, b)) {
              if (!lineStarted) {
                lineStarted = true;
                currentLine = []; // 开始新线条
              }
              
              currentLine.push({ x, y });
            } else if (lineStarted) {
              lineStarted = false;
              
              if (currentLine.length > 1) {
                lines.push({
                  points: currentLine,
                  tool: 'draw',
                  strokeWidth,
                  color: lineColor
                });
              }
            }
          }
          
          if (lineStarted && currentLine.length > 1) {
            lines.push({
              points: currentLine,
              tool: 'draw',
              strokeWidth,
              color: lineColor
            });
          }
        }
        
        resolve(lines);
      } catch (error: unknown) {
        const err = error as Error;
        reject(new Error('处理图片时出错: ' + (err.message || '未知错误')));
      }
    };
    
    img.onerror = (error) => {  
      clearTimeout(timeoutId);
      console.error('图片加载失败:', error, '图片URL:', localImageUrl.substring(0, 50) + '...');
      reject(new Error('图片加载失败，请检查图片URL或网络连接'));
    };
    
    // 设置图片源为通过fetchOssBlobUrl获取的本地URL
    try {
      img.src = localImageUrl;
    } catch (e: unknown) {
      const error = e as Error;
      reject(new Error('设置图片源时出错: ' + (error.message || '未知错误')));
    }
  });
};

/**
 * 获取鼠标在 Stage 中的相对坐标
 * @param {Object} e - Konva 事件对象
 * @returns {Object} 包含 x 和 y 的坐标对象
 */
export const getStagePointerPosition = (e) => {
  const stage = e.target.getStage();
  if (!stage) return null;
  
  // Stage 中的位置就是绝对位置
  return stage.getPointerPosition();
};

/**
 * 获取鼠标在 Layer 中的相对坐标
 * @param {Object} e - Konva 事件对象
 * @returns {Object} 包含 x 和 y 的坐标对象
 */
export const getLayerPointerPosition = (e) => {
  const stage = e.target.getStage();
  if (!stage) return null;
  
  // 找到事件发生的层
  const layer = e.target.getLayer();
  if (!layer) return null;
  
  // 使用 layer 的 getRelativePointerPosition 方法
  return layer.getRelativePointerPosition();
  
  // 或者手动计算：
  // const pos = stage.getPointerPosition();
  // const transform = layer.getAbsoluteTransform().copy().invert();
  // return transform.point(pos);
};

/**
 * 获取鼠标在 Group 中的相对坐标
 * @param {Object} e - Konva 事件对象
 * @param {Konva.Group} [group] - 可选的指定 Group，如果不提供则使用事件目标的父 Group
 * @returns {Object} 包含 x 和 y 的坐标对象
 */
export const getGroupPointerPosition = (e, group) => {
  const stage = e.target.getStage();
  if (!stage) return null;
  
  // 确定要使用的 Group
  const targetGroup = group || e.target.getParent();
  if (!targetGroup) return null;
  
  // 使用 group 的 getRelativePointerPosition 方法
  return targetGroup.getRelativePointerPosition();
  
  // 或者手动计算：
  // const pos = stage.getPointerPosition();
  // const transform = targetGroup.getAbsoluteTransform().copy().invert();
  // return transform.point(pos);
};

/**
 * 检测 canvas 是否有可显示的内容
 * @param stageRef - Konva Stage 的 useRef
 * @returns boolean - true 表示有内容，false 表示无内容
 */
export const hasCanvasContent = (stageRef: React.RefObject<any>): boolean => {
  // 1. 检查 ref 是否存在
  if (!stageRef || !stageRef.current) {
    return false;
  }

  try {
    const stage = stageRef.current;
    
    // 2. 检查 stage 是否有 layers
    const layers = stage.getLayers();
    if (!layers || layers.length === 0) {
      return false;
    }

    // 3. 遍历所有 layers 检查是否有内容
    for (let i = 0; i < layers.length; i++) {
      const layer = layers[i];
      if (!layer) continue;

      // 检查 layer 是否有子元素
      const children = layer.getChildren();
      if (children && children.length > 0) {
        // 检查是否有可见的子元素
        for (let j = 0; j < children.length; j++) {
          const child = children[j];
          if (child && child.isVisible() && child.opacity() > 0) {
            // 如果有可见且不透明的元素，进行像素级检查
            return hasVisiblePixels(layer);
          }
        }
      }
    }

    return false;
  } catch (error) {
    console.error('检测 canvas 内容时出错:', error);
    return false;
  }
};

/**
 * 检查 layer 是否有可见的像素
 * @param layer - Konva Layer
 * @returns boolean - true 表示有可见像素，false 表示无可见像素
 */
const hasVisiblePixels = (layer: any): boolean => {
  try {
    // 获取 layer 的 canvas
    const canvas = layer.getCanvas()._canvas;
    if (!canvas) {
      return false;
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      return false;
    }

    const width = canvas.width;
    const height = canvas.height;

    // 如果 canvas 尺寸为 0，则无内容
    if (width === 0 || height === 0) {
      return false;
    }

    // 获取像素数据
    const imageData = ctx.getImageData(0, 0, width, height);
    const data = imageData.data;

    // 采样检查策略：不需要检查每个像素，采样检查即可提高性能
    const sampleRate = Math.max(1, Math.floor(Math.sqrt(width * height) / 100)); // 动态采样率
    
    for (let y = 0; y < height; y += sampleRate) {
      for (let x = 0; x < width; x += sampleRate) {
        const index = (y * width + x) * 4;
        
        if (index + 3 < data.length) {
          const r = data[index];     // Red
          const g = data[index + 1]; // Green
          const b = data[index + 2]; // Blue
          const a = data[index + 3]; // Alpha

          // 检查是否有非白色且不透明的像素
          // 白色是 RGB(255, 255, 255)，如果不是白色且有透明度，则表示有内容
          if (a > 0 && (r !== 255 || g !== 255 || b !== 255)) {
            return true;
          }
        }
      }
    }

    return false;
  } catch (error) {
    console.error('检查像素时出错:', error);
    return false;
  }
};