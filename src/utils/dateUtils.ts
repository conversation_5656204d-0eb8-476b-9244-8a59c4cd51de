import moment from 'moment';

const pad = (num) => (num < 10 ? '0' + num : num);

export const formatFullDate = (date: Date) => {
  let month = date.getMonth() + 1;
  let day = date.getDate();
  return `${date.getFullYear()}-${pad(month)}-${pad(day)} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
};

export const formatYMD = (date: Date) => {
  let month = date.getMonth() + 1;
  let day = date.getDate();
  return `${date.getFullYear()}-${pad(month)}-${pad(day)}`;
};

export const formatYMDShort = (date: Date) => {
  let month = date.getMonth() + 1;
  let day = date.getDate();
  return `${date.getFullYear()}-${pad(month)}-${pad(day)}`;
};

export const formatFullTime = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需+1 <button class="citation-flag" data-index="9">
  const day = String(date.getDate()).padStart(2, '0');

  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

export const convertDateToMoment = (date: Date) => {
  return moment(date);
};

export const getNowMoment = () => {
  return convertDateToMoment(new Date());
};

export const getLastWeekMoment = () => {
  const now = new Date();
  const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  return convertDateToMoment(lastWeek);
};

export const calculateTimeDifference = (createTime: string) => {

  // 解析目标时间
  const targetTime = new Date(createTime);

  // 在当前时间的基础上加上24小时
  targetTime.setTime(targetTime.getTime() + 24 * 60 * 60 * 1000);

  // 获取当前时间
  const currentTime = new Date();

  // 计算时间差（毫秒）
  const timeDiff = targetTime.getTime() - currentTime.getTime();

  // 将毫秒转换为小时，四舍五入向下取整
  const hoursDiff = Math.floor(timeDiff / (1000 * 60 * 60));

  // 返回时间差，保证最少为 1 小时
  return Math.max(hoursDiff, 1);
};