/**
 * 获取数组长度
 * @param arr 数组
 */
export const getArrayList = (arr: any) => {
  // 如果数组为空，则返回空数组
  if (arr === null || arr === undefined) {
    return 0;
  }

  // 如果数组为字符串，则先转换为数组
  if (typeof arr === 'string') {
    arr = strArrayToArray(arr);
  }

  if (arr === undefined) {
    return 0;
  }

  // 返回数组长度
  return arr.length;
};


/**
 * 字符串类型数组转换为数组方法
 * @param arrString 字符串数组
 */
export const strArrayToArray = (arrString: string | undefined | null) => {
  // 如果字符串为空，则返回空数组
  if (arrString === null || arrString === undefined) {
    return [];
  }
  try {
    // 解析字符串为数组
    return JSON.parse(arrString);
  } catch (e) {
    return [];
  }
};


/**
 * 获取数组中指定元素的索引
 * @param arr 数组
 * @param item 索引
 */
export const getIndexOfArray = (arr: any[], item: any) => {
  return arr.indexOf(item);
};

/**
 * 将数组按固定长度拆分成数组列表
 * @param arr 原始数组
 * @param size 固定大小
 */
export function chunkArray<T>(arr: T[], size: number): T[][] {
  const result: T[][] = [];
  for (let i = 0; i < arr.length; i += size) {
    result.push(arr.slice(i, i + size));
  }
  return result;
}