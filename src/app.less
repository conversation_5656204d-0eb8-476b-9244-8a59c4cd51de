@navigation-height: 56px;
@tab-bar-height: 72px;
@menu-width: 105px;
@flow-height: 144px;
@scroll-width: 17px;


// Toolkit 相关变量定义
@toolkit-work-block-width: 644px;
@toolkit-table-height: 74px;
@toolkit-menu-width: 100px;
@toolkit-output-height: calc(100vh - @navigation-height - @toolkit-table-height);
@toolkit-output-height-fix: calc(100vh - @navigation-height);
@toolkit-output-width: calc(100vw - @toolkit-work-block-width - @toolkit-menu-width);
@toolkit-footer-height: 60px;

@font-face {
  font-family: "Alibaba Sans";
  src: url("../public/font/AlibabaSans-Medium.otf");
}

@font-face {
  font-family: "DingTalk JinBuTi";
  src: url("../public/font/DingTalk-JinBuTi.otf");
}

@font-face {
  font-family: "MiSans";
  src: url("../public/font/MiSans-Medium.otf");
}

@font-face {
  font-family: "MiSans-Bold";
  src: url("../public/font/MiSans-Bold.otf");
}

@font-face {
  font-family: "MiSans-Semibold";
  src: url("../public/font/MiSans-Semibold.otf");
}
@font-face {
  font-family: "PingFang SC";
  src: url("../public/font/PingFangSC-Regular.otf");
}

@font-face {
  font-family: "PingFang SC Medium";
  src: url("../public/font/PingFangSC-Medium.otf");
}

@font-face {
  font-family: "PingFang SC Light";
  src: url("../public/font/PingFangSC-Light.otf");
}

@font-face {
  font-family: "PingFang SC Semibold";
  src: url("../public/font/PingFangSC-Semibold.otf");
}

@font-face {
  font-family: "PingFang SC Thin";
  src: url("../public/font/PingFangSC-Thin.otf");
}

@font-face {
  font-family: "PingFang SC Ultralight";
  src: url("../public/font/PingFangSC-Ultralight.otf");
}

//清除框架自带的所有padding
.ant-pro-page-container-children-container, .ant-layout-content, .ant-pro-layout, .ant-pro-layout-content, .ant-pro-page-container-children-container-no-header {
  padding: 0 !important;
}

.ant-layout-sider {
  background: #FFFFFF !important;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.font-pf {
  font-family: "PingFang SC", serif;
  font-variation-settings: "opsz" auto;
}

.font-pf-medium {
  font-family: "PingFang SC Medium", serif;
  font-variation-settings: "opsz" auto;
}

.font-dingTalk-jbt {
  font-family: DingTalk JinBuTi, serif;
}

.font-mi {
  font-family: MiSans, serif;
}

.font-mi-bold {
  font-family: MiSans-Bold, serif;
}

.font-mi-semi-bold {
  font-family: MiSans-Semibold, serif;
}

.text10 {
  font-size: 10px;
  line-height: normal;
  letter-spacing: 0em;
}

.text12 {
  font-size: 12px;
  line-height: normal;
  letter-spacing: 0em;
}

.text14 {
  font-size: 14px;
  line-height: normal;
  letter-spacing: 0em;
}

.text16 {
  font-size: 16px;
  line-height: normal;
  letter-spacing: 0em;
}

.text18 {
  font-size: 18px;
  line-height: normal;
  letter-spacing: 0em;
}

.text20 {
  font-size: 20px;
  line-height: normal;
  letter-spacing: 0em;
}

.text24 {
  font-size: 24px;
  line-height: normal;
  letter-spacing: 0em;
}

.text32 {
  font-size: 32px;
  line-height: normal;
  letter-spacing: 0;
}

.text38 {
  font-size: 38px;
  line-height: normal;
  letter-spacing: 0;
}

.text40 {
  font-size: 40px;
  line-height: normal;
  letter-spacing: 0;
}

.text-center {
  text-align: center;
}

.color-n {
  color: #1A1B1D;
}

.color-n6 {
  color: #969799;
}

.color-3d {
  color: #3D3D3D;
}

.color-36 {
  color: #366EF4;
}

.color-1a {
  color: #1A1B1D;
}

.color-a0 {
  color: #A0A0A0;
}

.color-1e {
  color: #1E65FF;
}

.color-e1 {
  color: #E1E3EB;
}

.color-f5 {
  color: #F5F6F9;
}

.color-26 {
  color: #262626;
}

.color-2d {
  color: #2D7DFF;
}

.color-96 {
  color: #969799;
}

.color-9e {
  color: #9E9FA8;
}

.color-24 {
  color: #2466FA;
}

.color-70 {
  color: #707070;
}

.color-72 {
  color: #727375;
}

.color-74 {
  color: #747F96;
}

.color-w {
  color: #FFFFFF;
}

.color-b {
  color: #000000;
}

.color-ff {
  color: #FF7979;
}

.color-w0-6 {
  color: rgba(255, 255, 255, 0.6);
}

.color-b-4 {
  color: rgba(0, 0, 0, 0.4);
}

.color-f6 {
  color: #F6B553;
}

.color-brand {
  color: #0052D9;
}

.color-error {
  color: #FC343F;
}

.weight {
  font-weight: 500;
}

.normal {
  font-weight: 400;
}

.margin-16 {
  margin: 16px;
}

.margin-top-24 {
  margin-top: 24px;
}

.margin-top-28 {
  margin-top: 28px;
}

.margin-top-16 {
  margin-top: 16px;
}

.margin-top-12 {
  margin-top: 12px;
}

.margin-top-8 {
  margin-top: 8px;
}

.margin-top-4 {
  margin-top: 4px;
}

.margin-bottom-24 {
  margin-bottom: 24px;
}

.margin-bottom-16 {
  margin-bottom: 16px;
}

.margin-bottom-12 {
  margin-bottom: 12px;
}

.margin-bottom-8 {
  margin-bottom: 8px;
}

.margin-bottom-4 {
  margin-bottom: 4px;
}

.margin-left-4 {
  margin-left: 4px;
}

.margin-left-8 {
  margin-left: 8px;
}

.margin-left-12 {
  margin-left: 12px;
}

.margin-left-16 {
  margin-left: 16px;
}

.margin-left-24 {
  margin-left: 24px;
}

.margin-right-8 {
  margin-right: 8px;
}

.margin-right-12 {
  margin-right: 12px;
}

.margin-right-16 {
  margin-right: 16px;
}

.border-selected {
  border: 2px solid #2D7DFF;
}

.pointer {
  cursor: pointer;
}

/* 动画关键帧定义 */
/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 元素项淡入动画 */
@keyframes fadeInItem {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 从右侧滑入 */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 从左侧滑入 */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 缩放淡入 */
@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 弹跳动画 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

/* 创建通用动画类 */
.ani-fade-in {
  animation: fadeIn 0.3s ease-in-out forwards;
}

.ani-fade-in-item {
  animation: fadeInItem 0.3s ease-in-out forwards;
}

.ani-slide-in-right {
  animation: slideInRight 0.3s ease-in-out forwards;
}

.ani-slide-in-left {
  animation: slideInLeft 0.3s ease-in-out forwards;
}

.ani-zoom-in {
  animation: zoomIn 0.3s ease-in-out forwards;
}

.ani-bounce {
  animation: bounce 1s ease-in-out;
}