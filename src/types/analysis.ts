// BadCase 数据类型
export interface BadCaseItem {
  totalCount: number;
  count: number;
  ratio: number;
}

export interface BadCaseData {
  id: number;
  experimentalGroupMap: Record<string, BadCaseItem>;
  controlGroupMap: Record<string, BadCaseItem>;
  totalMap: Record<string, BadCaseItem>;
}

// 相似度数据类型
export interface SimilarityItem {
  totalCount: number;
  goodCount: number;
  badCount: number;
  notOperationCount: number;
}

export interface SimilarityData {
  id: number;
  experimentalGroupMap: Record<string, SimilarityItem>;
  controlGroupMap: Record<string, SimilarityItem>;
  totalMap: Record<string, SimilarityItem>;
}

// API 响应数据类型
export interface BadCaseResponse {
  resultAnalysisRatioVO: BadCaseData;
  childAnalysisRatioVOList: BadCaseData[];
}

// 相似度API响应数据类型
export interface SimilarityResponse {
  resultAnalysisRatioVO: SimilarityData;
  childAnalysisRatioMap: Record<string, SimilarityData>;
}

// 分析数据类型
export interface AnalysisData {
  id?: number;
  experimentalGroupMap: Record<string, BadCaseItem>;
  controlGroupMap: Record<string, BadCaseItem>;
  totalMap?: Record<string, BadCaseItem>;
} 