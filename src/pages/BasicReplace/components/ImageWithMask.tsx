import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { imageToLines } from '@/utils/canvasUtils';
import { Stage, Layer, Line, Image as KonvaImage } from 'react-konva';
import useImage from 'use-image';

interface ImageWithMaskProps {
  image: string; // 原始图片URL
  maskImage?: string; // 蒙层图片URL
  hasError?: boolean; // 是否有错误状态
  errorOverlayStyle?: React.CSSProperties; // 错误状态覆盖样式
  maskOpacity?: number; // 蒙层透明度，0-1之间的值
}

const ImageWithMask: React.FC<ImageWithMaskProps> = React.memo(({
  image,
  maskImage,
  hasError = false,
  errorOverlayStyle,
  maskOpacity = 0.1,
}) => {
  const [lines, setLines] = useState<any[]>([]);
  const [imageObj] = useImage(image);
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
  const [stageSize, setStageSize] = useState({ width: 0, height: 0 });

  // 使用useMemo缓存容器样式
  const containerStyle = useMemo<React.CSSProperties>(() => ({
    position: 'relative',
    height: '100%',
    width: '100%',
    borderRadius: '8px',
    overflow: 'hidden',
    backgroundColor: '#f6f7fa',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
  }), []);

  // 使用useMemo缓存图片容器样式
  const imageContainerStyle = useMemo<React.CSSProperties>(() => ({
    position: 'relative',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    width: '100%',
    maxHeight: '90%',
    maxWidth: '90%',
  }), []);

  // 使用useCallback处理图片尺寸计算
  const calculateSizes = useCallback((img: HTMLImageElement) => {
    const containerHeight = 226;
    const scale = containerHeight / img.height;
    const scaledWidth = img.width * scale;
    
    return {
      imageSize: {
        width: img.width,
        height: img.height
      },
      stageSize: {
        width: scaledWidth,
        height: containerHeight
      }
    };
  }, []);

  // 使用useCallback处理蒙层处理
  const processMaskImage = useCallback(async (
    maskImageUrl: string,
    newImageSize: { width: number; height: number },
    newStageSize: { width: number; height: number }
  ) => {
    const lineColor = `rgba(97, 179, 255, ${maskOpacity})`;
    
    try {
      const result = await imageToLines(maskImageUrl, {
        strokeWidth: 2,
        sampleRate: 4,
        threshold: 40,
        lineColor: lineColor,
        isBlackBackground: true
      });

      const scaleX = newImageSize.width ? (newStageSize.width / newImageSize.width) : 1;
      const scaleY = newImageSize.height ? (newStageSize.height / newImageSize.height) : 1;
      
      const processedLines = result.map(line => ({
        ...line,
        points: line.points.map(point => ({
          x: Number.isFinite(point.x * scaleX) ? point.x * scaleX : point.x,
          y: Number.isFinite(point.y * scaleY) ? point.y * scaleY : point.y
        }))
      }));
      
      return processedLines.filter(line => 
        line.points.every(point => 
          Number.isFinite(point.x) && Number.isFinite(point.y)
        )
      );
    } catch (error) {
      console.error('处理蒙层图片时出错:', error);
      return [];
    }
  }, [maskOpacity]);

  // 使用useEffect处理图片和蒙层的更新
  useEffect(() => {
    if (!imageObj) return;

    const { imageSize: newImageSize, stageSize: newStageSize } = calculateSizes(imageObj);
    setImageSize(newImageSize);
    setStageSize(newStageSize);

    // 只有当maskImage存在且发生变化时才处理蒙层
    if (maskImage && image) {
      processMaskImage(maskImage, newImageSize, newStageSize).then(setLines);
    } else {
      setLines([]);
    }
  }, [imageObj, maskImage, image, processMaskImage, calculateSizes]);

  // 使用useMemo缓存错误样式
  const errorOverlayFinalStyle = useMemo(() => ({
    position: 'absolute' as const,
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundColor: '#FF3636',
    opacity: 0.3,
    pointerEvents: 'none' as const,
    borderRadius: '4px',
    ...errorOverlayStyle
  }), [errorOverlayStyle]);

  return (
    <div style={containerStyle}>
      <div style={imageContainerStyle}>
        <Stage width={stageSize.width} height={stageSize.height}>
          <Layer>
            {imageObj && (
              <KonvaImage
                image={imageObj}
                width={stageSize.width}
                height={stageSize.height}
              />
            )}
            {lines.map((line, i) => (
              <Line
                key={i}
                points={line.points.flatMap(p => [p.x, p.y])}
                stroke={line.color}
                strokeWidth={line.strokeWidth}
                tension={0.5}
                lineCap="round"
                lineJoin="round"
              />
            ))}
          </Layer>
        </Stage>
        {hasError && <div style={errorOverlayFinalStyle} />}
      </div>
    </div>
  );
});

export default ImageWithMask; 