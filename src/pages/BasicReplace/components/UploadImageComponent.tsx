import React, { useState } from 'react';
import { Button, Flex, Image, Tooltip, Upload, UploadProps, message } from 'antd';
import IconFont from '@/components/IconFont';
import ImageWithMask from './ImageWithMask';
import ImageLoadingAnimation from '@/components/ImageLoadingAnimation';
import animationData from '@/assets/animations/uploadRet_two.json';

const { Dragger } = Upload;

// 定义图片类型
export type ImageType = 'upper garment' | 'lower garment' | 'outfit';

// 组件属性接口
interface UploadImageComponentProps {
  tag: string;
  smallTitle?: string;
  image: string;
  systemMattingStatus: string;
  maskImage?: string;
  imageType?: ImageType;
  onImageChange: (image: string | null, type?: ImageType, uploadSizeError?: boolean) => void;
  uploadProps: UploadProps;
  onSmearClick?: () => void; // 添加涂抹按钮点击事件
}

/**
 * 服装上传组件
 * 用于上传上装或下装图片
 */
const UploadImageComponent: React.FC<UploadImageComponentProps> = ({
  tag,
  smallTitle,
  image,
  systemMattingStatus,
  maskImage,
  imageType,
  onImageChange,
  uploadProps,
  onSmearClick
}) => {
  // 上传加载状态
  const [isLoading, setIsLoading] = useState(false);
  // 添加图片尺寸校验错误状态
  const [uploadSizeError, setUploadSizeError] = useState(false);

  /**
   * 处理上传状态变化
   * @param info 上传信息
   * @param clothingType 服装类型
   */
  const handleUploadChange = (info, clothingType?: ImageType) => {
    const { file } = info;

    // 上传中设置 loading 状态
    if (file.status === 'uploading') {
      // 只有当当前没有上传进行中时才设置loading状态
      // 这样可以防止多次触发上传状态导致上传中断
      if (!isLoading) {
        setIsLoading(true);
      }
      return; // 上传中不做其他处理
    }

    // 处理上传完成状态
    if (file.status === 'done' || file.status === 'error') {
      setIsLoading(false);
      if (file.status === 'done' && file.response && file.response.success) {
        // 验证图片尺寸
        validateImageSize(file.response.data, clothingType);
      } else if (file.status === 'error') {
        message.error('上传图片异常，请重试');
      }
    }
  };

  /**
   * 验证图片尺寸
   * @param imageUrl 图片URL
   * @param clothingType 服装类型
   */
  const validateImageSize = (imageUrl, clothingType?: ImageType) => {
    const img = new window.Image();

    img.onload = () => {
      // 检查图片是否满足最小尺寸要求
      const isSizeTooSmall = img.width < 768 || img.height < 1024;


      setUploadSizeError(isSizeTooSmall);

      // 尺寸符合要求，更新图片数据
      onImageChange(imageUrl, clothingType, isSizeTooSmall);
    };

    img.onerror = () => {
      message.error('图片加载异常，请重试');
      onImageChange(imageUrl, clothingType);
    };

    img.src = imageUrl;
  };

  return (
    <Flex vertical gap={8} justify={'flex-start'} className={'work-item-container'}>
      {/* 标题区域 */}
      <div className={'text16 font-pf color-n weight'} style={{ display: 'flex', justifyContent: 'space-between' }}>
        <span>{tag}</span>
      </div>

      {/* 副标题 - 仅在有图片类型时显示 */}
      {imageType && (
        <div className={'text14 color-96'}>{smallTitle}</div>
      )}

      {/* 上传区域 - 仅在没有图片时显示 */}
      {!image && (
        <Flex vertical className={'upload-container'} gap={8}>
          <div className="text14 color-96" style={{ margin: '0 auto' }}>
            上传单件上装或下装，不支持套装
          </div>

          {/* 上传组件区域 */}
          <div className="upload-box">
            {/* 上装上传组件 */}
            <Dragger
              className={'logo-upload upload-box-item'}
              {...uploadProps}
              onChange={(info) => handleUploadChange(info, 'upper garment')}
            >
              {/* 显示上传中状态，但不禁用上传功能 */}
              <Flex vertical align="center" justify="center" style={{ height: '100%' }}>
                {isLoading ? (
                  <>
                    <IconFont type={'icon-loading'} spin style={{ fontSize: '24px' }} />
                    <div style={{ margin: '8px auto' }}>上传中...</div>
                  </>
                ) : (
                  <>
                    <IconFont type={'icon-leimutubiaofuzhuangneiyi'}
                      style={{ fontSize: '24px', borderRadius: '8px' }} />
                    <div style={{ margin: '8px auto' }}>上传上装</div>
                    <div className="text12 color-96">点击/粘贴/拖拽图片至此</div>
                  </>
                )}
              </Flex>
            </Dragger>

            {/* 下装上传组件 */}
            <Dragger
              className={'logo-upload upload-box-item'}
              {...uploadProps}
              onChange={(info) => handleUploadChange(info, 'lower garment')}
            >
              {/* 显示上传中状态，但不禁用上传功能 */}
              <Flex vertical align="center" justify="center" style={{ height: '100%' }}>
                {isLoading ? (
                  <>
                    <IconFont type={'icon-loading'} spin style={{ fontSize: '24px' }} />
                    <div style={{ margin: '8px auto' }}>上传中...</div>
                  </>
                ) : (
                  <>
                    <IconFont type={'icon-kuzi1'}
                      style={{ fontSize: '24px', borderRadius: '8px' }} />
                    <div style={{ margin: '8px auto' }}>上传下装</div>
                    <div className="text12 color-96">点击/粘贴/拖拽图片至此</div>
                  </>
                )}
              </Flex>
            </Dragger>
          </div>
        </Flex>
      )}


      {/* 图片展示组件 */}
      {image && (
        <Flex gap={14} justify={'center'} className={'logo-upload-main-img'}
          style={{ position: 'relative', height: 226 }}>

          {/* 原图存在且蒙版图不存在时展示加载动效 */}
          {!maskImage && (
            <ImageLoadingAnimation
              width="100%"
              height="100%"
              image={image}
              systemMattingStatus={systemMattingStatus}
              scanAnimation={animationData}
            />
          )}

          {/* 有蒙版图片时显示带蒙版的图片 */}
          {maskImage &&
            <ImageWithMask
              image={image}
              maskImage={maskImage}
              hasError={uploadSizeError}
              errorOverlayStyle={{ backgroundColor: 'rgba(97, 179, 255, 1)' }}
            />}

          {/* 上传图片大小有错误-提醒 */}
          {(uploadSizeError) && (
            <>
              {/* 图片展示区域 */}
              <div className="show-image-container">
                <div style={{ position: 'relative', height: '100%' }}>
                  <Image src={image} alt="加载中..." preview={{ mask: '点击预览' }}
                    style={{ height: '100%', objectFit: 'contain' }} />
                  <div style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    backgroundColor: '#FF3636',
                    opacity: 0.2,
                    pointerEvents: 'none',
                    zIndex: 1,
                  }} />
                </div>
              </div>
              <div className={'bottom-status-row'} style={{
                background: '#FDECEE',
                position: 'absolute',
                bottom: 0,
                left: 0,
                zIndex: 1,
                right: 0,
                width: '100%',
                height: 36,
                padding: '8px 0',
                borderBottomLeftRadius: '4px',
                borderBottomRightRadius: '4px',
              }}>
                <Flex gap={5} align="center" justify="center">
                  <IconFont type={'icon-cuowu'} style={{ fontSize: 14, color: '#FFFFFF' }} />
                  <div style={{ fontSize: 14 }}>像素过低请重新上传</div>
                </Flex>
              </div>
            </>
          )}

          {/* 删除/更换/涂抹工具 */}
          <Flex vertical gap={8} className={'logo-combine-upload-image-btn'}>
            <Tooltip title="删除" placement="left">
              <Button shape="circle"
                icon={<IconFont type={'icon-icon_shanchu'} />}
                onClick={() => onImageChange('')} />
            </Tooltip>
            <Tooltip title="重新上传" placement="left">
              <Upload {...uploadProps} onChange={(info) => handleUploadChange(info, imageType)}>
                <Button shape="circle"
                  icon={<IconFont type={'icon-shangchuan'} />} />
              </Upload>
            </Tooltip>
            {
              !uploadSizeError && (
                <Tooltip title="重新涂抹" placement="left">
                  <Button shape="circle" onClick={onSmearClick}
                    icon={<IconFont type={'icon-tumo'} />} />
                </Tooltip>
              )
            }
          </Flex>
        </Flex>
      )}
    </Flex>
  );
};

export default UploadImageComponent; 