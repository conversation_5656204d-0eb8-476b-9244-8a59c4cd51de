import React from 'react';
import { Alert, Flex, Segmented, Slider } from 'antd';
import IconFont from '@/components/IconFont';
import SmudgeCanvas from '@/components/imageOperate/smudgeCanvas/SmudgeCanvas';
import { ImageUploaderButton } from '@/components/Common/ImageUploader';
import { Stage } from 'konva/lib/Stage';

// 系统涂抹处理状态
const SYSTEM_MATTING_STATUS = {
  // 等待
  WAITING: 'WAITING',
  // 处理中
  PROCESSING: 'PROCESSING',
  // 完成
  FINISHED: 'FINISHED',
  // 失败
  FAILED: 'FAILED',
};

// 涂抹笔属性标签组件
const SegLabel = ({ iconurl, text }) => {
  return (
    <Flex style={{ width: 62, height: 28, gap: 8 }} align={'center'} justify={'center'}>
      <IconFont type={iconurl} width={16} height={16} />
      <div className={'text14 font-pf color-1a'}>{text}</div>
    </Flex>
  );
};

/**
 * 涂抹画布组件属性
 */
interface SmudgeCanvasComponentProps {
  // 原始图片
  image: string;
  // 蒙版图片
  maskImage: string;
  // 是否处于擦除模式
  isErasing: boolean;
  // 设置擦除模式
  setIsErasing: (value: boolean) => void;
  // 笔刷宽度
  strokeWidth: number;
  // 设置笔刷宽度
  setStrokeWidth: (value: number) => void;
  // 线条数据
  lines: any[];
  // 设置线条数据
  setLines: (lines: any[]) => void;
  // 服装线条数据
  clothLines: any[];
  // 设置服装线条数据
  setClothLines: (lines: any[]) => void;
  // 画布引用
  stageRef: React.RefObject<Stage>;
  // 系统涂抹状态
  systemMattingStatus: string;
}

/**
 * 涂抹画布组件
 * 用于渲染涂抹画布和相关控制
 */
const SmudgeCanvasComponent: React.FC<SmudgeCanvasComponentProps> = ({
  image,
  maskImage,
  isErasing,
  setIsErasing,
  strokeWidth,
  setStrokeWidth,
  lines,
  setLines,
  clothLines,
  setClothLines,
  stageRef,
  systemMattingStatus
}) => {
  return (
    <div style={{
      width: '100%',
      padding: '16px 78px',
      borderRadius: 8,
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: '#F5F6F9',
      gap: 16,
    }}>
      {/* 提示信息 */}
      <Alert type={'info'}
        showIcon
        style={{
          width: '100%',
          textAlign: 'left',
          border: 'none',
          color: '#366EF4',
          backgroundColor: '#D9E1FF',
        }}
        className={'text14 font-pf'}
        message={'系统识别区域如果有误差，请手工涂抹，确保服装区域被蓝色蒙层全部完整覆盖' }
      />

      {/* 涂抹组件 */}
        <div style={{ height: '560px', width: '560px', position: 'relative', margin: '0 auto' }}>
          <SmudgeCanvas
            reversal={true}
            originSize={true}
            imageUrl={image}
            strokeWidth={strokeWidth}
            singleSmudge={false}
            stageCmtRef={stageRef}
            lines={lines}
            isErasing={isErasing}
            maskImage={maskImage}
            onLinesChange={(lines) => {
              setLines(lines);
            }}
          />
          {/* 将ImageUploaderButton放在绝对定位，避免干扰鼠标事件 */}
          <div style={{ 
            position: 'absolute', 
            top: '10px', 
            right: '10px', 
            zIndex: 10,
            pointerEvents: 'auto'
          }}>
            <ImageUploaderButton onImageChange={() => {}} />
          </div>
        </div>

      {/* 涂抹笔区域 */}
      <Flex gap={16} align={'flex-start'} justify="center">
        <Flex vertical align="start">
          <div className={'text14 font-pf color-96'}>笔刷属性</div>
          <div>
            <Segmented
              value={isErasing}
              onChange={(value) => setIsErasing(value as boolean)}
              style={{ backgroundColor: '#E1E3EB', marginTop: 4 }}
              options={[
                {
                  label: <SegLabel iconurl={'icon-tumo'} text={'涂抹'} />,
                  value: false,
                },
                {
                  label: <SegLabel iconurl={'icon-cachu'} text={'擦除'} />,
                  value: true,
                },
              ]}
            />
            <Segmented
              style={{ backgroundColor: '#E1E3EB', marginLeft: 8, marginTop: 4 }}
              value={null}
              onClick={() => {
                setLines([]);
                setClothLines([]);
              }}
              options={[
                {
                  label: <SegLabel iconurl={'icon-delete'} text={'清空选区'} />,
                  value: false,
                },
              ]}
            />
          </div>
        </Flex>
        <Flex align="start" vertical style={{ width: 190 }}>
          <div className={'text14 font-pf color-96'}>笔刷大小</div>
          <Slider 
            style={{ width: 150 }}
            value={strokeWidth}
            min={8}
            max={150}
            tooltip={{ open: false }}
            onChange={e => setStrokeWidth(e)} />
        </Flex>
      </Flex>
    </div>
  );
};

export default SmudgeCanvasComponent; 