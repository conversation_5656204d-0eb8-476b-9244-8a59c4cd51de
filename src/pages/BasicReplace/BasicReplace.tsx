import React, { useEffect, useRef, useState, useCallback } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Button,
  Checkbox,
  Flex,
  Image,
  message,
  Modal,
  Segmented,
  Upload,
  UploadProps,
} from 'antd';
import './index.less';
import {
  CreativeVO,
  isProcessing,
  LoraType,
  pictureMatting,
  queryActiveAndTodayCreative,
  batchQueryCreative,
  basicChangingClothes,
} from '@/services/CreativeController';
import TaskOutputBlock, { TaskOutputBlockRef } from '@/components/Creative/TaskOutputBlock';
import PredictBlock from '@/components/Creative/PredictBlock';
import { predict, PredictVO, queryImagePoint } from '@/services/PointController';
import {
  BG_ALL_MODELS,
  EXPERIENCE_POINT,
  IMAGE_POINT,
  IMAGE_POINT_CHANGE_EVENT,
  IS_TRIAL_ACCOUNT,
  MINI_LOADING_ICON,
  UPLOAD_URL,
} from '@/constants';
import { getUserInfo } from '@/utils/utils';
import TopupModal from '../Topup/Topup';
import { isVipUser } from '@/services/UserController';
import { dataURLToBlob, isStageWhite } from '@/utils/imageUtils';
import { Stage } from 'konva/lib/Stage';
import { uploadFile } from '@/services/FileController';
import SmudgeCanvasComponent from './components/SmudgeCanvasComponent';
import UploadImageComponent, { ImageType } from './components/UploadImageComponent';
import ElementWithTypeBlock from '@/components/Creative/ElementWithTypeBlock';
import {
  intelligentRecommendation,
  ElementConfig,
  batchQueryElementById,
  getBasicChangeConfig,
} from '@/services/ElementController';
import { InboxOutlined, RightOutlined } from '@ant-design/icons';
import IconFont from '@/components/IconFont';
import { StaticImage } from '@/assets/images/oss/StaticImage';
import StyleLoraDrawer, { SelectedItem } from '@/components/BasicReplace/StyleLoraDrawer';
import ProportionBlock from '@/components/Creative/ProportionBlock';
import { useLocation, useNavigate } from 'react-router-dom';

const { Dragger } = Upload;


// 当前类型
const currentType = 'BASIC_CHANGING_CLOTHES';
// 是否为试用账号
const isTrialAccount = sessionStorage.getItem(IS_TRIAL_ACCOUNT) === 'Y';
// 系统涂抹处理状态
const SYSTEM_MATTING_STATUS = {
  // 等待
  WAITING: 'WAITING',
  // 处理中
  PROCESSING: 'PROCESSING',
  // 完成
  FINISHED: 'FINISHED',
  // 失败
  FAILED: 'FAILED',
};

// localStorage 存储键名
const BASIC_REPLACE_FORM_DATA = 'BASIC_REPLACE_FORM_DATA';

/**
 * 从localStorage中获取保存的mattingId
 * @returns {number|undefined} mattingId或undefined
 */
const getMattingIdFromStorage = (): number | undefined => {
  try {
    const savedData = localStorage.getItem(BASIC_REPLACE_FORM_DATA);
    if (savedData) {
      const parsedData = JSON.parse(savedData);
      return parsedData?.data?.mattingId;
    }
    return undefined;
  } catch (error) {
    console.error('获取mattingId失败:', error);
    return undefined;
  }
};

/**
 * 更新localStorage中的表单数据
 * @param {string} field - 要更新的字段名称
 * @param {any} value - 字段的新值
 * @returns {boolean} 是否更新成功
 */
const updateFormDataInStorage = (field: string, value: any): boolean => {
  try {
    const savedData = localStorage.getItem(BASIC_REPLACE_FORM_DATA);
    if (!savedData) return false;
    
    const parsedData = JSON.parse(savedData);
    if (!parsedData?.data) return false;
    
    // 更新指定字段
    parsedData.data[field] = value;
    
    // 保存回localStorage
    localStorage.setItem(BASIC_REPLACE_FORM_DATA, JSON.stringify(parsedData));
    return true;
  } catch (error) {
    console.error(`更新${field}失败:`, error);
    return false;
  }
};

// 基础款换衣
const BasicReplace: React.FC<{ type: LoraType }> = ({ type = 'CUSTOM' }) => {
  // 获取用户信息
  const userInfo = getUserInfo();

  // 空数据
  const emptyData = {
    image: '',
    maskImage: '',
    mattingId: undefined as number | undefined,
    imageType: undefined as ImageType | undefined,
    configs: {},
    proportion: isTrialAccount && type === 'CUSTOM' ? 'THREE_FOUR_LG_N' : 'P_1152_1536',
    imageNum: userInfo?.roleType === 'OPERATOR' ? 1 : 1,
  };

  //==================================== Ref 管理 =========================================
  // 输出组件
  const outputBlockRef = useRef(null);
  // 结果展示组件
  const taskOutputRef = useRef<TaskOutputBlockRef>(null);
  // 涂抹画布Ref
  const stageRef = useRef<Stage>(null);
  // 轮询定时器Ref
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  //==================================== boolean类型管理 =========================================
  // 是否可以保存
  const [canSave, setCanSave] = useState(false);
  // 是否显示充值弹窗
  const [showTopupModal, setShowTopupModal] = useState(false);
  // 是否是 vip 用户
  const [vipUser, setVipUser] = useState(false);
  // 添加图片尺寸校验错误状态
  const [uploadSizeError, setUploadSizeError] = useState(false);
  // 是否显示涂抹弹窗
  const [showSmearModal, setShowSmearModal] = useState(false);
  // 是否开启抽屉
  const [isOpenDrawer, setIsOpenDrawer] = useState(false);
  // 是否提交中
  const [commiting, setCommiting] = useState(false);
  // 是否显示恢复数据弹窗
  const [showRestoreModal, setShowRestoreModal] = useState(false);

  //==================================== 涂抹管理 =========================================
  // 是否涂抹
  const [isErasing, setIsErasing] = useState(false);
  // 笔刷大小
  const [strokeWidth, setStrokeWidth] = useState(24);
  // @ts-ignore
  const [lines, setLines] = useState<Array<CustomLine>>([]);
  // @ts-ignore
  const [clothLines, setClothLines] = useState<Array<CustomLine>>([]);

  //==================================== 状态管理 =========================================
  // 单价
  const [predictVO, setPredictVO] = useState<PredictVO | null>(null);
  // 积分
  const [imagePoint, setImagePoint] = useState(0);
  // 数据
  const [data, setData] = useState(emptyData);
  // 是否开启系统涂抹
  const [systemMattingStatus, setSystemMattingStatus] = useState<string>(SYSTEM_MATTING_STATUS.WAITING);
  // 今天列表
  const [todayList, setTodayList] = useState<Array<CreativeVO>>([]);
  // 展示id
  const [showId, setShowId] = useState<number>(0);
  // 元素配置
  const [configs, setConfigs] = useState<ElementConfig[]>();
  // 当前参考图类型
  const [currentReferenceType, setCurrentReferenceType] = useState<'系统生成参考图' | '本地上传'>('系统生成参考图');

  // 添加新的状态来存储预加载数据
  const [preloadedSceneModels, setPreloadedSceneModels] = useState<any[]>([]);
  const [isPreloading, setIsPreloading] = useState<boolean>(false);
  const [styleDescription, setStyleDescription] = useState<string>('');
  // 添加原始数据状态
  const [originalSceneModels, setOriginalSceneModels] = useState<any[]>([]);

  // 添加新的状态来存储过滤后的场景数据
  const [filteredSceneModels, setFilteredSczeneModels] = useState<any[]>([]);

  const location = useLocation();
  const navigate = useNavigate();

  // 参考图片列表
  const [referencePicture, setReferencePicture] = useState<{
    imageUrl: string,// 图片地址
    isSelected: boolean,// 是否选中 
    referenceConfig?: string | any,// 参考图配置
    backTags?: string,// 背景标签
    backExtTags?: string,// 背景扩展标签
  }[]>([]);

  // 上次保存的表单数据
  const [savedFormData, setSavedFormData] = useState<any>(null);
  // 是否已检查过数据
  const [hasCheckedFormData, setHasCheckedFormData] = useState<boolean>(false);

  //==================================== 属性监听 =========================================
  // 初始化
  useEffect(() => {
    init();

    // 检查是否有保存的表单数据
    checkSavedFormData();

    // 组件卸载时清理数据
    return () => {
      // 清空预加载的场景模型数据
      setPreloadedSceneModels([]);
      // 清空原始场景模型数据
      setOriginalSceneModels([]);
      // 清空过滤后的场景数据
      setFilteredSczeneModels([]);
    };
  }, []);

  // 监听路由变化，重新检查表单数据
  useEffect(() => {
    const handleRouteChange = () => {
      // 当路由变化时，检查是否有保存的表单数据
      checkSavedFormData();
    };

    // 添加路由变化监听
    window.addEventListener('popstate', handleRouteChange);

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  // 使用useEffect监听location变化
  useEffect(() => {
    // 当location变化时，检查保存的表单数据
    if (!data.image) { // 仅当当前没有图片时才尝试恢复
      checkSavedFormData();
    }
  }, [location, data.image]);

  // 将checkSavedFormData转为memoized函数，避免重复创建
  const checkSavedFormData = useCallback(() => {
    // 如果已经显示了恢复弹窗或已经检查过且没有找到数据，则不重复检查
    if (showRestoreModal || (hasCheckedFormData && !savedFormData)) {
      return;
    }

    try {
      const savedData = localStorage.getItem(BASIC_REPLACE_FORM_DATA);
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        if (parsedData && parsedData.data && parsedData.data.image) {
          // 确保数据格式正确且存在图片数据
          // 只要存在图片数据就可以恢复，不再限制必须有mattingId
          setSavedFormData(parsedData);
          setShowRestoreModal(true);
        } else {
          // 数据格式不正确，清除此数据
          console.log('保存的表单数据格式不正确，已清除');
          localStorage.removeItem(BASIC_REPLACE_FORM_DATA);
        }
      }

      // 标记已检查过数据
      setHasCheckedFormData(true);
    } catch (error) {
      console.error('读取保存的表单数据失败:', error);
      // 出错时清除数据，避免再次尝试解析
      localStorage.removeItem(BASIC_REPLACE_FORM_DATA);
      setHasCheckedFormData(true);
    }
  }, [showRestoreModal, hasCheckedFormData, savedFormData]);

  // 恢复保存的表单数据
  const restoreSavedFormData = () => {
    if (!savedFormData) return;

    try {
      // 恢复表单数据
      setData(savedFormData.data);
      setReferencePicture(savedFormData.referencePicture || []);
      setCurrentReferenceType(savedFormData.currentReferenceType || '系统生成参考图');

      // 如果数据齐全，设置为可保存状态
      if (savedFormData.data.image && 
          savedFormData.data.maskImage && 
          savedFormData.data.imageType && 
          (savedFormData.referencePicture || []).length > 0) {
        setCanSave(true);
      }

      // 如果有图片，则触发系统涂抹
      if (savedFormData.data.image && savedFormData.data.imageType) {
        setSystemMattingStatus(SYSTEM_MATTING_STATUS.WAITING);
        loadSceneData(savedFormData.data.image, savedFormData.data.imageType);
      }

      message.success('已恢复上次的创作');
    } catch (error) {
      console.error('恢复表单数据失败:', error);
      message.error('恢复失败，将重置表单');
      cancelOrDelete();
    } finally {
      setShowRestoreModal(false);
    }
  };

  // 放弃保存的表单数据
  const discardSavedFormData = () => {
    localStorage.removeItem(BASIC_REPLACE_FORM_DATA);
    setSavedFormData(null);
    setShowRestoreModal(false);
    // 重置检查状态，允许下次再检查
    setHasCheckedFormData(false);
    message.info('已放弃上次的创作');
  };

  // 保存表单数据到localStorage
  const saveFormDataToLocalStorage = useCallback(() => {
    try {
      // 不再限制必须有图片才保存，让模特选择状态能够单独保存
      const formData = {
        data: data,
        referencePicture: referencePicture,
        currentReferenceType: currentReferenceType,
        timestamp: new Date().getTime()
      };
      localStorage.setItem(BASIC_REPLACE_FORM_DATA, JSON.stringify(formData));
    } catch (error) {
      console.error('保存表单数据失败:', error);
    }
  }, [data, referencePicture, currentReferenceType]);

  // 监听表单数据变化，保存到localStorage
  useEffect(() => {
    // 不再限制必须有图片才保存
    saveFormDataToLocalStorage();
  }, [data.image, data.maskImage, data.proportion, data.configs, referencePicture, currentReferenceType, saveFormDataToLocalStorage]);

  // 在用户离开页面前保存数据
  useEffect(() => {
    const handleBeforeUnload = () => {
      saveFormDataToLocalStorage();
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [saveFormDataToLocalStorage]);

  // 成功提交后清除保存的表单数据
  const clearSavedFormData = () => {
    localStorage.removeItem(BASIC_REPLACE_FORM_DATA);
    setSavedFormData(null);
    // 重置检查状态
    setHasCheckedFormData(false);
  };

  useEffect(() => {
    // 当参考图片列表发生变化时，重新查询预测单价信息
    predict(currentType, (referencePicture.length == 0 ? 1 : referencePicture.length), null)
      .then(result => {
        if (result) {
          setPredictVO(result);
        }
      });
  }, [referencePicture]);

  useEffect(() => {
    // 如果图片、图片类型、参考图都存在，则可以进行新增
    if (data.image &&
      data.maskImage &&
      data.imageType &&
      referencePicture.length > 0) {
      setCanSave(true);
    } else {
      setCanSave(false);
    }
  }, [data.image, data.maskImage, data.imageType, referencePicture]);

  // 监听系统涂抹状态
  useEffect(() => {
    // 处理涂抹状态
    const handleMattingStatus = async () => {
      // 只有当状态为等待且图片和图片类型都存在时才执行
      if (systemMattingStatus !== SYSTEM_MATTING_STATUS.WAITING ||
        !data.image || !data.imageType) {
        return;
      }

      try {
        // 设置状态为处理中
        setSystemMattingStatus(SYSTEM_MATTING_STATUS.PROCESSING);

        // 获取保存的mattingId
        const mattingId = getMattingIdFromStorage();
        // 如果mattingId不存在，则执行图片抠图处理
        if (!mattingId) {
          // 图片抠图处理
          const result = await pictureMatting(data.image, data.imageType);
          // 如果抠图失败，则设置状态为失败
          if (!result.success) {
            setSystemMattingStatus(SYSTEM_MATTING_STATUS.FAILED);
            return;
          }
        }

        // 更新积分
        updatePoint(type);

        // 查询今天的创作任务
        const res = await queryActiveAndTodayCreative(['PICTURE_MATTING'], null);

        // 如果没有任务列表，设置状态为完成并返回
        if (!res?.todayList || res.todayList.length === 0) {
          setSystemMattingStatus(SYSTEM_MATTING_STATUS.FINISHED);
          return;
        }

        // 设置任务列表和显示ID
        const todayList = res.todayList;
        setTodayList(todayList);
        setShowId(todayList[0]?.id || 0);

        // 设置mattingId
        setData(pre => ({
          ...pre,
          mattingId: todayList[0]?.id,
        }));

        // 更新localStorage中的数据
        updateFormDataInStorage('mattingId', todayList[0]?.id);

        // 处理第一个任务的结果图片
        processFirstTaskResult(todayList);

        // 根据任务状态决定是否需要轮询
        if (todayList.some(e => isProcessing(e))) {
          // 有处理中的任务，开始轮询
          startPolling(todayList);
        } else {
          // 没有处理中的任务，设置状态为完成
          setSystemMattingStatus(SYSTEM_MATTING_STATUS.FINISHED);
        }
      } catch (error) {
        console.error('处理涂抹状态时出错：', error);
        message.error('处理图片时发生错误，请重试');
        // 发生错误时设置状态为完成
        setSystemMattingStatus(SYSTEM_MATTING_STATUS.FAILED);
      }
    };

    // 执行处理函数
    handleMattingStatus();
  }, [data.image]);

  // 从type数组中提取性别相关的值
  const extractGenderType = (typeArray: string[]): string | undefined => {
    if (!Array.isArray(typeArray)) return undefined;
    return typeArray.find(t =>
      t && typeof t === 'string' &&
      (t.toLowerCase() === 'female' || t.toLowerCase() === 'male' || t.toLowerCase() === 'child')
    );
  };

  // 根据性别过滤场景数据
  const filterModelsByGender = (models: any[], gender: string) => {
    return models.filter(model => {
      if (!model?.type || !Array.isArray(model.type)) {
        return false;
      }
      const genderType = extractGenderType(model.type);
      return genderType && genderType.toLowerCase() === gender.toLowerCase();
    });
  };

  // 监听模特选择变化，重新加载场景数据
  useEffect(() => {
    const config = data.configs[1]?.[0];

    // 如果 data.configs[1] 为 null 或者 config 的类型无效，重置为原始场景数据
    if (data.configs[1] === null || !config?.type || !Array.isArray(config.type)) {
      setPreloadedSceneModels(originalSceneModels);
      return;
    }

    // 使用模糊匹配查找模特类型
    const modelType = config.type.find(t =>
      t && typeof t === 'string' &&
      (t.toLowerCase().includes('female-model') ||
        t.toLowerCase().includes('male-model') ||
        t.toLowerCase().includes('child-model'))
    );

    if (!modelType) {
      // 如果没有找到匹配的模特类型，显示所有场景数据
      setPreloadedSceneModels(originalSceneModels);
      return;
    }

    // 根据模特类型过滤场景数据
    let filteredModels;
    if (modelType.toLowerCase().includes('female-model')) {
      filteredModels = filterModelsByGender(originalSceneModels, 'female');
    } else if (modelType.toLowerCase().includes('male-model')) {
      filteredModels = filterModelsByGender(originalSceneModels, 'male');
    } else if (modelType.toLowerCase().includes('child-model')) {
      filteredModels = filterModelsByGender(originalSceneModels, 'child');
    } else {
      filteredModels = originalSceneModels;
    }

    // 更新场景数据
    setPreloadedSceneModels(filteredModels);

  }, [data.configs, originalSceneModels]);

  // 处理第一个任务的结果图片
  const processFirstTaskResult = (taskList: CreativeVO[]) => {
    if (!taskList || taskList.length === 0) return;

    const firstTask = taskList[0];
    if (firstTask && firstTask.resultImages && firstTask.resultImages.length > 0) {
      const firstMaskImage = firstTask.resultImages[0];
      setData(prevData => ({
        ...prevData,
        maskImage: firstMaskImage,
      }));
      
      // 更新localStorage中的数据
      updateFormDataInStorage('maskImage', firstMaskImage);

      // 检查是否可以设置canSave为true
      if (data.image && data.imageType && referencePicture.length > 0) {
        setCanSave(true);
      }
    }
  };

  // 开始轮询任务状态
  const startPolling = (initialTasks: CreativeVO[]) => {
    // 清除现有定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    // 设置任务列表的初始值
    let currentTasks = [...initialTasks];

    // 定义单次轮询函数
    const pollOnce = async () => {
      try {
        // 只查询处理中的任务
        const processingTaskIds = currentTasks
          .filter(task => isProcessing(task))
          .map(task => task.id);

        // 如果没有处理中的任务，则停止轮询
        if (processingTaskIds.length === 0) {
          setSystemMattingStatus(SYSTEM_MATTING_STATUS.FINISHED);
          return;
        }

        // 批量查询任务状态
        const result = await batchQueryCreative(processingTaskIds);

        // 如果查询成功，更新任务列表
        if (result && result.length > 0) {
          // 更新当前任务列表
          setTodayList(prevList => {
            // 合并最新结果与现有任务
            const updatedList = prevList.map(task => {
              const updatedTask = result.find(t => t.id === task.id);
              return updatedTask || task;
            });
            currentTasks = updatedList;
            return updatedList;
          });

          // 检查结果中是否有已完成的任务，并提取resultImages
          for (const task of result) {
            if (!isProcessing(task) && task.resultImages && task.resultImages.length > 0) {
              // 找到一个已完成的任务且有结果图片，保存第一张图片
              const firstMaskImage = task.resultImages![0];
              setData(prevData => ({
                ...prevData,
                maskImage: firstMaskImage,
              }));

              // 更新localStorage中的数据
              updateFormDataInStorage('maskImage', firstMaskImage);

              break; // 只需要处理第一个符合条件的任务
            }
          }

          // 检查是否所有任务都已完成或失败
          const allTasksCompleted = !currentTasks.some(task => isProcessing(task));
          if (allTasksCompleted) {
            // 所有任务都已完成或失败，设置状态为完成
            setSystemMattingStatus(SYSTEM_MATTING_STATUS.FINISHED);
            return;
          }
        }

        // 安排下一次轮询
        timeoutRef.current = setTimeout(pollOnce, 3000); // 3秒后再次轮询
      } catch (error) {
        console.error('轮询状态时出错：', error);

        // 即使出错，也尝试继续轮询，除非所有任务都已完成
        if (currentTasks.some(task => isProcessing(task))) {
          timeoutRef.current = setTimeout(pollOnce, 3000);
        } else {
          // 如果没有处理中的任务，设置状态为完成
          setSystemMattingStatus(SYSTEM_MATTING_STATUS.FINISHED);
        }
      }
    };

    // 开始第一次轮询（稍微延迟，避免立即查询）
    timeoutRef.current = setTimeout(pollOnce, 300);
  };

  // 组件卸载时清除轮询定时器
  useEffect(() => {
    return () => {
      // 清除轮询定时器
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, []);

  // 监听系统抠图状态变化，确保在抠图完成后重新检查按钮状态
  useEffect(() => {
    // 当抠图完成时，重新检查是否可以保存
    if (systemMattingStatus === SYSTEM_MATTING_STATUS.FINISHED) {
      // 如果图片、图片类型、参考图都存在，则可以进行新增
      if (data.image &&
        data.maskImage &&
        data.imageType &&
        referencePicture.length > 0) {
        setCanSave(true);
      } else {
        setCanSave(false);
      }
    }
  }, [systemMattingStatus, data.image, data.maskImage, data.imageType, referencePicture]);

  //==================================== 具体方法 =========================================
  /**
   * 初始化
   */
  async function init() {
    // 获取元素配置
    const value = await getBasicChangeConfig();

    if (value) {
      // 设置元素配置
      setConfigs(value);

      // 设置数据
      value.forEach(item => {
        data.configs[item.id] = null;
      });
    }
    // 更新积分
    updatePoint(type);
  }

  /**
   * 更新积分
   */
  const updatePoint = (type) => {
    // 查询积分
    queryImagePoint().then(
      result => {
        if (result) {
          const imagePoint = type === 'CUSTOM' ? result.imagePoint : result.experiencePoint;
          setImagePoint(imagePoint);
          localStorage.setItem(IMAGE_POINT, result.imagePoint.toString());
          localStorage.setItem(EXPERIENCE_POINT, result.experiencePoint.toString());
          window.dispatchEvent(new Event(IMAGE_POINT_CHANGE_EVENT));
        }
      },
    );

    // 查询是否为VIP用户
    isVipUser().then(res => setVipUser(!!res));
  };

  /**
   * 加载场景和元素数据
   */
  const loadSceneData = async (image?: string, imageType?: ImageType) => {
    try {
      setIsPreloading(true);

      // 使用传入的参数或data中的值
      const currentImage = image || data.image;
      const currentType = imageType || data.imageType;


      // 检查是否有服装图片
      if (!currentImage || !currentType) {
        console.log('缺少必要的图片数据，跳过加载');
        setIsPreloading(false);
        return;
      }

      // 加载推荐场景数据
      const recommendedLoras = await intelligentRecommendation(currentType, [currentImage]);

      if (!recommendedLoras?.length) {
        console.log('未获取到推荐场景数据');
        setIsPreloading(false);
        return;
      }

      // 设置当前场景模型数据
      setPreloadedSceneModels(recommendedLoras);

      // 收集所有场景模型的ID
      const sceneIds = recommendedLoras.map(model => model.id);

      // 批量查询场景模型的详细数据
      if (sceneIds.length > 0) {
        const elementsDataMap = await batchQueryElementById(sceneIds);

        if (elementsDataMap) {
          // 更新场景模型数据，添加子项信息
          const updatedLoras = recommendedLoras.map(model => {
            const elementData = elementsDataMap[model.id];
            if (elementData) {
              return {
                ...model,
                children: elementData.children || []
              };
            }
            return model;
          });

          // 更新场景模型数据
          setPreloadedSceneModels(updatedLoras);

          // 保存原始场景模型数据
          setOriginalSceneModels(updatedLoras);

        } else {
          console.warn('批量查询场景模型数据返回为空');
        }
      }

      setIsPreloading(false);
    } catch (error) {
      console.error('加载场景数据失败:', error);
      setIsPreloading(false);
    }
  };

  /**
   * 处理涂抹确认操作
   * 获取涂抹区域图片并设置为蒙版图片
   * @returns {Promise<void>}
   */
  const doSmearPic = async (): Promise<void> => {
    try {
      // 获取涂层图片URL
      const maskUrl = await getMaskUrl();

      // 如果获取失败，直接返回（错误提示已在getMaskUrl中处理）
      if (!maskUrl) {
        return;
      }

      // 更新数据中的蒙版图片
      setData(prevData => ({
        ...prevData,
        maskImage: maskUrl,
      }));

      // 关闭涂抹弹窗
      setShowSmearModal(false);

      // 操作成功提示
      message.success('蒙版设置成功');
    } catch (error) {
      // 捕获并处理可能的异常
      console.error('设置蒙版图片失败:', error);
      message.error('设置蒙版图片失败，请重试');
    }
  };

  /**
   * 获取涂抹区域的图片URL
   * 将画布内容转换为图片并上传到服务器
   */
  const getMaskUrl = async (): Promise<string | undefined> => {
    try {
      // 获取画布引用
      const stage = stageRef.current;

      // 检查画布是否存在
      if (!stage) {
        message.error('画布初始化失败，请刷新页面重试');
        return undefined;
      }

      // 检查涂抹区域是否为空
      if (isStageWhite(stageRef)) {
        message.warning('涂抹区域为空，请先涂抹需要处理的区域');
        return undefined;
      }

      // 将画布转换为DataURL格式的图片
      const dataURL = stage.toDataURL({
        pixelRatio: 1,
        quality: 1,
      });

      // 转换为Blob对象
      const blob = dataURLToBlob(dataURL);

      // 创建File对象
      const maskFile = new File(
        [blob],
        `mask_${Date.now()}.png`,
        { type: 'image/png' },
      );

      // 上传文件并返回URL
      return await uploadFile(maskFile);
    } catch (error) {
      // 捕获并处理可能的异常
      console.error('获取蒙版图片失败:', error);
      message.error('获取蒙版图片失败，请重试');
      return undefined;
    }
  };


  /**
   * 处理人脸选择
   */
  const handleFaceChange = (configId: number, value: Array<ElementConfig> | null, isPreset?: boolean) => {
    if (isPreset) {
      data.configs[configId] = value;
      return;
    }

    setData((pre) => {
      const newConfigs = { ...pre.configs };
      // 如果当前选择的模特与之前选择的相同，则取消选择
      if (JSON.stringify(newConfigs[configId]) === JSON.stringify(value)) {
        newConfigs[configId] = null;
      } else {
        newConfigs[configId] = value;
      }
      return {
        ...pre,
        configs: newConfigs,
      };
    });
  };


  /**
   * 删除或取消
   */
  const cancelOrDelete = () => {
    // 关闭涂抹弹窗 
    setShowSmearModal(false);

    // 重置参考图片列表
    setReferencePicture([]);

    // 重置当前参考图类型为默认值
    setCurrentReferenceType('系统生成参考图');

    // 重置抽屉状态
    setIsOpenDrawer(false);

    // 重置涂抹相关状态
    setSystemMattingStatus(SYSTEM_MATTING_STATUS.WAITING);

    // 重新涂抹
    setIsErasing(false);
    // 清空涂抹区域
    setLines([]);
    setClothLines([]);
    // 清空笔刷大小
    setStrokeWidth(24);

    // 清除保存的表单数据
    clearSavedFormData();

    // 清空智能匹配数据
    setPreloadedSceneModels([]);
    setOriginalSceneModels([]);
    setFilteredSczeneModels([]);

    // 删除data中的数据
    setData(pre => {
      return {
        ...pre,
        configs: {}, // 清空模特选择
        image: '',
        maskImage: '',
        mattingId: undefined,
        imageType: undefined,
      };
    });
  };

  /**
   * 开始出图
   */
  const handleCreate = () => {
    // 如果需要充值，则显示充值弹窗
    if (predictVO && predictVO.needTopup) {
      setShowTopupModal(true);
      return;
    }
    // 如果正在提交，则不进行提交
    if (commiting || !canSave) {
      return;
    }

    // 设置正在提交 
    setCommiting(true);

    // 开始提交
    async function start(data) {
      // 发送请求
      const result = await basicChangingClothes({
        clotheImage: data.image,
        maskImage: data.maskImage,
        mattingId: data.mattingId,
        clotheType: data.imageType,
        configs: data.configs,
        proportion: data.proportion,
        referenceInfoList: referencePicture,
      });

      // 如果类型为系统，则检查积分
      if (type === 'SYSTEM') {
        return imagePoint >= data.imageNum;
      }

      // 设置正在提交为false
      setCommiting(false);

      // 更新积分
      updatePoint(type);

      // 如果生成失败，则提示
      if (!result) {
        console.log('生成图片失败', result);
        return;
      }

      // 如果角色为操作员，则提示
      if (userInfo?.roleType === 'OPERATOR') {
        message.success('开始生成图片');
      }

      // 清除保存的表单数据
      clearSavedFormData();

      // 重置抽屉状态
      setIsOpenDrawer(false);

      // 清空智能匹配数据
      setPreloadedSceneModels([]);
      setOriginalSceneModels([]);
      setFilteredSczeneModels([]);

      // 清空localStorage中的数据
      localStorage.removeItem(BASIC_REPLACE_FORM_DATA);

      taskOutputRef.current?.refresh();
      navigate(location.pathname, { replace: true });
    }


    // 重置configs数据
    const resetData = (data) => {
      // 重置configs
      const { configs, ...others } = data;

      const newConfigs = {};

      // 遍历configs
      for (let key in configs) {
        if (configs[key]) {
          newConfigs[key] = configs[key].map(item => item.id);
        }
      }

      // 返回数据
      return { ...others, configs: newConfigs };
    };


    start(resetData(data));
  };


  //==================================== 文件上传相关 =========================================
  /**
   * 单图上传配置 用于上传单张服装图片
   */
  const singleImageUploadProps: UploadProps = {
    accept: 'image/png, image/jpeg',
    showUploadList: false,
    action: UPLOAD_URL,
    multiple: false,
    name: 'file',
  };

  // 处理上传成功的文件
  const handleUploadSuccess = (fileList) => {
    // 使用Set去重，避免重复处理同一图片
    const uniqueUrls = new Set();

    // 获取所有上传成功的文件
    const successFiles = fileList
      .filter(f => f.status === 'done' && f.response && f.response.success)
      .filter(f => {
        // 过滤掉重复URL
        if (uniqueUrls.has(f.response.data)) {
          return false;
        }
        uniqueUrls.add(f.response.data);
        return true;
      })
      .map(f => ({
        imageUrl: f.response.data,
        isSelected: true,
      }));

    // 更新参考图列表
    if (successFiles.length > 0) {
      setReferencePicture(pre => {
        // 过滤掉已存在的URL
        const newFiles = successFiles.filter(file =>
          !pre.some(existing => existing.imageUrl === file.imageUrl),
        );
        return [...pre, ...newFiles];
      });
    }
  };

  // 处理上传失败
  const handleUploadError = (fileName) => {
    message.error(`图片 ${fileName} 上传失败，请重试`);
  };

  /**
   * 多图上传配置 用于上传多张参考图片
   */
  const multiImageUploadProps: UploadProps = {
    accept: 'image/png, image/jpeg',
    showUploadList: false,
    multiple: true,
    name: 'file',
    beforeUpload: (file, fileList) => {
      // 验证总数量是否超过限制
      if (!validateImageCount(file, fileList)) {
        return false;
      }

      // 获取当前文件在 fileList 中的索引
      const fileIndex = fileList.findIndex(item => item.uid === file.uid);
      // 判断当前文件是否是最后一个文件
      const isLastFile = fileIndex === fileList.length - 1;

      // 只有在最后一个文件时执行上传逻辑
      if (isLastFile) {
        // 批量处理所有文件
        const uploadPromises = fileList.map(fileItem => {
          return new Promise((resolve, reject) => {
            const formData = new FormData();
            formData.append('file', fileItem);

            fetch(UPLOAD_URL, {
              method: 'POST',
              body: formData,
            })
              .then(response => response.json())
              .then(response => {
                if (response.success) {
                  resolve({
                    uid: fileItem.uid,
                    status: 'done',
                    response: response,
                  });
                } else {
                  reject({
                    uid: fileItem.uid,
                    status: 'error',
                    name: fileItem.name,
                  });
                }
              })
              .catch(error => {
                reject({
                  uid: fileItem.uid,
                  status: 'error',
                  name: fileItem.name,
                });
              });
          });
        });

        // 等待所有上传完成
        Promise.allSettled(uploadPromises)
          .then(results => {
            // 收集成功的上传结果
            const successFiles = results
              .filter((result): result is PromiseFulfilledResult<any> => result.status === 'fulfilled')
              .map(result => result.value);

            // 收集失败的上传结果
            const failedFiles = results
              .filter((result): result is PromiseRejectedResult => result.status === 'rejected')
              .map(result => result.reason);

            // 处理成功上传的文件
            if (successFiles.length > 0) {
              handleUploadSuccess(successFiles);
            }

            // 处理失败的文件
            failedFiles.forEach(file => {
              handleUploadError(file.name);
            });
          });
      }

      // 阻止默认上传行为
      return false;
    },
    maxCount: 8,
  };

  // 防止重复显示警告信息的标志
  let isShowingWarning = false;

  /**
   * 验证上传图片数量 确保参考图片总数不超过8张
   */
  const validateImageCount = (file, fileList) => {
    // 检查上传后的总图片数是否超过限制
    const totalImagesAfterUpload = referencePicture.length + fileList.length;

    // 如果上传后的总图片数不超过8张，则允许上传  
    if (totalImagesAfterUpload <= 8) {
      return true;
    }

    // 防止重复显示警告
    if (!isShowingWarning) {
      isShowingWarning = true;
      message.error('最多选择8张参考图片');

      // 重置警告状态，避免用户快速多次操作时无法看到提示
      setTimeout(() => {
        isShowingWarning = false;
      }, 500);
    }

    // 仍然允许上传，但会在UI中显示警告
    return false;
  };

  /**
   * 处理上传响应 根据上传结果调用相应的处理函数
   */
  const handleUploadResponse = (info, onSuccess) => {
    const { file } = info;

    // 上传成功
    if (file.status === 'done' && file.response && file.response.success) {
      onSuccess(file.response.data);
    }

    // 上传失败
    else if (file.status === 'error') {
      message.error('图片上传失败，请重试');
    }
  };

  /**
   * 处理服装图片变更 更新组件状态中的图片数据
   */
  const handleImageChange = (image: string | null = '', type?: ImageType, uploadSizeError?: boolean) => {
    // 设置图片尺寸校验错误状态
    setUploadSizeError(!!uploadSizeError);

    // 更新数据
    setData(pre => {
      const newImage = image || '';
      const newMaskImage = '';
      return {
        ...pre,
        image: newImage,
        imageType: type,
        // 清空蒙版图
        maskImage: newMaskImage,
      };
    });

    // 如果图片尺寸不符合要求，则显示提示信息但不进行系统抠图
    if (uploadSizeError) {
      message.error('图片尺寸不符合要求，请上传宽度不小于768px，高度不小于1024px的图片');
      return;
    }

    // 如果图片为空，则关闭涂抹弹窗
    if (image === '' || image === null) {
      // 删除或取消
      cancelOrDelete();
      return;
    }

    // 如果有上传图片，则显示涂抹弹窗
    if (image && !uploadSizeError) {
      // 开启系统涂图
      setSystemMattingStatus(SYSTEM_MATTING_STATUS.WAITING);

      // 调用loadSceneData，传入当前的image和type
      loadSceneData(image, type);
    }
  };

  // 处理预加载场景模型数据更新
  const handlePreloadedSceneModelsUpdate = useCallback((newModels: any[]) => {
    setPreloadedSceneModels(newModels);
    setOriginalSceneModels(newModels);
  }, []);

  // 选择参考图组件
  const SelectReferenceImage = ({ tag }) => {
    // 空状态展示组件
    const EmptyStateView = () => (
      <Flex vertical align="center" justify="center" className="empty-state">
        <InboxOutlined className="empty-icon" />
        <div className="text14 empty-text">未选择</div>
      </Flex>
    );

    // 参考图缩略图组件
    const ReferenceThumbnail = ({ image, index }) => (
      <div key={index} className="thumbnail-container">
        <Image
          src={image.imageUrl}
          alt={`参考图${index + 1}`}
          className="thumbnail-image"
          preview={true}
        />
        <Checkbox
          checked={image.isSelected}
          className="round-checkbox checkbox-style"
          onChange={(e) => {
            // 如果取消选中，则直接删除该图片
            if (!e.target.checked) {
              setReferencePicture(pre => pre.filter((_, idx) => idx !== index));
            }
          }}
        />
      </div>
    );

    // 添加按钮组件
    const AddButton = ({ onClick, children = null }) => (
      <div
        className="add-button"
        onClick={onClick}
      >
        <RightOutlined className="add-icon" />
        <span className="add-text">添加</span>
        {children}
      </div>
    );

    // 本地上传按钮
    const UploadButton = () => (
      <Button
        className="upload-button"
        icon={<IconFont type={'icon-shangchuan'} className="upload-icon" />}
      >
        <div className={'color-36 weight'}>本地上传</div>
      </Button>
    );

    // 底部操作栏组件
    const BottomBar = () => (
      <Flex
        justify="flex-end"
        align="center"
        gap={16}
        className="bottom-bar"
      >
        <div className="text14 select-count">
          已选 {referencePicture.length}/8
        </div>
        <div
          className="text14"
          style={{ color: 'red',cursor: 'pointer' }}
          onClick={() => {
            setReferencePicture([]);
          }}
        >
          全部删除
        </div>
      </Flex>
    );

    return (
      <Flex vertical gap={8} justify={'flex-start'} className={'work-item-container select-reference-image'}>
        {/* 一级标题 */}
        <div className={'text16 font-pf color-n weight reference-title'}>
          <span>{tag}</span>
        </div>

        {/* 参考图上传方式 */}
        <Segmented
          value={currentReferenceType}
          className="reference-segmented"
          options={['系统生成参考图', '本地上传']}
          onChange={(value) => {
            // 设置当前参考图类型
            setCurrentReferenceType(value as '系统生成参考图' | '本地上传');
            // 切换时清空参考图集合
            setReferencePicture([]);
            // 切换时关闭抽屉
            setIsOpenDrawer(false);
          }}
        />

        {/* 参考图容器 */}
        <div className="reference-container">
          {/* 系统生成参考图 - 空状态 */}
          {currentReferenceType === '系统生成参考图' && referencePicture.length === 0 && !isOpenDrawer && (
            <Flex
              justify="space-between"
              align="center"
              className="system-select-container"
              onClick={() => setIsOpenDrawer(true)}
            >
              <Flex justify="flex-start" align="center" className="system-image">
                <img src={StaticImage.basicReplace.referencePic} className="reference-image" alt="系统生成参考图" />
                <Flex vertical justify="center" align="flex-start" className="system-text-container">
                  <div className="text16" style={{ marginBottom: '8px' }}>选择参考图</div>
                  <div className="text14 color-96">可选择多张参考图</div>
                </Flex>
              </Flex>
              {/* 右侧图标 */}
              <span className="system-arrow"><RightOutlined /></span>
            </Flex>
          )}

          {/* 本地上传 - 空状态 */}
          {currentReferenceType === '本地上传' && referencePicture.length === 0 && (
            <Dragger
              style={{ height: '100%', cursor: 'pointer' }}
              {...multiImageUploadProps}
              onChange={(info) => {
                const { file, fileList } = info;

                // 只在文件上传完成或失败时进行处理
                if (file.status === 'done') {
                  if (file.response && file.response.success) {
                    // 当一个文件上传完成时，处理整个文件列表
                    handleUploadSuccess(fileList);
                  }
                } else if (file.status === 'error') {
                  handleUploadError(file.name);
                }
              }}
            >
              <UploadButton />
              <div className="text14 color-96 upload-hint">
                点击/粘贴/拖拽图片至此，支持png、jpg格式
              </div>
            </Dragger>
          )}

          {/* 展示已选择的参考图或系统生成的参考图 */}
          {(referencePicture.length !== 0 || isOpenDrawer) && (
            <div className="reference-result-box result-box">
              <Flex wrap="wrap" gap={12}>
                {/* 系统生成参考图模式下的展示 - 空状态 */}
                {currentReferenceType === '系统生成参考图' && referencePicture.length === 0 && isOpenDrawer && (
                  <EmptyStateView />
                )}

                {/* 展示已选择的参考图 */}
                {referencePicture.length > 0 && referencePicture.map((image, index) => (
                  <ReferenceThumbnail key={index} image={image} index={index} />
                ))}

                {/* 系统生成参考图模式下的添加按钮 */}
                {currentReferenceType === '系统生成参考图' && referencePicture.length > 0 && referencePicture.length < 8 && !isOpenDrawer && (
                  <AddButton onClick={() => setIsOpenDrawer(true)} />
                )}

                {/* 本地上传模式下的添加按钮 */}
                {currentReferenceType === '本地上传' && referencePicture.length < 8 && !isOpenDrawer && (
                  <Upload
                    {...multiImageUploadProps}
                    onChange={(info) => {
                      const { file, fileList } = info;

                      // 只在文件上传完成或失败时进行处理
                      if (file.status === 'done') {
                        if (file.response && file.response.success) {
                          // 当一个文件上传完成时，处理整个文件列表
                          handleUploadSuccess(fileList);
                        }
                      } else if (file.status === 'error') {
                        handleUploadError(file.name);
                      }
                    }}
                  >
                    <AddButton onClick={() => {
                    }} />
                  </Upload>
                )}
              </Flex>

              {/* 底部操作栏 */}
              {!(currentReferenceType === '系统生成参考图' && referencePicture.length === 0) && (
                <BottomBar />
              )}
            </div>
          )}
        </div>
      </Flex>
    );
  };

  // 图片尺寸选择区域组件
  const ImageSizeSection = () => {
    // 本地上传模式下的尺寸说明组件
    const LocalUploadSizeInfo = () => (
      <Flex vertical gap={4} justify={'flex-start'} className={'work-item-container'}>
        {/* 标题 */}
        <div className={'text16 font-pf color-n weight'} style={{ display: 'flex', justifyContent: 'space-between' }}>
          <span>图片尺寸</span>
        </div>
        {/* 提示信息 */}
        <div className="text14 color-96">根据参考图比例出图，不支持选择</div>
      </Flex>
    );

    // 添加固定高度容器和固定尺寸类名以防止布局跳动
    return (
      <div className="image-size-section-wrapper" style={{
        overflow: 'visible',
        position: 'relative',
      }}>
        {currentReferenceType !== '本地上传' ? (
          <ProportionBlock
            value={data.proportion}
            onChange={(value) => {
              setData(pre => {
                return {
                  ...pre,
                  proportion: value,
                };
              });
            }}
            model={undefined}
            type={type}
            isVip={vipUser}
          />
        ) : (
          // 本地上传模式 - 显示尺寸说明
          <LocalUploadSizeInfo />
        )}
      </div>
    );
  };

  // 高亮数字显示组件
  const HighlightNumber = ({ value }) => (
    <span style={{ color: '#366EF4' }}> {value} </span>
  );

  // 生成数量区域组件
  const GenerationQuantitySection = () => (
    <Flex vertical justify="flex-start" className="work-item-container">
      {/* 标题 */}
      <div className="text16 font-pf color-n weight">生成数量</div>

      {/* 数量信息展示 */}
      <div className="work-item-row margin-bottom-8" style={{ gap: 8 }}>
        共选择<HighlightNumber value={referencePicture.length} />张参考图片，
        每张参考图生成<HighlightNumber value={4} />张商拍图，
        共生成<HighlightNumber value={referencePicture.length * 4} />张商拍图。
      </div>
    </Flex>
  );

  return (
    <PageContainer className="basic-replace-container">
      {/* 主内容区域 */}
      <Flex justify={'flex-start'} align={'flex-start'} className={'row-container'}>

        {/* 左侧区域 */}
        <div className={'work-block work-block-fixed'} style={{ 
          gap: 8,
          overflowY: 'auto',
          overflowX: 'hidden',
          height: 'calc(100vh - 136px)', // 适应页面高度
          position: 'relative',
        }}>
          <Flex vertical gap={8} justify={'flex-start'}>

            {/* 上传服装图片 */}
            <UploadImageComponent
              tag={'上传1张服装实拍图'}
              smallTitle={'建议上传人台图或真人拍摄的图片，平铺图效果欠佳'}
              image={data.image}
              systemMattingStatus={systemMattingStatus}
              maskImage={data.maskImage}
              imageType={data.imageType}
              onImageChange={handleImageChange}
              uploadProps={singleImageUploadProps}
              onSmearClick={() => setShowSmearModal(true)} />

            {/* 遮罩层 */}
            <div style={{ position: 'relative', flex: 1 }}>
              {(!data.image || uploadSizeError) && (
                <div
                  style={{
                    position: 'absolute',
                    top: '-8px', // 抵消gap的影响
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'rgba(255, 255, 255, 0.8)',
                    zIndex: 10,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backdropFilter: 'blur(2px)',
                    borderRadius: '8px',
                  }}>
                  <div style={{
                    textAlign: 'center',
                    color: '#666',
                  }}>
                    <div className="text14">请先上传服装图片</div>
                  </div>
                </div>
              )}

              <Flex vertical gap={8}>
                {/* 选择模特 */}
                <div className={'work-item-group'} style={{ gap: 8, marginBottom: 0 }}>
                  {configs?.filter(item => item.configKey === 'FACE').map(config => (
                    <ElementWithTypeBlock key={'FACE'}
                      config={config}
                      moreTitle={'（非必选）'}
                      defaultType={'recent'}
                      modelId={null}
                      current={data.configs[config.id]}
                      onChange={handleFaceChange}
                      conditions={{}}
                      needExclusive={true}
                      popShowAll={true}
                      moreIcon={'icon-quanbumote'}
                      creativeType={currentType}
                      moreBg={BG_ALL_MODELS}
                      orderByType={true}
                      showConfirmFooter={false}
                      loraType={type}
                      isVipUser={vipUser} />
                  ))}
                </div>


                {/* 选择参考图 */}
                <SelectReferenceImage tag={'选择参考图'} />

                {/* 选择图片尺寸 */}
                <ImageSizeSection />

                {/* 生成数量 */}
                <GenerationQuantitySection />

              </Flex>
            </div>

          </Flex>
        </div>

        {/* 结果展示部分 */}
        <div ref={outputBlockRef}
          className={'output-block output-block-fixed'}
          style={{ width: `calc((100% - 644px))`, maxWidth: '100%' }}>

          {/* 添加抽屉组件 */}
          {isOpenDrawer &&
            <StyleLoraDrawer
              isVipUser={vipUser}
              expand={isOpenDrawer}
              currentType={currentType}
              changeExpand={setIsOpenDrawer}
              selectedReferences={referencePicture}
              preloadedSceneModels={preloadedSceneModels}
              styleDescription={styleDescription || ''}
              onPreloadedSceneModelsUpdate={handlePreloadedSceneModelsUpdate}
              maxSelectCount={8}
              onSelect={(images: SelectedItem[]) => {
                setReferencePicture(images.map(image => ({
                  imageUrl: image.image,
                  isSelected: true,
                  referenceConfig: image.referenceConfig,
                  backTags: image.backTags,
                  backExtTags: image.backExtTags,
                })));
              }}
              width={'calc(100% - 68px)'}
              className="reference-drawer"
            />
          }

          {/* 结果展示部分，多任务 */}
          <TaskOutputBlock
            sliderValue={12}
            types={[currentType]}
            ref={taskOutputRef}
            pollingTimeout={3000}
            isSelectTask={true}
          />
        </div>
      </Flex>


      {/* 底部预测组件 */}
      <footer className={'footer-bar'}>
        <div className={'fixed-tab-bar fixed-tab-bar-fixed'}>
          <div className={'fixed-tab-content'}>
            <PredictBlock creativeType={currentType}
              predictVO={predictVO} />

            <Button type="primary"
              className={'create-btn'}
              disabled={
                // 当正在提交时禁用
                commiting ||
                // 当不可保存且非充值场景时禁用
                (!canSave && !(predictVO && predictVO.needTopup))
              }
              icon={commiting ?
                <img src={MINI_LOADING_ICON}
                  width={16}
                  height={16}
                  alt={'logo'}
                  className={'loading-img'} /> : ''
              }
              onClick={handleCreate}>
              {commiting ? '任务创建中' : (predictVO && predictVO.needTopup ? '余额不足，去充值' : '生成图片')}
            </Button>
          </div>
        </div>
      </footer>


      {/* 恢复数据弹窗 */}
      <Modal
        title="恢复创作"
        open={showRestoreModal}
        maskClosable={false}
        closable={false}
        centered
        footer={[
          <Button key="discard" onClick={discardSavedFormData}>
            放弃
          </Button>,
          <Button key="continue" type="primary" onClick={restoreSavedFormData}>
            继续
          </Button>,
        ]}
      >
        <p>是否继续完成上次的创作？</p>
      </Modal>

      {/* 充值弹窗 */}
      {showTopupModal &&
        <TopupModal visible={showTopupModal} onClose={() => setShowTopupModal(false)}
          onPaySuccess={() => {
            setShowTopupModal(false);
            updatePoint('CUSTOM');
          }} />
      }

      {/* 涂抹弹窗 */}
      <Modal
        width={1284}
        centered
        title={<div style={{ fontSize: 20 }}>涂抹服装区域</div>}
        maskClosable={false}
        open={showSmearModal}
        closable={false}
        modalRender={modal => (
          <div style={{ textAlign: 'center' }}>
            {modal}
          </div>
        )}
        footer={
          <Flex gap={10} style={{
            width: 'auto',
            borderTop: '1px solid #e8e8e8',
            paddingTop: 16,
          }} justify={'flex-end'}>
            <Button style={{ width: 212 }}
              onClick={() => setShowSmearModal(false)}>
              取消
            </Button>
            <Button
              className="repair-detail-submit-button"
              type="primary"
              onClick={() => doSmearPic()}
              style={{ width: 212, justifyContent: 'center' }}>
              确认
            </Button>
          </Flex>
        }
      >
        <SmudgeCanvasComponent
          image={data.image}
          maskImage={data.maskImage}
          isErasing={isErasing}
          setIsErasing={setIsErasing}
          strokeWidth={strokeWidth}
          setStrokeWidth={setStrokeWidth}
          lines={lines}
          setLines={setLines}
          clothLines={clothLines}
          setClothLines={setClothLines}
          stageRef={stageRef}
          systemMattingStatus={systemMattingStatus}
        />
      </Modal>

    </PageContainer>
  );
};

export default BasicReplace;

