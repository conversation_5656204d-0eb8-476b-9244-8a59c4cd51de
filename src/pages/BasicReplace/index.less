@import "@/app";

@body-height: calc(100vh - @navigation-height - @tab-bar-height);
@body-width: calc(100vw - @menu-width);
@logo-demo-container-width: 200px;
@body-padding: 16px;
@body-gap: 16px;

.basic-replace-container {

  .row-container {
    width: 100%;
    height: calc(@body-height);
    background: #FFFFFF;



    .work-item-container {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      height: auto;
      border-radius: 8px;
      background: #F5F6F9;
      padding: 12px;
      gap: 8px;

      .upload-container {
        width: 100% !important;
        border: #D8D8D8 1px dashed;
        border-radius: 8px;
        padding: 10px 5px;
        background-color: #EFF0F5;

        .upload-box {
          display: flex;
          justify-content: space-around;
          align-items: center;
          width: 100%;
          height: 170px;

          .upload-box-item {
            width: 48%;
            height: 100%;
            background-color: #ffffff;
            border-radius: 8px;
          }
        }
      }


      .logo-upload-main-img {
        width: 100% !important;
        height: calc(@body-height - 228px - 24px * 6 - 12px);

        .show-image-container {
          width: 100%;
          height: 100%;
          background-color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 8px;
          border: 1px dashed #D8D8D8;

          .ant-image {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
          }

          .ant-image-img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
          }

          .ant-image-mask {
            border-radius: 8px;
          }
        }

        .logo-combine-upload-image-btn {
          position: absolute;
          right: 16px;
          top: 16px;
          height: 100%;

          .ant-btn {
            height: 32px;
            width: 32px;

            .anticon {
              font-size: 16px;
            }
          }
        }
      }


      .logo-upload {
        width: calc(@body-width / 2 - @body-padding * 2 - @logo-demo-container-width - @body-gap);
        height: 228px;
        background: #F5F6F9;
        border-radius: 8px;
      }

    }

    .reference-container {
      width: 100%;
      height: 168px;
      border-radius: 8px;
      background-color: #EFF0F5;
      border: 1px dashed #D8D8D8;
      overflow: hidden;
      

      .reference-image {
        width: 108px;
        height: 136px;
      }

      .round-checkbox {
        .ant-checkbox-inner {
          border-radius: 50% !important;
          width: 18px;
          height: 18px;
          background-color: rgba(255, 255, 255, 0.8);
          border: 1px solid #D9D9D9;
        }
        
        .ant-checkbox-checked .ant-checkbox-inner {
          background-color: #366EF4;
          border-color: #366EF4;
        }
        
        .ant-checkbox-inner::after {
          top: 45%;
        }
      }
    }
  }
}

.reference-drawer {
  :global {
    .ant-drawer-content {
      background-color: #fff;
      border-radius: 8px 0 0 8px;
      box-shadow: -4px 0 12px rgba(0, 0, 0, 0.15);
      z-index: 999999;
    }
    
    .ant-drawer-body {
      padding: 0;
    }
  }
}

// SelectReferenceImage 组件样式
.select-reference-image {
  // 标题样式
  .reference-title {
    display: flex;
    justify-content: space-between;
  }
  
  // 分段控件样式
  .reference-segmented {
    background-color: #E5E7EE;
    width: fit-content;
  }
  
  // 系统生成参考图样式
  .system-select-container {
    height: 100%;
    cursor: pointer;
    padding: 16px;
  }
  
  .system-image {
    height: 100%;
  }
  
  .system-text-container {
    height: 100%;
    margin-left: 24px;
  }
  
  .system-arrow {
    cursor: pointer;
    font-size: 24px;
    color: #727375;
  }
  
  // 本地上传样式
  .upload-button {
    width: 132px;
    height: 38px;
    border: 1px solid #B5C7FF;
  }
  
  .upload-icon {
    font-size: 16px;
    color: #366EF4;
  }
  
  .upload-hint {
    margin-top: 8px;
  }
  
  // 参考图结果容器
  .result-box {
    padding: 8px;
    position: relative;
    min-height: 168px;
  }
  
  // 参考图缩略图
  .thumbnail-container {
    position: relative;
    width: 66px;
    height: 66px;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid #1890ff;
  }
  
  .thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .checkbox-style {
    position: absolute;
    top: 4px;
    right: 4px;
  }
  
  // 添加按钮样式
  .add-button {
    width: 66px;
    height: 66px;
    border-radius: 8px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  
  .add-icon {
    font-size: 20px;
    color: #727375;
  }
  
  .add-text {
    margin-top: 4px;
    font-size: 12px;
    color: #727375;
  }
  
  // 底部操作栏
  .bottom-bar {
    position: absolute;
    bottom: 8px;
    right: 8px;
  }
  
  .select-count {
    color: #366EF4;
  }
  
  .delete-button {
    color: #366EF4;
    cursor: pointer;
  }
  
  // 空状态
  .empty-state {
    width: 100%;
    padding: 40px 0;
  }
  
  .empty-icon {
    font-size: 24px;
    color: #4080FF;
  }
  
  .empty-text {
    color: #4080FF;
    margin-top: 8px;
  }
}


// 图片尺寸选择器相关样式
.stable-size-selector {
  transition: none !important; // 强制禁用所有过渡效果，防止高度变化时的动画
  height: 172px !important;
  min-height: 172px !important;
  box-sizing: border-box !important;
  transform: translate3d(0, 0, 0); // 启用硬件加速
  will-change: transform; // 提示浏览器此元素会发生变化
}

.image-size-section-wrapper {
  position: relative;
  transition: none !important; // 强制禁用过渡效果
  margin: 0 !important; // 强制移除所有外边距
  padding: 0 !important; // 强制移除所有内边距
  height: 172px !important;
  min-height: 172px !important;
  box-sizing: border-box !important;
  overflow: visible !important;
  transform: translate3d(0, 0, 0); // 启用硬件加速
  will-change: transform; // 提示浏览器此元素会发生变化
}

.reference-upload-container {
  height: 172px !important;
  min-height: 172px !important;
  box-sizing: border-box !important;
  transform: translate3d(0, 0, 0); // 启用硬件加速
  will-change: transform; // 提示浏览器此元素会发生变化
}

// 确保工作项容器在尺寸选择器上下文中有固定高度
.work-item-container.select-reference-image + .image-size-section-wrapper .work-item-container {
  height: 172px !important;
  min-height: 172px !important;
  box-sizing: border-box !important;
  transition: none !important;
}