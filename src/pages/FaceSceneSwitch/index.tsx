import React, { useEffect, useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Flex, Button, Upload, UploadProps, InputNumber, message } from 'antd';
import IconFont from '@/components/IconFont';
import { DeletableImage } from '@/components/Common/ImageComponent';
import { ElementConfig, getElementConfig } from '@/services/ElementController';
import ElementWithTypeBlock from '@/components/Creative/ElementWithTypeBlock';
import {
  BG_ALL_MODELS, EXPERIENCE_POINT,
  IMAGE_POINT,
  IMAGE_POINT_CHANGE_EVENT,
  MINI_LOADING_ICON,
  UPLOAD_URL,
} from '@/constants';
import ImageSelector from '@/components/Creative/ImageSelector';
import PredictBlock from '@/components/Creative/PredictBlock';
import { predict, PredictVO, queryImagePoint } from '@/services/PointController';
import { CreativeStatus, faceSceneSwitch, LoraType } from '@/services/CreativeController';
import { getUserInfo } from '@/utils/utils';
import OutputBlock, { OutputBlockRef } from '@/components/Creative/OutputBlock';
import TaskOutputBlock, { TaskOutputBlockRef } from '@/components/Creative/TaskOutputBlock';
import { isVipUser } from '@/services/UserController';
import TopupModal from '@/pages/Topup/Topup';
import '@/pages/FaceSceneSwitch/index.less'
import { compressFile, convertPngToJpg, convertToJPG } from '@/utils/imageUtils';
import { LoadingOutlined } from '@ant-design/icons';
import FaceExpressionBlock from '@/components/Creative/FaceExpressionBlock';

const { Dragger } = Upload;

export const UploadImage = ({tag, image, onImageChange, onShowImageSelector, uploadProps}) => {
  return (
    <Flex vertical gap={8} justify={'flex-start'} className={'work-item-container'}>
      <div className={'text16 font-pf color-n weight'}>{tag}</div>
      {!image &&
        <Dragger className={'logo-upload logo-upload-main-img upload-main-img-fix'} {...uploadProps}  >
          <Flex vertical gap={8}>
            <Flex gap={16} align={'center'} justify={'center'}>
              <Button type={'primary'} style={{ width: 196, height: 38 }}
                      icon={<IconFont type={'icon-lishirenwu_weixuanzhong1'} style={{ fontSize: 16 }} />}
                      onClick={onShowImageSelector}>
                从历史创作中选择
              </Button>
              <Button style={{ width: 132, height: 38, border: '1px solid #B5C7FF' }}
                      icon={<IconFont type={'icon-shangchuan'} style={{ fontSize: 16, color: '#366EF4' }} />}>
                <div className={'color-36 weight'}>本地上传</div>
              </Button>
            </Flex>
            <div className="text14 color-96">点击/粘贴/拖拽图片至此，支持png、jpg格式</div>
          </Flex>
          <Flex style={{height: "140px"}}><span></span></Flex>
        </Dragger>
      }
      {image &&
        <Flex gap={14} justify={'center'} className={'logo-upload-main-img'} style={{ position: 'relative', height: 380 }}>
          <DeletableImage className={'main-img-fix'} imgUrl={image}
                          onDelete={() => onImageChange('')}
                          needWatermark={false} />
          <Flex gap={8} className={'logo-combine-upload-image-btn'}>
            <Button icon={<IconFont type={'icon-icon_shanchu'} style={{ fontSize: 16 }} />}
                    onClick={() => onImageChange('')} style={{ height: 38, width: 38 }} />
            <Upload {...uploadProps}>
              <Button style={{ height: 38, width: 104 }}>重新上传</Button>
            </Upload>
          </Flex>
        </Flex>
      }
    </Flex>
  )
}

const FaceSceneSwitch: React.FC<{ type: LoraType }> = ({type = 'CUSTOM'}) => {
  const userInfo = getUserInfo();
  const emptyData = {
    loraId: null,
    image: '',
    configs: {},
    imageNum: userInfo?.roleType === 'OPERATOR' ? 4 : 4,
    customScene: '',
    customSceneImg: '',
  }

  const [configs, setConfigs] = useState<ElementConfig[]>();
  const [data, setData] = useState(emptyData);
  const [vipUser, setVipUser] = useState(false);
  const [showImageSelector, setShowImageSelector] = useState<boolean>(false);
  const [showSceneImgSelector, setShowSceneImgSelector] = useState<boolean>(false);
  const [sceneKey, setSceneKey] = useState<string>('');
  const [faceKey, setFaceKey] = useState<string>('');

  const outputBlockRef = useRef(null);
  const taskOutputRef = useRef<TaskOutputBlockRef>(null);
  const[sliderValue, setSliderValue] = useState(12);
  const [showTopupModal, setShowTopupModal] = useState(false);
  const [commiting, setCommiting] = useState(false);

  const [imagePoint, setImagePoint] = useState(0);
  const [canSave, setCanSave] = useState(false);
  const [predictVO, setPredictVO] = useState<PredictVO | null>(null);

  useEffect(() => {
    init();
  }, []);

  useEffect(() => {
    setCanSave(isCanSave(data));
    predict('FACE_SCENE_SWITCH', data.imageNum, null)
      .then(result => {
        if (result) {
          setPredictVO(result);
        }
      });
  }, [data]);

  const fetchElementConfig = async (onlyExclusive = true) =>{
    const value = await getElementConfig('FACE_SCENE_SWITCH', 'ALL', onlyExclusive);

    if (value) {
      setConfigs(value);
      value.forEach(item => {
        data.configs[item.id] = null;
      });
      const sceneKey = value.find(item => item.configKey === 'SCENE');
      const faceKey = value.find(item => item.configKey === 'FACE');
      if (sceneKey) {
        setSceneKey(sceneKey.id.toString());
      }
      if (faceKey) {
        setFaceKey(faceKey.id.toString())
      }
    }
    return value || [];
  }

  const fetchElementConfigMore = async (): Promise<ElementConfig[]> => {
    return await fetchElementConfig(false);
  }

  async function init() {
    fetchElementConfig();
    updatePoint(type);
  }

  const handleCreate = () => {
    if (predictVO && predictVO.needTopup) {
      setShowTopupModal(true);
      return;
    }
    if (commiting) {
      return;
    }
    if (!isCanSave(data)) {
      message.warning('数据异常, 请刷新重试');
      return;
    }
    setCommiting(true);
    async function start(data) {
      const result = await faceSceneSwitch(data);
      if (type === 'SYSTEM') {
        return imagePoint >= data.imageNum;
      }
      setCommiting(false);
      //更新图片生成数量
      updatePoint(type);
      if (!result) {
        console.log('生成图片失败', result);
        return;
      }

      if (userInfo?.roleType === 'OPERATOR') {
        message.success('开始生成图片');
      }
      taskOutputRef.current?.refresh();
    }
    start(resetData(data));
  }
  const resetData = (data) => {
    const {configs, ...others} = data;
    const newConfigs = {};
    for (let key in configs) {
      if (configs[key]) {
        newConfigs[key] = configs[key].map(item => item.id);
      }
    }
    return {...others, configs: newConfigs}
  }

  const uploadProps :UploadProps = {
    accept: 'image/png, image/jpeg',
    showUploadList: false,
    action: UPLOAD_URL,
    multiple: false,
    name: 'file',
    onChange(info) { handleFileChange(info, handleImageChange); },
    beforeUpload: async (file) => {
      let processedFile :any = file;
      // 压缩图片文件
      processedFile = await compressFile(processedFile);
      // 转成 JPG
      return convertToJPG(processedFile);
    },
  };

  const uploadBgProps :UploadProps = {
    ... uploadProps,
    onChange(info) { handleFileChange(info, handleBgImgChange);},
    beforeUpload: async (file) => {
      let processedFile :any = file;
      // 压缩图片文件
      processedFile = await compressFile(processedFile);
      // 转成 JPG
      return convertToJPG(processedFile);
    },
  }
  const handleFileChange = (info, handleChange) => {
    const {file} = info;
    // 检查文件是否上传成功
    if (file.status === 'done' && file.response && file.response.success) {
      // 处理上传成功的文件
      handleChange(file.response.data);
    } else if (file.status === 'error') {
      // 处理上传错误的文件
      message.error('上传图片异常，请重试');
    }
  };


  const handleShowImageSelector = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    setShowImageSelector(true);
  };
  const handleShowBgSelector = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    setShowSceneImgSelector(true);
  };
  const handleFaceChange = (configId: number, value: Array<ElementConfig> | null, isPreset?: boolean) => {
    if (isPreset) {
      data.configs[configId] = value;
      return;
    }
    setData((pre) => {
      const newConfigs = {...pre.configs};
      newConfigs[configId] = value;
      return {
        ...pre,
        configs: newConfigs,
      };
    });
  };
  const handleImageChange = (image: string | null = '') => {
    setData(pre => {
      const newImage = image || '';
      return {
        ...pre,
        image: newImage,
      }
    })
  };
  const handleBgImgChange = (image: string | null = '') => {
    setData(pre => {
      const newConfig = { ...pre.configs };
      delete newConfig[parseInt(sceneKey)];
      const newImage = image || '';
      return {
        ...pre,
        configs: newConfig,
        customScene: '',
        customSceneImg: newImage,
      }
    })
  };
  const handleNumberChange = (value: number | null) => {
    setData( pre => {
      const num = value && value > (type === 'CUSTOM' ? 4 : 10) ? value : (type === 'CUSTOM' ? 4 : 10);
      return {
        ...pre,
        imageNum: num,
      }
    })
  };
  const isCanSave = (data) => {
    const result :boolean = data.image && data.imageNum && data.imageNum > 0;
    if (!result) {
      return false;
    }
    const hasFace = data.configs[faceKey] !== null;
    const hasScene = !!(data.configs[sceneKey]) || data.customScene.length > 0 || data.customSceneImg.length > 0;
    if (!(hasFace || hasScene)) {
      return false;
    }
    if (type === 'SYSTEM') {
      return imagePoint >= data.imageNum;
    }
    return predictVO !== null && !predictVO.needTopup;
  };

  const updatePoint = (type) => {
    queryImagePoint().then(
      result => {
        if (result) {
          const imagePoint = type === 'CUSTOM' ? result.imagePoint : result.experiencePoint;
          setImagePoint(imagePoint);
          localStorage.setItem(IMAGE_POINT, result.imagePoint.toString());
          localStorage.setItem(EXPERIENCE_POINT, result.experiencePoint.toString());
          window.dispatchEvent(new Event(IMAGE_POINT_CHANGE_EVENT));
        }
      }
    );
    isVipUser().then(res => setVipUser(!!res));
  };



  return (
    <PageContainer>
      <Flex justify={'flex-start'} align={'flex-start'} className={'row-container'}>
        <div className={'work-block work-block-fixed'} style={{ gap: 8 }}>
          <Flex vertical gap={8} justify={'flex-start'}>
            {/* 用户上传代修改的图 */}
            <UploadImage tag={"上传原图"} image={data.image} onImageChange={handleImageChange} onShowImageSelector={handleShowImageSelector} uploadProps={uploadProps}  />
            <div className={'work-item-group'} style={{ gap: 16 }}>
              {/*模特配置*/}
              {configs?.filter(item => item.configKey === 'FACE').map(config => (
                <ElementWithTypeBlock key={'FACE'} config={config} defaultType={'recent'} modelId={null}
                                     current={data.configs[config.id] || []} onChange={handleFaceChange}
                                     conditions={{}} needExclusive={true} needFavor popShowAll={true} moreIcon={'icon-quanbumote'}
                                     moreBg={BG_ALL_MODELS} orderByType={true} loraType={type} isVipUser={vipUser}
                                     experienceModelOpenCfg={null}
                                     showConfirmFooter={true}
                                     foot={<FaceExpressionBlock changeData={setData} data={data} />} />
              ))}
              {/*上传场景*/}
              {userInfo && userInfo.roleType !== 'MERCHANT' &&
                <UploadImage tag={"上传背景参考图"} image={data.customSceneImg} onImageChange={handleBgImgChange}
                            onShowImageSelector={handleShowBgSelector} uploadProps={uploadBgProps} />
              }
            </div>
          </Flex>
          <Flex vertical gap={8} justify={'flex-start'} className={'work-item-container'}>
            <div className={'work-item-container'}>
              <div className={'text16 font-pf color-n weight'}>生成数量</div>
              <div className={'work-item-row margin-bottom-8'} style={{ gap: 8 }}>
                <InputNumber min={type === 'CUSTOM' ? 4 : 10} max={userInfo?.roleType === 'OPERATOR' ? 30 : 20}
                             defaultValue={userInfo?.roleType === 'OPERATOR' ? 10 : 20}
                             value={data.imageNum}
                             status={((type === 'SYSTEM' && data.imageNum > imagePoint) ? 'error' : '')}
                             onChange={handleNumberChange} className={'face-scene-work-item-input'} />
                <div className={'margin-left-8'}>最大生成数量{userInfo?.roleType === 'OPERATOR' ? 30 : 20}</div>

                {userInfo?.roleType === 'OPERATOR' &&
                  <>
                    <Button type={data.imageNum === 5 ? 'primary' : 'dashed'} onClick={() => handleNumberChange(5)}
                            style={{ width: 80 }}>5</Button>
                    <Button type={data.imageNum === 10 ? 'primary' : 'dashed'} onClick={() => handleNumberChange(10)}
                            style={{ width: 80 }}>10</Button>
                    <Button type={data.imageNum === 20 ? 'primary' : 'dashed'} onClick={() => handleNumberChange(20)}
                            style={{ width: 80 }}>20</Button>
                  </>
                }
              </div>
            </div>
          </Flex>
        </div>

        <div ref={outputBlockRef}
             className={'output-block output-block-fixed'}
             style={{ width: `calc((100% - 644px - 17px) * ${sliderValue / 12})`, maxWidth: '100%' }}>

          <TaskOutputBlock sliderValue={sliderValue} types={['FACE_SCENE_SWITCH']}
                             ref={taskOutputRef} pollingTimeout={3000}
                             changeSliderValue={setSliderValue} />
        </div>
      </Flex>
      <footer className={'footer-bar'}>
        <div className={'fixed-tab-bar fixed-tab-bar-fixed'}>
          <div className={'fixed-tab-content'} >
            <PredictBlock creativeType={'FACE_SCENE_SWITCH'} predictVO={predictVO} data={data} type={type}
                          imagePoint={imagePoint} />
            <Button type="primary" className={'create-btn'}
                    disabled={!((canSave && !commiting) || (predictVO?.needTopup))}
                    icon={commiting ? <LoadingOutlined style={{fontSize: 16, color: 'fff'}} /> : '' }
                    onClick={handleCreate}> {commiting ? '生成中' : (predictVO?.needTopup ? '余额不足，去充值' : '生成图片')}</Button>
          </div>
        </div>
      </footer>

      {showImageSelector &&
        <ImageSelector value={data.image ? [data.image] : []} onFinish={(value) => {
          handleImageChange(value && value.length > 0 ? value[0] : null);
          setShowImageSelector(false);
        }} onCancel={() => setShowImageSelector(false)} maxChoose={1} />
      }
      {showSceneImgSelector &&
        <ImageSelector value={data.customSceneImg ? [data.customSceneImg] : []} onFinish={(value) => {
          handleBgImgChange(value && value.length > 0 ? value[0] : null);
          setShowSceneImgSelector(false);
        }} onCancel={() => setShowSceneImgSelector(false)} maxChoose={1} />
      }
      {showTopupModal &&
        <TopupModal visible={showTopupModal} onClose={() => setShowTopupModal(false)}
                    onPaySuccess={() => {
                      setShowTopupModal(false);
                      updatePoint('CUSTOM');
                    }} />
      }
    </PageContainer>
  )
}
export default FaceSceneSwitch;