import React, { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import debounce from 'lodash/debounce';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Button,
  Checkbox,
  Flex,
  Input,
  message,
  Segmented,
  Tabs,
  Tooltip,
  Select,
  Dropdown,
  Image,
  Upload,
  UploadProps,
  Popconfirm,
  Modal,
} from 'antd';
import {
  ALL_LORAS_NEW,
  BG_ALL_MODELS,
  DEBOUNCE_DELAY,
  EXPERIENCE_POINT,
  IMAGE_POINT,
  IMAGE_POINT_CHANGE_EVENT,
  IS_TRIAL_ACCOUNT,
  MINI_LOADING_ICON,
  MORE_LORA_ICON,
  NEED_GUIDE,
  UPLOAD_URL,
} from '@/constants';
import './index.less';
import '@/app.less';
import { capitalizeFirstLetter, deepCopy } from '@/utils/utils';
import { ALLBizTypes, BizType, CreativeStatus, fixedPostureCreation, LoraType } from '@/services/CreativeController';
import {
  emptyClothCollocation,
  getClothMarkIcon,
  isClothMarkedColor,
  MaterialModel,
  queryMaterialModelByPage,
  querySystemModelList,
} from '@/services/MaterialModelController';
import { useLocation, useNavigate } from 'react-router-dom';
import { predict, PredictVO, queryImagePoint } from '@/services/PointController';
import {
  AllAgeRanges,
  CreationClothTypeCheckBoxOptions,
  ElementConfig,
  getElementConfig,
  getBasicChangeConfig,
  queryHasShowImageChildren,
} from '@/services/ElementController';
import NewUserGuide from '@/components/NewUserGuide';
import Flow from '@/components/Flow/flow';
import LoraSelector from '@/components/LoraSelector';
import PredictBlock from '@/components/Creative/PredictBlock';
import TopupModal from '@/pages/Topup/Topup';
import ElementWithTypeBlock from '@/components/Creative/ElementWithTypeBlock';
import ProportionBlock from '@/components/Creative/ProportionBlock';
import MasterSelector from '@/components/MasterSelector';
import { queryCustomerMusePoint, queryLoras4Distributor } from '@/services/DistributorController';
import ClothCollocationBlock from '@/components/Creative/ClothCollocationBlock';
import IconFont from '@/components/IconFont';
import TaskOutputBlock, { TaskOutputBlockRef } from '@/components/Creative/TaskOutputBlock';
import ClothColorSelector from '@/components/Creative/ClothColorSelector';
import OutputBlock, { OutputBlockRef } from '@/components/Creative/OutputBlock';
import { ExperienceModelOpenDetail, queryAllExperienceModelOpenCfg } from '@/services/SystemController';
import { isVipUser, queryUserProfileByKey, setUserProfileByKey } from '@/services/UserController';
import FaceExpressionBlock from '@/components/Creative/FaceExpressionBlock';
import { InboxOutlined, RightOutlined } from '@ant-design/icons';
import { StaticImage } from '@/assets/images/oss/StaticImage';
import StyleLoraDrawer, { SelectedItem } from '@/components/BasicReplace/StyleLoraDrawer';
import FavoriteTemplateModal from '@/components/Favorite/FavoriteTemplateModal';
import { create as createFixedCreativeTemplate } from '@/services/FixedCreativeTemplateController';
import SceneAccessoriesBlock from '@/components/Creative/SceneAccessoriesBlock';
import ClothColorBlock from '@/components/Creative/ClothColorBlock';

const { Dragger } = Upload;

interface CreativeProps {
  type: LoraType;
  bizType?: BizType;
}


// 当前类型
const currentType = 'FIXED_POSTURE_CREATION';

const CREATIVE_IMAGE_NUM = 'creative_image_num';

const BIZ_TYPE_CLASS_MAP = {
  'LOOK': {
    tabs: 'creation-scene-header-look',
    outputBg: 'task-image-bar-look',
    outputBgImg: 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/img-look.png',
  },
  'REAL-SCENE-SHOOTING': {
    tabs: 'creation-scene-header-real-scene-shooting',
    outputBg: 'task-image-bar-real-scene-shooting',
    outputBgImg: 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/img-real-scene-shooting.png',
  },
  'MODEL-SHOW': {
    tabs: 'creation-scene-header-model-show',
    outputBg: 'task-image-bar-model-show',
    outputBgImg: 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/img-model-show.png',
  },
};

export const removeFaceConfig = (current: {}, logoPosition: string, configs: Array<ElementConfig>) => {
  if (!logoPosition || logoPosition !== 'back') {
    return;
  }

  let faceId = '';
  configs.forEach(config => {
    if (config.configKey === 'FACE') {
      faceId = config.id + '';
    }
  });

  Object.keys(current).forEach(key => {
    if (key === faceId) {
      delete current[key];
    }
  });
};

function isCanSave(data: any, imagePoint: number, type: LoraType, predictVO: PredictVO | null, sceneKey: string, referencePicture?: any[], configs?: any[]) {
  // 找到模特配置的ID
  const faceConfig = configs?.find(item => item.configKey === 'FACE');
  const faceConfigId = faceConfig?.id;

  // 基础条件：必须有服装、比例、图片数量大于0、至少一张参考图
  const basicConditions = data.loraId && data.proportion && data.imageNum > 0
    && referencePicture && referencePicture.length > 0;

  // 检查模特是否已选择（只检查FACE配置项）
  const hasFaceSelected = faceConfigId ? data.configs[faceConfigId] !== null && data.configs[faceConfigId] !== undefined : false;

  if (!basicConditions || !hasFaceSelected) {
    return false;
  }

  if (type === 'SYSTEM') {
    return imagePoint >= data.imageNum;
  }

  return predictVO !== null && !predictVO.needTopup;
}

export const resetData = (data, logoPosition, dataConfigs) => {
  const { configs, ...others } = data;

  removeFaceConfig(configs, logoPosition, dataConfigs);

  const newConfigs = {};
  for (let key in configs) {
    if (configs[key]) {
      newConfigs[key] = configs[key].map(item => item.id);
    }
  }

  return { configs: newConfigs, ...others };
};

const useMultiTask = true;
const FixedPostureCreative: React.FC<CreativeProps> = ({ type = 'CUSTOM' }) => {
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const isTrialAccount = sessionStorage.getItem(IS_TRIAL_ACCOUNT) === 'Y';

    const emptyData = {
      loraId: null,
      configs: {},
      imageNum: 1,
      proportion: isTrialAccount && type === 'CUSTOM' ? 'THREE_FOUR_LG_N' : 'P_1152_1536',
      position: 'front view',
      wholeBody: true,
      bodyType: 'whole body',
      clothCollocation: emptyClothCollocation,
      colorIndex: null,
      clothColor: null,
      enableAntiBlurLora: false,
      enableNewModel: true,
      customScene: '',
      expression: '',
      bizType: 'ALL',
      useSceneAccessories: 'N',
      onlyExperimental: false,
    };

    const [sliderValue, setSliderValue] = useState(12);
    const [data, setData] = useState(emptyData);
    const [canSave, setCanSave] = useState(false);
    const [loraList, setLoraList] = useState<Array<MaterialModel>>([]);
    const [configs, setConfigs] = useState<Array<ElementConfig>>([]);
    const [status, setStatus] = useState<CreativeStatus>('INIT');
    const [imagePoint, setImagePoint] = useState(0);
    const [isOwner, setIsOwner] = useState(true);
    //当前选择的场景分类code，默认选中居家生活
    const [showGuide, setShowGuide] = useState(false);
    const [showFlowSteps, setShowFlowSteps] = useState(false);
    const [hasMoreLora, setHasMoreLora] = useState(false);
    const [showLoraSelector, setShowLoraSelector] = useState(false);
    const [predictVO, setPredictVO] = useState<PredictVO | null>(null);
    const [showTopupModal, setShowTopupModal] = useState(false);
    const [loraClothType, setLoraClothType] = useState('');
    const [sceneClothType, setSceneClothType] = useState('');
    const [searchKey, setSearchKey] = useState<string | null>(null);
    const [searchValue, setSearchValue] = useState<string>(''); // 用于输入框显示的值
    const [searchClothType, setSearchClothType] = useState<string>();
    const [searchAgeRange, setSearchAgeRange] = useState<string>();
    const [commiting, setCommiting] = useState(false);
    const [experienceModelOpenCfg, setExperienceModelOpenCfg] = useState<Array<ExperienceModelOpenDetail>>([]);
    const [vipUser, setVipUser] = useState(false);
    const [needShowWholeBody, setNeedShowWholeBody] = useState(false);
    const [typeSelectOpen, setTypeSelectOpen] = useState(false);
    const [ageSelectOpen, setAgeSelectOpen] = useState(false);

    const [principalId, setPrincipalId] = React.useState<null | number>(null);
    const [bizType, setBizType] = useState<BizType>('ALL');

    // 当前选中的服装
    const [currentSelectedLora, setCurrentSelectedLora] = useState<any | null>(null);

    // 防抖搜索函数
    const debouncedSearchKey = useCallback(
      debounce((value: string) => {
        setSearchKey(value || null);
      }, DEBOUNCE_DELAY),
      []
    );

    // 参考图片列表
    const [referencePicture, setReferencePicture] = useState<{
      imageUrl: string,// 图片地址
      isSelected: boolean,// 是否选中 
      referenceConfig?: string | any,// 参考图配置
      backTags?: string,// 背景标签
      backExtTags?: string,// 背景扩展标签
      loraId?: number,// 场景ID，用作loraId
      loraPath?: string,// lora路径，从extInfo.loraPath获取
      originalImageUrl?: string,// 原始图片地址
    }[]>([]);

    // 当前参考图类型
    const [currentReferenceType, setCurrentReferenceType] = useState<'系统生成参考图' | '本地上传' | '我收藏的模板'>('系统生成参考图');

    // 是否开启抽屉
    const [isOpenDrawer, setIsOpenDrawer] = useState(false);

    // 添加收藏模板弹窗状态
    const [showFavoriteModal, setShowFavoriteModal] = useState(false);

    // 添加完善收藏信息弹窗状态
    const [showCollectionInfoModal, setShowCollectionInfoModal] = useState(false);
    const [collectionTemplateName, setCollectionTemplateName] = useState('');

    // 添加新的状态来存储预加载数据
    const [preloadedSceneModels, setPreloadedSceneModels] = useState<any[]>([]);
    const [styleDescription, setStyleDescription] = useState<string>('');
    // 添加原始数据状态
    const [originalSceneModels, setOriginalSceneModels] = useState<any[]>([]);

    // 添加场景配置状态
    const [sceneConfig, setSceneConfig] = useState<ElementConfig | null>(null);

    const step1Ref = useRef(null);
    const step2Ref = useRef(null);
    const step3Ref = useRef(null);
    const outputBlockRef = useRef(null);

    const outputRef = useRef<OutputBlockRef>(null);
    const taskOutputRef = useRef<TaskOutputBlockRef>(null);

    //从其它页面跳转过来的，链接中指定modelId & isOwner
    const [loraIdInQueryParam, setLoraIdInQueryParam] = useState<string | null>('');
    const [sceneKey, setSceneKey] = useState<string>('');

    const predictLora = async (loraId: number | null, imageNum: number, proportionType: string | null = null) => {
      if (loraId === null) {
        setPredictVO(null);
        return;
      }
      if (imageNum === null || type === 'SYSTEM') {
        return;
      }
      predict('FIXED_POSTURE_CREATION', imageNum, loraId, false, proportionType).then((res) => {
        if (res) {
          setPredictVO(res);
        }
      });
    };

    // 服装选择
    const handleLoraChange = (value: React.SetStateAction<number>) => {
      if (value === data.loraId) {
        return;
      }
      setPredictVO(null);
      const copy = deepCopy(data);
      copy.loraId = value;

      // 立即清空模特和场景的选择，防止不匹配的情况
      const faceConfig = configs.find(config => config.configKey === 'FACE');
      const sceneConfig = configs.find(config => config.configKey === 'SCENE');

      if (faceConfig && copy.configs[faceConfig.id]) {
        copy.configs[faceConfig.id] = null;
      }

      if (sceneConfig && copy.configs[sceneConfig.id]) {
        copy.configs[sceneConfig.id] = null;
      }

      setData(copy);
    };

    const handleFaceChange = (configId: number, value: Array<ElementConfig> | null, isPreset?: boolean) => {
      if (isPreset) {
        data.configs[configId] = value;
        return;
      }
      const copy = deepCopy(data);
      copy.configs[configId] = value;
      setData(copy);

      if (value && value.length > 0) {
        setSceneClothType(getSceneClothType(value[0]));
      }
    };

    const handleSceneChange = (configId: number, value: Array<ElementConfig> | null, isPreset?: boolean, customScene?: string) => {
      if (isPreset) {
        data.configs[configId] = value;
        return;
      }
      setData((pre) => {
        const newConfigs = { ...pre.configs };
        let newCustomScene = pre.customScene;
        if (value === null) {
          delete newConfigs[configId];
          newCustomScene = customScene || '';
        }
        if (!customScene || customScene.length === 0) {
          newConfigs[configId] = value;
          newCustomScene = '';
        }
        return {
          ...pre,
          configs: newConfigs,
          customScene: newCustomScene,
        };
      });
    };

    const handleProportionChange = (value: string) => {
      data.proportion = value;

      const copy = deepCopy(data);
      copy.proportion = value;
      setData(copy);
    };

    const handleChangeColor = (value: number | null | undefined) => {
      const copy = deepCopy(data);
      copy.colorIndex = value;
      setData(copy);
    };

    const handleChangeBizType = (bizType) => {
      setBizType(bizType);
      setData({ ...data, bizType });
    };

    function changePrincipal(id: number) {
      setPrincipalId(id);
      if (!id) {
        setImagePoint(0);
        return;
      }
      queryCustomerMusePoint({ customerMasterId: id }).then(res => {
        if (res && res.imagePoint !== null) {
          setImagePoint(res.imagePoint);
        }
      });
    }

    const getSceneClothType = (face: ElementConfig) => {
      if (!loraClothType) {
        return undefined;
      }
      // 非男女同款的直接返回
      if (loraClothType !== 'unisex') {
        return capitalizeFirstLetter(loraClothType);
      }
      const type = face.type.find(t => t === 'male-model' || t === 'female-model');
      if (!type) {
        return capitalizeFirstLetter(loraClothType);
      }

      return capitalizeFirstLetter(type?.split('-')[0]);
    };

    const navigate = useNavigate();
    const handleUploadMaterial = () => {
      navigate('/upload');
    };

    const handleCreate = () => {
      if (predictVO && predictVO.needTopup) {
        setShowTopupModal(true);
        return;
      }

      if (commiting || !canSave || ((status !== 'INIT' && status !== 'FINISHED') && userInfo.roleType !== 'OPERATOR')) {
        return;
      }

      setCommiting(true);

      async function start(data) {
        const result = await fixedPostureCreation(data);
        setCommiting(false);
        // 更新用户常用图片数
        if (type === 'CUSTOM') {
          setUserProfileByKey({ key: CREATIVE_IMAGE_NUM, value: data.imageNum });
        }

        //更新图片生成数量
        updatePoint(type);
        updateLoraPoint();
        // 使用实际提交的图片数量进行预测更新
        const actualImageNum = referenceInfoList.length > 0 ? referenceInfoList.length : data.imageNum;
        predictLora(data.loraId, actualImageNum, data.proportion);
        if (!result) {
          console.log('生成图片失败', result);
          return;
        }

        if (userInfo.roleType === 'OPERATOR') {
          message.success('开始生成图片');
        }

        // 关闭抽屉
        setIsOpenDrawer(false);

        if (!useMultiTask) {
          const originStatus = status;
          //开始轮询状态,后台如果已经在执行和轮询了，则不再进行轮询
          if (originStatus !== 'QUEUE' && originStatus !== 'PROCESSING') {
            //将状态更改为提交状态
            setStatus(result.status);
            // @ts-ignore
            outputRef.current?.polling(result.id);
          }
        } else {
          taskOutputRef.current?.refresh();
        }
      }

      // processClothType(loraClothType, data);

      let adjust = {};
      if (isTrialAccount) {
        adjust = { enableNewModel: true };
      }

      // 构建参考图信息列表
      const referenceInfoList = referencePicture
        .filter(image => image.isSelected) // 只包含选中的参考图
        .map(image => ({
          imageUrl: image.imageUrl,
          referenceConfig: image.referenceConfig || {}, // 参考图配置，如果为空则传递空对象
          backTags: image.backTags || '', // 背景标签
          backExtTags: image.backExtTags || '', // 背景扩展标签
          loraId: image.loraId || 0, // 场景ID，用作loraId
          loraPath: image.loraPath || (image.referenceConfig?.loraPath || ''), // lora路径，优先使用loraPath字段，否则从referenceConfig中获取
          originalImageUrl: image.originalImageUrl || (image.referenceConfig?.styleImage || ''), // 原始图片地址，优先使用originalImageUrl字段，否则从referenceConfig中获取styleImage
        }));

      // 根据参考图数量调整imageNum参数
      const adjustedImageNum = referenceInfoList.length > 0 ? referenceInfoList.length : data.imageNum;

      // 将参考图信息添加到请求数据中
      const dataWithReferences = {
        ...data,
        ...adjust,
        imageNum: adjustedImageNum, // 使用调整后的图片数量
        referenceInfoList,
      };

      start(resetData(dataWithReferences, null, configs));
    };

    const updateLoraPoint = () => {
      if (!predictVO || !predictVO.modelPoint || predictVO.modelPoint <= 0) {
        return;
      }

      loraList.forEach((item) => {
        if (item.id === data.loraId && item.modelPoint) {
          // @ts-ignore
          item.modelPoint = item.modelPoint - predictVO.modelPoint;
        }
      });
      setLoraList(loraList);
    };

    const location = useLocation();
    const updatePoint = (type) => {
      queryImagePoint().then(
        (result) => {
          if (result) {
            const imagePoint = type === 'CUSTOM' ? result.imagePoint : result.experiencePoint;
            setImagePoint(imagePoint);
            localStorage.setItem(IMAGE_POINT, result.imagePoint.toString());
            localStorage.setItem(EXPERIENCE_POINT, result.experiencePoint.toString());
            window.dispatchEvent(new Event(IMAGE_POINT_CHANGE_EVENT));
          }
        },
      );

      isVipUser().then(res => setVipUser(!!res));
    };

    const queryLoraList = useCallback(
      debounce(async (isOwner: boolean, loraIdInQueryParam, searchKey, principalId, searchClothType, searchAgeRange) => {
        const orderBy = loraIdInQueryParam ? `if(id = '${loraIdInQueryParam}',1,0) desc, id desc` : null;
        const ownerField = userInfo.roleType === 'DISTRIBUTOR' ? false : (isOwner ? { isOwner } : {});
        const statusList = userInfo.roleType === 'OPERATOR' ? ['ENABLED', 'TESTING'] : ['ENABLED'];
        const nameLike = searchKey ? searchKey.trim() : null;
        const userId = userInfo.roleType === 'DISTRIBUTOR' && principalId ? principalId : null;
        const queryParam = {
          ...ownerField, statusList, pageNum: 1, pageSize: type === 'CUSTOM' ? 4 : 5,
          type, orderBy, needModelPoint: true, nameLike, userId,
          clothStyleType: searchClothType && searchClothType !== 'All' ? searchClothType.toLowerCase() : null,
          materialType: 'cloth',
          ageRange: searchAgeRange && searchAgeRange !== 'ALL' ? searchAgeRange : null,
          onlyExperimental: data.onlyExperimental,
        };
        let method = type === 'CUSTOM' ? queryMaterialModelByPage : querySystemModelList;

        if (type === 'CUSTOM' && userInfo.roleType === 'DISTRIBUTOR') {
          if (!principalId) {
            setLoraList([]);
            return;
          }
          method = queryLoras4Distributor;
        }

        method(queryParam).then((res) => {
          if (res) {
            setLoraList(res.list || []);
            setHasMoreLora(res.hasNextPage !== null ? !!res.hasNextPage : false);
          }
        });
      }, 300), []);

    const changeLoraType = (newValue: boolean, load: boolean = false, modelId: null | string = null) => {
      if (!load && isOwner === newValue) {
        return;
      }
      setIsOwner(newValue);
      const copy = deepCopy(data);

      if (loraIdInQueryParam) {
        copy.loraId = Number(loraIdInQueryParam);
      } else if (modelId) {
        copy.loraId = Number(modelId);
      } else {
        copy.loraId = null;
      }

      setData(copy);

      queryLoraList(newValue, loraIdInQueryParam, searchKey, principalId, searchClothType, searchAgeRange);
    };

    const handleChangeData = (data) => {
      setData(data);
    };

    //==================================== 文件上传相关 =========================================
    /**
     * 多图上传配置 用于上传多张参考图片
     */
    const multiImageUploadProps: UploadProps = {
      accept: 'image/png, image/jpeg',
      showUploadList: false,
      multiple: true,
      name: 'file',
      beforeUpload: (file, fileList) => {
        // 验证总数量是否超过限制
        if (!validateImageCount(file, fileList)) {
          return false;
        }

        // 获取当前文件在 fileList 中的索引
        const fileIndex = fileList.findIndex(item => item.uid === file.uid);
        // 判断当前文件是否是最后一个文件
        const isLastFile = fileIndex === fileList.length - 1;

        // 只有在最后一个文件时执行上传逻辑
        if (isLastFile) {
          // 批量处理所有文件
          const uploadPromises = fileList.map(fileItem => {
            return new Promise((resolve, reject) => {
              const formData = new FormData();
              formData.append('file', fileItem);

              fetch(UPLOAD_URL, {
                method: 'POST',
                body: formData,
              })
                .then(response => response.json())
                .then(response => {
                  if (response.success) {
                    resolve({
                      uid: fileItem.uid,
                      status: 'done',
                      response: response,
                    });
                  } else {
                    reject({
                      uid: fileItem.uid,
                      status: 'error',
                      name: fileItem.name,
                    });
                  }
                })
                .catch(error => {
                  reject({
                    uid: fileItem.uid,
                    status: 'error',
                    name: fileItem.name,
                  });
                });
            });
          });

          // 等待所有上传完成
          Promise.allSettled(uploadPromises)
            .then(results => {
              // 收集成功的上传结果
              const successFiles = results
                .filter((result): result is PromiseFulfilledResult<any> => result.status === 'fulfilled')
                .map(result => result.value);

              // 收集失败的上传结果
              const failedFiles = results
                .filter((result): result is PromiseRejectedResult => result.status === 'rejected')
                .map(result => result.reason);

              // 处理成功上传的文件
              if (successFiles.length > 0) {
                handleUploadSuccess(successFiles);
              }

              // 处理失败的文件
              failedFiles.forEach(file => {
                handleUploadError(file.name);
              });
            });
        }

        // 阻止默认上传行为
        return false;
      },
      // 移除maxCount限制，允许上传任意数量的图片
    };

    // 处理上传成功的文件
    const handleUploadSuccess = (fileList) => {
      // 使用Set去重，避免重复处理同一图片
      const uniqueUrls = new Set();

      // 获取所有上传成功的文件
      const successFiles = fileList
        .filter(f => f.status === 'done' && f.response && f.response.success)
        .filter(f => {
          // 过滤掉重复URL
          if (uniqueUrls.has(f.response.data)) {
            return false;
          }
          uniqueUrls.add(f.response.data);
          return true;
        })
        .map(f => ({
          imageUrl: f.response.data,
          isSelected: true,
          loraPath: '', // 本地上传的图片没有loraPath
          originalImageUrl: '', // 本地上传的图片没有originalImageUrl
        }));

      // 更新参考图列表
      if (successFiles.length > 0) {
        setReferencePicture(pre => {
          // 过滤掉已存在的URL
          const newFiles = successFiles.filter(file =>
            !pre.some(existing => existing.imageUrl === file.imageUrl),
          );
          return [...pre, ...newFiles];
        });
      }
    };

    // 处理上传失败
    const handleUploadError = (fileName) => {
      message.error(`图片 ${fileName} 上传失败，请重试`);
    };

    /**
     * 验证上传图片数量 - 移除数量限制，允许选择任意数量的参考图
     */
    const validateImageCount = (file, fileList) => {
      // 移除数量限制，允许用户选择任意数量的参考图
      return true;
    };

    // 添加场景数据加载状态
    const [sceneDataLoading, setSceneDataLoading] = useState(false);

    // 处理预加载场景模型数据更新
    const handlePreloadedSceneModelsUpdate = useCallback((newModels: any[]) => {
      setPreloadedSceneModels(newModels);
      setOriginalSceneModels(newModels);
    }, []);

    // 加载默认场景数据
    const loadDefaultSceneData = useCallback(async (conditions?: any) => {
      try {
        // 设置加载状态
        setSceneDataLoading(true);
        
        // 获取基础换衣配置
        const basicChangeConfig = await getBasicChangeConfig();
        if (!basicChangeConfig || basicChangeConfig.length === 0) {
          console.warn('未获取到基础换衣配置');
          // 设置空数据，让 StyleLoraDrawer 显示"暂无可用场景"
          setPreloadedSceneModels([]);
          setOriginalSceneModels([]);
          return;
        }

        // 查找场景配置
        const sceneConfigItem = basicChangeConfig.find(config => config.configKey === 'SCENE');
        if (!sceneConfigItem || !sceneConfigItem.children) {
          console.warn('未找到场景配置');
          // 设置空数据，让 StyleLoraDrawer 显示"暂无可用场景"
          setPreloadedSceneModels([]);
          setOriginalSceneModels([]);
          return;
        }

        // 保存场景配置
        setSceneConfig(sceneConfigItem);

        // 获取所有场景的ID列表
        const sceneIds = sceneConfigItem.children.map(child => child.id);

        let clothTypes = conditions && conditions.clothType ? (conditions.clothType === 'Unisex' ? ['Female', 'Male'] : [conditions.clothType, 'Common']) : [];

        // 构建查询参数，包含筛选条件传递给后端
        const queryParams = {
          idList: sceneIds,
          configKey: 'SCENE',
          clothType: conditions?.clothType || null,
          ageRange: conditions?.ageRange || null,
          positions: conditions?.positions && Array.isArray(conditions.positions) ? conditions.positions : null,
          bodyTypes: conditions?.bodyTypes && Array.isArray(conditions.bodyTypes) ? conditions.bodyTypes : null,
          clothCategory: conditions?.clothCategory || null,
          genderTypes: clothTypes,
          bizType: bizType || 'ALL',
          principalId: principalId || null,
        };

        // 调用后端接口，让后端进行筛选处理
        const elementsDataMap = await queryHasShowImageChildren(queryParams);

        // 如果接口返回数据为空，设置空数据让组件显示"暂无可用场景"
        if (!elementsDataMap || Object.keys(elementsDataMap).length === 0) {
          console.warn('接口返回的场景数据为空');
          setPreloadedSceneModels([]);
          setOriginalSceneModels([]);
          return;
        }

        // 合并场景配置数据和查询到的详细数据，确保不遗漏任何场景
        const allSceneIds = new Set([
          ...sceneIds,
          ...(elementsDataMap ? Object.keys(elementsDataMap).map(id => Number(id)) : []),
        ]);

        const defaultSceneModels = Array.from(allSceneIds).map(sceneId => {
          // 从场景配置中查找基础信息
          const configChild = sceneConfigItem.children.find(child => child.id === sceneId);
          // 从批量查询结果中获取详细数据
          const elementData = elementsDataMap ? elementsDataMap[sceneId] : null;

          // 如果既没有配置信息也没有详细数据，则跳过
          if (!configChild && !elementData) {
            return null;
          }

          const detailedChildren = elementData?.children || [];

          // 判断是否为专属场景（为数字或字符串类型的数字）
          const isExclusiveScene = elementData?.extInfo?.openScope &&
            (typeof elementData.extInfo.openScope === 'number' ||
              (typeof elementData.extInfo.openScope === 'string' && !isNaN(Number(elementData.extInfo.openScope))));

          // 将子图片数据转换为标准格式
          const childItems = Array.isArray(detailedChildren) ?
            detailedChildren
              .filter((elementItem): elementItem is {
                  id: number;
                  name: string;
                  extInfo: { showImage: string, styleImage: string },
                  tags: string,
                  extTags: string
                } =>
                  typeof elementItem?.extInfo?.showImage === 'string' || typeof elementItem?.extInfo?.styleImage === 'string',
              )
              .map((elementItem) => {
                if (isExclusiveScene) {
                  return {
                    id: elementItem.id,
                    name: elementItem.name,
                    showImage: elementItem.extInfo.styleImage,
                    extInfo: elementItem.extInfo,
                    tags: elementItem.tags || '',
                    extTags: elementItem.extTags || '',
                  };
                } else {
                  // 非专属场景：按照原逻辑赋值
                  return {
                    id: elementItem.id,
                    name: elementItem.name,
                    showImage: elementItem.extInfo.showImage,
                    extInfo: elementItem.extInfo,
                    tags: elementItem.tags || '',
                    extTags: elementItem.extTags || '',
                  };
                }
              }) : [];

          return {
            id: sceneId,
            name: configChild?.name || elementData?.name || `场景${sceneId}`,
            showImage: configChild?.showImage || elementData?.showImage || '',
            children: childItems,
            tags: (configChild as any)?.tags || elementData?.tags || '',
            extTags: (configChild as any)?.extTags || elementData?.extTags || '',
            isExclusiveScene: isExclusiveScene,
          };
        }).filter((scene): scene is NonNullable<typeof scene> => scene !== null); // 过滤掉null值并进行类型断言


        // 只保留有详细数据的场景
        const filteredSceneModels = defaultSceneModels.filter(scene => {
          // 检查是否有接口返回的详细数据
          return elementsDataMap && elementsDataMap[scene.id];
        });

        // 设置场景数据
        setPreloadedSceneModels(filteredSceneModels);
        setOriginalSceneModels(filteredSceneModels);
      } catch (error) {
        console.error('加载默认场景数据失败:', error);
        // 错误时也设置空数据，让组件显示"暂无可用场景"
        setPreloadedSceneModels([]);
        setOriginalSceneModels([]);
      } finally {
        // 无论成功失败都要清除加载状态
        setSceneDataLoading(false);
      }
    }, [bizType, principalId]);

    // 在组件初始化时加载默认场景数据
    useEffect(() => {
      loadDefaultSceneData(); // 首次加载不传递条件，获取所有场景
    }, []);  // 移除依赖，避免重复加载

    const fetchStyleScene = () => {
      const sceneRoot = configs.find(item => item.configKey === 'SCENE');
      if (!sceneRoot) return false;

      const current = data.configs[sceneRoot.id];
      const isStyleScene = current && current[0].styleScene;

      return isStyleScene ? current[0] : null;
    };

    const handleNeedShowWholeBody = () => {
      if (type === 'SYSTEM') return false;
      const find = loraList.find(item => item.id === data.loraId);
      let hit = find && find.halfBody;

      if (!hit) {
        setData({ ...data, bodyType: 'whole body' });
        return false;
      }

      const styleScene = fetchStyleScene();
      if (styleScene !== null && find && styleScene.type) {

        hit = styleScene.type.includes(find.halfBody) && styleScene.type.includes('whole body');

        if (!hit && styleScene.type.includes(find.halfBody)) {
          setData({ ...data, bodyType: find.halfBody });
        } else if (hit) {
          setData({ ...data, bodyType: 'unlimited' });
        } else {
          setData({ ...data, bodyType: 'whole body' });
        }
      } else {
        setData({ ...data, bodyType: 'whole body' });
      }

      return !!hit;
    };

    // 使用useMemo包装getClothPositions函数，减少不必要的重新计算
    const getClothPositions = useMemo(() => {
      if (!data.loraId) return null;
      const find = loraList.find(item => item.id === data.loraId);
      return find && find.hasBackView ? ['front view', 'back view'] : ['front view'];
    }, [data.loraId, loraList]);

    // 使用useMemo包装getClothBodyTypes函数，减少不必要的重新计算
    const getClothBodyTypes = useMemo(() => {
      if (!data.loraId) return null;
      const find = loraList.find(item => item.id === data.loraId);
      return find && find.halfBody ? ['whole body', find.halfBody] : ['whole body'];
    }, [data.loraId, loraList]);

    const fetchElementConfig = async (): Promise<ElementConfig[]> => {
      const value = await getElementConfig(currentType, bizType, true);
      if (value) {
        setConfigs(value);
      }
      return value || [];
    };

    async function init() {
      if (type === 'CUSTOM') {
        queryUserProfileByKey(CREATIVE_IMAGE_NUM).then(res => {
          if (!res || !res.profileVal) return;
          setData({ ...data, imageNum: Number(res.profileVal) });
          data.imageNum = Number(res.profileVal);
        });
      }

      const value = await fetchElementConfig();

      if (value) {
        value.forEach(item => {
          data.configs[item.id] = null;
        });
        const sceneKey = value.find(item => item.configKey === 'SCENE');
        if (sceneKey) {
          setSceneKey(sceneKey.id.toString());
        }
      }

      //跳转时页面参数指定了模型id
      const queryParams = new URLSearchParams(location.search);

      let my = true;
      if (queryParams.get('isOwner') === '1') {
        my = true;
      } else if (queryParams.get('isOwner') === '0') {
        my = false;
      } else if (userInfo.roleType === 'DISTRIBUTOR' || userInfo.roleType === 'OPERATOR') {
        my = false;
      }

      // 获取名为 'modelId' 的查询参数
      const modelId = queryParams.get('modelId');
      setLoraIdInQueryParam(modelId);

      changeLoraType(my, true, modelId);

      //设置新用户引导条件
      const showGuideStore = localStorage.getItem(NEED_GUIDE);
      if (type === 'SYSTEM' && showGuideStore === 'true') {
        setShowGuide(true);
      }

      updatePoint(type);

      isVipUser().then(res => setVipUser(!!res));
    }

    const handleChooseLora = (lora) => {
      const itemIndex = loraList.findIndex(item => item.id === lora.id);
      let newArray;

      if (itemIndex !== -1) {
        newArray = [lora, ...loraList.slice(0, itemIndex), ...loraList.slice(itemIndex + 1)];
      } else {
        newArray = [lora, ...loraList.slice(0, loraList.length - 1)];
      }

      setLoraList(newArray);
      setShowLoraSelector(false);

      const copy = deepCopy(data);
      copy.loraId = lora.id;

      // 立即清空模特和场景的选择，防止不匹配的情况
      const faceConfig = configs.find(config => config.configKey === 'FACE');
      const sceneConfig = configs.find(config => config.configKey === 'SCENE');

      if (faceConfig && copy.configs[faceConfig.id]) {
        copy.configs[faceConfig.id] = null;
      }

      if (sceneConfig && copy.configs[sceneConfig.id]) {
        copy.configs[sceneConfig.id] = null;
      }

      setData(copy);

      // 记录当前选中的服装
      setCurrentSelectedLora(lora);
    };

    const changeClothCollocation = (target, isPreset) => {
      if (isPreset) {
        data.clothCollocation.shoe = target.clothCollocation.shoe;
        data.clothCollocation.tops = target.clothCollocation.tops;
        data.clothCollocation.bottoms = target.clothCollocation.bottoms;
        data.clothCollocation.others = target.clothCollocation.others;
        data.clothCollocation.props = target.clothCollocation.props;
      } else {
        setData(target);
      }
    };

    const getModelType = () => {
      const faceRoot = configs.find(item => item.configKey === 'FACE');
      if (!faceRoot) return null;
      const current = data.configs[faceRoot.id];

      if (current && current[0].type) {
        return current[0].type.includes('female-model') ? 'female' : 'male';
      }
      return null;
    };

    const getCurrentExperienceModelOpenCfg = () => {
      if (!data.loraId) {
        return null;
      }

      return experienceModelOpenCfg.find(item => item.id === data.loraId);
    };

    // 使用useMemo包装模特组件的条件对象，特别注意clothType属性的处理
    const faceConditions = useMemo(() => {
      // 计算正确的clothType值
      let clothType: string | null = null;
      if (currentSelectedLora?.extInfo?.clothStyleType === 'male') {
        clothType = 'male-model';
      } else if (currentSelectedLora?.extInfo?.clothStyleType === 'female') {
        clothType = 'female-model';
      } else if (currentSelectedLora?.extInfo?.clothStyleType === 'unisex') {
        // unisex服装需要看已选的模特类型决定
        clothType = null;
      }

      return {
        version: loraList.find(item => item.id === data.loraId)?.version,
        principalId: principalId ? principalId : null,
        ageRange: currentSelectedLora?.extInfo?.ageRange || null,
        clothType: clothType,
      };
    }, [
      loraList,
      data.loraId,
      principalId,
      currentSelectedLora?.extInfo?.ageRange,
      currentSelectedLora?.extInfo?.clothStyleType,
      data.configs,
      configs.find(c => c.configKey === 'FACE')?.id,
    ]);

    // 使用useMemo包装场景组件的条件对象
    const sceneConditions = useMemo(() => ({
      clothType: currentSelectedLora?.extInfo?.clothStyleType && currentSelectedLora?.extInfo?.clothStyleType !== 'unisex' ? capitalizeFirstLetter(currentSelectedLora?.extInfo?.clothStyleType) : sceneClothType,
      version: loraList.find(item => item.id === data.loraId)?.version,
      principalId: principalId ? principalId : null,
      positions: getClothPositions,
      bodyTypes: getClothBodyTypes,
      ageRange: currentSelectedLora?.extInfo?.ageRange || null,
      clothCategory: currentSelectedLora?.extInfo?.clothCategory && Array.isArray(currentSelectedLora.extInfo.clothCategory) 
        ? currentSelectedLora.extInfo.clothCategory 
        : (currentSelectedLora?.extInfo?.clothCategory ? [currentSelectedLora.extInfo.clothCategory] : null),
    }), [loraList, data.loraId, principalId, loraClothType, sceneClothType, getClothPositions, getClothBodyTypes, currentSelectedLora?.extInfo?.ageRange, currentSelectedLora?.extInfo?.clothCategory]);

    // 监听data值变化
    useEffect(() => {
      setCanSave(isCanSave(data, imagePoint, type, predictVO, sceneKey, referencePicture, configs));
    }, [data, imagePoint, isOwner, predictVO, referencePicture, configs]);

    useEffect(() => {
      // 只有在选择了服装之后才调用predict方法
      if (data.loraId) {
        // 修正imageNum计算逻辑：有参考图时使用参考图数量，没有参考图时使用设置的图片数量
        const actualImageNum = referencePicture.length > 0 ? referencePicture.length : data.imageNum;
        predictLora(data.loraId, actualImageNum, data.proportion);
      } else {
        setPredictVO(null);
      }
    }, [data.loraId, data.imageNum, imagePoint, referencePicture.length, data.proportion]);

    useEffect(() => {

      setShowFlowSteps(false);

      queryAllExperienceModelOpenCfg().then(res => {
        if (res) {
          let cfg = res;
          // 演示数据的模特对所有服装可见
          if (!['MERCHANT'].includes(userInfo.roleType)) {
            cfg.forEach(item => {
              item.faces = [];
            });
          }
          setExperienceModelOpenCfg(cfg);
        }
        init();
      });

    }, [type]);

    useEffect(() => {

      //跳转时页面参数指定了face id/scene id
      const queryParams = new URLSearchParams(location.search);

      //从url中获取'faceId'字段，「全部资产」-去创作
      const faceId = queryParams.get('faceId');
      if (faceId && Number(faceId) !== null) {
        let faceParentConfig = configs.find(item => item.configKey === 'FACE');
        if (faceParentConfig && faceParentConfig?.children) {
          let selectedFace = faceParentConfig?.children?.find(f => f.id === Number(faceId));
          if (selectedFace) {
            handleFaceChange(faceParentConfig.id, [selectedFace], false);
          }
        }
      }

      //从url中获取'sceneId'字段，「全部资产」-去创作
      const sceneId = queryParams.get('sceneId');
      if (sceneId && Number(sceneId) !== null) {
        let sceneParentConfig = configs.find(item => item.configKey === 'SCENE');
        if (sceneParentConfig && sceneParentConfig?.children) {
          let selectedScene = sceneParentConfig?.children?.find(f => f.id === Number(faceId));
          if (selectedScene) {
            handleSceneChange(sceneParentConfig.id, [selectedScene], false);
          }
        }
      }
    }, [location, configs]);

    useEffect(() => {
      if (!data.loraId) return;

      // 找到当前选中的服装
      const selectedLora = loraList.find(item => item.id === data.loraId);
      if (selectedLora) {
        // 更新服装类型，该值会传递给场景组件使用
        const clothStyleType = selectedLora.extInfo ? selectedLora.extInfo['clothStyleType'] : '';
        setLoraClothType(clothStyleType);

        // 记录当前选中的服装，用于后续过滤
        setCurrentSelectedLora(selectedLora);
      }
    }, [data.loraId, loraList]);

    useEffect(() => {
      setNeedShowWholeBody(handleNeedShowWholeBody());
    }, [data.loraId, data.configs[sceneKey]?.[0].id]);

    useEffect(() => {
      queryLoraList(isOwner, loraIdInQueryParam, searchKey, principalId, searchClothType, searchAgeRange);
    }, [searchKey, principalId, searchClothType, queryLoraList, searchAgeRange]);

    // 组件卸载时清理防抖函数
    useEffect(() => {
      return () => {
        debouncedSearchKey.cancel();
      };
    }, [debouncedSearchKey]);

    // 监听参考图变化，更新预测单价
    useEffect(() => {
      // 只有在选择了服装之后才调用predict方法
      if (!data.loraId) {
        setPredictVO(null);
        return;
      }

      // 修正imageNum计算逻辑：有参考图时使用参考图数量，没有参考图时使用设置的图片数量
      const actualImageNum = referencePicture.length > 0 ? referencePicture.length : data.imageNum;

      predict('FIXED_POSTURE_CREATION', actualImageNum, data.loraId)
        .then(result => {
          if (result) {
            setPredictVO(result);
          }
        });
    }, [referencePicture, data.imageNum, data.loraId]);

    // 当筛选条件变化时，重新调用后端接口获取筛选后的场景数据
    useEffect(() => {
      // 只有当有有效的筛选条件时才重新加载，并且确保不是初始化状态
      if (sceneConfig && Object.keys(sceneConditions).some(key => sceneConditions[key] != null)) {
        loadDefaultSceneData(sceneConditions);
      }
    }, [sceneConditions.clothType, sceneConditions.ageRange, sceneConditions.positions, sceneConditions.bodyTypes, sceneConditions.clothCategory]);

    // 选择参考图组件
    const SelectReferenceImage = ({ tag }) => {
      // 添加显示数量控制，用于性能优化
      const [displayCount, setDisplayCount] = useState(50); // 初始显示50张图片

      // 获取要显示的图片列表（性能优化）
      const displayedPictures = referencePicture.slice(0, displayCount);
      const hasMorePictures = referencePicture.length > displayCount;

      // 加载更多图片
      const loadMorePictures = () => {
        setDisplayCount(prev => Math.min(prev + 50, referencePicture.length));
      };

      // 空状态展示组件
      const EmptyStateView = () => (
        <Flex vertical align="center" justify="center" className="empty-state">
          <InboxOutlined className="empty-icon" />
          <div className="text14 empty-text">未选择</div>
        </Flex>
      );

      // 参考图缩略图组件
      const ReferenceThumbnail = ({ image, index }) => (
        <div key={index} className="thumbnail-container">
          <Image
            src={image.imageUrl}
            alt={`参考图${index + 1}`}
            className="thumbnail-image"
            preview={true}
          />
          <Checkbox
            checked={image.isSelected}
            className="round-checkbox checkbox-style"
            onChange={(e) => {
              // 如果取消选中，则直接删除该图片
              if (!e.target.checked) {
                setReferencePicture(pre => pre.filter((_, idx) => idx !== index));
              }
            }}
          />
        </div>
      );

      // 添加按钮组件
      const AddButton = ({ onClick, children = null }) => (
        <div
          className="add-button"
          onClick={onClick}
        >
          <RightOutlined className="add-icon" />
          <span className="add-text">添加</span>
          {children}
        </div>
      );

      // 本地上传按钮
      const UploadButton = () => (
        <Button
          className="upload-button"
          icon={<IconFont type={'icon-shangchuan'} className="upload-icon" />}
        >
          <div className={'color-36 weight'}>本地上传</div>
        </Button>
      );

      // 底部操作栏组件
      const BottomBar = () => (
        <Flex
          justify="flex-end"
          align="center"
          gap={16}
          className="bottom-bar"
        >
          <div className="text14 select-count">
            已选 {referencePicture.length} 张
          </div>
          <Popconfirm
            title="确认删除"
            description="确定要删除所有已选择的参考图吗？"
            onConfirm={() => {
              setReferencePicture([]);
            }}
            okText="确认"
            cancelText="取消"
          >
            <div
              className="text14"
              style={{ color: 'red', cursor: 'pointer' }}
            >
              全部删除
            </div>
          </Popconfirm>
          {/* 添加加入收藏按钮，并设置合理颜色 */}
          <Popconfirm
            title="加入收藏"
            description="确定要将当前选择的参考图加入收藏吗？"
            onConfirm={() => {
              // 打开完善收藏信息弹窗
              setShowCollectionInfoModal(true);
            }}
            okText="确认"
            cancelText="取消"
          >
            <div
              className="text14 delete-button color-36"
              style={{ cursor: 'pointer' }}
            >
              加入收藏
            </div>
          </Popconfirm>
        </Flex>
      );

      return (
        <Flex vertical gap={8} justify={'flex-start'} className={'work-item-container select-reference-image'}>
          {/* 一级标题 */}
          <div className={'text16 font-pf color-n weight reference-title'}>
            <span>{tag}</span>
          </div>

          {/* 参考图上传方式 */}
          <Segmented
            value={currentReferenceType}
            className="reference-segmented"
            options={['系统生成参考图', '我收藏的模板']}
            onChange={(value) => {
              if (value === '我收藏的模板') {
                // 点击我收藏的模板时打开弹窗，不切换标签
                setShowFavoriteModal(true);
              } else {
                // 设置当前参考图类型
                setCurrentReferenceType(value as '系统生成参考图' | '本地上传');
                // 切换时清空参考图集合
                setReferencePicture([]);
                // 切换时关闭抽屉
                setIsOpenDrawer(false);
              }
            }}
          />

          {/* 参考图容器 */}
          <div className="reference-container">
            {/* 系统生成参考图 - 空状态 */}
            {currentReferenceType === '系统生成参考图' && referencePicture.length === 0 && !isOpenDrawer && (
              <Flex
                justify="space-between"
                align="center"
                className="system-select-container"
                onClick={() => setIsOpenDrawer(true)}
              >
                <Flex justify="flex-start" align="center" className="system-image">
                  <img src={StaticImage.basicReplace.referencePic} className="reference-image" alt="系统生成参考图" />
                  <Flex vertical justify="center" align="flex-start" className="system-text-container">
                    <div className="text16" style={{ marginBottom: '8px' }}>选择参考图</div>
                    <div className="text14 color-96">可选择多张参考图</div>
                  </Flex>
                </Flex>
                {/* 右侧图标 */}
                <span className="system-arrow"><RightOutlined /></span>
              </Flex>
            )}

            {/* 本地上传 - 空状态 */}
            {currentReferenceType === '本地上传' && referencePicture.length === 0 && (
              <Dragger
                style={{ height: '100%', cursor: 'pointer' }}
                {...multiImageUploadProps}
                onChange={(info) => {
                  const { file, fileList } = info;

                  // 只在文件上传完成或失败时进行处理
                  if (file.status === 'done') {
                    if (file.response && file.response.success) {
                      // 当一个文件上传完成时，处理整个文件列表
                      handleUploadSuccess(fileList);
                    }
                  } else if (file.status === 'error') {
                    handleUploadError(file.name);
                  }
                }}
              >
                <UploadButton />
                <div className="text14 color-96 upload-hint">
                  点击/粘贴/拖拽图片至此，支持png、jpg格式
                </div>
              </Dragger>
            )}

            {/* 展示已选择的参考图或系统生成的参考图 */}
            {(referencePicture.length !== 0 || isOpenDrawer) && (
              <div className="reference-result-box result-box">
                <Flex wrap="wrap" gap={12} align="flex-start" style={{ minHeight: '66px' }}>
                  {/* 系统生成参考图模式下的展示 - 空状态 */}
                  {currentReferenceType === '系统生成参考图' && referencePicture.length === 0 && isOpenDrawer && (
                    <EmptyStateView />
                  )}

                  {/* 展示已选择的参考图 */}
                  {displayedPictures.length > 0 && displayedPictures.map((image, index) => (
                    <ReferenceThumbnail key={index} image={image} index={index} />
                  ))}

                  {/* 加载更多按钮 */}
                  {hasMorePictures && (
                    <div
                      className="add-button"
                      onClick={loadMorePictures}
                      style={{
                        backgroundColor: '#f0f8ff',
                        border: '2px dashed #1890ff',
                        color: '#1890ff',
                      }}
                    >
                      <span className="add-text">
                        加载更多<br />
                        ({referencePicture.length - displayCount}张)
                      </span>
                    </div>
                  )}

                  {/* 系统生成参考图模式下的添加按钮 */}
                  {currentReferenceType === '系统生成参考图' && referencePicture.length > 0 && !isOpenDrawer && (
                    <AddButton onClick={() => setIsOpenDrawer(true)} />
                  )}

                  {/* 本地上传模式下的添加按钮 */}
                  {currentReferenceType === '本地上传' && !isOpenDrawer && (
                    <Upload
                      {...multiImageUploadProps}
                      onChange={(info) => {
                        const { file, fileList } = info;

                        // 只在文件上传完成或失败时进行处理
                        if (file.status === 'done') {
                          if (file.response && file.response.success) {
                            // 当一个文件上传完成时，处理整个文件列表
                            handleUploadSuccess(fileList);
                          }
                        } else if (file.status === 'error') {
                          handleUploadError(file.name);
                        }
                      }}
                    >
                      <AddButton onClick={() => {
                      }} />
                    </Upload>
                  )}
                </Flex>

                {/* 底部操作栏 */}
                {!(currentReferenceType === '系统生成参考图' && referencePicture.length === 0) && (
                  <BottomBar />
                )}
              </div>
            )}
          </div>

          {/* 使用场景配饰 */}
          <SceneAccessoriesBlock changeData={handleChangeData} data={data} />
        </Flex>
      );
    };

    return (
      <PageContainer>
        {type === 'CUSTOM' &&
          <Flow activeStep={3} onCollapseAction={setShowFlowSteps} />
        }
        <Flex justify={'flex-start'} align={'flex-start'}
              className={'row-container' + (showFlowSteps ? ' row-container-flow' : '')}>
          <div className={'work-block work-block-fixed'} style={{ gap: 16 }}>

            <Flex vertical
                  className={'creation-scene-header' + (BIZ_TYPE_CLASS_MAP[bizType]?.tabs ? ' ' + BIZ_TYPE_CLASS_MAP[bizType]?.tabs : '')}
                  style={{ marginBottom: -14, marginTop: -8 }}>
              <Tabs activeKey={bizType} items={ALLBizTypes.map(e => {
                return {
                  ...e,
                  label: <Flex align={'center'} justify={'center'} gap={4}>
                    {e.iconType && <IconFont type={e.iconType} style={{ fontSize: 16 }} />}
                    {e.label}
                  </Flex>,
                };
              })} onChange={handleChangeBizType} tabBarGutter={0}
                    indicator={{ size: 24 }} />
            </Flex>

            <div className={'work-item-container'} ref={step1Ref}>

              {type === 'CUSTOM' && userInfo?.roleType === 'DISTRIBUTOR' &&
                <div className={'work-item-container'} style={{ gap: 8 }}>
                  <div className={'text16 font-pf color-n weight'}>选择客户</div>
                  <div className={'work-item-lora margin-bottom-16'} style={{ padding: 16 }}>
                    <MasterSelector onChange={changePrincipal} width={434} />
                  </div>
                </div>
              }

              <div className={'text16 font-pf color-n weight'}>选择服装</div>

              <div className={'work-item-lora'} style={{ gap: 8 }}>
                <Flex justify={'space-between'} align={'center'} className={'width-100'}>
                  <div>
                    {type === 'CUSTOM' && userInfo.roleType !== 'DISTRIBUTOR' &&
                      <Segmented
                        value={isOwner}
                        onChange={(value) => changeLoraType(value as boolean)}
                        options={[{ label: '我的服装', value: true }, { label: '团队全部', value: false }]}
                      />
                    }
                  </div>
                  <Flex gap={4} align={'center'}>
                    {/* 运营、管理员、测试账号可进行筛选 */}
                    {['OPERATOR', 'ADMIN', 'DEMO_ACCOUNT'].includes(userInfo.roleType) &&
                      <>
                        <Dropdown
                          trigger={['hover']}
                          placement="bottomLeft"
                          open={typeSelectOpen}
                          onOpenChange={(visible) => setTypeSelectOpen(visible)}
                          dropdownRender={() => (
                            <div style={{
                              backgroundColor: 'white',
                              boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                              borderRadius: 4,
                              padding: 4,
                              maxHeight: 300,
                              overflow: 'auto',
                            }}>
                              {CreationClothTypeCheckBoxOptions.filter((item: any) => item.value !== 'Common' && item.value !== 'Unset').map((item: any) => (
                                <div
                                  key={item.value}
                                  style={{ padding: '5px 12px', cursor: 'pointer', borderRadius: 4, textAlign: 'left' }}
                                  onClick={() => {
                                    setSearchClothType(item.value);
                                    setTypeSelectOpen(false);
                                  }}
                                  onMouseEnter={e => e.currentTarget.style.backgroundColor = '#f5f5f5'}
                                  onMouseLeave={e => e.currentTarget.style.backgroundColor = 'transparent'}
                                >
                                  {item.label}
                                </div>
                              ))}
                            </div>
                          )}
                        >
                          <Select
                            placeholder={'类型'}
                            allowClear={true}
                            value={searchClothType}
                            options={CreationClothTypeCheckBoxOptions.filter((item: any) => item.value !== 'Common' && item.value !== 'Unset')}
                            style={{ width: 84 }}
                            onClick={() => setTypeSelectOpen(!typeSelectOpen)}
                            onChange={(value) => setSearchClothType(value)}
                            dropdownStyle={{ display: 'none' }} // 隐藏原始下拉框
                          />
                        </Dropdown>

                        <Dropdown
                          trigger={['hover']}
                          placement="bottomLeft"
                          open={ageSelectOpen}
                          onOpenChange={(visible) => setAgeSelectOpen(visible)}
                          dropdownRender={() => (
                            <div style={{
                              backgroundColor: 'white',
                              boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                              borderRadius: 4,
                              padding: 4,
                              maxHeight: 300,
                              overflow: 'auto',
                            }}>
                              {AllAgeRanges.map((item: any) => (
                                <div
                                  key={item.value}
                                  style={{
                                    padding: '5px 12px',
                                    cursor: item.disabled ? 'not-allowed' : 'pointer',
                                    borderRadius: 4,
                                    textAlign: 'left',
                                    color: item.disabled ? '#ccc' : 'inherit',
                                    opacity: item.disabled ? 0.5 : 1,
                                  }}
                                  onClick={() => {
                                    if (!item.disabled) {
                                      setSearchAgeRange(item.value);
                                      setAgeSelectOpen(false);
                                    }
                                  }}
                                  onMouseEnter={e => {
                                    if (!item.disabled) {
                                      e.currentTarget.style.backgroundColor = '#f5f5f5';
                                    }
                                  }}
                                  onMouseLeave={e => e.currentTarget.style.backgroundColor = 'transparent'}
                                >
                                  {item.label}
                                </div>
                              ))}
                            </div>
                          )}
                        >
                          <Select
                            placeholder={'年龄'}
                            allowClear={true}
                            value={searchAgeRange}
                            options={AllAgeRanges}
                            style={{ width: 84 }}
                            onClick={() => setAgeSelectOpen(!ageSelectOpen)}
                            onChange={(value) => setSearchAgeRange(value)}
                            dropdownStyle={{ display: 'none' }} // 隐藏原始下拉框
                          />
                        </Dropdown>
                      </>
                    }

                    {/* 服装名称模糊搜索 */}
                    <Input 
                      placeholder={'服装名称模糊搜索'} 
                      style={{ width: 160 }} 
                      allowClear
                      value={searchValue}
                      onChange={(e) => {
                        const value = e.target.value;
                        setSearchValue(value); // 立即更新输入框显示
                        
                        // 如果清空了输入框，立即触发搜索
                        if (!value) {
                          debouncedSearchKey.cancel(); // 取消之前的防抖
                          setSearchKey(null);
                        } else {
                          // 否则使用防抖搜索
                          debouncedSearchKey(value);
                        }
                      }} 
                    />
                  </Flex>
                </Flex>

                <Flex wrap={true}
                      className={'work-item-block ' + (!loraList || loraList.length === 0 ? ' work-item-empty-block' : '')}>
                  {loraList && loraList.length > 0 && loraList.slice(0, hasMoreLora ? 4 : loraList.length).map((item) => (
                    <Tooltip 
                      title={(userInfo.roleType === 'MERCHANT' && item.extInfo?.['memo']) ? item.extInfo['memo'] : null}
                      placement="top"
                    >
                      <div key={item.id}
                          className={'work-item work-item-fixed' + (data.loraId === item.id ? ' work-item-selected' : '')}
                          onClick={() => {
                            // 更新表单数据
                            handleLoraChange(item.id);
                            // 记录当前选中的服装
                            setCurrentSelectedLora(item);
                          }}>
                        <div className={'work-image-block'}>
                          {type === 'CUSTOM' &&
                            <div className={'work-item-block-desc text12 font-pf color-w'}>
                              剩余{item.modelPoint ? item.modelPoint : 0}张
                            </div>
                          }
                          <img src={item.showImage} alt={item.name} className={'lora-item-image'} />
                          {isClothMarkedColor(item) && getClothMarkIcon(item) && (
                            <IconFont type={getClothMarkIcon(item)}
                                      style={{ fontSize: 16, position: 'absolute', top: 8, right: 8 }} />
                          )}
                        </div>
                        <div style={{ overflowWrap: 'break-word', wordBreak: 'break-all' }}
                             className={'text14 font-pf color-n margin-top-4 margin-bottom-4 text-center'}>{item.name}</div>
                      </div>
                    </Tooltip>
                  ))}
                  {loraList && loraList.length > 0 && hasMoreLora &&
                    <div className={'work-item work-item-fixed'} onClick={() => setShowLoraSelector(true)}>
                      <div className={'work-image-block'}
                           style={{ backgroundImage: `url(${ALL_LORAS_NEW})`, backgroundSize: 'cover' }}>
                        <div className={'more-lora-block work-element-more-block'}>
                          <img src={MORE_LORA_ICON} alt={'more'} style={{ width: 32, height: 32 }} />
                          <div className={'text14 font-pf weight color-w text-center margin-top-4'}>全部服装</div>
                        </div>
                      </div>
                      <div
                        className={'text14 font-pf color-n margin-top-4 margin-bottom-4 text-center'}>更多
                      </div>
                    </div>
                  }
                  {loraList && loraList.length === 0 &&
                    <div className={'center-block'} style={{ width: '100%', height: '100%' }}>
                      <div className={'text14 font-pf color-1a'}>暂无服装，请先上传素材
                      </div>
                      <Button className={'update-material-btn margin-top-16'}
                              onClick={handleUploadMaterial}>
                        <div className={'text16 font-pf weight color-24'}>传素材</div>
                      </Button>
                    </div>
                  }
                </Flex>
              </div>
            </div>

            <div className={'work-item-group'} ref={step2Ref} style={{ gap: 16 }}>
              {/*'模特'配置*/}
              {configs.filter(c => c.configKey === 'FACE').map((config) => (
                <ElementWithTypeBlock key={'FACE'} config={config} defaultType={'recent'} modelId={data.loraId}
                                      current={data.configs[config.id]} onChange={handleFaceChange}
                                      acceptTypes={loraClothType ? (loraClothType === 'unisex' ? ['male-model', 'female-model'] : [loraClothType + '-model']) : undefined}
                                      conditions={faceConditions} needExclusive={true} needFavor popShowAll={true}
                                      moreIcon={'icon-quanbumote'}
                                      moreBg={BG_ALL_MODELS} orderByType={true} loraType={type} isVipUser={vipUser}
                                      experienceModelOpenCfg={getCurrentExperienceModelOpenCfg()}
                                      showConfirmFooter={true}
                                      foot={<FaceExpressionBlock changeData={handleChangeData} data={data} />} />
              ))}

              {/* 选择参考图 */}
              <SelectReferenceImage tag={'选择姿势图'} />

              {/*服装搭配*/}
              <ClothCollocationBlock data={data} onChange={((data, isPreset) => changeClothCollocation(data, isPreset))}
                                     defaultExpend={false}
                                     type={loraClothType ? (loraClothType === 'unisex' ? getModelType() : loraClothType) : null} />

              <ProportionBlock value={data.proportion} onChange={handleProportionChange} type={type}
                               model={loraList.find(item => item.id === data.loraId)} isVip={vipUser} />

              <ClothColorSelector model={loraList.find(item => item.id === data.loraId)} value={data.colorIndex}
                                  onChange={handleChangeColor} position={data.position} bodyType={data.bodyType} />

              {userInfo.roleType === 'OPERATOR' && <ClothColorBlock model={loraList.find(item => item.id === data.loraId)} value={data.clothColor}
                               onChange={value => {
                                 setData({ ...data, clothColor: value });
                               }} />}

              <div className={'work-item-container'} style={{ display: isTrialAccount ? 'none' : undefined }}>
                <div className={'text16 font-pf color-n weight'}>画面设置</div>
                <Flex gap={8} align={'center'} justify={'flex-start'}>
                  <Checkbox checked={!data.enableAntiBlurLora} onChange={e => {
                    setData({ ...data, enableAntiBlurLora: !e.target.checked });
                  }}>
                    <Flex>
                      <div>背景虚化</div>
                      <Tooltip title={'选中后，创作图将突出前景中的服装和模特，使背景模糊。'} placement="topRight">
                        <IconFont type={'icon-yiwen1'} style={{ fontSize: 18 }} />
                      </Tooltip>
                    </Flex>
                  </Checkbox>

                  <Checkbox checked={data.enableNewModel} onChange={e => {
                    setData({ ...data, enableNewModel: e.target.checked });
                  }}>
                    <Flex>
                      <div>脸部质感增强</div>
                      <Tooltip title={'可能会引起衣服质感出现问题。衣服花纹比较复杂不建议使用'} placement="topRight">
                        <IconFont type={'icon-yiwen1'} style={{ fontSize: 18 }} />
                      </Tooltip>
                    </Flex>
                  </Checkbox>

                </Flex>

              </div>

            </div>
          </div>
          <div ref={outputBlockRef}
               className={'output-block output-block-fixed' + (showFlowSteps ? ' output-block-flow' : '')}
               style={{ width: `calc((100% - 644px - 17px) * ${sliderValue / 12})`, maxWidth: '100%' }}>

            {/* 添加抽屉组件 */}
            {isOpenDrawer &&
              <StyleLoraDrawer
                isVipUser={vipUser}
                expand={isOpenDrawer}
                currentType={currentType}
                changeExpand={setIsOpenDrawer}
                selectedReferences={referencePicture}
                preloadedSceneModels={preloadedSceneModels}
                styleDescription={styleDescription || ''}
                onPreloadedSceneModelsUpdate={handlePreloadedSceneModelsUpdate}
                sceneConfig={sceneConfig}
                maxSelectCount={5} // 设置为null表示无数量限制
                loadingText="正在拉取数据，请稍等..." // 自定义加载提示文案
                conditions={sceneConditions} // 传递筛选条件
                sceneDataLoading={sceneDataLoading} // 传递场景数据加载状态
                onSelect={(images: SelectedItem[]) => {
                  setReferencePicture(images.map(image => ({
                    imageUrl: image.image,
                    isSelected: true,
                    referenceConfig: image.referenceConfig,
                    backTags: image.backTags,
                    backExtTags: image.backExtTags,
                    loraId: image.loraId, // 使用StyleLoraDrawer传递的loraId
                    loraPath: image.referenceConfig?.loraPath || '', // 从referenceConfig中获取loraPath
                    originalImageUrl: image.referenceConfig?.styleImage || '', // 从referenceConfig中获取styleImage
                  })));
                }}
                width={'calc(100% - 68px)'}
                className="reference-drawer"
              />
            }

            {!useMultiTask &&
              <OutputBlock types={[currentType]} status={status}
                           sliderValue={sliderValue} loraType={type} changeSliderValue={setSliderValue}
                           refreshStatus={setStatus} ref={outputRef} />
            }

            {useMultiTask &&
              <TaskOutputBlock sliderValue={sliderValue} bizType={bizType} types={[currentType]}
                               bgClassName={(BIZ_TYPE_CLASS_MAP[bizType]?.outputBg ? BIZ_TYPE_CLASS_MAP[bizType]?.outputBg : null)}
                               bgImg={(BIZ_TYPE_CLASS_MAP[bizType]?.outputBgImg ? BIZ_TYPE_CLASS_MAP[bizType]?.outputBgImg : null)}
                               ref={taskOutputRef} pollingTimeout={3000} loraType={type}
                               changeSliderValue={setSliderValue} isSelectTask={true} />
            }
          </div>
        </Flex>

        <footer className={'footer-bar'}>
          <div className={'fixed-tab-bar fixed-tab-bar-fixed'}>
            <div className={'fixed-tab-content'} ref={step3Ref}>
              <PredictBlock creativeType={currentType} predictVO={predictVO} data={data} type={type}
                            imagePoint={imagePoint} />
              <Button type="primary" className={'create-btn'}
                      disabled={!canSave || commiting || (predictVO?.needTopup === true)}
                      icon={(status === 'INIT' || status === 'FINISHED') ? '' :
                        <img src={MINI_LOADING_ICON} width={16} height={16} alt={'logo'}
                             className={'loading-img'} />}
                      onClick={handleCreate}> {(status === 'INIT' || status === 'FINISHED') ? (predictVO && predictVO.needTopup ? '余额不足，去充值' : '生成图片') : ''}</Button>
            </div>
          </div>
        </footer>

        <NewUserGuide visible={type === 'SYSTEM' && showGuide && loraList.length > 0} step1Ref={step1Ref}
                      step2Ref={step2Ref} step3Ref={step3Ref} handleFinish={() => setShowGuide(false)} />

        {showLoraSelector &&
          <LoraSelector show={true} isOwner={isOwner} type={type} onCancel={() => setShowLoraSelector(false)}
                        onChange={(lora) => handleChooseLora(lora)} principalId={principalId} />
        }

        {showTopupModal && !isTrialAccount &&
          <TopupModal visible={showTopupModal} onClose={() => setShowTopupModal(false)}
                      onPaySuccess={() => {
                        setShowTopupModal(false);
                        updatePoint('CUSTOM');
                      }} />
        }

        {/* 我收藏的模板弹窗 */}
        <FavoriteTemplateModal
          open={showFavoriteModal}
          onCancel={() => setShowFavoriteModal(false)}
          onSelect={(templates) => {
            // 将选择的模板转换为参考图格式并添加到参考图列表中
            const newReferencePictures = templates.map(template => ({
              imageUrl: template.imageUrl,
              isSelected: template.isSelected,
              referenceConfig: template.referenceConfig,
              backTags: template.backTags,
              backExtTags: template.backExtTags,
              loraId: template.loraId,
              loraPath: template.referenceConfig?.loraPath || '', // 从referenceConfig中获取loraPath
              originalImageUrl: template.referenceConfig?.styleImage || '', // 从referenceConfig中获取styleImage
            }));

            // 更新参考图列表
            setReferencePicture(prev => {
              // 过滤掉已存在的URL，避免重复
              const existingUrls = new Set(prev.map(item => item.imageUrl));
              const filteredNewPictures = newReferencePictures.filter(
                item => !existingUrls.has(item.imageUrl),
              );
              return [...prev, ...filteredNewPictures];
            });

            // 关闭弹窗
            setShowFavoriteModal(false);

            message.success(`已添加 ${templates.length} 个收藏模板到参考图`);
          }}
        />

        {/* 完善收藏信息弹窗 */}
        <Modal
          open={showCollectionInfoModal}
          title="完善收藏信息"
          onCancel={() => {
            setShowCollectionInfoModal(false);
            setCollectionTemplateName('');
          }}
          onOk={() => {
            if (!collectionTemplateName.trim()) {
              message.error('请输入模板名称');
              return;
            }

            // 获取选中的参考图
            const selectedImages = referencePicture.filter(img => img.isSelected);
            if (selectedImages.length === 0) {
              message.error('请至少选择一张参考图');
              return;
            }


            // 获取当前选中服装的lora路径
            const currentLora = loraList.find(item => item.id === data.loraId);
            // MaterialModel中没有直接的loraPath字段，可以使用loraName或其他字段，或者暂时设置为空
            const currentLoraPath = currentLora?.loraName || '';

            // 构建referenceInfoList数据
            const referenceInfoList = selectedImages.map(image => ({
              imageUrl: image.imageUrl,
              referenceConfig: image.referenceConfig || {}, // 直接传递JSONObject，如果为空则传递空对象
              backTags: image.backTags || '',
              backExtTags: image.backExtTags || '',
              loraId: image.loraId || 0, // 使用参考图中的loraId字段
              loraPath: image.loraPath || (image.referenceConfig?.loraPath || ''), // lora路径，优先使用loraPath字段，否则从referenceConfig中获取
              originalImageUrl: image.originalImageUrl || (image.referenceConfig?.styleImage || ''), // 原始图片地址，优先使用originalImageUrl字段，否则从referenceConfig中获取styleImage
            }));


            // 调用创建固定姿势模板API
            const templateData = {
              templateName: collectionTemplateName.trim(),
              userId: userInfo.userId || userInfo.id, // 添加当前用户ID
              referenceInfoList: referenceInfoList,
            };

            createFixedCreativeTemplate(templateData)
              .then((result) => {
                if (result) {
                  message.success('收藏成功');
                  setShowCollectionInfoModal(false);
                  setCollectionTemplateName('');
                } else {
                  message.error('收藏失败，请重试');
                }
              })
              .catch((error) => {
                console.error('收藏失败:', error);
                message.error('收藏失败，请重试');
              });
          }}
          okText="保存"
          cancelText="取消"
          width={800}
          centered
        >
          <Flex vertical gap={16}>
            {/* 模板名称输入框 */}
            <div>
              <div className="text16 font-pf color-n weight margin-bottom-8">模板名称</div>
              <Input
                placeholder="请输入模板名称"
                value={collectionTemplateName}
                onChange={(e) => setCollectionTemplateName(e.target.value)}
                maxLength={20}
                showCount
              />
            </div>

            {/* 收藏的图片展示 */}
            <div>
              <div className="text16 font-pf color-n weight margin-bottom-8">
                收藏姿势 ({referencePicture.filter(img => img.isSelected).length})
              </div>
              <div style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: '12px',
                maxHeight: '400px',
                overflowY: 'auto',
                padding: '8px',
                border: '1px solid #d9d9d9',
                borderRadius: '6px',
                backgroundColor: '#fafafa',
              }}>
                {referencePicture.filter(img => img.isSelected).length === 0 ? (
                  <div style={{
                    width: '100%',
                    textAlign: 'center',
                    padding: '40px 0',
                    color: '#999',
                  }}>
                    暂无选中的参考图
                  </div>
                ) : (
                  referencePicture
                    .filter(img => img.isSelected)
                    .map((image, index) => (
                      <div key={index} style={{ position: 'relative' }}>
                        <Image
                          src={image.imageUrl}
                          alt={`收藏图片${index + 1}`}
                          width={120}
                          height={120}
                          style={{
                            objectFit: 'cover',
                            borderRadius: '6px',
                            border: '1px solid #d9d9d9',
                          }}
                          preview={true}
                        />
                      </div>
                    ))
                )}
              </div>
            </div>
          </Flex>
        </Modal>
      </PageContainer>
    );
  }
;

export default FixedPostureCreative;