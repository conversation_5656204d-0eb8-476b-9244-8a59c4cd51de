@import "@/app";

@content-width: calc((100vw - @menu-width - 24px) / 2);

.video-row-container {
  padding-left: 0 !important;
  margin: 0 !important;
}

.video-header {
  width: @content-width;
  height: 78px;
  background-image: url('@/assets/images/bg-header-create-video.jpg');
  background-size: cover;
  background-repeat: no-repeat;
  padding-top: 18px;
}

.video-header-title {
  height: auto;
  background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.video-content {
  position: absolute;
  top: 56px;
  width: @content-width;
  padding: 16px 16px 0 16px;
  background: #FFFFFF;
  border-radius: 8px 8px 0 0;
}

.video-upload {
  width: calc(@content-width - 16px * 2);
  min-height: 208px;

  .ant-upload {
    padding: 0 !important;
  }
}

.video-delete-round:hover {
  background: #FDECEE;

  svg {
    color: #FC343F;
  }
}

.repair-free-icon-desc {
  position: absolute;
  right: 8px;
  top: 2px;
  color: #864D02;
  z-index: 2;
}
