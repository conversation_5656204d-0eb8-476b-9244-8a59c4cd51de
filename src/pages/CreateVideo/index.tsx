import { <PERSON><PERSON>ontainer } from '@ant-design/pro-components';
import { Button, Col, Flex, Input, message, Modal, Radio, Row, Space, Upload, UploadProps } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import PredictBlock from '@/components/Creative/PredictBlock';
import { IS_TRIAL_ACCOUNT, MINI_LOADING_ICON, UPLOAD_URL } from '@/constants';
import { predict, predict4Video, PredictVO, updatePoint } from '@/services/PointController';
import './index.less';
import { startCreateVideo } from '@/services/CreativeController';
import IconFont from '@/components/IconFont';
import TopupModal from '@/pages/Topup/Topup';
import ImageSelector from '@/components/Creative/ImageSelector';
import WatermarkImage from '@/components/WatermarkImage';
import { UploadFile } from 'antd/es/upload/interface';
import TaskOutputBlock, { TaskOutputBlockRef } from '@/components/Creative/TaskOutputBlock';
import FREE_BG from '@/assets/images/free-bg.png';
import { isVipUser } from '@/services/UserController';
import { getUserInfo } from '@/utils/utils';
import { formatFullTime } from "@/utils/dateUtils";

const { Dragger } = Upload;

const CreateVideo: React.FC = () => {
	const [selectedImages, setSelectedImages] = useState<string[]>([]);
	const [title, setTitle] = useState<string | null>(null);
	const [canSave, setCanSave] = useState(false);
	const [predictVO, setPredictVO] = useState<PredictVO | null>(null);
	const [fileList, setFileList] = useState<Array<UploadFile>>([]);
	const [isCommiting, setIsCommiting] = useState<boolean>(false);
	const [showTopupModal, setShowTopupModal] = useState(false);
	const [showImageSelector, setShowImageSelector] = useState(false);
	const [showVipNotice, setShowVipNotice] = useState(false);
	const [showDeleteImg, setShowDeleteImg] = useState<string | null>(null);
	const isTrialAccount = sessionStorage.getItem(IS_TRIAL_ACCOUNT) === 'Y';
	const userInfo = getUserInfo();

	const maxImageCount = 10;
	const [timeSecs4Video, setTimeSecs4Video] = useState(5);

	const outputRef = useRef<TaskOutputBlockRef>(null);

	useEffect(() => {
		doPredict();
	}, [timeSecs4Video, selectedImages]);

	useEffect(() => {
		setCanSave(selectedImages.length > 0);
	}, [selectedImages]);

	const doPredict = async (isCommit = false) => {
		const res = await predict4Video({
			imageNum: selectedImages?.length > 0 ? selectedImages.length : 1,
			timeSecs4Video: timeSecs4Video
		});
		if (res) {
			if (userInfo?.roleType === 'DEMO_ACCOUNT') {
				res.needTopup = false;
			}

			setPredictVO(res);
			if (isCommit && res.needTopup) {
				setShowTopupModal(true);
				setCanSave(false);
				return false;
			}
		}

		return true;
	};

	const handleShowImageSelector = (event: React.MouseEvent<HTMLButtonElement>) => {
		event.stopPropagation();
		setShowImageSelector(true);
	};

	const removeImg = (imgUrl: string, event: any) => {
		event.stopPropagation();
		setSelectedImages((prevImages) => prevImages.filter((image) => image !== imgUrl));
		setFileList((prevFileList) => prevFileList.filter((file) => file.response && file.response.success && file.response.data !== imgUrl));
	};

	const uploadProps: UploadProps = {
		fileList: fileList,
		onChange(info) {
			handleFileChange(info);
		},
		accept: 'image/png, image/jpeg',
		action: UPLOAD_URL,
		showUploadList: false,
		multiple: true,
		name: 'file',
		maxCount: maxImageCount,
		beforeUpload(file, fileList) {
			return handleBeforeUpload(file, fileList);
		},
	};

	let warning = false;
	const handleBeforeUpload = (file, fileList) => {
		// 检查文件大小，限制为10MB
		const maxSize = 10 * 1024 * 1024; // 10MB in bytes
		if (file.size > maxSize) {
			message.error('图片大小不能超过10MB，请选择较小的图片');
			return false;
		}

		if (selectedImages.length + fileList.length <= maxImageCount) {
			return true;
		}

		if (!warning) {
			warning = true;
			message.info(`最多选择${maxImageCount}张图片`);

			//防止重复报错
			setTimeout(() => {
				warning = false;
			}, 500);
		}

		return true;
	};

	const handleFileChange = ({ fileList }) => {
		let newFileList = [...fileList];
		let newSelectedImages = [...selectedImages];

		for (let i = 0; i < newFileList.length; i++) {
			const file = newFileList[i];

			if (file && file.response && !file.response.success) {
				message.error('上传图片异常，请重试');
				return;
			}

			if (file && file.response && file.response.success
				&& !newSelectedImages.some(e => e === file.response.data)) {
				if (newSelectedImages.length < maxImageCount) {
					newSelectedImages.push(file.response.data);
				}
			}
		}

		// 只保留前 maxImageCount 个文件
		if (selectedImages.length > maxImageCount) {
			newFileList = newFileList.slice(0, (maxImageCount - 1) - selectedImages.length);
		}

		setFileList(newFileList);
		setSelectedImages(newSelectedImages);
	};

	const mockVideoRecord = (title: string | null, images: string[]) => {
		return {
			id: Number(new Date().getTime().toString().slice(-7)),
			type: 'CREATE_VIDEO',
			showImage: images[0],
			batchCnt: 1,
			extInfo: {
				originImage: images,
			},
			status: 'PROCESSING',
			createTime: formatFullTime(new Date()),
			processing: true,
			title,
		}
	}

	const handleCreate = async () => {
		if (!canSave || !await doPredict(true)) {
			return;
		}

		if (userInfo?.roleType === 'DEMO_ACCOUNT') {
			outputRef.current?.mock(mockVideoRecord(title, selectedImages));
			setSelectedImages([]);
			setFileList([]);
			return;
		}

		const titleTrim = title?.trim();

		if (titleTrim?.length && titleTrim?.length > 32) {
			message.error('标题长度不能超过32个字符');
			return;
		}
		setIsCommiting(true);

		const res = await startCreateVideo({ title: titleTrim, images: selectedImages, timeSecs4Video });
		setIsCommiting(false);
		updatePoint();
		if (res) {
			outputRef.current?.refresh();
			setSelectedImages([]);
			setFileList([]);
		}
	};

	const DeletableImage = ({ imgUrl }) => (
		<div style={{ position: 'relative', borderRadius: 8, overflow: 'hidden', width: 'fit-content' }}
			onClick={e => e.stopPropagation()}
			onMouseEnter={() => setShowDeleteImg(imgUrl)} onMouseLeave={() => setShowDeleteImg(null)}>
			<WatermarkImage src={imgUrl} height={184} />
			{showDeleteImg === imgUrl &&
				<div className={'image-selector-round video-delete-round'} style={{ position: 'absolute', right: 8, top: 8 }}
					onClick={e => removeImg(imgUrl, e)}>
					<IconFont type={'icon-icon_shanchu'} style={{ fontSize: 16 }} />
				</div>
			}
		</div>
	);

	return (
		<PageContainer>
			<Row className={'row-container video-row-container'}>
				<Col span={16} className={'work-block'} style={{ gap: 16, padding: 0 }}>
					<Flex className={'video-header'} justify={'center'}>
						<div className={'text14 weight video-header-title'}>批量导入图片，每张图片可生成5~10秒的视频</div>
					</Flex>

					<Flex vertical gap={16} className={'video-content'}>
						<div className={'text16 color-1a weight'}>填写标题
							<span className={'color-72'}>（非必填，长度不超过32个字）</span>
						</div>

						<Input placeholder={'请输入标题'} style={{ width: '100%' }} onChange={e => setTitle(e.target.value)} />

						<Flex vertical={true} gap={16}>
							<span className={'text16 color-1a weight'}>单条视频时长</span>
							<Radio.Group options={[{ label: '5秒', value: 5 }, { label: '10秒', value: 10 }]}
								value={timeSecs4Video}
								onChange={e => setTimeSecs4Video(e.target.value)} />
						</Flex>

						<div className={'text16 color-1a weight'}>选择图片
							<span className={'color-72'}>（{selectedImages.length}/{maxImageCount}）</span>
						</div>

						<div className={'text14 color-96'}>你可以选择最多{maxImageCount}张图片，支持jpg、png、jpeg格式，单张图片大小不超过10MB</div>

						<Flex gap={8}>
							<Dragger className={'video-upload'} {...uploadProps}>
								{selectedImages.length <= 0 &&
									<Flex vertical gap={8}>
										<Flex gap={16} align={'center'} justify={'center'}>
											<Button type={'primary'} style={{ width: 196, height: 38 }}
												icon={<IconFont type={'icon-lishirenwu_weixuanzhong1'} style={{ fontSize: 16 }} />}
												onClick={handleShowImageSelector}>
												从历史创作中选择
											</Button>
											<Button style={{ width: 132, height: 38, border: '1px solid #B5C7FF' }}
												icon={<IconFont type={'icon-shangchuan'} style={{ fontSize: 16, color: '#366EF4' }} />}>
												<div className={'color-36 weight'}>本地上传</div>
											</Button>
										</Flex>
										<div className="text14" style={{ color: 'red' }}>
											仅限MuseGate产品上AI学习服装后的创作图片，AI未学习的服装无法生成视频
										</div>
										<div className="text14 color-96">点击/粘贴/拖拽图片至此</div>
									</Flex>
								}

								{selectedImages.length > 0 &&
									<Row gutter={[12, 6]} style={{ margin: 12 }}>

										{selectedImages.map((image, index) => (
											<Col span={5} key={index}
												style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
												<DeletableImage imgUrl={image} />
											</Col>
										))}

										{selectedImages.length < maxImageCount &&
											<Col span={6}>
												<Flex vertical gap={12} align={'center'} justify={'center'} style={{ height: '100%' }}>
													<Button type={'primary'} style={{ width: 196, height: 38 }}
														icon={<IconFont type={'icon-lishirenwu_weixuanzhong1'} style={{ fontSize: 16 }} />}
														onClick={handleShowImageSelector}>
														从历史创作中选择
													</Button>
													<Button style={{ width: 196, height: 38, border: '1px solid #B5C7FF' }}
														icon={<IconFont type={'icon-shangchuan'}
															style={{ fontSize: 16, color: '#366EF4' }} />}>
														<div className={'color-36 weight'}>本地上传</div>
													</Button>
													<div className="text14 color-96">点击/粘贴/拖拽图片至此</div>
												</Flex>
											</Col>
										}
									</Row>
								}
							</Dragger>
						</Flex>

					</Flex>
				</Col>

				<Col span={12} className={'output-block'}>
					<TaskOutputBlock sliderValue={12} types={['CREATE_VIDEO']} ref={outputRef} pollingTimeout={30000} />
				</Col>
			</Row>

			<footer className={'footer-bar'}>
				<div className={'fixed-tab-bar'}>
					<div className={'fixed-tab-content'}>
						<PredictBlock creativeType={'CREATE_VIDEO'} predictVO={predictVO} type={'CUSTOM'} />

						<div style={{ position: 'relative' }}>
							<Button type="primary" className={'create-btn'}
								disabled={!canSave || isCommiting}
								icon={isCommiting ? <img src={MINI_LOADING_ICON} width={16} height={16} alt={'logo'}
									className={'loading-img'} /> : ''}
								onClick={handleCreate}> {isCommiting ? '' : (predictVO && predictVO.needTopup ? (isTrialAccount ? '余额不足' : '余额不足，去充值') : '生成视频')}</Button>

							<div className={'text12 font-pf repair-free-icon-desc'}>限时优惠</div>
							<img src={FREE_BG} alt={'logo'} width={64} height={20}
								style={{ position: 'absolute', top: 0, right: 0, zIndex: 1 }} />
						</div>

					</div>
				</div>
			</footer>

			{showImageSelector &&
				<ImageSelector maxChoose={maxImageCount} value={selectedImages} onFinish={(value) => {
					setSelectedImages(value);
					setShowImageSelector(false);
				}} onCancel={() => setShowImageSelector(false)} />
			}

			{showTopupModal &&
				<TopupModal visible={showTopupModal} onClose={() => setShowTopupModal(false)}
					onPaySuccess={() => {
						setShowTopupModal(false);
						doPredict();
					}} />
			}

			{showVipNotice &&
				<Modal
					open={true}
					onCancel={() => setShowVipNotice(false)}
					centered={true}
					width={400}
					footer={null}
					closable={false}
				>
					<Flex vertical gap={24}>
						<Flex align={'center'} justify={'center'} className={'text16 text-center weight'}>
							{isTrialAccount ? '体验账号不支持充值' : '该功能仅对付费客户开放，请先充值'}
						</Flex>

						<Flex justify={'center'} align={'center'} gap={16}>
							<Button onClick={() => setShowVipNotice(false)} style={{ width: 140 }}>取消</Button>
							{!isTrialAccount &&
								<Button type={'primary'} onClick={() => {
									setShowTopupModal(true);
									setShowVipNotice(false);
								}} style={{ width: 140 }}>确定</Button>
							}
						</Flex>
					</Flex>
				</Modal>
			}
		</PageContainer>
	);
};

export default CreateVideo;