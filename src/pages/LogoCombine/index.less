@import "@/app";

@body-height: calc(100vh - @navigation-height - @tab-bar-height);
@body-width: calc(100vw - @menu-width);
@logo-demo-container-width: 200px;
@body-padding: 16px;
@body-gap: 16px;

.logo-upload {
  width: calc(@body-width / 2 - @body-padding * 2 - @logo-demo-container-width - @body-gap);
  height: 228px;
  background: #F5F6F9;
  border-radius: 8px;
}

.logo-upload-main-img {
  width: 100% !important;
  height: calc(@body-height - 228px - 24px * 6 - 12px);
}

.main-img-fix {
  height: 100%;
  max-width: 100%;
  object-fit: contain;
}

.logo-demo-container {
  width: @logo-demo-container-width;
  padding: 8px;
  border-radius: 8px;

  background: #F5F6F9;
}

.logo-demo {
  position: relative;
  width: 88px;
  height: 88px;
}

.logo-demo-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 88px;
  height: 88px;
  background: rgba(0, 0, 0, 0.4);
}

.logo-demo-btn{
  position: absolute;
  left: 7px;
  bottom: 7px;
  width: 74px;
  height: 36px;
}

.logo-combine-upload-image-wrapper {
  position: relative;
  width: calc(@body-width / 2 - @body-padding * 2 - 8px);
  height: 228px;
  background: #F5F6F9;
  border-radius: 8px;
  overflow: hidden;

  img {
    width: auto; /* 让图片宽度充满容器 */
    height: 228px; /* 保持图片高度自动适应宽度的变化，保持原始比例 */
    object-fit: cover; /* 保持图片的宽高比，并填充容器，可能导致部分图片被裁剪 */
  }
}

.logo-combine-upload-image-btn {
  position: absolute;
  right: 12px;
  bottom: 12px;
  height: 38px;
}

.logo-position-wrapper {
  width: calc((100% - 8px) / 2) !important;
  height: auto !important;;
  border-radius: 8px;
  opacity: 1;
  box-sizing: border-box;
  border: 0.5px solid #D8D8D8;
  cursor: pointer;
  padding: 8px;
}

.upload-main-img-fix {
  width: 100% !important;
  height: 380px;
}