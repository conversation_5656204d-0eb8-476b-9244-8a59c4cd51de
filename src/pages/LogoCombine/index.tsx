import { <PERSON><PERSON>ontainer } from '@ant-design/pro-components';
import { <PERSON>ton, Col, Flex, message, Row, Upload, UploadProps } from 'antd';
import { MINI_LOADING_ICON } from '@/constants';
import ElementWithTypeBlock from '@/components/Creative/ElementWithTypeBlock';
import React, { useEffect, useRef, useState } from 'react';
import './index.less';
import LOGO_DEMO_1 from '@/assets/images/logo-demo-1.png';
import LOGO_DEMO_2 from '@/assets/images/logo-demo-2.png';
import LOGO_DEMO_3 from '@/assets/images/logo-demo-3.png';
import LOGO_DEMO_4 from '@/assets/images/logo-demo-4.png';
import LOGO_FRONT_DEMO from '@/assets/images/logo-front-demo.png';
import LOGO_BACK_DEMO from '@/assets/images/logo-back-demo.png';
import { deepCopy, sleep } from '@/utils/utils';
import { ElementConfig, getElementConfig } from '@/services/ElementController';
import ElementWithPresetBlock from '@/components/Creative/ElementWithPresetBlock';
import PredictBlock from '@/components/Creative/PredictBlock';
import { predict, PredictVO, updatePoint } from '@/services/PointController';
import LogoCombineDrawer, { LogoCombineDrawerRef } from '@/components/Creative/LogoCombineDrawer';
import IconFont from '@/components/IconFont';
import { CreativeStatus, startLogoCombine } from '@/services/CreativeController';
import TopupModal from '@/pages/Topup/Topup';
import OutputBlock, { OutputBlockRef } from '@/components/Creative/OutputBlock';
import { dataURLToBlob, loadLocalImageFile, resizeImage } from '@/utils/imageUtils';
import { removeFaceConfig, resetData } from '@/pages/Creation';

const { Dragger } = Upload;

const emptyData = {
  configs: {},
  imageNum: 0,
  proportion: 'THREE_FOUR',
  image: '',
};

const colorList = ['#FFFFFF', '#E9EEF0', '#ACACAC', '#262626', '#E9DAD2', '#DEBDAD', '#BE807D', '#662B3A', '#FCECD0', '#EBC191', '#EF4656', '#8F6EB2', '#929F7D', '#345C4D', '#75BFE2', '#4B71AF'];

const dummyRequest = ({ file, onSuccess }) => {
  setTimeout(() => {
    onSuccess('ok');
  }, 0);
};

function isCanSave(data: any, predictVO: PredictVO | null, logoImageUrl: string | null, logoPosition: string, configs: Array<ElementConfig>) {
  const current = deepCopy(data.configs);
  removeFaceConfig(current, logoPosition, configs);
  // @ts-ignore
  const result = data.proportion && data.imageNum > 0 && Object.keys(configs).every(key => configs[key] !== null) && logoImageUrl;
  if (result === null || !result) {
    return false;
  }

  return predictVO != null && !predictVO.needTopup;
}

const LogoCombined = () => {
  const [sliderValue, setSliderValue] = useState(12);
  const [data, setData] = useState(emptyData);
  const [configs, setConfigs] = useState<Array<ElementConfig>>([]);
  const [predictVO, setPredictVO] = useState<PredictVO | null>(null);
  const [canSave, setCanSave] = useState(false);
  const [status, setStatus] = useState<CreativeStatus>('INIT');
  const [logoPosition, setLogoPosition] = useState('front');

  const [logoFileList, setLogoFileList] = useState([]);
  const [logoImageUrl, setLogoImageUrl] = useState<null | string>(null);
  const [logoFile, setLogoFile] = useState<null | File>(null);
  const [showTopupModal, setShowTopupModal] = useState(false);
  const [commiting, setCommiting] = useState(false);

  const [extended, setExtended] = useState(true);
  const [demoHover, setDemoHover] = useState<{}>({});
  const [acceptTypes, setAcceptTypes] = useState<Array<string>>([]);

  const drawerRef = useRef<LogoCombineDrawerRef>(null);
  const outputRef = useRef<OutputBlockRef>(null);

  const handleConfigChange = (configId: number, value: Array<ElementConfig> | null) => {
    const copy = deepCopy(data);
    copy.configs[configId] = value;
    setData(copy);
  };

  const getConfigsByKey: (configKey: string) => Array<ElementConfig> | null = (configKey: string) => {
    for (let key in data.configs) {
      if (data.configs[key] && data.configs[key].some(item => item.configKey === configKey)) {
        return data.configs[key];
      }
    }
    return null;
  };

  const getConfigTypesByKey = (configKey: string, extKey: string) => {
    const extKeys = extKey ? [extKey] : [];
    for (let key in data.configs) {
      if (data.configs[key] && data.configs[key].some(item => item.configKey === configKey)) {
        let types = data.configs[key].type;
        types = types ? types : [];
        return [...types, ...extKeys];
      }
    }
    return extKeys;
  };

  const getConfigExtIdsByKey = (configKey: string, extKey: string) => {
    let extIds: null | Array<number> = null;
    for (let key in data.configs) {
      if (data.configs[key] && data.configs[key].some(item => item.configKey === configKey)) {

        data.configs[key].forEach(item => {
          if (item.extInfo && item.extInfo[extKey]) {
            const extArray = JSON.parse(item.extInfo[extKey]);
            if (!extIds) {
              extIds = extArray;
            } else {
              extIds = [...extIds, ...extArray];
            }
          }
        });

      }
    }
    return extIds;
  };

  const handleLogoFileChange = ({ file, fileList }) => {
    setLogoFileList(fileList);
    if (file) {
      // 文件读取
      const fileObj = file.originFileObj;
      setLogoFile(fileObj);
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e && e.target) {
          setLogoImageUrl(e.target.result as string);
        }
      };
      reader.readAsDataURL(fileObj);
    }

    if (status === 'FINISHED') {
      setStatus('INIT');
    }
  };

  const changeClothStyleType = (type: string) => {
    setAcceptTypes(type === 'Female' ? ['female-model'] : ['male-model']);
  };

  const handleDemoTry = (src: string) => {
    console.log(src);
    setLogoImageUrl(src);
    setLogoFile(null);

    loadLocalImageFile(src).then(file => {
      if (file) {
        setLogoFile(file);
      }
    });
  };

  const resortColors = (configs: Array<ElementConfig>) => {
    if (!configs || configs.length <= 0) {
      return;
    }

    const clothStyleRoot = configs.findLast(item => item.configKey === 'CLOTH_STYLE');
    if (!clothStyleRoot || !clothStyleRoot.children) {
      return;
    }
    clothStyleRoot.children.forEach(clothStyle => {
      if (!clothStyle || !clothStyle.children || clothStyle.children.length <= 0) {
        return;
      }

      const map = new Map();
      clothStyle.children.forEach((item) => {
        map.set(item.extInfo['clothColor'], item);
      });

      let sortedList = colorList.map(each => map.get(each));
      sortedList = sortedList.filter(each => each !== undefined);
      clothStyle.children = sortedList;
    });
  };

  const uploadProps: UploadProps = {
    fileList: logoFileList,
    onChange(info) {
      handleLogoFileChange(info);
    },
    accept: 'image/png, image/jpeg',
    showUploadList: false,
    multiple: false,
    name: 'file',
    customRequest(option) {
      // @ts-ignore
      dummyRequest(option);
    },
    beforeUpload: async (file) => {
      return await resizeImage(file);
    },
  };

  const doPredict = async (imageNum: number) => {
    if (imageNum === null) {
      return;
    }
    predict('LOGO_COMBINE', imageNum).then((res) => {
      if (res) {
        setPredictVO(res);
      }
    });
  };

  const handleDownLoad = () => {
    const drawerRefCurr = drawerRef.current;
    if (!drawerRefCurr) return;
    drawerRefCurr.clearTransformer();
    drawerRefCurr.downloadImage();
  };

  const handleCreate = async () => {
    if (predictVO && predictVO.needTopup) {
      setShowTopupModal(true);
      return;
    }

    const drawerRefCurr = drawerRef.current;
    const outputRefCurr = outputRef.current;
    if (!canSave || !drawerRefCurr || !outputRefCurr || commiting) {
      if (commiting) {
        message.warning('正在生成图片，请勿重复提交');
      }
      return;
    }

    console.log('开始生成图片');
    setCommiting(true);
    setExtended(false);

    drawerRefCurr.clearTransformer();

    await sleep(50);

    const dataUrl = drawerRefCurr.getDataUrl();
    const payload = resetData(data, logoPosition, configs);

    // 带文件上传的表单
    const formData = new FormData();
    formData.append('image', dataURLToBlob(dataUrl));
    formData.append('payload', JSON.stringify(payload));

    startLogoCombine(formData).then(res => {
      setCommiting(false);
      updatePoint();
      doPredict(data.imageNum);
      if (res) {
        //更新图片生成数量
        const originStatus = status;
        //开始轮询状态,后台如果已经在执行和轮询了，则不再进行轮询
        if (originStatus !== 'QUEUE' && originStatus !== 'PROCESSING') {
          //将状态更改为提交状态
          setStatus(res.status);
          outputRefCurr.polling(res.id);
        }
      }
    });
  };

  useEffect(() => {
    setCanSave(isCanSave(data, predictVO, logoImageUrl, logoPosition, configs));
  }, [data, predictVO, logoImageUrl]);

  useEffect(() => {
    for (let key in data.configs) {
      let find = configs.find(item => item.id === Number.parseInt(key));
      if (find?.configKey === 'REFER' && data.configs[key]) {
        const copy = deepCopy(data);
        const size = copy.configs[key].length;
        if (data.imageNum !== size) {
          copy.imageNum = size;
          setData(copy);
        }
      }
    }
  }, [data.configs]);

  useEffect(() => {
    if (data.imageNum > 0) {
      doPredict(data.imageNum);
    } else {
      // @ts-ignore
      setPredictVO({ musePoint: 0 });
    }
  }, [data.imageNum]);

  useEffect(() => {
    getElementConfig('LOGO_COMBINE', 'ALL', false, true).then(res => {
      if (res) {
        resortColors(res); //先根据颜色重新排序
        setConfigs(res);
        res.forEach(item => {
          let preset: null | Array<ElementConfig> = null;
          if (item.configKey === 'CLOTH_STYLE' || item.configKey === 'LOGO_POSITION') {
            preset = item.children ? [item.children[0]] : null;
          }
          data.configs[item.id] = preset;
        });
      }
    });
  }, []);

  const LogoPositionBlock = ({ image, title, current, value }) => {
    return <>
      <Flex gap={12} align={'center'}
            className={'logo-position-wrapper' + (current === value ? ' work-item-selected' : '')}
            onClick={() => setLogoPosition(value)}>
        <img alt="img" src={image} width={72} height={72} />
        <div className={'text14 font-pf color-1a text-center'}>{title}</div>
      </Flex>
    </>;
  };

  const LogoDemoBlock = ({ src, index }) => {
    return <>
      <div style={{ position: 'relative' }} onMouseEnter={() => {
        if (demoHover[index]) {
          return;
        }
        setDemoHover({ [index]: true });
      }}
           onMouseLeave={() => setDemoHover({ [index]: false })}>
        <img src={src} alt={'logo-demo'} className={'logo-demo'} />
        <>
          <div className={'logo-demo-mask'} style={{ display: demoHover[index] ? 'block' : 'none' }} />
          <Button type={'primary'} className={'logo-demo-btn'} style={{ display: demoHover[index] ? 'block' : 'none' }}
                  onClick={() => handleDemoTry(src)}>试一下</Button>
        </>
      </div>
    </>;
  };

  return <PageContainer>
    <Row className={'row-container'}>
      <Col span={12} className={'work-block'}>
        <Flex vertical align={'flex-start'} gap={16} className={'work-item-container'}>
          <Flex vertical align={'flex-start'} gap={8}>
            <div className={'text16 font-pf color-n weight'}>上传印花图案</div>
            {!logoImageUrl &&
              <Flex gap={8}>

                <Dragger className={'logo-upload'} {...uploadProps}>
                  <Button type={'primary'} style={{ width: 144, height: 38 }}>上传我的印花</Button>
                  <div className="text12 color-b font-pf margin-top-8">支持png、jpg格式</div>
                </Dragger>

                <Flex vertical justify={'flex-start'} align={'center'} gap={8} className={'logo-demo-container'}>

                  <Flex wrap align={'center'} justify={'center'} gap={8}>
                    <LogoDemoBlock src={LOGO_DEMO_1} index={1} />
                    <LogoDemoBlock src={LOGO_DEMO_2} index={2} />
                    <LogoDemoBlock src={LOGO_DEMO_3} index={3} />
                    <LogoDemoBlock src={LOGO_DEMO_4} index={4} />
                  </Flex>
                  <div className={'text14 color-72 font-pf'}>示例印花</div>
                </Flex>
              </Flex>
            }

            {logoImageUrl &&
              <Flex gap={8} justify={'center'} className={'logo-combine-upload-image-wrapper'}>
                <img src={logoImageUrl} alt={''} />
                <Flex gap={8} className={'logo-combine-upload-image-btn'}>
                  <Button icon={<IconFont type={'icon-icon_shanchu'} style={{ fontSize: 16 }} />}
                          onClick={() => setLogoImageUrl(null)} style={{ height: 38, width: 38 }} />

                  <Upload {...uploadProps}>
                    <Button style={{ height: 38, width: 104 }}>重新上传</Button>
                  </Upload>
                </Flex>
              </Flex>
            }

          </Flex>

          {/*‘印花位置’配置*/}
          <Flex vertical gap={8} justify={'flex-start'} className={'width-100'}>
            <div className={'text16 font-pf color-n weight'}>选择印花位置</div>

            <Flex gap={8} className={'width-100'}>
              <LogoPositionBlock current={logoPosition} title={'正面印花'} value={'front'} image={LOGO_FRONT_DEMO} />
              <LogoPositionBlock current={logoPosition} title={'背面印花'} value={'back'} image={LOGO_BACK_DEMO} />
            </Flex>
          </Flex>

          {/*‘服装款式’配置*/}
          {configs.filter(c => c.configKey === 'CLOTH_STYLE').map((config) => (
            <ElementWithPresetBlock key={'CLOTH_STYLE'} config={config} current={data.configs[config.id]}
                                    conditions={{ type: [logoPosition] }} title={'款式'} onChange={handleConfigChange}
                                    pageSize={8} changeType={changeClothStyleType} forceProportion={true} />
          ))}

          {/*‘参考图’配置*/}
          {configs.filter(c => c.configKey === 'REFER').map((config) => (
            <ElementWithPresetBlock key={'REFER'} config={config} current={data.configs[config.id]}
                                    multipleOptions={true} maxChoose={8} title={'参考图'} pageSize={8}
                                    onChange={handleConfigChange} proportion={'THREE_FOUR'}
                                    conditions={{
                                      type: getConfigTypesByKey('CLOTH_STYLE', logoPosition),
                                      clothStyles: getConfigsByKey('CLOTH_STYLE'),
                                    }} />
          ))}

          {/*‘模特’配置*/}
          {logoPosition === 'front' && configs.filter(c => c.configKey === 'FACE').map((config) => (
            <ElementWithTypeBlock key={'FACE'} config={config} defaultType={'male-model'} acceptTypes={acceptTypes}
                                  current={data.configs[config.id]} onChange={handleConfigChange} moreBg={''}
                                  moreIcon={''}
                                  conditions={{ faceIds: getConfigExtIdsByKey('REFER', 'faces') }} />
          ))}

          <div />
        </Flex>

      </Col>
      <Col span={sliderValue} className={'output-block'}>
        {logoImageUrl &&
          <LogoCombineDrawer expand={extended} logoImageUrl={logoImageUrl} logoFile={logoFile} ref={drawerRef}
                             changeExpand={setExtended} clothStyle={getConfigsByKey('CLOTH_STYLE')?.[0]} />
        }

        <OutputBlock types={['LOGO_COMBINE']} status={status} sliderValue={sliderValue}
                     changeSliderValue={setSliderValue} refreshStatus={setStatus} ref={outputRef} />
      </Col>
    </Row>
    <footer className={'footer-bar'}>
      <div className={'fixed-tab-bar'}>
        <div className={'fixed-tab-content'}>
          <PredictBlock creativeType={'LOGO_COMBINE'} predictVO={predictVO} data={data} type={'CUSTOM'}
                        imagePoint={0} />
          <Button type="primary" className={'create-btn'}
                  disabled={(!canSave && (status === 'INIT' || status === 'FINISHED')) && (predictVO === null || !predictVO.needTopup)}
                  icon={(status === 'INIT' || status === 'FINISHED') ? '' :
                    <img src={MINI_LOADING_ICON} width={16} height={16} alt={'logo'}
                         className={'loading-img'} />}
                  onClick={handleCreate}> {(status === 'INIT' || status === 'FINISHED') ? (predictVO && predictVO.needTopup ? '余额不足，去充值' : '生成图片') : ''}</Button>

          <Button type="primary" className={'create-btn'} disabled={!logoImageUrl}
                  onClick={handleDownLoad}>下载图片</Button>
        </div>
      </div>
    </footer>
    {showTopupModal &&
      <TopupModal visible={showTopupModal} onClose={() => setShowTopupModal(false)}
                  onPaySuccess={() => {
                    setShowTopupModal(false);
                    doPredict(data.imageNum);
                  }} />
    }
  </PageContainer>;
};

export default LogoCombined;