import React, { useRef, useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Flex, InputNumber, Modal, message, Alert } from 'antd';
import PredictBlock from '@/components/Creative/PredictBlock';
import { EXPERIENCE_POINT, IMAGE_POINT, IMAGE_POINT_CHANGE_EVENT, MINI_LOADING_ICON } from '@/constants';
import { predict, PredictVO, queryImagePoint, updatePoint } from '@/services/PointController';
import TaskOutputBlock, { TaskOutputBlockRef } from '@/components/Creative/TaskOutputBlock';
import { getUserInfo } from '@/utils/utils';
import ImageUploader, { ImageUploaderButton } from '@/components/Common/ImageUploader';
import '@/pages/toolkit/PartialRedraw/index.less';
import TextArea from 'antd/lib/input/TextArea';
import SmudgeCanvas from '@/components/imageOperate/smudgeCanvas/SmudgeCanvasCopy';
import TopupModal from '@/pages/Topup/Topup';
import { getBlobByRef } from '@/utils/imageUtils';
import { CreativeType, partialRedraw, repairHands } from '@/services/CreativeController';
import { useLocation, useNavigate } from 'react-router-dom';
import { SmudgeToolkit, useSmudgeInteraction } from '@/components/imageOperate/smudgeCanvas/SmudgeBrushTool';
import useSmudgeToolkit from '@/hooks/useSmudgeToolkit';
import { LoadingOutlined } from '@ant-design/icons';

const userInfo = getUserInfo();
const PartialRedraw:React.FC<{creativeType: CreativeType}> = ({creativeType = 'PARTIAL_REDRAW'}) => {
  const emptyData = {
    taskId: undefined,
    modelId: undefined,
    image: '',
    redrawDesc: '',
    imageNum: 4,
  };
  const type: string = 'CUSTOM';
  const [data, setData] = useState(emptyData);
  const [predictVO, setPredictVO] = useState<PredictVO | null>(null);
  const [canSave, setCanSave] = useState<boolean>(false);
  const [sliderValue, setSliderValue] = useState(12);

  const outputBlockRef = useRef(null);
  const taskOutputRef = useRef<TaskOutputBlockRef>(null);
  const stageRef = useRef(null);
  const [showTopupModal, setShowTopupModal] = useState(false);
  const [commiting, setCommiting] = useState(false);
  const [imageSource, setImageSource] = useState<'upload' | 'history'>('upload');
  const location = useLocation();
  const navigate = useNavigate();
  
  // 使用 useSmudgeToolkit 钩子替换之前的 useState
  const { editorState, setEditorState } = useSmudgeToolkit();
  
  // 高级交互功能
  const { onScaleChange, onPositionChange } = useSmudgeInteraction([editorState, setEditorState]);
  
  // 获取当前绘制的线条
  const lines = editorState.lines;

  useEffect(() => {
    const img = localStorage.getItem('imageUrl');
    if (img) {
      handleImageChange(img);
      setImageSource('history');
    }
    updatePoint();
    predict('REPAIR_DETAIL', data.imageNum, null)
      .then(result => {
        if (result) {
          setPredictVO(result);
        }
      });
    return () => {
      localStorage.removeItem('imageUrl');
      localStorage.removeItem('modelId');
    }
  }, []);

  useEffect(() => {
    setCanSave(isCanSave(data.image, data.redrawDesc, data.imageNum, lines));
  }, [data, lines]);

  const isCanSave = (image, redrawDesc, imageNum, lines): boolean => {
    // 公共基础条件检查
    const hasBase = !!image && !!imageNum && !!lines && (lines.length > 0);
    if (!hasBase) return false;
    // 根据类型处理额外条件
    switch (creativeType) {
      case 'PARTIAL_REDRAW':
        return !!redrawDesc;
      case 'REPAIR_HANDS':
        return true;
      default:
        return false; // 处理未定义类型
    }
  };


  const handleCreate = () => {
    if ( imageSource === 'upload' && predictVO && predictVO.needTopup) {
      setShowTopupModal(true);
      return;
    }
    if (commiting) {
      return;
    }
    if (!lines|| lines.length <= 0) {
      message.warning(`请长按鼠标涂抹需要修复的区域`);
      return;
    }
    setCommiting(true);
    const maskBlob = getBlobByRef(stageRef);
    if (!maskBlob) {
      message.warning("蒙版处理异常, 请重新涂抹");
      return;
    }
    const formData = new FormData();
    formData.append('originImage', data.image);
    formData.append('redrawDesc', data.redrawDesc);
    formData.append('imageNum', data.imageNum.toString());
    formData.append('mask', maskBlob);
    async function start() {
      let result;
      switch (creativeType) {
        case 'PARTIAL_REDRAW':
          result = await partialRedraw(formData);
          break;
        case 'REPAIR_HANDS':
          result = await repairHands(formData);
          break;
        default:
          break;
      }
      setCommiting(false);
      updatePoint();
      if (!result) return;
      message.success('开始生成图片');
      taskOutputRef.current?.refresh();
      navigate(location.pathname, {replace: true});
    }
    start();
  };


  const handleImageChange = (image) => {
    setData({ ...data, image: image });
    setEditorState(prev => ({ ...prev, lines: [] }));
  };
  
  const handleTextChange = (e) => {
    setData({ ...data, redrawDesc: e.target.value.trim() });
  };
  
  const handleTextBlur = (e) => {
    setData({ ...data, redrawDesc: e.target.value.trim() });
  };

  const handleLinesChange = (value) => {
    setEditorState(prev => ({ ...prev, lines: value }));
  }

  return (
    <PageContainer>
      <Flex className={'toolkit-row-container'}>
        <div className={'toolkit-work-block'}>
          {/* 上传图片并涂抹 */}
          {data.image ? (
            <div className={'redraw-smudge-container'}>
              <span className={'font-pf text16 weight'}>上传图片</span>
              <span className={'font-pf text12 normal'}>
                长按鼠标涂抹需修复的区域后，点击开始按钮
              </span>
              <Flex vertical className={'redraw-smudge'}>
                <SmudgeCanvas
                  imageUrl={data.image}
                  singleSmudge={false}
                  stageCmtRef={stageRef}
                  lines={lines}
                  handleLinesChange={handleLinesChange}
                  editorState={editorState}
                  onScaleChange={onScaleChange}
                  onPositionChange={onPositionChange}
                />
                <ImageUploaderButton onImageChange={handleImageChange} />
              </Flex>
              <SmudgeToolkit useEditorState={[editorState, setEditorState]} />
            </div>
          ) : (
            <ImageUploader
              className={'redraw-image-upload'}
              title={'上传图片'}
              image={data.image}
              onImageChange={handleImageChange}
              historyFree
              uploadCharge
              setImageSource={setImageSource}
              resize={2100}
            />
          )}
          {/* 输入重绘内容 */}
          {creativeType === 'PARTIAL_REDRAW' && (
            <Flex vertical gap={8} className={'redraw-text-container'}>
              <div className={'redraw-text-title'}>请输入需要重绘的内容</div>
              <TextArea
                className={'redraw-text-area'}
                showCount={true}
                maxLength={100}
                style={{ height: 80, resize: 'none', fontSize: 14 }}
                rows={4}
                allowClear={true}
                onChange={handleTextChange}
                onBlur={handleTextBlur}
              />
            </Flex>
          )}
          <div className={'toolkit-number-input-container'}>
            <div className={'text16 font-pf color-n weight'}>生成数量</div>
            <InputNumber value={4} disabled={true} />
          </div>
        </div>
        <div ref={outputBlockRef} className={'toolkit-output-block'}
             style={{ width: `calc((100% - 644px) * ${sliderValue / 12})`, maxWidth: '100%' }}
        >
          <TaskOutputBlock
            sliderValue={sliderValue}
            changeSliderValue={setSliderValue}
            types={[creativeType]}
            ref={taskOutputRef}
            pollingTimeout={3000}
          />
        </div>
      </Flex>
      <footer className={'toolkit-footer'}>
        <div className={'toolkit-footer-content'}>
          {canSave && imageSource == 'upload' && (
            <PredictBlock
              creativeType={creativeType}
              predictVO={predictVO}
              data={data}
              type={type}
            />
          )}
          <Button
            type="primary"
            className={'toolkit-submit-btn'}
            disabled={
              !(
                (predictVO?.needTopup && imageSource === 'upload') ||
                (canSave && !commiting)
              )
            }
            icon={
              commiting ? (
                <LoadingOutlined style={{fontSize: 16, color: 'fff'}} />
              ) : (
                ''
              )
            }
            onClick={handleCreate}
          >
            {' '}
            {predictVO?.needTopup && imageSource === 'upload'
              ? '余额不足，去充值'
              : commiting
                ? '生成中'
                : '生成图片'}
          </Button>
        </div>
      </footer>
      {showTopupModal && (
        <TopupModal
          visible={showTopupModal}
          onClose={() => setShowTopupModal(false)}
          onPaySuccess={() => {
            setShowTopupModal(false);
            updatePoint();
          }}
        />
      )}
    </PageContainer>
  );
};

export default PartialRedraw;