@import "@/app";

@body-height: calc(100vh - @navigation-height - @tab-bar-height);
@redraw-work-block-width: 644px;

.redraw-row-container {
  //padding-left: 16px;
  width: 100%;
  height: calc(@body-height - 90px);
  background: #FFFFFF;
}

.redraw-image-upload {
  box-sizing: border-box;
  min-height: 680px;
  height: 680px;
}

.redraw-text-container {
  width: 100%;
  background-color: #F5F6F9;
  border-radius: 8px;
  padding: 8px 8px;
}

.redraw-text-title {
  font: 500 16px "PingFang SC";
  line-height: 22px;
  color: #1A1B1D;
}

.redraw-text-area .ant-input-data-count {
  bottom: 3px;
  right: 10px;
}

.redraw-work-block {
  padding: 0 8px 0 8px;
  background: linear-gradient(0deg, #FFFFFF, #FFFFFF), #FFFFFF;
  height: 100%;
  max-height: 100vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-content: flex-start;
  justify-content: flex-start;
}

.redraw-work-block-fixed {
  width: @redraw-work-block-width;
}

.redraw-output-block {
  position: absolute;
  width: 100%;
  right: 0;
  height: calc(100vh - @navigation-height - 90px);
  background-color: #F5F6F9;
  //box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  transition: width 0.3s ease-in-out;
  overflow: hidden;
  z-index: 99;
}

.redraw-output-block-fixed {
  width: calc(100% - @redraw-work-block-width);
}

.redraw-smudge-container {
  display: flex;
  flex-direction: column;
  height: auto;
  width: 100%;
  background: #F5F6F9;
  border-radius: 8px;
  gap: 8px;
  padding: 12px;
}

.redraw-smudge {
  position: relative;
  height: 680px;
  width: 100%;
}