import ImageUploader from '@/components/Common/ImageUploader';
import PredictBlock from '@/components/Creative/PredictBlock';
import TaskOutputBlock, {
  TaskOutputBlockRef,
} from '@/components/Creative/TaskOutputBlock';
import MaskCanvas from '@/components/imageOperate/maskCanvas/MaskCanvas';
import TopupModal from '@/pages/Topup/Topup';
import {
  clothImgAutoSeg,
  clothRecolor,
  CreativeVO,
  queryCreativeAndSync,
} from '@/services/CreativeController';
import { predict, PredictVO, updatePoint } from '@/services/PointController';
import { fetchOssBlobUrl } from '@/utils/ossUtils';
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  LoadingOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  Button,
  Card,
  Col,
  ColorPicker,
  Empty,
  Flex,
  message,
  Modal,
  Row,
  Space,
  Spin,
  Switch,
  Tooltip,
} from 'antd';
import { AggregationColor } from 'antd/es/color-picker/color';
import { CSSProperties, useEffect, useRef, useState } from 'react';
import './recolor.less';

// 透明格子背景的CSS样式
const transparentCheckerboardStyle: CSSProperties = {
  backgroundImage: `
    linear-gradient(45deg, #f0f0f0 25%, transparent 25%, transparent 75%, #f0f0f0 75%), 
    linear-gradient(45deg, #f0f0f0 25%, transparent 25%, transparent 75%, #f0f0f0 75%)
  `,
  backgroundSize: '16px 16px',
  backgroundPosition: '0 0, 8px 8px',
};

interface EditButtonProps {
  onClick: (e: React.MouseEvent) => void;
}

// Edit button component with hover effect
const EditButton: React.FC<EditButtonProps> = ({ onClick }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        position: 'absolute',
        top: 8,
        right: 8,
        background: isHovered ? '#38F' : 'rgba(0, 0, 0, 0.6)',
        width: 24,
        height: 24,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        transition: 'background-color 0.3s',
        cursor: 'pointer',
        borderRadius: '2px',
      }}
    >
      <EditOutlined style={{ color: '#FFFFFF', fontSize: '14px' }} />
    </div>
  );
};

// Utility function to load an image from OSS URL
const loadImageFromOss = async (url: string): Promise<HTMLImageElement> => {
  try {
    // Get blob URL from OSS URL
    const blobUrl = await fetchOssBlobUrl(url);

    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = () =>
        reject(new Error('Failed to load image from blob URL'));
      img.src = blobUrl!!;
    });
  } catch (error) {
    console.error('Error loading image from OSS:', error);
    throw error;
  }
};

const RecolorPage = () => {
  const [originImgUrl, setOriginImgUrl] = useState<string>('');
  const [maskImgUrl, setMaskImgUrl] = useState<string>('');
  const [showTopupModal, setShowTopupModal] = useState(false);
  const [predictVO, setPredictVO] = useState<PredictVO | null>(null);
  const [loading, setLoading] = useState(false);
  const [sliderValue, setSliderValue] = useState(12);
  //当前用户正在进行中的任务，最多只能有一个
  const [creativeVO, setCreativeVO] = useState<CreativeVO>();
  const taskOutputRef = useRef<TaskOutputBlockRef>(null);
  const outputBlockRef = useRef(null);
  const [canSave, setCanSave] = useState(false);
  const [committing, setCommitting] = useState<boolean>(false);
  const [imageSource, setImageSource] = useState<'upload' | 'history'>(
    'upload',
  );
  const [segmentedImages, setSegmentedImages] = useState<string[]>([]);
  const [maskImages, setMaskImages] = useState<string[]>([]);

  // 换色流程的状态
  const [originalImage, setOriginalImage] = useState<HTMLImageElement | null>(
    null,
  );
  const [loadedMaskImages, setLoadedMaskImages] = useState<
    Map<string, HTMLImageElement>
  >(new Map());
  const [loadedSegmentImages, setLoadedSegmentImages] = useState<
    Map<string, HTMLImageElement>
  >(new Map());

  const [selectedSegmentIndex, setSelectedSegmentIndex] = useState<number>(-1); // 选中的分割区域索引
  const [selectedMaskImage, setSelectedMaskImage] =
    useState<HTMLImageElement | null>(null); // 选中区域对应的蒙版图片元素
  const [selectedColor, setSelectedColor] = useState<string>(''); // 当前选择的颜色
  const [recentColors, setRecentColors] = useState<string[]>([]); // 历史使用的颜色
  const [colorAdjustments, setColorAdjustments] = useState({
    hue: 0, // 色调 0-360
    saturation: 0, // 饱和度 0-100
    brightness: 0, // 亮度 0-100
    contrast: 0, // 对比度 0-100
    smartColorAdjust: true, // 智能调色开关
  });
  const [isPickingColor, setIsPickingColor] = useState<boolean>(false); // 是否正在拾取颜色

  // 推荐颜色
  const recommendedColors = [
    '#FF6B6B', // 红色
    '#FF4D4F', // 深红
    '#FF7A45', // 橙红
    '#FFA940', // 橙色
    '#FFC53D', // 金色
    '#FFEC3D', // 黄色
    '#A0D911', // 黄绿
    '#52C41A', // 绿色
    '#13C2C2', // 青色
    '#1890FF', // 蓝色
    '#2F54EB', // 深蓝
    '#722ED1', // 紫色
    '#EB2F96', // 品红
    '#FF85C0', // 粉色
    '#D9D9D9', // 浅灰
    '#8C8C8C', // 深灰
  ];

  // 弹窗相关状态
  const [segmentModalVisible, setSegmentModalVisible] =
    useState<boolean>(false);
  const [zoomLevel, setZoomLevel] = useState<number>(100); // 图片缩放级别，百分比

  useEffect(() => {
    const img = localStorage.getItem('imageUrl');
    if (img) {
      setOriginImgUrl(img);
      setImageSource('history');
    }
    updatePoint();
    doPredict();

    return () => {
      localStorage.removeItem('imageUrl');
      localStorage.removeItem('modelId');
    };
  }, []);

  useEffect(() => {
    if (originImgUrl) {
      setCanSave(true);

      // Load the original image from OSS
      loadImageFromOss(originImgUrl)
        .then((img) => {
          setOriginalImage(img);
        })
        .catch((error) => {
          console.error('Error loading original image:', error);
        });
    } else {
      setCanSave(false);
    }
  }, [originImgUrl]);

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null; // 定义定时器变量

    if (creativeVO && creativeVO.id && creativeVO.status !== 'FINISHED') {
      // 只有在 creativeVO 存在时才开始轮询
      interval = setInterval(async () => {
        const result = await queryCreativeAndSync(creativeVO.id);
        if (result) {
          setCreativeVO(result); // 更新状态以触发重新渲染

          // 如果状态为 FINISHED，则停止定时器并更新 loading 状态
          if (result.status === 'FINISHED') {
            clearInterval(interval!); // 清理定时器
            setLoading(false); // 停止加载状态
            if (result.type === 'CLOTH_AUTO_SEGMENT') {
              // 分类 origin 与 mask
              const originImageArr: string[] = [];
              const maskImagesArr: string[] = [];

              const getIdx = (url: string) => {
                const m = url.match(/_(origin|mask)_(\d+)_/i);
                return m ? parseInt(m[2], 10) : 0;
              };

              result.resultImages?.forEach((u: string) => {
                if (/_origin_/i.test(u)) {
                  originImageArr.push(u);
                } else if (/_mask_/i.test(u)) {
                  maskImagesArr.push(u);
                }
              });

              // 按索引排序，保证 origin 与 mask 对应顺序一致
              originImageArr.sort((a, b) => getIdx(a) - getIdx(b));
              maskImagesArr.sort((a, b) => getIdx(a) - getIdx(b));

              setSegmentedImages(originImageArr);
              setMaskImages(maskImagesArr);

              // Load all segmented and mask images from OSS
              const loadSegmentImages = async () => {
                const newSegmentImages = new Map<string, HTMLImageElement>();

                for (const url of originImageArr) {
                  try {
                    const img = await loadImageFromOss(url);
                    newSegmentImages.set(url, img);
                  } catch (error) {
                    console.error('Error loading segment image:', error);
                  }
                }

                setLoadedSegmentImages(newSegmentImages);
              };

              const loadMaskImages = async () => {
                const newMaskImages = new Map<string, HTMLImageElement>();

                for (const url of maskImagesArr) {
                  try {
                    const img = await loadImageFromOss(url);
                    newMaskImages.set(url, img);
                  } catch (error) {
                    console.error('Error loading mask image:', error);
                  }
                }

                setLoadedMaskImages(newMaskImages);
              };

              loadSegmentImages();
              loadMaskImages();
            }
          }
        }
      }, 2000); // 每2秒调用一次
    }

    return () => {
      if (interval) {
        clearInterval(interval); // 清理定时器
      }
    };
  }, [creativeVO]); // 只依赖 creativeVO

  async function doPredict() {
    predict('CLOTH_RECOLOR', 1).then((res) => {
      if (res) {
        setPredictVO(res);
      }
    });
  }

  const handleCommit = () => {
    if (!originImgUrl) {
      message.error('请先上传图片');
      return;
    }
    if (!selectedMaskImage) {
      message.error('请先选择换色区域');
      return;
    }
    if (!selectedColor) {
      message.error('请先选择颜色');
      return;
    }
    if (imageSource === 'upload' && predictVO && predictVO.needTopup) {
      setShowTopupModal(true);
      return;
    }
    if (committing) {
      return;
    }
    setCommitting(true);

    clothRecolor({
      originImgUrl: originImgUrl,
      originMaskImgUrl: segmentedImages[selectedSegmentIndex],
      maskImgUrl: maskImgUrl,
      targetHexColor: selectedColor,
      smartRecolorMode: colorAdjustments.smartColorAdjust,
    })
      .then((res) => {
        if (res) {
          message.success('提交成功');
          setTimeout(() => {
            setLoading(true);
            setCreativeVO(res);
            taskOutputRef.current?.refresh();
          }, 0);
        }
        setCommitting(false);
        updatePoint();
      })
      .catch((error) => {
        message.error(error.message || '图片处理失败');
        setCommitting(false);
      });
  };

  const handleImageChange = (imgUrl) => {
    if (imgUrl) {
      setOriginImgUrl(imgUrl);
      setSegmentModalVisible(true);
      setLoading(true);

      clothImgAutoSeg({
        imgUrl: imgUrl,
      })
        .then((res) => {
          if (res) {
            setCreativeVO(res);
          }
        })
        .catch((error) => {
          message.error(error.message || '图片分割失败');
          setLoading(false);
        });
    } else {
      resetAllStates();
    }
  };

  // 处理选择分割区域的点击事件
  const handleSegmentSelect = (index: number) => {
    setSelectedSegmentIndex(index);
    const maskUrl = maskImages[index];
    setMaskImgUrl(maskUrl);
    const maskImage = loadedMaskImages.get(maskUrl);
    if (maskImage) {
      setSelectedMaskImage(maskImage);
    } else {
      console.warn('Mask image element not found for URL:', maskUrl);
      // Try to load it if not already loaded
      loadImageFromOss(maskUrl)
        .then((img) => {
          const updatedMaskImages = new Map(loadedMaskImages);
          updatedMaskImages.set(maskUrl, img);
          setLoadedMaskImages(updatedMaskImages);
          setSelectedMaskImage(img);
        })
        .catch((error) => {
          console.error('Error loading mask image on demand:', error);
        });
    }
  };

  // 处理继续按钮点击事件
  const handleContinue = () => {
    if (selectedSegmentIndex !== -1) {
      setSegmentModalVisible(false);
    }
  };

  // 处理缩放操作
  const handleZoom = (newZoom: number) => {
    setZoomLevel(newZoom);
  };

  // 处理颜色选择，并触发颜色迁移处理
  const handleColorSelect = (color: string) => {
    setSelectedColor(color);

    // 添加到最近使用的颜色中
    if (!recentColors.includes(color)) {
      const newRecentColors = [color, ...recentColors].slice(0, 5);
      setRecentColors(newRecentColors);
    }
  };

  // 处理颜色选择器变化
  const handleColorChange = (color: AggregationColor) => {
    handleColorSelect(color.toHexString());
  };

  // 处理颜色拾取
  const handleColorPickerClick = async () => {
    try {
      // 检查浏览器是否支持 EyeDropper API
      if (!('EyeDropper' in window)) {
        message.error(
          '您的浏览器不支持屏幕取色功能，请使用 Chrome、Edge 或其他现代浏览器',
        );
        return;
      }

      setIsPickingColor(true);

      // @ts-ignore - EyeDropper API 可能在 TypeScript 类型定义中不存在
      const eyeDropper = new window.EyeDropper();
      const result = await eyeDropper.open();

      // 设置选中的颜色
      handleColorSelect(result.sRGBHex);
      setIsPickingColor(false);
    } catch (error) {
      // 用户取消或发生错误
      setIsPickingColor(false);
    }
  };

  // 获取颜色的十六进制表示
  const getHexColor = (color: string) => {
    return color ? color.toUpperCase() : '# 未选择颜色';
  };

  // 处理颜色参数调整
  const handleColorAdjustment = (
    type: 'hue' | 'saturation' | 'brightness' | 'contrast',
    value: number,
  ) => {
    setColorAdjustments((prev) => ({
      ...prev,
      [type]: value,
    }));
  };

  // 重置所有状态
  const resetAllStates = () => {
    setCreativeVO(null);
    setOriginImgUrl('');
    setMaskImgUrl('');
    setSelectedMaskImage(null);
    setSelectedColor('');
    setSegmentedImages([]);
    setMaskImages([]);
    setSelectedSegmentIndex(-1);
    setImageSource('upload');
    setColorAdjustments({
      hue: 0,
      saturation: 0,
      brightness: 0,
      contrast: 0,
      smartColorAdjust: true,
    });
    // 清除localStorage中的图片缓存
    localStorage.removeItem('imageUrl');
    localStorage.removeItem('modelId');
  };

  return (
    <PageContainer>
      <Flex className={'toolkit-row-container'}>
        {segmentedImages.length > 0 ? (
          <Flex
            gap={8}
            style={{
              padding: '0 8px 0 8px',
              background: '#F5F6F9',
              overflowY: 'auto',
            }}
          >
            <Card
              title="换色区域"
              style={{ maxWidth: '310px', backgroundColor: '#F5F6F9' }}
              styles={{ body: { padding: '16px', marginBottom: 8 } }}
            >
              <Row gutter={[16, 16]}>
                <Col span={24}>
                  <div
                    style={{
                      textAlign: 'center',
                      marginBottom: 16,
                      position: 'relative',
                    }}
                  >
                    {selectedSegmentIndex > -1 && (
                      <div
                        style={{
                          position: 'relative',
                          width: '300px',
                          height: '300px',
                          ...transparentCheckerboardStyle,
                        }}
                      >
                        <div
                          style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            backgroundImage: `url(${segmentedImages[selectedSegmentIndex]})`,
                            backgroundSize: 'contain',
                            backgroundPosition: 'center',
                            backgroundRepeat: 'no-repeat',
                          }}
                        />
                      </div>
                    )}
                    {/* Selection icon in top-right corner */}
                    <EditButton
                      onClick={(e) => {
                        e.stopPropagation();
                        setSegmentModalVisible(true);
                      }}
                    />
                  </div>
                </Col>

                <Col>
                  <div style={{ marginBottom: 16 }}>
                    <div style={{ fontWeight: 500, marginBottom: 12 }}>
                      选择颜色：
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                      }}
                    >
                      <ColorPicker
                        className="color-picker"
                        style={{ flex: 1, justifyContent: 'start' }}
                        value={selectedColor}
                        onChange={handleColorChange}
                        trigger="hover"
                        showText
                        disabledAlpha
                        size="large"
                        format="hex"
                      />
                      <Button
                        icon={<EyeOutlined />}
                        onClick={handleColorPickerClick}
                        type={isPickingColor ? 'primary' : 'default'}
                      >
                        拾取颜色
                      </Button>
                    </div>
                  </div>
                </Col>
                {/* <Col span={24}>
                  <div style={{ marginBottom: 16 }}>
                    <div style={{ fontWeight: 500, marginBottom: 12 }}>
                      推荐颜色：
                    </div>
                    <Row gutter={[12, 12]}>
                      {recommendedColors.map((color, index) => (
                        <Col key={index}>
                          <Tooltip title={color.toUpperCase()}>
                            <div
                              style={{
                                width: 32,
                                height: 32,
                                borderRadius: '50%',
                                backgroundColor: color,
                                cursor: 'pointer',
                                border: `2px solid ${selectedColor === color ? '#1890ff' : 'transparent'}`,
                                transform:
                                  selectedColor === color
                                    ? 'scale(1.1)'
                                    : 'scale(1)',
                                transition: 'all 0.2s',
                              }}
                              onClick={() => handleColorSelect(color)}
                            />
                          </Tooltip>
                        </Col>
                      ))}
                    </Row>
                  </div>
                </Col> */}
                {recentColors.length > 0 && (
                  <Col span={24}>
                    <div>
                      <div style={{ fontWeight: 500, marginBottom: 12 }}>
                        最近使用：
                      </div>
                      <Row gutter={[12, 12]}>
                        {recentColors.map((color, index) => (
                          <Col key={`recent-${index}`}>
                            <Tooltip title={color.toUpperCase()}>
                              <div
                                style={{
                                  width: 32,
                                  height: 32,
                                  borderRadius: '50%',
                                  backgroundColor: color,
                                  cursor: 'pointer',
                                  border: `2px solid ${selectedColor === color ? '#1890ff' : 'transparent'}`,
                                  transform:
                                    selectedColor === color
                                      ? 'scale(1.1)'
                                      : 'scale(1)',
                                  transition: 'all 0.2s',
                                }}
                                onClick={() => handleColorSelect(color)}
                              />
                            </Tooltip>
                          </Col>
                        ))}
                      </Row>
                    </div>
                  </Col>
                )}
                <Col span={24} style={{ marginTop: 16 }}>
                  <Flex justify="space-between" align="center">
                    <Flex align="center">
                      <span>根据换色区域智能调色</span>
                      <Tooltip title="自适应调整换色后颜色的饱和度和亮度">
                        <QuestionCircleOutlined
                          style={{ marginLeft: 8, color: '#999' }}
                        />
                      </Tooltip>
                    </Flex>
                    <Switch
                      checked={colorAdjustments.smartColorAdjust}
                      onChange={(checked) =>
                        setColorAdjustments((prev) => ({
                          ...prev,
                          smartColorAdjust: checked,
                        }))
                      }
                    />
                  </Flex>
                </Col>
              </Row>
            </Card>
            <Card
              title="预览"
              style={{ maxWidth: '310px', backgroundColor: '#F5F6F9' }}
              extra={
                <Button
                  type="text"
                  icon={<DeleteOutlined />}
                  onClick={() => {
                    resetAllStates();
                  }}
                />
              }
            >
              <MaskCanvas
                imageElement={originalImage}
                maskImageElement={selectedMaskImage}
                width={310}
                mode="recolor"
                targetColor={selectedColor}
                smartColorAdjust={colorAdjustments.smartColorAdjust}
              />
            </Card>
          </Flex>
        ) : (
          <div className={'toolkit-work-block'}>
            <ImageUploader
              className={'redraw-image-upload'}
              title={'上传图片'}
              image={originImgUrl}
              setImageSource={setImageSource}
              onImageChange={handleImageChange}
              historyFree
              uploadCharge
            />
          </div>
        )}
        <div
          ref={outputBlockRef}
          className={'toolkit-output-block'}
          style={{
            width: `calc((100% - 644px) * ${sliderValue / 12})`,
            maxWidth: '100%',
          }}
        >
          <TaskOutputBlock
            sliderValue={sliderValue}
            changeSliderValue={setSliderValue}
            types={['CLOTH_RECOLOR']}
            ref={taskOutputRef}
            pollingTimeout={3000}
          />
        </div>
      </Flex>
      <footer className={'toolkit-footer'}>
        <div className={'toolkit-footer-content'}>
          {canSave && imageSource == 'upload' && (
            <PredictBlock
              creativeType={'CLOTH_RECOLOR'}
              predictVO={predictVO}
            />
          )}
          <Button
            type="primary"
            className={'create-btn'}
            disabled={!canSave || committing || predictVO?.needTopup}
            icon={
              committing ? (
                <LoadingOutlined style={{ fontSize: 16, color: 'fff' }} />
              ) : (
                ''
              )
            }
            onClick={handleCommit}
          >
            {' '}
            {predictVO?.needTopup
              ? '余额不足，去充值'
              : committing
                ? '生成中'
                : '生成图片'}
          </Button>
        </div>
      </footer>
      {/* 分割区域选择弹窗 */}
      <Modal
        title="选择换色区域"
        open={segmentModalVisible}
        footer={
          <Space>
            <Button onClick={() => setSegmentModalVisible(false)}>取消</Button>
            <Button
              type="primary"
              disabled={selectedSegmentIndex === -1 || loading}
              onClick={handleContinue}
            >
              确认选择
            </Button>
          </Space>
        }
        onCancel={() => setSegmentModalVisible(false)}
        width={1200}
        maskClosable={false}
        centered
      >
        <Row wrap={false} style={{ minHeight: 600 }}>
          {/* Left side - MaskHighlightCanvas */}
          <Col span={12} style={{ backgroundColor: '#fafafa' }}>
            <div
              style={{
                height: '100%',
                borderRadius: 8,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative',
                padding: 16,
              }}
            >
              {originalImage ? (
                <MaskCanvas
                  imageElement={originalImage}
                  maskImageElement={selectedMaskImage}
                  width={600}
                  height={550}
                  highlightColor="rgba(24, 144, 255, 0.5)"
                  mode="highlight"
                />
              ) : (
                originImgUrl && (
                  <img
                    src={originImgUrl}
                    alt="原始图片"
                    style={{
                      maxWidth: '100%',
                      maxHeight: 550,
                      objectFit: 'contain',
                      borderRadius: 4,
                    }}
                  />
                )
              )}
              {loading && (
                <Flex
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'rgba(255, 255, 255, 0.8)',
                  }}
                  align="center"
                  justify="center"
                  vertical
                >
                  <Spin
                    size="large"
                    indicator={
                      <LoadingOutlined style={{ fontSize: 24 }} spin />
                    }
                  />
                  <div style={{ marginTop: 16 }}>智能抠图中...</div>
                </Flex>
              )}
            </div>
          </Col>

          <div style={{ minWidth: 8, backgroundColor: '#fff' }}></div>

          {/* Right side - Mask thumbnails */}
          <Col span={12} style={{ backgroundColor: '#fafafa' }}>
            {loading ? (
              <Flex
                align="center"
                justify="center"
                style={{ height: '100%' }}
                vertical
              >
                <Spin />
              </Flex>
            ) : segmentedImages.length > 0 ? (
              <Row gutter={[16, 16]}>
                <Col span={5}>
                  <Flex
                    vertical
                    style={{
                      margin: '8px',
                      padding: '8px',
                      backgroundColor: '#fff',
                    }}
                  >
                    <Flex
                      align="center"
                      justify="center"
                      style={{ marginBottom: '8px' }}
                    >
                      <span>抠图结果</span>
                    </Flex>
                    {segmentedImages.map((img, index) => (
                      <div key={index}>
                        <div>
                          <div
                            style={{
                              width: '100%',
                              marginBottom: '8px',
                              border: `2px solid ${selectedSegmentIndex === index ? '#1890ff' : 'transparent'}`,
                              borderRadius: 8,
                              overflow: 'hidden',
                              cursor: 'pointer',
                              transition: 'all 0.3s',
                            }}
                            onClick={() => handleSegmentSelect(index)}
                          >
                            <div
                              style={{
                                width: '100%',
                                height: 120,
                                position: 'relative',
                                ...transparentCheckerboardStyle,
                              }}
                            >
                              <div
                                style={{
                                  position: 'absolute',
                                  top: 0,
                                  left: 0,
                                  width: '100%',
                                  height: '100%',
                                  backgroundImage: `url(${img})`,
                                  backgroundSize: 'contain',
                                  backgroundPosition: 'center',
                                  backgroundRepeat: 'no-repeat',
                                }}
                                aria-label={`区域 ${index + 1}`}
                              />
                            </div>
                          </div>
                        </div>
                        <div
                          style={{
                            textAlign: 'center',
                            fontSize: 12,
                            backgroundColor: '#fff',
                            fontWeight: 'normal',
                          }}
                        >
                          区域 {index + 1}
                        </div>
                      </div>
                    ))}
                  </Flex>
                </Col>
                <Col span={19}>
                  <div
                    style={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                    }}
                  >
                    {selectedSegmentIndex !== null && (
                      <div
                        style={{
                          flex: 1,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <div
                          style={{
                            width: '100%',
                            height: '400px',
                            position: 'relative',
                            ...transparentCheckerboardStyle,
                          }}
                        >
                          <div
                            style={{
                              position: 'absolute',
                              top: 0,
                              left: 0,
                              width: '100%',
                              height: '100%',
                              backgroundImage: `url(${segmentedImages[selectedSegmentIndex]})`,
                              backgroundSize: 'contain',
                              backgroundPosition: 'center',
                              backgroundRepeat: 'no-repeat',
                            }}
                            aria-label={`区域 ${selectedSegmentIndex + 1} 预览`}
                          />
                        </div>
                      </div>
                    )}

                    {selectedSegmentIndex === null && (
                      <Flex align="center" justify="center" style={{ flex: 1 }}>
                        <Empty
                          description="请从左侧选择一个区域"
                          image={Empty.PRESENTED_IMAGE_SIMPLE}
                        />
                      </Flex>
                    )}
                  </div>
                </Col>
              </Row>
            ) : (
              <Empty
                description="暂无可选区域，请等待分析完成"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )}
          </Col>
        </Row>
      </Modal>

      {showTopupModal && (
        <TopupModal
          visible={showTopupModal}
          onClose={() => setShowTopupModal(false)}
          onPaySuccess={() => {
            setShowTopupModal(false);
            doPredict();
            updatePoint();
          }}
        />
      )}
    </PageContainer>
  );
};

export default RecolorPage;
