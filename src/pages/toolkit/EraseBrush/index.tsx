import { PageContainer } from '@ant-design/pro-components';
import { Button, Flex, InputNumber, message, } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import "./index.less"
import ImageUploader, { ImageUploaderButton } from '@/components/Common/ImageUploader';
import SmudgeBrushTool from '@/components/imageOperate/smudgeCanvas/SmudgeBrushTool';
import SmudgeCanvas from '@/components/imageOperate/smudgeCanvas/SmudgeCanvas'
import PredictBlock from '@/components/Creative/PredictBlock';
import { MINI_LOADING_ICON } from '@/constants';
import { predict, PredictVO, updatePoint } from '@/services/PointController';
import {  getUserInfo } from '@/utils/utils';
import { getMaskUrl } from '@/utils/imageUtils';
import TaskOutputBlock, { TaskOutputBlockRef } from '@/components/Creative/TaskOutputBlock';
import { eraseBrush } from '@/services/CreativeController';
import TopupModal from '@/pages/Topup/Topup';
import { useLocation, useNavigate } from 'react-router-dom';
import { LoadingOutlined } from '@ant-design/icons';

const userInfo = getUserInfo();
const EraseBrush = () => {
  const [canSave, setCanSave] = useState(false);
  const [imageSource, setImageSource] = useState<'upload' | 'history'>('upload');
  const [image, setImage] = useState<string>('');
  const [imageNum, setImageNum] = useState(1);
  const [strokeWidth, setStrokeWidth] = useState<number>(32);
  const [lines, setLines] = useState<Array<{x: number, y: number}>>([]);
  const [predictVO, setPredictVO] = useState<PredictVO | null>(null);
  const [commiting, setCommiting] = useState<boolean>(false);
  const [sliderValue, setSliderValue] = useState<number>(12);
  const stageRef = useRef(null);
  const taskOutputRef = useRef<TaskOutputBlockRef>(null);
  const [showTopupModal, setShowTopupModal] = useState(false)
  const location = useLocation();
  const navigate = useNavigate();

  // 检查taskId是否存在
  useEffect(() => {
    const img = localStorage.getItem('imageUrl');
    if (img) {
      setImage(img);
      setImageSource('history');
    }
    updatePoint();
    predict('REPAIR_DETAIL', imageNum, null)
      .then(result => {
        if (result) {
          setPredictVO(result);
        }
      });
    return () => {
      localStorage.removeItem('imageUrl');
      localStorage.removeItem('modelId');
    }
  }, []);

  useEffect(() => {
    setCanSave(!!(image && lines.length > 0));
  }, [image, lines]);


  const handleCreate = async () => {
    if ( imageSource === 'upload' && predictVO && predictVO.needTopup) {
      setShowTopupModal(true);
      return;
    }
    if (lines.length <= 0) {
      message.warning('请长按鼠标涂抹需要消除的区域');
      return;
    }
    if (commiting) {
      return;
    }
    setCommiting(true);
    async function start() {
      const mask = await getMaskUrl(stageRef);
      if (!mask) {
        message.error("涂抹区域处理异常, 请清空重试")
        return;
      }

      const result = await eraseBrush(
        {
          originImage: image,
          maskImage: mask,
          imageNum: imageNum,
        }
      );
      setCommiting(false);
      updatePoint();
      if (!result) {return;}
      message.success('开始生成图片');
      taskOutputRef.current?.refresh();
      navigate(location.pathname, {replace: true});
    }
    start();
  };

  const handleImageChange = (value) => {
    setImage(value);
  }

  const handleLinesChange = (value)=> {
    setLines(value);
  }

  const switchSeg = (value) => {
    if (value) {
      setLines([]);
    }
  };

  return (
    <PageContainer>
      <Flex className={"toolkit-row-container"}>
        <div className={'toolkit-work-block'}>
          {/* 上传图片并涂抹 */}
          {image ?
            <div className={"redraw-smudge-container"}>
              <span className={"font-pf text16 weight"}>上传图片</span>
              <span className={"font-pf text12 normal"}>长按鼠标涂抹需要消除的区域后，点击开始按钮</span>
              <Flex className={'redraw-smudge'}>
                <SmudgeCanvas imageUrl={image} strokeWidth={strokeWidth} singleSmudge={false}
                              stageCmtRef={stageRef} lines={lines} onLinesChange={handleLinesChange} />
                <ImageUploaderButton onImageChange={handleImageChange}  />
                {commiting && <div style={{ height: '100%', width: '100%', position: 'absolute', backgroundColor: '#fff', opacity: 0.5, cursor: 'not-allowed'}} /> }
              </Flex>
              <SmudgeBrushTool strokeWidth={strokeWidth} setStrokeWidth={setStrokeWidth} switchSeg={switchSeg} />
            </div>
            :
            <ImageUploader className={'redraw-image-upload'} title={'上传图片'} image={image} resize={2100}
                           onImageChange={handleImageChange} historyFree uploadCharge setImageSource={setImageSource} />
          }
          <div className={'toolkit-number-input-container'}>
            <div className={'text16 font-pf color-n weight'}>生成数量</div>
            <InputNumber value={1} disabled={true}/>
          </div>
        </div>
        <div className={'toolkit-output-block'}
             style={{ width: `calc((100% - 644px) * ${sliderValue / 12})`, maxWidth: '100%' }}
        >
          <TaskOutputBlock
            sliderValue={sliderValue}
            changeSliderValue={setSliderValue}
            types={['ERASE_BRUSH']}
            ref={taskOutputRef}
            pollingTimeout={3000}
          />
        </div>
      </Flex>
      <footer className={'toolkit-footer'}>
        <div className={'toolkit-footer-content'}>
          {canSave && imageSource == 'upload' && <PredictBlock creativeType={'ERASE_BRUSH'} predictVO={predictVO}/>}
          <Button type="primary" className={'toolkit-submit-btn'}
                  disabled={!( (predictVO?.needTopup && imageSource === 'upload') || (canSave && !commiting))}
                  icon={(commiting) ? <LoadingOutlined style={{fontSize: 16, color: 'fff'}} /> : ''}
                  onClick={handleCreate}> {(predictVO?.needTopup && imageSource === 'upload') ? '余额不足，去充值' : commiting ? '修复中' : '开始修复'}</Button>
        </div>
      </footer>
      {showTopupModal &&
        <TopupModal visible={showTopupModal} onClose={() => setShowTopupModal(false)}
                    onPaySuccess={() => {
                      setShowTopupModal(false);
                      updatePoint();
                    }} />
      }
    </PageContainer>
  );
};

export default EraseBrush;