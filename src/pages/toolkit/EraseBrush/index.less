.repair-work-item-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: auto;
  border-radius: 8px;
  background: #F5F6F9;
  padding: 4px;
  gap: 8px;
}

.repair-create-btn {
  width: 104px;
  height: 38px;
  border-radius: 8px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px 32px;
  border: 0;
  color: #FFFFFF;
  border-color: transparent !important;
  z-index: 1;
}
.repair-create-btn:disabled {
  color: #FFFFFF;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.6)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%);
}

.repair-create-btn:enabled {
  background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.repair-create-btn:not(:disabled):hover, .create-btn:not(:disabled):focus {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.repair-create-btn:not(:disabled):active {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}
.erase-brush-show-image {
  display: flex;
  height: 100%;
  width: 100%;
  justify-content: center;
  align-items: center;
  .show-image {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    padding: 12px;
    border-radius: 8px;
    background: #FFFFFF;
    object-fit: contain;
    object-position: center; /* 控制图片居中显示 */
  }
}