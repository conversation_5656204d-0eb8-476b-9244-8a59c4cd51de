import React, { useEffect, useRef, useState } from 'react';
import { Image as KonvaImage, Layer, Line, Stage } from 'react-konva';
import useImage from 'use-image';
import { PageContainer } from '@ant-design/pro-components';
import { Alert, Button, Flex, InputNumber, message, Modal, notification, Segmented, Slider } from 'antd';
import { useLocation } from 'react-router-dom';
import {
  ALLCreativeType, CreativeType,
  fetchTaskImage,
  fetchTaskInfo, partialRedraw,
  repairHands,
} from '@/services/CreativeController';
import IconFont from '@/components/IconFont';
import { useNavigate } from '@umijs/max';
import './index.less';
import { dataURLToBlob } from '@/utils/imageUtils';
import { calcMaskRegions, calcOverlap, createBlankCanvas } from '@/utils/canvasUtils';
import { EXPERIENCE_POINT, IMAGE_POINT, IMAGE_POINT_CHANGE_EVENT, MINI_LOADING_ICON } from '@/constants';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import FREE_BG from '@/assets/images/free-bg.png';
import { downloadOss } from '@/services/SystemController';
import TextArea from 'antd/lib/input/TextArea';
import PredictBlock from '@/components/Creative/PredictBlock';
import { predict, PredictVO, queryImagePoint } from '@/services/PointController';

interface ImagePoint {
  x: number;
  y: number;
}

interface CustomLine {
  points: Array<ImagePoint>;
  tool: string;
  strokeWidth: number;
  color: string;
}

const shotLabels = new Map<CreativeType, string> ([
  ['REPAIR_HANDS', '修复'],
  ['PARTIAL_REDRAW', '重绘']
]);


const SmudgeTool: React.FC<{ createType: CreativeType }> = ({createType}) => {
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const type: string = 'CUSTOM';
    const predictImageNum = (userInfo.roleType === 'OPERATOR' || type === 'SYSTEM') ? 4 : 4;
    const [imagePoint, setImagePoint] = useState(0);
    const [imageUrl, setImageUrl] = useState<string>('');
    const [imageNum, setImageNum] = useState<number>(4);
    const [blankImageUrl, setBlankImageUrl] = useState<string>('');
    const [taskId, setTaskId] = useState<string>('');
    const [modelId, setModelId] = useState<string | null>(null);
    const [originImageUrl, setOriginImageUrl] = useState<string | null>(null);
    const [proportion, setProportion] = useState<string>('');
    const [fromPath, setFromPath] = useState<string>('');
    // @ts-ignore
    const [blankImage] = useImage(blankImageUrl, 'Anonymous');
    // @ts-ignore
    const [lines, setLines] = useState<Array<CustomLine>>([]);
    const [currentLine, setCurrentLine] = useState<Array<ImagePoint>>([]);
    const [lineColor, setLineColor] = useState('rgba(97, 179, 255, 1)');
    const [commiting, setCommiting] = useState(false);
    const [goBackConfirm, setGoBackConfirm] = useState(false);
    const [preCheckErr, setPreCheckErr] = useState(false);
    const [predictVO, setPredictVO] = useState<PredictVO | null>(null);
    const isDrawing = useRef(false);
    const stageRef = useRef(null);
    const stageCmtRef = useRef(null);
    const imageRef = useRef(null);
    const createRef = useRef(null);

    const [startPos, setStartPos] = useState({ x: 0, y: 0 });

    const [pointerPos, setPointerPos] = useState({ x: 0, y: 0 });
    const [isErasing, setIsErasing] = useState(false); // 是否处于擦除模式
    const [strokeWidth, setStrokeWidth] = useState(32);  // 设置画笔的宽度
    const [imageWidth, setImageWidth] = useState(570);
    const [imageHeight, setImageHeight] = useState(760);

    const [handTrackModel, setHandTrackModel] = useState();
    const [handRegions, setHandRegions] = useState([]);
    const [redrawDesc, setRedrawDesc] = useState('');
    const [textError, setTextError] = useState('');
    const [canCommit, setCanCommit] = useState(false);

    const createLabel = ALLCreativeType.find(item => item.value === createType)?.label;
    const shotLabel = shotLabels.get(createType);
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {

      //跳转时页面参数指定了模型id
      const queryParams = new URLSearchParams(location.search);
      setFromPath(location.state?.from);

      const idStr = queryParams.get('id');
      const originImage = queryParams.get('imageUrl');
      const modelId = queryParams.get('modelId');
      if (!idStr && !originImage) {
        return;
      }

      if (modelId) {
        setModelId(modelId);
      }

      setTaskId(idStr ? idStr : '');
      setOriginImageUrl(originImage);
      const id = idStr ? Number(idStr) : null;

      const init = async () => {
        if (id) {
          // @ts-ignore
          const res = await fetchTaskImage(id);

          // @ts-ignore
          const blob = await res.blob();
          const imageUrl = URL.createObjectURL(blob);
          setImageUrl(imageUrl);

          const task = await fetchTaskInfo(id);
          if (task) {
            setProportion(task.imageProportion);
            // @ts-ignore
            resizeWithProportion(task.imageProportion);
          }
        } else if (originImage) {
          const res = await downloadOss(originImage);
          const blob = await res.blob();
          const imageUrl = URL.createObjectURL(blob);

          setImageUrl(imageUrl);

          const imageElement = new Image();
          imageElement.src = imageUrl;

          imageElement.onload = () => {
            const imageProportion = imageElement.width != imageElement.height ? 'THREE_FOUR' : 'ONE_ONE';

            setProportion(imageProportion);
            // @ts-ignore
            resizeWithProportion(imageProportion);
          };
        }
        updatePoint(type);
      };

      init();

      window.addEventListener('resize', resize);
      return () => {
        window.removeEventListener('resize', resize);
      };
    }, []);

    useEffect(() => {
      resetPos();
    }, [imageWidth, imageHeight]);

    useEffect(() => {
      setCanCommit(isCanCommit(redrawDesc, lines));
    }, [lines, redrawDesc]);

    useEffect(() => {
      if (createType === 'PARTIAL_REDRAW') {
        predict(createType, imageNum, null)
          .then(result => {
            if (result) {
              setPredictVO(result);
            }
          });
      }
    }, [imageNum]);

    const isCanCommit = (redrawDesc: string, lines: Array<CustomLine>) => {
      if (createType === 'REPAIR_HANDS') {
        return !!(imageUrl && lines.length > 0);
      } else if (createType === 'PARTIAL_REDRAW') {
        return !!(imageUrl && redrawDesc.length > 0 && lines.length > 0);
      }
      return false;
    };

    const resize = () => {
      resizeWithProportion(null);
    };

    const resizeWithProportion = (predProportion = null) => {
      //计算图片宽高
      const height = window.innerHeight - 140;//calc(100vh - @repair-header-height - 16px - 38px - 8px);
      const width = (predProportion ? predProportion : proportion) !== 'THREE_FOUR' ? height : height / 4 * 3;
      setImageHeight(height);
      setImageWidth(width);
    };

    const resetPos = () => {
      if (imageRef.current) {
        //生成一张空白的图片
        const tmpCanvas = createBlankCanvas(imageWidth, imageHeight, '#FFFFFF');
        setBlankImageUrl(tmpCanvas.toDataURL('image/png'));

        // @ts-ignore
        const rect = imageRef.current.getBoundingClientRect();
        setStartPos({ x: rect.left + window.scrollX, y: rect.top + window.scrollY });
        //解决加载页面时，直接展示画笔的问题
        if (window.scrollX == 0 && window.scrollY === 0) {
          setPointerPos({ x: 0, y: 0 });
          return;
        }
        setPointerPos({ x: rect.left + window.scrollX, y: rect.top + window.scrollY });
      }
    };

    const handleMouseEnter = () => {
      resetPos();
    };

    const handleMouseLeave = () => {
      setPointerPos({ x: 0, y: 0 });
    };

    const handleMouseDown = () => {
      isDrawing.current = true;
      // @ts-ignore
      const pos = stageRef.current.getPointerPosition();
      // @ts-ignore
      setCurrentLine([{ x: pos.x, y: pos.y }]);
    };

    const handleMouseMove = () => {
      // @ts-ignore
      const pos = stageRef.current.getPointerPosition();
      setPointerPos({ x: pos.x + startPos.x, y: pos.y + startPos.y });

      if (!isDrawing.current) return;

      setCurrentLine((prevLine) => [...prevLine, { x: pos.x, y: pos.y }]);
    };

    const handleMouseUp = () => {
      isDrawing.current = false;
      // @ts-ignore
      setLines((prevLines) => [...prevLines, {
        points: currentLine,
        tool: isErasing ? 'erase' : 'draw',
        strokeWidth,
        color: lineColor,
      }]);
      setCurrentLine([]);
    };

    const handleSave = (forceCommit = true) => {
      if (createType === 'REPAIR_HANDS') {
        handleRepairSave(forceCommit);
      }
      if (createType === 'PARTIAL_REDRAW') {
        handleRedrawSave();
      }
      updatePoint(type);
    };

    const handleRepairSave = async (forceCommit = true) => {
      if (lines.length <= 0) {
        message.warning(`请长按鼠标涂抹需要${createType}的区域`);
        return;
      }

      // if (commiting || !handTrackModel || (!forceCommit && preCheckErr)) {
      if (commiting) {
        console.log('commiting');
        return;
      }

      setCommiting(true);
      const scale = 1024 / imageHeight; // Pixel ratio for higher quality
      const stage = stageCmtRef.current;

      //强制提交时，不进行检测(目前整体不走前置检测逻辑)
      if (!forceCommit) {
        // @ts-ignore
        const canvas = stage.toCanvas();
        const context = canvas.getContext('2d');

        const maskImageData = context.getImageData(0, 0, canvas.width, canvas.height);
        const maskData = maskImageData.data;

        const maskRegions = calcMaskRegions(maskData, canvas.width, canvas.height);
        const overlapping = handRegions.some(handRegion =>
          maskRegions.some(maskRegion => calcOverlap(maskRegion, handRegion) > 0.5),
        );

        if (!overlapping) {
          setCommiting(false);
          setPreCheckErr(true);
          return;
        }
      }

      // @ts-ignore
      const dataURL = stage.toDataURL({ pixelRatio: scale, quality: 1 });

      // 通过手部检测后将mask提交服务端，由服务端再次进行合图
      const maskBlob = dataURLToBlob(dataURL);
      const formData = new FormData();
      formData.append('mask', maskBlob);
      if (taskId) {
        formData.append('taskId', taskId);
      }
      if (originImageUrl) {
        formData.append('originImage', originImageUrl);
      }
      if (modelId) {
        formData.append('modelId', modelId);
      }

      repairHands(formData).then(res => {
        setCommiting(false);
        if (res) {
          notification.success({ message: `开始进行${createLabel}` });
          navigate('/creation');
        }
      });
    };

    const handleRedrawSave = async () => {
      if (lines.length <= 0) {
        message.warning(`请长按鼠标涂抹需要${createType}的区域`);
        return;
      }
      if (commiting) {
        console.log('commiting');
        return;
      }
      setCommiting(true);
      const scale = 1024 / imageHeight; // Pixel ratio for higher quality
      const stage = stageCmtRef.current;

      // @ts-ignore
      const dataURL = stage.toDataURL({ pixelRatio: scale, quality: 1 });

      // 通过手部检测后将mask提交服务端，由服务端再次进行合图
      const maskBlob = dataURLToBlob(dataURL);
      const formData = new FormData();
      formData.append('mask', maskBlob);
      if (taskId) {
        formData.append('taskId', taskId);
      }
      if (originImageUrl) {
        formData.append('originImage', originImageUrl);
      }
      if (modelId) {
        formData.append('modelId', modelId);
      }
      if (redrawDesc) {
        formData.append('redrawDesc', redrawDesc);
      }
      if (imageNum) {
        formData.append('imageNum', imageNum.toString());
      }

      partialRedraw(formData).then(res => {
        setCommiting(false);
        if (res) {
          notification.success({ message: `开始进行${createLabel}` });
          navigate('/creation');
        }
      });
    };

    const switchSeg = (value) => {
      if (value) {
        setLines([]);
        setPreCheckErr(false);
      }
    };

    const goBack = () => {
      if (lines.length > 0) {
        setGoBackConfirm(true);
        return;
      }
      confirmGoBack();
    };

    const confirmGoBack = () => {
      if (fromPath) {
        navigate(fromPath);
      } else {
        window.close();
      }
    };

    const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const value = e.target.value;
      setRedrawDesc(value);
      setTextError('');
    };

    const handleNumberChange = (value) => {
      value = value < predictImageNum? predictImageNum : value;
      setImageNum(value);
    };

    const handleTextBlur = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const value = e.target.value;
      setRedrawDesc(value);
      if (value === '') {
        setTextError('输入内容不得为空');
      } else {
        setTextError('');
      }
      setRedrawDesc(value);
    };

    const updatePoint = (type) => {
      queryImagePoint().then(
        (result) => {
          if (result) {
            const imagePoint = type === 'CUSTOM' ? result.imagePoint : result.experiencePoint;
            setImagePoint(imagePoint);
            localStorage.setItem(IMAGE_POINT, result.imagePoint.toString());
            localStorage.setItem(EXPERIENCE_POINT, result.experiencePoint.toString());
            window.dispatchEvent(new Event(IMAGE_POINT_CHANGE_EVENT));
          }
        },
      );
    };

    const SegLable = ({ iconurl, text }) => {
      return <Flex style={{ width: 62, height: 28, gap: 8 }} align={'center'} justify={'center'}>
        <IconFont type={iconurl} width={16} height={16} />
        <div className={'text14 font-pf color-1a'}>{text}</div>
      </Flex>;
    };

    return (
      <PageContainer>
        <Flex vertical align={'flex-start'} justify={'flex-start'} className={'repair-container'}>
          <Flex align={'center'} justify={'flex-start'} gap={16} className={'repair-header'}>
            <Button className={'repair-back-btn'} onClick={goBack}
                    icon={<IconFont type={'icon-youjiantou_16px'} height={16}
                                    style={{ transform: 'rotate(180deg)' }} />}>
              {fromPath ? '返回' : '关闭窗口'}
            </Button>

            <div className={'text16 font-pf weight color-1a'}>
              {createLabel}
            </div>
          </Flex>

          <Flex className={'repair-body'}>
            <Flex className={createType === 'REPAIR_HANDS' ? 'repair-body-left' : 'redraw-body-left'}  vertical>
              <Flex vertical className={'repair-body-left-top'} gap={16} align={'flex-start'} justify={'flex-start'}>
                <div className={'text14 font-pf weight color-1a'}>请选择需要{shotLabel}的区域</div>

                <Segmented
                  value={isErasing}
                  style={{ backgroundColor: '#E1E3EB' }}
                  onChange={(value) => switchSeg(value as boolean)}
                  options={[{
                    label: <SegLable iconurl={'icon-tumo'} text={'涂抹'} />,
                    value: false,
                  }, {
                    label: <SegLable iconurl={'icon-cachu'} text={'清空'} />,
                    value: true,
                  }]}
                />

                <div className={'text14 font-pf color-1a'}>笔刷大小</div>

                <Slider value={strokeWidth} min={8} max={100} tooltip={{ open: false }}
                        onChange={e => setStrokeWidth(e)}
                        style={{ width: '95%' }} />

              </Flex>

              {createType === 'PARTIAL_REDRAW' &&
                <Flex vertical className={'repair-body-left-top'} gap={8} align={'flex-start'} justify={'flex-start'}>
                  <div className={'text14 font-pf weight color-1a'} style={{marginBottom: 8}}>请输入需要{shotLabel}的内容</div>
                  {textError && <div style={{ color: 'red', fontSize: 14 }}>* {textError}</div>}
                  <TextArea className={'repair-text-area'} showCount={true}
                            maxLength={300} style={{ height: 176, resize: 'none', fontSize: 14 }} rows={4}
                            allowClear={true} onChange={handleTextChange} onBlur={handleTextBlur}
                  />
                </Flex>
              }

              {createType === 'REPAIR_HANDS' &&
                <Flex className={'repair-body-left-bottom'} align={'center'} justify={'center'}>
                  <div style={{ position: 'absolute' }}>
                    <Button className={'create-btn'} onClick={() => handleSave()}
                            disabled={!canCommit}
                            icon={commiting ? <img src={MINI_LOADING_ICON} width={16} height={16} alt={'logo'}
                                                   className={'loading-img'} /> : ''}>
                      {commiting ? ' ' : `开始${shotLabel}`}
                    </Button>
                    <div className={'text12 font-pf repair-free-icon-desc'}>限时免费</div>
                    <img src={FREE_BG} alt={'logo'} width={64} height={20}
                         style={{ position: 'absolute', top: 0, right: 0, zIndex: 1 }} />
                  </div>
                </Flex>
              }
              {createType === 'PARTIAL_REDRAW' &&
                <Flex>
                  <div className={'repair-work-item-container'} style={{width: '100%', margin: '8px 8px'}}>
                    <div className={'text14 font-pf color-n weight'}>生成数量</div>
                    <div className={'work-item-row margin-bottom-8'} style={{ gap: 8 }}>
                      <InputNumber min={4} max={userInfo.roleType === 'OPERATOR' ? 30 : 20}
                                   defaultValue={predictImageNum} value={imageNum}
                                   status={((type === 'SYSTEM' && imageNum > imagePoint) ? 'error' : '')}
                                   onChange={handleNumberChange} className={'work-item-input'} />
                      <div className={'margin-left-8'}>最大生成数量{userInfo.roleType === 'OPERATOR' ? 30 : 20}</div>
                    </div>
                  </div>
                  <Flex className={'repair-body-left-bottom'} align={'center'} justify={'center'}>
                    <div className={'fixed-tab-bar fixed-tab-bar-fixed'}>
                      <div className={'fixed-tab-content'} ref={createRef}>
                        <PredictBlock creativeType={'CREATE_IMAGE'} predictVO={predictVO} data={{ imageNum: imageNum }}
                                      type={type} font={14}
                                      imagePoint={imagePoint} />
                        <Button type="primary" className={'create-btn'}
                                disabled={!canCommit && (predictVO === null || !predictVO.needTopup)}
                                icon={commiting ? <img src={MINI_LOADING_ICON} width={16} height={16} alt={'logo'} className={'loading-img'} /> : ''}
                                onClick={() => handleSave(true)}>
                          {predictVO && predictVO.needTopup ? '余额不足，去充值' : '生成图片'}
                        </Button>
                      </div>
                    </div>
                  </Flex>
                </Flex>
              }
            </Flex>


            <Flex vertical className={'repair-body-right'} gap={8} justify={'flex-start'} align={'center'}>
              <Alert type={'info'} showIcon className={'color-brand text14 font-pf width-100'}
                     message={`长按鼠标涂抹需要${createLabel}的区域后，点击开始${shotLabel}按钮`} />

              <Flex ref={imageRef} style={{
                backgroundImage: `url(${imageUrl})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
              }}>
                <div style={{ opacity: 0.5 }}>
                  <Stage className={'repair-stage'}
                         width={imageWidth}
                         height={imageHeight}
                         onMouseDown={handleMouseDown}
                         onMousemove={handleMouseMove}
                         onMouseup={handleMouseUp}
                         onMouseEnter={handleMouseEnter}
                         onMouseLeave={handleMouseLeave}
                         ref={stageRef}
                         style={{ cursor: 'none' }}  // 隐藏默认鼠标指针
                  >
                    <Layer>
                      <KonvaImage image={blankImage} width={imageWidth} height={imageHeight} opacity={0} />
                      {lines.map((line, i) => (
                        <Line
                          key={i}
                          points={line.points.flatMap(p => [p.x, p.y])}
                          stroke={'rgba(97, 179, 255, 1)'}
                          strokeWidth={line.strokeWidth}
                          tension={0.5}
                          lineCap="round"
                          lineJoin="round"
                          globalCompositeOperation={line.tool === 'erase' ? 'destination-out' : 'source-over'}
                        />
                      ))}
                      {currentLine.length > 0 && (
                        <Line
                          points={currentLine.flatMap(p => [p.x, p.y])}
                          stroke={isErasing ? 'rgba(255, 255, 255, 0)' : lineColor}
                          strokeWidth={strokeWidth}
                          tension={0.5}
                          lineCap="round"
                          lineJoin="round"
                          globalCompositeOperation={isErasing ? 'destination-out' : 'source-over'}
                        />
                      )}
                    </Layer>
                  </Stage>
                </div>
                {/*鼠标图标*/}
                <div
                  style={{
                    display: pointerPos.y > 0 || pointerPos.x > 0 ? 'block' : 'none',
                    position: 'absolute',
                    top: pointerPos.y - strokeWidth / 2,
                    left: pointerPos.x - strokeWidth / 2,
                    width: strokeWidth,
                    height: strokeWidth,
                    borderRadius: '50%',
                    backgroundColor: isErasing ? 'rgba(255, 255, 255, 1)' : 'rgba(97, 179, 255, 0.5)',
                    pointerEvents: 'none',
                    zIndex: 100,
                  }}
                />
              </Flex>
            </Flex>
          </Flex>
        </Flex>

        {/*异常蒙板*/}
        {createType === 'REPAIR_HANDS' && preCheckErr &&
          <Flex vertical gap={32} align={'center'} justify={'center'} className={'repair-warn'} style={{
            left: `calc(244px + (100vw - 244px) / 2 - ${imageWidth}px / 2)`,
            width: imageWidth,
            height: imageHeight,
          }}>
            <Flex vertical gap={8} className={'text16 font-pf weight color-w'} align={'center'}
                  justify={'center'}>
              <ExclamationCircleOutlined style={{ fontSize: 40, color: '#fff' }} />
              涂抹区域未检测到手部
            </Flex>

            <Flex gap={16}>
              <Button className={'retry-btn text14 font-pf color-2d'}
                      onClick={() => switchSeg(true)}>重新涂抹</Button>
              <Button className={'retry-btn text14 font-pf color-3d'}
                      onClick={() => handleSave(true)}>继续提交</Button>
            </Flex>
          </Flex>
        }

        {createType === 'REPAIR_HANDS' && <div className={'repair-video'}>
          <video width="264" height="160" autoPlay loop muted>
            <source src="https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/repair-hands-Demo.mp4"
                    type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </div>}

        <Stage className={'repair-stage'}
               width={imageWidth}
               height={imageHeight}
               ref={stageCmtRef}
               style={{ display: 'none' }}
        >
          <Layer>
            <KonvaImage image={blankImage} width={imageWidth} height={imageHeight} />
            {lines.map((line, i) => (
              <Line
                key={i}
                points={line.points.flatMap(p => [p.x, p.y])}
                stroke={'rgba(0,0,0,1)'}
                strokeWidth={line.strokeWidth}
                tension={0.5}
                lineCap="round"
                lineJoin="round"
                globalCompositeOperation={line.tool === 'erase' ? 'destination-out' : 'source-over'}
              />
            ))}
          </Layer>
        </Stage>
        <Modal open={goBackConfirm} maskClosable={false} centered title={'你做的修改尚未保存，确认退出吗？'}
               onCancel={() => setGoBackConfirm(false)} onOk={confirmGoBack}>
          返回后相关编辑内容将无法恢复
        </Modal>
      </PageContainer>
    );
  }
;

export default SmudgeTool;