@repair-header-height: 56px;
@repair-body-left-width: 244px;

.repair-container {
  width: 100vw;
  height: 100vh;
  background: #F5F6F9;
}

.repair-header {
  width: 100%;
  height: @repair-header-height;
  background: #FFFFFF;
  padding: 14px 16px;

  box-sizing: border-box;
  border-width: 0 0 0.5px 0;
  border-style: solid;
  border-color: #E8EAF0;
}

.repair-back-btn {
  //width: 68px;
  height: 28px;
}

.repair-body {
  width: 100%;
  height: calc(100vh - @repair-header-height);
}

.repair-body-left {
  position: relative;
  width: @repair-body-left-width;
  height: 100%;
  background: #FFFFFF;
}

.redraw-body-left {
  position: relative;
  width: 650px;
  height: 100%;
  background: #FFFFFF;
}

.repair-body-right {
  width: calc(100vw - @repair-body-left-width);
  height: 100%;
  padding: 16px;
}

.repair-body-left-top {
  width: 100%;
  height: auto;

  padding: 16px;
}

.repair-body-left-bottom {
  position: absolute;
  bottom: 5px;
  width: 100%;
  height: 72px;

  background: rgba(255, 255, 255, 0.8);
  box-sizing: border-box;
  /* 中性色/N3-边框、背板 */
  border-width: 1px 0px 0px 0px;
  border-style: solid;
  border-color: #E1E3EB;

  /* 背景模糊30 */
  backdrop-filter: blur(30px);
}


.repair-stage {
  height: auto;
  width: 100%;
}

.repair-img {
  width: 100%;
  height: calc(100vh - @repair-header-height - 16px - 38px - 8px);
}

.repair-video {
  position: fixed;
  top: calc(@repair-header-height + 16px + 38px + 8px);
  right: 16px;
}

.retry-btn {
  width: 104px;
  height: 38px;
  border-radius: 8px;
  background: #FFFFFF;
}

.repair-warn {
  position: fixed;
  top: calc(@repair-header-height + 16px + 38px + 8px);
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 103;
}

.repair-free-icon-desc {
  position: absolute;
  right: 8px;
  top: 2px;
  color: #864D02;
  z-index: 2;
}
.repair-text-area .ant-input-data-count {
  bottom: 3px;
  right: 10px;
}

.repair-work-item-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: auto;
  border-radius: 8px;
  background: #F5F6F9;
  padding: 12px;
  gap: 8px;
}

.repair-submit-button:disabled {
  color: #FFFFFF;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.6)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%);
}

.repair-submit-button:enabled {
  background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.repair-submit-button:not(:disabled):hover,
.repair-submit-button:not(:disabled):focus {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.repair-submit-button:not(:disabled):active {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}