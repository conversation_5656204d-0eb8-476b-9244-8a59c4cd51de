@import "@/app";

@repair-detail-body-height: calc(100vh - @navigation-height - 74px - 59px);
.repair-2-image-upload {
  height: 454px !important;
}

.repair-row-container {
  //padding-left: 16px;
  width: 100%;
  height: calc(@repair-detail-body-height);
  background: #FFFFFF;
}

.repair-detail-submit-button {
  width: 212px;
  height: 38px;
  border-radius: 8px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px 32px;
  gap: 8px;

  /* 渐变色1-disabled */
  background: linear-gradient(90deg, #99C0FF 0%, #D4C9F7 100%);

  z-index: 0;
}

.repair-detail-submit-button:disabled {
  color: #FFFFFF;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.6)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%);
}

.repair-detail-submit-button:enabled {
  background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.repair-detail-submit-button:not(:disabled):hover,
.repair-detail-submit-button:not(:disabled):focus {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.repair-detail-submit-button:not(:disabled):active {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.repair-detail-stage {
  position: absolute;
  height: auto;
  width: 100%;
}

.repair-detail-example-button{
  display: flex;
  align-items: center;
  justify-content: center;
  height: 28px;
  width: 118px;
  border-radius: 8px;
  background-color: #D9E1FF;
  color: #366EF4;
  font-size: 14px;
  gap: 2px;
}

.repair-detail-tag {
  height: 28px;
  line-height: 20px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 4px;
  padding: 4px 8px;
  position: absolute;
  top: 16px;
  left: 16px;
  color: #fff;
  text-align: center;
}
.repair-second-option-modal {
  font-family: PingFang SC, serif;
  .ant-modal-content {
    border-radius: 24px;
    padding: 24px;
  }
  .ant-modal-footer {
    position: relative;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -24px;              // 控制边框起始位置（距离左边的百分比）
      width: calc(100% + 48px); // 控制边框长度（占容器宽度的百分比）
      height: 1px;            // 边框高度
      background-color: #e8e8e8; // 边框颜色
    }
  }
}

.repair-detail-brush-tool {
  .brush-tool-content {
    display: flex;
    flex-direction: row;
    gap: 8px;
  }
  .brush-tool-text {
    align-self: center;
  }
  .ant-slider {
    flex-grow: 1;
  }
}

.repair-detail-image-sticker {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 26px;
  text-align: center;
  align-content: center;
  backdrop-filter: blur(10px);
  background: rgba(26, 27, 29, 0.4);
}

.repair-detail-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.4);
  z-index: 10;
  backdrop-filter: blur(1px);
}

.repair-detail-loading-icon {
  animation: repair-detail-spin 1.2s linear infinite;
}

.repair-detail-footer-button {
  width: 212px;
  height: 38px;
  font-size: 16px;
  color: #969799;
}

@keyframes repair-detail-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.repair-detail-blink {
  animation: repair-detail-blink-animation 0.5s ease-in-out 2;
}

@keyframes repair-detail-blink-animation {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}