import React, { useEffect, useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Button,
  Flex,
  InputNumber,
  message,
  Modal,
  Popover,
  Tooltip,
} from 'antd';
import { predict, PredictVO, updatePoint } from '@/services/PointController';
import { repairDetail } from '@/services/CreativeController';
import TaskOutputBlock, {
  TaskOutputBlockRef,
} from '@/components/Creative/TaskOutputBlock';
import TopupModal from '@/pages/Topup/Topup';
import '@/pages/toolkit/RepairDetail/index.less';
import ImageUploader, {
  ImageUploaderButton,
} from '@/components/Common/ImageUploader';
import ClothImageUploader from '@/components/Common/ClothImageUploader';
import SmudgeCanvas from '@/components/imageOperate/smudgeCanvas/SmudgeCanvasCopy';
import IconFont from '@/components/IconFont';
import { getMaskUrl } from '@/utils/imageUtils';
import { LoadingOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { useLocation, useNavigate } from 'react-router-dom';
import { SmudgeToolkit, useSmudgeInteraction } from '@/components/imageOperate/smudgeCanvas/SmudgeBrushTool';
import { sleep } from '@/utils/utils';
import useSmudgeToolkit, { LineConfig } from '@/hooks/useSmudgeToolkit';
import RepairDetailExampleContent from '@/components/Guide/RepairDetailExampleContent';

const RepairDetail = () => {
  const emptyData = {
    image: '',
    clothImage: '',
    imageNum: 4,
  };
  const [data, setData] = useState(emptyData);

  const outputBlockRef = useRef(null);
  const taskOutputRef = useRef<TaskOutputBlockRef>(null);
  const [sliderValue, setSliderValue] = useState(12);
  const [showTopupModal, setShowTopupModal] = useState(false);

  const [canSave, setCanSave] = useState(false);
  const [predictVO, setPredictVO] = useState<PredictVO | null>(null);
  const [modelId, setModelId] = useState<number>();

  const [showSecondOption, setShowSecondOption] = useState(false  );
  const stageRef = useRef(null);
  const clothStageRef = useRef(null);
  const [imageSource, setImageSource] = useState<'upload' | 'history'>(
    'upload',
  );
  const [commiting, setCommiting] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  // 图片上传中添加一个蒙版
  const [isPreloading, setIsPreloading] = useState(false);
  // 添加淡入淡出效果的状态
  const [fadeIn, setFadeIn] = useState(false);
  // 闪烁状态
  const [leftBlinking, setLeftBlinking] = useState(false);
  const [rightBlinking, setRightBlinking] = useState(false);
  // 左侧涂抹组件工具栏
  const leftToolkit = useSmudgeToolkit();
  const {
    editorState: leftEditorState,
    setEditorState: setLeftEditorState,
    handleLinesChange: handleOriginLinesChange,
  } = leftToolkit;
  
  // 左侧高级交互功能
  const {
    onScaleChange: leftOnScaleChange,
    onPositionChange: leftOnPositionChange
  } = useSmudgeInteraction([leftEditorState, setLeftEditorState]);

  // 右侧涂抹组件工具栏
  const rightToolkit = useSmudgeToolkit();
  const {
    editorState: rightEditorState,
    setEditorState: setRightEditorState,
    handleLinesChange: handleClothLinesChange
  } = rightToolkit;
  
  // 右侧高级交互功能
  const {
    onScaleChange: rightOnScaleChange,
    onPositionChange: rightOnPositionChange
  } = useSmudgeInteraction([rightEditorState, setRightEditorState]);

  const lines = leftEditorState.lines;
  const clothLines = rightEditorState.lines;


  useEffect(() => {
    const img = localStorage.getItem('imageUrl');
    if (img) {
      handleImageChange(img);
      setImageSource('history');
    }
    const mid = localStorage.getItem('modelId');
    if (mid) {
      handleModelIdChange(mid);
    }
    updatePoint();
    predict('REPAIR_DETAIL', data.imageNum, null).then((result) => {
      if (result) {
        setPredictVO(result);
      }
    });
    return () => {
      localStorage.removeItem('imageUrl');
      localStorage.removeItem('modelId');
    };
  }, []);

  useEffect(() => {
    setCanSave(isCanSave(data));
  }, [data, lines, clothLines]);

  // 监听第一次涂抹，触发闪烁
  useEffect(() => {
    if (lines.length === 1 && !leftBlinking) {
      setLeftBlinking(true);
      // setTimeout(() => setLeftBlinking(false), 1000);
    }
  }, [lines.length]);

  useEffect(() => {
    if (clothLines.length === 1 && !rightBlinking) {
      setRightBlinking(true);
      // setTimeout(() => setRightBlinking(false), 1000);
    }
  }, [clothLines.length]);

  const handleCreate = () => {
    if (imageSource === 'upload' && predictVO && predictVO.needTopup) {
      setShowTopupModal(true);
      return;
    }

    if (commiting) {
      return;
    }
    if (lines.length <= 0 || clothLines.length <= 0) {
      message.warning(`请长按鼠标涂抹需要修复的区域`);
      return;
    }
    setCommiting(true);

    async function start() {
      const mask = await getMaskUrl(stageRef);
      sleep(50);
      const clothMask = await getMaskUrl(clothStageRef);
      if (!mask || !clothMask) {
        message.error('涂抹区域处理异常, 请清空并重试');
        setCommiting(false);
        return;
      }
      const payload = {
        ...data,
        maskMergeModels: [
          { key: 'origin', image: data.image, mask: mask },
          { key: 'cloth', image: data.clothImage, mask: clothMask },
        ],
      };
      const result = await repairDetail(payload);
      setCommiting(false);
      updatePoint();
      if (!result) return;
      message.success('开始生成图片');
      handleModalCancel();
      taskOutputRef.current?.refresh();
      navigate(location.pathname, { replace: true });
    }

    start();
  };

  const isCanSave = (data) => {
    return (
      data.image &&
      data.clothImage &&
      data.imageNum &&
      data.imageNum > 0 &&
      lines.length > 0 &&
      clothLines.length > 0
    );
  };

  const handleImageChange = (value) => {
    if (value) {
      setShowSecondOption(true);
    } else {
      setShowSecondOption(false);
    }
    setData({ ...data, image: value });
  };

  const handleClothImageChange = (value) => {
    setIsPreloading(false);
    
    // 如果有值，先设置淡入效果的状态为false，然后更新数据，最后设置淡入效果
    if (value) {
      setFadeIn(false);
      setData({ ...data, clothImage: value });
      // 延迟设置淡入效果，给渲染留出时间
      setTimeout(() => {
        setFadeIn(true);
      }, 50);
    } else {
      setData({ ...data, clothImage: value });
    }
  };
  const handleModelIdChange = (value) => {
    setModelId(value);
  };
  const handleModalCancel = () => {
    setData({ ...data, image: '', clothImage: '' });
    setModelId(undefined);
    setShowSecondOption(false);
    setIsPreloading(false);
    navigate(location.pathname, { replace: true });
  };

  const ModalFooter = () => {
    return (
      <Flex
        gap={10}
        style={{
          width: 'auto',
          paddingTop: 8,
        }}
        justify={'flex-end'}
      >
        <Button className="repair-detail-footer-button" onClick={handleModalCancel}>
          取消
        </Button>
        <Button
          className="repair-detail-submit-button"
          type="primary"
          disabled={
            !(
              (predictVO?.needTopup && imageSource === 'upload') ||
              (canSave && !commiting)
            )
          }
          style={{ width: 'auto', justifyContent: 'center' }}
          onClick={handleCreate}
          icon={
            commiting ? (
              <LoadingOutlined style={{fontSize: 16, color: 'fff'}} />
            ) : (
              ''
            )
          }
        >
          <div style={{ width: 139 }}>
            {imageSource == 'history' || !predictVO?.needTopup
              ? '确认涂抹区域并生成'
              : '余额不足，去充值'}
          </div>
          {data.image &&
            data.clothImage &&
            imageSource === 'upload' &&
            canSave && (
              <>
                <IconFont
                  type={'icon-icon_mousidian'}
                  style={{ fontSize: 16 }}
                />
                {predictVO?.musePoint}
                <Tooltip
                  title={
                    '用户上传图片修复消耗0.4缪斯点，在创作历史中进行选择修复的图片免费。'
                  }
                >
                  <QuestionCircleOutlined
                    style={{ fontSize: 16 }}
                    className={'margin-left-4 color-96'}
                  />
                </Tooltip>
              </>
            )}
        </Button>
      </Flex>
    );
  };

  return (
    <PageContainer>
      <Flex className={'toolkit-row-container'}>
        <div className={'toolkit-work-block'}>
          {/* 用户上传原图 */}
          <ImageUploader
            className={'redraw-image-upload'}
            title={'选择需要修复logo的图片'}
            image={data.image}
            onImageChange={handleImageChange}
            historyFree={true}
            uploadCharge={true}
            onModelIdChange={handleModelIdChange}
            setImageSource={setImageSource}
          />
          <div className={'toolkit-number-input-container'}>
            <div className={'text16 font-pf color-n weight'}>生成数量</div>
            <InputNumber value={8} disabled={true} />
          </div>
        </div>
        <div ref={outputBlockRef} className={'toolkit-output-block'}
             style={{ width: `calc((100% - 644px) * ${sliderValue / 12})`, maxWidth: '100%' }}
        >
          <TaskOutputBlock
            sliderValue={sliderValue}
            changeSliderValue={setSliderValue}
            types={['REPAIR_DETAIL']}
            ref={taskOutputRef}
            pollingTimeout={3000}
          />
        </div>
      </Flex>
      <footer className={'toolkit-footer'}>
        <div className={'toolkit-footer-content'}></div>
      </footer>
      {showTopupModal && (
        <TopupModal
          visible={showTopupModal}
          onClose={() => setShowTopupModal(false)}
          onPaySuccess={() => {
            setShowTopupModal(false);
            updatePoint();
          }}
        />
      )}
      <Modal
        className={'repair-second-option-modal'}
        width={1284}
        centered
        footer={<ModalFooter />}
        open={showSecondOption}
        onCancel={handleModalCancel}
        maskClosable={false}
      >
        <Flex vertical gap={16} justify={'center'} align={'center'}>
          {/* Step indicator */}
          {data.image && !data.clothImage ? (
            <>
              <div
                className={'text24 weight color-1a'}
                style={{ lineHeight: '32px' }}
              >
                第二步 选择正确印花服装图片
              </div>
              <Flex gap={16} style={{ height: 622 }} align={'center'}>
                {/* Left panel - Original image section */}
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    gap: 4,
                    justifyContent: 'space-between',
                  }}
                >
                  <div
                    style={{
                      height: '560px',
                      width: '560px',
                      position: 'relative',
                    }}
                  >
                    <ImageUploader
                      image={data.image}
                      onImageChange={handleImageChange}
                    />
                    <span className={'repair-detail-tag'}>待修复图</span>
                  </div>
                </div>

                {/* Right panel - Cloth image section */}
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    gap: 4,
                    justifyContent: 'space-between',
                  }}
                >
                  <div
                    style={{
                      height: '560px',
                      width: '560px',
                      position: 'relative',
                    }}
                  >
                    <div
                      style={{
                        position: 'relative',
                        display: 'flex',
                        height: '100%',
                        width: '100%',
                      }}
                    >
                      <ClothImageUploader
                        image={data.clothImage}
                        onImageChange={handleClothImageChange}
                        render={
                          <div>选择与图片同角度的带有正确印花的服装原图</div>
                        }
                        modelId={modelId}
                        originImage={data.image}
                        setLoading={setIsPreloading}
                        desc={'请选择与待修复图片近似角度的图片'}
                      />
                      {isPreloading && (
                        <div className="repair-detail-loading-overlay">
                          <LoadingOutlined
                            style={{ fontSize: 48, color: 'fff' }}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Flex>
            </>
          ) : (
            <>
              <div
                className={'text24 weight color-1a'}
                style={{ lineHeight: '32px' }}
              >
                第三步 分别涂抹待修复图与正确图片中需要修复的区域，进行修复替换
              </div>

              <Flex gap={16}>
                {/* Left panel - Original image section */}
                <div
                  style={{
                    backgroundColor: '#F5F6F9',
                    border: '1px solid #E1E3EB',
                    borderRadius: 8,
                    padding: 16,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    gap: 8,
                    justifyContent: 'space-between',
                  }}
                >
                  <div style={{display: 'flex', alignItems: 'center'}}>
                    <span className={`text16 weight color-1a ${leftBlinking ? 'repair-detail-blink' : ''}`}>待修复图涂抹范围需</span>
                    <span className={`text16 weight color-error ${leftBlinking ? 'repair-detail-blink' : ''}`} >贴合图案实际大小</span>
                    <span className={`text16 weight color-1a ${leftBlinking ? 'repair-detail-blink' : ''}`}>↓</span>
                    <Popover content={<RepairDetailExampleContent />} trigger={['click', 'hover']}>
                      <div className={'repair-detail-example-button'}>
                        查看涂抹示例 <IconFont type={'icon-xiajiantou'} />
                      </div>
                    </Popover>
                  </div>
                  <div
                    style={{
                      height: '560px',
                      width: '560px',
                      position: 'relative',
                    }}
                  >
                    <div
                      style={{
                        backgroundColor: '#ffffff',
                        borderRadius: 8,
                        opacity: fadeIn ? 1 : 0,
                        transition: 'opacity 0.3s ease-in-out',
                        width: '100%',
                        height: '100%',
                        position: 'relative',
                      }}
                    >
                      <SmudgeCanvas
                        imageUrl={data.image}
                        stageCmtRef={stageRef}
                        lines={lines}
                        editorState={leftEditorState}
                        handleLinesChange={handleOriginLinesChange}
                        onScaleChange={leftOnScaleChange}
                        onPositionChange={leftOnPositionChange}
                      />
                      <ImageUploaderButton
                        onImageChange={handleImageChange}
                        setImageSource={setImageSource}
                      />
                      <span className={'repair-detail-tag'}>待修复图</span>
                    </div>
                  </div>
                  <div
                    style={{
                      width: '100%',
                      opacity: fadeIn ? 1 : 0,
                      transition: 'opacity 0.3s ease-in-out',
                    }}
                  >
                    <SmudgeToolkit
                      useEditorState={[leftEditorState, setLeftEditorState]}
                      gap={2}
                    />
                  </div>
                </div>

                {/* Right panel - Cloth image section */}
                <div
                  style={{
                    backgroundColor: '#F5F6F9',
                    border: '1px solid #E1E3EB',
                    borderRadius: 8,
                    padding: 16,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    gap: 8,
                    justifyContent: 'space-between',
                  }}
                >
                  <div style={{display: 'flex', alignItems: 'center'}}>
                    <span className={`text16 weight color-1a ${rightBlinking ? 'repair-detail-blink' : ''}`}>正确图涂抹范围需</span>
                    <span className={`text16 weight color-error ${rightBlinking ? 'repair-detail-blink' : ''}`}>额外扩大</span>
                    <span className={`text16 weight color-1a ${rightBlinking ? 'repair-detail-blink' : ''}`}>↓</span>
                    <Popover content={<RepairDetailExampleContent />} trigger={['click', 'hover']}>
                      <div className={'repair-detail-example-button'}>
                        查看涂抹示例 <IconFont type={'icon-xiajiantou'} />
                      </div>
                    </Popover>
                  </div>
                  <div
                    style={{
                      height: '560px',
                      width: '560px',
                      position: 'relative',
                    }}
                  >
                    <div
                      style={{
                        backgroundColor: '#ffffff',
                        borderRadius: 8,
                        transition: 'opacity 0.3s ease-in-out',
                        width: '100%',
                        height: '100%',
                        position: 'relative',
                      }}
                    >
                      <SmudgeCanvas
                        imageUrl={data.clothImage}
                        stageCmtRef={clothStageRef}
                        lines={clothLines}
                        editorState={rightEditorState}
                        handleLinesChange={handleClothLinesChange}
                        onScaleChange={rightOnScaleChange}
                        onPositionChange={rightOnPositionChange}
                      />
                      <ImageUploaderButton
                        onImageChange={handleClothImageChange}
                      />
                      <span className={'repair-detail-tag'}>正确图</span>
                    </div>
                  </div>
                  <div
                    style={{
                      width: '100%',
                      opacity: fadeIn ? 1 : 0,
                      transition: 'opacity 0.3s ease-in-out',
                    }}
                  >
                    <SmudgeToolkit
                      useEditorState={[rightEditorState, setRightEditorState]}
                      gap={2}
                    />
                  </div>
                </div>
              </Flex>
            </>
          )}
        </Flex>
      </Modal>
    </PageContainer>
  );
};

export default RepairDetail;