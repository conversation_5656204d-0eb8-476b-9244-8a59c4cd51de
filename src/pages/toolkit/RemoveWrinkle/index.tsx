import { PageContainer } from '@ant-design/pro-components';
import { Button, Flex, InputNumber, message, Upload } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { applyRemoveWrinkle, CreativeVO, queryCreativeAndSync } from '@/services/CreativeController';
import PredictBlock from '@/components/Creative/PredictBlock';
import { predict, PredictVO } from '@/services/PointController';
import TopupModal from '@/pages/Topup/Topup';
import TaskOutputBlock, { TaskOutputBlockRef } from '@/components/Creative/TaskOutputBlock';
import ImageUploader from '@/components/Common/ImageUploader';
import { MINI_LOADING_ICON } from '@/constants';
import { LoadingOutlined } from '@ant-design/icons';


const RemoveWrinkle = () => {
  const [repairButtonDisabled, setrepairButtonDisabled] = useState(false);
  const [originImgUrl, setOriginImgUrl] = useState<string>('');
  const [showTopupModal, setShowTopupModal] = useState(false);
  const [userInfo, setUserInfo] = useState<any>(null);
  const [predictVO, setPredictVO] = useState<PredictVO | null>(null);
  const [loading, setloading] = useState(false);
  //当前用户正在进行中的去皱任务，最多只能有一个
  const [creativeVO, setCreativeVO] = useState<CreativeVO>();
  const outputRef = useRef<TaskOutputBlockRef>(null);
  const outputBlockRef = useRef(null);
  const [canSave, setCanSave] = useState(false);
  const [commiting, setCommiting] = useState<boolean>(false);
  const [imageSource, setImageSource] = useState<'upload' | 'history'>('upload');
  const [sliderValue, setSliderValue] = useState<number>(12);

  useEffect(() => {
    const img = localStorage.getItem('imageUrl');
    if (img) {
      setOriginImgUrl(img);
      setImageSource('history');
    }
    const userInfo = localStorage.getItem('userInfo');
    if (userInfo) {
      // 解析用户信息
      const userInfoObj = JSON.parse(userInfo);
      // 设置用户信息
      setUserInfo(userInfoObj);
    }

    doPredict();

    return () => {
      localStorage.removeItem('imageUrl');
      localStorage.removeItem('modelId');
    }
  }, []);

  useEffect(() => {
    if (originImgUrl) {
      setCanSave(true);
    } else {
      setCanSave(false);
    }
  }, [originImgUrl]);

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null; // 定义定时器变量

    if (creativeVO && creativeVO.id && creativeVO.status !== 'FINISHED') {
      // 只有在 creativeVO 存在时才开始轮询
      interval = setInterval(async () => {
        const result = await queryCreativeAndSync(creativeVO.id);
        if (result) {
          setCreativeVO(result); // 更新状态以触发重新渲染

          // 如果状态为 FINISHED，则停止定时器并更新 loading 状态
          if (result.status === 'FINISHED') {
            clearInterval(interval!); // 清理定时器
            setloading(false); // 停止加载状态
          }
        }
      }, 2000); // 每2秒调用一次
    }

    return () => {
      if (interval) {
        clearInterval(interval); // 清理定时器
      }
    };
  }, [creativeVO]); // 只依赖 creativeVO

  async function doPredict(){
    predict('REMOVE_WRINKLE', 1).then(res=> {
      if (res){
        setPredictVO(res);
        // if (res.needTopup) {
        //   setShowTopupModal(true);
        // }
      }
    })
  }

  const handleCommit = () => {
    if (!originImgUrl) {
      message.error('请先上传图片');
      return;
    }
    if (imageSource === 'upload' && predictVO && predictVO.needTopup) {
      setShowTopupModal(true);
      return;
    }
    if (commiting) {
      return;
    }
    setCommiting(true);
    


      applyRemoveWrinkle({
          originImage: originImgUrl,
        })
      .then(res => {
        if (res) {
          message.success('提交成功');
          setTimeout(() => {
            setloading(true);
            setCreativeVO(res);
            outputRef.current?.refresh();
          }, 0);
        }
        setCommiting(false);
      })
      .catch(error => {
        message.error(error.message || '图片处理失败');
        setCommiting(false);
      });
  }

  const handleImageChange = (value) => {
    setOriginImgUrl(value);
  }

  return (
    <PageContainer>
      <Flex className={"toolkit-row-container"}>
        <div className={'toolkit-work-block'}>
          <ImageUploader className={"redraw-image-upload"} title={'待修复图片'} image={originImgUrl} setImageSource={setImageSource} onImageChange={handleImageChange} historyFree uploadCharge />
          <div className={'toolkit-number-input-container'}>
            <div className={'text16 font-pf color-n weight'}>生成数量</div>
            <InputNumber value={1} disabled={true}/>
          </div>
        </div>
        <div ref={outputBlockRef} className={'toolkit-output-block'}
             style={{ width: `calc((100% - 644px) * ${sliderValue / 12})`, maxWidth: '100%' }}
        >
          <TaskOutputBlock sliderValue={12}
                           changeSliderValue={setSliderValue}
                           types={['REMOVE_WRINKLE']}
                           ref={outputRef}
                           pollingTimeout={2000}
          />
        </div>
      </Flex>
      <footer className={'toolkit-footer'}>
        <div className={'toolkit-footer-content'}>
          {canSave && imageSource == 'upload' && <PredictBlock creativeType={'REMOVE_WRINKLE'} predictVO={predictVO} /> }
          <Button type="primary" className={'create-btn'}
                  disabled={!canSave || commiting || predictVO?.needTopup}
                  icon={(commiting) ? <LoadingOutlined style={{fontSize: 16, color: 'fff'}} /> : ''}
                  onClick={handleCommit}> {predictVO?.needTopup ? '余额不足，去充值' : commiting ? '生成中' : '生成图片'}</Button>
        </div>
      </footer>
      {showTopupModal &&
        <TopupModal visible={showTopupModal} onClose={() => setShowTopupModal(false)}
                    onPaySuccess={() => {
                      setShowTopupModal(false);
                      doPredict();
                    }} />
      }
    </PageContainer>
  );
};

export default RemoveWrinkle;