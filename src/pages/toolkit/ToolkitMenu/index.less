@import "@/app";

.toolkit-form-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.toolkit-form-body {
  padding: 0 16px 0;
  border-radius: 8px;
  background: #FFFFFF;
  height: @toolkit-table-height;
  .ant-tabs-tab {
    width: 102px;
    justify-content: center;
  }
  .toolkit-content {
    width: 100%;
  }
}

.toolkit-work-block {
  padding: 0 8px 0 8px;
  background: linear-gradient(0deg, #FFFFFF, #FFFFFF), #FFFFFF;
  width: @toolkit-work-block-width;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-content: flex-start;
  justify-content: flex-start;
  gap: 8px;
}

.toolkit-row-container {
  width: 100%;
  height: calc(100vh - @navigation-height - @toolkit-table-height - @toolkit-footer-height);
  font-family: "PingFang SC", serif;
}

.toolkit-row-container-fix {
  width: 100%;
  height: calc(100vh - @navigation-height - @toolkit-footer-height);
  font-family: "PingFang SC", serif;
}

.toolkit-output-block {
  position: absolute;
  right: 0;
  width: @toolkit-output-width;
  height: @toolkit-output-height;
  background: #f3f5f8;
  border-radius: 8px;

  .task-bar {
    height: @toolkit-output-height;
  }
  .task-image-bar {
    height: @toolkit-output-height;
    .image-container {
      height: 100%;
    }
    /*.slider-bar {
      height: @toolkit-footer-height;
      .message-card {
        margin-bottom: 13px;
      }
      .col-start-block {
        margin-bottom: 13px;
      }
    }*/
  }
}

.toolkit-output-block-fix {
  position: absolute;
  right: 0;
  width: @toolkit-output-width;
  height: @toolkit-output-height-fix;
  .task-bar {
    height: @toolkit-output-height-fix;
  }
  .task-image-bar {
    height: @toolkit-output-height-fix;
    .image-container {
      height: 100%;
    }
    /*.slider-bar {
      height: @toolkit-footer-height;
      .message-card {
        margin-bottom: 13px;
      }
      .col-start-block {
        //width: 80%;
        margin-bottom: 13px;
      }
    }*/
  }
}

.toolkit-footer {
  width: @toolkit-work-block-width;
  height: @toolkit-footer-height;
  padding: 8px 32px 7px 16px;
  background: #FFFFFF;
  border-top: 1px solid #e8eaf0;
  &-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 16px;
  }
}

.toolkit-upload-common {
  height: 680px !important;
}

.toolkit-number-input-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 64px;
  background: #F5F6F9;
  border-radius: 8px;
  padding: 12px;
  .ant-input-number {
    background: #FFFFFF;
    width: 150px;
  }
}
.toolkit-submit-btn {
  width: 212px;
  height: 46px;
  border-radius: 8px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px 32px;
  border: 0;
  color: #FFFFFF;
  border-color: transparent !important;
  z-index: 1;
}

.toolkit-submit-btn:disabled {
  color: #FFFFFF;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.6)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%);
}

.toolkit-submit-btn:enabled {
  background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.toolkit-submit-btn:not(:disabled):hover, .toolkit-submit-btn:not(:disabled):focus {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.toolkit-submit-btn:not(:disabled):active {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}
