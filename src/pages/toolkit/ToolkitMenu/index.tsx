import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Flex, Tabs, TabsProps } from 'antd';
import '@/pages/toolkit/ToolkitMenu/index.less';
import RepairDetail from '@/pages/toolkit/RepairDetail';
import RemoveWrinkle from '@/pages/toolkit/RemoveWrinkle';
import PartialRedraw from '@/pages/toolkit/PartialRedraw';
import EraseBrush from '@/pages/toolkit/EraseBrush';
import RepairHands from '@/pages/toolkit/PartialRedraw/RepairHands';
import { CreativeType, isValidCreativeType } from '@/services/CreativeController';
import ImageUpscale from '@/pages/toolkit/ImageUpscale';
import ClothRecolor from '@/pages/toolkit/Recolor/recolor';
import { USER_INFO } from '@/constants';

// 同步初始化状态
const getInitialToolType = (): CreativeType => {
  try {
    const storedType = localStorage.getItem('creativeType');
    return storedType && isValidCreativeType(storedType)
      ? storedType
      : 'REPAIR_DETAIL';
  } catch {
    return 'REPAIR_DETAIL';
  }
};

const ToolkitMenu = () => {
  const userInfo = localStorage.getItem(USER_INFO);
  const userRole = userInfo ? JSON.parse(userInfo).roleType : 'NONE';
  const [toolType, setToolType] = useState<CreativeType>(getInitialToolType());

  useEffect(() => {
    // Clear cache on page refresh
    const handleBeforeUnload = () => {
      localStorage.removeItem('creativeType');
      localStorage.removeItem('imageUrl');
      localStorage.removeItem('modelId');
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    }
  }, []);

  // 工具配置，统一管理工具项、组件映射和权限控制
  const toolConfig = {
    'REPAIR_DETAIL': {
      label: '细节修补',
      component: RepairDetail,
      permission: true,
    },
    'PARTIAL_REDRAW': {
      label: '局部重绘',
      component: PartialRedraw,
      permission: true,
    },
    'REPAIR_HANDS': {
      label: '手部修复',
      component: RepairHands,
      permission: true,
    },
    'REMOVE_WRINKLE': {
      label: '衣服去皱',
      component: RemoveWrinkle,
      permission: true,
    },
    'ERASE_BRUSH': {
      label: '消除笔',
      component: EraseBrush,
      permission: true,
    },
    'IMAGE_UPSCALE': {
      label: '2倍放大',
      component: ImageUpscale,
      permission: true,
    },
    'CLOTH_RECOLOR': {
      label: '服装换色',
      component: ClothRecolor,
      permission: ['OPERATOR', 'ADMIN'].includes(userRole),
    },
  };
  // 动态获取当前组件
  const CurrentComponent = toolType && toolConfig[toolType]?.permission ? toolConfig[toolType].component : null;

  const handleTabChange = (key) => {
    setToolType(key);
  }

  return (
    <PageContainer>
      <div className="toolkit-form-container">
        <Flex vertical className={'toolkit-form-body'}>
          <Tabs 
            activeKey={toolType} 
            items={Object.entries(toolConfig)
              .filter(([_, config]) => config.permission)
              .map(([key, config]) => ({
                key,
                label: config.label,
              }))} 
            onChange={handleTabChange} 
            size={'large'}
            indicator={{ size: 102 }} 
          />
        </Flex>
      </div>
      {CurrentComponent && <CurrentComponent className={"toolkit-content"} />}
    </PageContainer>
  )
}

export default ToolkitMenu;