import ImageUploader from '@/components/Common/ImageUploader';
import PredictBlock from '@/components/Creative/PredictBlock';
import TaskOutputBlock, {
  TaskOutputBlockRef,
} from '@/components/Creative/TaskOutputBlock';
import TopupModal from '@/pages/Topup/Topup';
import { CreativeType, imageUpscale } from '@/services/CreativeController';
import { predict, PredictVO, updatePoint } from '@/services/PointController';
import { getTaskIdByImage } from '@/utils/utils';
import { LoadingOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Button, Flex, InputNumber, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

const type: CreativeType = 'IMAGE_UPSCALE';
const maxImageSize = 1920;
const EraseBrush = () => {
  const [canSave, setCanSave] = useState(false);
  const [imageSource, setImageSource] = useState<'upload' | 'history'>(
    'history',
  );
  const [image, setImage] = useState<string>('');
  const [imageNum, setImageNum] = useState(2);
  const [predictVO, setPredictVO] = useState<PredictVO | null>(null);
  const [commiting, setCommiting] = useState<boolean>(false);
  const taskOutputRef = useRef<TaskOutputBlockRef>(null);
  const [showTopupModal, setShowTopupModal] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const [sliderValue, setSliderValue] = useState<number>(12);

  // 检查taskId是否存在
  useEffect(() => {
    const img = localStorage.getItem('imageUrl');
    if (img) {
      setImage(img);
      setImageSource('history');
    }
    updatePoint();
    doPredict();
    return () => {
      localStorage.removeItem('imageUrl');
      localStorage.removeItem('modelId');
    };
  }, []);

  useEffect(() => {
    doPredict();
  }, [imageSource]);

  useEffect(() => {
    setCanSave(!!image);
  }, [image]);

  const doPredict = async () => {
    predict(type, imageNum, null, imageSource === 'upload').then((result) => {
      if (result) {
        setPredictVO(result);
      }
    });
  };
  const handleCreate = async () => {
    if (predictVO && predictVO.needTopup) {
      setShowTopupModal(true);
      return;
    }
    if (commiting) {
      return;
    }
    setCommiting(true);

    async function start() {
      let taskId = getTaskIdByImage(image);
      const result = await imageUpscale(taskId, image, imageSource);
      setCommiting(false);
      updatePoint();
      doPredict();
      if (!result) {
        return;
      }
      message.success('开始生成图片');
      taskOutputRef.current?.refresh();
      navigate(location.pathname, { replace: true });
    }

    start();
  };

  const handleImageChange = (value) => {
    setImage(value);
  };

  return (
    <PageContainer>
      <Flex className={'toolkit-row-container'}>
        <div className={'toolkit-work-block'}>
          {/* 上传图片并涂抹 */}
          {image ? (
            <div className={'redraw-smudge-container'}>
              <span className={'font-pf text16 weight'}>上传图片</span>
              <span className={'font-pf text12 normal'}>
                最大支持{maxImageSize}*{maxImageSize}的图片，超出时会自动进行缩小
              </span>
              <img src={image} alt={'image'} />
            </div>
          ) : (
            <ImageUploader
              className={'redraw-image-upload'}
              title={'上传图片'}
              image={image}
              resize={maxImageSize}
              onImageChange={handleImageChange}
              historyCost={0.4}
              uploadCost={0.4}
              uploadCharge
              setImageSource={setImageSource}
              excludesType={['IMAGE_UPSCALE']}
            />
          )}
          <div className={'toolkit-number-input-container'}>
            <div className={'text16 font-pf color-n weight'}>生成数量</div>
            <InputNumber
              value={imageNum}
              disabled={true}
              onChange={(value) => {
                setImageNum(value || 0);
              }}
            />
          </div>
        </div>
        <div className={'toolkit-output-block'}
             style={{ width: `calc((100% - 644px) * ${sliderValue / 12})`, maxWidth: '100%' }}
        >
          <TaskOutputBlock
            sliderValue={sliderValue}
            changeSliderValue={setSliderValue}
            types={[type]}
            ref={taskOutputRef}
            pollingTimeout={3000}
          />
        </div>
      </Flex>
      <footer className={'toolkit-footer'}>
        <div className={'toolkit-footer-content'}>
          <PredictBlock creativeType={type} predictVO={predictVO} />
          <Button
            type="primary"
            className={'toolkit-submit-btn'}
            disabled={
              !(
                (predictVO?.needTopup && imageSource === 'upload') ||
                (canSave && !commiting)
              )
            }
            icon={
              commiting ? (
                <LoadingOutlined style={{ fontSize: 16, color: 'fff' }} />
              ) : (
                ''
              )
            }
            onClick={handleCreate}
          >
            {' '}
            {predictVO?.needTopup && imageSource === 'upload'
              ? '余额不足，去充值'
              : commiting
                ? '提交中'
                : '开始放大'}
          </Button>
        </div>
      </footer>
      {showTopupModal && (
        <TopupModal
          visible={showTopupModal}
          onClose={() => setShowTopupModal(false)}
          onPaySuccess={() => {
            setShowTopupModal(false);
            updatePoint();
          }}
        />
      )}
    </PageContainer>
  );
};

export default EraseBrush;
