import React, { useCallback, useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Row,
  Col,
  Button,
  DatePicker,
  Select,
  message,
  Input,
  Tooltip,
  Pagination,
  Tabs,
  Flex,
  notification, Modal, Alert, Radio,
} from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';
import './index.less';
import '@/app.less';
import {
  assignElementModelToUser,
  assignLoraTo,
  changeExampleImages,
  clothMarkFlags, getClothMarkedColor,
  markCloth, MaterialModel, MaterialModelWithBlogs,
  queryAllModelCreators,
  queryMaterialModelByPage,
} from '@/services/MaterialModelController';
import { queryAllMaster, UserVO } from '@/services/UserController';
import Flow from '@/components/Flow/flow';
import ClothDetailDlg from './ClothDetailDlg';
import { helix } from 'ldrs';
import { calculateTimeDifference } from '@/utils/dateUtils';
import IconFont from '@/components/IconFont';
import {
  getDistributorCustomerByPage,
  queryCustomersOptionsByDistributorMasterId,
  queryLoras4Distributor,
} from '@/services/DistributorController';
import { getUserInfo } from '@/utils/utils';
import LoraImageSelector from '@/components/Operate/LoraImageSelector';
import AssignModel2User from '@/components/AssignModel2User';
import NewLabel from '@/components/new/NewLabel';
import { DEBOUNCE_DELAY } from '@/constants';
import debounce from 'lodash/debounce';

helix.register();

const { RangePicker } = DatePicker;
const { Option } = Select;

//全部服装页面中的服装详情页
const AllModelsPage = () => {

  const [dates, setDates] = useState([]);
  const [selectedCreator, setSelectedCreator] = useState(undefined);
  const [nameLike, setNameLike] = useState<string>('');
  const [searchValue, setSearchValue] = useState<string>(''); // 用于输入框显示的值

  const [models, setModels] = useState<Array<MaterialModel>>([]);
  const [creators, setCreators] = useState<Array<UserVO>>([]);

  //显示衣服详情
  const [showClothDetail, setShowClothDetail] = useState<boolean>(false);
  const [selectedModel, setSelectedModel] = useState<MaterialModel>();

  //渠道商-设置客户精选图
  const [exampleImagesLora, setExampleImagesLora] = useState<null | number>(null);
  const [exampleImages, setExampleImages] = useState<null | Array<string>>(null);
  //渠道商的客户列表
  const [customerOptions, setCustomerOptions] = useState<Array<any>>([]);
  const [searchUserId, setSearchUserId] = useState<number | null>(null);

  const userInfo = getUserInfo();

  const [total, setTotal] = useState(0);

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const [loading, setLoading] = useState(true);

  const [materialType, setMaterialType] = useState<string>('cloth');
  const [materialTypeTitle, setMaterialTypeTitle] = useState<string>('服装');

  const [assignElementLora, setAssignElementLora] = useState<MaterialModelWithBlogs | null>(null);

  const location = useLocation();
  const navigate = useNavigate();

  // 防抖搜索函数
  const debouncedSearchName = useCallback(
    debounce((value: string) => {
      setNameLike(value || '');
    }, DEBOUNCE_DELAY),
    []
  );

  const handlePageChange = (page: number, pageSize?: number) => {
    setPage(page);
    if (pageSize) {
      setPageSize(pageSize);
    }
  };

  useEffect(() => {

    //跳转时页面参数指定了type
    const queryParams = new URLSearchParams(location.search);
    const type = queryParams.get('type') || 'cloth';
    setMaterialType(type);

    //获取前台商家视角对应的资产创建人名单
    if (userInfo && userInfo?.roleType !== 'DISTRIBUTOR') {
      queryAllModelCreators().then(res => {
        if (res){
          setCreators(res);
        }
      });
    }

    //获取渠道商的客户列表
    if (userInfo && userInfo?.roleType === 'DISTRIBUTOR'){
      getDistributorCustomerByPage({
        pageNum: 1,
        pageSize: 9999999,
        orderBy: 'id desc',

      }).then(res => {
        if (res && res.list) {
          //@ts-ignore 追加一个key字段，避免警告
          let newData = res.list.map(item => ({ label: item.nickName, value: item.id }));
          setCustomerOptions(newData);
        } else {
          setCustomerOptions([]);
        }
      });
    }

  }, []);

  useEffect(()=>{
    if (materialType === 'cloth') {
      setMaterialTypeTitle('服装');
    } else if (materialType === 'face') {
      setMaterialTypeTitle('模特');
    } else if (materialType === 'scene') {
      setMaterialTypeTitle('场景');
    }
  }, [materialType]);

  async function fetchModels() {

    let query = {};
    if (dates && dates.length > 1) {
      // @ts-ignore
      query['dateFrom'] = dates[0].format('YYYYMMDD');
      // @ts-ignore
      query['dateTo'] = dates[1].format('YYYYMMDD');
    }

    //创建者（商家/运营视角）
    if (selectedCreator) {
      query['operatorId'] = selectedCreator;
    }

    if (nameLike && nameLike.trim()) {
      query['nameLike'] = nameLike.trim();
    }

    query['pageNum'] = page;
    query['pageSize'] = pageSize;
    query['materialType'] = materialType;

    // 渠道商-客户
    if (searchUserId){
      query['userId'] = searchUserId;
    }

    setLoading(true)

    let method = userInfo?.roleType === 'DISTRIBUTOR' ? queryLoras4Distributor : queryMaterialModelByPage;

    method(query).then(res => {
      if (res){
        setModels(res?.list || []);
        if (res?.totalCount !== null) {
          setTotal(res?.totalCount || 0);
        }
      }
      setLoading(false);
    });
  }

  useEffect(() => {
    fetchModels();
  }, [page, pageSize]);

  useEffect(() => {
    setPage(1)
    fetchModels();
  }, [dates, selectedCreator, nameLike, materialType, searchUserId]);

  // 组件卸载时清理防抖函数
  useEffect(() => {
    return () => {
      debouncedSearchName.cancel();
    };
  }, [debouncedSearchName]);

  // 渠道商：设置精选图
  const handleChangeExampleImages = () => {
    if (!exampleImages || exampleImages.length <= 0) {
      message.warning('请选择图片');
      return;
    }
    console.log('handleChangeExampleImages:', exampleImages);
    changeExampleImages({ id: exampleImagesLora, exampleImages }).then((res) => {
      if (res) {
        notification.success({ message: '修改成功' });
        setExampleImagesLora(null);
        setExampleImages(null);
        fetchModels();
      }
    });
  };

  const handleDateChange = (newDates) => {
    console.log('handleDateChange:', newDates);
    setDates(newDates);
  };

  const handleCreatorChange = (value) => {
    setSelectedCreator(value);
  };

  //查看服装详情
  function onShowClothDetail(model: MaterialModel) {
    setShowClothDetail(true);
    setSelectedModel(model);

    console.log('onShowClothDetail:', model);
  }

  function onCancelClothDetailDlg() {
    setShowClothDetail(false);
    setSelectedModel(undefined);
  }

  function onConfirmClothDetailDlg() {
    setShowClothDetail(false);
    setSelectedModel(undefined);
    //刷新当前页面
    window.location.reload();
  }

  function getCreationUrl(item: MaterialModel){
    let url = '/creation?isOwner=' + (userInfo && userInfo.id === item.operatorId ? '1' : '0');

    if (materialType === 'cloth') {
      url += ('&modelId=' + item.id);
    } else if (materialType === 'face') {
      url += ('&faceId=' + item?.relatedElementVO?.id);
    } else if (materialType === 'scene') {
      url += ('&sceneId=' + item?.relatedElementVO?.id);
    }

    return url;
  }

  const ImageCard = (item: any) => (
    <div className="models-image-card" >
      <Tooltip title={(userInfo.roleType === 'MERCHANT' && item.extInfo?.memo) ? item.extInfo.memo : null} placement="top">
        <div className="models-img-cover" style={{ cursor: 'pointer' }}>
          <img src={item?.relatedElementVO ? item?.relatedElementVO?.showImage : item.showImage} alt={''} onClick={() => {onShowClothDetail(item);}} />

          {userInfo && userInfo.roleType === 'MERCHANT' && clothMarkFlags.map((flag) => {
            let isMarkedThisColor = getClothMarkedColor(item) === flag.key;
            return (
              <Tooltip title={isMarkedThisColor ? `点击取消${materialTypeTitle}的${flag.name}标记` : `点击标记${materialTypeTitle}为${flag.name}`}
                      key={flag.key} placement={'leftTop'}>
                <IconFont
                  className={isMarkedThisColor ? 'cloth-flag cloth-flag-marked' : 'cloth-flag'}
                  type={flag.icon}
                  style={{ top: flag.top }}
                  onClick={() => {
                    markCloth(item.id, 'markedColor', !isMarkedThisColor ? flag.key : 'None').then((res) => {
                      if (res) {
                        message.info(isMarkedThisColor ? `你取消了${materialTypeTitle}的${flag.name}标记` : `你标记${materialTypeTitle}为${flag.name}`);
                        fetchModels();
                      } else {
                        message.error('操作失败');
                      }
                    });
                  }}
                />
              </Tooltip>
            );
          })}

          {userInfo?.roleType === 'DISTRIBUTOR' && item?.extInfo && item?.extInfo['clothNum'] && Number(item?.extInfo['clothNum']) <= 3 &&
            <NewLabel />
          }

          {(() => {

            //渠道商
            if (userInfo?.roleType === 'DISTRIBUTOR') {

              //训练中 & 审核不通过
              if (item.status === 'IN_TRAINING' || item.status === 'TESTING') {
                return (
                  <div className="models-traing-mask"
                      style={{ cursor: 'pointer' }}
                      onClick={() => {
                        onShowClothDetail(item);
                      }}>
                    <div style={{ marginTop: 74 }}>
                      <l-helix size="45" speed="2.5" color="#D88FFF" />
                    </div>
                    <div
                      className="models-training-title">模型训练中
                    </div>
                    <div
                      className="models-training-finish-time">预计完成时间{calculateTimeDifference(item.createTime)}小时
                    </div>
                  </div>
                )

              } else if (item?.status === 'DISABLED') {
                return (
                  <div className="models-traing-mask" onClick={() => onShowClothDetail(item)}>
                    <div className="models-training-title" style={{marginTop: '50%'}}>取消学习</div>
                    {item.extInfo && item.extInfo['disableReason'] &&
                    <div className="models-training-finish-time">{item.extInfo['disableReason']}</div>}
                    <div className="button-container">
                      <Button className="model-action-btn"
                              onClick={() => onShowClothDetail(item)}>查看信息</Button>
                    </div>
                  </div>
                );
                //审核通过
              } else if (item?.status === 'ENABLED') {
                return (
                  <div className="button-container">
                    <Button className="model-action-btn" onClick={() => {
                      onShowClothDetail(item);
                    }}>
                      详情
                    </Button>

                    {materialType === 'cloth' &&
                      <Button className="model-action-btn" onClick={() => {
                        setExampleImagesLora(item.id);
                      }}>配精选图</Button>
                    }

                    {(materialType ==='face' || materialType ==='scene') &&
                      <Button className="model-action-btn" onClick={() => {
                        setAssignElementLora(item);
                      }}>转交客户
                      </Button>
                    }

                  </div>
                )
              }

              //其它角色（前台商家，运营）
            } else {

              // 商家、运营视角：训练中
              if (item.status === 'IN_TRAINING' || item.status === 'TESTING') {
                return (
                  <div className="models-traing-mask" onClick={() => onShowClothDetail(item)}>
                    <div style={{ marginTop: 65 }}>
                      <l-helix size="45" speed="2.5" color="#D88FFF" />
                    </div>
                    <div className="models-training-title">深度学习{materialTypeTitle}中</div>
                    <div
                      className="models-training-finish-time">预估剩余时间：{calculateTimeDifference(item.createTime)}小时
                    </div>
                    <div className="button-container">
                      <Button className="model-action-btn"
                              onClick={() => onShowClothDetail(item)}>查看信息</Button>
                    </div>
                  </div>
                );
              }
              // 取消学习/审核不通过
              else if (item.status === 'DISABLED') {
                return (
                  <div className="models-traing-mask" onClick={() => onShowClothDetail(item)}>
                    <div className="models-training-title" style={{marginTop: '50%'}}>取消学习</div>
                    {item.extInfo && item.extInfo['disableReason'] &&
                    <div className="models-training-finish-time">{item.extInfo['disableReason']}</div>}
                    <div className="button-container">
                      <Button className="model-action-btn"
                              onClick={() => onShowClothDetail(item)}>查看信息</Button>
                    </div>
                  </div>
                );
              }
              // 训练完成
              else {
                return (
                  <div className="button-container">
                    <Button className="model-action-btn" onClick={() => {
                      onShowClothDetail(item);
                    }}>
                      详情
                    </Button>

                    {materialType === 'cloth' &&
                      <Button className="model-action-btn" onClick={() => {
                        navigate('/history?modelId=' + item.id);
                      }}>
                        历史创作
                      </Button>
                    }

                    <Button className="go-create-button" onClick={() => navigate(getCreationUrl(item))}>
                      去创作
                    </Button>
                  </div>
                );
              }
            }

          })()}

        </div>
      </Tooltip>
      {/*模型名称*/}
      <div className="models-image-card-info-left">
        <div className="models-image-card-name">
          {item?.relatedElementVO ? item?.relatedElementVO?.name : item.name}
        </div>
        <div className="models-image-card-info">创建时间：{item.createTime}</div>
        {materialType === 'cloth' && item.clothTypeDesc && <div className="models-image-card-info">服装种类：{item.clothTypeDesc}</div>}

        {/*渠道商视角：客户和销售信息，如果是属于渠道商自己上传的，则不展示客户和销售信息*/}
        {userInfo?.roleType === 'DISTRIBUTOR' && item?.userRole !== 'DISTRIBUTOR' &&
          <Flex vertical gap={4}>
            <div className="models-image-card-info">客户：{item?.userNick}</div>
            <div className="models-image-card-info">销售：{item?.relatedDistributorInfo?.distributorSalesNickName}</div>
          </Flex>
        }
      </div>
    </div>
  );

  const handleUploadMaterial = () => {
    navigate('/upload?type=' + materialType);
  };

  return (
    <PageContainer className={'models-page'}>

      {/*步骤条*/}
      {userInfo?.roleType === 'MERCHANT' && <Flow activeStep={2} />}

      <div className={'models-main-container'}>
        <Tabs
          activeKey={materialType}
          items={[
            { key: 'cloth', label: '全部服装' },
            { key: 'face', label: '全部模特' },
            { key: 'scene', label: '全部场景' },
          ]}
          onChange={(key) => {
            setMaterialType(key);
            setPage(1);
          }}
          tabBarExtraContent={(
            <>
              {userInfo?.roleType !== 'DISTRIBUTOR' &&
                <div className="models-filter-row">
                  <RangePicker onChange={handleDateChange} placeholder={['开始日期', '结束日期']} />
                  <Input 
                    placeholder={'名称模糊搜索'} 
                    style={{ width: 200 }} 
                    allowClear
                    value={searchValue}
                    onChange={(e) => {
                      const value = e.target.value;
                      setSearchValue(value); // 立即更新输入框显示
                      
                      // 如果清空了输入框，立即触发搜索
                      if (!value) {
                        debouncedSearchName.cancel(); // 取消之前的防抖
                        setNameLike('');
                      } else {
                        // 否则使用防抖搜索
                        debouncedSearchName(value);
                      }
                    }} 
                  />
                  <Select placeholder="请选择创建者" onChange={handleCreatorChange} style={{ width: 200 }} allowClear>
                    {creators.map((creator) => (
                      <Option key={creator.id} value={creator.id}>{creator.nickName}</Option>
                    ))}
                  </Select>
                </div>
              }

              {userInfo?.roleType === 'DISTRIBUTOR' &&
                <Flex justify={'space-between'}>
                  <Flex gap={16}>
                    <Flex align={'center'}>
                      <div>选择客户：</div>
                      <Select options={customerOptions} style={{ width: 'auto', minWidth: 160 }} showSearch allowClear
                              placeholder={'选择客户'}
                              optionFilterProp="label" defaultActiveFirstOption={true} value={searchUserId}
                              onChange={(e) => setSearchUserId(e)} />
                    </Flex>

                    <Flex align={'center'}>
                      <div>{materialTypeTitle}名称：</div>
                      <Input 
                        allowClear
                        placeholder={`${materialTypeTitle}名称`}
                        value={searchValue}
                        onChange={(e) => {
                          const value = e.target.value;
                          setSearchValue(value); // 立即更新输入框显示
                          
                          // 如果清空了输入框，立即触发搜索
                          if (!value) {
                            debouncedSearchName.cancel(); // 取消之前的防抖
                            setNameLike('');
                          } else {
                            // 否则使用防抖搜索
                            debouncedSearchName(value);
                          }
                        }}
                        style={{ width: 120 }} 
                      />
                    </Flex>

                  </Flex>
                </Flex>
              }
            </>
          )}
        />

        {(() => {

          if (loading) {
            return <></>;
          }

          if (total === 0 && userInfo?.roleType === 'MERCHANT') {
            return (
              <div className={'models-no-cloth-container'}>
                <div className={'upload-cloth-text'}>暂无数据，请先上传素材</div>
                <Button className={'upload-cloth-btn'}
                        onClick={handleUploadMaterial}>
                  <div className={'text16 font-pf weight color-24'}>传素材</div>
                </Button>
              </div>
            );
          } else {
            return (
              <>
                {/* 服装列表 */}
                <div className="models-form-container">
                  <Row gutter={[16,16]}>
                    {models.map((model, index) => (
                      <Col key={index}>
                        <ImageCard {...model} />
                      </Col>
                    ))}
                  </Row>
                </div>
              </>
            );
          }
        })()}

      </div>

      <div className={'stick-bottom-pagination'}>
        <Pagination
          current={page}
          pageSize={pageSize}
          total={total}
          showTotal={(total) => `共 ${total} 条记录`}
          onChange={handlePageChange}
          showSizeChanger // 允许用户更改每页显示条数
          showQuickJumper // 允许用户快速跳转到某一页
          style={{ marginTop: '8px', textAlign: 'center' }}
        />
      </div>

      {/* 服装详情，只给自己创建的情况下，允许编辑 */}
      {showClothDetail && selectedModel && (
        <ClothDetailDlg
          materialType={materialType}
          materialTypeTitle={materialTypeTitle}
          model={selectedModel}
          onCancel={onCancelClothDetailDlg}
          onConfirm={onConfirmClothDetailDlg}
          editAble={userInfo?.masterId === selectedModel?.userId || userInfo?.id === selectedModel?.userId}
        />
      )}

      {/*渠道商：设置精选图*/}
      {exampleImagesLora &&
        <Modal open={true} centered closable={false} width={'auto'} maskClosable={false}
               onCancel={() => setExampleImagesLora(null)} onOk={handleChangeExampleImages}>
          <Flex style={{ width: 1300 }} justify={'flex-start'} align={'center'}>
            <div className={'weight'} style={{ margin: '0 20px' }}>
              <span style={{ color: 'red' }}>* </span>精选图:
            </div>
            <LoraImageSelector maxChoose={50} modelId={exampleImagesLora} onChange={(value) => {
              setExampleImages(value);
            }} />
          </Flex>
        </Modal>
      }

      {/*渠道商：将模特和场景转移给客户*/}
      {assignElementLora &&
        <AssignModel2User
          materialType={materialType}
          onCancel={() => {
            setAssignElementLora(null);
          }} onOk={()=>{
          setAssignElementLora(null);
          fetchModels();
        }}
          assignElementLora={assignElementLora}
        />
      }

    </PageContainer>
  );
};

export default AllModelsPage;
