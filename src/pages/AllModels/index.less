.models-page{
  position: absolute;
  opacity: 1;

  width: 100%;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  padding: 16px 16px 0px 16px;
}

.models-main-container{
  border-radius: 8px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  padding: 0 16px 16px 16px;

  background: #FFFFFF;

  z-index: 0;


  // 所有标签项的基础样式
  .ant-tabs-tab {
    font-family: PingFang SC;
    font-size: 16px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0px;

    font-variation-settings: "opsz" auto;
    /* 中性色/N7-主文字2 */
    color: #727375;

    &:hover {
      color: #0052D9;
    }
  }

  // 激活状态的标签样式
  .ant-tabs-tab-active {
    .ant-tabs-tab-btn {
      font-family: PingFang SC;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      letter-spacing: 0px;

      font-variation-settings: "opsz" auto;
      /* Brand 品牌/Brand7-Normal */
      color: #0052D9;
    }
  }
}

.models-no-cloth-container{
  width: 100%;
  height: 684px;
  border-radius: 8px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0;
  gap: 16px;
  align-self: stretch;

  .upload-cloth-text {
    height: 20px;
    opacity: 1;
    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0em;

    font-variation-settings: "opsz" auto;
    color: #727375;
    z-index: 0;
  }

  .upload-cloth-btn {
    width: 112px;
    height: 38px;
    border-radius: 8px;
    opacity: 1;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 8px 32px;
    gap: 8px;

    border: 1px solid #B9C7FA;

    z-index: 1;
  }
}

.models-form-container {
  display: flex;
  flex-direction: column;
  min-height: 500px;
}

.stick-bottom-pagination{
  position: sticky;
  bottom: 0;
  right: 0;

  width: 100%;
  height: 62px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 8px 16px;

  background: rgba(255, 255, 255, 1);

  box-sizing: border-box;
  /* 中性色/N3-边框、背板 */
  border-width: 0.5px 0px 0px 0px;
  border-style: solid;
  border-color: #E1E3EB;

  /* 背景模糊30 */
  backdrop-filter: blur(30px);
  z-index: 10;
}

.models-filter-row {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0;
  margin-top: 16px;
  margin-right: 16px;
  margin-bottom: 7px;
  gap: 16px;
  z-index: 0;
}

.models-image-card {
  position: relative;
  width: 248px;
  opacity: 1;

  display: flex;
  flex-direction: column;
  padding: 0px;
  gap: 4px;

  z-index: 0;
}

.models-img-cover {
  position: relative;
  width: 248px;
  height: 248px;
  border-radius: 8px;
  opacity: 1;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;

  box-sizing: border-box;
  border: 1px solid #E0E0E0;
  z-index: 0;
}

.models-img-cover img {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  object-fit: contain;
}

.models-img-count {
  position: absolute;
  right: 12px;
  bottom: 12px;

  height: 29px;
  border-radius: 4px;
  opacity: 1;

  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 6px 12px;
  gap: 8px;

  background: rgba(0, 0, 0, 0.6);

  font-family: "PingFang SC Medium";
  font-size: 12px;
  font-weight: 500;
  line-height: normal;
  text-align: right;
  letter-spacing: 0em;

  font-variation-settings: "opsz" auto;
  color: #FFFFFF;

  z-index: 0;
}

.models-image-card-name {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;
  font-variation-settings: "opsz" auto;
  color: #1A1B1D;
}

.models-image-card-info {
  height: 20px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  padding: 0px;

  font-family: PingFang SC;
  font-size: 12px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0em;

  font-variation-settings: "opsz" auto;
  color: #727375;
}

.models-traing-mask {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  opacity: 1;

  background: rgba(41, 41, 41, 0.3);

  display: flex;
  flex-direction: column;
  align-items: center;
}

.models-training-title {
  margin-top: 12px;

  font-family: "PingFang SC Medium";
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0em;

  font-variation-settings: "opsz" auto;
  color: #FFFFFF;
}

.models-cancel-training-user {
  margin-top: 8px;

  font-family: "PingFang SC";
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0em;

  font-variation-settings: "opsz" auto;
  color: #FFFFFF;
}

.models-training-finish-time {
  margin-top: 4px;

  font-family: "PingFang SC";
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0em;

  font-variation-settings: "opsz" auto;
  color: rgba(255, 255, 255, 0.6);
}

.button-container {
  position: absolute;
  width: 100%;
  bottom: 0;
  //水平居中
  margin: 0 auto;
  display: flex;
  transition: opacity 0.3s ease;

  border-radius: 0 0 12px 12px;

  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px;
  gap: 8px;
  background: rgba(255, 255, 255, 0.8);

  /* 背景模糊30 */
  backdrop-filter: blur(30px);
}

.check-info-button {
  width: 94px;
  height: 32px;
  border-radius: 8px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 6px 16px;
  gap: 8px;
  flex-grow: 1;
  background: #FFFFFF;
  box-sizing: border-box;
  border: 1px solid #B5C7FF;

  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  color: #0052D9;
}

.model-action-btn {
  width: 72px;
  height: 38px;
  border-radius: 8px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px 32px;
  gap: 8px;
  flex-grow: 1;

  background: #FFFFFF;

  box-sizing: border-box;
  /* 中性色/N3-边框、背板 */
  border: 1px solid #E1E3EB;

  z-index: 0;

  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 中性色/N7-主文字2 */
  color: #727375;
}

.go-create-button {
  width: 72px;
  height: 38px;
  border-radius: 8px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px 32px;
  gap: 8px;
  flex-grow: 1;

  /* 渐变色1-nomal */
  background: linear-gradient(90deg, #366EF4 0%, #9478EA 100%);

  z-index: 2;

  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 白色 */
  color: #FFFFFF;
}

.cloth-flag {
  opacity: 0;
  font-size:16px;
  position: absolute;
  right: 8px;
  top: 8px;
  cursor: pointer;
  z-index: 1;
}

.models-img-cover:not(:hover) .cloth-flag-marked {
  opacity: 1 !important;
  top: 8px !important;
}

.models-img-cover:hover {
  border: 2px solid #3E39F4;
  box-shadow: 9px 8px 33px 0px #D1D7E7;
  background: #D9E1FF;

  .cloth-flag:not(:hover) {
    opacity: 0.6;
  }

  .cloth-flag:hover {
    opacity: 1;
  }

  .button-container {
    opacity: 1;
  }
}

.models-image-card-info-block {
  display: flex;
}

.models-image-card-info-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.models-image-card-info-right {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  margin-left: auto;
  padding-top: 4px;
  width: 10%;
}

.models-top-toolbar{
  display: flex;
  align-items: flex-end; /* 对齐底部 */
  gap: 16px; /* Tabs 和过滤输入框之间的间距 */
  background-color: white; /* 背景颜色 */
  padding: 16px; /* 内边距 */

  .tabs-container {
    flex: 1; /* 占据剩余空间 */
  }

  /* 自定义 Tabs 样式 */
  .custom-tabs {
    margin-bottom: 0; /* 移除默认的底部间距 */
  }

  .custom-tabs .ant-tabs-nav {
    margin-bottom: 0; /* 移除默认的底部间距 */
  }

  .custom-tabs .ant-tabs-nav::before {
    border-bottom: 1px solid #d9d9d9; /* 自定义横线颜色 */
  }
}