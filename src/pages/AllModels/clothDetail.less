.cloth-detail-dlg {
    .ant-modal-content {
        max-width: 1160px;
        min-width: 800px;
        padding: 0;
        border-radius: 24px;
    }

    .cloth-detail-dlg-content {
        position: relative;
        padding: 24px 24px 0 24px;
        border-radius: 24px;
    }

    .cloth-form-container {
        margin-top: 8px;
        border-radius: 8px;
        opacity: 1;
        display: flex;
        flex-direction: column;
        gap: 16px;
        padding: 24px;
        background: #F5F6F9;
        overflow-y: auto;
    }

    .cloth-name-title {
        font-family: PingFang SC Medium;
        font-size: 24px;
        font-weight: 500;
        line-height: 32px;
        letter-spacing: 0px;
        color: #1A1B1D;
    }

    .cloth-detail-desc {
        margin-top: 12px;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        padding: 0px;
        gap: 16px;
    }

    .cloth-desc-item {
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0em;

        font-variation-settings: "opsz" auto;
        color: #727375;
    }

    .cloth-base-info {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    /* 表单行样式 */
    .form-row {
        opacity: 1;

        /* 自动布局 */
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 8px;

        z-index: 0;
    }

    /* 表单标签样式 */
    .form-label {
        margin-right: 10px;
        white-space: nowrap;

        font-family: "PingFang SC Medium";
        font-size: 14px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0em;

        font-variation-settings: "opsz" auto;
        color: #1A1B1D;
    }

    /* 控件样式，如 Input 和 Select */
    .form-control {
        flex-grow: 1;
        margin-right: 10px;
    }

    /* 上传部分样式 */
    .upload-section {
        display: flex;
        flex-direction: column;
        padding: 0px;
        gap: 8px;
        align-self: stretch;
        z-index: 3;
    }

    .upload-img-title {
        font-family: PingFang SC Medium;
        font-size: 14px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0em;

        font-variation-settings: "opsz" auto;
        color: #1A1B1D;
    }

    .upload-instructions {
        font-family: "PingFang SC";
        font-size: 12px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0em;

        font-variation-settings: "opsz" auto;
        color: #626264;
        z-index: 1;
    }

    .upload-image-title {
        font-family: "PingFang SC";
        font-size: 14px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0em;

        font-variation-settings: "opsz" auto;
        color: #1A1B1D;

        z-index: 0;
    }

    .upload-list {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 8px 16px;
    }

    .upload-item {
        width: 166px;

        text-align: center;
        opacity: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 8px;
        gap: 8px;

        box-sizing: border-box;
        border: 0.5px solid #D8D8D8;
        border-radius: 8px;

        z-index: 0;
    }

    .img-container {
        position: relative;
        width: 200px;
        height: 200px;
    }

    .upload-again {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
        border-radius: 139px;
        opacity: 1;
        background: rgba(0, 0, 0, 0.1);
        z-index: 0;
    }

    .img-container:hover .upload-again {
        transform: scale(1.2);
        /* 放大图标 */
    }

    .uploaded-img {
        width: 100%;
        height: 100%;
        object-fit: fill;
    }

    .custom-upload {
        width: 150px;
        height: 200px;
        border-radius: 8px;
        opacity: 1;

        /* 自动布局 */
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 16px 0px;
        gap: 12px;

        background: #FFFFFF;

        box-sizing: border-box;
        border: 1px dashed #D8D8D8;

        z-index: 0;
    }

    .card-title {
        height: 20px;
        opacity: 1;

        font-family: 'PingFang SC';
        font-size: 14px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0em;

        font-variation-settings: "opsz" auto;
        color: #1A1B1D;

        z-index: 0;
    }

    .upload-text {
        /* 点击/粘贴/拖拽图片至此 */
        height: 17px;
        opacity: 1;

        font-family: 'PingFang SC';
        font-size: 12px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0em;

        font-variation-settings: "opsz" auto;
        color: #A7A7A7;

        z-index: 1;
    }

    .example-thumbnail {
        height: 85px;
        opacity: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0px;
        gap: 4px;

        z-index: 1;
    }

    .card-exam-title {
        height: 17px;
        opacity: 1;

        font-family: 'PingFang SC';
        font-size: 12px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0em;

        font-variation-settings: "opsz" auto;
        color: #A7A7A7;

        z-index: 1;
    }

    /* 细节图上传样式 */
    .details-upload-section {
        margin-top: 20px;
    }

    .details-upload-list {
        display: flex;
        justify-content: flex-start;
        flex-wrap: nowrap;
        gap: 16px;
        margin: -8px;
    }

    .details-upload-item {
        flex: 1;
        margin: 8px;
        text-align: center;
    }

    // 吸底
    .fixed-footer {
        position: sticky;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 70px;
        padding: 10px 20px;
        display: flex;
        justify-content: flex-end;
        border-top: 1px solid #f0f0f0;
        z-index: 10;
        opacity: 1;
        align-items: center;
        padding: 8px 24px 24px 24px;
        gap: 16px;
        background: rgba(255, 255, 255, 0.8);
        box-sizing: border-box;
        border-width: 0.5px 0px 0px 0px;
        border-style: solid;
        border-color: #E1E3EB;
        backdrop-filter: blur(30px);

        .submit-button {
            margin-right: 0px;
            width: 212px;
            height: 38px;
            border-radius: 8px;
            opacity: 1;

            /* 自动布局 */
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 8px 32px;
            gap: 8px;

            /* 渐变色1-disabled */
            background: linear-gradient(90deg, #99C0FF 0%, #D4C9F7 100%);

            font-family: PingFang SC;
            font-size: 16px;
            font-weight: 500;
            line-height: normal;
            letter-spacing: 0em;

            font-variation-settings: "opsz" auto;
            color: #FFFFFF;
        }

        .submit-button:disabled {
            color: #FFFFFF;
            background: linear-gradient(0deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.6)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%);
        }

        .submit-button:enabled {
            background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
        }

        .cancel-button {
            width: 212px;
            height: 38px;
            border-radius: 8px;
            opacity: 1;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 8px 32px;
            gap: 8px;
            background: #FFFFFF;
            border: 1px solid #E1E3EB;
            z-index: 0;

            font-family: PingFang SC;
            font-size: 16px;
            font-weight: 500;
            line-height: normal;
            letter-spacing: 0em;

            font-variation-settings: "opsz" auto;
            /* 中性色/N6 */
            color: #969799;
        }

        .submit-button:not(:disabled):hover {
            background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
        }

        .submit-button:not(:disabled):active {
            background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
        }
    }

}