import React, { useEffect, useRef, useState } from 'react';
import ImgPreview from "@/components/ImgPreview";
import {
    MaterialInfoVO,
    updateMaterialById,
    getMaterialInfoById,
    ClothMaterialImg,
    checkMaterialNameExists, getClothDescByKey, getSexTypeDescByKey,
} from '@/services/MaterialInfoController';
import { MaterialModel, updateMaterialModel } from "@/services/MaterialModelController";
import { Modal, Input, Select, Button, message, Flex } from 'antd';
import './clothDetail.less';
import { EditOutlined } from '@ant-design/icons';
import { queryCreateImageCnt } from '@/services/CreativeController';

interface ClothDetailDlgProps {
    editAble?: boolean;
    model: MaterialModel;
    onCancel?: () => void;
    onConfirm?: () => void;
    materialType?: string;
    materialTypeTitle?: string;
}

interface EditableInputProps {
    label?: string; // 标签文本
    initialValue?: string; // 初始值
    onSubmit?: (value: string) => void; // 提交回调函数
    showEditButton?: boolean;
}

const ClothDetailDlg: React.FC<ClothDetailDlgProps> = ({ editAble = true, model, onCancel, onConfirm,materialType='cloth', materialTypeTitle='服装' }) => {
    const [showClothDetail, setShowClothDetail] = useState(true);
    const [materialInfo, setMaterialInfo] = useState<MaterialInfoVO | undefined>();
    const [clothName, setClothName] = useState<string | undefined>();
    const [nameError, setNameError] = useState('');
    const [clothingType, setClothingType] = useState<string>('OnePiece');
    const [previewVisible, setPreviewVisible] = useState(false);
    const [previewImage, setPreviewImage] = useState('');
    const [confirmEnabled, setConfirmEnabled] = useState(false);
    const [createImageCnt, setCreateImageCnt] = useState(0);

    const [originalClothName, setOriginalClothName] = useState<string>('');

    useEffect(() => {
        //加载material info
        let mid = (model && model.clothLoraTrainDetail && model.clothLoraTrainDetail.originalMaterialId) || (model && model.materialInfoId);
        if (mid) {
            getMaterialInfoById(mid).then(res => {
                if (res) {
                    setShowClothDetail(true);
                    setMaterialInfo(res);
                    setClothName(res.name);
                    setClothingType(res.subType);
                    setOriginalClothName(res.name);

                } else {
                    message.error('查询服装详情失败');
                    setShowClothDetail(false);
                }
            });
        } else {
            console.log('当前衣服模型没有train detail或train detail里的originalMaterialId为空，历史脏数据，忽略');
        }

        queryCreateImageCnt(model.id).then(res=>{
            if(res !== null){
                setCreateImageCnt(res || 0);
            }
        })
    }, []);

    useEffect(() => {

        if (clothName && clothName !== originalClothName && nameError === '') {
            setConfirmEnabled(true);
        } else {
            setConfirmEnabled(false);
        }

    }, [clothName, originalClothName, nameError]);

    function onInputClothName(clothName: string) {
        setClothName(clothName);
        if (clothName) {

            // 允许的字符：中文、英文大小写、数字、连字符、下划线
            let regex = /^[\u4e00-\u9fa5a-zA-Z0-9-_]+$/;
            let valid = regex.test(clothName);

            if (!valid) {
                setNameError('名称只允许由20个以内的中文、英文、数字、连字符、下划线字符组成');
            } else {
                if (clothName !== originalClothName) {
                    checkMaterialNameExists(clothName).then(exists => {
                        if (exists && clothName !== originalClothName) {
                            setNameError('同名的素材已经存在');
                        } else {
                            setNameError('');
                            submit2Server(clothName);
                        }
                    });
                }
            }
        }
    }

    const handlePreviewUrl = (url: string) => {
        setPreviewImage(url);
        setPreviewVisible(true);
    };

    const handleCancelPreview = () => {
        setPreviewVisible(false);
    };

    //更新模型和素材数据的名称
    function submit2Server(value: string) {
        updateMaterialModel({
            id: model.id,
            name: value
        }).then(res => {
            if (res) {
                if (materialInfo) {
                    updateMaterialById({
                        id: materialInfo?.id,
                        name: value
                    });
                }
                message.info('操作成功');
            } else {
                message.error('操作失败');
            }
        });
    }

    const uploadCardList = (imgList: ClothMaterialImg[]) => {
        console.log('uploadCardList imgList', imgList);

        if (!imgList) return <div></div>;
        return imgList.map((img, index) => {
            return (
              <div className="img-container">
                  <img src={img.imgUrl} alt="Uploaded"
                       onClick={() => handlePreviewUrl(img.imgUrl)}
                       style={{ cursor: 'pointer', width: '100%', height: '100%', objectFit: 'contain' }} />
              </div>
            );
        });
    };

    const uploadCardListWithUrls = (imgList: string[]) => {
        console.log('uploadCardListWithUrls imgList', imgList);

        if (!imgList) return <div></div>;
        return imgList.map((img, index) => {
            return (
              <div className="img-container">
                  <img src={img} alt="Uploaded"
                       onClick={() => handlePreviewUrl(img)}
                       style={{ cursor: 'pointer', width: '100%', height: '100%', objectFit: 'contain' }} />
              </div>
            );
        });
    };

    const getUpperDetailImgs = (imgList: ClothMaterialImg[]) => {
        if (!imgList) {
            return [];
        }
        return imgList.filter(img => img.viewTags.includes('upper'));
    }

    const getLowerDetailImgs = (imgList: ClothMaterialImg[]) => {
        if (!imgList) {
            return [];
        }
        return imgList.filter(img => img.viewTags.includes('lower'));
    }

    const EditableInput: React.FC<EditableInputProps> = ({
                                                             label,
                                                             initialValue,
                                                             onSubmit,
                                                             showEditButton
                                                         }) => {
        // 状态管理
        const [isEditing, setIsEditing] = useState(false); // 是否处于编辑状态
        const [inputValue, setInputValue] = useState(initialValue); // 输入框内容
        const [originalValue, setOriginalValue] = useState(initialValue); // 原始内容

        // 处理修改按钮点击事件
        const handleEditClick = () => {
            setIsEditing(true); // 进入编辑状态
        };

        // 处理输入框内容变化
        const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            setInputValue(e.target.value); // 更新输入框内容
        };

        // 自动提交内容
        useEffect(() => {
            if (!isEditing && inputValue !== originalValue) {
                if (onSubmit) {
                    onSubmit(inputValue || ''); // 调用提交回调函数
                }
                setOriginalValue(inputValue); // 更新原始内容
            }
        }, [isEditing, inputValue, originalValue, onSubmit]);

        return (
          <div style={{ display: 'flex', alignItems: 'center'}}>
              <label className={'form-label'}>{label}</label>
              <Input
                value={inputValue}
                onChange={handleInputChange}
                onBlur={() => setIsEditing(false)} // 失去焦点时退出编辑状态
                onPressEnter={() => setIsEditing(false)} // 按下回车键时退出编辑状态
                autoFocus // 自动聚焦
                style={{ width: 433 }}
                disabled={!isEditing}
              />

              {showEditButton &&
                  <Button
                      type="text"
                      icon={<EditOutlined />}
                      onClick={handleEditClick}
                      style={{ border: 'none', boxShadow: 'none' }}
                  />
              }

          </div>
        );
    };

    return (
        <Modal
            open={showClothDetail}
            keyboard={true}
            centered={true}
            width={'auto'}
            onCancel={onCancel}
            onOk={onConfirm}
            closable={false}
            className='cloth-detail-dlg'
            mask={true}
            styles={{
                mask: {
                    backgroundColor: 'rgba(0,0,0,0.4)'
                }
            }}
            footer={null}
        >
            <div className='cloth-detail-dlg-content'>
                <div className='cloth-name-title'>{materialInfo?.name}</div>
                <div className='cloth-detail-desc'>
                    <div className='cloth-desc-item'>创建时间：{materialInfo?.createTime}</div>
                    {model?.operatorNick && <div className='cloth-desc-item'>创建者：{model?.operatorNick}</div>}
                    {materialType === 'cloth' && <div className='cloth-desc-item'>生成作品：{createImageCnt || 0}张</div>}
                    {model.status === 'DISABLED' && model.extInfo && model.extInfo['disableReason'] &&
                        <div className="cloth-desc-item">取消学习原因：{model.extInfo['disableReason']}</div>
                    }
                </div>
                <div className="cloth-form-container">
                    <div className='cloth-base-info'>

                        {clothName &&
                          <div className="form-row">
                              <EditableInput
                                label={`${materialTypeTitle}名称：`}
                                initialValue={clothName}
                                showEditButton={editAble}
                                onSubmit={onInputClothName}
                              />
                          </div>
                        }

                        {materialType === 'cloth' && materialInfo?.subType &&
                          <div className="form-row">
                              <div className="form-label">{materialTypeTitle}种类:</div>
                              <span className={'text14 font-pf color-1a'}>{getClothDescByKey(materialInfo?.subType)}</span>
                          </div>
                        }

                        {materialType === 'face' && materialInfo?.subType &&
                          <div className="form-row">
                              <div className="form-label">{materialTypeTitle}性别:</div>
                              <span className={'text14 font-pf color-1a'}>{getSexTypeDescByKey(materialInfo?.subType)}</span>
                          </div>
                        }

                        {materialType === 'scene' && materialInfo?.subType &&
                          <Flex gap={24} align={'center'}>
                              <div className="form-row">
                                  <div className="form-label">是否专属:</div>
                                  <span className={'text14 font-pf color-1a'}>{materialInfo?.extInfo?.exclusive ? '是' : '否'}</span>
                              </div>

                              <div className="form-row">
                                  <div className="form-label">是否有水印:</div>
                                  <span className={'text14 font-pf color-1a'}>{materialInfo?.extInfo?.hasWatermark ? '是' : '否'}</span>
                              </div>

                              <div className="form-row">
                                  <div className="form-label">适用性别:</div>
                                  <span className={'text14 font-pf color-1a'}>{getSexTypeDescByKey(materialInfo?.subType)}</span>
                              </div>
                          </Flex>

                        }

                    </div>
                    {nameError && <div className="cloth-error-message">{nameError}</div>}

                    {materialType === 'cloth' &&
                      <>
                          <div className="upload-section">
                              <div className="upload-img-title">全身图</div>
                              <div className="upload-list">
                                  {uploadCardList(materialInfo?.materialDetail?.fullShotImgList || [])}
                              </div>
                          </div>
                          {getUpperDetailImgs(materialInfo?.materialDetail?.detailShotImgList || [])?.length > 0 && (
                            <div className="upload-section">
                                <div className="upload-img-title">上半身细节图</div>
                                <div
                                  className="upload-list">{uploadCardList(getUpperDetailImgs(materialInfo?.materialDetail?.detailShotImgList || []))}</div>
                            </div>
                          )}
                          {getLowerDetailImgs(materialInfo?.materialDetail?.detailShotImgList || [])?.length > 0 && (
                            <div className="upload-section">
                                <div className="upload-img-title">下半身细节图</div>
                                <div
                                  className="upload-list">{uploadCardList(getLowerDetailImgs(materialInfo?.materialDetail?.detailShotImgList || []))}</div>
                            </div>
                          )}
                          {materialInfo?.materialDetail?.moreImgList && materialInfo?.materialDetail?.moreImgList?.length > 0 && (
                            <div className="upload-section">
                                <div className="upload-img-title">补充姿势图</div>
                                <div
                                  className="upload-list">{uploadCardList(materialInfo?.materialDetail?.moreImgList || [])}</div>
                            </div>
                          )}
                      </>
                    }

                    {(materialType === 'face' || materialType === 'scene') && materialInfo?.materialDetail &&
                      <div className="upload-section">
                          <div className="upload-img-title">上传素材</div>
                          <div className="upload-list">
                              {uploadCardListWithUrls(materialInfo?.materialDetail?.imgUrls || [])}
                          </div>
                      </div>
                    }

                </div>
            </div>

            {/* 图片预览 */}
            <ImgPreview previewVisible={previewVisible}
                handleCancelPreview={handleCancelPreview}
                previewImage={previewImage}
                showTools={false}
                enableGou={false}
                enableCha={false}
                needWatermark={false}
            />
        </Modal>
    );
};

export default ClothDetailDlg;