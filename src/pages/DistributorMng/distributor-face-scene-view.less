.distributor-view-main-container{
  border-radius: 8px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  padding: 0 16px 0 16px;

  background: #FFFFFF;

  z-index: 0;

  // 所有标签项的基础样式
  .ant-tabs-tab {
    font-family: PingFang SC;
    font-size: 16px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0px;

    font-variation-settings: "opsz" auto;
    /* 中性色/N7-主文字2 */
    color: #727375;

    &:hover {
      color: #0052D9;
    }
  }

  // 激活状态的标签样式
  .ant-tabs-tab-active {
    .ant-tabs-tab-btn {
      font-family: PingFang SC;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      letter-spacing: 0px;

      font-variation-settings: "opsz" auto;
      /* Brand 品牌/Brand7-Normal */
      color: #0052D9;
    }
  }
}