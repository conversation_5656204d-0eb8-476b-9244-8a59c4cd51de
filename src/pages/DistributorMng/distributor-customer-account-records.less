.user-center-container {
  padding: 16px;
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
}

.user-base-info {
  width: 214px;
  height: 66px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 0px;
  gap: 16px;
  z-index: 0;
}

.user-base-right {
  width: auto;
  opacity: 1;
  display: flex;
  flex-direction: column;
  padding: 0px;
  gap: 8px;
  z-index: 1;
}

.user-base-right-account-container {
  width: 44px;
  height: 22px;
  border-radius: 4px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  padding: 2px 4px;
  background: #F5F6F9;
  box-sizing: border-box;
  border: 0.5px solid #D8D8D8;
  z-index: 1;
}

.user-login-account {
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 中性色/N8-主文字 */
  color: #1A1B1D;
}

.user-right-account-container{
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  gap: 4px;
}

.user-account-type {
  height: 22px;
  border-radius: 4px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  padding: 2px 4px;
  background: #F5F6F9;
  box-sizing: border-box;
  border: 0.5px solid #D8D8D8;
  z-index: 1;
}

.master-account-belongs {
  height: 20px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  padding: 0px;
  z-index: 1;
  white-space: nowrap;

  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 中性色/N6 */
  color: #969799;

}


.user-center-table {

  //表头
  .ant-table-thead > tr > th {
    background: #F2F6FC;
    text-align: left;

    font-weight: normal;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: 0;
    color: #909399;
  }

  //单元格
  .ant-table-tbody > tr > td {
    height: 61.45px;
    padding: 12px 16px;
    gap: 10px;
    background: #FFFFFF;
    border-bottom: 1px solid #DCDFE6; /* 使用边框代替阴影 */
    box-shadow: none;

    font-size: 14px;
    font-weight: normal;
    line-height: 24px;
    letter-spacing: 0px;
    color: #606266;
  }

  /* 设置操作列的标题右对齐 */
  .ant-table-thead > tr > th.action-column {
    text-align: right;
  }

  .ant-table-tbody > tr > td.action-column {
    text-align: right;
  }
}