import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Button, Checkbox,
  Descriptions,
  Form,
  Input,
  message,
  Modal,
  Pagination,
  Radio,
  Select,
  Table,
  Tooltip,
} from 'antd';

import { createUserProfile, ROLE_TYPE, UserVO } from '@/services/UserController';
import {
  createDistributorCustomer,
  deleteDistributorCustomer,
  DistributorMerchantVO,
  getDistributorCustomerByPage,
  queryAllDistributorOperators,
  queryAllDistributorSales,
  updateDistributorCustomer,
} from '@/services/DistributorController';
import './distributor-customers.less';
import { formatDateToCompactString, getUserInfo, isProdEnv } from '@/utils/utils';
import TopupModal from '@/pages/Topup/Topup';
import MemoEditor from '@/components/MemoEditor';
import { CreatePromotionCodeModal, PromotionCodeListModal } from '@/components/RegisterPromotion';
import { queryCodeList } from '@/services/CodeController';
import IconFont from '@/components/IconFont';
import { getPricePlan, PricePlan } from '@/services/OrderInfoController';

interface UserModalConfig {
  title: string,
  uid?: number | undefined,
  // eslint-disable-next-line @typescript-eslint/ban-types
  onOk: Function,
  // eslint-disable-next-line @typescript-eslint/ban-types
  onCancel: Function
}

const DistributorCustomersPage: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [data, setData] = useState<Array<DistributorMerchantVO>>([]);
  const [operatorList, setOperatorList] = useState<UserVO[]>([]);
  const [salesList, setSalesList] = useState<UserVO[]>([]);
  const [promotionCodeList, setPromotionCodeList] = useState<any[]>([]); // 添加推广码列表状态
  const [selectedPromotionCode, setSelectedPromotionCode] = useState<string>(); // 添加选中的推广码状态

  const [showUserModal, setShowUserModal] = useState(false);
  const [showUserModalConfig, setShowUserModalConfig] = useState<UserModalConfig>();

  // 推广注册相关状态
  const [showPromotionListModal, setShowPromotionListModal] = useState(false);

  const [userForm] = Form.useForm();
  const [selectedOperatorId, setSelectedOperatorId] = useState<number>();
  const [selectedSalesId, setSelectedSalesId] = useState<number>();

  const [showTopupModal2CollectMoney, setShowTopupModal2CollectMoney] = useState(false);
  const [currentUser, setCurrentUser] = useState<UserVO>();

  //99元体验包
  const [newbiePlan, setNewbiePlan] = useState<PricePlan>();

  const userInfo = getUserInfo();

  //渠道管理员
  const distAdmin = userInfo && userInfo.customRole === 'CHANNEL_ADMIN';

  // 管理员或销售
  const adminOrSales = userInfo && (userInfo.customRole === 'CHANNEL_ADMIN' || userInfo.customRole === 'SALES_MEMBER' || userInfo.customRole === 'SECOND_CHANNEL_ADMIN');

  //  管理员或运营
  const adminOrOps = userInfo && (userInfo.customRole === 'CHANNEL_ADMIN' || userInfo.customRole === 'OPS_MEMBER' || userInfo.customRole === 'SECOND_CHANNEL_ADMIN');

  // 搜索框
  const [customerNameOrCorpName, setCustomerNameOrCorpName] = useState<string>();

  //15天未使用
  const [show15DaysNotUsed, setShow15DaysNotUsed] = useState(false);
  //muse点不足500
  const [customerMusePointLessThan500, setCustomerMusePointLessThan500] = useState(false);

  const columns = [
    {
      title: '客户ID',
      dataIndex: 'id',
      key: 'id',
      render: (text) => <span style={{ color: '#409EFF' }}>{text}</span>,
    },
    {
      title: '企业名称',
      dataIndex: 'corpName',
      key: 'corpName',
    },
    {
      title: '管理员昵称',
      dataIndex: 'nickName',
      key: 'nickName',
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
      key: 'mobile',
    },
    {
      title: '状态',
      dataIndex: 'statusDesc',
      key: 'statusDesc',
      render: (text: ROLE_TYPE, record) => <span
        style={{ color: record.status !== 'ENABLED' ? 'red' : '' }}>{text}</span>,
    },
    {
      title: '缪斯点余额',
      dataIndex: 'imagePoint',
      key: 'imagePoint',
      render: (text: ROLE_TYPE) => <span style={{ color: 'red', textAlign: 'right' }}>{text ? text : 0}</span>,
    },
    {
      title: '最近访问',
      dataIndex: 'lastVisitDate',
      key: 'lastVisitDate',
      render: (_, record) => {
        if (record?.lastVisitDate) {
          return record.lastVisitDate;
        } else if (record?.lastLoginTime) {
          return formatDateToCompactString(record.lastLoginTime);
        } else {
          return '';
        }
      }
    },
    {
      title: <Tooltip title={'客户通过邀请人的邀请码完成注册'} style={{cursor: 'pointer'}}>邀请人<IconFont type={'icon-yiwen1'}/></Tooltip>,
      key: 'inviteRegister',
      width: 100,
      render: (_, record) => {
        if (!record.inviteRegister) {
          return null;
        }
        return <Tooltip title={record.inviteRegister}><span>{record.inviteRegister}</span></Tooltip>;
      },
      ellipsis: {
        showTitle: false,
      },
    },
    {
      title: '销售',
      key: 'sales',
      render: (_: any, record: DistributorMerchantVO) => (
        record.distributorSalesNickName
          ? <span>{record.distributorSalesNickName}</span>
          : <span></span>
      )
    },
    {
      title: '运营',
      key: 'operators',
      render: (_: any, record: DistributorMerchantVO) => (
        record.distributorOperatorNickName
          ? <span>{record.distributorOperatorNickName}</span>
          : <span></span>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '备注',
      dataIndex: 'memo',
      key: 'memo',
      render: (_, record: DistributorMerchantVO) => {
        const saveMemo = async (userId: number, memo: string) => {
          const res = await createUserProfile({ uid: userId, profileKey: 'memo', profileVal: memo });
          if (res) {
            return Promise.resolve();
          }
          return Promise.reject();
        };

        return (
          <MemoEditor
            memo={record.profiles?.memo?.profileVal}
            userId={record.id}
            onSave={saveMemo}
            onSuccess={() => fetchData(currentPage, pageSize)}
            lastModified={record.profiles?.memo?.modifyTime}
          />
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      className: 'action-column',
      render: (text: any, record: DistributorMerchantVO) => (
        <span className="action-buttons">
          {adminOrSales && <>
            <a style={{ color: 'red' }} onClick={() => {
              setCurrentUser(record);
              setShowTopupModal2CollectMoney(true);
            }}>99元体验包</a>
          </>
          }

          {adminOrSales && <>
            <a onClick={() => showUpdateUserModal(record)}>编辑</a>
            <a onClick={() => showDeleteUserModal(record)}>删除</a>
          </>
          }
        </span>
      ),
    },
  ];

  // 修改 fetchData 函数，添加推广码过滤参数
  async function fetchData(pageNum: number, pageSize: number) {

    getDistributorCustomerByPage({
      pageNum: pageNum,
      pageSize: pageSize,
      orderBy: 'id desc',
      needCustomerImagePoint: true,
      distributorOperatorUserId: selectedOperatorId,
      distributorSalesUserId: selectedSalesId,
      customerLike: customerNameOrCorpName || null,
      onlyShow15DaysNotUsed: show15DaysNotUsed,
      customerMusePointLessThan500: customerMusePointLessThan500,
      promotionCode: selectedPromotionCode || null // 添加推广码过滤参数

    }).then(res => {
      console.log('fetch data list response:', res);
      if (res) {

        //@ts-ignore 追加一个key字段，避免警告
        let newData = res.list.map(item => ({ ...item, key: item.id }));
        console.log('newData:', newData);

        setData(newData);

        console.log('data:', data);

        // @ts-ignore
        setTotalCount(res.totalCount);

      }

    });
  }

  async function refreshData() {
    setCurrentPage(1);
    fetchData(1, pageSize);
  }

  // 在组件顶部定义缓存键名
  const MUSE_POINT_REMINDER_LAST_SHOWN = 'MUSE_POINT_REMINDER_LAST_SHOWN';
  const FOUR_HOURS_MS = 4 * 60 * 60 * 1000; // 4小时毫秒数

  function alertNeedTopupCustomers() {
    // 获取上次提醒时间
    const lastShown = JSON.parse(localStorage.getItem(MUSE_POINT_REMINDER_LAST_SHOWN) || 'null');
    const shouldShowReminder = !lastShown || (Date.now() - lastShown.timestamp) > FOUR_HOURS_MS;

    if (shouldShowReminder) {
      getDistributorCustomerByPage({
        pageNum: 1,
        pageSize: 10,
        customerMusePointLessThan500: true,
      }).then(res => {
        if (res && res?.totalCount && res?.totalCount > 0) {
          Modal.confirm({
            title: '客户待续约',
            content: `有${res?.totalCount}个客户剩余不足500缪斯点，需要续约，是否查看？`,
            okText: '查看',
            cancelText: '暂时不用',
            centered: true,
            onOk: () => {
              // 更新最后显示时间
              localStorage.setItem(MUSE_POINT_REMINDER_LAST_SHOWN,
                JSON.stringify({ timestamp: Date.now() }));
              setCustomerMusePointLessThan500(true);
            },
            onCancel: () => {
              // 即使用户取消查看也更新最后显示时间
              localStorage.setItem(MUSE_POINT_REMINDER_LAST_SHOWN,
                JSON.stringify({ timestamp: Date.now() }));
            },
          });
        }
      });
    }
  }

  // 修改 useEffect 中的代码
  useEffect(() => {
    queryAllDistributorSales({}).then(res => {
      if (res) {
        setSalesList(res);
      }
    });
    queryAllDistributorOperators({}).then(res => {
      if (res) {
        setOperatorList(res);
      }
    });

    // 查询推广注册码列表
    queryCodeList({
      codeType: 'registerPromotion',
      orderBy: 'id desc'
    }).then(res => {
      if (res && Array.isArray(res)) {
        setPromotionCodeList(res);
      } else {
        setPromotionCodeList([]);
      }
    });

    getPricePlan().then(res => {
      if (res && Array.isArray(res)) {
        setNewbiePlan(res.find(item => item.code === 'PLAN_NEWBIE'));

      } else {
        console.log('查询支付配置为空');
      }
    });

    if (isProdEnv()) {
      //客户待续约提醒
      alertNeedTopupCustomers();
    }
  }, []);

  useEffect(() => {
    refreshData();
  }, [selectedOperatorId, customerNameOrCorpName, show15DaysNotUsed, customerMusePointLessThan500, selectedSalesId, selectedPromotionCode]);

  // 删除用户弹窗确认
  function showDeleteUserModal(u: UserVO) {
    Modal.confirm({
      title: '确认删除',
      content: '删除后不可恢复，请谨慎操作',
      okText: '确认删除',
      cancelText: '取消',
      okType: 'danger',
      icon: null,
      onOk() {
        deleteDistributorCustomer(u.id).then(res => {
          if (res) {
            message.success('操作成功');
            refreshData();
          }
        });
      },
    });
  }

  const handlePageChange = (page) => {
    setCurrentPage(page);
    fetchData(page, pageSize);
  };

  const handlePageSizeChange = (current, size) => {
    setPageSize(size);
    setCurrentPage(1); // 重置到第一页
    fetchData(1, size);
  };

  const handleCancel = () => {
    setShowUserModal(false);
    userForm.resetFields();
  };

  const handleNewUser = () => {

    userForm.validateFields()
      .then(values => {
        // 在这里处理表单提交
        console.log('表单值: ', values);
        createDistributorCustomer(values).then(res => {
          console.log('res:', res);
          if (res) {
            message.success({ content: '添加成功', duration: 5 });
            refreshData();
          } else {
            message.error('操作失败');
          }
        });

        setShowUserModal(false);
        userForm.resetFields();
      })
      .catch(info => {
        console.log('验证失败:', info);
      });
  };

  const handleUpdateUser = (uid: number) => {
    userForm.validateFields()
      .then(values => {

        values.userId = uid;

        console.log('表单值: ', values);
        updateDistributorCustomer(values).then(res => {
          console.log('res:', res);
          if (res) {
            message.success('操作成功');
            refreshData();
          }
        });

        setShowUserModal(false);
        userForm.resetFields();
      })
      .catch(info => {
        console.log('验证失败:', info);
      });
  };

  const showNewUserModal = () => {
    setShowUserModal(true);
    setShowUserModalConfig({
      title: '添加客户',
      uid: undefined,
      onOk: handleNewUser,
      onCancel: handleCancel,
    });
    userForm.resetFields();
    userForm.setFieldsValue({ userType: 'SUB', needInitMusePoints: 'N', relatedSalesId: getUserInfo()?.id || null });
  };

  const showUpdateUserModal = (u: DistributorMerchantVO) => {
    setShowUserModal(true);
    setShowUserModalConfig({
      title: '编辑',
      onOk: () => handleUpdateUser(u.id),
      onCancel: handleCancel,
      uid: u.id,
    });

    userForm.setFieldsValue(u);
    userForm.setFieldsValue({ relatedOperatorId: u.distributorOperatorUserId, relatedSalesId: u.distributorSalesUserId });
  };

  return (
    <PageContainer>
      <div className={'distributor-customers-main'}>
        <div className="distributor-customers-top-bar">
          {adminOrSales &&
            <Button type="primary" className="distributor-customers-add-user-button"
              onClick={() => showNewUserModal()}>
              添加客户
            </Button>
          }

          {distAdmin &&
            <>
              <Button
                style={{ marginLeft: 8 }}
                onClick={() => setShowPromotionListModal(true)}
              >
                推广注册
              </Button>
              <Select
                showSearch
                allowClear
                style={{ width: 150, marginLeft: 8 }}
                placeholder="按推广码筛选"
                optionFilterProp="label"
                onChange={value => setSelectedPromotionCode(value)}
                options={
                  promotionCodeList.map(promotionCode => ({
                    value: promotionCode.code,
                    label: `${promotionCode?.codeInfo?.nick}(${promotionCode?.code})`,
                  }))
                }
              />
            </>
          }

          {adminOrOps &&
            <Select showSearch
              allowClear
              style={{ width: 150 }}
              placeholder="选择运营"
              optionFilterProp="label"
              onChange={value => setSelectedOperatorId(value)}
              options={
                operatorList.map(operator => ({
                  value: operator.id,
                  label: operator.nickName,
                }))
              }
            />
          }

          {adminOrOps &&
            <Select showSearch
              allowClear
              style={{ width: 150 }}
              placeholder="选择销售"
              optionFilterProp="label"
              onChange={value => setSelectedSalesId(value)}
              options={
                salesList.map(operator => ({
                  value: operator.id,
                  label: operator.nickName,
                }))
              }
            />
          }

          <Input placeholder="搜索" style={{ width: 150 }} allowClear onChange={e => setCustomerNameOrCorpName(e.target.value)} />

          <Checkbox checked={show15DaysNotUsed}
            onChange={e => { setShow15DaysNotUsed(e.target.checked) }}>客户15天未访问
          </Checkbox>

          <Tooltip title={'充值过3999以上且当前余额小于500'}>
            <Checkbox checked={customerMusePointLessThan500}
              onChange={e => { setCustomerMusePointLessThan500(e.target.checked) }}>客户待续约
            </Checkbox>
          </Tooltip>

        </div>

        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
          className="distributor-customers-user-table"
        />
        <div className="distributor-customers-pagination-container">
          <span>共 {totalCount} 条</span>
          <Pagination
            className="distributor-customers-pagination"
            current={currentPage}
            total={totalCount}
            pageSize={pageSize}
            onChange={handlePageChange}
            showSizeChanger
            onShowSizeChange={handlePageSizeChange}
            pageSizeOptions={['10', '20', '50', '100']}
          />
          <div className="distributor-customers-goto-page">
            <span>前往</span>
            <Input
              className="distributor-customers-goto-input"
              value={currentPage}
              onChange={(e) => setCurrentPage(Number(e.target.value))}
              onPressEnter={(e) => handlePageChange(Number(e.currentTarget.value))}
            />
            <span>页</span>
          </div>
        </div>
      </div>

      {/*添加用户和编辑用户的弹窗*/}
      <Modal
        className="distributor-customers-user-modal"
        title={showUserModalConfig?.title}
        open={showUserModal}
        onOk={() => showUserModalConfig?.onOk()}
        onCancel={handleCancel}
        okText="确定"
        cancelText="取消"
        width={575}
        maskClosable={false}
      >
        <Form form={userForm} layout="horizontal" name="user_form">
          <Form.Item
            name="corpName"
            label="企业名全称"
            rules={[{ required: true, message: '请输入企业名全称!' }]}
            style={{ padding: '0 12px' }}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            colon={false}
          >
            <Input placeholder="请输入" autoFocus style={{ width: 270 }} />
          </Form.Item>
          <Form.Item
            name="nickName"
            label="管理员昵称"
            rules={[{ required: true, message: '请输入管理员昵称!' }]}
            style={{ padding: '0 12px' }}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            colon={false}
          >
            <Input placeholder="请输入" autoFocus style={{ width: 270 }} />
          </Form.Item>
          <Form.Item
            name="mobile"
            label="手机号"
            rules={[{ required: true, message: '请输入手机号!' }]}
            style={{ padding: '0 12px' }}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            colon={false}
          >
            <Input placeholder="请输入" style={{ width: 270 }} maxLength={11} />
          </Form.Item>

          <Form.Item
            name="relatedOperatorId"
            label="指定运营人员"
            rules={[{ required: false, message: '请选择运营人员!' }]}
            style={{ padding: '0 12px' }}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            colon={false}
          >
            <Select allowClear placeholder="请选择" style={{ width: 270 }}>
              {operatorList.map((operator) => (
                <Select.Option value={operator.id}>
                  {operator.nickName}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="relatedSalesId"
            label="指定销售人员"
            rules={[{ required: true, message: '请选择销售人员!' }]}
            style={{ padding: '0 12px' }}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            colon={false}
          >
            <Select allowClear placeholder="请选择" style={{ width: 270 }}>
              {salesList.map((sales) => (
                <Select.Option value={sales.id}>
                  {sales.nickName}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          {!showUserModalConfig?.uid &&
            <Form.Item
              label={(
                <Tooltip title="选择是，将赠送120缪斯点，客户将不再能充值99元体验套餐">
                  初始化
                </Tooltip>
              )}
              name="needInitMusePoints"
              style={{ padding: '0 12px' }}
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 18 }}
              colon={false}
            >
              <Radio.Group options={[
                { label: '否', value: 'N' },
                { label: '是', value: 'Y' }
              ]} optionType="button" buttonStyle={'solid'} />
            </Form.Item>
          }

        </Form>
      </Modal>

      {showTopupModal2CollectMoney && (
        <TopupModal
          visible={showTopupModal2CollectMoney}
          onClose={() => setShowTopupModal2CollectMoney(false)}
          onPaySuccessWithOrderNo={(orderNo: string) => {
            setShowTopupModal2CollectMoney(false);
            fetchData(currentPage, pageSize);
            message.success('体验包领取成功');
          }}
          backroleAgentOrder={true}
          payMasterId={currentUser?.id}
          customOrderPlan={newbiePlan}
        />
      )}

      {/* 推广注册码列表弹窗，传入销售列表以支持创建功能 */}
      {showPromotionListModal &&
        <PromotionCodeListModal
          visible={showPromotionListModal}
          onCancel={() => setShowPromotionListModal(false)}
          salesList={salesList}
          onSuccess={() => {
            message.success('操作成功');
          }}
        />
      }

    </PageContainer>
  );
};

export default DistributorCustomersPage;
