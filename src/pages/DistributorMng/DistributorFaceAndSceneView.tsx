import {<PERSON><PERSON>ontainer} from '@ant-design/pro-components';
import React, {useEffect, useState} from 'react';
import {
    Flex,
    Image,
    Input,
    message,
    Pagination,
    Radio,
    Tabs,
    Tooltip
} from 'antd';
import {
    ElementConfigWithBlobs,
    getElementTypes,
    queryPubViewByPage, SceneType,
} from '@/services/ElementController';
import '@/pages/Operate/Face/index.css';
import './distributor-face-scene-view.less';
import NewLabel from '@/components/new/NewLabel';
import ClothDetailDlg from "@/pages/AllModels/ClothDetailDlg";
import {getMaterialModelById, MaterialModelWithBlogs} from "@/services/MaterialModelController";

export const FaceBaseType = ['male-model', 'female-model', 'child-model'];

const DistributorFaceSceneView: React.FC = () => {
    // @ts-ignore
    const [datas, setDatas] = useState<Array<ElementConfigWithBlobs>>([]);
    const [searchId, setSearchId] = useState<number | undefined | null>();
    const [searchKey, setSearchKey] = useState<string | undefined>();

    const [selectType, setSelectType] = useState<string>('all');
    const [selectSubType, setSelectSubType] = useState<string>('all');
    const [showOpenScope, setShowOpenScope] = useState('all');
    const [showTesting, setShowTesting] = useState('PROD');
    const [showSwapType, setShowSwapType] = useState<string>('all');

    const [total, setTotal] = useState(0);
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(20);

    //图片上传相关
    const [previewOpen, setPreviewOpen] = useState(false);
    const [previewImage, setPreviewImage] = useState('');
    const [faceConfig, setFaceConfig] = useState<Array<any>>([]);

    //显示详情
    const [showClothDetail, setShowClothDetail] = useState<boolean>(false);
    const [selectedModel, setSelectedModel] = useState<MaterialModelWithBlogs>();
    const [materialType, setMaterialType] = useState<string>('face');
    const [materialTypeTitle, setMaterialTypeTitle] = useState<string>('模特');

    //场景列表，scene type code array
    const [sceneTypeList, setSceneTypeList] = useState<SceneType[]>([]);

    async function fetchData() {
        const query = {configKey: materialType.toUpperCase(), openScope: showOpenScope === 'all' ? null: showOpenScope,
            status: 'PROD',
            genderType: selectType === 'all' ? null: selectType, type: selectSubType === 'all' ? null: selectSubType,
            id: searchId, name: searchKey?.trim(), pageNum: page, pageSize,
            belong: null,
            orderBy: 'is_new desc,`order`,modify_time desc'};

        if (materialType === 'face') {
            if (showSwapType !== 'all'){
                query['swapType'] = showSwapType;
            }
        } else if (materialType ==='scene'){
            query['isLora'] = showSwapType === 'all' ? null : showSwapType === 'style';
        }

        queryPubViewByPage(query).then(res=>{
            if(!res) return;
            setTotal(res.totalCount || 0);

            res.list?.forEach(item => {
                item.type = [...new Set(item.type)];
            });

            setDatas(res.list || []);
        });
    }

    const handlePageChange = (page: number, pageSize?: number) => {
        setPage(page);
        if (pageSize) {
            setPageSize(pageSize);
        }
    };

    const fetchFaceConfig = () => {
        getElementTypes('FACE').then(res => {
            if (res) {
                const arr = [];
                res.forEach((item) => {
                    //@ts-ignore
                    arr.push({label: item.name, name: item.name, value: item.code, code: item.code});
                });
                setFaceConfig(arr);
            }
        });
    };

    function fetchSceneTypes() {
        getElementTypes('SCENE').then(types => {
            if (types) {
                setSceneTypeList(types);
            }
        });
    }

    useEffect(() => {
        fetchData();

        fetchFaceConfig();

        //场景分类列表-下拉菜单
        fetchSceneTypes();

    }, []);

    useEffect(()=>{
        if (materialType === 'cloth') {
            setMaterialTypeTitle('服装');
        } else if (materialType === 'face') {
            setMaterialTypeTitle('模特');
        } else if (materialType === 'scene') {
            setMaterialTypeTitle('场景');
        }
    }, [materialType]);

    useEffect(() => {
        fetchData();
    }, [page, pageSize]);

    useEffect(() => {
        setPage(1)
        fetchData();
    }, [showOpenScope, searchKey, showSwapType, showTesting, selectType, selectSubType, searchId, materialType]);

    function getTrainingStatusTitle(item: ElementConfigWithBlobs) {
        let labelStatus = item?.labelFinished ? '打标已完成' : '抠图打标中';
        let loraStatus = item?.loraTaskStatusDesc || '未开始';

        return item && item?.loraConfirmed ? (item?.loraTaskStatus === 'RUNNING' ? '模型训练中' : '模型' + loraStatus) : (labelStatus + '-未确认');
    }

    const ImageCard = (item: ElementConfigWithBlobs) => (
            <div className="models-image-card" style={{
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                borderRadius: '12px',
                overflow: 'hidden',
                transition: 'all 0.3s ease',
                position: 'relative',
                width: '248px',
                background: '#FFFFFF',
                padding: 0,
            }}
                 onMouseOver={(e) => {
                     e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.12)';
                     e.currentTarget.style.transform = 'translateY(-2px)';
                 }}
                 onMouseOut={(e) => {
                     e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.08)';
                     e.currentTarget.style.transform = 'translateY(0)';
                 }}
            >
                {/* New标签 */}
                {item.isNew &&
                    <NewLabel/>
                }

                <div className="models-img-cover"
                     style={{
                         cursor: item?.loraModelId ? 'pointer' : 'default',
                         width: '100%',
                         height: '248px',
                         background: '#F5F6F9',
                         borderRadius: '8px 8px 0 0',
                         border: 'none'
                     }}
                     onClick={() => {
                         if (!item.loraModelId) {
                             return;
                         }

                         getMaterialModelById(item.loraModelId).then(m => {
                             if (m){
                                 setSelectedModel(m);
                                 setShowClothDetail(true);
                             }
                         })
                     }}
                >
                    <img src={item.showImage} alt={item.name} loading={'lazy'} style={{objectFit: 'contain'}} />
                    {item.status === 'TEST' &&
                        <Flex align={'center'} justify={'center'} vertical={true}
                              style={{width: '100%', height: '100%', position: 'absolute', background: 'rgba(41, 41, 41, 0.3)'}}>
                            {item?.loraTraining === true &&
                                <>
                                    <l-helix size="45" speed="2.5" color={item?.loraTaskStatus === 'RUNNING' ? 'blue' : '#D88FFF'}/>
                                    <div className={'color-w text16'} style={{marginTop: 8}}>{getTrainingStatusTitle(item)}</div>
                                </>
                            }

                            {!item?.loraTraining &&
                                <div className={'color-w text16'}>测试中</div>
                            }

                        </Flex>
                    }
                </div>

                <div style={{padding: '16px', display: 'flex', flexDirection: 'column', gap: '8px'}}>
                    {/*模型名称*/}
                    <div className="models-image-card-name" style={{fontSize: 14, fontWeight: '600'}}>
                        <Tooltip title={item.name}>
							<span
                                style={{
                                    maxWidth: '180px',
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    display: 'inline-block',
                                    verticalAlign: 'middle',
                                    marginRight: '4px',
                                    cursor: 'pointer'
                                }}
                                onClick={() => {
                                    navigator.clipboard.writeText(item.name);
                                    message.success('已复制模特名称');
                                }}
                            >
								{item.name.length > 18 ? item.name.slice(0, 18) + '...' : item.name}
							</span>
                        </Tooltip>
                        <Tooltip title={`复制ID: ${item?.id}`}>
							<span
                                style={{ color: '#727375', fontSize: 12, cursor: 'pointer' }}
                                onClick={() => {
                                    navigator.clipboard.writeText(item?.id?.toString());
                                    message.success('已复制ID');
                                }}
                            >
								{`(${item?.id})`}
							</span>
                        </Tooltip>
                    </div>
                    <div style={{display: 'flex', flexDirection: 'column', gap: '4px'}}>
                        <div className="models-image-card-name" style={{fontSize: 12, fontWeight: 'normal'}}>
                            创建时间：{item.createTime}
                        </div>
                    </div>
                </div>
            </div>
        )
    ;

    const handleChangeType = (e) => {
        setSelectType(e.target.value);
    };
    const handleChangeSubType = (e) => {
        setSelectSubType(e.target.value);
    };

    const filterBaseType = (type: Array<any>): Array<any> => {
        return type.filter(e => FaceBaseType.includes(e.code));
    };

    const filterSubType = (type: Array<{ code: string, name: string }>): Array<any> => {
        return type.filter(e => !FaceBaseType.includes(e.code));
    };

    function onCancelClothDetailDlg() {
        setShowClothDetail(false);
        setSelectedModel(undefined);
    }

    function onConfirmClothDetailDlg() {
        setShowClothDetail(false);
        setSelectedModel(undefined);
        //刷新当前页面
        window.location.reload();
    }

    const getSearchSceneList = (sceneTypeList: any[]) => {
        const res = [];
        sceneTypeList.forEach(item => {
            // @ts-ignore
            res.push({label: item.name, value: item.code});
        });

        return [{label: '全部', value: 'all'}, ...res];
    };

    return (
        <PageContainer>

            <div className={'distributor-view-main-container'}>
                <Tabs
                    activeKey={materialType}
                    items={[
                        { key: 'face', label: '平台模特' },
                        { key: 'scene', label: '平台场景' },
                    ]}

                    onChange={(key) => {
                        setMaterialType(key);
                        setSelectType('all');
                        setSelectSubType('all');
                        setShowOpenScope('all');
                        setShowTesting('PROD');
                        setShowSwapType('all');
                    }}
                />
            </div>

            <Flex vertical gap={8} style={{padding: '0 16px 16px 16px'}}>
                {materialType === 'face' &&
                    <Flex vertical justify={'flex-start'} gap={8}>
                        <Flex align={'center'} justify={'flex-start'} gap={8}>
                            <Flex align={'center'} justify={'flex-start'} gap={8}>
                                <Radio.Group optionType="button" defaultValue={'all'} size={'small'}
                                             options={[{label: '全部', value: 'all'},
                                                 {label: '公共', value: 'public'},
                                                 {label: '专属', value: 'private'},
                                             ]} onChange={e => setShowOpenScope(e.target.value)}/>

                                <Radio.Group optionType="button" size={'small'}
                                             options={[{label: '全部', value: 'all'}, {label: 'V1模特', value: 'IMAGE'}, {
                                                 label: 'V2模特', value: 'LORA',
                                             }]} defaultValue={'all'} onChange={e => setShowSwapType(e.target.value)}/>

                                <Radio.Group options={[{label: '全部', value: 'all'}, ...filterBaseType(faceConfig)]} size={'small'}
                                             defaultValue={'all'} optionType="button" onChange={e => handleChangeType(e)}/>

                                <Input placeholder={'名称搜索'} onChange={(e) => setSearchKey(e.target.value)} style={{width: 150, height: 24}}/>
                            </Flex>

                        </Flex>

                        <Flex align={'center'} justify={'flex-start'} gap={8}>
                            <Radio.Group options={[{label: '全部', value: 'all'}, ...filterSubType(faceConfig)]} size={'small'}
                                         defaultValue={'all'} optionType="button" onChange={e => handleChangeSubType(e)}/>
                        </Flex>

                    </Flex>
                }

                {materialType === 'scene' &&
                    <Flex vertical={true} gap={8}>
                        <Flex gap={8} align={'center'} justify={'flex-start'}>

                            <Radio.Group optionType="button" defaultValue={'all'} size={'small'}
                                         options={[{label: '全部', value: 'all'},
                                             {label: '公共', value: 'public'},
                                             {label: '专属', value: 'private'},
                                         ]} onChange={e => setShowOpenScope(e.target.value)}/>

                            <Radio.Group optionType="button" size={'small'}
                                         options={[{label: '全部', value: 'all'}, {label: '普通场景', value: 'normal'},
                                             {label: '风格场景', value: 'style'}]} defaultValue={'all'}
                                         onChange={e => setShowSwapType(e.target.value)}/>

                            <Input placeholder={'名称搜索'} onChange={(e) => setSearchKey(e.target.value)} style={{width: 150, height: 24}}/>

                        </Flex>

                        <Radio.Group options={getSearchSceneList(sceneTypeList)} size={'small'}
                                     defaultValue={'all'} optionType="button" onChange={e => setSelectType(e.target.value)}/>
                    </Flex>

                }

                <Flex gap={8} style={{flexWrap: 'wrap'}}>
                    {datas.map(item => (
                        <ImageCard {...item} key={item.id}/>
                    ))}
                </Flex>

                <div className={'stick-bottom-pagination'}>
                    <Pagination
                        current={page}
                        pageSize={pageSize}
                        total={total}
                        onChange={handlePageChange}
                        showTotal={(total) => `共 ${total} 条记录`}
                        showSizeChanger // 允许用户更改每页显示条数
                        pageSizeOptions={[20, 50, 100]}
                        showQuickJumper // 允许用户快速跳转到某一页
                        style={{ marginTop: '16px', textAlign: 'center' }}
                    />
                </div>
            </Flex>

            {previewImage && (
                <Image
                    wrapperStyle={{display: 'none'}}
                    preview={{
                        visible: previewOpen,
                        onVisibleChange: (visible) => setPreviewOpen(visible),
                        afterOpenChange: (visible) => !visible && setPreviewImage(''),
                    }}
                    src={previewImage}
                />
            )}

            {showClothDetail && selectedModel && (
                <ClothDetailDlg
                    materialType={materialType}
                    materialTypeTitle={materialTypeTitle}
                    model={selectedModel}
                    onCancel={onCancelClothDetailDlg}
                    onConfirm={onConfirmClothDetailDlg}
                    editAble={false}
                />
            )}

        </PageContainer>
    );
};

export default DistributorFaceSceneView;
