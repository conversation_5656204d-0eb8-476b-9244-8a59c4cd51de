import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Checkbox, Flex, GetProps, Radio, Switch, TreeDataNode } from 'antd';
import { Button, Form, Input, message, Modal, Pagination, Select, Table, Tree } from 'antd';

import { deleteUser, UserVO } from '@/services/UserController';
import {
  createDept,
  createStaff,
  CustomRoleVO,
  deleteDept,
  getOrganizationTreeByUserId,
  queryAllCorpRoles,
  queryDeptUsersIncludeSubByPage,
  updateDept,
  updateStaff,
} from '@/services/DistributorController';
import './distributor-operators.less';
import { findOrganizationById, OrganizationVO, salesTypeMap } from '@/services/OrganizationController';
import { USER_INFO } from '@/constants';
import { ProForm, ProFormDigit, ProFormRadio, ProFormSelect, ProFormSwitch, ProFormText } from '@ant-design/pro-components';

type DirectoryTreeProps = GetProps<typeof Tree.DirectoryTree>;
const { DirectoryTree } = Tree;

interface UserModalConfig {
  title: string,
  uid?: number | undefined,
  // eslint-disable-next-line @typescript-eslint/ban-types
  onOk: Function,
  // eslint-disable-next-line @typescript-eslint/ban-types
  onCancel: Function
}

const DistributorTeamPage: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [data, setData] = useState<Array<UserVO>>([]);

  //部门/子组织列表
  const [depts, setDepts] = useState<OrganizationVO>();
  const [deptTree, setDeptTree] = useState<TreeDataNode>();
  const [showCreateDepts, setShowCreateDepts] = useState(false);
  const [showUpdateDepts, setShowUpdateDepts] = useState(false);
  const [selectedDept, setSelectedDept] = useState<OrganizationVO>();
  const [selectedUser, setSelectedUser] = useState<UserVO>();
  const [customRoles, setCustomRoles] = useState<CustomRoleVO[]>([]);

  const userInfo = localStorage.getItem(USER_INFO);
  const distributorAdmin = userInfo != null && (JSON.parse(userInfo)?.customRole === 'CHANNEL_ADMIN');

  const onSelect: DirectoryTreeProps['onSelect'] = (keys, info) => {
    console.log('Trigger Select', keys, info);
    if (depts) {
      setSelectedDept(findOrganizationById(depts, Number(keys[0])));
    }
  };

  const columns = [
    {
      title: '客户ID',
      dataIndex: 'id',
      key: 'id',
      render: (text) => <span style={{ color: '#409EFF' }}>{text}</span>,
    },
    {
      title: '昵称',
      dataIndex: 'nickName',
      key: 'nickName',
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
      key: 'mobile',
    },
    {
      title: '角色',
      dataIndex: 'customRoleName',
      key: 'customRoleName',
    },
    {
      title: '关联虚拟账号',
      dataIndex: 'relatedAccounts',
      key: 'relatedAccounts',
      render: (_,record) => (
        <Flex vertical={true} style={{fontSize: 12}}>
          {record?.relatedAccounts?.relatedDemoAccount &&
            <span>{`销售演示账号：${record?.relatedAccounts?.relatedDemoAccount?.nickName}(${record?.relatedAccounts?.relatedDemoAccount?.mobile})`}</span>
          }
          {record?.relatedAccounts?.relatedMerchantAccount &&
            <span>{`虚拟商家账号：${record?.relatedAccounts?.relatedMerchantAccount?.nickName}(${record?.relatedAccounts?.relatedMerchantAccount?.mobile})`}</span>
          }
        </Flex>
      ),
    },
    {
      title: '最近登录',
      dataIndex: 'lastLoginTime',
      key: 'lastLoginTime',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '操作',
      key: 'action',
      className: 'action-column',
      render: (text, record: UserVO) => (
        <span className="action-buttons">
          <a onClick={() => showUpdateUserModal(record)}>编辑</a>
          <a onClick={() => showDeleteUserModal(record)}>删除</a>
        </span>
      ),
    },
  ];

  async function fetchStaffData(pageNum: number, pageSize: number) {

    if (!selectedDept) {
      console.log('selectedDept is null');
      return;
    }

    queryDeptUsersIncludeSubByPage({
      pageNum: pageNum,
      pageSize: pageSize,
      deptOrgId: selectedDept?.id

    }).then(res => {
      console.log('fetch data list response:', res);
      if (res) {
        //@ts-ignore 追加一个key字段，避免警告
        if (res.list) {
          let newData = res.list.map(item => ({ ...item, key: item.id }));
          console.log('newData:', newData);

          setData(newData);

          console.log('data:', data);
        } else {
          setData([]);
        }

        // @ts-ignore
        setTotalCount(res.totalCount);
      }
    });
  }

  async function refreshStaffList() {
    setCurrentPage(1);
    fetchStaffData(1, pageSize);
  }

  async function refreshOrgTree(){
    getOrganizationTreeByUserId().then(res => {
      if (res) {
        setDepts(res);
        let newTree = convertToTreeDataNode(res);
        setDeptTree(newTree);
        setSelectedDept(res);
      }
    });
  }

  useEffect(() => {
    queryAllCorpRoles({}).then(res => {
      if (res) {
        setCustomRoles(res);
      }
    });

    refreshOrgTree();

  }, []);

  useEffect(() => {
    refreshStaffList();
  }, [selectedDept]);

  function convertToTreeDataNode(organization: OrganizationVO): TreeDataNode {
    const treeNode: TreeDataNode = {
      key: organization.id.toString(),
      title: getSalesTypeDesc(organization) + organization.name,
      children: [],
    };

    if (organization.children && organization.children.length > 0) {
      treeNode.children = organization.children.map(convertToTreeDataNode);
    }

    console.log('treeNode:', treeNode);
    return treeNode;
  }

  function getSalesTypeDesc(organization: OrganizationVO) {
    if (organization && organization.extInfo && organization.extInfo['salesType']) {
      return `(${ salesTypeMap[organization.extInfo.salesType].desc })`;
    }
    return '';
  }

  // 删除用户弹窗确认
  function showDeleteUserModal(u: UserVO) {
    Modal.confirm({
      title: '确认删除',
      content: '删除后不可恢复，请谨慎操作',
      okText: '确认删除',
      cancelText: '取消',
      okType: 'danger',
      icon: null,
      onOk() {
        deleteUser(u.id).then(res => {
          if (res) {
            message.success('操作成功');
            refreshStaffList();
          }
        });
      },
    });
  }

  const handlePageChange = (page) => {
    setCurrentPage(page);
    fetchStaffData(page, pageSize);
  };

  const handlePageSizeChange = (current, size) => {
    setPageSize(size);
    setCurrentPage(1); // 重置到第一页
    fetchStaffData(1, size);
  };

  const [showUserModal, setShowUserModal] = useState(false);
  const [showUserModalConfig, setShowUserModalConfig] = useState<UserModalConfig>();

  const [form] = Form.useForm();
  const customRole = Form.useWatch('customRole', form);

  const handleCancel = () => {
    setShowUserModal(false);
    form.resetFields();
  };

  const handleNewUser = () => {

    form.validateFields()
      .then(values => {
        // 在这里处理表单提交
        console.log('表单值: ', values);
        createStaff(values).then(res => {
          console.log('res:', res);
          if (res) {
            message.success('操作成功');
            refreshStaffList();
          } else {
            message.error('操作失败');
          }
        });

        setShowUserModal(false);
        form.resetFields();
      })
      .catch(info => {
        console.log('验证失败:', info);
      });
  };

  const handleUpdateUser = (uid: number) => {
    form.validateFields()
      .then(values => {
        values.id = uid;
        // 在这里处理表单提交
        console.log('表单值: ', values);
        updateStaff(values).then(res => {
          console.log('res:', res);
          if (res) {
            message.success('操作成功');
            refreshStaffList();
          }
        });

        setShowUserModal(false);
        form.resetFields();
      })
      .catch(info => {
        console.log('验证失败:', info);
      });
  };

  const showNewUserModal = () => {
    setShowUserModal(true);
    setShowUserModalConfig({
      title: '添加员工',
      uid: undefined,
      onOk: handleNewUser,
      onCancel: handleCancel,
    });
    form.resetFields();
    form.setFieldsValue({ userType: 'SUB' });
  };

  const showUpdateUserModal = (u: UserVO) => {
    setShowUserModal(true);
    setShowUserModalConfig({
      title: '编辑',
      onOk: () => handleUpdateUser(u.id),
      onCancel: handleCancel,
      uid: u.id,
    });

    form.setFieldsValue({...u });
    console.log('showUpdateUserModal user:', u);
  };

  const handleCreateDept = () => {
    form.validateFields().then((values) => {
      createDept({
        parentId: selectedDept?.id,
        name: values.name,
        salesType: values.salesType,
        customRole: values.customRole,
        mobile: values.mobile,
        nickName: values.nickName,
      }).then(res => {
        if (res) {
          message.success('操作成功');
          setShowCreateDepts(false);
          form.resetFields();
          refreshOrgTree();
        }
      });
    });
  };

  const handleCancelCreateDepts = () => {
    setShowCreateDepts(false);
    form.resetFields();
  };

  function onUpdateDeptName() {
    form.validateFields().then((values) => {

      updateDept({
        id: selectedDept?.id,
        name: values.name,
      }).then(res => {
        if (res) {
          message.success('操作成功');
          setShowUpdateDepts(false);
          form.resetFields();
          refreshOrgTree();
        }
      });
    });
  }

  function getStaffCustomRoles4UserDlg(isNewUser: boolean, isRootCorpOrg: boolean, isDistributorAdmin: boolean){

    if (isNewUser && isRootCorpOrg && isDistributorAdmin) {
      return customRoles.filter(item => item.code !== 'CHANNEL_ADMIN' && item.code !== 'SECOND_CHANNEL_ADMIN');
    }

    if (isNewUser && !isRootCorpOrg && isDistributorAdmin) {
      return customRoles.filter(item => item.code !== 'CHANNEL_ADMIN');
    }

    return customRoles;
  }

  return (
    <PageContainer>
      <div className={'distributor-operators-main'}>

        {deptTree && distributorAdmin && (
          <div className={'distributor-operators-left'}>
            <DirectoryTree
              multiple={false}
              expandAction={false}
              defaultExpandAll
              selectedKeys={[selectedDept?.id.toString() || '']}
              treeData={[deptTree]}
              onSelect={onSelect}
            />
          </div>
        )}

        <div className={'distributor-operators-right'}>
          {
            distributorAdmin && (
              <>
                <Button type="primary" className="distributor-operators-add-user-button"
                        onClick={() => {
                          setShowCreateDepts(true);
                          form.resetFields();
                        }}>
                  创建二级渠道
                </Button>

                <Button type="primary" className="distributor-operators-add-user-button"
                        onClick={() => {
                          setShowUpdateDepts(true);
                          form.setFieldsValue({ name: selectedDept?.name });
                        }}>
                  <span>{selectedDept?.root === true ? '编辑企业' : '编辑二级渠道'}</span>
                </Button>

                <Button type="primary"
                        disabled={selectedDept?.root === true}
                        className="distributor-operators-add-user-button"
                        onClick={() => {
                          Modal.confirm({
                            title: '确认删除',
                            content: `删除后不可恢复，请谨慎操作，确认删除："${selectedDept?.name}"吗?`,
                            okText: '确认删除',
                            cancelText: '取消',
                            okType: 'danger',
                            centered: true,
                            icon: null,
                            onOk() {
                              deleteDept({ id: selectedDept?.id }).then(res => {
                                if (res) {
                                  message.success('操作成功');
                                  refreshOrgTree();
                                }
                              });
                            },
                          });
                        }}>
                  删除二级渠道
                </Button>
              </>
            )
          }

          <Button type="primary" className="distributor-operators-add-user-button"
                  onClick={() => {showNewUserModal(); form.setFieldsValue({ deptOrgId: selectedDept?.id })}}>
            添加员工
          </Button>
          <Table
            columns={columns}
            dataSource={data}
            pagination={false}
            className="distributor-operators-user-table"
          />
          <div className="distributor-operators-pagination-container">
            <span>共 {totalCount} 条</span>
            <Pagination
              className="distributor-operators-pagination"
              current={currentPage}
              total={totalCount}
              pageSize={pageSize}
              onChange={handlePageChange}
              showSizeChanger
              onShowSizeChange={handlePageSizeChange}
              pageSizeOptions={['10', '20', '50', '100']}
            />
            <div className="distributor-operators-goto-page">
              <span>前往</span>
              <Input
                className="distributor-operators-goto-input"
                value={currentPage}
                onChange={(e) => setCurrentPage(Number(e.target.value))}
                onPressEnter={(e) => handlePageChange(Number(e.currentTarget.value))}
              />
              <span>页</span>
            </div>
          </div>
        </div>

        {/*添加用户和编辑用户的弹窗*/}
        {showUserModal && (
          <Modal
            className="distributor-operators-user-modal"
            title={showUserModalConfig?.title}
            open={showUserModal}
            onOk={() => showUserModalConfig?.onOk()}
            onCancel={handleCancel}
            okText="确定"
            cancelText="取消"
            width={475}
            maskClosable={false}
            centered
          >
            <Form form={form} layout="horizontal" name="user_form">
              <Form.Item
                name="nickName"
                label="昵称"
                rules={[{ required: true, message: '请输入昵称!' }]}
                style={{ padding: '0 12px' }}
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
                colon={false}
              >
                <Input placeholder="请输入" autoFocus style={{ width: 270 }} />
              </Form.Item>
              <Form.Item
                name="mobile"
                label="手机号"
                rules={[
                  { required: true, message: '请输入手机号!' },
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号!' },
                ]}
                style={{ padding: '0 12px' }}
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
                colon={false}
              >
                <Input placeholder="请输入" style={{ width: 270 }} maxLength={11} showCount />
              </Form.Item>

              <Form.Item
                name="customRole"
                label="角色"
                rules={[{ required: true, message: '请选择角色!' }]}
                style={{ padding: '0 12px' }}
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
                colon={false}
                hidden={form.getFieldValue('customRole') === 'CHANNEL_ADMIN'}
              >
                <Select placeholder="请选择" style={{ width: 270 }}>
                  {getStaffCustomRoles4UserDlg(!form.getFieldValue('id'), deptTree != null && deptTree.key === selectedDept?.id.toString(), distributorAdmin).map((role) => (
                    <Select.Option key={role.code} value={role.code}>
                      {role.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>

              {!form.getFieldValue('id') && customRole != null && customRole !== 'OPS_MEMBER' &&
                <span style={{color: 'red',fontSize: 10}}>说明：创建销售后，将创建关联的商家账号和演示账号，初始密码bb123456.员工账号-右上角菜单-'客户信息'页面，可以查看关联的虚拟商家账号和演示账号</span>
              }

              <Form.Item
                name="deptOrgId"
                label="二级渠道"
                rules={[{ required: false, message: '请选择二级渠道!' }]}
                style={{ padding: '0 12px' }}
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
                colon={false}
                hidden={true}
              >
                <Select placeholder="请选择" style={{ width: 270 }} disabled={true}>
                    <Select.Option value={selectedDept?.id}>
                      {selectedDept?.name}
                    </Select.Option>
                </Select>
              </Form.Item>

            </Form>
          </Modal>
        )}

        {showCreateDepts && (
          <Modal title="创建二级渠道" centered open={showCreateDepts} onOk={handleCreateDept} onCancel={handleCancelCreateDepts}>
            <ProForm
              form={form}
              layout="vertical"
              name="add_dept_form"
              submitter={false}
            >
              <ProFormText
                name="name"
                label="二级渠道名称"
                rules={[{ required: true, message: '请输入名称!' }]}
              />
              <ProFormRadio.Group
                name="salesType"
                label="渠道类型"
                rules={[{ required: true, message: '请选择渠道类型!' }]}
                initialValue="AGENT_NORMAL"
                radioType="button"
                fieldProps={{
                  buttonStyle: 'solid',
                }}
                options={Object.entries(salesTypeMap).map(([_, e]) => {
                  return {
                    label: e.desc,
                    value: e.code,
                  }
                })}
                hidden={salesTypeMap['DIRECT'].code !== depts?.extInfo['salesType']}
              />
              <ProFormSwitch
                name="isCreatSndChannelAdmin"
                label="同时创建二级渠道管理员"
                checkedChildren="是"
                unCheckedChildren="否"
              />
              <ProForm.Item noStyle shouldUpdate >
                {({getFieldValue}) => {
                  const isCreatSndChannelAdmin = getFieldValue('isCreatSndChannelAdmin');
                  return isCreatSndChannelAdmin ? (
                    <>
                      <ProFormText
                        name="nickName"
                        label="昵称"
                        placeholder="请输入"
                        rules={[{ required: true, message: '请输入昵称!' }]}
                      />
                      <ProFormText
                        name="mobile"
                        label="手机号"
                        placeholder="请输入"
                        rules={[
                          { required: true, message: '请输入手机号!' },
                          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号!' },
                        ]}
                        fieldProps={{
                          maxLength: 11,
                          showCount: true,
                        }}
                      />
                      <ProFormSelect
                        name="customRole"
                        placeholder="请选择"
                        disabled
                        label="角色"
                        rules={[
                          { required: true, message: '请选择角色' },
                        ]}
                        options={customRoles.filter(role => role.code === 'SECOND_CHANNEL_ADMIN').map(
                          role => ({label: role.name, value: role.code})
                        )}
                        initialValue={'SECOND_CHANNEL_ADMIN'}
                      />
                    </>
                  ) : null;
                }}
              </ProForm.Item>
            </ProForm>
          </Modal>
        )}

        {showUpdateDepts && (
          <Modal title="编辑"
                 open={showUpdateDepts}
                 centered
                 onOk={onUpdateDeptName}
                 onCancel={() => {
                   setShowUpdateDepts(false);
                   form.resetFields();
                 }}>
            <Form form={form} layout="vertical" name="update_dept_form">
              <Form.Item
                name="name"
                label="名称"
                rules={[{ required: true, message: '请输入名称!' }]}
              >
                <Input />
              </Form.Item>
            </Form>
          </Modal>
        )}

      </div>

    </PageContainer>
  );
};

export default DistributorTeamPage;
