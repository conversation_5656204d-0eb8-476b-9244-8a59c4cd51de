.distributor-customers-main {
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  padding: 16px;
  align-self: stretch;
  background: #FFFFFF;
}

.distributor-customers-top-bar {
  display: flex;
  margin-bottom: 16px;
  gap: 16px;
  align-items: center;
}

.distributor-customers-user-table {
  .ant-table-container {
    border-left: 1px solid #d9d9d9;  /* 设置左边框 */
    border-right: 1px solid #d9d9d9; /* 设置右边框 */
  }

  //表头
  .ant-table-thead > tr > th {
    background: #F2F6FC;
    text-align: left;

    font-weight: normal;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: 0;
    color: #909399;
  }

  //单元格
  .ant-table-tbody > tr > td {
    height: 61.45px;
    padding: 12px 16px;
    gap: 10px;
    background: #FFFFFF;
    border-bottom: 1px solid #DCDFE6; /* 使用边框代替阴影 */
    box-shadow: none;

    font-size: 14px;
    font-weight: normal;
    line-height: 24px;
    letter-spacing: 0px;
    color: #606266;
  }

  /* 设置操作列的标题右对齐 */
  .ant-table-thead > tr > th.action-column {
    text-align: right;
  }

  .ant-table-tbody > tr > td.action-column {
    text-align: right;
  }
}

.action-buttons {
  display: inline-flex;
  justify-content: flex-end;
  width: 100%;
}

.action-buttons a {
  margin-left: 12px;
}

.action-buttons a:first-child {
  margin-left: 0;
}

.distributor-customers-pagination-container {
  height: 72px;
  padding: 20px 0;
  gap: 16px;
  display: flex;
  align-items: center;
  justify-content: start;

  .distributor-customers-pagination {
    display: flex;
    align-items: center;
  }

  .distributor-customers-goto-page {
    display: flex;
    align-items: center;

    .distributor-customers-goto-input {
      width: 50px;
      margin: 0 8px;
      text-align: center;
    }
  }
}

.distributor-customers-user-modal {
  .ant-modal-header {
    padding: 20px;
  }

  .ant-modal-footer {
    display: flex;
    justify-content: end;
    padding: 15px;

    .ant-btn {
      margin: 0 8px;
    }
  }
}
