import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Table, Flex, Segmented, Pagination, DatePicker, Select, Descriptions, Space, Row, Col } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import './distributor-customer-account-records.less';
import { UserOptionVO, UserVO } from '@/services/UserController';
import { USER_INFO } from '@/constants';
import { download, getUserInfo, getUserRoleType, maskPhoneNumber } from '@/utils/utils';
import IconFont from '@/components/IconFont';
import InvoiceDlg from '@/components/InvoiceDlg';
import { InvoiceTitleVO } from '@/services/InvoiceInfoController';
import {
  DistributorCustomerPointUsageVO, DistributorCustomerTopupStaffSummary,
  DistributorCustomerTopupSummary,
  DistributorCustomerTopupVO,
  queryCustomerTopupByPage, queryTopupSummary,
  queryUsagePerformanceByPage,
  queryUserOptions4TopupPerformance,
  queryUserOptions4UsagePerformance,
} from '@/services/DistributorController';

const { RangePicker } = DatePicker;

const DistributorCustomerAccountRecords: React.FC = () => {
  const [usageRecords, setUsageRecords] = useState<DistributorCustomerPointUsageVO[]>([]);
  const [topupRecords, setTopupRecords] = useState<DistributorCustomerTopupVO[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [hasNextPage, setHasNextPage] = useState<boolean>(true);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(10);
  const [activeTab, setActiveTab] = useState<string>('充值业绩'); // Track the active tab

  const [orderByCreateTime, setOrderByCreateTime] = useState('');
  const [dates, setDates] = useState([]);

  const [showInvoiceDlg, setShowInvoiceDlg] = useState(false);
  const [showInvoiceTitleDlg, setShowInvoiceTitleDlg] = useState(false);

  const [invoiceTitle, setInvoiceTitle] = useState<InvoiceTitleVO>();

  const [relatedCustomers, setRelatedCustomers] = useState<UserOptionVO[]>([]);
  const [relatedOperators, setRelatedOperators] = useState<UserOptionVO[]>([]);
  const [relatedSales, setRelatedSales] = useState<UserOptionVO[]>([]);

  const [selectedCustomerId, setSelectedCustomerId] = useState<number>();
  const [selectedOperatorId, setSelectedOperatorId] = useState<number>();
  const [selectedSalesId, setSelectedSalesId] = useState<number>();

  const [topupSummary, setTopupSummary] = useState<DistributorCustomerTopupSummary>();

  // @ts-ignore
  const userInfo: UserVO = JSON.parse(localStorage.getItem(USER_INFO));

  useEffect(() => {

    let orderBy = 'id desc';

    if (orderByCreateTime === 'ascend') {
      orderBy = 'create_time asc';
    } else if (orderByCreateTime === 'descend') {
      orderBy = 'create_time desc';
    }

    let query = {
      pageNum: currentPage,
      pageSize: pageSize,
      orderBy: orderBy,
    };

    if (dates && dates.length > 1) {
      // @ts-ignore
      query['dateFrom'] = dates[0].format('YYYYMMDD');
      // @ts-ignore
      query['dateTo'] = dates[1].format('YYYYMMDD');
    }

    if (selectedCustomerId) {
      query['customerId'] = selectedCustomerId;
    }
    if (selectedOperatorId) {
      query['operatorId'] = selectedOperatorId;
    }
    if (selectedSalesId) {
      query['salesId'] = selectedSalesId;
    }

    console.log('查询参数:', query);

    //使用记录
    if (activeTab === '使用记录') {
      queryUsagePerformanceByPage(query).then(page => {
        if (page) {
          setHasNextPage(page.hasNextPage != null && page.hasNextPage);
          setUsageRecords(page?.list || []);
          setTotalCount(page?.totalCount || 0);
        }
      });

      //充值业绩
    } else {
      queryCustomerTopupByPage(query).then(page => {
        if (page) {
          setHasNextPage(page.hasNextPage != null && page.hasNextPage);
          setTopupRecords(page?.list || []);
          setTotalCount(page?.totalCount || 0);
        }
      });

      //统计信息
      queryTopupSummary(query).then(summary => {
        if (summary) {
          setTopupSummary(summary);
        }
      });
    }

    //过滤器
    let filterOptionsFetcher = (activeTab === '使用记录') ? queryUserOptions4UsagePerformance : queryUserOptions4TopupPerformance;
    filterOptionsFetcher(query).then(res => {
      if (res) {
        if (res.relatedCustomers) {
          setRelatedCustomers(res.relatedCustomers);
        }

        if (res.relatedOperators) {
          setRelatedOperators(res.relatedOperators);
        }

        if (res.relatedSales) {
          setRelatedSales(res.relatedSales);
        }
      }
    });

  }, [activeTab, currentPage, pageSize, orderByCreateTime, dates, selectedCustomerId, selectedOperatorId, selectedSalesId]);

  const usageColumns: ColumnsType<DistributorCustomerPointUsageVO> = [
    { title: '客户名称', key: 'masterUserNickName',
      render: (_: any, record: DistributorCustomerPointUsageVO) => (
        record.masterUserNickName
          ? <span>{record.masterUserNickName}<span
            style={{ color: 'gray' }}>&nbsp;({record.masterLoginId})</span></span>
          : <span></span>
      )
    },
    {
      title: '运营',
      key: 'operators',
      render: (_: any, record: DistributorCustomerPointUsageVO) => (
        record.distributorOperatorNickName
          ? <span>{record.distributorOperatorNickName}<span
            style={{ color: 'gray' }}>&nbsp;({record.distributorOperatorMobile})</span></span>
          : <span></span>
      ),
    },
    {
      title: '销售',
      key: 'sales',
      render: (_: any, record: DistributorCustomerPointUsageVO) => (
        record.distributorSalesNickName
          ? <span>{record.distributorSalesNickName}<span
            style={{ color: 'gray' }}>&nbsp;({record.distributorSalesMobile})</span></span>
          : <span></span>
      ),
    },
    { title: '使用场景', dataIndex: 'usageScene', key: 'usageScene' },
    { title: '使用方式', dataIndex: 'usageWay', key: 'usageWay' },
    {
      title: '消耗数量', key: 'usedCount',
      render: (text, record) => {
        return (
          <Flex gap={'10px'} align={'center'}>

            {record.usedModelPoint != null && record.usedModelPoint > 0 && (
              <Flex gap={'4px'} align={'center'} style={{ fontSize: 14 }}>
                <IconFont type={'icon-icon_tupian'} style={{ fontSize: 24 }} />
                {record.usedModelPoint}张创作图
              </Flex>
            )}
            {record.usedGivePoint != null && record.usedGivePoint > 0 && (
              <Flex gap={'4px'} align={'center'} style={{ fontSize: 14 }}>
                <IconFont type={'icon-icon_tupian'} style={{ fontSize: 24 }} />
                {record.usedGivePoint}张赠送创作图
              </Flex>
            )}
            {record.usedPoint != null && (
              <Flex gap={'4px'} align={'center'} style={{ fontSize: 14 }}>
                <IconFont type={'icon-icon_mousidian'} style={{ fontSize: 24 }} />
                {record.usedPoint}元
              </Flex>
            )}
          </Flex>
        );
      },
    },
    { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
  ];

  const handleTableChange = (pagination, filters, sorter) => {
    console.log('handleTableChange:', sorter);
    if (sorter.field === 'createTime') {
      setOrderByCreateTime(sorter.order);
    }
  };

  const handleDateChange = (newDates) => {
    console.log('handleDateChange:', newDates);
    setDates(newDates);
    setCurrentPage(1);
  };

  const topupColumns: ColumnsType<DistributorCustomerTopupVO> = [
    { title: '企业名称', key: 'masterCorpName', dataIndex: 'masterCorpName' },
    { title: '管理员', key: 'masterUserNickName',
      render: (_: any, record: DistributorCustomerTopupVO) => (
        <span>{record.masterUserNickName}<span
          style={{ color: 'gray' }}>&nbsp;({record.masterLoginId})</span></span>
      )
    },
    {
      title: '销售',
      key: 'sales',
      render: (_: any, record: DistributorCustomerTopupVO) => (
        <span>{record.distributorSalesNickName}<span
          style={{ color: 'gray' }}>&nbsp;({record.distributorSalesMobile})</span></span>
      ),
    },
    {
      title: '套餐名称',
      dataIndex: 'productName',
      key: 'productName'
    },
    { title: '订单号', dataIndex: 'orderNo', key: 'orderNo' },
    {
      title: '金额',
      key: 'payAmount',
      render: (text: number, record: DistributorCustomerTopupVO) => {
        return <span>{record.payAmount}元</span>;
      },
    },
    { title: '充值渠道', key: 'createTime', render: (_, record) => '微信' },
    { title: '充值时间', dataIndex: 'createTime', key: 'createTime' },
    { title: '过期日期', dataIndex: 'expireTime', key: 'expireTime' },
    {
      title: '付款账号', key: 'operatorUserLoginId', render: (_, record: DistributorCustomerTopupVO) => {
        return (
          <Flex gap={'8px'}>
            <span>{maskPhoneNumber(record.operatorUserLoginId)}</span>
            <div style={{ borderRadius: '4px', background: '#F5F6F9', color: '#727375' }}>
              {record.masterUser ? '主账号' : '子账号'}
            </div>
          </Flex>
        );
      },
    },
  ];

  const topupStaffSummaryColumns: ColumnsType<DistributorCustomerTopupStaffSummary> = [
    { title: '员工ID', dataIndex: 'staffId', key: 'staffId' },
    {
      title: '员工名称',
      dataIndex: 'staffName',
      key: 'staffName',
    },
    {
      title: '充值金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: text => <span>{text}元</span>
    },
    {
      title: '充值客户数',
      dataIndex: 'totalTopupCustomerCount',
      key: 'totalTopupCustomerCount',
    },
    { title: '充值次数', dataIndex: 'totalTopupCount', key: 'totalTopupCount' }
  ];

  const onSelectCustomer = (value: string) => {
    setSelectedCustomerId(Number(value));
  };

  const onSelectOperator = (value: string) => {
    setSelectedOperatorId(Number(value));
  };

  const onSelectSales = (value: string) => {
    setSelectedSalesId(Number(value));
  };

  return (
    <PageContainer>
      <div className={'user-center-container'}>

        <div
          style={{ display: 'flex', justifyContent: 'start', alignItems: 'center', margin: '24px 0 24px 0', gap: 24 }}>
          <Segmented
            options={['充值业绩', '使用记录']}
            style={{ backgroundColor: '#E1E3EB', width: 'auto', height: 32 }}
            value={activeTab}
            onChange={(val) => {
              setActiveTab(val as string);
              setCurrentPage(1);
              setHasNextPage(true);
            }}
          />

          <Select showSearch
                  allowClear
                  style={{ width: 150 }}
                  placeholder="选择客户"
                  optionFilterProp="label"
                  onChange={onSelectCustomer}
                  options={relatedCustomers.map(customer => ({
                    value: customer.id.toString(),
                    label: customer.nickName,
                  }))}
          />

          {activeTab === '充值业绩' &&
            <Select showSearch
                    allowClear
                    placeholder="选择销售"
                    optionFilterProp="label"
                    style={{ width: 150 }}
                    onChange={onSelectSales}
                    options={relatedSales.map(customer => ({
                      value: customer.id.toString(),
                      label: customer.nickName,
                    }))}
            />

          }

          {activeTab === '使用记录' &&
            <Select showSearch
                    allowClear
                    placeholder="选择运营"
                    optionFilterProp="label"
                    onChange={onSelectOperator}
                    style={{ width: 150 }}
                    options={relatedOperators.map(customer => ({
                      value: customer.id.toString(),
                      label: customer.nickName,
                    }))}
            />
          }

          <RangePicker onChange={handleDateChange} placeholder={['开始日期', '结束日期']}
                       style={{ width: 320, height: 32 }} />

        </div>

        {activeTab === '使用记录' && (
          <Table columns={usageColumns}
                 dataSource={usageRecords}
                 rowKey="pointLogId"
                 className={'user-center-table'}
                 onChange={handleTableChange}
                 pagination={false} />
        )}

        {activeTab === '充值业绩' && topupSummary && (
          <Flex vertical={true} gap={24}>

            {/*渠道管理员才可以看整体业绩*/}
            {getUserInfo()?.customRole?.includes('ADMIN') && (
              <Flex vertical={true} gap={12}>
                <span>整体业绩：</span>
                <Row gutter={16} style={{ width: 600 }}>
                  <Col span={8}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div style={{ marginRight: 8 }}>充值金额:</div>
                      <div>{topupSummary.totalAmount}元</div>
                    </div>
                  </Col>
                  <Col span={8}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div style={{ marginRight: 8 }}>充值客户数:</div>
                      <div>{topupSummary.totalTopupCustomerCount}</div>
                    </div>
                  </Col>
                  <Col span={8}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div style={{ marginRight: 8 }}>充值次数:</div>
                      <div>{topupSummary.totalTopupCount}</div>
                    </div>
                  </Col>
                </Row>
              </Flex>
            )}

            <Flex vertical={true} gap={12}>
              <span>业绩列表：</span>
              <Table columns={topupStaffSummaryColumns}
                     dataSource={topupSummary.topupStaffItems}
                     rowKey="staffId"
                     className={'user-center-table'}
                     pagination={false} />
            </Flex>

            <Flex vertical={true} gap={12}>
              <span>充值明细：</span>
              <Table columns={topupColumns}
                     dataSource={topupRecords}
                     rowKey="id"
                     className={'user-center-table'}
                     pagination={false}
                     onChange={handleTableChange} />
            </Flex>

          </Flex>
        )}

        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={totalCount}
          onChange={(page: number, pageSize: number) => {
            console.log('Pagination onChange:', page, pageSize);
            setCurrentPage(page);
            setPageSize(pageSize);
          }}
          showTotal={(total, range) => `共 ${totalCount} 条`}
          showSizeChanger // 允许用户更改每页显示条数
          showQuickJumper // 允许用户快速跳转到某一页
          onShowSizeChange={(current, size) => {
            console.log('Pagination onShowSizeChange:', current, size);
            setPageSize(size);
            setCurrentPage(1);
          }}
          pageSizeOptions={['10', '20', '50', '100']}
          style={{ marginTop: '16px', textAlign: 'center' }}
        />

      </div>

    </PageContainer>
  );
};

export default DistributorCustomerAccountRecords;