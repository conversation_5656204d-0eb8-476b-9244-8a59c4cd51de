import React, { useEffect, useState } from 'react';
import {
  createSuccessStories, deleteSuccessStories,
  querySuccessStoriesByPage,
  SalesSuccessStoriesVO,
  updateSuccessStories,
} from '@/services/SalesSuccessStoriesController';
import {
  Button,
  Col,
  Flex,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Table,
} from 'antd';
import UploadFormItem from '@/components/Common/UploadFormItem';
import { PageContainer } from '@ant-design/pro-layout';
import LoraSelectFormItem from '@/components/Common/LoraSelectFormItem';
import MasterSelector from '@/components/MasterSelector';
import ImgPreview from '@/components/ImgPreview';

const SalesSuccessStoriesMng: React.FC = () => {
  const [data, setData] = useState<SalesSuccessStoriesVO[]>([]);
  const [loading, setLoading] = useState(false);
  const [opItem, setOpItem] = useState<SalesSuccessStoriesVO | null>(null);
  const [previewItem, setPreviewItem] = useState<SalesSuccessStoriesVO | null>(null);
  const [add, setAdd] = useState(false);

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);

  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [previewList, setPreviewList] = useState<string[]>([]);

  const [form] = Form.useForm();

  // useEffect(() => {
  //   fetchData();
  // }, []);

  useEffect(() => {
    fetchData();
  }, [page, pageSize]);

  // useEffect(() => {
  //   setPage(1);
  //   fetchData();
  // }, [searchUserId, searchId, searchName, searchStatus]);

  const fetchData = () => {
    setLoading(true)
    querySuccessStoriesByPage({
      pageNum: page,
      pageSize,
    }).then((res) => {
      setLoading(false);
      if (!res) return;
      setData(res.list || []);
      setTotal(res.totalCount ? res.totalCount : 0);
    });
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setPage(page);
    if (pageSize) {
      setPageSize(pageSize);
    }
  };

  const handleAdd = () => {
    setOpItem(null);

    form.resetFields();
    setAdd(true);
  };

  const handleEdit = (item) => {
    form.resetFields();
    setAdd(false);

    form.setFieldsValue(item);
    setOpItem(item);
  };

  const closeModal = () => {
    setOpItem(null);
    setAdd(false);
  };

  const handleCommit = async (values) => {
    const method = add ? createSuccessStories : updateSuccessStories;
    method(values).then(res => {
      if (res) {
        message.success('保存成功');
        setAdd(false);
        setOpItem(null);
        fetchData();
      }
    });
  };

  const handleDelete = async (id) => {
    deleteSuccessStories(id).then(res => {
      if (res) {
        message.success('删除成功');
        fetchData();
      }
    });
  };

  const buildMiniImg = (url, title: null | string = null, size = 48) => {
    if (!url) return null;

    return <Flex gap={4} align={'center'} key={url}>
      <img src={url} alt={'img'} width={size} style={{ borderRadius: 8 }} />
      {title &&
        <div>{title}</div>
      }
    </Flex>;
  };
  
  const ShowRowBlock = ({ title, value }) => (
    <Row gutter={[16, 24]}>
      <Col span={2} >
        <Flex className={'weight text14'} justify={'flex-end'}>{title}：</Flex>
      </Col>
      <Col span={22}>
        <div className={'text16'}>{value}</div>
      </Col>
    </Row>
  );

  const columns = [
    { title: 'ID', dataIndex: 'id', key: 'id' },
    { title: '名称', dataIndex: 'name', key: 'name' },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      key: 'customerName',
    },
    {
      title: '图片列表',
      dataIndex: 'imageUrls',
      key: 'imageUrls',
      render: (imageUrls,idx) => (<Flex justify={'flex-start'} gap={4} key={idx}>
          {imageUrls.map(url => buildMiniImg(url, null))}
        </Flex>),
    },
    { title: '服装', dataIndex: 'modelUrl', key: 'modelUrl', render: (url, record) => (buildMiniImg(url, record.modelName)) },
    { title: '提交人', dataIndex: 'operatorName', key: 'operatorName' },
    { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <span>
          <Button type="link" onClick={() => setPreviewItem(record)}>
            查看
          </Button>
          <Button type="link" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Popconfirm title={'确定要删除吗？'} onConfirm={() => handleDelete(record.id)}>
            <Button type="link" danger> 删除 </Button>
          </Popconfirm>
        </span>
      ),
    },
  ];

  return <PageContainer style={{ padding: 12 }}>
    <div className={'tag-management'} style={{ gap: 8 }}>
      <Flex style={{ marginBottom: 8 }}>
        <Button onClick={handleAdd}>新增</Button>
      </Flex>

      <Table
        columns={columns}
        dataSource={data}
        loading={loading}
        rowKey="id"
        className="tm-table"
        pagination={{
          current: page,
          pageSize: pageSize,
          total,
          onChange: handlePageChange,
          position: ['bottomCenter'],
        }}
      />

      {(add || opItem) &&
        <Modal open={true} onCancel={closeModal} maskClosable={false} centered closeIcon={null}
               onOk={form.submit} width={'calc(100vw * 0.8)'}>
          <Form form={form} onFinish={handleCommit} labelCol={{ span: 2 }} wrapperCol={{ span: 22 }}>
            <Form.Item name="id" rules={[{ required: false }]} hidden>
              <Input disabled />
            </Form.Item>
            <Form.Item name="name" label={'名称'} rules={[{ required: false }]}>
              <Input placeholder={'案例名称'} style={{width: 400}} />
            </Form.Item>
            <Form.Item name="customerId" label={'选择客户'} rules={[{ required: true }]}>
              <MasterSelector onChange={userId => form.setFieldValue('customerId', userId)} width={400} />
            </Form.Item>
            <UploadFormItem form={form} name="imageUrls" label={'精选图'} labelSpan={2} required={true} maxCount={50} />

            <LoraSelectFormItem form={form} name="modelId" required={false} label={'服装'}
                                btnTitle={'选择服装'} labelSpan={2} authorized={true} />

            <Form.Item name="topped" label={'是否置顶'} rules={[{ required: true }]} initialValue={false}>
              <Radio.Group options={[{ label: '否', value: false }, { label: '是', value: true }]}
                           optionType="button" buttonStyle="solid" />
            </Form.Item>

            <Form.Item name="memo" label="备注">
              <Input.TextArea rows={4} />
            </Form.Item>
          </Form>
        </Modal>
      }

      {previewItem &&
        <Modal open={true} onCancel={() => setPreviewItem(null)} maskClosable={true} centered
               width={'calc(100vw * 0.8)'} footer={null}>
          <Flex vertical gap={12}>
            <ShowRowBlock title={'案例名称'} value={previewItem.name} />
            <ShowRowBlock title={'客户名称'} value={previewItem.customerName} />
            <ShowRowBlock title={'服装名称'} value={<Flex gap={8} align={'center'}>{previewItem.modelName}
              {buildMiniImg(previewItem.modelUrl, null, 128)}
            </Flex>} />
            <ShowRowBlock title={'精选图片'} value={<Flex gap={8} wrap={'wrap'}>
              {previewItem.imageUrls.map(url => <div key={url} className={'pointer'} onClick={() => {
                setPreviewImage( url);
                setPreviewList(previewItem?.imageUrls)}}>
                {buildMiniImg(url, null, 256)}
              </div>)}
            </Flex>} />
            <ShowRowBlock title={'备注'} value={previewItem.memo} />
          </Flex>
        </Modal>
      }

      {previewImage &&
        <ImgPreview
          previewVisible={true}
          handleCancelPreview={() => {
            setPreviewImage(null);
            setPreviewList([]);
          }}
          previewImage={previewImage}
          previewImgs={previewList}
          previewIdx={previewList.findIndex(item => item === previewImage)}
          needSwitch={true}
        />
      }

    </div>
  </PageContainer>;
}

export default SalesSuccessStoriesMng