import React, { useEffect, useState } from 'react';
import { <PERSON>Container } from '@ant-design/pro-layout';
import { ProFormCaptcha } from '@ant-design/pro-form';
import { Button, Flex, Form, Input, Modal, Space } from 'antd';
import { BRAND, LOGO, PASSWORD_REGEX, PHONE_REGEX } from '@/constants';
import './index.less';
import '@/app.less';
import { fetchSalt, restPassword, sendCaptcha, verifyCaptcha } from '@/services/LoginController';
import { useNavigate } from 'react-router-dom';
import cryptoStr from '@/utils/cryptojs';

function isCanVerify(mobile: string, code: string) {
    return PHONE_REGEX.test(mobile) && code.length === 4;
}

function isCanCommit(mobile: string, pwd: string) {
    return PHONE_REGEX.test(mobile) && PASSWORD_REGEX.test(pwd);
}

const FindPwd = () => {
    const [canVerify, setCanVerify] = useState(false);
    const [canCommit, setCanCommit] = useState(false);
    const [mobile, setMobile] = useState<string>('');
    const [verifiedCode, setVerifiedCode] = useState(false);
    const [mobileError, setMobileError] = useState(false);
    const [pwd, setPwd] = useState('');
    const [code, setCode] = useState('');
    const [salt, setSalt] = useState('');
    const [showDialog, setShowDialog] = useState(false);
    const navigate = useNavigate();

    const handleMobileChange = (e: { target: { value: any; }; }) => {
        const mobile = e.target.value;
        setMobile(mobile);

        if (mobile && !PHONE_REGEX.test(mobile)) {
            setMobileError(true)
            return;
        }

        setMobileError(false)
    }

    const handleCodeChange = (e) => {
        let code = e.target.value;
        setCode(code);
    }

    const handleGetCaptcha = () => {
        if (!mobile || !PHONE_REGEX.test(mobile)) {
            return;
        }

        // @ts-ignore
        sendCaptcha(mobile).then((res) => {
            console.log(mobile, '发送验证码', res ? '成功' : '失败')
        });
    }

    const handlePwdChange = (e) => {
        setPwd(e.target.value)
    }

    const handleVerify = () => {
        if (!isCanVerify(mobile, code)) {
            return;
        }
        verifyCaptcha(mobile, code).then((res) => {
            if (!res) {
                return;
            }
            setVerifiedCode(true);

            fetchSalt().then((res) => {
                if (!res) {
                    return;
                }
                setSalt(res)
            })
        })
    }

    const handleCommit = () => {
        if (!isCanCommit(mobile, pwd)) {
            return;
        }

        const password = cryptoStr.aesEncrypt(pwd, salt);
        restPassword(password).then((r) => {
            if (!r) {
                return;
            }

            setShowDialog(true);
        });
    }

    const handleLogin = () => {
        navigate('/login?loginType=pwd');
    }

    //监听输入变化，设置是否能提交状态
    useEffect(() => {
        setCanVerify(isCanVerify(mobile, code))
        setCanCommit(isCanCommit(mobile, pwd))
    }, [mobile, code, pwd]);

    //获取盐值
    useEffect(() => {
        async function init() {
        }

        init().then(() => {
        });
    }, []);

    return (<PageContainer>
        <Form className={'body-container'}>
            <Flex>
                <Space>
                    <img src={LOGO} alt={'logo'} width={'96'} height={'96'} />
                    <img src={BRAND} alt={'logo'} width={'310'} height={'72'} />
                </Space>]
            </Flex>
            <div className={'login-form'}>
                <div className={'row-center-block'}>
                    {!verifiedCode &&
                      <div className={'text24 weight font-pf color-w'}>忘记密码</div>
                    }
                    {verifiedCode &&
                      <div className={'text24 weight font-pf color-w'}>设置新密码</div>
                    }
                </div>
                <div className={'row-center-block margin-top-24'}>
                    {!verifiedCode &&
                      <Input onChange={handleMobileChange} addonBefore="+86" size={'large'}
                             status={mobileError ? 'error' : ''} allowClear showCount
                             placeholder={'请输入注册手机号码'} maxLength={11} className={'login-input'} />
                    }
                    {verifiedCode &&
                      <div className={'text16 font-pf color-26'} style={{
                          marginLeft: '-8px', marginRight: '-8px',
                      }}>请设置8-16位新密码，须为数字与字母组合</div>
                    }
                </div>
                <div className={'row-center-block margin-top-12'}>
                    {!verifiedCode &&
                      <ProFormCaptcha
                        className={'captcha-input'}
                        allowClear
                        fieldProps={{ size: 'large', maxLength: 4 }}
                        captchaProps={{ size: 'large', disabled: !PHONE_REGEX.test(mobile) }}
                        // 手机号的 name，onGetCaptcha 会注入这个值
                        phoneName="phone"
                        name="captcha"
                        rules={[{ required: true, message: '请输入验证码' }]}
                        placeholder="请输入验证码"
                        onChange={handleCodeChange}
                        onGetCaptcha={async () => handleGetCaptcha()}
                      />
                    }
                    {verifiedCode &&
                      <Input.Password placeholder="设置新密码" size={'large'} onChange={handlePwdChange}
                                      className={'margin-bottom-24'} />
                    }

                </div>
                <div className={'row-center-block'}>
                    {!verifiedCode &&
                      <Button className={'login-btn'} type="text" disabled={!canVerify}
                              onClick={() => handleVerify()}>
                          <div className={'text16 font-pf weight color-w'}>下一步</div>
                      </Button>
                    }
                    {verifiedCode &&
                      <Button className={'login-btn'} type="text" disabled={!canCommit} onClick={handleCommit}>
                          <div className={'text16 font-pf weight color-w'}>确认并提交</div>
                      </Button>
                    }
                </div>
                <div className={'row-center-block margin-top-12'}>
                    <div className={'text16 font-pf color-2d margin-left-12'} onClick={handleLogin}>
                        返回登录
                    </div>
                </div>

            </div>
            <div className={'margin-top-24 text24 weight font-pf color-w'} style={{ letterSpacing: '0.8em' }}>
                AI 让服装拍摄更简单
            </div>
        </Form>
        <Modal title="" open={showDialog} centered mask={true} closable={false}
               footer={null} width={444}>
        <div className={'dialog-block'}>
                <p className={'text24 font-pf weight color-26'}>设置成功</p>
                <p className={'text16 font-pf color-26'}>新密码设置成功，请返回使用新密码重新登录</p>
                <div className={'dialog-operate'}>
                    <div className={'text16 font-pf color-2d'} onClick={handleLogin}>返回登录</div>
                </div>
            </div>

        </Modal>
    </PageContainer>);
};

export default FindPwd;
