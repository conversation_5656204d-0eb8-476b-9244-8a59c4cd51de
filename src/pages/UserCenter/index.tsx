import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Table, Button, Flex, Segmented, Pagination, DatePicker, message, Space } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import './index.less';
import { queryRelatedAccounts, RelatedAccounts, UserVO } from '@/services/UserController';
import {
  MerchantTopupVO,
  queryMerchantTopupByPage,
} from '@/services/OrderInfoController';
import { USER_INFO } from '@/constants';
import { exportPointUsage, queryPointUsageInfoByPage, UserPointUsageInfoVO } from '@/services/UserPointLogController';
import { download, maskPhoneNumber } from '@/utils/utils';
import IconFont from '@/components/IconFont';
import InvoiceDlg from '@/components/InvoiceDlg';
import {
  applyInvoice,
  InvoiceApplyForm,
  InvoiceTitleVO,
  queryMerchantInvoiceTitle, updateInvoiceTitle,
} from '@/services/InvoiceInfoController';
import { DownloadOutlined } from '@ant-design/icons';
import { downloadExcel } from '@/utils/downloadUtils';

const { RangePicker } = DatePicker;

const UserCenterPage: React.FC = () => {
  const [usageRecords, setUsageRecords] = useState<UserPointUsageInfoVO[]>([]);
  const [topupRecords, setTopupRecords] = useState<MerchantTopupVO[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [hasNextPage, setHasNextPage] = useState<boolean>(true);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(10);
  const [activeTab, setActiveTab] = useState<string>('使用记录'); // Track the active tab

  const [orderByCreateTime, setOrderByCreateTime] = useState('');
  const [dates, setDates] = useState([]);

  const [showInvoiceDlg, setShowInvoiceDlg] = useState(false);
  const [invoiceFormData, setInvoiceFormData] = useState<InvoiceApplyForm>();

  const [showInvoiceTitleDlg, setShowInvoiceTitleDlg] = useState(false);
  const [invoiceTitle, setInvoiceTitle] = useState<InvoiceTitleVO>();
  const [exporting, setExporting] = useState(false);

  // @ts-ignore
  const userInfo: UserVO = JSON.parse(localStorage.getItem(USER_INFO));

  const [relatedAccounts, setRelatedAccounts] = useState<RelatedAccounts>();

  const buildQuery = () => {
    let orderBy = 'id desc';
    if (orderByCreateTime === 'ascend') {
      orderBy = 'create_time asc';
    } else if (orderByCreateTime === 'descend') {
      orderBy = 'create_time desc';
    }

    let query = {
      pageNum: currentPage,
      pageSize: pageSize,
      orderBy: orderBy,
    };

    if (dates && dates.length > 1) {
      // @ts-ignore
      query['dateFrom'] = dates[0].format('YYYYMMDD');
      // @ts-ignore
      query['dateTo'] = dates[1].format('YYYYMMDD');
    }

    return query;
  };

  useEffect(() => {

    const query = buildQuery();

    console.log('查询参数:', query);

    //使用记录
    if (activeTab === '使用记录') {
      queryPointUsageInfoByPage(query).then(page => {
        if (page) {
          setHasNextPage(page.hasNextPage != null && page.hasNextPage);
          setUsageRecords(page?.list || []);
          setTotalCount(page?.totalCount || 0);
        }
      });

      //充值记录
    } else {
      queryMerchantTopupByPage(query).then(page => {
        if (page) {
          setHasNextPage(page.hasNextPage != null && page.hasNextPage);
          setTopupRecords(page?.list || []);
          setTotalCount(page?.totalCount || 0);
        }
      });
    }

    queryMerchantInvoiceTitle().then(res => {
      if (res && res.id){
        setInvoiceTitle(res);
      } else {
        setInvoiceTitle(undefined);
      }
    });

  }, [activeTab, currentPage, pageSize, orderByCreateTime, dates, showInvoiceDlg, showInvoiceTitleDlg]);

  useEffect(()=>{
    if (userInfo?.roleType === 'DISTRIBUTOR') {
      queryRelatedAccounts().then(accounts => {
        if (accounts){
          setRelatedAccounts(accounts);
        }
      })
    }
  }, []);

  const usageColumns: ColumnsType<UserPointUsageInfoVO> = [
    { title: '使用场景', dataIndex: 'usageScene', key: 'usageScene' },
    { title: '使用方式', dataIndex: 'usageWay', key: 'usageWay' },
    {
      title: '消耗数量', key: 'usedCount',
      render: (text, record) => {
        return (
          <Flex gap={'10px'} align={'center'}>

            {record.usedModelPoint != null && record.usedModelPoint != 0 && (
              <Flex gap={'4px'} align={'center'} style={{ fontSize: 14 }}>
                <IconFont type={'icon-icon_tupian'} style={{ fontSize: 24 }} />
                {record.usedModelPoint}张创作图
              </Flex>
            )}
            {record.usedGivePoint != null && record.usedGivePoint != 0 && (
              <Flex gap={'4px'} align={'center'} style={{ fontSize: 14 }}>
                <IconFont type={'icon-icon_tupian'} style={{ fontSize: 24 }} />
                {record.usedGivePoint > 0 ? '+' : ''}{record.usedGivePoint}张赠送创作图
              </Flex>
            )}
            {record.usedPoint != null && record.usedPoint != 0 && (
              <Flex gap={'4px'} align={'center'} style={{ fontSize: 14 }}>
                <IconFont type={'icon-icon_mousidian'} style={{ fontSize: 24 }} />
                {record.usedPoint > 0 ? '+' : ''}{record.usedPoint}缪斯点
              </Flex>
            )}

          </Flex>
        );
      },
    },
    { title: '创建时间', dataIndex: 'createTime', key: 'createTime', sorter: true },
    {
      title: '使用账号', key: 'operatorLoginId',
      render: (text, record) => {
        return (
          <Flex gap={'8px'}>
            {record.operatorRole !== 'ADMIN' &&
              <>
                <span>{maskPhoneNumber(record.operatorLoginId)}</span>
                <div style={{ borderRadius: '4px', background: '#F5F6F9', color: '#727375' }}>
                  {record.masterUser ? '主账号' : '子账号'}
                </div>
              </>
            }

            {record.operatorRole === 'ADMIN' &&
              <>
                <div style={{ borderRadius: '4px', background: '#F5F6F9', color: '#727375' }}>
                  系统
                </div>
              </>
            }
          </Flex>
        );
      },
    },
  ];

  const handleTableChange = (pagination, filters, sorter) => {
    console.log('handleTableChange:', sorter);
    if (sorter.field === 'createTime') {
      setOrderByCreateTime(sorter.order);
    }
  };

  const handleInvoice = (record: MerchantTopupVO) => {
    console.log(`开票操作，订单号: ${record.orderNo}`);
    setShowInvoiceDlg(true);
    setInvoiceFormData({
      orderId: record.id,
      orderAmount: record.payAmount,
      ...invoiceTitle
    });
  };

  const handleDownloadInvoice = (record: MerchantTopupVO) => {
    if (record.invoiceFileUrl) {
      download(record.invoiceFileUrl);
    }
  };

  const handleDateChange = (newDates) => {
    console.log('handleDateChange:', newDates);
    setDates(newDates);
    setCurrentPage(1);
  };

  const handleExportExcel = () => {
    setExporting(true)
    exportPointUsage(buildQuery()).then(res => {
      setExporting(false)
      downloadExcel(res, '消费记录.xlsx')
    });
  };

  function getInvoiceStatusDotColor(status: string) {
    if (status === 'NONE') {
      return '#D8D8D8';
    } else if (status === 'INVOICING') {
      return '#FF9900';
    } else if (status === 'INVOICE_END') {
      return '#1ECD04';
    }
    console.error('Invalid invoice status:', status);
    return '#D8D8D8';
  }

  const topupColumns: ColumnsType<MerchantTopupVO> = [
      {
        title: '套餐名称',
        key: 'productName',
        render: (_, record: MerchantTopupVO) => {
          return <span>{record?.productName || record?.productCode}</span>
        }
      },
      { title: '订单号', dataIndex: 'orderNo', key: 'orderNo' },
      {
        title: '金额',
        key: 'payAmount',
        render: (text: number, record: MerchantTopupVO) => {
          return <span>{record.payAmount}元</span>;
        },
      },
      {
        title: '支付方式',
        key: 'payType',
        render: (_, record: MerchantTopupVO) => {
          if (record.payType === 'alipay') {
            return <span>{'支付宝'}</span>;
          }

          if (record.payType === 'offlinePayment') {
            return <span>{'线下汇款'}</span>;
          }

          return <span>{'微信'}</span>;
        },
      },
      { title: '充值时间', dataIndex: 'createTime', key: 'createTime', sorter: true },
      { title: '过期日期', dataIndex: 'expireTime', key: 'expireTime' },
      {
        title: '付款账号', key: 'operatorUserLoginId', render: (_, record: MerchantTopupVO) => {

          return (
            <Flex gap={'8px'}>
              <span>{maskPhoneNumber(record.operatorUserLoginId)}</span>
              <div style={{ borderRadius: '4px', background: '#F5F6F9', color: '#727375' }}>
                {record.masterUser ? '主账号' : '子账号'}
              </div>
            </Flex>
          );

        },
      },

      {
        title: '开票状态', key: 'invoiceStatus', render: (_: any, record: MerchantTopupVO) => {
          return (
            <Flex gap={'8px'} align={'center'}>
              <div style={{
                width: 6,
                height: 6,
                backgroundColor: getInvoiceStatusDotColor(record.invoiceStatus),
                borderRadius: '50%',
              }} />
              <span>{record.invoiceStatusName}</span>
            </Flex>
          );
        },
      },
      {
        title: '操作',
        key: 'action',
        render: (_: any, record: MerchantTopupVO) => (
          <Space>
            {record.invoiceStatus === 'NONE' && (
              <a onClick={() => handleInvoice(record)}>去开票</a>
            )
            }

            {record.invoiceStatus === 'INVOICING' && (
              <span>-</span>
            )}

            {record.invoiceStatus === 'INVOICE_END' && record.invoiceFileUrl && (
              <a onClick={() => handleDownloadInvoice(record)}>下载发票</a>
            )}
          </Space>
        ),
      },

    ]
  ;


  return (
    <PageContainer>
      <div className={'user-center-container'}>

        <div className={'user-base-info'}>

          <IconFont type={'icon-icon_touxiang'} style={{ fontSize: 32 }} />

          {(() => {
            //主账号
            if (userInfo.userType === 'MASTER') {
              return (
                <div className={'user-base-right'}>
                  <div className={'user-right-account-container'}>
                    <div className={'user-login-account'}>{maskPhoneNumber(userInfo?.mobile)}</div>
                    <div className={'user-account-type'}>
                      主账号
                    </div>
                  </div>
                </div>
              );

              //子账号
            } else {
              return (
                <div className={'user-base-right'}>
                  <div className={'user-right-account-container'}>
                    <div className={'user-login-account'}>{maskPhoneNumber(userInfo?.mobile)}</div>
                    <div className={'user-account-type'}>
                      子账号
                    </div>
                  </div>
                  <div className={'master-account-belongs'}>所属主账号：{maskPhoneNumber(userInfo?.masterLoginId)}</div>
                </div>
              );
            }
          })()}

        </div>

        {userInfo?.roleType === 'DISTRIBUTOR' &&
          <Flex vertical={true} gap={8}>
            {relatedAccounts && relatedAccounts?.relatedMerchantAccount &&
              <div>
                {`关联商家账号：${relatedAccounts?.relatedMerchantAccount?.nickName}(${relatedAccounts?.relatedMerchantAccount?.mobile})`}
              </div>
            }

            {relatedAccounts && relatedAccounts?.relatedDemoAccount &&
              <div>
                {`关联演示账号：${relatedAccounts?.relatedDemoAccount?.nickName}(${relatedAccounts?.relatedDemoAccount?.mobile})`}
              </div>
            }
          </Flex>
        }

        {invoiceTitle !== null && (
          <Button type="default" style={{width: 100}} onClick={() => setShowInvoiceTitleDlg(true)}>
            编辑发票抬头
          </Button>
        )}

        <div
          style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', margin: '24px 0 24px 0' }}>
          <Segmented
            options={['使用记录', '充值记录']}
            style={{ backgroundColor: '#E1E3EB', width: 'auto', height: 32 }}
            value={activeTab}
            onChange={(val) => {
              setActiveTab(val as string);
              setCurrentPage(1);
              setHasNextPage(true);
            }}
          />

          <Flex gap={12}>
            <RangePicker onChange={handleDateChange} placeholder={['开始日期', '结束日期']}
                         style={{ width: 320, height: 32 }} />

            {activeTab === '使用记录' &&
              <Button icon={<DownloadOutlined />} onClick={() => handleExportExcel()} loading={exporting}>
                导出excel
              </Button>
            }
          </Flex>

        </div>

        {activeTab === '使用记录' && (
          <Table columns={usageColumns}
                 dataSource={usageRecords}
                 rowKey="pointLogId"
                 className={'user-center-table'}
                 onChange={handleTableChange}
                 pagination={false} />
        )}

        {activeTab === '充值记录' && (
          <Table columns={topupColumns}
                 dataSource={topupRecords}
                 rowKey="id"
                 className={'user-center-table'}
                 pagination={false}
                 onChange={handleTableChange} />

        )}

        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={totalCount}
          onChange={(page: number, pageSize: number) => {
            console.log('Pagination onChange:', page, pageSize);
            setCurrentPage(page);
            setPageSize(pageSize);
          }}
          showTotal={(total, range) => `共 ${totalCount} 条`}
          showSizeChanger // 允许用户更改每页显示条数
          showQuickJumper // 允许用户快速跳转到某一页
          onShowSizeChange={(current, size) => {
            console.log('Pagination onShowSizeChange:', current, size);
            setPageSize(size);
            setCurrentPage(1);
          }}
          pageSizeOptions={['10', '20', '50', '100']}
          style={{ marginTop: '16px', textAlign: 'center' }}
        />

      </div>

      {/*开票*/}
      {showInvoiceDlg &&
        <InvoiceDlg<InvoiceApplyForm>
          visible={showInvoiceDlg}
          formData={invoiceFormData}
          onCancel={() => {
            setShowInvoiceDlg(false);
          }}
          onConfirm={(item: InvoiceApplyForm) => {

            //申请开票
            applyInvoice({
              title: item,
              orderId: item.orderId
            }).then((res) => {
              if (res){
                message.success('申请开票成功');
                setShowInvoiceDlg(false);
                setInvoiceFormData(undefined);
              }
            });

          }}
          title="开票"
          confirmText="申请开票"
        />
      }

      {/*编辑发票抬头*/}
      {showInvoiceTitleDlg &&
        <InvoiceDlg<InvoiceTitleVO>
          visible={showInvoiceTitleDlg}
          formData={invoiceTitle}
          onCancel={() => {
            setShowInvoiceTitleDlg(false);
          }}
          onConfirm={(item: InvoiceTitleVO) => {
            //更新发票抬头
            updateInvoiceTitle(item).then((res) => {
              if (res){
                message.success('操作成功');
                setShowInvoiceTitleDlg(false);
              }
            });
          }}
          title="编辑发票抬头"
          confirmText="保存"
        />
      }

    </PageContainer>
  );
};

export default UserCenterPage;