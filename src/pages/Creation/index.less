@import "@/app";

@body-height: calc(100vh - @navigation-height - @tab-bar-height);
@work-block-width: 644px;
@work-item-fixed-width: calc((@work-block-width - 2 * 12px - 2 * 8px - 4 * 12px) / 5);
@look-table-height: 74px;

.page-container {
  background: #F5F6F9;

  .ant-row {
    height: 100vh;
  }

  .ant-col {
    transition: all 0.3s ease;

    &:first-child {
      padding-right: 16px;
    }

    &:last-child {
      padding-left: 16px;
    }
  }

  .ant-card {
    overflow: hidden;
    height: 100%;

    .ant-card-body {
      overflow: auto;
    }
  }

  .ant-slider {
    height: 100vh;
    margin: 0 16px;

    // 默认隐藏标记文本
    .ant-slider-mark-text {
      display: none !important; // 使用 `!important` 确保覆盖默认样式
    }
  }
}

.ant-pro-page-container {
  display: flex;
  height: 100%;
}


.right-block {
  padding-right: 12px;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;
}

.row-space-block {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.row-align-items {
  align-items: flex-start !important;
}

.slider-desc-block {
  margin: 16px 0;
  width: 378px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  text {
    font-family: "PingFang SC";
    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0em;

    font-variation-settings: "opsz" auto;
    color: #3D3D3D;
  }
}

.image-container {
  width: 100%;
  height: @body-height;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.image-container-processing {
  height: calc(@body-height - 54px) !important;
}

.width-100 {
  width: 100%;
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 8px;
}

.creative-type {
  height: 28px;
  border-radius: 4px;
  padding: 4px;
  background: #EDEEF3;
}

.image-block {
  position: relative;
  width: calc((100% - 4 * 8px) / 5) !important;
  height: auto; // 保持图片比例不变
}

.repair-hands-tag {
  position: absolute;
  left: 4px;
  top: 4px;
  height: 22px;
  width: auto;
  min-width: 22px;
  border-radius: 4px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 2px;
  background: rgba(0, 0, 0, 0.6);
  z-index: 4;
}

.output-image {
  width: 100%;
  height: auto;
  cursor: pointer;
}

.ant-page-header-no-children {
  height: 0;
}

.ant-pro-page-container-children-container {
  height: 0;
}

.row-container {
  //padding-left: 16px;
  width: 100%;
  height: calc(@body-height);
  background: #FFFFFF;
}

.row-container-flow {
  height: calc(@body-height - @flow-height) !important;
}

.row-container-look {
  height: calc(@body-height - @look-table-height) !important;
}

.work-block {
  padding: 16px 8px 0 8px;
  background: linear-gradient(0deg, #FFFFFF, #FFFFFF), #FFFFFF;
  height: 100%;
  max-height: 100vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-content: flex-start;
  justify-content: flex-start;
  scrollbar-gutter: stable;
}

.work-block-fixed {
  width: calc( @work-block-width + @scroll-width);
}

.work-item-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: auto;
  border-radius: 8px;
  background: #F5F6F9;
  padding: 12px;
  gap: 8px;
}

.work-item-group {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: 100%;
}

.work-item-block {
  width: 612px;
  border-radius: 8px;
  max-height: 620px;
  opacity: 1;
  overflow-y: auto;
  will-change: transform;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;

  //padding: 12px;
  gap: 12px;
  align-self: stretch;
  background: #F5F6F9;
  z-index: 1;
}

.work-item-lora {
  width: 100%;
  border-radius: 8px;
  opacity: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-self: stretch;
  background: #F5F6F9;
  z-index: 1;
  
  .ant-segmented {
    margin-bottom: 8px;
    background-color: #E1E3EB;
    height: 32px;
    
    .ant-segmented-item {
      border-radius: 4px;
      transition: all 0.3s;
      height: 100%;
      
      .ant-segmented-item-label {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 78px;
      }
    }
    
    .ant-segmented-item-selected {
      background-color: #FFFFFF;
      color: #366EF4;
      font-weight: 500;
    }
  }
}

.work-item-empty-block {
  min-height: 134px;
}

.work-item-config-block {
  min-height: 160px;
  max-height: 528px;
}

.work-item {
  width: calc((100% - 24px - 4 * 12px) / 5);
  min-width: 114px;
  height: auto !important;
  border-radius: 8px;
  opacity: 1;
  box-sizing: border-box;
  border: 0.5px solid #D8D8D8;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden; /* 防止内容溢出 */
  cursor: pointer;
}

.work-item-fixed {
  width: @work-item-fixed-width !important;
  min-width: @work-item-fixed-width !important;
}

.work-item-fixed-height {
  height: @work-item-fixed-width !important;
  //padding-bottom: 100%;
}

.work-item-fixed-height-modal {
  height: calc(@work-item-fixed-width + 8px) !important;
}

.work-element-more {
  position: relative;
  width: 100%;
  height: 100%;
}

.work-element-more-block {
  background: radial-gradient(50% 50% at 50% 50%, rgba(0, 9, 170, 0.5) 0%, rgba(0, 29, 176, 0.5) 100%);
  backdrop-filter: blur(2px);
}

.work-item-selected {
  background: #E1F0FF !important;
  border: 2px solid #2D7DFF !important;;

  text {
    color: #2D7DFF !important;
  }
}

.work-image-block {
  position: relative;
  width: 100%; /* 控制容器的宽度 */
  height: 0;
  padding-top: 133.33%; /* 3:4 比例 */
  overflow: hidden;
}

.work-item-block-desc {
  position: absolute;
  bottom: 4px;
  right: 4px;

  border-radius: 4px;
  display: flex;
  flex-direction: row;
  padding: 2px;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1;
}

.more-lora-block {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.work-item-image {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.work-item-image-scene {
  width: auto;
  height: @work-item-fixed-width;
  object-fit: contain;
}

.lora-item-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.work-item-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
}

.work-item-input {
  width: 184px;
  height: 36px;
  border-radius: 8px;
  opacity: 0.8;

  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;

  background: #FFFFFF;

  box-sizing: border-box;
  border: 0.5px solid #D8D8D8;
}

.image-size-block {
  width: 145px;
  max-width: 196px;
  height: 58px;
  border-radius: 8px;
  opacity: 0.8;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 16px;
  gap: 4px;
  background: #FFFFFF;
  box-sizing: border-box;
  border: 0.5px solid #D8D8D8;
  z-index: 0;
  cursor: pointer;
}

.image-size-block-LG {
}

.image-size-proportion-34 {
  width: 30px;
  height: 40px;
  border-radius: 4px;
  opacity: 1;
  background: #DFE1E8;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.image-size-proportion-11 {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  opacity: 1;
  background: #DFE1E8;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.image-size-title {
  margin-left: 8px;
  width: 64px;
  height: 41px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  gap: 1px;
  z-index: 1;
}

.fixed-tab-bar {
  width: 51%;
  height: 72px;
  opacity: 1;
  z-index: 98;

  /* 自动布局 */
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  padding: 8px 32px 8px 16px;
  align-self: stretch;
  background: #FFFFFF;
  box-sizing: border-box;
  border-width: 1px 0px 0px 0px;
  border-style: solid;
  border-color: #E8EAF0;
}

.fixed-tab-bar-fixed {
  width: @work-block-width;
}

.fixed-tab-content {
  width: 100%;
  gap: 16px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}

.footer-bar {
  padding-left: 16px;
  margin-left: -12px;
  background: #FFFFFF;
}

.create-btn {
  width: 212px;
  height: 46px;
  border-radius: 8px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px 32px;
  border: 0;
  color: #FFFFFF;
  border-color: transparent !important;
  z-index: 1;
}

.create-btn:disabled {
  color: #FFFFFF;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.6)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%);
}

.create-btn:enabled {
  background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.create-btn:not(:disabled):hover, .create-btn:not(:disabled):focus {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.create-btn:not(:disabled):active {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.output-block {
  position: absolute;
  width: 100%;
  right: 0;
  height: calc(100vh - @navigation-height);
  background-color: #F5F6F9;
  //box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  transition: width 0.3s ease-in-out;
  overflow: hidden;
  z-index: 99;
}

.output-block-fixed {
  width: calc(100% - @work-block-width);
}

.output-block-flow {
  height: calc(100vh - @navigation-height - @flow-height) !important;
}

.center-block {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.normal-image-block {
  padding-left: 8px;
  padding-right: 8px;
}

.example-images-block {
  border-radius: 8px;
  padding: 8px;
  background: linear-gradient(180deg, #EDEAF1 0%, #F6F3EE 100%), #FFFFFF;
  border: 1px solid #FFCCBA;
}

.example-images-title {
  width: 100%;
  height: 26px;
  border-radius: 113px;
  padding: 4px 8px;
  background: linear-gradient(90deg, #FFCCB9 0%, rgba(214, 206, 255, 0) 100%);
}

.update-material-btn {
  border-color: #B9C7FA;
}

.message-card {
  width: 100%;
  height: 54px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;

  box-sizing: border-box;
  border-width: 0 0 0.5px 0;
  border-style: solid;
  border-color: #D8D8D8;
  z-index: 0;
}

.detail-download-block {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.download-btn {
  width: 120px;
  height: 33px;
  border-radius: 8px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  background: #000000;
  z-index: 3;

  .ant-dropdown-trigger {
    color: #FFFFFF;
  }
}

.download-btn:not(:disabled):hover, .download-btn:not(:disabled):focus {
  background: #313132 !important;
}

.detail-image-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  z-index: 1001;
}

.detail-image-block {
  position: fixed;
  height: 100%;
  width: auto;
  z-index: 1002;
}

.detail-image {
  height: 100vh;
  width: auto;
  min-width: 100%;
  z-index: 1003;
}

.detail-image-download {
  width: auto !important;
}

.loading-img {
  animation: spin 2s linear infinite;
}

.detail-round {
  width: 64px;
  height: 64px;
  border-radius: 205px;
  background: rgba(0, 0, 0, 0.4);
  opacity: 1;
  z-index: 1002;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.detail-round:hover .icon-jiantou:hover {
  color: #498FFF;
}

.detail-round-left {
  position: fixed;
  left: 40px;
  top: 400px;
}

.detail-round-right {
  position: fixed;
  right: 40px;
  top: 400px;
}

.icon-jiantou {
  font-size: 64px;
  z-index: 10003;
  color: #FFFFFF;
}

.scene-type-row {
  width: 100%;
  opacity: 1;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.scene-type-item {
  min-width: 88px;
  height: 30px;
  border-radius: 32px;
  opacity: 1;
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  gap: 10px;
  background: #FFFFFF;
  font-size: 13px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: 0em;
  text-align: center;
  color: #727375;
  border: none; /* 移除默认边框 */

  /* 确保 Button 组件的 padding 不影响居中效果 */

  .ant-btn {
    padding: 0;
    display: flex;
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
  }
}

.scene-type-item-checked {
  background: #E1E6FF;
  color: #1664FF;
  font-weight: 500;
}

.scene-inner-container {
  //min-height: 252px;
  width: 100%;
  border-radius: 8px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  align-self: stretch;
  background: #F5F6F9;

  //padding: 12px 4px 12px 12px;
}

.no-min-height {
  min-height: 0 !important;
}

.scene-item-block {
  width: 100%;
  max-height: 620px;
  min-height: 138px;
  opacity: 1;
  overflow-y: auto;
  will-change: transform;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 12px;
  align-self: stretch;
  z-index: 1;
  scrollbar-width: none;
}

.element-item-block {
  max-height: calc(136px * 3 + 8px * 3 + 2px);
}

.slider-bar {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  height: auto;
  padding: 8px 16px 16px 16px;

  gap: 8px;
  align-self: stretch;

  background: rgba(255, 255, 255, 0.8);

  box-sizing: border-box;
  border-width: 1px 0 0 0;
  border-style: solid;
  border-color: #E8EAF0;

  backdrop-filter: blur(15px);
  z-index: 100;
}

.cancel-btn {
  height: 38px;
  border-radius: 8px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px 32px;
  box-sizing: border-box;
  border: 1px solid rgba(0, 0, 0, 0.2) !important;
  box-shadow: none !important;
  background-color: #FFFFFF;
  z-index: 1;
}

.cancel-btn:hover, .cancel-btn:focus {
  background-color: #FFFFFF !important;
}

.col-start-block {
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  box-sizing: border-box;
  border-width: 0 0 0.5px 0;
  border-style: solid;
  border-color: #D8D8D8;
}

.repair-failed-warning {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 3;
}

.repair-retry-btn {
  position: absolute;
  bottom: 12px;
  right: 4px;
  left: 4px;
  width: calc(100% - 8px);
  height: 34px;
  z-index: 4;
}

.repair-delete-icon {
  position: absolute;
  top: 4px;
  right: 4px;
  height: 22px;
  width: 22px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.6);
  cursor: pointer;
  z-index: 4;
}

.work-item-container-fix {
  height: 480px;
}
.element-block-item-block {
  display: flex;
  width: 100%;
}

.element-block-item-name {
  display: flex;
  width: 80%;
  justify-content: center;
}
.element-block-item-block-right {
  display: flex;
  width: 20px;
  height: 20px;
  position: absolute;
  bottom: 4px;
  right: 4px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.6);
  align-items: center;
  justify-content: center;

  .element-block-favor-button {
    svg {
      width: 20px;
      height: 20px;
    }
  }
}

.look-image {
  flex-direction: column;
  width: 100%;

  .work-block-fixed {
    padding-top: 0;
  }
  .output-block {
    position: absolute;
    width: 100%;
    right: 0;
    height: calc(100vh - @tab-bar-height - @tab-bar-height) !important;
    background-color: #F5F6F9;
    //box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    transition: width 0.3s ease-in-out;
    overflow: hidden;
    z-index: 99;
  }
}


.creation-scene-header {
  padding: 2px;
  border-radius: 8px 8px 0 0;
  height: 40px;
  background: linear-gradient(180deg, #F5F6F9 50%, #E4E6ED 100%), #F5F6F9;

  .ant-tabs-nav {
    margin: 0 !important;
  }

  .ant-tabs-top {
    height: 100px !important;
  }

  .ant-tabs-tab {
    width: calc((@work-block-width + @scroll-width - 16px - 20px) / 4);
    padding: 6px;
    justify-content: center;
  }

  .ant-tabs-tab-btn {
    padding: 2px;
    width: 100% !important;
    height: 100% !important;
  }

  .ant-tabs-tab-active {
    border-radius: 8px 8px 0 0;
    background: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0.6164) 64%, rgba(255, 255, 255, 0) 100%);

    .ant-tabs-tab-btn {
      color: #366EF4; // 选中标签文字颜色
    }
  }

  .toolkit-content {
    width: 100%;
  }

  .ant-tabs-ink-bar {
    border-radius: 2px !important; /* 圆角大小 */
    height: 4px !important; /* 可选：调整指示器高度 */
    background-color: #366EF4; // 选中标签文字颜色
  }
}

.creation-scene-header-look {
  .ant-tabs-tab-active {
    background: linear-gradient(180deg, #E4E4FF 0%, rgba(228, 228, 255, 0.2) 100%), linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0.6164) 64%, rgba(255, 255, 255, 0) 100%);

    .ant-tabs-tab-btn {
      color: #873BFA !important; // 选中标签文字颜色
    }
  }
  .ant-tabs-ink-bar {
    background-color: #873BFA !important; // 选中标签文字颜色
  }
}

.creation-scene-header-real-scene-shooting {
  .ant-tabs-tab-active {
    background: linear-gradient(180deg, #DCFFEE 0%, rgba(220, 255, 238, 0.2) 100%), linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0.6164) 64%, rgba(255, 255, 255, 0) 100%);

    .ant-tabs-tab-btn {
      color: #27C165 !important; // 选中标签文字颜色
    }
  }
  .ant-tabs-ink-bar {
    background-color: #27C165 !important; // 选中标签文字颜色
  }
}

.creation-scene-header-model-show {
  .ant-tabs-tab-active {
    background: linear-gradient(180deg, #E5F6FF 0%, rgba(229, 246, 255, 0.2) 100%), linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0.6164) 64%, rgba(255, 255, 255, 0) 100%);

    .ant-tabs-tab-btn {
      color: #00BDFF !important; // 选中标签文字颜色
    }
  }
  .ant-tabs-ink-bar {
    background-color: #00BDFF !important; // 选中标签文字颜色
  }
}