import React, { useState } from 'react';
import { TabsProps, Flex, Tabs } from 'antd';
import Creation from '@/pages/Creation/index';
import HistoryPage from '@/pages/History';

const LookImage: React.FC = ({}) => {

  const [toolType, setToolType] = useState<string>('creation');

  const toolItems :TabsProps['items'] = [
    {key: 'creation', label: '创作'},
    {key: 'history', label: '历史记录'},
  ];

  // 组件映射
  const componentMap = {
    creation: Creation,
    history: HistoryPage
  }

  const params = {
    creation : {
      type: 'CUSTOM',
      bizType: 'LOOK',
    }, 
    history : {
      bizType: 'LOOK',
    }
  }
  
  // 动态获取当前组件
  const CurrentComponent = toolType ? componentMap[toolType] : null;

  return (
    <Flex className={'look-image'}>
      <div className="toolkit-form-container">
        <Flex vertical className={'toolkit-form-body'}>
          <Tabs activeKey={toolType} items={toolItems} onChange={setToolType} size={'large'}
                indicator={{ size: 102 }} />
        </Flex>
      </div>
      {CurrentComponent && <CurrentComponent {...params[toolType]} />}
    </Flex>
  );
};

export default LookImage;
