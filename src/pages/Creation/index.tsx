import React, { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import debounce from 'lodash/debounce';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Checkbox, Flex, Input, InputNumber, message, Segmented, Tabs, Tooltip, Select, Dropdown } from 'antd';
import {
  ALL_LORAS_NEW,
  BACK_DEMO,
  BG_ALL_MODELS,
  BG_ALL_SCENES,
  DEBOUNCE_DELAY,
  EXPERIENCE_POINT,
  FRONT_DEMO,
  IMAGE_POINT,
  IMAGE_POINT_CHANGE_EVENT,
  IS_TRIAL_ACCOUNT,
  LOWER_BODY_ICON,
  MINI_LOADING_ICON,
  MORE_LORA_ICON,
  NEED_GUIDE,
  UPPER_BODY_ICON,
  WHOLE_BODY_ICON,
} from '@/constants';
import './index.less';
import '@/app.less';
import { capitalizeFirstLetter, deepCopy } from '@/utils/utils';
import { ALLBizTypes, BizType, CreativeStatus, LoraType, startCreative } from '@/services/CreativeController';
import {
  emptyClothCollocation,
  getClothMarkIcon,
  isClothMarkedColor,
  MaterialModel,
  queryMaterialModelByPage,
  querySystemModelList,
} from '@/services/MaterialModelController';
import { useLocation, useNavigate } from 'react-router-dom';
import { predict, PredictVO, queryImagePoint } from '@/services/PointController';
import { AllAgeRanges, CreationClothTypeCheckBoxOptions, ElementConfig, getElementConfig } from '@/services/ElementController';
import NewUserGuide from '@/components/NewUserGuide';
import Flow from '@/components/Flow/flow';
import LoraSelector from '@/components/LoraSelector';
import PredictBlock from '@/components/Creative/PredictBlock';
import TopupModal from '@/pages/Topup/Topup';
import ElementWithTypeBlock from '@/components/Creative/ElementWithTypeBlock';
import ProportionBlock from '@/components/Creative/ProportionBlock';
import MasterSelector from '@/components/MasterSelector';
import { queryCustomerMusePoint, queryLoras4Distributor } from '@/services/DistributorController';
import HalfSelectItemBlock from '@/components/Creative/HalfSelectItemBlock';
import ClothCollocationBlock from '@/components/Creative/ClothCollocationBlock';
import IconFont from '@/components/IconFont';
import TaskOutputBlock, { TaskOutputBlockRef } from '@/components/Creative/TaskOutputBlock';
import ClothColorSelector from '@/components/Creative/ClothColorSelector';
import OutputBlock, { OutputBlockRef } from '@/components/Creative/OutputBlock';
import { ExperienceModelOpenDetail, queryAllExperienceModelOpenCfg } from '@/services/SystemController';
import { isVipUser, queryUserProfileByKey, setUserProfileByKey } from '@/services/UserController';
import FaceExpressionBlock from '@/components/Creative/FaceExpressionBlock';
import SceneAccessoriesBlock from '@/components/Creative/SceneAccessoriesBlock';
import ClothColorBlock from '@/components/Creative/ClothColorBlock';

interface CreativeProps {
  type: LoraType;
  bizType?: BizType;
}

const CREATIVE_IMAGE_NUM = "creative_image_num";

const BIZ_TYPE_CLASS_MAP = {
  'LOOK': {
    tabs: 'creation-scene-header-look',
    outputBg: 'task-image-bar-look',
    outputBgImg: 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/img-look.png',
  },
  'REAL-SCENE-SHOOTING': {
    tabs: 'creation-scene-header-real-scene-shooting',
    outputBg: 'task-image-bar-real-scene-shooting',
    outputBgImg: 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/img-real-scene-shooting.png',
  },
  'MODEL-SHOW': {
    tabs: 'creation-scene-header-model-show',
    outputBg: 'task-image-bar-model-show',
    outputBgImg: 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/img-model-show.png',
  },
};

export const removeFaceConfig = (current: {}, logoPosition: string, configs: Array<ElementConfig>) => {
  if (!logoPosition || logoPosition !== 'back') {
    return;
  }

  let faceId = '';
  configs.forEach(config => {
    if (config.configKey === 'FACE') {
      faceId = config.id + '';
    }
  });

  Object.keys(current).forEach(key => {
    if (key === faceId) {
      delete current[key];
    }
  });
};

function isCanSave(data: any, imagePoint: number, type: LoraType, predictVO: PredictVO | null, sceneKey: string) {
  // @ts-ignore
  const result = data.loraId && data.proportion && data.imageNum > 0
    && Object.keys(data.configs).every(key => {
      return data.configs[key] !== null;
    })
    && (data.configs[sceneKey] || data.customScene.length >= 20);
  if (result === null || !result) {
    return false;
  }

  if (type === 'SYSTEM') {
    return imagePoint >= data.imageNum;
  }

  return predictVO !== null && !predictVO.needTopup;
}

export const resetData = (data, logoPosition, dataConfigs) => {
  const { configs, ...others } = data;

  removeFaceConfig(configs, logoPosition, dataConfigs);

  const newConfigs = {};
  for (let key in configs) {
    if (configs[key]) {
      newConfigs[key] = configs[key].map(item => item.id);
    }
  }

  return { configs: newConfigs, ...others };
};

const useMultiTask = true;
const Creative: React.FC<CreativeProps> = ({ type = 'CUSTOM' }) => {
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const isTrialAccount = sessionStorage.getItem(IS_TRIAL_ACCOUNT) === 'Y';

    const emptyData = {
      loraId: null,
      configs: {},
      imageNum: (['OPERATOR', 'DISTRIBUTOR'].includes(userInfo.roleType) || type === 'SYSTEM') ? 10 : 20,
      proportion: isTrialAccount && type === 'CUSTOM' ? 'THREE_FOUR_LG_N' : (type === 'CUSTOM' ? 'P_1152_1536' : 'THREE_FOUR'),
      position: 'front view',
      wholeBody: true,
      bodyType: 'whole body',
      clothCollocation: emptyClothCollocation,
      colorIndex: null,
      clothColor: null,
      enableAntiBlurLora: false,
      enableNewModel: true,
      customScene: '',
      expression: '',
      useSceneAccessories: 'N',
      bizType: 'ALL',
      onlyExperimental: false,
    };

    const [sliderValue, setSliderValue] = useState(12);
    const [data, setData] = useState(emptyData);
    const [canSave, setCanSave] = useState(false);
    const [loraList, setLoraList] = useState<Array<MaterialModel>>([]);
    const [configs, setConfigs] = useState<Array<ElementConfig>>([]);
    const [status, setStatus] = useState<CreativeStatus>('INIT');
    const [imagePoint, setImagePoint] = useState(0);
    const [isOwner, setIsOwner] = useState(true);
    //当前选择的场景分类code，默认选中居家生活
    const [showGuide, setShowGuide] = useState(false);
    const [showFlowSteps, setShowFlowSteps] = useState(false);
    const [hasMoreLora, setHasMoreLora] = useState(false);
    const [showLoraSelector, setShowLoraSelector] = useState(false);
    const [predictVO, setPredictVO] = useState<PredictVO | null>(null);
    const [showTopupModal, setShowTopupModal] = useState(false);
    const [loraClothType, setLoraClothType] = useState('');
    const [sceneClothType, setSceneClothType] = useState('');
    const [searchKey, setSearchKey] = useState<string | null>(null);
    const [searchValue, setSearchValue] = useState<string>(''); // 用于输入框显示的值
    const [searchClothType, setSearchClothType] = useState<string>();
    const [searchAgeRange, setSearchAgeRange] = useState<string>();
    const [commiting, setCommiting] = useState(false);
    const [experienceModelOpenCfg, setExperienceModelOpenCfg] = useState<Array<ExperienceModelOpenDetail>>([]);
    const [vipUser, setVipUser] = useState(false);
    const [needShowWholeBody, setNeedShowWholeBody] = useState(false);
    const [typeSelectOpen, setTypeSelectOpen] = useState(false);
    const [ageSelectOpen, setAgeSelectOpen] = useState(false);

    const [principalId, setPrincipalId] = React.useState<null | number>(null);
    const [bizType, setBizType] = useState<BizType>('ALL');

    // 当前选中的服装
    const [currentSelectedLora, setCurrentSelectedLora] = useState<any | null>(null);

    // 防抖搜索函数
    const debouncedSearchKey = useCallback(
      debounce((value: string) => {
        setSearchKey(value || null);
      }, DEBOUNCE_DELAY),
      []
    );


    const step1Ref = useRef(null);
    const step2Ref = useRef(null);
    const step3Ref = useRef(null);
    const outputBlockRef = useRef(null);

    const outputRef = useRef<OutputBlockRef>(null);
    const taskOutputRef = useRef<TaskOutputBlockRef>(null);

    //从其它页面跳转过来的，链接中指定modelId & isOwner
    const [loraIdInQueryParam, setLoraIdInQueryParam] = useState<string | null>('');
    const [sceneKey, setSceneKey] = useState<string>('');

    const predictLora = async (loraId: number | null, imageNum: number, proportionType: string | null = null) => {
      if (loraId === null) {
        setPredictVO(null);
        return;
      }
      if (imageNum === null || type === 'SYSTEM') {
        return;
      }
      predict('CREATE_IMAGE', imageNum, loraId, false, proportionType).then((res) => {
        if (res) {
          setPredictVO(res);
        }
      });
    };

    // 服装选择
    const handleLoraChange = (value: React.SetStateAction<number>) => {
      if (value === data.loraId) {
        return;
      }
      setPredictVO(null);
      const copy = deepCopy(data);
      copy.loraId = value;

      // 立即清空模特和场景的选择，防止不匹配的情况
      const faceConfig = configs.find(config => config.configKey === 'FACE');
      const sceneConfig = configs.find(config => config.configKey === 'SCENE');

      if (faceConfig && copy.configs[faceConfig.id]) {
        copy.configs[faceConfig.id] = null;
      }

      if (sceneConfig && copy.configs[sceneConfig.id]) {
        copy.configs[sceneConfig.id] = null;
      }

      setData(copy);
    };

    const handleFaceChange = (configId: number, value: Array<ElementConfig> | null, isPreset?: boolean) => {
      if (isPreset) {
        data.configs[configId] = value;
        return;
      }
      const copy = deepCopy(data);
      copy.configs[configId] = value;
      setData(copy);

      if (value && value.length > 0) {
        setSceneClothType(getSceneClothType(value[0]));
      }
    };

    const handleSceneChange = (configId: number, value: Array<ElementConfig> | null, isPreset?: boolean, customScene?: string) => {
      if (isPreset) {
        data.configs[configId] = value;
        return;
      }
      setData((pre) => {
        const newConfigs = { ...pre.configs };
        let newCustomScene = pre.customScene;
        if (value === null) {
          delete newConfigs[configId];
          newCustomScene = customScene || '';
        }
        if (!customScene || customScene.length === 0) {
          newConfigs[configId] = value;
          newCustomScene = '';
        }
        return {
          ...pre,
          configs: newConfigs,
          customScene: newCustomScene,
        };
      });
    };

    const handleNumberChange = (value: number | null) => {
      const num = value && value > (type === 'CUSTOM' ? 4 : 10) ? value : (type === 'CUSTOM' ? 4 : 10);
      const copy = deepCopy(data);
      copy.imageNum = num;
      setData(copy);
    };

    const handleProportionChange = (value: string) => {
      data.proportion = value;

      const copy = deepCopy(data);
      copy.proportion = value;
      setData(copy);
    };

    const handleChangeColor = (value: number | null | undefined) => {
      const copy = deepCopy(data);
      copy.colorIndex = value;
      setData(copy);
    };

    const handleChangeBizType = (bizType) => {
      setBizType(bizType);
      setData({ ...data, bizType });
    };

    function changePrincipal(id: number) {
      setPrincipalId(id);
      if (!id) {
        setImagePoint(0);
        return;
      }
      queryCustomerMusePoint({ customerMasterId: id }).then(res => {
        if (res && res.imagePoint !== null) {
          setImagePoint(res.imagePoint);
        }
      });
    }

    const getSceneClothType = (face: ElementConfig) => {
      if (!loraClothType) {
        return undefined;
      }
      // 非男女同款的直接返回
      if (loraClothType !== 'unisex') {
        return capitalizeFirstLetter(loraClothType);
      }
      const type = face.type.find(t => t === 'male-model' || t === 'female-model');
      if (!type) {
        return capitalizeFirstLetter(loraClothType);
      }

      return capitalizeFirstLetter(type?.split('-')[0]);
    };

    const navigate = useNavigate();
    const handleUploadMaterial = () => {
      navigate('/upload');
    };

    const handleCreate = () => {
      if (predictVO && predictVO.needTopup) {
        setShowTopupModal(true);
        return;
      }

      if (commiting || !canSave || ((status !== 'INIT' && status !== 'FINISHED') && userInfo.roleType !== 'OPERATOR')) {
        return;
      }

      setCommiting(true);

      async function start(data) {
        const result = await startCreative(data);
        setCommiting(false);
        // 更新用户常用图片数
        if (type === 'CUSTOM'){
          setUserProfileByKey({ key: CREATIVE_IMAGE_NUM, value: data.imageNum });
        }

        //更新图片生成数量
        updatePoint(type);
        updateLoraPoint();
        predictLora(data.loraId, data.imageNum, data.proportion);
        if (!result) {
          console.log('生成图片失败', result);
          return;
        }

        if (userInfo.roleType === 'OPERATOR') {
          message.success('开始生成图片');
        }

        if (!useMultiTask) {
          const originStatus = status;
          //开始轮询状态,后台如果已经在执行和轮询了，则不再进行轮询
          if (originStatus !== 'QUEUE' && originStatus !== 'PROCESSING') {
            //将状态更改为提交状态
            setStatus(result.status);
            // @ts-ignore
            outputRef.current?.polling(result.id);
          }
        } else {
          taskOutputRef.current?.refresh();
        }
      }

      // processClothType(loraClothType, data);

      let adjust = {};
      if (isTrialAccount) {
        adjust = { enableNewModel: true };
      }
      start(resetData({ ...data, ...adjust }, null, configs));
    };

    const updateLoraPoint = () => {
      if (!predictVO || !predictVO.modelPoint || predictVO.modelPoint <= 0) {
        return;
      }

      loraList.forEach((item) => {
        if (item.id === data.loraId && item.modelPoint) {
          // @ts-ignore
          item.modelPoint = item.modelPoint - predictVO.modelPoint;
        }
      });
      setLoraList(loraList);
    };

    const location = useLocation();
    const updatePoint = (type) => {
      queryImagePoint().then(
        (result) => {
          if (result) {
            const imagePoint = type === 'CUSTOM' ? result.imagePoint : result.experiencePoint;
            setImagePoint(imagePoint);
            localStorage.setItem(IMAGE_POINT, result.imagePoint.toString());
            localStorage.setItem(EXPERIENCE_POINT, result.experiencePoint.toString());
            window.dispatchEvent(new Event(IMAGE_POINT_CHANGE_EVENT));
          }
        },
      );

      isVipUser().then(res => setVipUser(!!res));
    };

    const queryLoraList = useCallback(
      debounce(async (isOwner: boolean, loraIdInQueryParam, searchKey, principalId, searchClothType, searchAgeRange) => {
        const orderBy = loraIdInQueryParam ? `if(id = '${loraIdInQueryParam}',1,0) desc, id desc` : null;
        const ownerField = userInfo.roleType === 'DISTRIBUTOR' ? false : (isOwner ? { isOwner } : {});
        const statusList = userInfo.roleType === 'OPERATOR' ? ['ENABLED', 'TESTING'] : ['ENABLED'];
        const nameLike = searchKey ? searchKey.trim() : null;
        const userId = userInfo.roleType === 'DISTRIBUTOR' && principalId ? principalId : null;
        const queryParam = {
          ...ownerField, statusList, pageNum: 1, pageSize: type === 'CUSTOM' ? 4 : 5,
          type, orderBy, needModelPoint: true, nameLike, userId,
          clothStyleType: searchClothType && searchClothType !== 'All' ? searchClothType.toLowerCase() : null,
          materialType: 'cloth',
          ageRange: searchAgeRange && searchAgeRange !== 'ALL' ? searchAgeRange : null,
          onlyExperimental: data.onlyExperimental,
        };
        let method = type === 'CUSTOM' ? queryMaterialModelByPage : querySystemModelList;

        if (type === 'CUSTOM' && userInfo.roleType === 'DISTRIBUTOR') {
          if (!principalId) {
            setLoraList([]);
            return;
          }
          method = queryLoras4Distributor;
        }

        method(queryParam).then((res) => {
          if (res) {
            setLoraList(res.list || []);
            setHasMoreLora(res.hasNextPage !== null ? !!res.hasNextPage : false);
          }
        });
      }, 300), []);

    const changeLoraType = (newValue: boolean, load: boolean = false, modelId: null | string = null) => {
      if (!load && isOwner === newValue) {
        return;
      }
      setIsOwner(newValue);
      const copy = deepCopy(data);

      if (loraIdInQueryParam) {
        copy.loraId = Number(loraIdInQueryParam);
      } else if (modelId) {
        copy.loraId = Number(modelId);
      } else {
        copy.loraId = null;
      }

      setData(copy);

      queryLoraList(newValue, loraIdInQueryParam, searchKey, principalId, searchClothType, searchAgeRange);
    };

    const changePosition = (value: string) => {
      const copy = deepCopy(data);
      copy.position = value;
      setData(copy);
    };

    const changeBodyType = (value: boolean) => {
      const copy = deepCopy(data);
      copy.bodyType = value;
      setData(copy);
    };

    const handleChangeData = (data) => {
      setData(data);
    };

    const fetchStyleScene = () => {
      const sceneRoot = configs.find(item => item.configKey === 'SCENE');
      if (!sceneRoot) return null;

      const current = data.configs[sceneRoot.id];
      const isStyleScene = current && current[0]?.styleScene;

      return isStyleScene ? current[0] : null;
    };

    const handleNeedShowWholeBody = () => {
      if (type === 'SYSTEM') return false;
      const find = loraList.find(item => item.id === data.loraId);
      let hit = find && find.halfBody;

      if (!hit) {
        setData({ ...data, bodyType: 'whole body' });
        return false;
      }

      const styleScene = fetchStyleScene();
      if (styleScene !== null && find && styleScene.type) {

        hit = styleScene.type.includes(find.halfBody) && styleScene.type.includes('whole body');

        if (!hit && styleScene.type.includes(find.halfBody)) {
          setData({ ...data, bodyType: find.halfBody });
        } else if (hit) {
          setData({ ...data, bodyType: 'unlimited' });
        } else {
          setData({ ...data, bodyType: 'whole body' });
        }
      } else {
        setData({ ...data, bodyType: 'whole body' });
      }

      return !!hit;
    };

    const isLowerBody = () => {
      const find = loraList.find(item => item.id === data.loraId);
      let hit = find && find.halfBody === 'lower body';

      if (!hit) {
        return false;
      }

      const styleScene = fetchStyleScene();
      if (styleScene !== null && styleScene.type) {
        return styleScene.type.includes('lower body');
      }

      return hit;
    };

    const needShowPosition = () => {
      if (type === 'SYSTEM') return false;
      const find = loraList.find(item => item.id === data.loraId);
      let hit = find && find.hasBackView;

      if (!hit) {
        return false;
      }

      const styleScene = fetchStyleScene();
      if (styleScene !== null && styleScene.type) {
        hit = styleScene.type.includes('back view') && styleScene.type.includes('front view');

        if (!hit && styleScene.type.includes('back view')) {
          data.position = 'back view';
        }
      }

      return hit;
    };

    // 使用useMemo包装getClothPositions函数，减少不必要的重新计算
    const getClothPositions = useMemo(() => {
      if (!data.loraId) return null;
      const find = loraList.find(item => item.id === data.loraId);
      return find && find.hasBackView ? ['front view', 'back view'] : ['front view'];
    }, [data.loraId, loraList]);

    // 使用useMemo包装getClothBodyTypes函数，减少不必要的重新计算
    const getClothBodyTypes = useMemo(() => {
      if (!data.loraId) return null;
      const find = loraList.find(item => item.id === data.loraId);
      return find && find.halfBody ? ['whole body', find.halfBody] : ['whole body'];
    }, [data.loraId, loraList]);

    const fetchElementConfig = async (): Promise<ElementConfig[]> => {
      const value = await getElementConfig('CREATE_IMAGE', bizType, true);
      if (value) {
        setConfigs(value);
      }
      return value || [];
    };

    async function init() {
      if (type === 'CUSTOM') {
        queryUserProfileByKey(CREATIVE_IMAGE_NUM).then(res => {
          if (!res || !res.profileVal) return;
          setData({ ...data, imageNum: Number(res.profileVal) })
          data.imageNum = Number(res.profileVal);
        });
      }

      const value = await fetchElementConfig();

      if (value) {
        value.forEach(item => {
          data.configs[item.id] = null;
        });
        const sceneKey = value.find(item => item.configKey === 'SCENE');
        if (sceneKey) {
          setSceneKey(sceneKey.id.toString());
        }
      }

      //跳转时页面参数指定了模型id
      const queryParams = new URLSearchParams(location.search);

      let my = true;
      if (queryParams.get('isOwner') === '1') {
        my = true;
      } else if (queryParams.get('isOwner') === '0') {
        my = false;
      } else if (userInfo.roleType === 'DISTRIBUTOR' || userInfo.roleType === 'OPERATOR') {
        my = false;
      }

      // 获取名为 'modelId' 的查询参数
      const modelId = queryParams.get('modelId');
      setLoraIdInQueryParam(modelId);

      changeLoraType(my, true, modelId);

      //设置新用户引导条件
      const showGuideStore = localStorage.getItem(NEED_GUIDE);
      if (type === 'SYSTEM' && showGuideStore === 'true') {
        setShowGuide(true);
      }

      updatePoint(type);

      isVipUser().then(res => setVipUser(!!res));
    }

    const handleChooseLora = (lora) => {
      const itemIndex = loraList.findIndex(item => item.id === lora.id);
      let newArray;

      if (itemIndex !== -1) {
        newArray = [lora, ...loraList.slice(0, itemIndex), ...loraList.slice(itemIndex + 1)];
      } else {
        newArray = [lora, ...loraList.slice(0, loraList.length - 1)];
      }

      setLoraList(newArray);
      setShowLoraSelector(false);

      const copy = deepCopy(data);
      copy.loraId = lora.id;

      // 立即清空模特和场景的选择，防止不匹配的情况
      const faceConfig = configs.find(config => config.configKey === 'FACE');
      const sceneConfig = configs.find(config => config.configKey === 'SCENE');

      if (faceConfig && copy.configs[faceConfig.id]) {
        copy.configs[faceConfig.id] = null;
      }

      if (sceneConfig && copy.configs[sceneConfig.id]) {
        copy.configs[sceneConfig.id] = null;
      }

      setData(copy);

      // 记录当前选中的服装
      setCurrentSelectedLora(lora);
    };

    const changeClothCollocation = (target, isPreset) => {
      if (isPreset) {
        data.clothCollocation.shoe = target.clothCollocation.shoe;
        data.clothCollocation.tops = target.clothCollocation.tops;
        data.clothCollocation.bottoms = target.clothCollocation.bottoms;
        data.clothCollocation.others = target.clothCollocation.others;
        data.clothCollocation.props = target.clothCollocation.props;
      } else {
        setData(target);
      }
    };

    const getModelType = () => {
      const faceRoot = configs.find(item => item.configKey === 'FACE');
      if (!faceRoot) return null;
      const current = data.configs[faceRoot.id];

      if (current && current[0].type) {
        return current[0].type.includes('female-model') ? 'female' : 'male';
      }
      return null;
    };

    const getCurrentExperienceModelOpenCfg = () => {
      if (!data.loraId) {
        return null;
      }

      return experienceModelOpenCfg.find(item => item?.id === data.loraId);
    };

    // 使用useMemo包装模特组件的条件对象，特别注意clothType属性的处理
    const faceConditions = useMemo(() => {
      // 计算正确的clothType值
      let clothType: string | null = null;
      if (currentSelectedLora?.extInfo?.clothStyleType === 'male') {
        clothType = 'male-model';
      } else if (currentSelectedLora?.extInfo?.clothStyleType === 'female') {
        clothType = 'female-model';
      } else if (currentSelectedLora?.extInfo?.clothStyleType === 'unisex') {
        // unisex服装需要看已选的模特类型决定
        clothType = null;
      }

      return {
        version: loraList.find(item => item.id === data.loraId)?.version,
        principalId: principalId ? principalId : null,
        ageRange: currentSelectedLora?.extInfo?.ageRange || null,
        clothType: clothType
      };
    }, [
      loraList,
      data.loraId,
      principalId,
      currentSelectedLora?.extInfo?.ageRange,
      currentSelectedLora?.extInfo?.clothStyleType,
      data.configs,
      configs.find(c => c.configKey === 'FACE')?.id
    ]);

    // 使用useMemo包装场景组件的条件对象
    const sceneConditions = useMemo(() => ({
      clothType: currentSelectedLora?.extInfo?.clothStyleType && currentSelectedLora?.extInfo?.clothStyleType !== 'unisex' ? capitalizeFirstLetter(currentSelectedLora?.extInfo?.clothStyleType) : sceneClothType,
      version: loraList.find(item => item.id === data.loraId)?.version,
      principalId: principalId ? principalId : null,
      positions: getClothPositions,
      bodyTypes: getClothBodyTypes,
      ageRange: currentSelectedLora?.extInfo?.ageRange || null,
      clothCategory: (userInfo.roleType === 'OPERATOR' && currentSelectedLora?.extInfo?.clothCategory) || null,
    }), [loraList, data.loraId, principalId, loraClothType, sceneClothType, getClothPositions, getClothBodyTypes, currentSelectedLora?.extInfo?.ageRange, currentSelectedLora?.extInfo?.clothCategory]);

    // 监听data值变化
    useEffect(() => {
      setCanSave(isCanSave(data, imagePoint, type, predictVO, sceneKey));
    }, [data, imagePoint, isOwner, predictVO]);

    useEffect(() => {
      predictLora(data.loraId, data.imageNum, data.proportion);
    }, [data.loraId, data.imageNum, imagePoint, data.proportion]);

    useEffect(() => {

      setShowFlowSteps(false);

      queryAllExperienceModelOpenCfg().then(res => {
        if (res) {
          let cfg = res;
          // 演示数据的模特对所有服装可见
          if (!['MERCHANT'].includes(userInfo.roleType)) {
            cfg.forEach(item => {
              item.faces = [];
            })
          }
          setExperienceModelOpenCfg(cfg);
        }
        init();
      });

    }, [type]);

    useEffect(() => {

      //跳转时页面参数指定了face id/scene id
      const queryParams = new URLSearchParams(location.search);

      //从url中获取'faceId'字段，「全部资产」-去创作
      const faceId = queryParams.get('faceId');
      if (faceId && Number(faceId) !== null) {
        let faceParentConfig = configs.find(item => item.configKey === 'FACE');
        if (faceParentConfig && faceParentConfig?.children) {
          let selectedFace = faceParentConfig?.children?.find(f => f.id === Number(faceId));
          if (selectedFace) {
            handleFaceChange(faceParentConfig.id, [selectedFace], false);
          }
        }
      }

      //从url中获取'sceneId'字段，「全部资产」-去创作
      const sceneId = queryParams.get('sceneId');
      if (sceneId && Number(sceneId) !== null) {
        let sceneParentConfig = configs.find(item => item.configKey === 'SCENE');
        if (sceneParentConfig && sceneParentConfig?.children) {
          let selectedScene = sceneParentConfig?.children?.find(f => f.id === Number(faceId));
          if (selectedScene) {
            handleSceneChange(sceneParentConfig.id, [selectedScene], false);
          }
        }
      }
    }, [location, configs]);

    useEffect(() => {
      if (!data.loraId) return;

      // 找到当前选中的服装
      const selectedLora = loraList.find(item => item.id === data.loraId);
      if (selectedLora) {
        // 更新服装类型，该值会传递给场景组件使用
        const clothStyleType = selectedLora.extInfo ? selectedLora.extInfo['clothStyleType'] : '';
        setLoraClothType(clothStyleType);

        // 记录当前选中的服装，用于后续过滤
        setCurrentSelectedLora(selectedLora);
      }
    }, [data.loraId, loraList]);

    useEffect(() => {
      if (sceneKey && data.configs[sceneKey]?.[0]) {
        setNeedShowWholeBody(handleNeedShowWholeBody());
      }
    }, [data.loraId, data.configs[sceneKey]?.[0]?.id]);

    useEffect(() => {
      queryLoraList(isOwner, loraIdInQueryParam, searchKey, principalId, searchClothType, searchAgeRange);
    }, [searchKey, principalId, searchClothType, queryLoraList, searchAgeRange]);

    // 组件卸载时清理防抖函数
    useEffect(() => {
      return () => {
        debouncedSearchKey.cancel();
      };
    }, [debouncedSearchKey]);

    return (
      <PageContainer>
        {type === 'CUSTOM' &&
          <Flow activeStep={3} onCollapseAction={setShowFlowSteps} />
        }
        <Flex justify={'flex-start'} align={'flex-start'}
              className={'row-container' + (showFlowSteps ? ' row-container-flow' : '')}>
          <div className={'work-block work-block-fixed'} style={{ gap: 16 }}>

            <Flex vertical
                  className={'creation-scene-header' + (BIZ_TYPE_CLASS_MAP[bizType]?.tabs ? ' ' + BIZ_TYPE_CLASS_MAP[bizType]?.tabs : '')}
                  style={{ marginBottom: -14, marginTop: -8 }}>
              <Tabs activeKey={bizType} items={ALLBizTypes.map(e => {
                return {
                  ...e,
                  label: <Flex align={'center'} justify={'center'} gap={4}>
                    {e.iconType && <IconFont type={e.iconType} style={{ fontSize: 16 }} />}
                    {e.label}
                  </Flex>,
                };
              })} onChange={handleChangeBizType} tabBarGutter={0}
                    indicator={{ size: 24 }} />
            </Flex>

            <div className={'work-item-container'} ref={step1Ref}>

              {type === 'CUSTOM' && userInfo?.roleType === 'DISTRIBUTOR' &&
                <div className={'work-item-container'} style={{ gap: 8 }}>
                  <div className={'text16 font-pf color-n weight'}>选择客户</div>
                  <div className={'work-item-lora margin-bottom-16'} style={{ padding: 16 }}>
                    <MasterSelector onChange={changePrincipal} width={434} />
                  </div>
                </div>
              }

              <div className={'text16 font-pf color-n weight'}>选择服装</div>

              <div className={'work-item-lora'} style={{ gap: 8 }}>
                <Flex justify={'space-between'} align={'center'} className={'width-100'}>
                  <div>
                    {type === 'CUSTOM' && userInfo.roleType !== 'DISTRIBUTOR' &&
                      <Segmented
                        value={isOwner}
                        onChange={(value) => changeLoraType(value as boolean)}
                        options={[{ label: '我的服装', value: true }, { label: '团队全部', value: false }]}
                      />
                    }
                  </div>
                  <Flex gap={4} align={'center'}>
                    {/* 运营、管理员、测试账号可进行筛选 */}
                    {['OPERATOR', 'ADMIN', 'DEMO_ACCOUNT'].includes(userInfo.roleType) &&
                      <>
                        <Dropdown
                          trigger={['hover']}
                          placement="bottomLeft"
                          open={typeSelectOpen}
                          onOpenChange={(visible) => setTypeSelectOpen(visible)}
                          dropdownRender={() => (
                            <div style={{ backgroundColor: 'white', boxShadow: '0 2px 8px rgba(0,0,0,0.15)', borderRadius: 4, padding: 4, maxHeight: 300, overflow: 'auto' }}>
                              {CreationClothTypeCheckBoxOptions.filter((item: any) => item.value !== 'Common' && item.value !== 'Unset').map((item: any) => (
                                <div
                                  key={item.value}
                                  style={{ padding: '5px 12px', cursor: 'pointer', borderRadius: 4, textAlign: 'left' }}
                                  onClick={() => {
                                    setSearchClothType(item.value);
                                    setTypeSelectOpen(false);
                                  }}
                                  onMouseEnter={e => e.currentTarget.style.backgroundColor = '#f5f5f5'}
                                  onMouseLeave={e => e.currentTarget.style.backgroundColor = 'transparent'}
                                >
                                  {item.label}
                                </div>
                              ))}
                            </div>
                          )}
                        >
                          <Select
                            placeholder={'类型'}
                            allowClear={true}
                            value={searchClothType}
                            options={CreationClothTypeCheckBoxOptions.filter((item: any) => item.value !== 'Common' && item.value !== 'Unset')}
                            style={{ width: 84 }}
                            onClick={() => setTypeSelectOpen(!typeSelectOpen)}
                            onChange={(value) => setSearchClothType(value)}
                            dropdownStyle={{ display: 'none' }} // 隐藏原始下拉框
                          />
                        </Dropdown>

                        <Dropdown
                          trigger={['hover']}
                          placement="bottomLeft"
                          open={ageSelectOpen}
                          onOpenChange={(visible) => setAgeSelectOpen(visible)}
                          dropdownRender={() => (
                            <div style={{ backgroundColor: 'white', boxShadow: '0 2px 8px rgba(0,0,0,0.15)', borderRadius: 4, padding: 4, maxHeight: 300, overflow: 'auto' }}>
                              {AllAgeRanges.map((item: any) => (
                                <div
                                  key={item.value}
                                  style={{
                                    padding: '5px 12px',
                                    cursor: item.disabled ? 'not-allowed' : 'pointer',
                                    borderRadius: 4,
                                    textAlign: 'left',
                                    color: item.disabled ? '#ccc' : 'inherit',
                                    opacity: item.disabled ? 0.5 : 1
                                  }}
                                  onClick={() => {
                                    if (!item.disabled) {
                                      setSearchAgeRange(item.value);
                                      setAgeSelectOpen(false);
                                    }
                                  }}
                                  onMouseEnter={e => {
                                    if (!item.disabled) {
                                      e.currentTarget.style.backgroundColor = '#f5f5f5'
                                    }
                                  }}
                                  onMouseLeave={e => e.currentTarget.style.backgroundColor = 'transparent'}
                                >
                                  {item.label}
                                </div>
                              ))}
                            </div>
                          )}
                        >
                          <Select
                            placeholder={'年龄'}
                            allowClear={true}
                            value={searchAgeRange}
                            options={AllAgeRanges}
                            style={{ width: 84 }}
                            onClick={() => setAgeSelectOpen(!ageSelectOpen)}
                            onChange={(value) => setSearchAgeRange(value)}
                            dropdownStyle={{ display: 'none' }} // 隐藏原始下拉框
                          />
                        </Dropdown>
                      </>
                    }

                    {/* 服装名称模糊搜索 */}
                    <Input 
                      placeholder={'服装名称模糊搜索'} 
                      style={{ width: 160 }} 
                      allowClear
                      value={searchValue}
                      onChange={(e) => {
                        const value = e.target.value;
                        setSearchValue(value); // 立即更新输入框显示
                        
                        // 如果清空了输入框，立即触发搜索
                        if (!value) {
                          debouncedSearchKey.cancel(); // 取消之前的防抖
                          setSearchKey(null);
                        } else {
                          // 否则使用防抖搜索
                          debouncedSearchKey(value);
                        }
                      }} 
                    />
                  </Flex>
                </Flex>

                <Flex wrap={true}
                      className={'work-item-block ' + (!loraList || loraList.length === 0 ? ' work-item-empty-block' : '')}>
                  {loraList && loraList.length > 0 && loraList.slice(0, hasMoreLora ? 4 : loraList.length).map((item) => (
                    <Tooltip key={item.id}
                      title={(userInfo.roleType === 'MERCHANT' && item.extInfo?.['memo']) ? item.extInfo['memo'] : null}
                      placement="top"
                    >
                      <div key={item.id}
                          className={'work-item work-item-fixed' + (data.loraId === item.id ? ' work-item-selected' : '')}
                          onClick={() => {
                            // 更新表单数据
                            handleLoraChange(item.id);
                            // 记录当前选中的服装
                            setCurrentSelectedLora(item);
                          }}>
                        <div className={'work-image-block'}>
                          {type === 'CUSTOM' &&
                            <div className={'work-item-block-desc text12 font-pf color-w'}>
                              剩余{item.modelPoint ? item.modelPoint : 0}张
                            </div>
                          }
                          <img src={item.showImage} alt={item.name} className={'lora-item-image'} />
                          {isClothMarkedColor(item) && getClothMarkIcon(item) && (
                            <IconFont type={getClothMarkIcon(item)}
                                      style={{ fontSize: 16, position: 'absolute', top: 8, right: 8 }} />
                          )}
                        </div>
                        <div style={{ overflowWrap: 'break-word', wordBreak: 'break-all' }}
                            className={'text14 font-pf color-n margin-top-4 margin-bottom-4 text-center'}>{item.name}</div>
                      </div>
                    </Tooltip>
                  ))}
                  {loraList && loraList.length > 0 && hasMoreLora &&
                    <div className={'work-item work-item-fixed'} onClick={() => setShowLoraSelector(true)}>
                      <div className={'work-image-block'}
                           style={{ backgroundImage: `url(${ALL_LORAS_NEW})`, backgroundSize: 'cover' }}>
                        <div className={'more-lora-block work-element-more-block'}>
                          <img src={MORE_LORA_ICON} alt={'more'} style={{ width: 32, height: 32 }} />
                          <div className={'text14 font-pf weight color-w text-center margin-top-4'}>全部服装</div>
                        </div>
                      </div>
                      <div
                        className={'text14 font-pf color-n margin-top-4 margin-bottom-4 text-center'}>更多
                      </div>
                    </div>
                  }
                  {loraList && loraList.length === 0 &&
                    <div className={'center-block'} style={{ width: '100%', height: '100%' }}>
                      <div className={'text14 font-pf color-1a'}>暂无服装，请先上传素材
                      </div>
                      <Button className={'update-material-btn margin-top-16'}
                              onClick={handleUploadMaterial}>
                        <div className={'text16 font-pf weight color-24'}>传素材</div>
                      </Button>
                    </div>
                  }
                </Flex>
              </div>
            </div>

            <div className={'work-item-group'} ref={step2Ref} style={{ gap: 16 }}>
              {/*'模特'配置*/}
              {configs.filter(c => c.configKey === 'FACE').map((config) => (
                <ElementWithTypeBlock key={'FACE'} config={config} defaultType={'recent'} modelId={data.loraId}
                                      current={data.configs[config.id] || []} onChange={handleFaceChange}
                                      acceptTypes={loraClothType ? (loraClothType === 'unisex' ? ['male-model', 'female-model'] : [loraClothType + '-model']) : undefined}
                                      conditions={faceConditions} needExclusive={true} needFavor popShowAll={true} moreIcon={'icon-quanbumote'}
                                      moreBg={BG_ALL_MODELS} orderByType={true} loraType={type} isVipUser={vipUser}
                                      experienceModelOpenCfg={getCurrentExperienceModelOpenCfg()}
                                      showConfirmFooter={true}
                                      foot={<FaceExpressionBlock changeData={handleChangeData} data={data} />} />
              ))}

              {/*'场景'配置*/}
              {configs.filter(c => c.configKey === 'SCENE').map((config) => (
                <ElementWithTypeBlock key={'SCENE'} config={config} defaultType={'recent'} modelId={data.loraId}
                                      current={data.configs[config.id] || []} onChange={handleSceneChange}
                                      conditions={sceneConditions} needExclusive={true} needFavor popShowAll={true}
                                      moreIcon={'icon-quanbuchangjing'}
                                      moreBg={BG_ALL_SCENES} orderByType={true} loraType={type} isVipUser={vipUser}
                                      experienceModelOpenCfg={getCurrentExperienceModelOpenCfg()} bizType={bizType}
                                      showConfirmFooter={true}
                                      foot={<SceneAccessoriesBlock changeData={handleChangeData} data={data} />}
                />
              ))}

              {/*服装搭配*/}
              <ClothCollocationBlock data={data} onChange={((data, isPreset) => changeClothCollocation(data, isPreset))}
                                     defaultExpend={false}
                                     type={loraClothType ? (loraClothType === 'unisex' ? getModelType() : loraClothType) : null} />

              {needShowPosition() &&
                <div className={'work-item-container'} style={{ gap: 8 }}>
                  <div className={'text16 font-pf color-n weight'}>正面/背面</div>
                  <Flex gap={8}>
                    <HalfSelectItemBlock image={FRONT_DEMO} title={'正面照'} current={data.position}
                                         value={'front view'}
                                         onClick={value => changePosition(value)} />
                    <HalfSelectItemBlock image={BACK_DEMO} title={'背面照'} current={data.position}
                                         value={'back view'}
                                         onClick={value => changePosition(value)} />
                  </Flex>
                </div>
              }

              {needShowWholeBody &&
                <div className={'work-item-container'} style={{ gap: 8 }}>
                  <div className={'text16 font-pf color-n weight'}>全身/半身</div>
                  <Flex gap={8}>
                    {fetchStyleScene() !== null &&
                      <HalfSelectItemBlock image={WHOLE_BODY_ICON} title={'不限'} current={data.bodyType}
                                           value={'unlimited'}
                                           onClick={value => changeBodyType(value)} />
                    }
                    <HalfSelectItemBlock image={WHOLE_BODY_ICON} title={'更多全身图'} current={data.bodyType}
                                         value={'whole body'}
                                         onClick={value => changeBodyType(value)} />
                    {isLowerBody() &&
                      <HalfSelectItemBlock image={LOWER_BODY_ICON} title={'更多下半身图'} current={data.bodyType}
                                           value={'lower body'} onClick={value => changeBodyType(value)} />
                    }
                    {!isLowerBody() &&
                      <HalfSelectItemBlock image={UPPER_BODY_ICON} title={'更多上半身图'} current={data.bodyType}
                                           value={'upper body'} onClick={value => changeBodyType(value)} />
                    }
                  </Flex>
                </div>
              }

              <ProportionBlock value={data.proportion} onChange={handleProportionChange} type={type}
                               model={loraList.find(item => item.id === data.loraId)} isVip={vipUser} />

              <ClothColorSelector model={loraList.find(item => item.id === data.loraId)} value={data.colorIndex}
                                  onChange={handleChangeColor} position={data.position} bodyType={data.bodyType} />

              {userInfo.roleType === 'OPERATOR' && <ClothColorBlock model={loraList.find(item => item.id === data.loraId)} value={data.clothColor}
                               onChange={value => {
                                 setData({ ...data, clothColor: value });
                               }} />}

              <div className={'work-item-container'} style={{ display: isTrialAccount ? 'none' : undefined }}>
                <div className={'text16 font-pf color-n weight'}>画面设置</div>
                <Flex gap={8} align={'center'} justify={'flex-start'}>
                  <Checkbox checked={!data.enableAntiBlurLora} onChange={e => {
                    setData({ ...data, enableAntiBlurLora: !e.target.checked });
                  }}>
                    <Flex>
                      <div>背景虚化</div>
                      <Tooltip title={'选中后，创作图将突出前景中的服装和模特，使背景模糊。'} placement="topRight">
                        <IconFont type={'icon-yiwen1'} style={{ fontSize: 18 }} />
                      </Tooltip>
                    </Flex>
                  </Checkbox>

                  <Checkbox checked={data.enableNewModel} onChange={e => {
                    setData({ ...data, enableNewModel: e.target.checked });
                  }}>
                    <Flex>
                      <div>脸部质感增强</div>
                      <Tooltip title={'可能会引起衣服质感出现问题。衣服花纹比较复杂不建议使用'} placement="topRight">
                        <IconFont type={'icon-yiwen1'} style={{ fontSize: 18 }} />
                      </Tooltip>
                    </Flex>
                  </Checkbox>

                </Flex>

              </div>


              <div className={'work-item-container'}>
                <div className={'text16 font-pf color-n weight'}>生成数量</div>
                <div className={'work-item-row margin-bottom-8'} style={{ gap: 8 }}>
                  <InputNumber min={4} max={30}
                               defaultValue={data.imageNum} value={data.imageNum}
                               status={((type === 'SYSTEM' && data.imageNum > imagePoint) ? 'error' : '')}
                               onChange={handleNumberChange} className={'work-item-input'} />
                  <div className={'margin-left-8'}>最大生成数量30</div>

                  {userInfo.roleType === 'OPERATOR' &&
                    <>
                      <Button type={data.imageNum === 5 ? 'primary' : 'dashed'} onClick={() => handleNumberChange(5)}
                              style={{ width: 80 }}>5</Button>
                      <Button type={data.imageNum === 10 ? 'primary' : 'dashed'} onClick={() => handleNumberChange(10)}
                              style={{ width: 80 }}>10</Button>
                      <Button type={data.imageNum === 20 ? 'primary' : 'dashed'} onClick={() => handleNumberChange(20)}
                              style={{ width: 80 }}>20</Button>
                    </>
                  }
                </div>
              </div>

            </div>
          </div>
          <div ref={outputBlockRef}
               className={'output-block output-block-fixed' + (showFlowSteps ? ' output-block-flow' : '')}
               style={{ width: `calc((100% - 644px - 17px) * ${sliderValue / 12})`, maxWidth: '100%' }}>

            {!useMultiTask &&
              <OutputBlock types={['CREATE_IMAGE']} status={status}
                           sliderValue={sliderValue} loraType={type} changeSliderValue={setSliderValue}
                           refreshStatus={setStatus} ref={outputRef} />
            }

            {useMultiTask &&
              <TaskOutputBlock sliderValue={sliderValue} bizType={bizType} types={['CREATE_IMAGE']}
                               bgClassName={(BIZ_TYPE_CLASS_MAP[bizType]?.outputBg ? BIZ_TYPE_CLASS_MAP[bizType]?.outputBg : null)}
                               bgImg={(BIZ_TYPE_CLASS_MAP[bizType]?.outputBgImg ? BIZ_TYPE_CLASS_MAP[bizType]?.outputBgImg : null)}
                               ref={taskOutputRef} pollingTimeout={3000} loraType={type}
                               changeSliderValue={setSliderValue} />
            }
          </div>
        </Flex>

        <footer className={'footer-bar'}>
          <div className={'fixed-tab-bar fixed-tab-bar-fixed'}>
            <div className={'fixed-tab-content'} ref={step3Ref}>
              <PredictBlock creativeType={'CREATE_IMAGE'} predictVO={predictVO} data={data} type={type}
                            imagePoint={imagePoint} />
              <Button type="primary" className={'create-btn'}
                      disabled={(!canSave && (status === 'INIT' || status === 'FINISHED')) && (type === 'SYSTEM' || predictVO === null || !predictVO.needTopup)}
                      icon={(status === 'INIT' || status === 'FINISHED') ? '' :
                        <img src={MINI_LOADING_ICON} width={16} height={16} alt={'logo'}
                             className={'loading-img'} />}
                      onClick={handleCreate}> {(status === 'INIT' || status === 'FINISHED') ? (predictVO && predictVO.needTopup ? '余额不足，去充值' : '生成图片') : ''}</Button>
            </div>
          </div>
        </footer>

        <NewUserGuide visible={type === 'SYSTEM' && showGuide && loraList.length > 0} step1Ref={step1Ref}
                      step2Ref={step2Ref} step3Ref={step3Ref} handleFinish={() => setShowGuide(false)} />

        {showLoraSelector &&
          <LoraSelector show={true} isOwner={isOwner} type={type} onCancel={() => setShowLoraSelector(false)}
                        onChange={(lora) => handleChooseLora(lora)} principalId={principalId} />
        }

        {showTopupModal && !isTrialAccount &&
          <TopupModal visible={showTopupModal} onClose={() => setShowTopupModal(false)}
                      onPaySuccess={() => {
                        setShowTopupModal(false);
                        updatePoint('CUSTOM');
                      }} />
        }
      </PageContainer>
    );
  }
;

export default Creative;