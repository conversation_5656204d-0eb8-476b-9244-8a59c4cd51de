.body-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow-y: initial;
  background-image: url("@/assets/images/bg-login.png");
  background-size: cover;
  background-repeat: no-repeat;
}

.login-form {
  margin-top: 32px;
  width: 694px;
  height: 398px;
  border-radius: 24px;
  opacity: 1;

  background: url("@/assets/images/bg-login-form.png") no-repeat;
  background-size: cover !important;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding: 68px 195px 0 195px;
}

.row-center-block {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.login-input {
  width: 304px;
  //border-radius: 226px;

  .ant-input-group-addon {
    background: #FFFFFF;
  }
}

.captcha-input {
  margin-bottom: 0 !important;
}

.row-center-block {
  .ant-btn-default {
    background: #FFFFFF !important;
  }
}

.login-btn {
  width: 304px;
  height: 46px;
  border-radius: 108px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 8px;
  background: linear-gradient(90deg, #0060FF 0%, #9978FF 100%);
  z-index: 1;
}


.login-btn:disabled {
  color: #FFFFFF;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.6)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%);
}

.login-btn:enabled {
  background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.login-btn:not(:disabled):hover, .login-btn:not(:disabled):focus {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.login-btn:not(:disabled):active {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.caps-lock-icon {
  position: absolute;
  right: 32px;
  top : 12px;
  font-size: 16px;
  color: #C8C9CC;
  z-index: 100;
}

.pwd-block{
  position: relative;
}