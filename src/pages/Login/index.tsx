import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>ontainer } from '@ant-design/pro-layout';
import { ProForm<PERSON>aptcha } from '@ant-design/pro-form';
import { Button, Flex, Form, Input, notification, Space } from 'antd';
import { BRAND, LOGO, PHONE_REGEX, NEED_GUIDE, USER_INFO } from '@/constants';
import './index.less';
import '@/app.less';
import { fetchSalt, pwdLogin, sendCaptcha, smsLogin } from '@/services/LoginController';
import { useNavigate } from 'react-router-dom';
import cryptoStr from '@/utils/cryptojs';
import IconFont from '@/components/IconFont';
import { recordClickBtnTrack } from '@/utils/trackingUtils';
import { TRACK_EVENT_BTN } from '@/constants/TrackingConstants';

type LoginType = 'sms' | 'pwd';

function isCanCommit(loginType: LoginType, mobile: string, code: string, pwd: string) {
  if (loginType === 'sms') {
    return PHONE_REGEX.test(mobile) && code.length === 4;
  }
  return PHONE_REGEX.test(mobile) && pwd.length >= 6;
}

const Login = () => {
  const [loginType, setLoginType] = useState<LoginType>('sms');
  const [canCommit, setCanCommit] = useState(false);
  const [mobile, setMobile] = useState<string>('');
  const [mobileError, setMobileError] = useState(false);
  const [pwd, setPwd] = useState('');
  const [code, setCode] = useState('');
  const [salt, setSalt] = useState('');
  const [capsLockOn, setCapsLockOn] = useState(false);
  const navigate = useNavigate();

  const handleMobileChange = (e: { target: { value: any; }; }) => {
    const mobile = e.target.value;
    setMobile(mobile);

    if (mobile && !PHONE_REGEX.test(mobile)) {
      setMobileError(true);
      return;
    }

    setMobileError(false);
  };

  const handleCodeChange = (e) => {
    let code = e.target.value;
    setCode(code);
  };

  const handleGetCaptcha = () => {
    if (!mobile || !PHONE_REGEX.test(mobile)) {
      return;
    }
    console.log(mobile, '发送验证码');
    // @ts-ignore
    sendCaptcha(mobile);
  };

  const handlePwdChange = (e) => {
    setPwd(e.target.value);
  };

  const loginNavigate = (r) => {
    if (!r) {
      return;
    }
    notification.success({ message: '登录成功' });
    localStorage.setItem(USER_INFO, JSON.stringify(r));

    if (!r.lastLoginTime) {
      localStorage.setItem(NEED_GUIDE, String(true));
    }

    setTimeout(() => {
      let path = '/upload';
      if (r.roleType === 'ADMIN') {
        path = '/cloth-mng';
      } else if (r.roleType === 'DISTRIBUTOR'){
        path = '/distributor/customers';
      } else if (!r.imagePoint || r.imagePoint <= 0 || !r.lastLoginTime) {
        path = '/experience';
      }

      navigate(path);
    }, 1000);
  };

  const handleLogin = () => {
    if (!isCanCommit(loginType, mobile, code, pwd)) {
      return;
    }

    if (loginType === 'sms') {
      smsLogin(mobile, code).then(r => {
        loginNavigate(r);
      });
    }

    if (loginType === 'pwd') {
      const password = cryptoStr.aesEncrypt(pwd, salt);
      pwdLogin(mobile, password).then((r) => {
        loginNavigate(r);
      });
    }
  };

  const handleFindPwd = () => {
    // 上报埋点信息
    recordClickBtnTrack(TRACK_EVENT_BTN.FORGET_PWD_BTN.label);

    navigate('/find-pwd');
  };

  const loginBySms = () => {
    if (loginType === 'sms') {
      return;
    }
    setLoginType('sms');
  };
  const loginByPwd = () => {
    if (loginType === 'pwd') {
      return;
    }
    setLoginType('pwd');
  };

  //监听输入变化，设置是否能提交状态
  useEffect(() => {
    setCanCommit(isCanCommit(loginType, mobile, code, pwd));
  }, [loginType, mobile, code, pwd]);

  //获取盐值
  useEffect(() => {
    async function init() {
      const value = await fetchSalt();

      if (value) {
        setSalt(value);
      }
    }

    init().then(() => {
    });

    //跳转时页面参数指定了模型id
    const queryParams = new URLSearchParams(location.search);
    const loginType = queryParams.get('loginType') as LoginType; // 获取名为 'modelId' 的查询参数
    if (loginType) {
      setLoginType(loginType);
    }

    // 设置当前页面的 body 样式
    document.body.style.overflow = 'hidden'; // 防止页面上下移动
    // 在组件卸载时恢复原样
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  const handlePwdDown = (e) => {
    if (e.getModifierState('CapsLock')) {
      setCapsLockOn(true);
    } else {
      setCapsLockOn(false);
    }
  };

  const handlePwdUp = (e) => {
    if (e.getModifierState('CapsLock')) {
      setCapsLockOn(true);
    } else {
      setCapsLockOn(false);
    }
  };

  return (<PageContainer>
    <Form className={'body-container'} onFinish={handleLogin}>
      <Flex>
        <Space>
          <img src={LOGO} alt={'logo'} width={'96'} height={'96'} />
          <img src={BRAND} alt={'logo'} width={'310'} height={'72'} />
        </Space>
      </Flex>
      <div className={'login-form'}>
        <div className={'row-center-block'}>
          <div className={'text24 weight font-pf' + (loginType === 'sms' ? ' color-w' : ' color-9e')}
               style={{ cursor: 'pointer' }}
               onClick={loginBySms}>短信登录
          </div>
          <div
            className={'text24 weight font-pf margin-left-24' + (loginType === 'pwd' ? ' color-w' : ' color-9e')}
            style={{ cursor: 'pointer' }}
            onClick={loginByPwd}>密码登录
          </div>
        </div>
        {loginType === 'sms' &&
          <div className={'row-center-block margin-top-24'}>
            <div className={'text14 font-pf color-9e'}>未注册conrain账号时将自动注册</div>
          </div>
        }
        <div className={'row-center-block ' + (loginType === 'sms' ? 'margin-top-12' : 'margin-top-24')}>
          <Input onChange={handleMobileChange} addonBefore="+86" size={'large'}
                 status={mobileError ? 'error' : ''} allowClear showCount
                 placeholder={'请输入手机号码'} maxLength={11} className={'login-input'} />
        </div>
        <div className={'row-center-block margin-top-12'}>
          {loginType === 'sms' &&
            <ProFormCaptcha
              className={'captcha-input'}
              allowClear
              fieldProps={{ size: 'large', maxLength: 4 }}
              captchaProps={{ size: 'large', disabled: !PHONE_REGEX.test(mobile) }}
              // 手机号的 name，onGetCaptcha 会注入这个值
              phoneName="phone"
              name="captcha"
              rules={[{ required: true, message: '请输入验证码' }]}
              placeholder="请输入验证码"
              onChange={handleCodeChange}
              onGetCaptcha={async () => handleGetCaptcha()}
            />
          }
          {loginType === 'pwd' &&
            <div className={'row-center-block pwd-block'}>
              <Input.Password placeholder="请输入登录密码" size={'large'} onChange={handlePwdChange}
                              onKeyDown={(e) => handlePwdDown(e)}
                              onKeyUp={(e) => handlePwdUp(e)}
                              className={'margin-bottom-24'}
              />
              {capsLockOn &&
                <IconFont type={'icon-iocn_daxiesuoding'} className={'caps-lock-icon'} />
              }
            </div>
          }
        </div>
        <div className={'row-center-block'}>
          <Button className={'login-btn'} type="primary" htmlType="submit" disabled={!canCommit}>
            <div className={'text16 font-pf weight color-w'}>登录</div>
          </Button>
        </div>
        {loginType === 'pwd' &&
          <div className={'row-center-block margin-top-12'}>
            <div className={'text16 font-pf color-9e'} onClick={handleFindPwd}>忘记密码</div>
          </div>
        }
        <Flex vertical className={'margin-top-12'} style={{ width: '120%',marginTop: loginType === 'pwd'?'14px':''}}>
          <div className={'text12 font-pf weight color-9e'}>登录即表示已阅读并同意《<a target={'_blank'} href={'user_service.html'}>用户服务协议</a>》和《<a target={'_blank'} href={'privacy_agreement.html'}>隐私政策</a>》</div>
        </Flex>

      </div>
      <div className={'margin-top-24 text24 weight font-pf color-w'} style={{ letterSpacing: '0.8em' }}>
        灵感创作，点击成片
      </div>
    </Form>
  </PageContainer>);
};

export default Login;
