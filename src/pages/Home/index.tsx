import './index.less';
import { BRAND, LOGO, Q_CODE_WX_VIDEO, REGISTER_INVITE_CODE } from '@/constants';
import React, { FC, useEffect, useRef, useState } from 'react';
import { But<PERSON>, Flex } from 'antd';
import { useLocation } from 'react-router-dom';
import { PageContainer } from '@ant-design/pro-layout';
import IconFont from '@/components/IconFont';
import { getShowCaseItems, partnerData, sellingPointsData, showCaseData } from '@/pages/Home/data';
import ShowCaseCard from '@/components/Home/ShowCaseCard';
import { getUserInfo } from '@/utils/utils';
import { UserVO } from '@/services/UserController';
import { queryIndexShowCase, ShowCaseDetail } from '@/services/ShowCaseController';
import NavigationBar, { NavigationBarRef } from '@/components/Home/NavigationBar';

export const HiddenInfo = () => <div style={{ display: 'none' }}>
  MuseGate musegate 霖润智能 杭州霖润智能科技有限公司
</div>;

const HomePage: React.FC = () => {
  const showCaseItems = getShowCaseItems();
  const location = useLocation();
  const userInfo: UserVO | null = getUserInfo();

  // @ts-ignore
  const [showCaseTag, setShowCaseTag] = useState('all');
  const [showCaseList, setShowCaseList] = useState<Array<ShowCaseDetail>>(showCaseData);

  const moreCustomerCaseRef = useRef(null);
  const navigationBarRef = useRef<NavigationBarRef>(null);

  useEffect(() => {
    queryIndexShowCase().then(res => {
      if (res && res.length > 0) {
        setShowCaseList(res);
      }
    });

    //路径查询参数
    const queryParams = new URLSearchParams(location.search);

    if (location.pathname.includes('/login')) {
      navigationBarRef.current?.checkLogin();
    }

    if (location.pathname.includes('/register')) {
      let code = queryParams.get('ic');
      if (code) {
        sessionStorage.setItem(REGISTER_INVITE_CODE, code);
      }

      navigationBarRef.current?.gotoRegister();
    }
  }, [location]);


  const handleScroll = (ref: any) => {
    ref.current.scrollIntoView({ behavior: 'smooth' });
  };

  const gotoStart = () => {
    navigationBarRef?.current?.gotoStart();
  };

  const gotoCreateSame = (data) => {
    sessionStorage.setItem('preset.face', data.face.id);
    sessionStorage.setItem('preset.scene', data.scene.id);
    sessionStorage.setItem('preset.clothCollocation', JSON.stringify(data.clothCollocation));
    if (!userInfo) {
      gotoStart();
    } else {
      gotoStart();
    }
  };

  const SellingPointsCard = ({ data }) => {
    return <Flex vertical justify={'space-between'} gap={4} className={'home-selling-points-card pointer'}>
      {/*图片区域*/}
      <Flex align={'center'} className={'home-selling-points-img'} justify={'flex-start'}>
        {data.mainUrl.map((item, index) => <img key={index} src={item}
          style={{ width: `calc(100%/${data.mainUrl.length})` }}
          height={256} alt={'img'} className={'home-selling-points-card-img'} />)}
      </Flex>

      <Flex align={'center'} justify={'center'} className={'home-selling-points-txt'}>
        <div style={{ width: 128, whiteSpace: 'nowrap' }}>{data.title}</div>
        <Flex vertical className={'home-selling-points-card-desc'}>
          {data.desc.map(item => <div className={'text16 weight color-72'} key={item}
            style={{ whiteSpace: 'nowrap' }}>{item}</div>)}
          <div></div>
        </Flex>
      </Flex>
    </Flex>;
  };

  const SubSellingPointsText = ({ text }) => {
    return <Flex align={'center'} justify={'flex-start'} gap={5.5} style={{ height: 28 }}>
      <IconFont type={'icon-icon_duihao'} style={{ fontSize: 24 }} className={'color-1a'} />
      <div className={'text20 weight color-74 font-pf'}>{text}</div>
    </Flex>;
  };

  const VideoCard = ({ url, width, height }) => {
    return <video width={width} height={height} autoPlay={true} controls={false} loop={true} muted
      style={{ borderRadius: 24 }}>
      <source src={url} type="video/mp4" />
      Your browser does not support the video tag.
    </video>;
  };

  const SubSellingPointsCard: React.FC<{
    title: string, tips: string, descList: Array<string>, imgPosition?: 'left' | 'right',
    tipColor?: string, url: string, type?: 'image' | 'video',
  }> = ({ title, tips, descList, imgPosition, tipColor, url, type = 'image' }) => {
    return <Flex align={'center'} justify={'space-between'} className={'home-content'} gap={72}>
      {imgPosition !== 'right' &&
        <>
          {type === 'image' &&
            <img loading={'lazy'} src={url} alt={'img'} width={608} />
          }
          {type === 'video' &&
            <VideoCard url={url} width={608} height={448} />
          }
        </>
      }
      <Flex vertical justify={'flex-start'} align={'flex-start'} gap={24} style={{ width: 500 }}>
        <div className={'home-tips font-mi-semi-bold text20 color-w'}
          style={{ padding: (tips && tips.length > 4 ? '8px' : '8px 30px'), backgroundColor: tipColor }}>
          {tips}
        </div>
        <div className={'font-mi-bold text38 home-selling-points-title margin-top-8'}>
          {title}
        </div>
        {descList.map((item, index) => (<SubSellingPointsText key={index} text={item} />))}
      </Flex>

      {imgPosition === 'right' &&
        <>
          {type === 'image' &&
            <img loading={'lazy'} src={url} alt={'img'} width={608} />
          }
          {type === 'video' &&
            <VideoCard url={url} width={608} height={448} />
          }
        </>
      }
      <HiddenInfo />
    </Flex>;
  };

  const TopShowCaseBlock: FC<{ title: string, type: string }> = ({ title, type }) => {
    return <Flex vertical gap={16} style={{ width: '50%' }}>
      <div className={'text32 font-mi weight color-f5'}>{title}</div>
      <Flex align={'center'} justify={'flex-start'} gap={16} className={'width-100'}>
        {showCaseList.filter(item => item.topped).filter(item => item.type === type).slice(0, 2).map((item, index) =>
          <ShowCaseCard data={item} key={index} index={index} gotoCreateSame={gotoCreateSame} />)}
      </Flex>
    </Flex>;
  };

  const PartnerCard = ({ data }) => {
    return <Flex vertical gap={8} className={'home-partner-card'} align={'center'} justify={'center'}>
      <Flex className={'home-partner-logo'} justify={'center'} align={'center'} style={{ background: data.background }}>
        <img alt={'img'} src={data.logo} height={data.height ? data.height : 27}
          width={data.width ? data.width : undefined} />
      </Flex>
      {data.name &&
        <div className={'text16 weight color-72'}>{data.name}</div>
      }
    </Flex>;
  };

  const AboutCard: FC<{ title: any, desc?: string, imgUrl?: string }> = ({ title, desc, imgUrl }) => {
    return <Flex vertical gap={8} align={'flex-start'} justify={'flex-start'}>
      <div className={'text16 weight color-w margin-bottom-4'}>{title}</div>
      {imgUrl &&
        <img loading={'lazy'} src={imgUrl} width={142} height={142} style={{ borderRadius: 8 }} alt={'img'} />
      }
      <Flex className={'text14 width-100'} style={{ color: 'rgba(255, 255, 255, 0.8)' }} justify={'center'}
        align={'center'}>
        {desc}
      </Flex>
    </Flex>;
  };

  return (
    <PageContainer>
      <Flex vertical className={'home-page'}>
        <Flex vertical className={'home-main'}>
          {/*header区域*/}
          <NavigationBar ref={navigationBarRef} />

          <Flex vertical align={'center'} justify={'center'} style={{ marginTop: 120 }} gap={12}>
            <div className={'home-title'}>
              演绎
              <span className={'home-title-highlight'}>服饰</span>
              万种风格，拓宽
              <span className={'home-title-highlight'}>营销</span>
              之路
            </div>

            <div className={'home-sub-title'}>服饰企业增长的发动机</div>
            <HiddenInfo />
          </Flex>

          <Flex vertical align={'center'} justify={'center'} style={{ marginTop: 50 }} gap={12}>
            <Button style={{ width: 254, height: 65, borderRadius: 270 }} className={'home-main-btn'}
              onClick={() => gotoStart()}>
              <Flex align={'center'} gap={8}>
                <div className={'text24 font-mi-bold color-w'}>立即使用</div>
                <IconFont type={'icon-lijishiyongjiantou'} style={{ fontSize: 24 }} />
              </Flex>
            </Button>
          </Flex>

          <Flex align={'center'} justify={'center'} style={{ marginTop: 42, marginBottom: 57 }} gap={16}>
            {sellingPointsData.map((item, index) => <SellingPointsCard data={item} key={index} />)}
          </Flex>

        </Flex>


        {/*AI图片和视频案例show*/}
        <Flex className={'home-show-case'} justify={'center'}>
          <Flex vertical align={'flex-start'} justify={'flex-start'} className={'home-content'} gap={16}>
            <Flex className={'width-100'} gap={16}>
              <TopShowCaseBlock title={'图片创作'} type={'IMAGE'} />
              <TopShowCaseBlock title={'视频创作'} type={'VIDEO'} />
            </Flex>
            <Flex className={'home-show-case-more pointer'} gap={8} align={'center'} justify={'center'}
              onClick={() => handleScroll(moreCustomerCaseRef)}>
              <div className={'text16 font-pf weight color-w'}>查看更多案例</div>
              <IconFont type={'icon-cujiantou_2'} style={{ fontSize: 22 }} />
            </Flex>
          </Flex>
        </Flex>


        {/*卖点描述1*/}
        <Flex vertical className={'home-gray-block'} justify={'center'} align={'center'} gap={24}
          style={{ padding: '40px 0 72px 0', height: 638 }}>
          <div className={'text40 font-mi-bold home-value-title margin-top-24'}>MuseGate 如何帮助企业拓宽销量</div>

          <SubSellingPointsCard tips={'拓客群'} title={'短视频营销的千人千面'} type={'video'}
            url={'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/sub-selling-point-1.mp4'}
            descList={['根据平台人群标签生成不同风格服饰模特图', '配合素材混剪分发，有效扩大覆盖人群范围']} />
        </Flex>

        {/*卖点描述2*/}
        <Flex vertical className={'home-white-block'} justify={'center'} align={'center'} gap={42}
          style={{ padding: 72 }}>

          <SubSellingPointsCard tips={'拓客群 提转化'} title={'多店经营，详情页多搭配提高不同人群转化率'}
            imgPosition={'right'}
            url={'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/sub-selling-point-2.jpg'}
            descList={['一套衣服不同演绎，多店经营，扩大客户范围', '详情页多搭配，提高不同人群转化']} />
        </Flex>

        {/*卖点描述3*/}
        <Flex vertical className={'home-gray-block'} justify={'center'} align={'center'} gap={42}
          style={{ padding: 72 }}>

          <SubSellingPointsCard tips={'拓客群 提转化'} title={'真正的服饰广告千人千面投放'} tipColor={'#FE5403'}
            imgPosition={'left'}
            url={'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/sub-selling-point-3.jpg'}
            descList={['不同人群标签投放不同模特搭配，有效拓客', '真正千人千面广告，实现广告效率和规模双增长']} />
        </Flex>

        {/*卖点描述4*/}
        <Flex vertical className={'home-white-block'} justify={'center'} align={'center'} gap={42}
          style={{ padding: 72 }}>

          <SubSellingPointsCard tips={'提升转化'} title={'朋友圈营销的千人千面'} tipColor={'#1CCE56'}
            imgPosition={'right'}
            url={'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/sub-selling-point-4.jpg'}
            descList={['终端零售图片多样化风格', '满足不同圈子的审美偏好']} />
        </Flex>

        {/*卖点描述5*/}
        <Flex vertical className={'home-gray-block'} justify={'center'} align={'center'} gap={42}
          style={{ padding: 72 }}>

          <SubSellingPointsCard tips={'提升销售转化'} title={'实体店电子显示屏视频展示'} tipColor={'#262626'}
            type={'video'}
            imgPosition={'left'}
            url={'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/sub-selling-point-5.mp4'}
            descList={['增强视觉营销效果，提升店铺现代感，提升品牌形象', '促进产品展示与推广，提高销售转化率']} />
        </Flex>


        {/*合作客户*/}
        <Flex vertical className={'home-white-block'} justify={'center'} align={'center'} gap={67}
          style={{ padding: '33px 0 100px 0', height: 380 }}>

          <div className={'text40 font-mi-bold home-value-title margin-top-24'}>合作客户</div>

          <Flex align={'center'} justify={'center'} gap={16}>
            {partnerData.map(item => <PartnerCard data={item} key={item.name} />)}
          </Flex>
        </Flex>

        {/*优秀案例列表*/}
        <Flex vertical className={'home-more-customer-cases'} justify={'center'} align={'center'} gap={32}
          style={{ padding: '32px 0 136px 0' }} ref={moreCustomerCaseRef}>

          <div className={'text40 font-mi-bold color-w'}>优秀案例</div>

          {/*<Segmented*/}
          {/*  value={showCaseTag}*/}
          {/*  style={{ backgroundColor: '#E1E3EB' }}*/}
          {/*  onChange={value => setShowCaseTag(value)}*/}
          {/*  options={showCaseItems}*/}
          {/*/>*/}

          <Flex align={'center'} justify={'flex-start'} gap={16} className={'home-content'} wrap={'wrap'}>
            {showCaseList.filter(item => showCaseTag === 'all' || item.tags.includes(showCaseTag)).map((item, index) =>
              <ShowCaseCard data={item} key={index} index={index} gotoCreateSame={gotoCreateSame} />)}
          </Flex>

        </Flex>

        {/* about */}
        <Flex justify={'center'} align={'center'} style={{ height: 346, background: '#000000' }}>
          <Flex vertical className={'home-content'} gap={34} style={{ padding: '40px 0 24px 0' }}>
            <Flex justify={'flex-start'} gap={122}>
              <AboutCard title={<Flex><img src={LOGO} alt={'logo'} width={28} height={28} />
                <img src={BRAND} alt={'logo'} width={95} height={22} /></Flex>} />
              <HiddenInfo />

              {/*<AboutCard title={'联系我们'} desc={'微信客服'} imgUrl={Q_CODE_WX_CS} />*/}
              <AboutCard title={'关注我们'} desc={'微信视频号'} imgUrl={Q_CODE_WX_VIDEO} />
            </Flex>

            <Flex vertical gap={8} align={'center'}>
              <Flex vertical gap={8} align={'flex-start'}>
                <Flex align={'center'} justify={'center'} gap={8}>
                  <div className={'text12 color-w'}>About Us: 杭州霖润智能科技有限公司</div>
                </Flex>

                <Flex align={'center'} justify={'center'} gap={8}>
                  <div className={'text12 color-w'}>
                    ICP备案：<a target="blank" href="https://beian.miit.gov.cn">浙ICP备2023026993号-4</a>
                  </div>
                  <div className={'home-short-line'} />
                  <div className={'text12 color-w'}>
                    算法备案：<a target="blank" href="https://beian.cac.gov.cn">网信算备330110233883701240011号</a>
                  </div>
                </Flex>
              </Flex>

            </Flex>
          </Flex>
        </Flex>

      </Flex>
    </PageContainer>
  );
};

export default HomePage;
