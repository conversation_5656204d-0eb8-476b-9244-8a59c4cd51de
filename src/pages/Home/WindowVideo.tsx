import { PageContainer } from '@ant-design/pro-layout';
import { Flex } from 'antd';
import React from 'react';
import { Q_CODE_WX } from '@/constants';


const WindowVideo = ({}) => {

  return <PageContainer>
    <Flex vertical align={'center'} justify={'flex-start'} gap={16} style={{ marginTop: 56 }}>
      <video width={1066} autoPlay={true} controls={true} loop={true} muted>
        <source
          src={'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/%E6%A9%B1%E7%AA%97AI%E8%A7%86%E5%B1%8F.mp4'}
          type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      <Flex gap={6} align={'center'} style={{
        padding: 8, width: 1066, background: '#FFFFFF', borderRadius: 8, boxSizing: 'border-box',
        border: '2px solid #FFB0B0',
      }}>
        <div style={{ fontSize: 46 }} className={'color-1a'}>定制橱窗视频请联系：</div>
        <img src={Q_CODE_WX} alt={'img'} style={{ width: 86 }} />
      </Flex>
    </Flex>
  </PageContainer>;
};

export default WindowVideo;