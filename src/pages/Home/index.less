@content-width: 1184px;

.home-page {
  height: 100vh;
}

.home-main {
  height: 840px;
  width: 100%;
  background-image: url('https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/bg-main.jpg');
  background-size: cover;
  background-position: center;
}

.home-main-title {
  position: fixed;
  padding: 16px;
  height: 56px;
  width: 100%;
  z-index: 100;
  transition: background-color 0.3s ease, box-shadow 0.3s ease; /* 添加过渡效果 */
}

.home-main-title-scrolled {
  box-sizing: border-box;
  border-width: 0 0 0.5px 0;
  border-style: solid;
  border-color: #E8EAF0;
  background: radial-gradient(26% 100% at 0% 0%, rgba(255, 227, 247, 0.6) 0%, rgba(255, 227, 247, 0) 100%), radial-gradient(0% 67% at 100% 0%, rgba(237, 227, 255, 0.67) 0%, rgba(255, 255, 255, 0) 100%), radial-gradient(1% 100% at 0% 0%, rgba(164, 203, 252, 0.2822) 0%, rgba(102, 186, 255, 0.24) 100%), #FFFFFF;
}

.home-main-menu-selected {
  position: absolute;
  bottom: 0;
  left: 0;

  width: 100%;
  height: 14px;
  border-radius: 4px;
  opacity: 1;
  z-index: 1;

  background: #AFFFF4;
}

.home-navigation-menu {
  width: 92px;
  height: 36px;
  border-radius: 4px;
}

.home-navigation-menu-selected {
  background: rgba(255, 255, 255, 0.6);
}

.home-navigation-menu:hover {
  background: rgba(255, 255, 255, 0.6);
}

.home-title {
  font-family: DingTalk JinBuTi, serif;
  font-size: 64px;
  font-weight: normal;
  line-height: 78px;
  text-align: center;
  letter-spacing: 0;

  background: radial-gradient(47% 208% at 102% 137%, #0D09FF 0%, rgba(188, 170, 255, 0) 100%), #343434;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.home-title-highlight {
  background: radial-gradient(88% 88% at 100% 105%, #3A68FF 0%, rgba(62, 125, 234, 0) 100%), #7E4DEE;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.home-sub-title {
  font-family: MiSans, serif;
  font-size: 30px;
  text-align: center;
  background: radial-gradient(47% 208% at 102% 137%, #0D09FF 0%, rgba(188, 170, 255, 0) 100%), #53517E;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.home-main-btn {
  width: 234px;
  height: 59px;
  border-radius: 270px;
  opacity: 1;

  background: url('@/assets/images/index/bg-index-main-btn.png') center/cover no-repeat, linear-gradient(154deg, rgba(24, 28, 255, 0.86) 2%, rgba(133, 181, 251, 0) 54%), linear-gradient(341deg, #8AC4FE 9%, rgba(154, 21, 255, 0) 60%), linear-gradient(102deg, #8DCCFF 6%, #4A00DE 99%);
  //background: linear-gradient(154deg, rgba(24, 28, 255, 0.86) 2%, rgba(133, 181, 251, 0) 54%), linear-gradient(341deg, #8AC4FE 9%, rgba(154, 21, 255, 0) 60%), linear-gradient(102deg, #8DCCFF 6%, #4A00DE 99%);

  box-sizing: border-box;
  border: 1px solid rgba(255, 255, 255, 0.6);

  box-shadow: inset 0 4px 10px 0 rgba(255, 255, 255, 0.62);
}

.home-main-btn:disabled {
  color: #FFFFFF;
  background: url('@/assets/images/index/bg-index-main-btn.png') center/cover no-repeat, linear-gradient(0deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.6)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%);
}

.home-main-btn:not(:disabled):hover, .home-main-btn:not(:disabled):focus {
  background: url('@/assets/images/index/bg-index-main-btn.png') center/cover no-repeat, linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.home-main-btn:not(:disabled):active {
  background: url('@/assets/images/index/bg-index-main-btn.png') center/cover no-repeat, linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.home-selling-points-txt {
  width: 100%;
  height: 64px;

  font-family: MiSans, serif;
  font-size: 32px;
  font-weight: 600;
  line-height: 46px;
  letter-spacing: 0;
  color: #1A1B1D;
  padding: 20px;

  background: rgba(255, 255, 255, 0.7);

  box-sizing: border-box;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #FFFFFF 100%);

  backdrop-filter: blur(173px);
}

.home-selling-points-card {
  width: 284px;
  //height: 328px;
  border-radius: 24px;
  overflow: hidden;

  transition: width 0.7s ease; /* 动画效果 */

  background: rgba(255, 255, 255, 0.4);

  box-sizing: border-box;
  border: 1px solid;
  border-image: linear-gradient(180deg, #FFFFFF 68%, rgba(255, 255, 255, 0) 100%);
}

.home-selling-points-card-desc {
  display: flex;
  margin-left: 0;
  width: 0;
  overflow: hidden;
  opacity: 0;
  transition: opacity 0.3s ease 0.3s, margin-left 0.5s ease; /* 添加过渡动画 */
}

.home-selling-points-card:hover {
  width: 578px !important;

  .home-selling-points-txt {
    justify-content: flex-start;
  }

  .home-selling-points-card-desc {
    display: flex;
    width: auto;
    opacity: 1;
    margin-left: 38px;
  }
}

body:has(.home-selling-points-card:hover) .home-selling-points-card:not(:hover) {
  width: 184px !important;

  .home-selling-points-card-desc {
    display: none;
  }
}

//.home-selling-points-card:not(:hover) {
//  width: 184px !important;
//}

.home-selling-points-card-img {
  object-fit: cover; /* 确保图片内容填充且裁剪 */
  object-position: center; /* 水平居中显示中间部分 */
}

.home-selling-points-img {
  margin: 4px 4px 0 4px;
  //width: 268px;
  height: 248px;
  border-radius: 20px;
  opacity: 1;

  background: #D8D8D8;
  overflow: hidden;
}

.home-show-case {
  width: 100%;
  height: 592px;
  padding: 40px 0 48px 0;
  background: linear-gradient(180deg, #151515 0%, #082052 100%);
  //background: url('@/assets/images/index/bg-index-main-btn.png') center/cover no-repeat;
}

.home-show-case-more {
  width: 1184px;
  height: 48px;
  border-radius: 99px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
}

.home-show-case-more:hover {
  background: rgba(255, 255, 255, 0.4);
}

.home-content {
  width: @content-width;
}

.home-show-case-width {
  width: calc((@content-width - 3 * 16px) / 4);
}

.home-show-case-height {
  height: calc((@content-width - 3 * 16px) / 3);
}

.home-show-case-video-height {
  height: calc((@content-width - 3 * 16px) / 3 + 12px);
}

.home-show-case-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  background: rgba(0, 0, 0, 0.4);
}

.home-upscale-block {
  position: relative;
  border-radius: 16px;

  img {
    transition: transform 0.3s ease; /* 添加过渡动画 */
    object-fit: cover;
  }
}

.home-upscale-block:hover {
  img {
    transform: scale(1.2);
  }

  .home-show-case-mask {
    display: flex;
  }
}

.home-mini-img-title {
  width: 44px;
  height: 66px;
  border-radius: 4px;
  opacity: 1;

  padding: 2px;

  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  z-index: 0;
}

.home-gray-block {
  width: 100%;
  height: 592px;
  padding: 0 130px;
  background: #F5F6F9;
}

.home-white-block {
  width: 100%;
  height: 592px;
  padding: 0 130px;
  background: #FFFFFF;
}

.home-main-login-btn {
  width: 96px;
  height: 32px;
  border-radius: 216px;
  opacity: 1;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0;

  box-sizing: border-box;
  border: 1px solid rgba(0, 0, 0, 0.2);

  color: rgba(0, 0, 0, 0.4);
  font-family: PingFang SC, serif;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  text-align: center;
  letter-spacing: 0;

  z-index: 1;
}


.home-value-title {
  background: radial-gradient(26% 74% at 0% 0%, rgba(116, 85, 255, 0.44) 0%, rgba(255, 198, 85, 0) 100%), radial-gradient(41% 116% at 73% 109%, rgba(255, 198, 85, 0.44) 0%, rgba(255, 198, 85, 0) 100%), #000000;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.home-tips {
  height: 44px;
  border-radius: 18px 8px 18px 8px;
  padding: 8px;
  background: #FF8C3A;
  font-weight: 600;
  z-index: 0;
}

.home-selling-points-title {
  background: radial-gradient(114% 114% at 50% 50%, #1A1B1D 0%, #322B40 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  z-index: 0;
}

.home-partner-card {
  width: 183.33px;
  height: 126px;
  border-radius: 32px;
  opacity: 1;
  padding: 16px 0;
  flex-grow: 1;
  background: #F5F6F9;
  z-index: 0;
}

.home-partner-logo {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  opacity: 1;
  background: #FFFFFF;
}

.home-more-customer-cases {
  background: linear-gradient(180deg, #151515 0%, #082052 100%);
}

.home-creative-same-btn {
  position: absolute;
  right: 8px;
  bottom: 8px;

  width: 112px;
  height: 48px;
  border-radius: 8px;
  opacity: 1;

  background: rgba(255, 255, 255, 0.2);

  box-sizing: border-box;
  border: 1px solid rgba(255, 255, 255, 0.4);

  backdrop-filter: blur(4px);
}

.home-creative-same-btn-icon-bg {
  position: relative;
  border-radius: 50%;
  z-index: 2;
}

.home-creative-same-btn:hover {
  background: rgba(255, 255, 255, 0.4);

  .home-creative-same-btn-icon-bg {
    background-color: #000000;
  }
}

.home-tag {
  position: absolute;
  left: 12px;
  top: 12px;
  height: 34px;
  border-radius: 4px;

  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;

  padding: 6px 12px;
  gap: 8px;

  background: rgba(0, 0, 0, 0.6);
}

.home-show-case-element {
  width: 184px;
  height: 244px;
  border-radius: 8px;
  opacity: 1;
  padding: 8px;
  gap: 8px;
  background: #F2F5FC;
  backdrop-filter: blur(10px);
  z-index: 0;
}

.home-show-case-collocation {
  width: 400px;
  height: 216px;
  border-radius: 8px;
  opacity: 1;

  padding: 8px;

  background: #F2F5FC;

  backdrop-filter: blur(10px);

  z-index: 1;
}

.home-show-case-collocation-tag {
  min-height: 38px;
  height: auto;
  border-radius: 135px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  padding: 8px;
  background: #FFFFFF;
  box-sizing: border-box;
  border: 1px solid #C8C9CC;
  z-index: 2;
  white-space: normal;
}

.home-show-case-btn {
  width: 1136px;
  height: 67px;
  border-radius: 107px;
  opacity: 1;

  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 8px;

  background: radial-gradient(97% 97% at 53% 100%, #8761F9 0%, #0060FF 100%) !important;
}

.home-show-case-btn:not(:disabled):hover, .home-show-case-btn:not(:disabled):focus {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.home-show-case-btn:not(:disabled):active {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.home-show-case-config-bg {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 78px;
  border-radius: 0 0 16px 16px;
  opacity: 1;

  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%);
}

.home-short-line {
  width: 8px;
  height: 0;
  transform: rotate(90deg);
  border-top: 1px solid #D8D8D8;
  z-index: 1;
}


.home-nav-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: auto;
  background: linear-gradient(to bottom right, #020203, #322858);
  border-color: rgba(231, 231, 231, 0);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);

  padding: 15px;
  z-index: 100;
}

.home-nav-block {
  width: 1170px;
  min-height: 50px;

  display: flex;
  flex-direction: row;
  justify-content: space-between;

  padding: 0 15px;
}

.navbar-brand {
  font-family: "Raleway", sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: white;
  text-transform: uppercase;
  cursor: pointer;
}

.navbar-item {
  width: auto;
  font-family: "Lato", sans-serif;
  text-transform: uppercase;
  color: white;
  font-size: 15px;
  font-weight: 400;
  text-align: center;
  padding: 8px 2px;
  border-radius: 0;
  margin: 9px 20px 0;
}

.navbar-item:after {
  position: absolute;
  margin-top: 12px;
  width: 0;
  height: 2px;

  display: block;
  background: linear-gradient(to right, #1D73FF 0%, #9478EA 100%);
  content: "";
  transition: width 0.2s;
}

.navbar-item:hover:after {
  width: 78px;
}

.navbar-item-about:hover:after {
  width: 50px;
}

.navbar-item,
.navbar-item:hover,
.navbar-item:focus {
  background-color: transparent;
}

.home-header-container {
  margin-top: 80px;
  width: 100%;
  height: 938px;
  background: #e5e5e5 url(@/assets/images/index-header.png) no-repeat center center;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  background-size: cover;
  -o-background-size: cover;
}

.home-header-block {
  width: 100%;
  height: 938px;
  background: rgba(0, 0, 0, 0.2);
  padding-top: 350px;
  padding-bottom: 200px;
}

.home-intro-title {
  font-family: "Raleway", sans-serif;
  color: #fff;
  font-size: 82px;
  font-weight: 700;
  text-transform: uppercase;
  margin-top: 0;
  margin-bottom: 10px;

  width: 750px;
  text-align: center;
}

.home-intro-desc {
  width: 750px;
  color: #fff;
  font-size: 22px;
  font-weight: 300;
  line-height: 30px;
  margin-bottom: 60px;
  text-align: center;
}

.section-title {
  margin-bottom: 70px;
}

.section-title h2 {
  position: relative;
  margin-top: 10px;
  margin-bottom: 15px;
  padding-bottom: 15px;
  font-weight: 800;
  font-size: 36px;
  color: #333333;
}

.section-title h2::after {
  position: absolute;
  content: "";
  background: linear-gradient(to right, #1D73FF 0%, #9478EA 100%);
  height: 4px;
  width: 60px;
  bottom: 0;
  margin-left: -30px;
  left: 50%;
}

.section-title p {
  font-size: 18px;
}

.home-features-block {
  margin-bottom: 12px;
}

.home-features-item {
  width: 260px;
  text-align: center;
}

.home-features-desc {
  color: #777777;
}

.home-about-container {
  margin: 0 375px;
  padding: 100px 0;
}

.about-text h2 {
  margin-bottom: 6px;
  margin-left: 6px;
  list-style: none;
  padding: 0;
  font-weight: 800;
  font-size: 36px;
  color: #FFFFFF;
}

.about-text h2:before {
  font-family: "FontAwesome";
  color: #78dd02;
  font-size: 11px;
  font-weight: 300;
  padding-right: 8px;
}

.about-text h2::after {
  position: absolute;
  content: "";
  background: linear-gradient(to right, #1D73FF 0%, #9478EA 100%);
  height: 4px !important;
  width: 60px !important;
  left: 50%;
  margin-left: 10px;
  margin-top: 50px;
}

.about-text div {
  color: #777777;
}

#about img {
  width: 520px;
  margin-top: 10px;
  background: #fff;
  border-right: 0;
}

.btn-custom {
  font-family: "Raleway", sans-serif;
  text-transform: uppercase;
  color: #fff;
  background-color: #1D73FF;
  background-image: linear-gradient(to right, #1D73FF 0%, #9478EA 100%);
  padding: 14px 34px;
  letter-spacing: 1px;
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  border-radius: 25px;
  transition: all 0.5s linear;
  border: 0;
}

.btn-custom:hover,
.btn-custom:focus,
.btn-custom.focus,
.btn-custom:active,
.btn-custom.active {
  color: #fff;
  background-image: none;
  background-color: #6150CA;
}

.tutorial-item-card {
  position: relative;
  border-radius: 8px;
  background: #FFFFFF;
  height: 120px;
}

.tutorial-item-card-title {
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  text-align: center;
  letter-spacing: 0;
  color: #1A1B1D;
}

.tutorial-item-card-selected {
  border: 2px solid #366EF4;

  .tutorial-item-card-title {
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
    text-align: center;
    letter-spacing: 0;
    color: #366EF4;
  }
}

.tutorial-item-card-tag {
  position: absolute;
  top: 0;
  right: 0;
  width: 96px;
  height: 28px;
  background: radial-gradient(65% 65% at 50% 50%, #D9E1FF 0%, #E6F6FF 100%);
  border-radius: 0 8px 0 16px;
}

.advanced-tag {
  background: radial-gradient(72% 72% at 50% 50%, #393596 0%, #7B75EE 100%);

  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  text-align: center;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 白色 */
  color: #FFFFFF !important;
}

.tutorial-item-card-tag-advance {
  background: radial-gradient(102% 102% at 70% 0%, #FEFFDD 0%, rgba(255, 246, 221, 0.49) 100%);

  div {
    color: #F6B553;
  }
}

.tutorial-item-video {
  padding: 100px 40px 100px 40px;
  background-color: #F5F6F9;
  width: 100%;
  height: calc(100VH - 56px);
}

.show-case-card-video {
  .show-case-card-video-div {
    display: block;

    position: absolute;
    top: 12px;
    right: 12px;
  }
}

.show-case-card-video:hover {

  .show-case-card-video-div {
    display: none;
  }
}

body:has(.show-case-card-video:hover) .show-case-card-video:not(:hover) {

  .show-case-card-video-div {
    display: block;
  }
}