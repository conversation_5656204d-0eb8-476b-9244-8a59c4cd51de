import imgShowCase1 from '@/assets/images/index/img-show-case-1.jpg';
import imgShowCase1Goods from '@/assets/images/index/img-show-case-1-goods.jpg';
import imgShowCase1Model from '@/assets/images/index/img-show-case-1-model.jpg';
import imgShowCase1Scene from '@/assets/images/index/img-show-case-1-scene.jpg';
import logoYalu from '@/assets/images/index/logo-yalu.png';
import logoAlpinpepro from '@/assets/images/index/logo-ALPINPEPRO.png';
import logoFengxi from '@/assets/images/index/logo-fengxi.png';
import logoNamguagu from '@/assets/images/index/logo-nanguagu.png';
import logoRongmei from '@/assets/images/index/logo-rongmei.png';
import logoYabaobao from '@/assets/images/index/logo-yabaobao.png';

import {
  IMG_SELLING_POINTS_11,
  IMG_SELLING_POINTS_12,
  IMG_SELLING_POINTS_13,
  IMG_SELLING_POINTS_31,
  IMG_SELLING_POINTS_32,
  IMG_SELLING_POINTS_33,
} from '@/constants';
import { ShowCaseDetail } from '@/services/ShowCaseController';
import { SHOW_CASE_TAGS } from '@/components/Operate/ShowCaseBlock';

export const sellingPointsData = [
  {
    title: '一衣多搭',
    desc: ['敢想就能搭', 'AI模特演绎服饰百变风格'], //多行分多个
    type: 'IMAGE',
    mainUrl: ['https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/1-1.jpg', 'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/1-2.jpg', 'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/1-3.jpg'],
    extUrl: [''],
    goods: '',
    model: '',
    scene: '',
  },
  {
    title: '一衣多国',
    desc: ['还原海外本土审美', 'AI模特助力原生态出海'], //多行分多个
    type: 'IMAGE',
    mainUrl: ['https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/2-1.jpg', 'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/2-2.jpg', 'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/2-3.jpg'],
    extUrl: [''],
    goods: '',
    model: '',
    scene: '',
  },
  {
    title: '一衣多码',
    desc: ['维密都有大码模特了', 'AI助力追赶国际趋势'], //多行分多个
    type: 'IMAGE',
    mainUrl: ['https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/3-1.jpg', 'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/3-2.jpg', 'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/3-3.jpg'],
    extUrl: [''],
    goods: '',
    model: '',
    scene: '',
  },
  {
    title: '一衣多色',
    desc: ['多种颜色，一套价格', 'AI为您极致降本'], //多行分多个
    type: 'IMAGE',
    mainUrl: ['https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/4-1.jpg', 'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/4-2.jpg', 'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/4-3.jpg'],
    extUrl: [''],
    goods: '',
    model: '',
    scene: '',
  },
];


export const partnerData = [
  {
    name: '鸭宝宝',
    logo: logoYabaobao,
  },
  {
    name: '戎美',
    logo: logoRongmei,
  },
  {
    name: '雅鹿',
    logo: logoYalu,
  },
  {
    name: '南瓜谷',
    logo: logoNamguagu,
    width: 58,
    height: 12,
  },
  {
    name: 'ALPINPEPRO',
    logo: logoAlpinpepro,
    width: 58,
    height: 42,
  },
  // {
  //   name: '凤禧服饰',
  //   logo: logoFengxi,
  //   width: 42,
  //   height: 42,
  //   background: '#AA99C3',
  // },
];

export const showCaseData: Array<ShowCaseDetail> = [
  {
    type: 'IMAGE',
    topped: true,
    tags: ['日韩系', '甜美可爱', '法式文艺', '时尚优雅', '性感御姐', '个性时髦'],
    mainUrl: imgShowCase1,
    showImage: imgShowCase1,
    modelMiniUrl: imgShowCase1Goods,
    modelUrl: imgShowCase1Goods,
    face: { name: 'sophia', url: imgShowCase1Model, id: 7 },
    scene: { name: '绿色草地', url: imgShowCase1Scene, id: 39 },
    clothCollocation: {
      tops: '',
      shoe: '高跟鞋,丝袜',
      others: '太阳镜,钻石项链',
      bottoms: '',
      props: '手提包',
    },
  },
  {
    type: 'VIDEO',
    topped: true,
    tags: ['日韩系', '性感御姐'],
    mainUrl: 'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/video/product_621949_00001_v_f_alpFu.mp4',
    showImage: 'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/video/product_621949_00001_v_f_alpFu.mp4',
    modelMiniUrl: imgShowCase1Goods,
    modelUrl: imgShowCase1Goods,
    face: { name: 'sophia', url: imgShowCase1Model, id: 7 },
    scene: { name: '绿色草地', url: imgShowCase1Scene, id: 39 },
    clothCollocation: {
      tops: '',
      shoe: '高跟鞋,丝袜',
      others: '太阳镜,钻石项链',
      bottoms: '',
      props: '手提包',
    },
  },
  {
    type: 'IMAGE',
    topped: true,
    tags: ['日韩系'],
    mainUrl: imgShowCase1,
    showImage: imgShowCase1,
    modelMiniUrl: imgShowCase1Goods,
    modelUrl: imgShowCase1Goods,
    face: { name: 'sophia', url: imgShowCase1Model, id: 7 },
    scene: { name: '绿色草地', url: imgShowCase1Scene, id: 39 },
    clothCollocation: {
      tops: '',
      shoe: '高跟鞋,丝袜',
      others: '太阳镜,钻石项链',
      bottoms: '',
      props: '手提包',
    },
  },
  {
    type: 'IMAGE',
    topped: true,
    tags: ['日韩系'],
    mainUrl: imgShowCase1,
    showImage: imgShowCase1,
    modelMiniUrl: imgShowCase1Goods,
    modelUrl: imgShowCase1Goods,
    face: { name: 'sophia', url: imgShowCase1Model, id: 7 },
    scene: { name: '绿色草地', url: imgShowCase1Scene, id: 39 },
    clothCollocation: {
      tops: '',
      shoe: '高跟鞋,丝袜',
      others: '太阳镜,钻石项链',
      bottoms: '',
      props: '手提包',
    },
  },
];

const buildLabel = (tag) => {
  return <div className={'text14 color-1a text-center'} style={{ width: 70, lineHeight: '28px' }}>{tag}</div>;
};
export const getShowCaseItems = () => {
  const result = SHOW_CASE_TAGS.map(tag => {
    return { label: buildLabel(tag), value: tag };
  });
  result.unshift({ label: buildLabel('全部'), value: 'all' });
  return result;
};
