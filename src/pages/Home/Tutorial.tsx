import NavigationBar from '@/components/Home/NavigationBar';
import React, { useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Flex } from 'antd';
import IconFont from '@/components/IconFont';
import { useLocation, useNavigate } from 'react-router-dom';

const data = [
  {
    id: 1,
    title: '第一步 拍摄服装',
    tag: '基础必看',
    video: 'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/%E7%AC%AC1%E6%AD%A5%20%E6%8B%8D%E6%91%84%E6%9C%8D%E8%A3%85.mp4',
    level: 1,
    videoExtra: {
      title: '图片拍摄注意事项 文字图片版',
      url: '/capture-notice',
    },
  },
  {
    id: 2,
    title: '第二步 上传照片',
    tag: '基础必看',
    video: 'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/%E7%AC%AC2%E6%AD%A5%20%E4%B8%8A%E4%BC%A0%E7%85%A7%E7%89%87.mp4',
    level: 1,
  },
  {
    id: 3,
    title: '第三步 创作图片',
    tag: '基础必看',
    video: 'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/%E7%AC%AC3%E6%AD%A5%20%E5%88%9B%E4%BD%9C%E5%9B%BE%E7%89%87.mp4',
    level: 1,
  },
  {
    id: 4,
    title: '真人拍摄服装，',
    content: '生成更灵动的姿势',
    tag: '进阶教程',
    video: 'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/%E6%95%99%E7%A8%8B_%E7%9C%9F%E4%BA%BA%E6%8B%8D%E6%91%84.mp4',
    level: 1,
  },
  {
    id: 5,
    title: '如何创建风格场景',
    tag: '基础必看',
    video: 'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/%E9%A3%8E%E6%A0%BC%E5%9C%BA%E6%99%AF%E5%8F%82%E8%80%83%E5%9B%BE%E4%B8%8A%E4%BC%A0%E8%A7%84%E8%8C%83.mp4',
    level: 2,
  },
  {
    id: 6,
    title: '如何创建模特',
    tag: '基础必看',
    video: 'https://conrain-index.oss-cn-zhangjiakou.aliyuncs.com/index2411/%E6%A8%A1%E7%89%B9%E5%9B%BE%E4%B8%8A%E4%BC%A0%E8%A7%84%E8%8C%83.mp4',
    level: 3,
  }
];

const TutorialPage: React.FC = () => {
  const location = useLocation();

  //跳转时页面参数指定了教程idx
  const queryParams = new URLSearchParams(location.search);
  const idx = queryParams.get('idx') ? Number(queryParams.get('idx')) : 1;

  const navigate = useNavigate();
  const [current, setCurrent] = useState<any>(idx);

  const getCurrent = () => {
    return data.find(e => e.id === current);
  };

  const getCurrentVideo = () => {
    return getCurrent()?.video ? getCurrent()?.video : '';
  };

  const goto = () => {
    const path = getCurrent()?.videoExtra?.url;
    if (path) {
      navigate(path);
    }
  };

  const ItemCard = ({ data }) => <>
    <Flex vertical className={'tutorial-item-card' + (current === data.id ? ' tutorial-item-card-selected' : '')}
          align={'center'} justify={'center'} onClick={() => setCurrent(data.id)}>
      <div className={'tutorial-item-card-title'}>{data.title}</div>
      <div className={'tutorial-item-card-title'}>{data.content}</div>

      <Flex justify={'center'} align={'center'}
            className={'text16 weight color-36 tutorial-item-card-tag' + (data.tag === '进阶教程' ? ' advanced-tag' : '')}>
        {data.tag}
      </Flex>
    </Flex></>;

  return <PageContainer>
    <Flex vertical className={'home-page'} style={{ background: '#FFFFFF' }}>
      {/*header区域*/}
      <NavigationBar needBorder={true} />

      <Flex style={{ marginTop: 56 }}>
        <Flex vertical style={{ padding: 16 }} gap={16}>
          <Flex vertical gap={8} style={{ width: 264, borderRadius: 8, padding: 8, background: '#F5F6F9' }}>
            <div className={'text16 weight color-1a'}>创建服装教程</div>
            {data.filter(e => e.level === 1).map(e => <ItemCard data={e} key={e.id} />)}
          </Flex>

          <Flex vertical gap={8} style={{ width: 264, borderRadius: 8, padding: 8, background: '#F5F6F9' }}>
            <div className={'text16 weight color-1a'}>创建场景教程</div>
            {data.filter(e => e.level === 2).map(e => <ItemCard data={e} key={e.id} />)}
          </Flex>

          <Flex vertical gap={8} style={{ width: 264, borderRadius: 8, padding: 8, background: '#F5F6F9' }}>
            <div className={'text16 weight color-1a'}>创建模特教程</div>
            {data.filter(e => e.level === 3).map(e => <ItemCard data={e} key={e.id} />)}
          </Flex>

        </Flex>

        <Flex vertical gap={16} className={'tutorial-item-video'} justify={'flex-start'} align={'center'}>

          <video height={600} autoPlay={true} controls={true} loop={true} muted key={getCurrentVideo()}>
            <source src={getCurrentVideo()} type="video/mp4" />
            Your browser does not support the video tag.
          </video>

          {getCurrent()?.videoExtra &&
            <Flex justify={'center'} align={'center'} gap={2} className={'pointer'}
                  style={{ background: '#D9E1FF', padding: 4, width: 213, borderRadius: 4 }}
                  onClick={goto}>
              <div className={'text14 weight color-brand'}>{getCurrent()?.videoExtra?.title}</div>
              <IconFont type={'icon-youjiantou_16px'} style={{ fontSize: 16 }} className={'color-brand'} />
            </Flex>
          }

        </Flex>
      </Flex>

    </Flex>
  </PageContainer>;
};

export default TutorialPage;