@import "@/app";

.cap-nav-bar {
  width: 100%;
  height: 56px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 12px 16px;
  gap: 981px;

  background: #FFFFFF;

  box-sizing: border-box;
  border-width: 0px 0px 0 0px;
  border-style: solid;
  border-color: #E8EAF0;
}

.capture-notice-container {
  position: relative;
  padding: 16px;

  width: 100%;
  border-radius: 6px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 16px;

  background: linear-gradient(180deg, #EDEAF1 0%, #F6F3EE 100%);

  box-sizing: border-box;
  border: 1px solid #FFFFFF;

  z-index: 1;

  min-width: 1300px;
}

.capture-notice-title {
  width: 200px;
  font-size: 24px;
  font-weight: 900;
  line-height: 32px;
  letter-spacing: 0;

  background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.capture-notice-close {
  position: absolute;
  right: -40px;
  top: 2px;

  font-size: 24px;
  color: #FFFFFF;
}


.capture-collapse-container {
  position: absolute;
  right: 16px;
  top: 16px;

  width: 64px;
  height: 24px;
  border-radius: 8px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 2.67px;
  box-sizing: border-box;
  border: 0.67px solid #D8D8D8;
  z-index: 1;
  background: white;
}

.capture-collapse-text{
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: normal;
  line-height: 18px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 中性色/N6 */
  color: #969799;
}

.capture-notice-details {
  height: 636px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  padding: 0px;
  gap: 16px;
  align-self: stretch;
  z-index: 1;
}

.notice-1-2 {
  width: 17%;
  height: 636px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 0;
}

.capture-notice-item {
  border-radius: 8px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  flex-grow: 1;

  background: rgba(255, 255, 255, 0.6);

  box-sizing: border-box;
  border: 1px solid #FFFFFF;

  backdrop-filter: blur(122px);

  z-index: 0;
}

.capture-title-number-row {
  height: 32px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  gap: 8px;

  z-index: 0;
}

.capture-item-number {
  width: 24px;
  height: 24px;
  border-radius: 8px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0px;
  background: linear-gradient(90deg, #592DF8 0%, #886DE1 100%);
  z-index: 0;


  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 白色 */
  color: #FFFFFF;
}

.capture-item-title {
  width: 140px;
  height: 32px;
  opacity: 1;

  font-family: PingFang SC;
  font-size: 20px;
  font-weight: 500;
  line-height: 32px;
  letter-spacing: 0px;

  /* 中性色/N8-主文字 */
  color: #1A1B1D;

  z-index: 1;
}

.capture-item-content {
  margin-top: 16px;
  opacity: 1;

  /* body/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 中性色/N8-主文字 */
  color: #1A1B1D;

  z-index: 1;
}