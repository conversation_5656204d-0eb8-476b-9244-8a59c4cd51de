import { PageContainer } from '@ant-design/pro-layout';
import { Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import UploadClothPage from '@/pages/UploadMaterial/uploadCloth';
import UploadCommonMaterialPage from '@/pages/UploadMaterial/uploadCommonTab';
import Flow from '@/components/Flow/flow';
import { useLocation, useNavigate } from 'react-router-dom';
import CaptureNoticeWrapper from '@/pages/UploadMaterial/UploadCaptureNotice';
import './uploadMaterial.less';
import { getTutorialIdx, getUserInfo } from '@/utils/utils';

const UploadMaterialTotalPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  //cloth|face|scene
  const [materialType, setMaterialType] = React.useState<string>('cloth');
  const [materialTypeTitle, setMaterialTypeTitle] = useState<string>('服装');

  const userInfo = getUserInfo();

  useEffect(() => {

    //跳转时页面参数指定了类型
    const queryParams = new URLSearchParams(location.search);
    const type = queryParams.get('type');
    setMaterialType(type || 'cloth');
  }, []);

  useEffect(()=>{
    if (materialType === 'cloth') {
      setMaterialTypeTitle('服装');
    } else if (materialType === 'face') {
      setMaterialTypeTitle('模特');
    } else if (materialType === 'scene') {
      setMaterialTypeTitle('场景');
    }
  }, [materialType]);


  return (
    <PageContainer>

      {/*步骤条*/}
      {userInfo && userInfo?.roleType !== 'ADMIN' &&
        <Flow activeStep={1} />
      }

      {/*拍摄提示*/}
      <CaptureNoticeWrapper materialType={materialType} materialTypeTitle={materialTypeTitle} onClick={() => navigate('/tutorial?idx=' + getTutorialIdx(materialType))} />

      <div className={'upload-main'}>

        <div className={'tabs-sticky-container'}>
          <Tabs
            activeKey={materialType}
            items={[
              { key: 'cloth', label: '创建服装' },
              { key: 'face', label: '创建模特' },
              { key: 'scene', label: '创建场景' },
            ]}
            onChange={(key) => {
              setMaterialType(key)
            }}
          />
        </div>

        {materialType === 'cloth' &&
          <UploadClothPage />
        }

        {materialType === 'face' &&
          <UploadCommonMaterialPage materialType={'face'} materialTypeTitle={materialTypeTitle} />
        }

        {materialType === 'scene' &&
          <UploadCommonMaterialPage materialType={'scene'} materialTypeTitle={materialTypeTitle} />
        }
      </div>

    </PageContainer>
  )
}

export default UploadMaterialTotalPage;