import React, { useEffect, useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import {
  Button,
  Checkbox,
  Flex,
  Input,
  InputNumber,
  message,
  Modal,
  notification, Popconfirm,
  Radio,
  Space,
  Spin,
  Tooltip,
} from 'antd';
import IconFont from '@/components/IconFont';
import { checkMaterialNameExists, MaterialType, uploadCommonMaterialInfo } from '@/services/MaterialInfoController';
import { useNavigate } from 'react-router-dom';
import './uploadCloth.less';
import './uploadMaterial.less';
import animationData from '@/assets/animations/uploadRet.json';
import ImageLottieCarousel from '@/components/ImageLottieCarousel';
import { queryImagePoint } from '@/services/PointController';
import TopupModal from '@/pages/Topup/Topup';
import { queryUserProfileByKey, setUserProfileByKey, UserVO } from '@/services/UserController';
import { queryCustomerMusePoint } from '@/services/DistributorController';
import TextArea from 'antd/lib/input/TextArea';
import { queryConfigByKeys } from '@/services/SystemController';
import { formatPrompt } from '@/components/Common/SystemConfigSwitch';

import uploadFaceBgPng from '@/assets/images/上传模特底图@1x.png';
import uploadFaceBgLeftPng from '@/assets/images/上传模特底图_左.png';
import uploadFaceBgRightPng from '@/assets/images/上传模特底图_右.png';
import uploadSceneBgPng from '@/assets/images/上传场景底图@1x.png';
import uploadDemoCenter from '@/assets/images/face/model_upload_demo_center.jpg';
import uploadDemoLeft from '@/assets/images/face/model_upload_demo_left.jpg';
import uploadDemoRight from '@/assets/images/face/model_upload_demo_right.jpg';

import { ExclamationCircleOutlined } from '@ant-design/icons';
import DisclaimerModal from '@/components/Disclaimer/DisclaimerModal';
import { getTutorialIdx, getUserInfo } from '@/utils/utils';
import LoraPromptsSwitch from '@/components/Lora/LoraPromptsSwitch';
import BatchUploader, { BatchUploaderRef, fetchUploadUrl, lengthOfUpload } from '@/components/Common/BatchUploader';
import { batchQueryCreative, CreativeVO, facePinching, isProcessing } from '@/services/CreativeController';
import FacePinchingImageBlock from '@/components/Lora/FacePinchingImageBlock';
import AgeRangeSelector from '@/components/AgeRangeSelector';
import ClothCategorySelect from '@/components/Operate/ClothCategory';
import { ClothCategoryGroup, getClothCategoryCfg } from '@/services/ElementController';

interface UploadCommonPageProps {
  //指定的上传素材类型，face | scene
  materialType: string;
  materialTypeTitle: string;
}

interface UploadItem {
  index: number;
  url?: string;//上传服务端前是本地地址，上传服务端后与imgUrl相同，为远程地址
  file?: any;//文件对象
  imgUrl?: string;//远程地址oss url
  uploadSizeError?: boolean; //是否上传大小超出限制
  uploadSizeRatioError?: boolean;//宽高比是否有误
}

const FP_SESSION_KEY = 'facePinchingTask';
const UploadCommonMaterialPage: React.FC<UploadCommonPageProps> = ({ materialType, materialTypeTitle }) => {
  const [loraName, setLoraName] = useState('');

  //素材类型，男装｜女装｜童装, male|female|child
  const [sexType, setSexType] = useState(materialType === 'face' ? '' : 'female');
  const [ageRange, setAgeRange] = useState(materialType === 'face' ? '' : 'adult');
  const [childSize, setChildSize] = useState<string | null>(null);
  const [sceneType, setSceneType] = useState<string>('REAL-SCENE-SHOOTING');
  const [clothCategory, setClothCategory] = useState<string[]>([]);

  //素材是否为专属，模特只能专属，不可选；场景可以选择，专属或不专属，默认为专属
  const [exclusive, setExclusive] = useState(true);

  const [showRetDialog, setShowRetDialog] = useState<boolean>(false);
  const [retImgs, setRetImgs] = useState<string[]>([]);

  const [flag, setFlag] = useState<boolean>(false);

  const [imgPoint, setImgPoint] = useState<number>(0);

  const navigate = useNavigate();

  const [clothNameError, setNameError] = useState('');

  const [clothCategoryCfg, setClothCategoryCfg] = useState<ClothCategoryGroup[]>([]);

  const [showTopupModal, setShowTopupModal] = useState(false);

  const [userInfo, setUserInfo] = useState<UserVO>();
  const [principalId, setPrincipalId] = React.useState<null | number>(null);

  const [loraMusePoint, setLoraMusePoint] = useState(0);

  const [trainRepeatTimes, setTrainRepeatTimes] = useState<number | null>(15);

  const [isFacePinching, setIsFacePinching] = useState<boolean | null>(null);
  const [fpUploadItems, setFpUploadItems] = useState<UploadItem[]>([]);
  const [fpSelected, setFpSelected] = useState<string[]>([]);
  const [fpSkinType, setFpSkinType] = useState<string>('realistic');

  //是否需要预处理被裁剪的人脸 Y|N
  const [preprocessCensoredFace, setPreprocessCensoredFace] = useState<string>('N');

  //确认学习二次弹窗
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [showLearnDoubleConfirmModal, setShowLearnDoubleConfirmModal] = useState(false);

  const [uploadItems, setUploadItems] = useState<UploadItem[]>([]);

  const [captionPrompt, setCaptionPrompt] = useState<string>('');

  const [noshowFace, setNoshowFace] = useState<string | null>('N');

  const [waterMarkDesc, setWaterMarkDesc] = useState<string>('');
  const [waterMarkDescType, setWaterMarkDescType] = useState<string | null>('none');
  const [hasWaterMark, setHasWaterMark] = useState(false);
  const [waterMarkDescPos, setWaterMarkDescPos] = useState<string>();
  const [labelType, setLabelType] = useState<string>('mini');
  const [fpLoading, setFpLoading] = useState(false);
  const [fpSchedule, setFpSchedule] = useState(0);
  const [fpTask, setFpTask] = useState<CreativeVO | null>(null);

  //图片数量错误
  const [imgCountErr, setImgCountErr] = useState('');

  // 在组件内部添加状态和处理函数
  const [disclaimerVisible, setDisclaimerVisible] = useState(false);

  //当前用户是否点击同意上传声明，值为'Y'|''
  const UPLOAD_DISCLAIMER_KEY = 'upload_disclaimer';
  const UPLOAD_DISCLAIMER_YES = 'Y';
  const [agreeUploadDisclaimer, setAgreeUploadDisclaimer] = useState(false);

  const batchUploaderRef = useRef<BatchUploaderRef>(null);
  const fpBatchUploaderRef = useRef<BatchUploaderRef>(null);
  // 轮询定时器Ref
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const minImageCount = [100827, 100155, 100006, 100152, 100918, 101160, 101220,101683,101684,101986].includes(getUserInfo()?.id || 0) ? 1 : (materialType === 'face' ? 3 : 10);

  const clearTimeoutRef = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };

  useEffect(() => {

    //用户信息
    let user = getUserInfo();
    if (user) {
      setUserInfo(user);

      //非渠道商：商户、运营等
      if (user && user.roleType !== 'DISTRIBUTOR') {

        //查当前登录用户积分
        queryImagePoint().then(res => {
          if (res && res.imagePoint !== null) {
            setImgPoint(res.imagePoint);
          }
        });
      }
    }

    //用户是否同意过上传声明
    queryUserProfileByKey(UPLOAD_DISCLAIMER_KEY).then(res => {
      if (res && res.profileVal === UPLOAD_DISCLAIMER_YES) {
        setAgreeUploadDisclaimer(true);
        setDisclaimerVisible(false);
      } else {
        setAgreeUploadDisclaimer(false);
      }
    });

    getClothCategoryCfg().then(res => {
      if (res) {
        setClothCategoryCfg(res);
      }
    });

    return () => {
      clearTimeoutRef();
    };

  }, []);

  useEffect(() => {

    let imgNum = uploadItems?.filter(u => u?.url)?.length;
    if (imgNum > 0 && (imgNum < minImageCount)) {
      setImgCountErr(`请至少上传${minImageCount}张图片`);
    } else {
      setImgCountErr('');
    }
  }, [uploadItems]);

  useEffect(() => {
    if (!waterMarkDescType || waterMarkDescType === 'none') {
      setWaterMarkDescPos('');
      setWaterMarkDesc('');
    } else if (waterMarkDescType && waterMarkDescPos) {
      setWaterMarkDesc(waterMarkDescType + ' in the ' + waterMarkDescPos + ' corner');
    }
  }, [waterMarkDescType, waterMarkDescPos]);

  useEffect(() => {
    if (materialType === 'scene') {
      if (userInfo && ['OPERATOR', 'ADMIN'].includes(userInfo.roleType)) {
        queryConfigByKeys(['TRAIN_LABEL_SCENE_PROMPT']).then(res => {
          if (res) {
            setCaptionPrompt(formatPrompt(res['TRAIN_LABEL_SCENE_PROMPT']));
          }
        });
        setTrainRepeatTimes(5);
      }

      if (userInfo && userInfo.roleType === 'MERCHANT') {
        //专属场景
        if (exclusive) {
          setLoraMusePoint(200);
        } else {
          setLoraMusePoint(40);
        }
      }

    } else if (materialType === 'face') {
      // 设置模特的默认值为空
      setSexType(''); // 默认为空
      setAgeRange(''); // 默认为空
      setChildSize(null); // 清空儿童尺码

      if (userInfo && ['OPERATOR', 'ADMIN'].includes(userInfo.roleType)) {
        queryConfigByKeys(['TRAIN_LABEL_FACE_MINI_PROMPT']).then(res => {
          if (res) {
            setCaptionPrompt(formatPrompt(res['TRAIN_LABEL_FACE_MINI_PROMPT']));
          }
        });
        setTrainRepeatTimes(15);
      }

      if (userInfo && userInfo.roleType === 'MERCHANT') {
        //专属场景
        if (exclusive) {
          setLoraMusePoint(1200);
        } else {
          throw new Error('场景只支持专属');
        }
      }

    }

    if (materialType === 'face') {
      let fpObj = localStorage.getItem(FP_SESSION_KEY);
      let cacheObj = fpObj ? JSON.parse(fpObj) : null;
      //48小时以内
      if (cacheObj && cacheObj.storeTime && new Date(cacheObj.storeTime).getTime() + 1000 * 60 * 60 * 48 > new Date().getTime()) {
        recoverFromFpCache(cacheObj);
      } else if (cacheObj) {
        console.log('捏脸缓存已过期，开始清除');
        localStorage.removeItem(FP_SESSION_KEY);
      }
    }
  }, [materialType, userInfo, exclusive]);

  useEffect(() => {
    //渠道商代用户上传服装，此时查询代用户积分
    if (userInfo && userInfo.roleType === 'DISTRIBUTOR' && principalId !== null && Number(principalId) > 0) {
      queryCustomerMusePoint({ customerMasterId: Number(principalId) }).then(res => {
        if (res && res.imagePoint !== null) {
          setImgPoint(res.imagePoint);
        }
      });
    }
  }, [principalId, userInfo]);

  useEffect(() => {
    if (materialType === 'face' && ageRange && ageRange !== 'adult') {
      setFpSkinType('smooth');
    }
  }, [ageRange]);

  const buildFpCache = (fpTask) => {
    return {
      fpTask,
      uploadItems: JSON.stringify(uploadItems),
      fpUploadItems: JSON.stringify(fpUploadItems),
      fpSkinType,
      loraName,
      sexType,
      ageRange,
      childSize,
      waterMarkDesc,
      waterMarkDescType,
      hasWaterMark,
      waterMarkDescPos,
      storeTime: new Date(),
    };
  };

  const recoverFromFpCache = (cache) => {
    const fpTask = cache.fpTask;
    setFpTask(fpTask);
    setLoraName(cache.loraName);
    setUploadItems(JSON.parse(cache.uploadItems));
    setFpUploadItems(JSON.parse(cache.fpUploadItems));
    setSexType(cache.sexType);
    setAgeRange(cache.ageRange);
    setChildSize(cache.childSize);
    setWaterMarkDesc(cache.waterMarkDesc);
    setWaterMarkDescType(cache.waterMarkDescType);
    setHasWaterMark(cache.hasWaterMark);
    setWaterMarkDescPos(cache.waterMarkDescPos);
    setIsFacePinching(true);
    setFpSkinType(cache.fpSkinType);

    setFpLoading(true);
    refreshSchedule(fpTask);
    startPolling(fpTask);
  };

  const handleChangeClothPrompt = (value: string, tag: string | undefined) => {
    setCaptionPrompt(value);
    setLabelType(tag || 'default');
  };

  const changeShowTopupModal = (visible: boolean) => {
    if (userInfo?.roleType === 'DISTRIBUTOR' || userInfo?.roleType === 'DEMO_ACCOUNT') {
      return;
    }
    setShowTopupModal(visible);
  };

  function onCloseTopup() {
    setShowTopupModal(false);
  }

  function onTopupSuccess() {
    setShowTopupModal(false);
    window.location.reload();
  }

  function getShowRetImgs(): string[] {
    let ret: string[] = [];
    if (isFacePinching) {
      ret = fpSelected;
    } else {
      ret.push(...uploadItems.filter(img => img && img.url).map(img => img.url as string));
    }
    return ret;
  }

  async function doSubmit() {

    //防重复点击
    if (flag) {
      return;
    }
    setFlag(true);

    //如果是演示账号，则直接mock结果
    //提交素材成功
    if (userInfo?.roleType === 'DEMO_ACCOUNT') {
      message.success('操作成功');
      setRetImgs(getShowRetImgs());
      setShowRetDialog(true);

      //成功后关闭弹窗
      setConfirmLoading(false);
      setShowLearnDoubleConfirmModal(false);
      setFlag(false);
      return;
    }

    //等待图片完成上传
    if (batchUploaderRef?.current) {
      await batchUploaderRef.current.handleImageUploads();
    }
    console.log('图片完成上传, 开始上传素材');

    const imgUrls = isFacePinching ? fpSelected : uploadItems.filter(item => item.url && item.imgUrl).map(item => item.imgUrl);

    uploadCommonMaterialInfo({
      name: loraName,
      materialType: materialType,
      materialSubType: sexType,
      ageRange: ageRange === 'adult' ? ageRange : childSize,
      materialTags: '',
      materialDetail: {
        imgUrls,
      },
      principalId,
      captionPrompt,
      waterMarkDesc,
      noshowFace: materialType === 'scene' ? noshowFace : null,
      trainRepeatTimes,
      sceneType: materialType === 'scene' ? sceneType : null,
      preprocessCensoredFace: materialType === 'scene' ? preprocessCensoredFace : null,
      exclusive,
      labelType: materialType === 'face' ? labelType : null,
      clothCategory: materialType === 'scene' && sceneType !== 'MODEL-SHOW' ? clothCategory : null,
    }).then(res => {

      //提交素材成功
      if (res) {
        message.success('操作成功');
        localStorage.removeItem(FP_SESSION_KEY);
        setRetImgs(getShowRetImgs());

        setShowRetDialog(true);
        //成功后关闭弹窗
        setConfirmLoading(false);
        setShowLearnDoubleConfirmModal(false);

        //提交素材失败
      } else {
        notification.error({ message: '提交素材异常，请重试' });
        setFlag(false);

        //失败后保留二次弹窗
        setConfirmLoading(false);
        setShowLearnDoubleConfirmModal(true);
      }
    });
  }

  const buildTopupTip = () => {
    return userInfo?.roleType === 'DISTRIBUTOR' ? `当前客户点数为${imgPoint}，不足以进行学习，请联系客户进行充值` : `当前点数为${imgPoint}，不足以进行学习，请充值`;
  };

  //渲染一组图片上传区域
  const checkNeedTopup = () => {
    return !!(imgPoint > -1 && imgPoint < loraMusePoint && userInfo && userInfo.roleType === 'MERCHANT');
  };

  function showTopupIfNeed() {
    if (checkNeedTopup()) {
      message.info(buildTopupTip(), 5);
      changeShowTopupModal(true);
    }
  }

  function onCloseRetDlg() {
    setRetImgs([]);
    setShowRetDialog(false);
    window.location.reload();
  }

  function onInputName(name: string) {
    setLoraName(name);
    if (name) {

      // 允许的字符：中文、英文大小写、数字、连字符、下划线
      let regex = /^[\u4e00-\u9fa5a-zA-Z0-9-_]+$/;
      let valid = regex.test(name);

      if (!valid) {
        setNameError('名称只允许由20个以内的中文、英文、数字、连字符、下划线字符组成');
      } else {
        checkMaterialNameExists(name).then(exists => {
          if (exists) {
            setNameError('同名的模型已经存在');
          } else {
            setNameError('');
          }
        });
      }
    }
  }


  function onClickLearnClothButton() {
    if (flag) {
      message.warning('正在处理中，请稍后');
      return;
    }

    if (checkNeedTopup()) {
      message.warning(buildTopupTip(), 5);
      changeShowTopupModal(true);
      return;
    }

    if (loraName === '') {
      message.warning('请输入名称');
      return;
    }
    console.log('ageRange', ageRange);

    if (!ageRange) {
      message.warning('请选择年龄区段');
      return;
    }

    if (sexType === '') {
      message.warning('请选择男女款式');
      return;
    }

    if (userInfo?.roleType !== 'MERCHANT' && materialType === 'scene' && sceneType !== 'MODEL-SHOW' && !clothCategory.length) {
      message.warning('请选择服装款式');
      return;
    }

    //检查图片上传情况
    let imgExist = uploadItems.filter(item => item.url).length > 0;
    if (!imgExist) {
      message.warning('请完成图片上传');
      return;
    }

    if (!waterMarkDescType) {
      message.warning('选择水印类型');
      return;
    }

    if (materialType === 'scene' && !noshowFace) {
      message.warning('选择图片中是否包含人脸');
      return;
    }

    if (clothNameError !== '') {
      message.warning('请输入有效的名称');
      return;
    }

    if (['ADMIN', 'OPERATOR'].includes(userInfo?.roleType || '') && (captionPrompt === '' || captionPrompt.length < 1)) {
      message.warning('请填写打标prompt');
      return;
    }

    if (userInfo?.roleType !== 'ADMIN' && imgCountErr !== '') {
      message.warning('请按规则完成图片上传');
      return;
    }

    //卷草管理员账号，不限制像素大小
    if (userInfo?.id !== 100155){
      for (const item of uploadItems) {
        if (item.url && item.uploadSizeError) {
          message.warning('图片像素不可低于1024 * 1024像素，请重新上传');
          return;
        }
      }
    }

    if (batchUploaderRef?.current?.checkSize()) {
      message.warning(`请至少上传10张${materialTypeTitle}图片`);
      return;
    }

    if (materialType === 'face' && isFacePinching) {
      if (!fpTask) {
        message.warning('请先点击"开始捏脸"按钮进行捏脸');
        return;
      }
      if (fpSelected.length < uploadItems.filter(e => e.url).length) {
        message.warning('请先完成捏脸结果选择，对想要的捏脸结果进行打勾');
        return;
      }
    }

    // 如果用户是商户或分销商，且未同意免责声明，则显示免责声明模态框
    if (userInfo && (userInfo?.roleType === 'MERCHANT' || userInfo?.roleType === 'DISTRIBUTOR') && !agreeUploadDisclaimer) {
      setDisclaimerVisible(true);
      return;
    }

    setShowLearnDoubleConfirmModal(true);
  }

  function getCheckLearnPagePath() {
    let path = '/models?type=' + materialType;
    if (userInfo?.roleType === 'ADMIN') {
      if (materialType === 'face') {
        path = '/face';
      } else if (materialType === 'scene') {
        path = '/scene';
      }
    }
    return path;
  }

  const refreshSchedule = (data: CreativeVO | null) => {
    if (!data) return 0;

    if (!isProcessing(data)) {
      setFpSchedule(100);
    }

    const current = data?.extInfo['schedule'];
    let { finished, subSchedule } = current || { finished: 0, subSchedule: 0 };

    const total = data.batchCnt * 100;
    let scheduleTotal = Math.round(((Number(finished) * 100 + Number(subSchedule)) * 100) / total);

    scheduleTotal = scheduleTotal > 100 ? 99 : scheduleTotal;
    setFpSchedule(prevState => {
      if (scheduleTotal > prevState) {
        return scheduleTotal;
      }
      return prevState;
    });
  };

  const startPolling = (data: CreativeVO | null) => {
    clearTimeoutRef();

    if (!data) return;
    if (!isProcessing(data)) {
      setFpLoading(false);
      setFpSchedule(100);
    }

    const pollOnce = async (data: CreativeVO | null) => {
      try {
        clearTimeoutRef();
        // 只查询处理中的任务
        const processingTaskIds: number[] = (isProcessing(data) ? [data?.id] : []) as number[];

        // 如果没有处理中的任务，则停止轮询
        if (processingTaskIds.length === 0) {
          setFpLoading(false);
          return;
        }

        // 批量查询任务状态
        const result = await batchQueryCreative(processingTaskIds);

        // 如果查询成功，更新任务列表
        let task: CreativeVO | null = null;
        if (result && result.length > 0) {
          task = result[0];
          setFpTask(task);

          localStorage.setItem(FP_SESSION_KEY, JSON.stringify(buildFpCache(task)));

          refreshSchedule(task);

          if (!isProcessing(task)) {
            setFpLoading(false);
            return;
          }
        }

        // 安排下一次轮询
        timeoutRef.current = setTimeout(() => pollOnce(task), 3000); // 3秒后再次轮询
      } catch (error) {
        console.error('轮询状态时出错：', error);

        // 即使出错，也尝试继续轮询，除非所有任务都已完成
        if (isProcessing(data)) {
          timeoutRef.current = setTimeout(() => pollOnce(data), 3000);
        }
      }
    };

    // 开始第一次轮询（稍微延迟，避免立即查询）
    if (isProcessing(data)) {
      timeoutRef.current = setTimeout(() => pollOnce(data), 3000);
    }
  };

  const startFacePinching = async () => {
    //等待图片完成上传
    if (batchUploaderRef?.current) {
      await batchUploaderRef.current.handleImageUploads();
    }

    //等待图片完成上传
    if (fpBatchUploaderRef?.current) {
      await fpBatchUploaderRef.current.handleImageUploads();
    }

    if (!ageRange) {
      message.warning('请选择年龄区段');
      return;
    }

    if (!sexType) {
      message.warning('请选择模特性别');
      return;
    }

    if (batchUploaderRef?.current?.checkSize()) {
      message.warning('请至少上传10张原始模特图片');
      return;
    }

    if (fpBatchUploaderRef?.current?.checkSize()) {
      message.warning('请至少上传3张捏脸图片');
      return;
    }

    const originImages = fetchUploadUrl(uploadItems);
    const targetImages = fetchUploadUrl(fpUploadItems);

    facePinching({
      originImages,
      targetImages,
      skinType: fpSkinType,
      genderType: sexType,
      ageRange: ageRange === 'adult' ? ageRange : childSize,
    }).then(res => {
      if (!res) return;
      setFpLoading(true);
      setFpSchedule(0);
      setFpTask(res);
      localStorage.setItem(FP_SESSION_KEY, JSON.stringify(buildFpCache(res)));
      startPolling(res);
    });
  };

  const handleReFacePinching = async () => {
    setFpTask(null);
    localStorage.removeItem(FP_SESSION_KEY);
    await startFacePinching();
  };

  const clearFacePinching = () => {
    setFpTask(null);
    setFpUploadItems([]);
    localStorage.removeItem(FP_SESSION_KEY);
  };

  const changeFacePinching = (checked: boolean) => {
    setIsFacePinching(checked);
    if (!checked) {
      clearFacePinching();
    }
  };

  const clearAllUpload = () => {
    setUploadItems([]);
    changeFacePinching(false);
    if (batchUploaderRef.current) {
      batchUploaderRef.current.clear();
    }
    if (fpBatchUploaderRef.current) {
      fpBatchUploaderRef.current.clear();
    }
  };

  const FpDemoBlock = ({ url, title }) =>
    <Flex vertical gap={4} style={{ borderRadius: 8, padding: 8, background: '#F5F6F9' }}>
      <div style={{ position: 'relative' }}>
        <img alt={''} src={url} style={{ width: 150 }} />
        <div style={{ position: 'absolute', top: 9, right: 9 }}>
          <IconFont type={'icon-gou'} style={{ fontSize: 32, color: '#1ECD04' }} />
        </div>
      </div>
      <Flex justify={'center'} align={'center'} className={'color-72'}>
        {title}
      </Flex>
    </Flex>;

  return (
    <PageContainer>

      {/* 主体 */}
      <div className={'upload-form-container'}>
        <div className={'text20 weight color-1a'} style={{ marginBottom: 8 }}>
          {`填写${materialTypeTitle}信息`}
          <span style={{ marginLeft: 8, fontWeight: 400, fontSize: 14, color: '#969799' }}>带
						<span style={{ color: '#FF7979' }}>*</span>为必填项</span>
        </div>

        <Space direction={'vertical'} style={{ gap: 16 }}>

          <div className="form-row">
            <div className="form-label">
              <span style={{ color: '#FF7979', fontSize: 14, marginRight: 1 }}>*</span>{materialTypeTitle}名称:
            </div>
            <Input placeholder={`请输入${materialTypeTitle}名称`}
                   style={{ width: '434px', borderColor: clothNameError ? 'red' : '' }} value={loraName}
                   maxLength={20} onChange={e => onInputName(e.target.value)} />
            {clothNameError && <div className="cloth-error-message">{clothNameError}</div>}
          </div>

          {(materialType === 'scene' || userInfo?.roleType === 'ADMIN' || userInfo?.roleType === 'OPERATOR') &&
            <div className="form-row">
              <div className="form-label">
                <span style={{ color: '#FF7979', fontSize: 14, marginRight: 1 }}>*</span>是否专属:
              </div>

              <Radio.Group
                value={exclusive}
                buttonStyle="solid"
                onChange={e => {
                  setExclusive(e.target.value);
                  showTopupIfNeed();
                }}>
                <Tooltip
                  title={`专属${materialTypeTitle}学习后，仅您所属账号及子账号可以使用，其他客户不可见`}>
                  <Radio.Button style={{ width: 96, borderRadius: 32, textAlign: 'center' }}
                                value={true}>专属</Radio.Button>
                </Tooltip>
                <Tooltip title={`非专属${materialTypeTitle}学习后，所有客户可以使用`}>
                  <Radio.Button style={{ width: 96, borderRadius: 32, textAlign: 'center' }}
                                value={false} disabled={userInfo?.roleType === 'DISTRIBUTOR' || (userInfo?.roleType === 'MERCHANT' && userInfo?.memo?.includes('虚拟商家'))}>非专属</Radio.Button>
                </Tooltip>

              </Radio.Group>
            </div>
          }
          <div className="form-row" style={{ height: 22 }}>
            <div className="form-label">
              <span style={{ color: '#FF7979', fontSize: 14, marginRight: 1 }}>*</span>年龄区段:
            </div>
            <AgeRangeSelector
              value={ageRange}
              childSize={childSize}
              onChange={(value) => {
                setAgeRange(value);
                showTopupIfNeed();
              }}
              onChildSizeChange={(value) => {
                setChildSize(value);
                showTopupIfNeed();
              }}
            />
          </div>


          <div className="form-row" style={{ height: 22 }}>
            <div className="form-label">
              <span style={{
                color: '#FF7979',
                fontSize: 14,
                marginRight: 1,
              }}>*</span>{materialType === 'face' ? '模特性别' : '适用性别'}:
            </div>
            <Radio.Group
              buttonStyle="solid"
              onChange={(e) => {
                setSexType(e.target.value);
                showTopupIfNeed();
              }}
              value={sexType}>
              <Radio.Button style={{ width: 96, borderRadius: 32, textAlign: 'center' }}
                            value="female">{materialType === 'face' ? '女模' : '女'}</Radio.Button>
              <Radio.Button style={{ width: 96, borderRadius: 32, textAlign: 'center' }}
                            value="male">{materialType === 'face' ? '男模' : '男'}</Radio.Button>
              {materialType === 'scene' &&
                <Radio.Button value="unisex">男女通用</Radio.Button>
              }
            </Radio.Group>
          </div>

          {userInfo?.roleType === 'ADMIN' &&
            <div className="form-row">
              <div className="form-label">
                <span style={{ color: '#FF7979', fontSize: 14, marginRight: 1 }}>*</span>训练重复次数:
              </div>
              <InputNumber placeholder="请输入训练重复次数"
                           style={{ width: '80px', borderColor: clothNameError ? 'red' : '' }} value={trainRepeatTimes}
                           maxLength={3} onChange={e => setTrainRepeatTimes(e)} />
            </div>
          }

          {materialType === 'scene' &&
            <div className="form-row" style={{ height: 22 }}>
              <div className="form-label">
                <span style={{ color: '#FF7979', fontSize: 14, marginRight: 1}}>*</span>场景类型:
              </div>


                <Radio.Group
                  buttonStyle="solid"
                  onChange={(e) => {
                    setSceneType(e.target.value);
                    showTopupIfNeed();
                  }}
                  value={sceneType}>
                  <Radio.Button style={{ width: 96, borderRadius: 32, textAlign: 'center' }}
                                value="REAL-SCENE-SHOOTING">实景拍摄</Radio.Button>
                  <Tooltip title={'勾选后此场景将归类到Look图场景，并可以在该功能场景中使用'}>
                    <Radio.Button style={{ width: 96, borderRadius: 32, textAlign: 'center' }}
                                  value="LOOK">Look图</Radio.Button>
                  </Tooltip>
                  <Tooltip title={'勾选后此场景将归类到T台秀场场景，并可以在该功能场景中使用'}>
                    <Radio.Button style={{ width: 96, borderRadius: 32, textAlign: 'center' }}
                                  value="MODEL-SHOW">T台秀场</Radio.Button>
                  </Tooltip>
                </Radio.Group>
            </div>
          }
          {userInfo?.roleType !== 'MERCHANT' && materialType === 'scene' && sceneType !== 'MODEL-SHOW' &&
            <div className="form-row" style={{ height: 22 }}>
              <div className="form-label">
                <span style={{ color: '#FF7979', fontSize: 14, marginRight: 1}}>*</span>服装款式:
              </div>

              <ClothCategorySelect
                width={434}
                clothCategoryCfg={clothCategoryCfg}
                onChange={(value) => {
                  setClothCategory(value);
                }}
              />
            </div>
          }

        </Space>

        <Flex gap={8} align={'center'}>
          <Flex className={'upload-tutorial-title'}>
            <div style={{ fontSize: 20, fontWeight: 500, color: '#1A1B1D' }}>
              上传{materialTypeTitle}图片
            </div>
            <div className={'upload-tutorial-container'}
                 onClick={() => navigate('/tutorial?idx=' + getTutorialIdx(materialType))}>
              <IconFont type={'icon-lujing4'} style={{ fontSize: 12 }} />
              <span className={'upload-tutorial-notice-text'}>上传须知</span>
              <IconFont type={'icon-youjiantou_16px'} style={{ fontSize: 12, color: '#366EF4' }} />
            </div>
          </Flex>

          <div style={{ marginTop: 24 }}>
            {(lengthOfUpload(uploadItems) || lengthOfUpload(fpUploadItems) || fpTask) &&
              <Popconfirm title={'确认要清空所有已上传图片吗？'} onConfirm={clearAllUpload}>
                <Button>清空已上传图片</Button>
              </Popconfirm>
            }
          </div>
        </Flex>

        <div className={'color-group-block'}>
          {materialType === 'face' &&
            <div className="form-row">
              <span className="form-label color-a0">至少上传三张正面图片（包含左30°-45°半侧面、纯正面、右30°-45°半侧面），如需生成背面需上传1张背面</span>
            </div>
          }
          <div style={{ position: 'relative' }}>
            <div style={{ position: 'relative' }}>
              <BatchUploader
                minCount={minImageCount}
                uploadBGList={[materialType === 'face' ? uploadFaceBgPng : uploadSceneBgPng]}
                minSize={(materialType === 'scene' || [100918,101986].includes(getUserInfo()?.id || 0)) ? 750 : 1024}
                checkNeedTopup={checkNeedTopup}
                onUploadChange={setUploadItems}
                ref={batchUploaderRef}
                value={uploadItems}
              />
              {materialType === 'face' && (!ageRange || !sexType) && (
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(8px)',
                  WebkitBackdropFilter: 'blur(8px)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 1,
                }}>
                  <div style={{
                    textAlign: 'center',
                    color: '#666',
                    fontSize: 16,
                    background: 'rgba(255, 255, 255, 0.7)',
                    padding: '16px 24px',
                    borderRadius: '8px',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  }}>
                    <div style={{ color: '#666' }}>
                      选择“年龄区段”和“模特性别”之后即可进行上传
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          <Flex vertical={true} gap={8}>
            <div className="form-row">
              <span className="form-label">是否有水印：</span>
              <Radio.Group onChange={e => {
                setHasWaterMark(e.target.value);
                if (!e.target.value) {
                  setWaterMarkDescType('none');
                }
              }}
                           value={hasWaterMark}
                           optionType={'button'}
                           buttonStyle="solid">
                <Radio.Button style={{ width: 96, borderRadius: 32, textAlign: 'center' }}
                              value={false}>无水印</Radio.Button>
                <Radio.Button style={{ width: 96, borderRadius: 32, textAlign: 'center' }}
                              value={true}>有水印</Radio.Button>
              </Radio.Group>
            </div>

            {hasWaterMark &&
              <Flex vertical={true} gap={8}>
                <div className="form-row">
                  <span className="form-label">请选择水印类型:</span>
                  <Radio.Group onChange={e => setWaterMarkDescType(e.target.value)}
                               value={waterMarkDescType}
                               optionType={'button'}>
                    <Radio.Button style={{ width: 'auto', height: 30, borderRadius: 32, textAlign: 'center' }}
                                  value="logo and text">logo水印和文字水印</Radio.Button>
                    <Radio.Button style={{ width: 'auto', height: 30, borderRadius: 32, textAlign: 'center' }}
                                  value="logo">仅logo水印</Radio.Button>
                    <Radio.Button style={{ width: 'auto', height: 30, borderRadius: 32, textAlign: 'center' }}
                                  value="text">仅文字水印</Radio.Button>
                  </Radio.Group>
                </div>

                <div className="form-row">
                  <span className="form-label">请选择水印位置:</span>
                  <Radio.Group onChange={e => setWaterMarkDescPos(e.target.value)}
                               value={waterMarkDescPos}
                               optionType={'button'}>
                    <Radio.Button style={{ width: 96, height: 30, borderRadius: 32, textAlign: 'center' }}
                                  value="top left">左上角</Radio.Button>
                    <Radio.Button style={{ width: 96, height: 30, borderRadius: 32, textAlign: 'center' }}
                                  value="bottom left">左下角</Radio.Button>
                    <Radio.Button style={{ width: 96, height: 30, borderRadius: 32, textAlign: 'center' }}
                                  value="top right">右上角</Radio.Button>
                    <Radio.Button style={{ width: 96, height: 30, borderRadius: 32, textAlign: 'center' }}
                                  value="bottom right">右下角</Radio.Button>
                  </Radio.Group>
                </div>

                {userInfo && (userInfo?.roleType === 'OPERATOR' || userInfo?.roleType === 'ADMIN') &&
                  <div className="form-row">
                    <span className="form-label">水印prompt:</span>
                    <Input
                      allowClear
                      style={{ width: '100%' }}
                      value={waterMarkDesc}
                      onChange={(e) => {
                        setWaterMarkDesc(e.target.value);
                      }} />
                  </div>
                }

              </Flex>
            }
          </Flex>

        </div>

        {materialType === 'face' &&
          <Flex vertical gap={8} style={{ marginTop: 8 }} className={'color-group-block'}>
            <Flex align={'center'}>
              <Checkbox className={'text16 '} onChange={e => changeFacePinching(e.target.checked)}
                        disabled={uploadItems.length === 0} checked={isFacePinching || false}>
                <div className={'text20 weight color-1a'}>是否需要捏脸</div>
              </Checkbox>
              <span className={'text16'}>（通过上传其它照片来合成新的脸）</span>

              {(lengthOfUpload(fpUploadItems) > 0 || fpTask) &&
                <Popconfirm title={'确认要清空捏脸图片吗？'} onConfirm={clearFacePinching} style={{ marginLeft: 8 }}
                            disabled={fpLoading}>
                  <Button>清空捏脸图片</Button>
                </Popconfirm>
              }

            </Flex>

            {isFacePinching &&

              <Flex gap={8} vertical>
                <Flex vertical gap={8}>
                  <div>图片要求：</div>
                  <div>1. 需上传同一模特，正面、左半侧、右半侧，共三张照片。</div>
                  <div>2. 侧面图需为半侧面，勿上传纯侧面，纯侧面效换脸效果不佳。</div>
                  <div>3. 模特面部需干净。无头发、配饰等遮挡，发际线高度与底图尽量保持一致，脸部轮廓差异不宜过大。</div>
                  <div>4. 图片像素大于1024。请勿上传全身图，请上传胸像照片。</div>
                  <Flex gap={8} vertical>
                    <div className={'text16 color-1a weight'}>上传示例图</div>
                    <Flex gap={8} align={'center'}>
                      <FpDemoBlock url={uploadDemoLeft} title={'左边侧'} />
                      <FpDemoBlock url={uploadDemoCenter} title={'正面照'} />
                      <FpDemoBlock url={uploadDemoRight} title={'右边侧'} />
                    </Flex>
                  </Flex>

                  <Spin spinning={fpLoading} delay={500}
                        tip={`生成中，预计需要几分钟，切换至其他页面，任务仍将保留，当前进度${fpSchedule}%`}>
                    <Flex gap={8} vertical>
                      <BatchUploader maxCount={3} minCount={3} value={fpUploadItems} ref={fpBatchUploaderRef}
                                     uploadBGList={materialType === 'face' ? [uploadFaceBgLeftPng, uploadFaceBgPng, uploadFaceBgRightPng] : [uploadSceneBgPng]}
                                     uploadBGMaskList={[true, false, true]} onUploadChange={setFpUploadItems} />

                      <Flex align={'center'} gap={8}>
                        <Radio.Group defaultValue={'realistic'} onChange={e => setFpSkinType(e.target.value)}
                                     value={fpSkinType}
                                     options={[
                                       { label: '真实肌理感皮肤（含有皮肤斑点）', value: 'realistic' },
                                       { label: '光滑质感皮肤', value: 'smooth' },
                                     ]} />
                      </Flex>

                      <Flex align={'center'} gap={8}>
                        <Button className="submit-button" onClick={startFacePinching} type={'primary'}
                                disabled={fpUploadItems.filter(e => e.url).length < 3 || fpLoading || isProcessing(fpTask) || !!fpTask || false}>
                          <div className={'submit-inner'}>开始捏脸</div>
                        </Button>
                        <Button
                          disabled={fpUploadItems.filter(e => e.url).length < 3 || fpLoading || isProcessing(fpTask) || !fpTask || false}
                          onClick={handleReFacePinching}>
                          重新捏脸
                        </Button>
                      </Flex>
                    </Flex>
                  </Spin>
                </Flex>

                {fpTask && !isProcessing(fpTask) &&
                  <Flex vertical gap={8}>
                    <div>
                      已为您生成3 * {uploadItems.filter(e => e.url).length}组张图片，请在下列图片分组中选择您想要的图片（每3个为一组，每组选一张，点击右上角的勾，或点击图片放大后再次点击图片进行选择）
                    </div>
                    <FacePinchingImageBlock task={fpTask} onChange={setFpSelected} />
                  </Flex>
                }
              </Flex>


            }
          </Flex>
        }


        {userInfo && (userInfo?.roleType === 'OPERATOR' || userInfo?.roleType === 'ADMIN') &&
          <div>
            <div style={{ fontSize: 20, fontWeight: 500, color: '#1A1B1D', marginTop: 24, marginBottom: 8 }}>
              <span style={{ color: '#FF7979', fontSize: 14, marginRight: 1 }}>*&nbsp;</span>
              打标Prompt（多数情况下用默认即可）
              <LoraPromptsSwitch value={captionPrompt} materialType={materialType as MaterialType}
                                 onChange={handleChangeClothPrompt} tag={labelType} />
            </div>
            <TextArea rows={10}
                      value={captionPrompt}
                      onChange={e => setCaptionPrompt(e.target.value)}
            />

            <Flex gap={8} vertical={true} style={{ marginBottom: 24, minHeight: 200 }}>

              {materialType === 'scene' &&
                <div>
                  <div style={{ fontSize: 20, fontWeight: 500, color: '#1A1B1D', marginTop: 24, marginBottom: 8 }}>
                    <span style={{ color: '#FF7979', fontSize: 14, marginRight: 1 }}>*&nbsp;</span>
                    其他参数
                  </div>
                  <Flex gap={12}>
                    <Radio.Group
                      value={noshowFace}
                      options={[{ label: '生成图片展示脸部', value: 'N' }, { label: '生成图片不展示脸部', value: 'Y' }]}
                      optionType="button" buttonStyle="solid" onChange={e => setNoshowFace(e.target.value)} />
                    <Radio.Group
                      value={preprocessCensoredFace}
                      options={[{ label: '不进行白头预处理', value: 'N' }, { label: '进行白头预处理', value: 'Y' }]}
                      optionType="button" buttonStyle="solid"
                      onChange={e => setPreprocessCensoredFace(e.target.value)} />
                  </Flex>
                </div>
              }

            </Flex>
          </div>
        }


      </div>

      <div className="form-footer">
        <Flex className={'text16 font-pf color-1a'} align={'flex-end'} justify={'center'} vertical>
          <div>本次学习需消耗<IconFont type={'icon-icon_mousidian'} />{loraMusePoint}缪斯点</div>
          <a target={'_blank'} rel="noreferrer" href={'disclaimer.html'}
             className={'color-96'}>免责声明 <ExclamationCircleOutlined
            style={{ fontSize: 14 }} className={'color-96'} /></a>
        </Flex>

        {/* 确认上传 */}
        <Button className="submit-button" type="primary"
                onClick={onClickLearnClothButton}>
          <div className={'submit-inner'}>
            {`创建${materialTypeTitle}`}
          </div>
        </Button>
      </div>

      {showLearnDoubleConfirmModal && (
        <Modal
          title="操作确认"
          centered
          open={showLearnDoubleConfirmModal}
          okText={confirmLoading ? '上传中' : '确定'}
          cancelButtonProps={{ disabled: confirmLoading }} // 禁用"取消"按钮
          closable={!confirmLoading} // 禁用右上角的关闭按钮
          maskClosable={false} // 禁用点击蒙层关闭
          keyboard={false}
          onOk={() => {
            setConfirmLoading(true);
            // 让 React 先更新 UI，然后再执行耗时操作
            setTimeout(() => {
              doSubmit();
            }, 0);
          }}
          confirmLoading={confirmLoading}
          onCancel={() => {
            setShowLearnDoubleConfirmModal(false);
            setConfirmLoading(false);
          }}
        >
          <p>{`将扣除 ${loraMusePoint} 缪斯点，并创建${materialTypeTitle}"${loraName}"`}</p>
        </Modal>
      )
      }

      {/* 提交成功页面 */}
      {showRetDialog && (
        <Modal
          open={showRetDialog}
          footer={null}
          keyboard={false}
          centered={true}
          width={'auto'}
          mask={true}
          closable={false}
          className="ret-modal"
          styles={{
            mask: {
              backgroundColor: 'rgba(0,0,0,0.8)',
            },
          }}
        >
          <div>
            <div className="modal-content">
              <ImageLottieCarousel images={retImgs} scanAnimation={animationData} />
              <div className="modal-time">预计学习时间约24小时</div>
            </div>
            <Button type="primary" className="modal-button" onClick={() => {
              navigate(getCheckLearnPagePath());
              window.location.reload();

            }}>查看学习进度</Button>
            <div className="modal-close" onClick={onCloseRetDlg}>
              <IconFont type={'icon-lujing'} style={{ fontSize: 24, color: 'white' }} />
            </div>
          </div>
        </Modal>
      )}

      {showTopupModal && <TopupModal visible={showTopupModal} onClose={onCloseTopup} onPaySuccess={onTopupSuccess} />}

      {disclaimerVisible &&
        <DisclaimerModal
          visible={disclaimerVisible}
          onClose={() => setDisclaimerVisible(false)}
          onAgree={() => {
            setUserProfileByKey({ key: UPLOAD_DISCLAIMER_KEY, value: UPLOAD_DISCLAIMER_YES }).then(res => {
              if (res) {
                // 同意免责声明，继续
                setAgreeUploadDisclaimer(true);

                //关闭当前框
                setDisclaimerVisible(false);

                // 显示二次确认弹窗
                setShowLearnDoubleConfirmModal(true);

              } else {
                message.error('操作失败');
              }
            });
          }}
        />
      }

    </PageContainer>
  );
};

export default UploadCommonMaterialPage;