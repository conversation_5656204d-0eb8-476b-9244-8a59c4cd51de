import { Al<PERSON>, Button, Flex, Modal } from 'antd';
import React, { useEffect, useState } from 'react';
import './CaptureNotice.less';
import captureYes from '@/assets/images/cloth/请竖屏拍摄.png';
import captureNo from '@/assets/images/cloth/请勿横屏拍摄.png';
import photoSetting from '@/assets/images/cloth/photo-setting.png';
import bgClean from '@/assets/images/cloth/背景干净颜色有差异.png';
import bgMess from '@/assets/images/cloth/背景杂乱.png';
import normalShotWay from '@/assets/images/cloth/肚脐位置水平拍摄.png';
import mobileTooHigh from '@/assets/images/cloth/手机高度过高.png';
import dontCut from '@/assets/images/cloth/在全身图上裁剪.png';
import IPHONE_TUTORIAL from '@/assets/images/cloth/iphone-tutorial.png';
import ANDROID_TUTORIAL from '@/assets/images/cloth/android-tutorial.png';
import IconFont from '@/components/IconFont';
import shotLight from '@/assets/images/cloth/打灯拍摄.png';
import envDark from '@/assets/images/cloth/环境昏暗.png';
import shotAgain from '@/assets/images/cloth/请重新拍摄.png';
import { queryUserProfileByKey, setUserProfileByKey } from '@/services/UserController';
import { useNavigate } from 'react-router-dom';
import captureBigPng from '@/assets/images/cloth/拍摄示例大图.png'

interface CaptureNoticeProps {
}

type Type = 'ANDROID' | 'IPHONE';

const CaptureNotice: React.FC<CaptureNoticeProps> = () => {
  const [collapse, setCollapse] = useState(false);
  const [showTutorial, setShowTutorial] = React.useState(false);
  const [type, setType] = React.useState<Type>();
  const bodyRef = React.useRef<HTMLDivElement>(null);

  const navigate = useNavigate();

  const handleShowTutorial = (type: Type) => {
    setType(type);
    setShowTutorial(true);
  };

  useEffect(()=>{
    queryUserProfileByKey('showCaptureNotice').then(res => {
      if (res && res.profileVal === 'N'){
        setCollapse(true);
      } else {
        setCollapse(false);
      }
    })
  }, []);

  return (<>

    <div className={'cap-nav-bar'}>
      <Button size={'small'} style={{borderRadius: 8, width: 68, height: 28, fontSize: 14}} onClick={()=>{history.back()}}>
        <IconFont type={'icon-fanhuitubiao'} style={{ fontSize: 14 }} />
        <span className={'font-pf'}>返回</span>
      </Button>
    </div>

    <Flex vertical className={'capture-notice-container'} ref={bodyRef}>
      <Flex>
        <div className={'capture-notice-title font-pf'}>图片拍摄注意事项</div>
        <div style={{ display: 'none' }} className={'capture-collapse-container'} onClick={() => {
          setCollapse(!collapse);
          setUserProfileByKey({
            key: 'showCaptureNotice',
            value: collapse ? 'Y' : 'N',
          });
        }}>
          <IconFont type={collapse ? 'icon-a-jiantou1x' : 'icon-shouqi'} style={{ fontSize: 16 }} />
          <span className={'capture-collapse-text'}>{collapse ? '展开' : '折叠'}</span>
        </div>
      </Flex>

      <div className={'capture-notice-details'}>

        {/*s1、s2*/}
        <div className={'notice-1-2'}>

          {/*s1*/}
          <div className={'capture-notice-item'} style={{ width: '100%', height: 310 }}>
            <div className={'capture-title-number-row'}>
              <div className={'capture-item-number'}>1</div>
              <div className={'capture-item-title'}>根据参考图上传</div>
            </div>
            <div className={'capture-item-content'}>
              请根据参考图上传图片，图片质量对生成效果有直接影响。
            </div>
          </div>

          {/*s2*/}
          <div className={'capture-notice-item'} style={{ width: '100%', height: 310, marginTop: 16 }}>
            <div className={'capture-title-number-row'}>
              <div className={'capture-item-number'}>2</div>
              <div className={'capture-item-title'}>模特要求</div>
            </div>
            <div className={'capture-item-content'}>
              请使用人台模特拍摄、或真人拍摄。<br />
              请勿使用平铺图、挂拍图。<br />
              如果服装有袖子，需使用<span style={{ color: '#FC343F' }}>带胳膊</span>的人台模特或真人模特。<br />
            </div>
          </div>
        </div>

        {/*s3*/}
        <div className={'capture-notice-item'} style={{ width: '26%', height: '100%' }}>
          <div className={'capture-title-number-row'}>
            <div className={'capture-item-number'}>3</div>
            <div className={'capture-item-title'}>拍摄比例</div>
          </div>
          <div className={'capture-item-content'}>
            手机拍摄即可。所有图片请<span style={{ color: '#FC343F' }}>竖屏</span>拍摄，勿横屏拍摄。
            画面比例请设置为<span
            style={{ color: '#FC343F' }}>4:3或3:4</span>（不同品牌手机表示方式不同），像素不低于<span
            style={{ color: '#FC343F' }}>2000*1500px</span>。
            图片分辨率越高创作图质量越好。
          </div>
          <Flex style={{ gap: 12, marginTop: 8 }}>
            <Flex vertical gap={8}>
              <img src={captureYes} alt={''} style={{ width: 150, height: 200 }} />
              <div className={'font-pf text14 text-center'} style={{ color: '#727375' }}>请竖屏拍摄</div>
            </Flex>
            <Flex vertical gap={8}>
              <img src={captureNo} alt={''} style={{ width: 150, height: 200 }} />
              <div className={'font-pf text14 text-center'} style={{ color: '#727375' }}>请勿横屏拍摄</div>
            </Flex>
          </Flex>

          <Flex vertical gap={8} style={{ marginTop: 16 }}>
            <Flex gap={8}>
              <div className={'font-pf text14 color-1a text-center'}>画面比例的相机设置选择4:3</div>
            </Flex>
            <img src={photoSetting} alt={''} style={{ width: 324, height: 160 }} />
            <Flex gap={16}>
              <Flex className={'font-pf text14 color-brand'} align={'center'}
                    onClick={() => handleShowTutorial('IPHONE')}>
                iPhone手机设置教程<IconFont type={'icon-icon_youjiantou-copy'} style={{ fontSize: 16 }}
                                            className={'color-brand'} />
              </Flex>
              <Flex className={'font-pf text14 color-brand'} align={'center'}
                    onClick={() => handleShowTutorial('ANDROID')}>
                安卓手机设置教程<IconFont type={'icon-icon_youjiantou-copy'} style={{ fontSize: 16 }}
                                          className={'color-brand'} />
              </Flex>
            </Flex>
          </Flex>
        </div>

        {/*s4*/}
        <div className={'capture-notice-item'} style={{ width: '26%', height: '100%' }}>
          <div className={'capture-title-number-row'}>
            <div className={'capture-item-number'}>4</div>
            <div className={'capture-item-title'}>拍摄场地</div>
          </div>
          <div className={'capture-item-content'}>
            请使用纯色背景，且要与服装颜色有较大差异。如有条件建议使用幕布。半透明材质服装请使用白色背景。
          </div>
          <Flex style={{ gap: 12, marginTop: 8 }}>
            <Flex vertical gap={8}>
              <img src={bgClean} alt={''} style={{ width: 150, height: 200 }} />
              <div className={'font-pf text14 text-center'} style={{ color: '#727375' }}>背景干净、颜色有差异</div>
            </Flex>
            <Flex vertical gap={8}>
              <img src={bgMess} alt={''} style={{ width: 150, height: 200 }} />
              <div className={'font-pf text14 text-center'} style={{ color: '#727375' }}>背景杂乱</div>
            </Flex>
          </Flex>

          <div style={{ marginTop: 16 }}>建议打光拍摄，效果更佳。</div>
          <Flex style={{ gap: 12, marginTop: 8 }}>
            <Flex vertical gap={8}>
              <img src={shotLight} alt={''} style={{ width: 150, height: 200 }} />
              <div className={'font-pf text14 text-center'} style={{ color: '#727375' }}>打光拍摄</div>
            </Flex>
            <Flex vertical gap={8}>
              <img src={envDark} alt={''} style={{ width: 150, height: 200 }} />
              <div className={'font-pf text14 text-center'} style={{ color: '#727375' }}>环境昏暗</div>
            </Flex>
          </Flex>
        </div>

        {/*s5*/}
        <div className={'capture-notice-item'} style={{ width: '26%', height: '100%' }}>
          <div className={'capture-title-number-row'}>
            <div className={'capture-item-number'}>5</div>
            <div className={'capture-item-title'}>拍摄方式</div>
          </div>
          <div className={'capture-item-content'}>
            全身图和半身图分别参考以下拍摄：
          </div>
          <div style={{ marginTop: 8 }}>全身图：肚脐位置水平拍摄</div>
          <Flex style={{ gap: 12, marginTop: 8 }}>
            <Flex vertical gap={8}>
              <img src={normalShotWay} alt={''} style={{ width: 150, height: 200 }} />
              <div className={'font-pf text14 text-center'} style={{ color: '#727375' }}>肚脐位置水平拍摄</div>
            </Flex>
            <Flex vertical gap={8}>
              <img src={mobileTooHigh} alt={''} style={{ width: 150, height: 200 }} />
              <div className={'font-pf text14 text-center'} style={{ color: '#727375' }}>手机高度过高</div>
            </Flex>
          </Flex>
          <div style={{ marginTop: 16 }}>半身图：请勿在全身图基础上剪裁，请重新拍摄。</div>
          <Flex style={{ gap: 12, marginTop: 8 }}>
            <Flex vertical gap={8}>
              <img src={shotAgain} alt={''} style={{ width: 150, height: 200 }} />
              <div className={'font-pf text14 text-center'} style={{ color: '#727375' }}>重新拍摄半身图</div>
            </Flex>
            <Flex vertical gap={8}>
              <img src={dontCut} alt={''} style={{ width: 150, height: 200 }} />
              <div className={'font-pf text14 text-center'} style={{ color: '#727375' }}>在全身图基础上剪裁</div>
            </Flex>
          </Flex>
        </div>
      </div>

      <img src={captureBigPng} alt={''} style={{ width: '100%', height: '100%' }} />

    </Flex>

    <Modal
      open={showTutorial}
      centered
      footer={null}
      title={null}
      closable={false}
      onCancel={() => setShowTutorial(false)}
      width={1160}
      height={'auto'}
      styles={{
        mask: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          backdropFilter: 'blur(10px)',
        },
      }}>
      <Flex vertical gap={16}>
        <Alert type="info" showIcon className={'color-brand text14 font-pf'}
               message="注：因手机品牌不同，有些品牌选择 4:3后，像素就已经大于2000*1500；有些品牌则需要在画面比例设置中继续选择分辨率。" />
        <div className={'text24 font-pf weight color-1a'} style={{ marginTop: 14 }}>
          {type === 'IPHONE' ? 'iPhone手机设置教程' : '安卓手机设置教程（以华为手机为例）'}
        </div>
        {type === 'IPHONE' &&
          <img src={IPHONE_TUTORIAL} width={1066} height={456} alt={''} />
        }
        {type === 'ANDROID' &&
          <img src={ANDROID_TUTORIAL} width={1000} height={540} alt={''} />
        }
      </Flex>
      <IconFont type={'icon-lujing'} className={'capture-notice-close'} onClick={() => setShowTutorial(false)} />
    </Modal>
  </>);
};

export default CaptureNotice;