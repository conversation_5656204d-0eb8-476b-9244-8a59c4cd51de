.upload-main {
  position: relative;
  background-color: white;
  border-radius: 24px 24px 0 0;
  padding: 0 24px 8px 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  height: calc(100vh - 200px);
  margin-top: -24px;
  overflow: auto;

  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;

  .ant-tabs-tab {
    font-family: PingFang SC;
    font-size: 16px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0px;

    font-variation-settings: "opsz" auto;
    /* 中性色/N7-主文字2 */
    color: #727375;

    &:hover {
      color: #0052D9;
    }
  }

  // 激活状态的标签样式
  .ant-tabs-tab-active {
    .ant-tabs-tab-btn {
      font-family: PingFang SC;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      letter-spacing: 0px;

      font-variation-settings: "opsz" auto;
      /* Brand 品牌/Brand7-Normal */
      color: #0052D9;
    }
  }
}

.tabs-sticky-container {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
  padding: 0;
}

.upload-tutorial-title {
  margin-top: 24px;
  width: 240px;
  height: 28px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: row;
  padding: 0px;
  gap: 12px;

  z-index: 0;
}

.upload-tutorial-container{
  height: 28px;
  border-radius: 8px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  gap: 2px;

  /* 中性色/N2-背景色 */
  background: #F0F1F4;

  z-index: 1;

  cursor: pointer;
}

.upload-tutorial-notice-text{
  height: 20px;
  opacity: 1;

  /* body/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 品牌色/N6-hover */
  color: #366EF4;

  z-index: 1;
}