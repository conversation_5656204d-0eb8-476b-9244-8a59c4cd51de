@import "@/app";

@footer-height: 62px;
@main-gap: 16px;

.capture-notice-img-container {
  position: relative;
  width: 100%;
  height: 102px;
  background-image: url('@/assets/images/cloth/图片拍摄注意事项.png');
  background-size: 100% 100%;
  background-position: center center;
  background-repeat: no-repeat;

  &:hover {
    .go-check-btn {
      background: #FFFFFF;
    }
  }
}

.go-check-btn {
  width: 140px;
  height: 36px;

  border-radius: 164px;
  background-image: url("@/assets/images/cloth/查看border.png");
  background-repeat: no-repeat;

  opacity: 1;

  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px 32px;
  gap: 8px;

  z-index: 1;
  cursor: pointer;

  .go-check-text {
    background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0px;
  }
}

/* 表单标签样式 */
.form-label {
  margin-right: 10px;
  white-space: nowrap;

  font-family: "PingFang SC";
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0;

  font-variation-settings: "opsz" auto;
  color: #1A1B1D;
}

/* 页面容器样式 */
.upload-form-container {
  display: flex;
  flex-direction: column;
  align-content: flex-start;
  padding-bottom: 84px;
  gap: 8px;
  background: #FFFFFF;
  width: 100%;
  height: auto;
  position: absolute;

  /* 表单行样式 */
  .form-row {
    height: 32px;
    opacity: 1;

    /* 自动布局 */
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0px;
    gap: 8px;

    z-index: 0;

    /* 自定义 Radio.Button 的样式 */
    .ant-radio-button-wrapper {
      border: 1px solid #d9d9d9;
      border-radius: 32px;
      padding: 0 15px;
      margin-right: 8px;
      height: 32px;
      line-height: 30px;
      font-size: 14px;
      font-weight: normal;
    }

    .ant-radio-button-wrapper-checked {
      background: #E1E6FF;
      color: #366EF4;
    }

    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {}

    .ant-radio-button-wrapper:hover {}

    /* 去除相邻按钮之间的分隔线 */
    .ant-radio-button-wrapper:not(:first-child)::before {
      display: none;
    }
  }

  /* 控件样式，如 Input 和 Select */
  .form-control {
    flex-grow: 1;
    margin-right: 10px;
  }

  .match-prefer {
    width: 100%;
    height: 92px;
    border-radius: 8px;
    opacity: 1;

    font-size: 14px;
    font-weight: normal;

    gap: 8px;
    align-self: stretch;

    /* 中性色/N1-背景色 */
    background: #F5F6F9;

    box-sizing: border-box;
    /* 中性色/N2-背景色 */
    border: 0.5px solid #EDEEF3;
  }

  .radius-segment {

    /* 选中状态的样式 */
    .ant-segmented-item-selected {
      border-radius: 209px;
      color: #0052D9;
      font-weight: 500;
      text-align: center;
    }
  }

  /* 上传部分样式 */
  .upload-section {
    display: flex;
    flex-direction: column;
    padding: 0px;
    gap: 8px;
    align-self: stretch;
    z-index: 3;
  }

  .upload-instructions {
    font-family: "PingFang SC";
    font-size: 12px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0em;

    font-variation-settings: "opsz" auto;
    color: #626264;
    z-index: 1;
  }

  .upload-image-title {
    font-family: "PingFang SC";
    font-size: 14px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0em;

    font-variation-settings: "opsz" auto;
    color: #1A1B1D;

    z-index: 0;
  }

  .upload-fullbody-png-container {
    width: 100%;
    height: 172px;
    border-radius: 8px;
    opacity: 1;

    /* 自动布局 */
    display: flex;
    flex-direction: row;
    padding: 8px;
    gap: 8px;
    align-self: stretch;

    /* 中性色/N1-背景色 */
    background: #F5F6F9;

    z-index: 1;
  }

  .upload-list {
    display: flex;
    justify-content: flex-start;
    /* 左对齐 */
    flex-wrap: wrap;
    /* 确保不换行 */
    gap: 8px 16px;
  }

  .batch-upload-list {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 8px;
    border-radius: 8px;
    opacity: 1;
    padding: 8px;
    background: #F5F6F9;
    z-index: 1;
  }

  .upload-item {
    position: relative;

    width: 322px;
    text-align: center;
    opacity: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    gap: 8px;

    box-sizing: border-box;
    border: 0.5px solid #D8D8D8;
    border-radius: 8px;

    z-index: 0;

    cursor: pointer;
    background: #F5F6F9;
  }

  .batch-upload-item {
    position: relative;

    width: 150px;
    height: 200px;
    text-align: center;
    opacity: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    box-sizing: border-box;
    border: 0.5px solid #D8D8D8;
    border-radius: 8px;

    z-index: 0;

    cursor: pointer;
    background: #F5F6F9;
  }

  .img-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .size-error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 0, 0, 0.1);
    /* 浅红色蒙层 */
    border-radius: 12px 12px 0 0;
  }

  .bottom-status-row {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 36px;
    opacity: 1;

    /* 自动布局 */
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 8px 0px;
    gap: 4px;

    font-size: 14px;
  }

  .upload-again {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    border-radius: 139px;
    z-index: 0;
    opacity: 0;
    font-size: 16px;
  }

  .upload-remove {
    position: absolute;
    top: 50px;
    right: 8px;
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    border-radius: 139px;
    opacity: 0;
    z-index: 0;
    font-size: 16px;
  }

  //hover到大图区域
  .img-container:hover .upload-again {
    opacity: 1;
    transform: scale(1.2);
    background-color: rgba(255, 255, 255, 0.6);
  }

  .img-container:hover .upload-remove {
    opacity: 1;
    transform: scale(1.2);
    background-color: rgba(255, 255, 255, 0.6);
  }

  //hover到小图标
  .upload-again:hover {
    opacity: 1;
    transform: scale(1.2);
    background-color: #366EF4 !important;
    color: #F5F6F9;
  }

  .upload-remove:hover {
    color: #FC343F;
    opacity: 1;
    transform: scale(1.2);
    background: #FDECEE !important;
  }

  .uploaded-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .custom-upload {
    width: 306px;
    height: 408px;
    border-radius: 8px;
    opacity: 1;

    /* 自动布局 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 16px 0px;
    gap: 12px;

    background: #FFFFFF;

    box-sizing: border-box;
    border: 1px dashed #D8D8D8;

    z-index: 0;
  }

  .batch-custom-upload {
    position: relative;
    width: 150px;
    height: 200px;
    border-radius: 8px;
    opacity: 1;
    overflow: hidden;

    /* 自动布局 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 16px 0;
    gap: 12px;

    box-sizing: border-box;
    border: 1px dashed #D8D8D8;

    z-index: 0;
    cursor: pointer;
  }

  .card-title {
    height: 20px;
    opacity: 1;

    font-family: 'PingFang SC';
    font-size: 14px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0em;

    font-variation-settings: "opsz" auto;
    color: #1A1B1D;

    z-index: 0;
  }

  .upload-text {
    /* 点击/粘贴/拖拽图片至此 */
    height: 17px;
    opacity: 1;

    font-family: 'PingFang SC';
    font-size: 12px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0em;

    font-variation-settings: "opsz" auto;
    color: #A7A7A7;

    z-index: 1;
  }

  .example-thumbnail {
    width: 100%;
    opacity: 1;
    display: flex;
    flex-direction: row;
    padding: 0;
    gap: 6px;

    z-index: 1;
  }

  .exam-icon-container {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #ffffff;
  }

  .upload-error-container {
    width: 100%;
    height: 408px;
    border-radius: 8px;
    opacity: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0px;
    gap: 12px;
    background: #FDECEE;
    box-sizing: border-box;
    border: 1px dashed #FF7979;
    z-index: 0;
  }

  .card-exam-title {
    height: 17px;
    opacity: 1;

    font-family: 'PingFang SC';
    font-size: 12px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0em;

    font-variation-settings: "opsz" auto;
    color: #A7A7A7;

    z-index: 1;
  }
}

.submit-button {
  width: 212px;
  height: 38px;
  border-radius: 8px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px 32px;
  gap: 8px;

  /* 渐变色1-disabled */
  background: linear-gradient(90deg, #99C0FF 0%, #D4C9F7 100%);

  z-index: 0;
}

.submit-button:disabled {
  color: #FFFFFF;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.6)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%);
}

.submit-button:enabled {
  background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.submit-button:not(:disabled):hover,
.create-btn:not(:disabled):focus {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

.submit-button:not(:disabled):active {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
}

/* 细节图上传样式 */
.details-upload-section {
  margin-top: 20px;
}

.details-upload-list {
  display: flex;
  justify-content: flex-start;
  flex-wrap: nowrap;
  gap: 16px;
  margin: -8px;
}

.details-upload-item {
  flex: 1;
  margin: 8px;
  text-align: center;
}

.form-footer {
  position: fixed;
  bottom: 0;
  right: 16px;

  width: calc(100vw - @menu-width - @main-gap);
  height: @footer-height;
  opacity: 1;
  z-index: 100;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  padding: 8px 16px;
  align-self: stretch;
  background: rgba(255, 255, 255, 0.8);
  box-sizing: border-box;
  border-width: 0.5px 0px 0px 0px;
  border-style: solid;
  border-color: #E1E3EB;
  backdrop-filter: blur(30px);
  gap: 16px;
}

.submit-inner {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
}

.custom-tooltip {
  .ant-tooltip-inner {
    background-color: #666; // 背景颜色
    color: white; // 文本颜色
    border-radius: 6px; // 圆角
    font-size: 14px; // 字体大小
    line-height: 1.5; // 行高
    padding: 10px; // 内边距
    max-width: 250px; // 最大宽度
  }

  // 使 Tooltip 向左偏移
  .ant-tooltip-content {
    transform: translateX(-30px);
  }

  // 透明箭头
  .ant-tooltip-arrow {
    border-color: transparent transparent #666 transparent;
    margin-left: 70px;
  }

  /* 轮播图，自定义 dots 样式 */
  .custom-dots {
    li {
      width: 8px;
      height: 8px;
      opacity: 1;
      position: relative;
      display: flex;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 50%;

      &.slick-active {
        background: rgba(255, 255, 255, 1.0);

        &::after {
          display: none;
        }
      }
    }
  }

  .custom-dots li.slick-active {
    width: 8px;
    height: 8px;
    opacity: 1;

    /* 自动布局 */
    display: flex;
    background: rgba(255, 255, 255, 1.0);
    border-radius: 50%;
    /* 设置为圆形 */

    ::after {
      display: none;
    }
  }
}

.tip-target {
  cursor: pointer; // 鼠标样式
  padding: 5px; // 内边距
}

//提交成功结果页
.ret-modal {
  .ant-modal-content {
    padding: 0;
    background: none;
    box-shadow: none;
  }

  .modal-content {
    position: relative;
    width: 464px;
    height: 610px;
    background-image: url('https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/%E6%8F%90%E4%BA%A4%E7%B4%A0%E6%9D%90%E8%83%8C%E6%99%AF1.png');
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;

  }

  .modal-content2 {
    position: absolute;
    bottom: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
  }

  .modal-picture {}

  .modal-time {
    color: white;
    font-family: PingFang SC;
    font-size: 20px;
    font-weight: normal;
    line-height: 28px;
    letter-spacing: 0px;
    margin-top: 8px;
  }

  .modal-note {
    color: rgba(255, 255, 255, 0.6);
    ;
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0px;
  }

  .modal-button {
    margin-top: 32px;
    width: 462px;
    height: 64px;
    border-radius: 226px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 16px 32px;
    gap: 8px;
    background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%);

    font-family: PingFang SC;
    font-size: 24px;
    font-weight: 500;
    line-height: 32px;
    letter-spacing: 0px;

    color: #FFFFFF;
  }

  .modal-close {
    position: absolute;
    top: 0;
    right: -40px;
    z-index: 1000;
    cursor: pointer;
  }
}

.cloth-error-message {
  color: red;
}

.color-group-block {
  position: relative;
  width: 100%;
  border-radius: 16px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 8px;
  align-self: stretch;

  background: #FCFDFD;

  box-sizing: border-box;
  border: 1px solid #D9E1FF;
  z-index: 2;
}

.del-color {
  position: absolute;
  right: 16px;
  top: 16px;
  width: 32px;
  height: 32px;
  border-radius: 139px;

  font-size: 16px;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0px;
  background: rgba(0, 0, 0, 0.1);
  z-index: 0;
  opacity: 0;
}

.color-group-block:hover {
  box-sizing: border-box;
  /* 品牌色/B3 */
  border: 1px solid #B5C7FF;
  /* 弥散投影2 */
  box-shadow: 4px 4px 16px 0px rgba(104, 112, 120, 0.15);

  .del-color {
    opacity: 1;
  }

  .del-color:hover {
    opacity: 1;
    background-color: #FDECEE;
    color: #FC343F;
    cursor: pointer;
  }
}

.upload-color-block {
  position: relative;
  width: 96px;
  height: 32px;
  opacity: 1;
  z-index: 0;

  font-family: PingFang SC;
  font-size: 18px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0px;

  /* 中性色/N8-主文字 */
  color: #1A1B1D;
}

.color-desc-block {
  width: 800px;
  height: 36px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  gap: 8px;
  z-index: 3;

}

.color-desc-title {
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 中性色/N8-主文字 */
  color: #1A1B1D;
}

.color-desc-input {
  width: 458px;
  height: 36px;
  border-radius: 8px;
  opacity: 0.8;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  padding: 8px 16px;

  background: #FFFFFF;

  box-sizing: border-box;
  /* 中性色/N4-描边线色 */
  border: 0.5px solid #D8D8D8;

  z-index: 1;
}

.empty-color-group-block {
  width: 100%;
  height: 180px;
  border-radius: 16px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 8px;
  align-self: stretch;
  background: #FCFDFD;
  box-sizing: border-box;
  border: 1px solid #D9E1FF;
  z-index: 3;
}

.empty-color-group-block:not(:disabled):hover {
  background: #E1F0FF;

  box-sizing: border-box;
  /* 品牌色/B2 */
  border: 1px solid #D9E1FF;

  /* 弥散投影2 */
  box-shadow: 4px 4px 16px 0px rgba(104, 112, 120, 0.15);

  .new-color-icon {
    color: #618DFF;
  }
}

.empty-color-group-inner {
  width: 100%;
  height: 84px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0px;
  gap: 8px;
  align-self: stretch;
  z-index: 1;
}

.new-color-icon {
  font-size: 28px;
  color: #B6C1FF;
  cursor: pointer;
}

.empty-color-group-text {
  height: 44px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0px;
  gap: 4px;
  z-index: 1;
}

.auto-create-imgs {
  margin-top: 32px;

  width: 100%;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  padding: 0px;
  gap: 8px;
  align-self: stretch;

  .auto-create-imgs-title {
    /* title/medium */
    font-family: PingFang SC;
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
    letter-spacing: 0px;

    /* 中性色/N8-主文字 */
    color: #1A1B1D;
  }

  .auto-create-imgs-subtitle {
    /* body/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: normal;
    line-height: 20px;
    letter-spacing: 0px;

    font-variation-settings: "opsz" auto;
    /* 中性色/N7-主文字2 */
    color: #727375;
  }

  .auto-create-options-container {
    width: 100%;
    height: auto;
    border-radius: 8px;
    opacity: 1;

    /* 自动布局 */
    display: flex;
    flex-direction: column;
    padding: 16px;
    gap: 16px;
    align-self: stretch;

    /* 中性色/N1-背景色 */
    background: #F5F6F9;

    z-index: 2;
  }

  .auto-create-options-models {
    width: 100%;
    height: 68px;
    opacity: 1;

    /* 自动布局 */
    display: flex;
    flex-direction: column;
    padding: 0px;
    gap: 4px;
    align-self: stretch;

    z-index: 0;
  }

  .auto-create-options-scenes {
    width: 100%;
    opacity: 1;

    /* 自动布局 */
    display: flex;
    flex-direction: column;
    padding: 0px;
    gap: 4px;
    align-self: stretch;

    z-index: 1;
  }

  .auto-create-options-title {
    /* head/medium */
    font-family: PingFang SC;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    letter-spacing: 0px;

    font-variation-settings: "opsz" auto;
    /* 中性色/N8-主文字 */
    color: #1A1B1D;
  }
}

.auto-create-options-scenes-recommend {
  width: 100%;
  height: 26px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 8px;
  gap: 8px;
  align-self: stretch;

  z-index: 3;
}

.auto-create-options-scenes-recommend-title {
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: normal;
  line-height: 18px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 中性色/N6 */
  color: #969799;
}

.auto-create-options-scenes-recommend-item-active {
  // 基础布局
  position: relative; // 关键：为伪元素定位提供基准
  width: auto;
  height: 26px;
  display: inline-flex; // 改为 inline-flex 避免宽度问题
  justify-content: center;
  align-items: center;
  padding: 4px 16px;
  border-radius: 32px;
  cursor: pointer;
  overflow: hidden; // 关键：裁剪伪元素超出圆角的部分

  // 文字样式
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: normal;
  line-height: 18px;
  background: transparent; // 移除原背景设置

  // 文字渐变效果（需要包裹 span）
  &>span {
    background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative; // 提升层级
    z-index: 1; // 确保文字在边框之上
  }

  // 渐变边框实现（伪元素方案）
  &::before {
    content: '';
    position: absolute;
    top: -1px; // 补偿边框宽度
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%);
    border-radius: 32px; // 必须与父元素相同
    z-index: 0; // 置于背景层
  }

  // 白色背景覆盖（实现边框镂空效果）
  &::after {
    content: '';
    position: absolute;
    top: 1px; // 内缩 1px
    left: 1px;
    right: 1px;
    bottom: 1px;
    background: white; // 与设计稿背景色一致
    border-radius: 31px; // 32px - 1px
    z-index: 0;
  }
}

.auto-create-options-scenes-recommend-item {
  width: auto;
  height: 26px;
  border-radius: 32px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 16px;

  box-sizing: border-box;
  border: 1px solid #99C0FF;
  z-index: 3;
  cursor: pointer;

  font-family: PingFang SC;
  font-size: 12px;
  font-weight: normal;
  line-height: 18px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 中性色/N7-主文字2 */
  color: #727375;

}

.carousel-tooltip {

  // 调整默认样式
  .ant-tooltip-arrow {
    &::before {
      background: white; // 箭头颜色匹配背景
    }
  }

  .ant-tooltip-inner {
    background: white;
    border-radius: 8px;
    padding: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    overflow: hidden;
  }
}

.tooltip-carousel {
  width: 224px;
  height: 316px;

  .carousel-image {
    width: 224px;
    height: 316px;
    object-fit: fill;
    border-radius: 8px;
  }
}

.learn-double-confirm-modal {

  .learn-double-confirm-modal-title {
    margin-left: 24px;
    margin-top: 24px;
    font-size: 20px;
    font-weight: 500;
    color: #1A1B1D;
  }

  height: 186px !important;

  // 调整 Modal 内容区域样式
  .ant-modal-content {
    padding: 24px 0 0 0 !important; // 移除左右内边距，保留顶部内边距
  }

  .ant-modal-body {
    padding: 0 24px !important; // 单独设置 body 的内边距
  }

  .modal-cancel-button {
    width: 212px !important;
    height: 38px !important;
    border: 1px solid #E1E3EB !important;
    border-radius: 8px !important;
    color: #969799 !important;
    font-size: 16px !important;
    font-weight: 500;
    background: white !important;

    &:hover {
      color: #366EF4 !important;
      border-color: #366EF4 !important;
    }
  }

  .modal-confirm-button {
    width: 212px !important;
    height: 38px !important;
    border-radius: 8px !important;
    font-size: 16px !important;
    background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%) !important;
    border: none !important;

    &:hover {
      background: linear-gradient(90deg, #5C8EF6 0%, #8273F1 100%) !important;
    }
  }

  // 调整按钮容器样式
  .ant-modal-footer {
    display: flex !important;
    justify-content: center !important;
    gap: 16px !important;
    padding: 8px 24px 24px 24px !important;
    margin-top: 16px !important;
    border-top: 0.5px solid #E1E3EB !important;
    width: 100% !important; // 确保宽度是100%
  }
}

.upload-exam-wrapper {
  height: 28px;
  border-radius: 8px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  gap: 2px;

  /* 品牌色/B1 */
  background: #F2F3FF;

  z-index: 2;
}

.upload-exam-container {
  position: fixed;
  /* 使用固定定位 */
  top: 70px;
  left: 635px;
  width: 660px;
  /* 根据设计稿调整宽度 */
  height: 744px;
  background-color: white;
  z-index: 1000;
  /* 确保在其他元素上方 */
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  /* 允许内容溢出时滚动 */

  border-radius: 8px;
}

.left-arrow {
  position: absolute;
  left: -20px;
  top: 50%;
  z-index: 100100;
}

.upload-exam-main {
  overflow-y: auto;

  border-radius: 8px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 8px;

  /* 白色 */
  background: #FFFFFF;

}

.upload-exam-main-t1 {
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 中性色/N8-主文字 */
  color: #1A1B1D;
}

.upload-exam-main-t2{
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 错误色/红色 */
  color: #FC343F;
}


.upload-exam-main-t3{
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 中性色/N8-主文字 */
  color: #1A1B1D;

}

.upload-exam-main-view {
  border-radius: 8px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  gap: 4px;

  /* 中性色/N1-背景色 */
  background: #F5F6F9;
}

.view-img-list {

  width: 612px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: row;
  padding: 0px;
  gap: 4px;
}

.view-img-list img {
  width: 120px;
  height: 120px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
}

.view-img-list-title {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  text-align: center;
  letter-spacing: 0px;

  /* 中性色/N8-主文字 */
  color: #1A1B1D;
}