import React from 'react';
import checkCaptureNoticePngCamera from '@/assets/images/cloth/拍摄-相机.png';
import { Flex } from 'antd';
import checkCaptureNoticePngArrow from '@/assets/images/cloth/查看箭头.png';

function getTitle(materialType){
  switch (materialType) {
    case 'cloth':
      return '创建服装注意事项';
    case 'face':
      return '简单几步，创建你的专属模特';
    case 'scene':
      return '简单几步，创建你的风格场景';
    default:
      return '';
  }
}

class CaptureNoticeWrapper extends React.Component<{ materialType: string, materialTypeTitle:string, onClick: () => any }> {

  render() {
    return <div className={'capture-notice-img-container'}>
      <div style={{
        position: 'absolute',
        top: 17,
        left: '33%',
        width: 'auto',
        height: '41px',
        display: 'flex',
        alignItems: 'center',
      }}>
        <img src={checkCaptureNoticePngCamera} alt={''} style={{ width: '41px', height: '41px' }} />
        <Flex vertical={true} style={{ marginLeft: 16, marginRight: 84, gap: 4 }}>
          <div style={{ fontSize: 16, lineHeight: '22px', fontWeight: 500, color: '#D9E1FF' }}>{getTitle(this.props.materialType)}</div>
          <div
            style={{ fontSize: 14, lineHeight: '20px', color: '#C8C9CC' }}>按照规定要求上传素材，可大幅提升出图效果
          </div>
        </Flex>
        <div className={'go-check-btn'} onClick={this.props.onClick}>
          <div className={'go-check-text'}>去查看</div>
          <img src={checkCaptureNoticePngArrow} alt={''} style={{ width: '16px' }} />
        </div>
      </div>
    </div>;
  }
}

export default CaptureNoticeWrapper;