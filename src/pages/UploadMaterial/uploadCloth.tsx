import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import {
	Alert,
	Button, Carousel,
	Flex,
	Input,
	message,
	Modal,
	notification, Popover,
	Radio,
	Select,
	Space,
	Spin,
	Switch,
	Tooltip,
	Upload,
} from 'antd';
import IconFont from '@/components/IconFont';
import { checkMaterialNameExists, getMaxColorNumberCfg, uploadMaterial } from '@/services/MaterialInfoController';
import { uploadFile } from '@/services/FileController';
import { useNavigate } from 'react-router-dom';
import './uploadCloth.less';
import ImgPreview from '@/components/ImgPreview';
import { compressFile, convertHeicToJpeg } from '@/utils/imageUtils';
import animationData from '@/assets/animations/uploadRet.json';
import ImageLottieCarousel from '@/components/ImageLottieCarousel';
import { queryImagePoint } from '@/services/PointController';
import TopupModal from '@/pages/Topup/Topup';
import { IS_TRIAL_ACCOUNT, USER_INFO } from '@/constants';
import { isVipUser, UserVO } from '@/services/UserController';
import uploadFullBgPng from '@/assets/images/cloth/上传全身-底图.png';
import uploadUpperBgPng from '@/assets/images/cloth/上传上半身-底图.png';
import uploadLowerBgPng from '@/assets/images/cloth/上传下半身-底图.png';
import { queryCustomerMusePoint } from '@/services/DistributorController';
import MasterSelector from '@/components/MasterSelector';
import uploadColorPng from '@/assets/images/cloth/上传颜色.png';
import TextArea from 'antd/lib/input/TextArea';
import { LabelTypeItems } from '@/components/Lora/LoraPromptsSwitch';
import { DownOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import ElementWithTypeSelector from '@/components/Creative/ElementWithTypeSelector';
import {
	ElementConfig,
	getElementConfig,
	getMerchantRecentElement,
	queryRecommendAutoGenElements,
} from '@/services/ElementController';
import ProportionBlock from '@/components/Creative/ProportionBlock';
import { getTutorialIdx, getUserInfo } from '@/utils/utils';
import AgeRangeSelector from '@/components/AgeRangeSelector';

enum ImgType {
	fullBody = 'fullBody',
	halfUpper = 'halfUpper',
	halfLower = 'halfLower'
}

interface UploadItem {
	colorGroup: number;//颜色组，1-3，单色就是颜色1，多色时颜色组最大为2或3
	type: ImgType;
	index: number;
	viewTags: string;

	url?: string;//上传服务端前是本地地址，上传服务端后与imgUrl相同，为远程地址
	file?: any;//文件对象
	imgUrl?: string;//远程地址oss url
	uploadSizeError?: boolean; //是否上传大小超出限制
	uploadSizeRatioError?: boolean;//宽高比是否有误
}

interface UploadColorGroup {
	colorGroup: number;

	fullBodyImgs?: UploadItem[];
	fullBodyImgCountErr?: string;

	upperImgs?: UploadItem[];
	upperImgCountErr?: string;

	lowerImgs?: UploadItem[];
	lowerImgCountErr?: string;

	colorDescription?: string;
}

const UploadMaterialPage = () => {
	const MIN_UPLOAD_IMAGE_CNT = ['ADMIN', 'OPERATOR'].includes(getUserInfo()?.roleType || '') ? 1 : 7;

	//上传颜色组
	const [uploadColorGroups, setUploadColorGroups] = useState<UploadColorGroup[]>([{ colorGroup: 1 }]);

	const [previewVisible, setPreviewVisible] = useState(false);
	const [previewImageUrl, setPreviewImageUrl] = useState('');

	//服装类型，男装｜女装｜童装, male|female|child|unisex
	const [clothStyleType, setClothStyleType] = useState<string | null>(null);
	const [ageRange, setAgeRange] = useState<string | null>(null);
	const [childSize, setChildSize] = useState<string | null>(null);
	const [labelType, setLabelType] = useState('default');
	const [reservedItems, setReservedItems] = useState('N');
	const [usageType, setUsageType] = useState('experience');
	const [usageMemo, setUsageMemo] = useState<string | undefined>(undefined);

	// 是否需要熨烫
	const [ironingCloth, setIroningCloth] = useState('N');

	const [clothingName, setClothingName] = useState('');
	const [clothingType, setClothingType] = useState<string | null>(null);

	const [showRetDialog, setShowRetDialog] = useState<boolean>(false);
	const [retImgs, setRetImgs] = useState<string[]>([]);

	const [flag, setFlag] = useState<boolean>(false);

	const [imgPoint, setImgPoint] = useState<number>(-1);

	const navigate = useNavigate();

	const [clothNameError, setClothNameError] = useState('');

	const [showTopupModal, setShowTopupModal] = useState(false);

	const [userInfo, setUserInfo] = useState<UserVO>();
	const [principalId, setPrincipalId] = React.useState<null | number>(null);

	const [loraMusePoint, setLoraMusePoint] = useState(40);

	//确认学习二次弹窗
	const [confirmLoading, setConfirmLoading] = useState(false);
	const [showLearnDoubleConfirmModal, setShowLearnDoubleConfirmModal] = useState(false);

	//搭配偏好
	const [matchPrefer, setMatchPrefer] = useState('');

	const [maxColorNumberCfg, setMaxColorNumberCfg] = useState<Map<string, number>>(new Map<string, number>());

	//加载图片中的loading
	const [loading, setLoading] = useState<Map<string, boolean>>(new Map<string, boolean>()); // 用于控制加载状态

	const isTrialAccount = sessionStorage.getItem(IS_TRIAL_ACCOUNT) === 'Y';

	//是否自动生成图片
	const [autoGenImgs, setAutoGenImgs] = useState(true);

	//自动生成图片时，选择的模特element id列表
	const [autoGenImgFaces, setAutoGenImgFaces] = useState<number[]>([]);
	const [showChooseFaceModal, setShowChooseFaceModal] = useState(false);

	//自动生成图片时，选择的场景element id列表
	const [autoGenImgScenes, setAutoGenImgScenes] = useState<number[]>([]);
	const [showChooseSceneModal, setShowChooseSceneModal] = useState(false);

	// 自动生成图片时，选择的模特&场景组合的配置集
	const [autoGenImgProportions, setAutoGenImgProportions] = useState<string[]>(['THREE_FOUR']);

	// 自动生成图片时，模特&场景配置集
	const [configs, setConfigs] = useState<Array<ElementConfig>>([]);
	const autoGenImgNum = 20;

	const [vipUser, setVipUser] = useState(false);

	// 系统推荐场景，用于自动化出图
	const [sysRecommendScenes, setSysRecommendScenes] = useState<ElementConfig[]>([]);
	const [clothImgUrls4RecommendScene, setClothImgUrls4RecommendScene] = useState<Array<string>>([]);
	const clothImgUrlMaxNum = 1;
	const sysRecommendSceneNum = 5;

	// 抠图准备样本 是否裁剪用于放大
	const [cut4ScaleUp, setCut4ScaleUp] = useState(false);

	const [recentFaces, setRecentFaces] = useState<ElementConfig[]>([]);
	const [recentScenes, setRecentScenes] = useState<ElementConfig[]>([]);

	useEffect(() => {

		//用户信息
		let user = localStorage.getItem(USER_INFO);
		if (user) {
			let userVO: UserVO = JSON.parse(user);
			setUserInfo(userVO);

			//非渠道商：商户、运营等
			if (userVO && userVO.roleType !== 'DISTRIBUTOR') {

				//查当前登录用户积分
				queryImagePoint().then(res => {
					if (res && res.imagePoint !== null) {
						setImgPoint(res.imagePoint);
					}
				});
			}
		}

		getMaxColorNumberCfg().then(res => {
			if (res && Object.keys(res).length > 0) {
				setMaxColorNumberCfg(new Map<string, number>(Object.entries(res)));
			} else {
				setMaxColorNumberCfg(new Map<string, number>());
			}
		});

		isVipUser().then(res => setVipUser(!!res));

	}, []);

	useEffect(() => {

		getMerchantRecentElement({
			key: 'FACE',
			userId: principalId || null
		}).then(res => {
			if (res && Array.isArray(res)) {
				setRecentFaces(res);
			}
		});

		//系统推荐场景，用于自动化出图，默认值以用户最近使用的最近场景（当商家上传第1张全身图后，将根据服装图片重新推荐）
		getMerchantRecentElement({
			key: 'SCENE',
			userId: principalId || null
		}).then(res => {
			if (res && Array.isArray(res)) {
				setRecentScenes(res);
			}
		});

	}, [clothStyleType]);

	useEffect(() => {

		if (clothImgUrls4RecommendScene && clothImgUrls4RecommendScene.length === clothImgUrlMaxNum) {

			//根据用户上传的服装图，挑选一张全身图，用于自动出图的场景推荐
			queryRecommendAutoGenElements({
				clothType: clothingType,
				clothStyleType: clothStyleType,
				clothImgUrls: clothImgUrls4RecommendScene,
				ageRange: ageRange=== 'adult'? ageRange : childSize,
			}).then(res => {
				if (res && Array.isArray(res)) {
					setSysRecommendScenes(res.slice(0, sysRecommendSceneNum));
				}
			});
		}

	}, [clothStyleType, clothImgUrls4RecommendScene]);

	useEffect(() => {
		let colorNum = uploadColorGroups.length;

		let p = colorNum > 1 ? 50 : 40;

		//渠道商代传
		if (userInfo && userInfo.roleType === 'DISTRIBUTOR') {
			p += 20 * colorNum;

			if (ironingCloth === 'Y') {
				p += 10 * colorNum;
			}
		}

		setLoraMusePoint(p);

	}, [uploadColorGroups, userInfo, ironingCloth]);

	useEffect(() => {
		//渠道商代用户上传服装，此时查询代用户积分
		if (userInfo && userInfo.roleType === 'DISTRIBUTOR' && principalId !== null && Number(principalId) > 0) {
			queryCustomerMusePoint({ customerMasterId: Number(principalId) }).then(res => {
				if (res && res.imagePoint !== null) {
					setImgPoint(res.imagePoint);
				}
			});
		}
	}, [principalId, userInfo]);

	useEffect(() => {
		// 拷贝 uploadColorGroups 以避免直接修改状态
		let newUploadColorGroups = uploadColorGroups.map(group => ({
			...group,
			fullBodyImgs: group.fullBodyImgs ? [...group.fullBodyImgs] : [],
			upperImgs: group.upperImgs ? [...group.upperImgs] : [],
			lowerImgs: group.lowerImgs ? [...group.lowerImgs] : [],
		}));

		let hasChanges = false;

		newUploadColorGroups.forEach(group => {
			if (group.fullBodyImgs.length > 0 && group.fullBodyImgs.filter(item => item.url).length < MIN_UPLOAD_IMAGE_CNT) {
				if (group.fullBodyImgCountErr !== `请至少上传${MIN_UPLOAD_IMAGE_CNT}张全身图`) {
					group.fullBodyImgCountErr = `请至少上传${MIN_UPLOAD_IMAGE_CNT}张全身图`;
					hasChanges = true;
				}
			} else {
				if (group.fullBodyImgCountErr !== '') {
					group.fullBodyImgCountErr = '';
					hasChanges = true;
				}
			}

			if (clothingType !== 'Bottoms' && group.upperImgs.length > 0 && group.upperImgs.filter(item => item.url).length < MIN_UPLOAD_IMAGE_CNT) {
				if (group.upperImgCountErr !== `请至少上传${MIN_UPLOAD_IMAGE_CNT}张上半身图`) {
					group.upperImgCountErr = `请至少上传${MIN_UPLOAD_IMAGE_CNT}张上半身图`;
					hasChanges = true;
				}
			} else {
				if (group.upperImgCountErr !== '') {
					group.upperImgCountErr = '';
					hasChanges = true;
				}
			}

			if (clothingType === 'Bottoms' && group.lowerImgs.length > 0 && group.lowerImgs.filter(item => item.url).length < MIN_UPLOAD_IMAGE_CNT) {
				if (group.lowerImgCountErr !== `请至少上传${MIN_UPLOAD_IMAGE_CNT}张下半身图`) {
					group.lowerImgCountErr = `请至少上传${MIN_UPLOAD_IMAGE_CNT}张下半身图`;
					hasChanges = true;
				}
			} else {
				if (group.lowerImgCountErr !== '') {
					group.lowerImgCountErr = '';
					hasChanges = true;
				}
			}
		});

		// 只有在有变更时才更新状态，避免无限循环
		if (hasChanges) {
			setUploadColorGroups(newUploadColorGroups);
		}
	}, [clothingType, uploadColorGroups]);

	// 获取元素配置	
	const fetchElementConfig = async (): Promise<ElementConfig[]> => {
		const value = await getElementConfig('CREATE_IMAGE');
		if (value) {
			setConfigs(value);
		}
		return value || [];
	};

	// 展示选择模特弹窗
	const handleShowChooseFaceModal = async () => {
		if (configs.length > 0) {
			setShowChooseFaceModal(true);
			return;
		}

		await fetchElementConfig();
		setShowChooseFaceModal(true);
	}

	// 展示选择场景弹窗
	const handleShowChooseSceneModal = async () => {
		if (configs.length > 0) {
			setShowChooseSceneModal(true);
			return;
		}
		await fetchElementConfig();
		setShowChooseSceneModal(true);
	}

	function filterScenesByClothStyleType(scenes: ElementConfig[], clothType: string) {
		if (!clothType) {
			return scenes || [];
		}
		return scenes.filter(c => clothType === 'unisex' ? c.type.some(type => ['Female', 'Male'].includes(type)) : (c.type.includes(clothType) || c.type.includes('Common')));
	}

	function changePrincipal(id: number) {
		setPrincipalId(id);
		if (id) {
			queryCustomerMusePoint({ customerMasterId: id }).then(res => {
				if (res && res.imagePoint !== null) {
					setImgPoint(res.imagePoint);
				}
			});
		}
	}

	function getMaxColorNumber() {
		if (userInfo && userInfo?.id && maxColorNumberCfg && maxColorNumberCfg.has(userInfo.id.toString())) {
			return maxColorNumberCfg.get(userInfo.id.toString()) || 3;
		}
		return 3;
	}

	const changeShowTopupModal = (visible: boolean) => {
		if (userInfo?.roleType === 'DISTRIBUTOR' || userInfo?.roleType === 'DEMO_ACCOUNT') {
			return;
		}
		setShowTopupModal(visible);
	};

	function onCloseTopup() {
		setShowTopupModal(false);
	}

	function onTopupSuccess() {
		setShowTopupModal(false);
		window.location.reload();
	}


	const handlePreviewUrl = (url: string | undefined) => {
		console.log('preview url:', url);
		if (url) {
			setPreviewImageUrl(url);
			setPreviewVisible(true);
		}
	};

	const handleCancelPreview = () => {
		setPreviewVisible(false);
	};

	const onFileChange = (event, item: UploadItem) => {
		const file = event.target.files[0];
		handleUploadImgItem(file, item);
		event.target.value = '';
	};

	const onFileDrop = (event, item: UploadItem) => {
		if (imgPoint > -1 && imgPoint < loraMusePoint && userInfo?.roleType === 'MERCHANT') {
			message.info(buildTopupTip(), 5);
			changeShowTopupModal(true);
		} else {
			event.preventDefault();
			const file = event.dataTransfer.files[0];
			handleUploadImgItem(file, item);
			event.target.value = '';
		}
	}

	// 更新或增加指定的上传项
	function updateImgItemByTypeAndIndex(uploadItem: UploadItem) {
		setUploadColorGroups(prevGroup => {
			return prevGroup.map(group => {
				// 检查颜色组是否匹配
				if (group.colorGroup === uploadItem.colorGroup) {
					// 根据不同的图片类型（fullBody, halfUpper, halfLower）去定位
					switch (uploadItem.type) {
						case ImgType.fullBody:
							return {
								...group,
								fullBodyImgs: group.fullBodyImgs
									? group.fullBodyImgs.some((img) => img.index === uploadItem.index)
										// 如果存在，则更新该项
										? group.fullBodyImgs.map((img) =>
											img.index === uploadItem.index ? { ...img, ...uploadItem } : img,
										)
										// 如果不存在，则追加新项
										: [...group.fullBodyImgs, uploadItem]
									: [uploadItem],
							};
						case ImgType.halfUpper:
							return {
								...group,
								upperImgs: group.upperImgs
									? group.upperImgs.some((img) => img.index === uploadItem.index)
										? group.upperImgs.map((img) =>
											img.index === uploadItem.index ? { ...img, ...uploadItem } : img,
										)
										: [...group.upperImgs, uploadItem]
									: [uploadItem],
							};
						case ImgType.halfLower:
							return {
								...group,
								lowerImgs: group.lowerImgs
									? group.lowerImgs.some((img) => img.index === uploadItem.index)
										? group.lowerImgs.map((img) =>
											img.index === uploadItem.index ? { ...img, ...uploadItem } : img,
										)
										: [...group.lowerImgs, uploadItem]
									: [uploadItem],
							};
						default:
							return group;
					}
				}
				return group;
			});
		});
	}

	const handleUploadImgItem = async (file: any, uploadItem: UploadItem, doUpload: boolean = true, retry: boolean = false): Promise<string | null> => {
		return new Promise(async (resolve, reject) => {
			try {
				// 转换 HEIC 文件
				const processedFile = await convertHeicToJpeg(file);
				if (processedFile && processedFile.type.startsWith('image/')) {
					const img = new Image();
					let objectUrl = URL.createObjectURL(processedFile);
					console.log('doUpload 1', doUpload, objectUrl)

					img.onload = () => {
						uploadItem.url = objectUrl;
						uploadItem.file = processedFile;

						if (userInfo?.roleType === 'MERCHANT') {
							// 检查图片宽度和高度
							uploadItem.uploadSizeError = img.width < 1500 || img.height < 2000;
						}

						//检查图片尺寸宽高比是否接近3：4（含有0.01的比值兼容，即 0.74-0.76）
						uploadItem.uploadSizeRatioError = (Math.abs(img.width / img.height - 3 / 4) >= 0.01);
						updateImgItemByTypeAndIndex(uploadItem);

						if (doUpload) {
							//先压缩图像到1M以内，再进行上传
							compressFile(processedFile).then(f => {
								const uploadWithRetry = (retryCount, compressedFile) => {
									uploadFile(compressedFile).then(imgUrl => {
										if (imgUrl) {
											uploadItem.imgUrl = imgUrl;

											//上传成功，更新url/file/objectUrl，清理浏览器缓存
											uploadItem.url = imgUrl;
											uploadItem.file = null;

											URL.revokeObjectURL(objectUrl);
											objectUrl = imgUrl;

											updateImgItemByTypeAndIndex(uploadItem);

											// 返回上传后的图片URL
											resolve(imgUrl);
										} else {
											resolve(null);
										}
									}).catch(error => {
										if (retry && retryCount < 5) {
											setTimeout(() => {
												console.log('group,index,retryCount:', uploadItem.colorGroup, uploadItem.index, retryCount);
												uploadWithRetry(retryCount + 1, compressedFile);
											}, 1000);
										} else {
											console.log(error);
											reject(error);
										}
									});
								};
								uploadWithRetry(0, f);
							});
						} else {
							resolve(null);
						}
					};

					img.onerror = (event) => {
						console.error('Image load error:', event);
						message.error('无法加载图片，请检查文件格式');
						URL.revokeObjectURL(objectUrl);
						reject(new Error('Image load error'));
					};

					img.src = objectUrl;
				} else {
					reject(new Error('Invalid file type'));
				}
			} catch (error) {
				console.error('File processing error:', error);
				reject(error);
			}
		});
	};

	function getNewBatchUploadInputRefKey(colorGroupNumber: number, type: ImgType) {
		return colorGroupNumber.toString() + type.toString();
	}

	const handleImageUploads = async () => {
		const startTime = Date.now();
		let totalUploadedImages = 0;

		// 遍历每个 colorGroup 进行图片上传
		for (let i = 0; i < uploadColorGroups.length; i++) {
			const group = uploadColorGroups[i];

			// 处理全身照
			const fullBodyImgs = getShowFullBodyImgs(group, true);
			for (let j = 0; j < fullBodyImgs.length; j++) {
				const img = fullBodyImgs[j];
				if (img && img.url && !img.imgUrl) {
					const imgUrl = await handleUploadImgItem(img.file, img, true, true);
					// 如果上传成功且是全身图，且推荐样本数量不足，设置为推荐样本
					if (imgUrl && clothImgUrls4RecommendScene?.length < clothImgUrlMaxNum) {
						setClothImgUrls4RecommendScene(pre => [...pre, imgUrl]);
					}
					totalUploadedImages++;
				}
			}

			// 处理上半身细节照
			const upperImgs = getShowDetailUpperImgs(group, true);
			for (let j = 0; j < upperImgs.length; j++) {
				const img = upperImgs[j];
				if (img && img.url && !img.imgUrl) {
					handleUploadImgItem(img.file, img, true, true);
					totalUploadedImages++;
				}
			}

			// 处理下半身细节照
			const lowerImgs = getShowDetailLowerImgs(group, true);
			for (let j = 0; j < lowerImgs.length; j++) {
				const img = lowerImgs[j];
				if (img && img.url && !img.imgUrl) {
					handleUploadImgItem(img.file, img, true, true);
					totalUploadedImages++;
				}
			}
		}

		// 轮询检查图片上传状态
		let isUploading = true;
		while (isUploading) {
			isUploading = false;

			// 遍历所有 colorGroup 检查是否有图片正在上传
			for (let i = 0; i < uploadColorGroups.length; ++i) {
				const group = uploadColorGroups[i];
				const fullBodyImgs = getShowFullBodyImgs(group, true);
				const upperImgs = getShowDetailUpperImgs(group, true);
				const lowerImgs = getShowDetailLowerImgs(group, true);

				// 如果存在 url 已设置但 imgUrl 为空的图片，表示仍在上传中
				for (let j = 0; j < fullBodyImgs.length; j++) {
					const img = fullBodyImgs[j];
					if (img && img.url && !img.imgUrl) {
						isUploading = true;
						break;
					}
				}

				for (let j = 0; j < upperImgs.length; j++) {
					const img = upperImgs[j];
					if (img && img.url && !img.imgUrl) {
						isUploading = true;
						break;
					}
				}

				for (let j = 0; j < lowerImgs.length; j++) {
					const img = lowerImgs[j];
					if (img && img.url && !img.imgUrl) {
						isUploading = true;
						break;
					}
				}
			}

			if (isUploading) {
				console.log('图片上传中');
				await new Promise(resolve => setTimeout(resolve, 2000));
			}
		}

		const endTime = Date.now();
		const totalTime = endTime - startTime;

		console.log(`补偿上传图片完成，总共补偿上传图片张数：${totalUploadedImages}，总耗时：${totalTime}毫秒`);
	};

	function getFullShotImgs4Submit(newUploadColorGroups: UploadColorGroup[]) {
		return newUploadColorGroups.flatMap(group => {
			return getShowFullBodyImgs(group, true).filter(img => img && img.imgUrl);
		});
	}

	function getDetailShotImgs4Submit(newUploadColorGroups: UploadColorGroup[]) {
		return newUploadColorGroups.flatMap(group => {
			return getShowDetailUpperImgs(group, true).filter(img => img && img.imgUrl)
				.concat(getShowDetailLowerImgs(group, true).filter(img => img && img.imgUrl));
		});
	}

	function getUploadColorGroups4Submit() {
		// 深拷贝 uploadColorGroups 以避免直接修改状态
		let newUploadColorGroups: UploadColorGroup[] = uploadColorGroups.map(group => ({
			...group,
			fullBodyImgs: group.fullBodyImgs ? [...group.fullBodyImgs] : [],
			upperImgs: group.upperImgs ? [...group.upperImgs] : [],
			lowerImgs: group.lowerImgs ? [...group.lowerImgs] : [],
		}));

		//按colorGroupNumber=1开始重置newUploadColorGroups中的colorGroup值，让为1、2、3...的colorGroupNumber连续
		for (let i = 0; i < newUploadColorGroups.length; ++i) {
			newUploadColorGroups[i].colorGroup = i + 1;
			if (newUploadColorGroups[i] !== null && newUploadColorGroups[i].fullBodyImgs !== null) {
				// @ts-ignore
				for (let j = 0; j < newUploadColorGroups[i].fullBodyImgs.length; ++j) {
					// @ts-ignore
					newUploadColorGroups[i].fullBodyImgs[j].colorGroup = i + 1;
				}
			}
			if (newUploadColorGroups[i] !== null && newUploadColorGroups[i].upperImgs !== null) {
				// @ts-ignore
				for (let j = 0; j < newUploadColorGroups[i].upperImgs.length; ++j) {
					// @ts-ignore
					newUploadColorGroups[i].upperImgs[j].colorGroup = i + 1;
				}
			}
			if (newUploadColorGroups[i] !== null && newUploadColorGroups[i].lowerImgs !== null) {
				// @ts-ignore
				for (let j = 0; j < newUploadColorGroups[i].lowerImgs.length; ++j) {
					// @ts-ignore
					newUploadColorGroups[i].lowerImgs[j].colorGroup = i + 1;
				}
			}
		}

		return newUploadColorGroups;
	}

	async function doSubmit() {

		//防重复点击
		if (flag) {
			return;
		}
		setFlag(true);

		//如果是演示账号，则直接mock结果
		//提交素材成功
		if (userInfo?.roleType === 'DEMO_ACCOUNT') {
			message.success('操作成功');
			setRetImgs(getShowRetImgs());
			setShowRetDialog(true);

			//成功后关闭弹窗
			setConfirmLoading(false);
			setShowLearnDoubleConfirmModal(false);
			setFlag(false);
			return;
		}

		//等待图片完成上传
		await handleImageUploads();
		console.log('图片完成上传, 开始上传素材/uploadMaterial/create');

		//重排color group number，让它从1开始
		let newUploadColorGroups = getUploadColorGroups4Submit();

		uploadMaterial({
			name: clothingName,
			clothType: clothingType,
			ageRange: ageRange === 'adult' ? ageRange : childSize,
			childSize: childSize,
			multiColors: uploadColorGroups.length > 1,
			ironingCloth: ironingCloth === 'Y',
			matchPrefer: matchPrefer,
			backPhotoNeeded: false,
			clothStyleType: clothStyleType,
			labelType,
			reservedItems,
			colorNumber: uploadColorGroups.length,
			colorDescriptions: newUploadColorGroups.map(group => group.colorDescription || ''),
			materialDetail: {
				fullShotImgList: getFullShotImgs4Submit(newUploadColorGroups).map((img) => ({
					imgUrl: img.imgUrl,
					viewTags: img.viewTags,
					colorGroupNumber: img.colorGroup,
				})),
				detailShotImgList: getDetailShotImgs4Submit(newUploadColorGroups).map((img) => ({
					imgUrl: img.imgUrl,
					viewTags: img.viewTags,
					colorGroupNumber: img.colorGroup,
				})),
			},
			principalId,

			autoGen: autoGenImgs && autoGenImgFaces?.length > 0 && autoGenImgScenes?.length > 0,
			autoGenImgParam: {
				autoGenFaceIds: autoGenImgFaces,
				autoGenSceneIds: autoGenImgScenes,
				autoGenImgProportions: autoGenImgProportions,
				autoGenTotalImgNum: autoGenImgNum
			},

			cut4ScaleUp: cut4ScaleUp ? 'Y' : 'N',
			usageType: userInfo?.roleType === 'MERCHANT' && userInfo?.memo?.includes('虚拟商家') ? usageType : null,
			usageMemo: userInfo?.roleType === 'MERCHANT' && userInfo?.memo?.includes('虚拟商家') && usageType === 'experience' ? usageMemo : null,

		}).then(res => {

			//提交素材成功
			if (res) {
				message.success('操作成功');
				setRetImgs(getShowRetImgs());
				setShowRetDialog(true);

				//成功后关闭弹窗
				setConfirmLoading(false);
				setShowLearnDoubleConfirmModal(false);

				//提交素材失败
			} else {
				notification.error({ message: '提交素材异常，请重试' });
				setFlag(false);

				//失败后保留二次弹窗
				setConfirmLoading(false);
				setShowLearnDoubleConfirmModal(true);
			}
		});
	}

	function showTopupIfNeed() {
		if (imgPoint > -1 && imgPoint < loraMusePoint && userInfo && userInfo.roleType === 'MERCHANT') {
			message.info(buildTopupTip(), 5);
			changeShowTopupModal(true);
		}
	}

	function getShowRetImgs(): string[] {
		let ret: string[] = [];

		uploadColorGroups.forEach(group => {
			// 处理全身照
			if (group.fullBodyImgs && group.fullBodyImgs.length > 0) {
				ret.push(...group.fullBodyImgs.filter(img => img && img.url).map(img => img.url as string));
			}

			// 处理上半身细节照
			if (group.upperImgs && group.upperImgs.length > 0) {
				ret.push(...group.upperImgs.filter(img => img && img.url).map(img => img.url as string));
			}

			// 处理下半身细节照
			if (group.lowerImgs && group.lowerImgs.length > 0) {
				ret.push(...group.lowerImgs.filter(img => img && img.url).map(img => img.url as string));
			}
		});

		return ret;
	}

	function getShowFullBodyImgs(group: UploadColorGroup, onlyUploaded: boolean = false) {
		let ret = group.fullBodyImgs || [];
		if (onlyUploaded) {
			ret = ret?.filter(i => i && i.url);
		}
		return ret;
	}

	//获取展示的上半身细节坑位列表
	function getShowDetailUpperImgs(group: UploadColorGroup, onlyUploaded: boolean = false) {
		let detailUpperImgs = group.upperImgs;
		if (!detailUpperImgs) {
			return [];
		}

		if (clothingType === 'Bottoms') {
			return [];
		}

		if (onlyUploaded) {
			return detailUpperImgs.filter(i => i && i.url);
		}

		return detailUpperImgs;
	}

	//获取展示的下半身细节坑位列表
	function getShowDetailLowerImgs(group: UploadColorGroup, onlyUploaded: boolean = false) {
		let detailLowerImgs = group.lowerImgs;
		if (!detailLowerImgs) {
			return [];
		}

		if (clothingType === 'Tops') {
			return [];
		}

		if (onlyUploaded) {
			return detailLowerImgs.filter(i => i && i.url);
		}

		return detailLowerImgs;
	}

	function onCloseRetDlg() {
		setRetImgs([]);
		setShowRetDialog(false);
		window.location.reload();
	}

	function onInputClothName(nameInputVal: string) {
		setClothingName(nameInputVal);
		if (nameInputVal) {
			if (nameInputVal.length < 4) {
				setClothNameError('服装名称至少4个字');
				return;
			}

			// 允许的字符：中文、英文大小写、数字、连字符、下划线
			let regex = /^[\u4e00-\u9fa5a-zA-Z0-9-_]+$/;
			let valid = regex.test(nameInputVal);

			if (!valid) {
				setClothNameError('名称只允许由4~20个以内的中文、英文大小写、数字、连字符、下划线字符组成');
			} else {
				checkMaterialNameExists(nameInputVal).then(exists => {
					if (exists) {
						setClothNameError('同名的服装已经存在');
					} else {
						setClothNameError('');
					}
				});
			}
		}
	}

	const buildTopupTip = () => {
		if (isTrialAccount) {
			return '当前缪斯点数不足，无法学习服装';
		}
		return userInfo?.roleType === 'DISTRIBUTOR' ? `当前客户点数为${imgPoint}，不足以学习服装，请联系客户进行充值` : `当前点数为${imgPoint}，不足以学习服装，请充值`;
	};

	function getNextIndexByUploadItems(items: UploadItem[] | undefined) {
		if (!items) {
			return 0;
		}
		let max = -1;
		items.forEach(item => {
			if (item.index > max) {
				max = item.index;
			}
		});

		return max + 1;
	}

	const batchUploadFiles = async (files, viewTags: string, uploadItems: UploadItem[] | undefined, imgType: ImgType, colorGroupNumber: number) => {
		if (files !== null && files.length > 0) {
			// 如果是全身图且还没有推荐样本，先单独处理第一张图片
			if (imgType === ImgType.fullBody && clothImgUrls4RecommendScene?.length < clothImgUrlMaxNum) {
				try {
					// 创建第一张图片的上传项
					let firstUploadItem: UploadItem = {
						colorGroup: colorGroupNumber,
						viewTags: viewTags,
						index: getNextIndexByUploadItems(uploadItems),
						type: imgType,
					};

					const doUpload = userInfo?.roleType !== 'DEMO_ACCOUNT';

					// 处理第一张图片并上传到远程
					const imgUrl = await handleUploadImgItem(files[0], firstUploadItem, doUpload, false);

					// 如果上传成功且是全身图，设置为推荐样本
					if (imgUrl) {
						setClothImgUrls4RecommendScene(pre => [...pre, imgUrl]);

						// 等待一段时间，确保推荐请求已发出
						await new Promise(resolve => setTimeout(resolve, 500));
					}

					// 并发处理剩余的图片
					if (files.length > 1) {
						const remainingFiles = files.slice(1);
						const uploadPromises = remainingFiles.map((file, index) => {
							const uploadImgItem: UploadItem = {
								colorGroup: colorGroupNumber,
								viewTags: viewTags,
								index: getNextIndexByUploadItems(uploadItems) + index + 1,
								type: imgType,
							};
							return handleUploadImgItem(file, uploadImgItem, doUpload);
						});

						// 等待所有剩余图片上传完成
						await Promise.all(uploadPromises);
					}
				} catch (error) {
					console.error('Error processing images:', error);
				}
			} else {
				// 对于非全身图或已有推荐样本的情况，直接并发上传所有图片
				const uploadPromises = files.map((file, index) => {
					const uploadImgItem: UploadItem = {
						colorGroup: colorGroupNumber,
						viewTags: viewTags,
						index: getNextIndexByUploadItems(uploadItems) + index,
						type: imgType,
					};
					return handleUploadImgItem(file, uploadImgItem);
				});

				// 等待所有图片上传完成
				await Promise.all(uploadPromises);
			}
		}
	};

	function getUploadExamBgPng(imgType: ImgType) {
		if (imgType === ImgType.fullBody) {
			return uploadFullBgPng;
		}
		if (imgType === ImgType.halfUpper) {
			return uploadUpperBgPng;
		}
		return uploadLowerBgPng;
	}

	function isLoading(imgType: ImgType, colorGroupNumber: number) {
		return loading[getNewBatchUploadInputRefKey(colorGroupNumber, imgType)] || false;
	}

	function setLoadingState(imgType: ImgType, colorGroupNumber: number, loadingState: boolean) {
		setLoading(pre => ({ ...pre, [getNewBatchUploadInputRefKey(colorGroupNumber, imgType)]: loadingState }));
	}

	//渲染一组图片上传区域
	function batchUploadSection(title: string, imgCountErr: string | undefined, uploadItems: UploadItem[] | undefined, viewTags: string, imgType: ImgType, mandatory: boolean = true, colorGroupNumber: number) {
		return <>
			<div className="upload-section">
				<div className="upload-img-title">
					{mandatory ? <span style={{ color: '#FF7979', fontSize: 14, marginRight: 1 }}>*&nbsp;</span> : ''}
					<span style={{ fontSize: 16 }}>{title}</span>
				</div>
				<div className="batch-upload-list" style={{ border: imgCountErr ? '1px solid #FC343F' : '' }}>

					{/*已经上传的图片列*/}
					{uploadItems && uploadItems.length > 0 && uploadItems.filter(imgItem => imgItem.url).map((imgItem, idx) => {
						return (
							<div key={idx} className="batch-upload-item"
								onDrop={(e) => onFileDrop(e, imgItem)}
								onDragOver={(e) => e.preventDefault()}
								data-index={idx}>

								<div className="img-container">
									<img src={imgItem.url}
										alt="Uploaded"
										className="uploaded-img"
										onClick={() => {
											handlePreviewUrl(imgItem.url || imgItem.imgUrl);
										}} />

									{/*底部状态栏-图片比例不对*/}
									{imgItem.uploadSizeRatioError && (
										<div className={'bottom-status-row'} style={{ background: '#FEF3E6' }}>
											<Flex gap={5}>
												<IconFont type={'icon-a-lujing2'} style={{ fontSize: 14 }} />
												<div>图片比例建议4:3</div>
											</Flex>
										</div>
									)}

									{/*上传图片大小有错误-提醒*/}
									{imgItem.uploadSizeError !== null && imgItem.uploadSizeError && (
										<>
											<div className="size-error-overlay"></div>
											<div className={'bottom-status-row'} style={{ background: '#FEF3E6' }}>
												<Tooltip title={'请上传1500x2000以上的图片'}>
													<Flex gap={5}>
														<IconFont type={'icon-cuowu'} style={{ fontSize: 14 }} />
														<div>像素过低请重新上传</div>
													</Flex>
												</Tooltip>
											</div>
										</>
									)}

									{/*重新上传*/}
									<Tooltip title="重新上传">
										<Upload
											accept=".jpg,.jpeg,.png,.heic,.heif"
											showUploadList={false}
											className="upload-again"
											beforeUpload={(file) => {
												if (imgPoint > -1 && imgPoint < loraMusePoint && userInfo?.roleType === 'MERCHANT') {
													message.info(buildTopupTip(), 5);
													changeShowTopupModal(true);
													return false;
												}
												onFileChange({ target: { files: [file] } }, imgItem);
												return false; // 阻止 Upload 组件的默认上传行为
											}}
										>
											<IconFont type={'icon-icon_qiehuan'} />
										</Upload>
									</Tooltip>

									{/*清除图片*/}
									<IconFont className="upload-remove" type={'icon-icon_shanchu'}
										onClick={() => {
											imgItem.file = null;
											imgItem.url = '';
											imgItem.imgUrl = '';
											updateImgItemByTypeAndIndex(imgItem);
										}} />
								</div>
							</div>
						);
					})}

					{/*上传图片*/}
					<Spin spinning={isLoading(imgType, colorGroupNumber)} tip="文件上传中...">
						<Upload
							accept=".jpg,.jpeg,.png,.heic,.heif"
							showUploadList={false}
							multiple={true}
							beforeUpload={async (file, fileList) => {

								// 获取当前文件在 fileList 中的索引
								const fileIndex = fileList.findIndex(item => item.uid === file.uid);

								if (fileIndex === 0 && imgPoint > -1 && imgPoint < loraMusePoint && userInfo && userInfo.roleType === 'MERCHANT') {
									message.info(buildTopupTip(), 5);
									changeShowTopupModal(true);
									return false; // 阻止上传
								}

								// 判断当前文件是否是最后一个文件
								const isLastFile = fileIndex === fileList.length - 1;

								// 只有在最后一个文件时执行上传逻辑
								if (isLastFile) {
									setLoadingState(imgType, colorGroupNumber, true); // 开始加载状态
									// 强制更新 UI
									await new Promise(resolve => setTimeout(resolve, 0));

									try {
										await batchUploadFiles(fileList, viewTags, uploadItems, imgType, colorGroupNumber); // 异步上传
									} catch (error) {
										console.error('文件上传失败:', error);
									} finally {
										setLoadingState(imgType, colorGroupNumber, false); // 结束加载状态
										// 强制更新 UI
										await new Promise(resolve => setTimeout(resolve, 0));
									}
								}

								return false; // 阻止默认上传行为
							}}
						>
							<div className="batch-custom-upload">
								<img src={getUploadExamBgPng(imgType)} alt={''}
									style={{
										width: '100%',
										height: '100%',
										position: 'absolute',
										zIndex: 0,
										filter: 'opacity(0.2)',
									}} />
								{!isLoading(imgType, colorGroupNumber) && (
									<>
										<IconFont type={'icon-a-shangchuantupian1x'} style={{ fontSize: '32px' }} />
										<div className="card-title">点击/拖拽图片至此</div>
										<div className="upload-text">（支持批量上传）</div>
									</>
								)}
							</div>
						</Upload>
					</Spin>

				</div>

				<Flex>
					{imgCountErr && <div style={{ fontSize: 16, color: '#FC343F' }}>{imgCountErr}，</div>}
					{uploadItems && uploadItems.length > 0 && uploadItems.filter(imgItem => imgItem.url && imgItem.uploadSizeError)?.length > 0 &&
						<div style={{ fontSize: 16, color: '#FC343F' }}>请上传1500x2000像素以上的图片</div>
					}
				</Flex>

			</div>
		</>;
	}

	function onClickLearnClothButton() {
		if (flag) {
			message.warning('正在处理中，请稍后');
			return;
		}

		if (userInfo?.roleType === 'DISTRIBUTOR' && !principalId) {
			message.warning('请选择服装所属的客户');
			return;
		}

		if (userInfo?.roleType === 'MERCHANT' && userInfo?.memo?.includes('虚拟商家') && usageType === 'experience' && (!usageMemo || !usageMemo.trim())) {
			message.warning('请填写客户公司名称');
			return;
		}

		if (imgPoint > -1 && imgPoint < loraMusePoint && userInfo?.roleType === 'MERCHANT') {
			if (isTrialAccount) {
				return;
			}
			message.warning(buildTopupTip(), 5);
			changeShowTopupModal(true);
			return;
		}

		if (clothingName === '') {
			message.warning('请输入服装名称');
			return;
		}

		if (clothingName.length < 4) {
			setClothNameError('服装名称至少4个字');
			message.warning('服装名称至少4个字');
			return;
		}

		if (clothNameError !== '') {
			message.warning('请输入有效的服装名称');
			return;
		}

		if (!clothingType || clothingType === '') {
			message.warning('请选择服装种类');
			return;
		}

		if (!ageRange) {
			message.warning('请选择服装年龄范围');
			return;
		}

		if (!clothStyleType) {
			message.warning('请选择服装款式');
			return;
		}

		for (const group of uploadColorGroups) {

			// 逐个遍历 uploadColorGroups 以检查图片上传情况
			let fullBodyImgsExist = false;
			let upperImgsExist = false;
			let lowerImgsExist = false;

			let fullBodyImgHasError = false;
			let upperImgHasError = false;
			let lowerImgHasError = false;

			if (group.fullBodyImgs && group.fullBodyImgs.length > 0) {
				fullBodyImgsExist = !group.fullBodyImgCountErr;
				if (group.fullBodyImgs.some(item => item.url && item.uploadSizeError)) {
					fullBodyImgHasError = true;
				}
			}

			if (group.upperImgs && group.upperImgs.length > 0) {
				upperImgsExist = !group.upperImgCountErr;
				if (group.upperImgs.some(item => item.url && item.uploadSizeError)) {
					upperImgHasError = true;
				}
			}

			if (group.lowerImgs && group.lowerImgs.length > 0) {
				lowerImgsExist = !group.lowerImgCountErr;
				if (group.lowerImgs.some(item => item.url && item.uploadSizeError)) {
					lowerImgHasError = true;
				}
			}

			// 检查图片上传数量
			if (!fullBodyImgsExist) {
				message.warning('请完成全身图片上传');
				return;
			}

			if (clothingType !== 'Bottoms' && !upperImgsExist) {
				message.warning('请完成上半身图片上传');
				return;
			}

			if (clothingType === 'Bottoms' && !lowerImgsExist) {
				message.warning('请完成下半身图片上传');
				return;
			}

			// 检查图片尺寸错误
			if (fullBodyImgHasError) {
				message.warning('全身图片像素不可低于1500 * 2000像素，请重新上传');
				return;
			}

			if (clothingType !== 'Bottoms' && upperImgHasError) {
				message.warning('上半身图片像素不可低于1500 * 2000像素，请重新上传');
				return;
			}

			if (clothingType === 'Bottoms' && lowerImgHasError) {
				message.warning('下半身图片像素不可低于1500 * 2000像素，请重新上传');
				return;
			}
		}

		setShowLearnDoubleConfirmModal(true);
	}

	function getSceneShowImgs(item: ElementConfig): string[] {
		try {
			if (item.extInfo && item.extInfo['showImgs']) {
				const parsed = JSON.parse(item.extInfo['showImgs']) as string[];
				if (Array.isArray(parsed) && parsed.every(item => typeof item === 'string')) {
					return parsed;
				}
			}
			return [];
		} catch (e) {
			console.error('解析 showImgs 失败:', e);
			return [];
		}
	}

	const RecommendSceneItem: React.FC<{
		scene: ElementConfig,
		onClickItem: (checked: boolean) => void,
		disabled: boolean
	}> = ({ scene, onClickItem, disabled }) => {
		const [isHovered, setIsHovered] = useState(false);
		const [isActive, setIsActive] = useState(false);
		const previewImages = getSceneShowImgs(scene);

		// 轮播图内容（与原来保持一致）
		const carouselContent = (
			<div className="custom-tooltip">
				<Carousel
					fade
					autoplay
					arrows={false}
					autoplaySpeed={1500}
					infinite
					dots={{ className: 'custom-dots' }}
					className="tooltip-carousel"
				>
					{previewImages.map((img, index) => (
						<div key={index}>
							<img
								src={img}
								alt={`预览图 ${index + 1}`}
								className="carousel-image"
							/>
						</div>
					))}
				</Carousel>
			</div>
		);

		return (
			<Popover
				placement="top"
				content={previewImages.length ? carouselContent : null}
				trigger="hover"
				mouseEnterDelay={0.3}     // 悬停300ms后显示
				mouseLeaveDelay={1}       // 移出后保持1秒
				open={isHovered && previewImages.length > 0}
				overlayStyle={{
					pointerEvents: 'auto',  // 允许与轮播图交互
					borderRadius: 8
				}}
				onOpenChange={(visible) => setIsHovered(visible)}
			>
				<Button
					className={
						isHovered || isActive
							? 'auto-create-options-scenes-recommend-item-active'
							: 'auto-create-options-scenes-recommend-item'
					}
					onMouseEnter={() => setIsHovered(true)}
					disabled={disabled}
					onMouseLeave={() => setIsHovered(false)}
					onClick={() => {
						if (disabled) {
							return;
						}
						setIsActive(!isActive);
						onClickItem(!isActive);
					}}
				>
					<span>{scene.name}</span>
				</Button>
			</Popover>
		);
	};


	function getCheckLearnPagePath() {
		let path = '/models?type=cloth';
		if (userInfo?.roleType === 'ADMIN') {
			path = '/cloth-mng';
		}
		return path;
	}

	function getAcceptTypes4Scene() {
		if (clothStyleType === 'male') {
			return ['Male', 'Common'];
		}
		if (clothStyleType === 'female') {
			return ['Female', 'Common'];
		}
		return undefined;
	}

	return (
		<PageContainer>

			{/* 主体 */}
			<div className={'upload-form-container'}>
				<Alert type="info" showIcon style={{padding: 8, marginTop: -8}}
							 description={<div style={{color:'red'}}>调价公告：自2025年7月1日起，3色服装的学习价格将从50缪斯点调整到60缪斯点，单色和2色服装价格保持不变。注意：2025年7月1日前充值的用户，在这部分充值金额消耗完前，仍旧按照3色50缪斯点计算</div>} />
				<div className={'text20 weight color-1a'} style={{ marginBottom: 8 }}>
					填写服装信息
					<span style={{ marginLeft: 8, fontWeight: 400, fontSize: 14, color: '#969799' }}>带
						<span style={{ color: '#FF7979' }}>*</span>为必填项</span>
				</div>

				<Space direction={'vertical'} style={{ gap: 16 }}>
					{userInfo?.roleType === 'DISTRIBUTOR' &&
						<div className="form-row">
							<div className="form-label"><span style={{ color: '#FF7979', fontSize: 14, marginRight: 1 }}>
								*</span>所属客户:
							</div>
							<MasterSelector onChange={changePrincipal} width={434} />
						</div>
					}

					{userInfo?.roleType === 'MERCHANT' && userInfo?.memo?.includes('虚拟商家') &&
						<div className="form-row">
							<div className="form-label"><span style={{ color: '#FF7979', fontSize: 14, marginRight: 1 }}>
								*</span>服装用途:
							</div>

							<Select defaultValue="experience" style={{width:120}} value={usageType} onChange={setUsageType}
											options={[{label: '售前体验', value: 'experience'},  {label: '测试', value: 'test'}]} />

							{usageType === 'experience' &&
								<>
									<div className="form-label" style={{marginLeft:12}}><span style={{ color: '#FF7979', fontSize: 14, marginRight: 1 }}>
									*</span>客户公司名称:
									</div>
									<Input style={{width: 434}} value={usageMemo} minLength={2} maxLength={50}
																							onChange={e=>setUsageMemo(e.target.value)}
																							placeholder={'请填写服装所属客户公司名称'} />
								</>
							}
						</div>
					}

					<div className="form-row">
						<div className="form-label"><span style={{ color: '#FF7979', fontSize: 14, marginRight: 1 }}>*</span>服装名称:
						</div>
						<Input placeholder="请输入服装款式的名称"
							style={{ width: '434px', borderColor: clothNameError ? 'red' : '' }} value={clothingName}
							maxLength={20} onChange={e => onInputClothName(e.target.value)} />
						{clothNameError && <div className="cloth-error-message">{clothNameError}</div>}
					</div>

					<div className="form-row">
						<div className="form-label">
							<span style={{ color: '#FF7979', fontSize: 14, marginRight: 1 }}>*</span>服装种类:
						</div>

						<Radio.Group defaultValue="TwoPiece"
							value={clothingType}
							buttonStyle="solid"
							onChange={e => {
								setClothingType(e.target.value);
								showTopupIfNeed();
							}}>
							<Radio.Button value="TwoPiece">套装（上装+下装）</Radio.Button>
							<Radio.Button value="Tops">仅上装</Radio.Button>
							<Radio.Button value="Bottoms">仅下装</Radio.Button>
							<Radio.Button value="OnePiece">连体服装（如连衣裙）</Radio.Button>
							<Radio.Button value="SwimSuit">泳衣</Radio.Button>
              <Radio.Button value="SexyLingerie">内衣</Radio.Button>
						</Radio.Group>
					</div>

					<div className="form-row" style={{ height: 22 }}>
						<div className="form-label">
							<span style={{ color: '#FF7979', fontSize: 14, marginRight: 1 }}>*</span>年龄区段:
						</div>
						<AgeRangeSelector
							value={ageRange}
							childSize={childSize}
							onChange={setAgeRange}
							onChildSizeChange={setChildSize}
							showTopupIfNeed={showTopupIfNeed}
						/>
					</div>

					<div className="form-row" style={{ height: 22 }}>
						<div className="form-label">
							<span style={{ color: '#FF7979', fontSize: 14, marginRight: 1 }}>*</span>服装款式:
						</div>
						<Radio.Group defaultValue="female"
							buttonStyle="solid"
							onChange={(e) => {
								setClothStyleType(e.target.value);
								showTopupIfNeed();
							}}
							value={clothStyleType}>
							<Radio.Button style={{ width: 96, borderRadius: 32, textAlign: 'center' }} value="female">女款</Radio.Button>
							<Radio.Button style={{ width: 96, borderRadius: 32, textAlign: 'center' }} value="male">男款</Radio.Button>
							<Radio.Button style={{ width: 96, borderRadius: 32, textAlign: 'center' }} value="unisex">男女同款</Radio.Button>
							<Radio.Button value="child" style={{ display: 'none' }}>童款</Radio.Button>
						</Radio.Group>
					</div>

					<div className="form-row" style={{ height: 22 }}>
						<div className="form-label">
							<span style={{ color: '#FF7979', fontSize: 14, marginRight: 1 }}>*</span>保留配饰:
						</div>
						<Radio.Group buttonStyle="solid" value={reservedItems}
												 onChange={(e) => {
													 setReservedItems(e.target.value);
													 showTopupIfNeed();
												 }}>
							<Radio.Button style={{ width: 96, borderRadius: 32, textAlign: 'center' }} value="N">否</Radio.Button>
							<Radio.Button style={{ width: 96, borderRadius: 32, textAlign: 'center' }} value="Y">是</Radio.Button>
						</Radio.Group>
					</div>

					{userInfo && userInfo.roleType === 'OPERATOR' &&
						<div className="form-row" style={{ height: 22 }}>
							<div className="form-label">
								<span style={{ color: '#FF7979', fontSize: 14, marginRight: 1 }}>*</span>打标方式:
							</div>
							<Radio.Group defaultValue="N" buttonStyle="solid" value={labelType}
								onChange={(e) => setLabelType(e.target.value)}>
								{LabelTypeItems.cloth.map(e => <Radio.Button value={e.tag} key={e.tag}>{e.label}</Radio.Button>)}
							</Radio.Group>
						</div>
					}

					{userInfo && userInfo.roleType === 'DISTRIBUTOR' && (
						<div className="form-row" style={{ height: 22 }}>
							<div className="form-label">
								<span style={{ color: '#FF7979', fontSize: 14, marginRight: 1 }}>*</span>是否代熨烫:
							</div>
							<Radio.Group defaultValue="female"
								buttonStyle="solid"
								onChange={(e) => {
									setIroningCloth(e.target.value);
									showTopupIfNeed();
								}}
								value={ironingCloth}>
								<Radio.Button value="N">否</Radio.Button>
								<Tooltip title={'代熨烫将额外消耗muse点'}>
									<Radio.Button value="Y">是</Radio.Button>
								</Tooltip>
							</Radio.Group>
						</div>
					)}

				</Space>

				<div style={{ fontSize: 20, fontWeight: 500, color: '#1A1B1D', marginTop: 24, marginBottom: 8 }}>
					<span>款式偏好</span>
					<span style={{ fontSize: 16, color: '#969799' }}>（选填）</span>
				</div>

				<TextArea className={'match-prefer'} value={matchPrefer} onChange={(e) => setMatchPrefer(e.target.value)}
					maxLength={500}
					showCount
					placeholder={'请在这里填写你希望的服装搭配，模特形象，服装适用的场景等等，便于AI更好的学习和理解这套服装。 \n如：外国模特，街拍场景，模特穿着高跟鞋。'}>
				</TextArea>


				<Flex className={'upload-tutorial-title'}>
					<div style={{ fontSize: 20, fontWeight: 500, color: '#1A1B1D' }}>
						上传服装图片
					</div>
					<div className={'upload-tutorial-container'} onClick={() => navigate('/tutorial?idx=' + getTutorialIdx('cloth'))}>
						<IconFont type={'icon-lujing4'} style={{ fontSize: 12 }} />
						<span className={'upload-tutorial-notice-text'}>上传须知</span>
						<IconFont type={'icon-youjiantou_16px'} style={{ fontSize: 12, color: '#366EF4' }} />
					</div>
				</Flex>

				{uploadColorGroups.map((group, idx) => (
					<div key={group.colorGroup} className={'color-group-block'}>

						{uploadColorGroups.length > 1 && (
							<Tooltip title={'删除该颜色'}>
								<IconFont type={'icon-icon_shanchu'} className={'del-color'} onClick={() => {
									setUploadColorGroups(prevGroups =>
										prevGroups.filter(innerGroup => innerGroup.colorGroup !== group.colorGroup),
									);
								}} />
							</Tooltip>
						)}

						<div className={'upload-color-block'}>
							<div style={{ position: 'absolute', zIndex: 1 }}>上传颜色&nbsp;{idx + 1}</div>
							<img src={uploadColorPng} alt={''}
								style={{ width: 79, height: 12, position: 'absolute', left: 4, top: 15, zIndex: 0 }} />
						</div>

						{batchUploadSection(`1.上传全身图（至少${MIN_UPLOAD_IMAGE_CNT}张）`, group?.fullBodyImgCountErr, group?.fullBodyImgs, 'full', ImgType.fullBody, true, group.colorGroup)}
						{clothingType !== 'Bottoms' &&
							batchUploadSection(`2.上传上半身图（至少${MIN_UPLOAD_IMAGE_CNT}张）`, group.upperImgCountErr, group?.upperImgs, 'upper', ImgType.halfUpper, true, group.colorGroup)}
						{clothingType === 'Bottoms' &&
							batchUploadSection(
								`${clothingType !== 'Bottoms' ? '3' : '2'}.上传下半身图${clothingType === 'Bottoms' ? `（至少${MIN_UPLOAD_IMAGE_CNT}张）` : '（可选）'}`,
								group?.lowerImgCountErr, group?.lowerImgs, 'lower', ImgType.halfLower, clothingType === 'Bottoms', group.colorGroup)}

						<div className={'color-desc-block'}>
							<span className={'color-desc-title'}>补充该颜色准确名称：</span>

							<Input className={'color-desc-input'}
								placeholder={'请输入颜色的准确名称，如：卡其色、浅棕色、深蓝绿色（非必填）'}
								onChange={(e) => {
									setUploadColorGroups(prevGroups => {
										return prevGroups.map(colorGroup => {
											if (colorGroup.colorGroup === group.colorGroup) {
												// 更新指定 colorGroup 的 colorDescription
												return {
													...colorGroup,
													colorDescription: e.target.value,
												};
											} else {
												return colorGroup;
											}
										});
									});
								}} />
							<span style={{ color: '#969799', fontSize: 14, fontWeight: 'normal' }}>帮助ai更好的还原服装颜色</span>
						</div>

					</div>
				))}

				<div className={'empty-color-group-block'}>
					<div className={'upload-color-block'}>
						<div style={{ position: 'absolute', zIndex: 1 }}>上传颜色&nbsp;{uploadColorGroups.length + 1}</div>
						<img src={uploadColorPng} alt={''}
							style={{ width: 79, height: 12, position: 'absolute', left: 4, top: 15, zIndex: 0 }} />
					</div>

					{uploadColorGroups.length < getMaxColorNumber() && (
						<div className={'empty-color-group-inner'}
							style={{ cursor: 'pointer' }}
							onClick={() => {
								setUploadColorGroups([...uploadColorGroups, {
									colorGroup: uploadColorGroups[uploadColorGroups.length - 1].colorGroup + 1,
									fullBodyImgs: [],
									upperImgs: [],
									lowerImgs: [],
								}]);
							}}>
							<IconFont type={'icon-lujing3'} className={'new-color-icon'} />
							<div className={'empty-color-group-text'}>
								<div style={{ color: '#1A1B1D' }}>点击上传&nbsp;颜色{uploadColorGroups.length + 1}</div>
								<div style={{ color: '#727375' }}>仅支持<span style={{ color: '#366EF4' }}>相同款式</span>多色上传。仅支持纯色服装，拼色服装请单独上传。
								</div>
							</div>
						</div>
					)}

					{uploadColorGroups.length >= getMaxColorNumber() && (
						<div className={'empty-color-group-inner'}>
							<IconFont type={'icon-lujing3'} style={{ fontSize: 28, color: '#D8D8D8' }} />
							<div className={'empty-color-group-text'}>
								<div style={{ color: '#727375' }}>
									单款多色上传颜色已到最大限度，请新建服装模型上传
								</div>
							</div>
						</div>
					)}

				</div>

				<div className={'auto-create-imgs'}>
					<div className={'auto-create-imgs-title'}>{`服装学习完成后，自动免费生成${autoGenImgNum}张创作图`}</div>
					<div className={'auto-create-imgs-subtitle'}>
						{`注：${autoGenImgNum}张图跟随学习过程一同生成，跟随服装学习结束一同展示。不占用套餐100张创作图额度。可节约您的生图等待时长。如果您不需要，可以点击右侧按钮关闭`}
						<Switch style={{ marginLeft: 4 }}
							size={'small'}
							defaultChecked
							onChange={checked => setAutoGenImgs(checked)}
						/>
					</div>

					{autoGenImgs &&
						<div className={'auto-create-options-container'}>

							{/*选模特*/}
							<div className={'auto-create-options-models'}>
								<div className={'auto-create-options-title'}>
									{`选择模特（最多3个）`}
								</div>
								<div onClick={(e) => {
									e.stopPropagation(); // 阻止事件冒泡
									handleShowChooseFaceModal();
								}} style={{ display: 'inline-block', cursor: 'pointer' }}>
									<Select
										value={autoGenImgFaces}
										mode="tags"
										options={configs.filter(c => c.configKey === 'FACE')[0]?.children?.map((c: ElementConfig) => ({
											key: c.id,
											label: <Flex gap={4} align={'center'}>
												<img src={c.showImage} alt={c.name} style={{ width: 24, height: 24 }} />
												<span style={{ fontSize: 14 }}>{c.name}</span>
											</Flex>,
											value: c.id
										}))}
										open={false} // 强制关闭原生下拉菜单
										suffixIcon={
											<DownOutlined />
										}
										onChange={(values) => {
											setAutoGenImgFaces(values);
										}}
										placeholder="点击选择模特"
										style={{ width: '100%' }}
										dropdownStyle={{ display: 'none' }}
									/>
								</div>
							</div>

							{/*选场景*/}
							<div className={'auto-create-options-scenes'}>
								<div className={'auto-create-options-title'}>
									{`选择场景（最多5个）`}
								</div>

								<div onClick={(e) => {
									e.stopPropagation(); // 阻止事件冒泡
									handleShowChooseSceneModal();
								}} style={{ display: 'inline-block', cursor: 'pointer' }}>
									<Select
										value={autoGenImgScenes}
										mode="tags"
										options={configs.filter(c => c.configKey === 'SCENE')[0]?.children?.map((c: ElementConfig) => ({
											key: c.id,
											label: <Flex gap={4} align={'center'}>
												<img src={c.showImage} alt={c.name} style={{ width: 24, height: 24 }} />
												<span style={{ fontSize: 14 }}>{c.name}</span>
											</Flex>,
											value: c.id
										}))}
										open={false} // 强制关闭原生下拉菜单
										suffixIcon={
											<DownOutlined />
										}
										onChange={(values) => {
											// 添加 onChange 处理器来处理删除操作
											setAutoGenImgScenes(values);
										}}
										placeholder="点击选择场景"
										style={{ width: '100%' }}
										dropdownStyle={{ display: 'none' }}
									/>
								</div>

								{/*系统推荐的场景*/}
								{sysRecommendScenes && sysRecommendScenes?.length > 0 &&
									<div className={'auto-create-options-scenes-recommend'}>
										<div className={'auto-create-options-scenes-recommend-title'}>系统推荐</div>
										{sysRecommendScenes && sysRecommendScenes.map((scene: ElementConfig) => (
											<RecommendSceneItem
												scene={scene}
												disabled={!autoGenImgScenes?.includes(scene.id) && autoGenImgScenes?.length >= 5}
												onClickItem={checked => {
													if (checked) {
														if (!autoGenImgScenes.includes(scene.id)) {
															setAutoGenImgScenes(prev => [...prev, scene.id]);
														}
													} else {
														setAutoGenImgScenes(prev => prev.filter(s => s !== scene.id));
													}
												}} />
										))}
									</div>
								}

							</div>

							{/*选出图比例（单选）*/}
							<ProportionBlock style={{ padding: 0 }}
								value={'P_1152_1536'}
								onChange={p => setAutoGenImgProportions([p])}
								type={'CUSTOM'}
								isVip={vipUser} />
						</div>
					}

				</div>

				{userInfo && userInfo?.roleType === 'OPERATOR' &&
					<div style={{ marginTop: 32, marginBottom: 32 }}>
						<span style={{ fontSize: 20, fontWeight: 500, color: '#1A1B1D', marginTop: 24 }}>实验参数</span>
						<Flex gap={8} style={{ marginTop: 24 }}>
							<span style={{ fontSize: 14 }}>开启抠图放大比例</span>
							<Switch size={'small'} onChange={checked => setCut4ScaleUp(checked)} />
						</Flex>
					</div>
				}

			</div>

			{/*选模特-全部模特*/}
			{showChooseFaceModal &&
				<ElementWithTypeSelector
					current={autoGenImgFaces ? configs.filter(c => c.configKey === 'FACE')[0]?.children?.filter(f => autoGenImgFaces?.includes(f.id)) : []}
					title={'模特'} orderByType={false} maxChoose={3}
					config={configs.filter(c => c.configKey === 'FACE')[0]}
					onClose={() => setShowChooseFaceModal(false)}
					conditions={
						{
							ageRange: ageRange === 'adult' ? ageRange : childSize,
						}
					}
					recentList={recentFaces || []}
					onChange={(faces) => {
						if (faces) {
							setAutoGenImgFaces(faces.map(face => face.id));
						}
						setShowChooseFaceModal(false);
					}}
					acceptTypes={clothStyleType ? (clothStyleType === 'unisex' ? ['male-model', 'female-model'] : [clothStyleType + '-model']) : undefined}
					showConfirmFooter={true}
					isVipUser={vipUser}
				/>
			}

			{/*选场景-全部场景*/}
			{showChooseSceneModal &&
				<ElementWithTypeSelector
					current={autoGenImgScenes ? configs.filter(c => c.configKey === 'SCENE')[0]?.children?.filter(f => autoGenImgScenes?.includes(f.id)) : []}
					title={'场景'}
					orderByType={false}
					maxChoose={5}
					config={configs.filter(c => c.configKey === 'SCENE')[0]}
					onClose={() => setShowChooseSceneModal(false)}
					recentList={filterScenesByClothStyleType(recentScenes, clothStyleType)}
					conditions={
						{
							ageRange: ageRange === 'adult' ? ageRange : childSize,
						}
					}
					onChange={(scenes) => {
						if (scenes) {
							setAutoGenImgScenes(scenes.map(it => it.id));
						}
						setShowChooseSceneModal(false);
					}}
					acceptTypes={getAcceptTypes4Scene()}
					showConfirmFooter={true}
					isVipUser={vipUser}
				/>
			}

			{/* 图片预览 */}
			<ImgPreview
				previewVisible={previewVisible}
				handleCancelPreview={handleCancelPreview}
				previewImage={previewImageUrl}
				needWatermark={false}
			/>

			<div className="form-footer">
				<Flex className={'text16 font-pf color-1a'} align={'flex-end'} justify={'center'} vertical>
					<div>本次学习需消耗<IconFont type={'icon-icon_mousidian'} />{loraMusePoint}缪斯点</div>
					<a target={'_blank'} href={'disclaimer.html'} className={'color-96'}>免责声明 <ExclamationCircleOutlined
						style={{ fontSize: 14 }} className={'color-96'} /></a>
				</Flex>

				{/* 确认上传 */}
				<Button className="submit-button" type="primary"
					onClick={onClickLearnClothButton}>
					<div className={'submit-inner'}>
						创建服装
					</div>
				</Button>
			</div>

			{showLearnDoubleConfirmModal &&
				<Modal
					className='learn-double-confirm-modal'
					width={'auto'}
					styles={{
						content: {
							borderRadius: 16,
						}
					}}
					title={<span
						className='learn-double-confirm-modal-title'>{(autoGenImgs && (!autoGenImgFaces || autoGenImgFaces?.length === 0 || !autoGenImgScenes || autoGenImgScenes?.length === 0))
							? '未选择自动生成创作的模特或场景，是否继续'
							: '操作确认'}</span>}
					centered
					open={showLearnDoubleConfirmModal}
					okText={confirmLoading ? '上传中' : ((autoGenImgs && (!autoGenImgFaces || autoGenImgFaces?.length === 0 || !autoGenImgScenes || autoGenImgScenes?.length === 0)) ? '直接学习' : '确定')}
					cancelText={(autoGenImgs && (!autoGenImgFaces || autoGenImgFaces?.length === 0 || !autoGenImgScenes || autoGenImgScenes?.length === 0)) ? '返回添加' : '取消'}
					cancelButtonProps={{ disabled: confirmLoading, className: 'modal-cancel-button' }}
					onCancel={() => {
						setShowLearnDoubleConfirmModal(false);
						setConfirmLoading(false);
					}}
					footer={[
						<Button
							key="cancel"
							disabled={confirmLoading}
							className='modal-cancel-button'
							onClick={() => {
								setShowLearnDoubleConfirmModal(false);
								setConfirmLoading(false);
							}}
						>
							{(autoGenImgs && (!autoGenImgFaces || autoGenImgFaces?.length === 0 || !autoGenImgScenes || autoGenImgScenes?.length === 0)) ? '返回添加' : '取消'}
						</Button>,
						<Button
							key={'submit'}
							type="primary"
							className='modal-confirm-button'
							loading={confirmLoading}
							onClick={() => {
								if (confirmLoading || flag) {
									message.info('正在处理中，请不要重复点击');
									return;
								}

								setConfirmLoading(true);
								setTimeout(() => {
									doSubmit();
								}, 0);
							}}
						>
							{confirmLoading ? '上传中' : ((autoGenImgs && (!autoGenImgFaces || autoGenImgFaces?.length === 0 || !autoGenImgScenes || autoGenImgScenes?.length === 0)) ? '直接学习' : '确定')}
						</Button>
					]}
					closable={!confirmLoading}
					maskClosable={false}
					keyboard={false}
				>
					<span style={{ fontSize: 14, color: '#727375' }}>
						{(autoGenImgs && (!autoGenImgFaces || autoGenImgFaces?.length === 0 || !autoGenImgScenes || autoGenImgScenes?.length === 0))
							? <>
								{`推荐添加模特和场景，服装学习完成后将自动免费展示${autoGenImgNum}张创作图，`}
								<br />
								直接学习将扣除 {loraMusePoint} 缪斯点，并创建服装"{clothingName}"，含 100 张图片创作数量
							</>
							: `将扣除 ${loraMusePoint} 缪斯点，并创建服装"${clothingName}"，含 100 张图片创作数量`
						}
					</span>
				</Modal>
			}

			{/* 提交成功页面 */}
			{showRetDialog && (
				<Modal
					open={showRetDialog}
					footer={null}
					keyboard={false}
					centered={true}
					width={'auto'}
					mask={true}
					closable={false}
					className="ret-modal"
					styles={{
						mask: {
							backgroundColor: 'rgba(0,0,0,0.8)',
						},
					}}
				>
					<div>
						<div className="modal-content">
							<ImageLottieCarousel images={retImgs} scanAnimation={animationData} />
							<div className="modal-content2">
								<div className="modal-time">预计学习时间约24小时</div>
								<div className="modal-note">退出后学习不受影响</div>
							</div>
						</div>
						<Button type="primary" className="modal-button" onClick={() => {
							navigate(getCheckLearnPagePath());
							window.location.reload();

						}}>查看服装学习进度</Button>
						<div className="modal-close" onClick={onCloseRetDlg}>
							<IconFont type={'icon-lujing'} style={{ fontSize: 24, color: 'white' }} />
						</div>
					</div>
				</Modal>
			)}

			{showTopupModal && <TopupModal visible={showTopupModal} onClose={onCloseTopup} onPaySuccess={onTopupSuccess} />}

		</PageContainer>
	);
};

export default UploadMaterialPage;