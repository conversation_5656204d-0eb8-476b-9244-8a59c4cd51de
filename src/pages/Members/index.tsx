import React, {useEffect, useState} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Pagination, Input, Modal, Form, message, Flex, Tooltip } from 'antd';
import './index.less';

import {
    createSubUser,
    deleteUser,
    disableSubUser, enableSubUser,
    getPageUser,
    updateSubUser,
    USER_TYPE,
    UserVO
} from "@/services/UserController";

interface UserModalConfig {
    title: string,
    uid?: number|undefined,
    onOk: (e: React.MouseEvent<HTMLButtonElement>) => void,
    onCancel: (e: React.MouseEvent<HTMLButtonElement>) => void
}

const UserManagement: React.FC = () => {
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [hasNextPage, setHasNextPage] = useState(true);
    const [totalCount, setTotalCount] = useState(0);
    const [data, setData] = useState<Array<UserVO>>([]);
    const [modifyTimeSort, setModifyTimeSort] = useState<string>('');
    const [lastLoginTimeSort, setLastLoginTimeSort] = useState<string>('');

    const columns = [
        {
            title: '用户ID',
            dataIndex: 'id',
            key: 'id',
            render: (text) => <span style={{color: '#409EFF'}}>{text}</span>
        },
        {
            title: '昵称',
            dataIndex: 'nickName',
            key: 'nickName',
        },
        {
            title: '手机号',
            dataIndex: 'mobile',
            key: 'mobile',
        },
        {
            title: '修改时间',
            dataIndex: 'modifyTime',
            key: 'modifyTime',
            sorter: true,
        },
        {
            title: '最近登录',
            dataIndex: 'lastLoginTime',
            key: 'lastLoginTime',
            sorter: true,
        },
        {
            title: '用户身份',
            dataIndex: 'userType',
            key: 'userType',
            render: (text: USER_TYPE) => <span>{text === "MASTER" ? '管理员' : '普通用户'}</span>
        },
        {
            title: '操作',
            key: 'action',
            className: 'action-column',
            render: (text, record: UserVO) => (
                <span className="action-buttons">
                    <a onClick={() => showUpdateUserModal(record)}>编辑</a>
                    <a onClick={() => showDeleteUserModal(record)}>删除</a>
                    <a onClick={() => record.status === "ENABLED" ? showDisableUserModal(record.id) : handleEnableSubUser(record.id)}>
                        {record.status === "ENABLED" ? '停用' : '启用'}
                    </a>
                </span>
            ),
        },
    ];

    function handleEnableSubUser(uid: number) {
        enableSubUser(uid).then(res => {
            if (res){
                message.success('操作成功');
                refreshDataList();
            }
        })
    }

    async function refreshDataList() {
        setCurrentPage(1);
        window.location.reload();
    }

    useEffect(() => {

        async function fetchData() {

            let orderBy = '';
            if (modifyTimeSort === 'ascend') {
                orderBy += 'modify_time asc';
            } else if (modifyTimeSort === 'descend') {
                orderBy += 'modify_time desc';
            }

            if (lastLoginTimeSort === 'ascend') {
                if (orderBy.length > 0) {
                    orderBy += ',';
                }
                orderBy += 'last_login_time asc';
            } else if (lastLoginTimeSort === 'descend') {
                if (orderBy.length > 0) {
                    orderBy += ',';
                }
                orderBy += 'last_login_time desc';
            }

            let query = {
                pageNum: currentPage,
                pageSize: pageSize
            }

            if (orderBy.length > 0) {
                query['orderBy'] = orderBy;
            }

            console.log('分页查询用户列表:', query);

            getPageUser(query).then(res => {
                console.log('fetch data list response:', res);

                //@ts-ignore 追加一个key字段，避免警告
                let newData = res.list.map(item => ({ ...item, key: item.id }));
                console.log('newData:', newData);

                setData(newData);

                console.log('data:', data);

                // @ts-ignore
                setHasNextPage(res.hasNextPage);

                // @ts-ignore
                setTotalCount(res.totalCount);
            });
        }

        fetchData();

    }, [currentPage, pageSize, modifyTimeSort, lastLoginTimeSort]);

    // 删除用户弹窗确认
    function showDeleteUserModal(u: UserVO){
        Modal.confirm({
            title: '确认删除',
            content: '删除后不可恢复，请谨慎操作',
            okText: '确认删除',
            cancelText: '取消',
            okType: 'danger',
            icon: null,
            onOk() {
                deleteUser(u.id).then(res => {
                    if (res){
                        message.success('操作成功');
                        refreshDataList();
                    }
                })
            }
        });
    }

    // 停用用户弹窗确认
    function showDisableUserModal(id: number){
        Modal.confirm({
            title: '确认停用该用户',
            content: '后续可对用户重新启用',
            okText: '确认停用',
            okType: 'danger',
            cancelText: '取消',
            icon: null,
            onOk() {
                disableSubUser(id).then(res => {
                    if (res){
                        message.success('操作成功');
                        refreshDataList();
                    }
                })
            }
        });
    }

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    const handlePageSizeChange = (current, size) => {
        setPageSize(size);
        setCurrentPage(1); // 重置到第一页
    };

    const [showUserModal, setShowUserModal] = useState(false);
    const [showUserModalConfig, setShowUserModalConfig] = useState<UserModalConfig>();

    const [form] = Form.useForm();

    const showNewUserModal = () => {
        setShowUserModal(true);
        setShowUserModalConfig({
            title: '新增成员',
            uid: undefined,
            onOk: handleNewUser,
            onCancel: handleCancel
        });
        form.resetFields();
    };

    const showUpdateUserModal = (u: UserVO) => {
        setShowUserModal(true);
        setShowUserModalConfig({
            title: '编辑用户',
            onOk: () => handleUpdateUser(u.id),
            onCancel: handleCancel,
            uid: u.id
        });

        form.setFieldsValue({
            nickName: u.nickName,
            mobile: u.mobile,
        });
    };

    const handleCancel = () => {
        setShowUserModal(false);
        form.resetFields();
    };

    const handleNewUser = () => {

        form.validateFields()
            .then(values => {
                // 在这里处理表单提交
                console.log('表单值: ', values);

                createSubUser({
                    'mobile': values.mobile,
                    'nickName': values.nickName
                }).then(res => {
                    console.log('res:', res);
                    if (res) {
                        message.success('操作成功');
                        refreshDataList();

                    } else {
                        message.error('操作失败');
                    }
                });

                setShowUserModal(false);
                form.resetFields();
            })
            .catch(info => {
                console.log('验证失败:', info);
            });
    };

    const handleUpdateUser = (uid: number) => {
        form.validateFields()
            .then(values => {
                // 在这里处理表单提交
                console.log('表单值: ', values);

                updateSubUser({
                    'userId': uid,
                    'mobile': values.mobile,
                    'nickName': values.nickName
                }).then(res => {
                    console.log('res:', res);
                    if (res) {
                        message.success('操作成功');
                        refreshDataList();
                    }
                });

                setShowUserModal(false);
                form.resetFields();
            })
            .catch(info => {
                console.log('验证失败:', info);
            });
    };

    // 处理排序
    // 备注：当前antd的Table控件只支持一个字段的排序，ProTable支持多个字段排序，但需要更复杂的研发，暂时先放一下。因此，下面的代码只支持一个字段排序。
    const handleTableChange = (pagination, filters, sorter) => {
        console.log('handleTableChange:', sorter);
        if (sorter.field === 'modifyTime') {
            setModifyTimeSort(sorter.order);
            setLastLoginTimeSort('');
        }
        if (sorter.field === 'lastLoginTime') {
            setModifyTimeSort('');
            setLastLoginTimeSort(sorter.order);
        }
    };

    return (
        <PageContainer>
            <div className={'members-main'}>
                <div className="members-top-bar">
                    <Tooltip title="添加子账号，邀请团队成员共同使用服务" placement="left">
                        <Button type="primary" className="members-add-user-button" onClick={() => showNewUserModal()}>
                            + 新增成员
                        </Button>
                    </Tooltip>
                </div>
                <Table
                    columns={columns}
                    dataSource={data}
                    pagination={false}
                    onChange={handleTableChange}
                    className="members-user-table"
                />
                <div className="members-pagination-container">
                    <span>共 {totalCount} 条</span>
                    <Pagination
                        className="members-pagination"
                        current={currentPage}
                        total={totalCount}
                        pageSize={pageSize}
                        onChange={handlePageChange}
                        showSizeChanger
                        onShowSizeChange={handlePageSizeChange}
                        pageSizeOptions={['10', '20', '50', '100']}
                    />
                    <div className="members-goto-page">
                        <span>前往</span>
                        <Input
                            className="members-goto-input"
                            value={currentPage}
                            onChange={(e) => setCurrentPage(Number(e.target.value))}
                            onPressEnter={(e) => handlePageChange(Number(e.currentTarget.value))}
                        />
                        <span>页</span>
                    </div>
                </div>
            </div>

            {/*添加用户和编辑用户的弹窗*/}
            <Modal
                className='members-user-modal'
                title={showUserModalConfig?.title}
                open={showUserModal}
                onOk={showUserModalConfig?.onOk}
                onCancel={showUserModalConfig?.onCancel}
                okText="确定"
                cancelText="取消"
                width={375}
                maskClosable={false}
            >
                <Form form={form} layout="horizontal" name="user_form">
                    <Form.Item
                        name="nickName"
                        label="昵称"
                        rules={[{ required: true, message: '请输入昵称!' }, {
                            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_]{2,32}$/,
                            message: '昵称长度必须2-32位',
                        }]}
                        style={{ padding: '0 50px'}}
                        labelCol={{ span: 6 }}
                        wrapperCol={{ span: 18 }}
                        colon={false}
                    >
                        <Input placeholder="请输入" min={2} max={50} style={{ width: 200 }}/>
                    </Form.Item>
                    <Form.Item
                        name="mobile"
                        label="手机号"
                        rules={[
                            { required: true, message: '请输入手机号!' },
                            {
                                pattern: /^[0-9]{8,11}$/,
                                message: '手机号必须是 8 到 11 位的纯数字！',
                            },
                        ]}
                        style={{ padding: '0 50px'}}
                        labelCol={{ span: 6 }}
                        wrapperCol={{ span: 18 }}
                        colon={false}
                    >
                        <Input placeholder="请输入" style={{ width: 200 }} maxLength={11}/>
                    </Form.Item>
                </Form>
            </Modal>

        </PageContainer>
    );
};

export default UserManagement;
