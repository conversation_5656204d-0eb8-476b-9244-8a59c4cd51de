.members-main {
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 12px;
  align-self: stretch;
  background: #FFFFFF;
}

.members-top-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  gap: 24px;

  .members-add-user-button {
    margin-left: auto;
    margin-right: 16px;
  }
}

.members-user-table {
  .ant-table-container {
    border-left: 1px solid #d9d9d9;  /* 设置左边框 */
    border-right: 1px solid #d9d9d9; /* 设置右边框 */
  }

  //表头
  .ant-table-thead > tr > th {
    background: #F2F6FC;
    text-align: left;

    font-weight: normal;
    font-size: 13px;
    line-height: 24px;
    letter-spacing: 0;
    color: #909399;
  }

  //单元格
  .ant-table-tbody > tr > td {
    height: 61.45px;
    padding: 12px 16px;
    gap: 10px;
    background: #FFFFFF;
    border-bottom: 1px solid #DCDFE6; /* 使用边框代替阴影 */
    box-shadow: none;

    font-size: 13px;
    font-weight: normal;
    line-height: 24px;
    letter-spacing: 0;
    color: #606266;
  }

  /* 设置操作列的标题右对齐 */
  .ant-table-thead > tr > th.action-column {
    text-align: right;
  }

  .ant-table-tbody > tr > td.action-column {
    text-align: right;
  }
}

.action-buttons {
  display: inline-flex;
  justify-content: flex-end;
  width: 100%;
}

.action-buttons a {
  font-size: 11px;
}

.members-pagination-container {
  height: 72px;
  padding: 20px 0;
  gap: 16px;
  display: flex;
  align-items: center;
  justify-content: start;

  .members-pagination {
    display: flex;
    align-items: center;
  }

  .members-goto-page {
    display: flex;
    align-items: center;

    .members-goto-input {
      width: 50px;
      margin: 0 8px;
      text-align: center;
    }
  }
}

.members-user-modal {
  .ant-modal-header {
    padding: 10px;
  }

  .ant-modal-content {
    padding: 5px !important;
  }

  .ant-modal-footer {
    display: flex;
    justify-content: end;
    padding: 15px;

    .ant-btn {
      margin: 0 8px;
    }
  }
}
