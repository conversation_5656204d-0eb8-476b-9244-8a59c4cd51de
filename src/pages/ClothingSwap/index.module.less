@import "@/app";

// Toolkit 相关变量定义
@toolkit-work-block-width: 644px;
@toolkit-table-height: 74px;
@toolkit-menu-width: 100px;
@toolkit-output-height: calc(100vh - @navigation-height - @toolkit-table-height);
@toolkit-output-height-fix: calc(100vh - @navigation-height);
@toolkit-output-width: calc(100vw - @toolkit-work-block-width - @toolkit-menu-width);
@toolkit-footer-height: 60px;

.rowContainer {
  width: 100%;
  height: calc(100vh - @navigation-height - @toolkit-footer-height);
  font-family: "PingFang SC", serif;
}

.workBlock {
  padding: 0 8px 0 8px;
  background: linear-gradient(0deg, #FFFFFF, #FFFFFF), #FFFFFF;
  width: @toolkit-work-block-width;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-content: flex-start;
  justify-content: flex-start;
  gap: 8px;
  position: relative;
  z-index: 10;
}

.referenceDrawer {
  :global {
    .ant-drawer-content {
      background-color: #fff;
      border-radius: 0 8px 8px 0;
      box-shadow: 4px 0 12px rgba(0, 0, 0, 0.15);
      border: 1px solid #e8e8e8;
      border-left: none;
      z-index: 5;
    }

    .ant-drawer-body {
      padding: 0;
      height: 100%;
      border: 1px solid #e8e8e8;
    }

    .ant-drawer-content-wrapper {
      box-shadow: none;
      position: relative;
      z-index: 5;
    }
    
    .ant-drawer-mask {
      z-index: 4;
    }
  }
}

.clothingSwapSmudgeModel {
  font-family: PingFang SC, serif;
  .ant-modal-content {
    border-radius: 24px;
    padding: 24px;
  }
  .ant-modal-footer {
    position: relative;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -24px;              // 控制边框起始位置（距离左边的百分比）
      width: calc(100% + 48px); // 控制边框长度（占容器宽度的百分比）
      height: 1px;            // 边框高度
      background-color: #e8e8e8; // 边框颜色
    }
  }
  .showImage {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
    border: 1px dashed #D8D8D8;
    border-radius: 8px;
    &:hover {
      border-color: #4096ff;
    }
  }
  .thumbnailContainer {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    justify-content: space-between;
    .thumbnailList {
      display: flex;
      gap: 8px;
      justify-content: center;
      flex-wrap: wrap;
      max-height: 80px;
      overflow-y: auto;
    }

    .thumbnail {
      width: 60px;
      height: 60px;
      border-radius: 8px;
      border: 2px solid transparent;
      cursor: pointer;
      overflow: hidden;
      transition: border-color 0.3s ease;

      &:hover {
        border-color: #366EF4;
      }

      .thumbnailImage {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .activeThumbnail {
      border-color: #366EF4;
      box-shadow: 0 0 0 1px #366EF4;
    }
  }
}

// 涂抹弹窗样式
.smudgeModal {
  :global(.ant-modal-content) {
    border-radius: 12px;
    padding: 24px;
    min-width: 1280px;
  }

  .smudgeContent {
    display: flex;
    gap: 24px;
    min-height: 600px; // 改为最小高度，支持自适应
  }

  .leftPanel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .mainImageContainer {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
    position: relative;
    min-height: 400px;
    max-height: 720px; // 设置最大高度

    :global(.ant-image) {
      max-width: 100%;
      max-height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    :global(.ant-image-img) {
      max-width: calc(100% - 16px); // 留出一些边距
      max-height: calc(100% - 16px); // 留出一些边距
      object-fit: contain;
      width: auto;
      height: auto;
    }
  }

  .navigationContainer {
    display: flex;
    justify-content: center;
    gap: 16px;

    .navButton {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 1px solid #d9d9d9;
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:hover {
        border-color: #366EF4;
        color: #366EF4;
      }
    }
  }

  .thumbnailContainer {
    .thumbnailList {
      display: flex;
      gap: 8px;
      justify-content: center;
      flex-wrap: wrap;
      max-height: 80px;
      overflow-y: auto;
    }

    .thumbnail {
      width: 60px;
      height: 60px;
      border-radius: 6px;
      border: 2px solid transparent;
      cursor: pointer;
      overflow: hidden;
      transition: border-color 0.3s ease;

      &:hover {
        border-color: #366EF4;
      }

      .thumbnailImage {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .activeThumbnail {
      border-color: #366EF4;
      box-shadow: 0 0 0 1px #366EF4;
    }
  }

  .rightPanel {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .rightContent {
    flex: 1;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
    padding: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;

    &::before {
      content: '内容待定';
    }
  }
}

.clothingSwapDetailTag {
  height: 28px;
  line-height: 20px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 4px;
  padding: 4px 8px;
  position: absolute;
  top: 16px;
  left: 16px;
  color: #fff;
  text-align: center;
}

.selectReferenceImage {
  // 标题样式
  .referenceTitle {
    display: flex;
    justify-content: space-between;
  }

  // 分段控件样式
  .referenceSegmented {
    background-color: #E5E7EE;
    width: fit-content;
  }

  // 参考图容器
  .referenceContainer {
    width: 100%;
    min-height: 168px;
    max-height: 400px; // 添加最大高度限制
    border-radius: 8px;
    background-color: #EFF0F5;
    border: 1px dashed #D8D8D8;
    overflow: hidden;

    .referenceImage {
      width: 108px;
      height: 136px;
    }

    .roundCheckbox {
      .ant-checkbox-inner {
        border-radius: 50% !important;
        width: 18px;
        height: 18px;
        background-color: rgba(255, 255, 255, 0.8);
        border: 1px solid #D9D9D9;
      }

      .ant-checkbox-checked .ant-checkbox-inner {
        background-color: #366EF4;
        border-color: #366EF4;
      }

      .ant-checkbox-inner::after {
        top: 45%;
      }
    }

    .referenceResultBox {
      width: 100%;
      min-height: 160px;
      border: 1px solid #E1E3EB;
      border-radius: 8px;
      padding: 16px;

      .thumbnail-container {
        position: relative;
        width: 100px;
        height: 100px;

        .thumbnail-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 4px;
        }

        .checkbox-style {
          position: absolute;
          top: 8px;
          right: 8px;
        }
      }

      .add-button {
        width: 100px;
        height: 100px;
        border: 1px dashed #CCCCCC;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        .add-icon {
          transform: rotate(90deg);
          font-size: 24px;
          color: #999;
          margin-bottom: 8px;
        }

        .add-text {
          color: #666;
        }

        &:hover {
          border-color: #366EF4;

          .add-icon, .add-text {
            color: #366EF4;
          }
        }
      }
    }

  }

  // 系统生成参考图样式
  .systemSelectContainer {
    height: 100%;
    cursor: pointer;
    padding: 16px;
  }

  .systemImage {
    height: 100%;
  }

  .systemTextContainer {
    height: 100%;
    margin-left: 24px;
  }

  .systemArrow {
    cursor: pointer;
    font-size: 24px;
    color: #727375;
  }

  // 本地上传样式
  .uploadButton {
    width: 132px;
    height: 38px;
    border: 1px solid #B5C7FF;
  }

  .uploadIcon {
    font-size: 16px;
    color: #366EF4;
  }

  .uploadHint {
    margin-top: 8px;
  }

  // 参考图结果容器
  .resultBox {
    padding: 8px;
    position: relative;
    min-height: 168px;
    max-height: 400px; // 添加最大高度限制
    overflow-y: auto; // 添加垂直滚动
    overflow-x: hidden; // 隐藏水平滚动

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }

    // 确保Flex容器能够正确换行
    .ant-flex {
      max-width: 100%;

      // 优化图片网格布局
      &[data-flex="wrap"] {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        align-content: flex-start; // 内容从顶部开始对齐
      }
    }
  }

  // 参考图缩略图
  .thumbnailContainer {
    position: relative;
    width: 66px;
    height: 66px;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid #1890ff;
    flex-shrink: 0; // 防止图片被压缩

    // 悬停效果
    &:hover {
      border-color: #40a9ff;
      transform: scale(1.02);
      transition: all 0.2s ease;
    }
  }

  .thumbnailImage {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .checkboxStyle {
    position: absolute;
    top: 4px;
    right: 4px;
  }

  // 添加按钮样式
  .addButton {
    width: 66px;
    height: 66px;
    border-radius: 8px;
    background-color: #fff;
    border: 2px dashed #d9d9d9;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    flex-shrink: 0; // 防止按钮被压缩
    transition: all 0.2s ease;

    &:hover {
      border-color: #40a9ff;
      background-color: #f6f8ff;

      .addIcon {
        color: #40a9ff;
      }

      .addText {
        color: #40a9ff;
      }
    }
  }

  .addIcon {
    font-size: 20px;
    color: #727375;
  }

  .addText {
    margin-top: 4px;
    font-size: 12px;
    color: #727375;
  }

  // 底部操作栏
  .bottomBar {
    position: sticky; // 改为sticky定位，在滚动时保持在底部可见
    bottom: 0;
    right: 0;
    background: linear-gradient(to top, rgba(239, 240, 245, 0.95) 0%, rgba(239, 240, 245, 0.8) 50%, transparent 100%);
    padding: 8px 8px 4px 8px;
    margin: 0 -8px -8px -8px; // 抵消父容器的padding
    backdrop-filter: blur(2px); // 添加模糊效果
    z-index: 10;
  }

  .selectCount {
    color: #366EF4;
  }

  .deleteButton {
    color: #366EF4;
    cursor: pointer;
  }

  // 空状态
  .emptyState {
    width: 100%;
    padding: 40px 0;
  }

  .emptyIcon {
    font-size: 24px;
    color: #4080FF;
  }

  .emptyText {
    color: #4080FF;
    margin-top: 8px;
  }
}

.numberInputContainer {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 64px;
  background: #F5F6F9;
  border-radius: 8px;
  padding: 12px;
  .ant-input-number {
    background: #FFFFFF;
    width: 150px;
  }
}

.outPutBlock {
  position: absolute;
  right: 0;
  width: @toolkit-output-width;
  height: @toolkit-output-height-fix;
  z-index: 1; // 设置较低的 z-index，确保它在 workBlock 下方
  
  .task-bar {
    height: @toolkit-output-height-fix;
  }
  .task-image-bar {
    height: @toolkit-output-height-fix;
    .image-container {
      height: 100%;
    }
  }
}