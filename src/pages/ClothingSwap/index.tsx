import React, { useCallback, useEffect, useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import styles from '@/pages/ClothingSwap/index.module.less'
import {
  Button, Checkbox,
  Flex,
  InputNumber,
  message,
  Modal,
  Popover,
  Tooltip,
  Image, Popconfirm, Segmented, Upload, UploadProps, Input, Carousel, Card,
} from 'antd';
import { predict, PredictVO, updatePoint } from '@/services/PointController';
import { clothingSwap } from '@/services/CreativeController';
import TaskOutputBlock, {
  TaskOutputBlockRef,
} from '@/components/Creative/TaskOutputBlock';
import TopupModal from '@/pages/Topup/Topup';
import '@/pages/toolkit/RepairDetail/index.less';
import ImageUploader, {
  ImageUploaderButton,
} from '@/components/Common/ImageUploader';
import ClothImageUploader from '@/components/Common/ClothImageUploader';
import SmudgeCanvas from '@/components/imageOperate/smudgeCanvas/SmudgeCanvasCopy';
import IconFont from '@/components/IconFont';
import { getMaskUrl, getMaskUrlFromLines, hasValidSmudgeContent, convertToJPG } from '@/utils/imageUtils';
import { InboxOutlined, LeftOutlined, LoadingOutlined, QuestionCircleOutlined, RightOutlined } from '@ant-design/icons';
import { useLocation, useNavigate } from 'react-router-dom';
import { SmudgeToolkit, useSmudgeInteraction } from '@/components/imageOperate/smudgeCanvas/SmudgeBrushTool';
import { sleep } from '@/utils/utils';
import useSmudgeToolkit from '@/hooks/useSmudgeToolkit';
import ClothingSwapExampleContent from '@/components/Guide/ClothingSwapExampleContent';
import { StaticImage } from '@/assets/images/oss/StaticImage';
import { UPLOAD_URL } from '@/constants';
import StyleLoraDrawer, { SelectedItem } from '@/components/BasicReplace/StyleLoraDrawer';
import { ElementConfig, getBasicChangeConfig, queryHasShowImageChildren } from '@/services/ElementController';
import { isVipUser } from '@/services/UserController';
import FavoriteTemplateModal from '@/components/Favorite/FavoriteTemplateModal';
import { create as createFixedCreativeTemplate } from '@/services/FixedCreativeTemplateController';
import PredictBlock from '@/components/Creative/PredictBlock';
const { Dragger } = Upload;

type ReferenceType = '系统生成参考图' | '本地上传参考图' | '我收藏的模板';
const MAX_IMAGE_NUM = 8;

const ClothingSwap = () => {
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const type: string = 'CUSTOM';
  // 页面跳转相关
  const location = useLocation();
  const navigate = useNavigate();
  // output 相关
  const outputBlockRef = useRef(null);
  const taskOutputRef = useRef<TaskOutputBlockRef>(null);
  const [sliderValue, setSliderValue] = useState(12);
  // 充值相关
  const [showTopupModal, setShowTopupModal] = useState(false);
  const [predictVO, setPredictVO] = useState<PredictVO | null>(null);
  // 涂抹弹窗相关
  const [canSmudge, setCanSmudge] = useState(false);
  const [showSmudgeModal, setShowSmudgeModal] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  // 参考图涂抹相关参数

  // 服装图涂抹相关参数
  const clothSmudgeToolkit = useSmudgeToolkit();
  const clothStageRef = useRef(null);
  // 图片上传中添加一个蒙版
  const [isPreloading, setIsPreloading] = useState(false);
  // 添加淡入淡出效果的状态
  const [fadeIn, setFadeIn] = useState(false);

  // 上传参考图相关参数
  const [referencePicture, setReferencePicture] = useState<{
    imageUrl: string,// 图片地址
    isSelected: boolean,// 是否选中
    referenceConfig?: string | any,// 参考图配置
    backTags?: string,// 背景标签
    backExtTags?: string,// 背景扩展标签
    loraId?: number,// 场景ID，用作loraId
    loraPath?: string,// lora路径，从extInfo.loraPath获取
    originalImageUrl?: string,// 原始图片地址
    id: string,// 唯一标识符，用于React key和ref管理
  }[]>([]);
  
  // 手动创建 MAX_IMAGE_NUM 个独立的涂抹工具实例
  const referenceToolkit0 = useSmudgeToolkit();
  const referenceToolkit1 = useSmudgeToolkit();
  const referenceToolkit2 = useSmudgeToolkit();
  const referenceToolkit3 = useSmudgeToolkit();
  const referenceToolkit4 = useSmudgeToolkit();
  const referenceToolkit5 = useSmudgeToolkit();
  const referenceToolkit6 = useSmudgeToolkit();
  const referenceToolkit7 = useSmudgeToolkit();

  // 手动创建 MAX_IMAGE_NUM 个独立的 stage 引用
  const referenceStageRef0 = useRef(null);
  const referenceStageRef1 = useRef(null);
  const referenceStageRef2 = useRef(null);
  const referenceStageRef3 = useRef(null);
  const referenceStageRef4 = useRef(null);
  const referenceStageRef5 = useRef(null);
  const referenceStageRef6 = useRef(null);
  const referenceStageRef7 = useRef(null);

  // 将工具实例放入数组，方便根据索引访问
  const referenceToolkits = [
    referenceToolkit0, referenceToolkit1, referenceToolkit2, referenceToolkit3,
    referenceToolkit4, referenceToolkit5, referenceToolkit6, referenceToolkit7
  ];

  const referenceStageRefs = [
    referenceStageRef0, referenceStageRef1, referenceStageRef2, referenceStageRef3,
    referenceStageRef4, referenceStageRef5, referenceStageRef6, referenceStageRef7
  ];

  // 获取当前活动的工具实例
  const getCurrentToolkit = () => {
    const index = Math.min(currentImageIndex, MAX_IMAGE_NUM - 1);
    return referenceToolkits[index];
  };

  const getCurrentStageRef = () => {
    const index = Math.min(currentImageIndex, MAX_IMAGE_NUM - 1);
    return referenceStageRefs[index];
  };
  // 服装图片
  const [clothImage, setClothImage] = useState<string>('');
  const [showCollectionInfoModal, setShowCollectionInfoModal] = useState(false);
  const [currentReferenceType, setCurrentReferenceType] = useState<ReferenceType>('系统生成参考图');
  const [showFavoriteModal, setShowFavoriteModal] = useState(false);
  const [collectionTemplateName, setCollectionTemplateName] = useState('');
  const [isOpenDrawer, setIsOpenDrawer] = useState(false);
  const [isShowingWarning, setIsShowingWarning] = useState(false);
  const warningTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [vipUser, setVipUser] = useState(false);
  const currentType = 'CLOTHING_SWAP';
  const [preloadedSceneModels, setPreloadedSceneModels] = useState<any[]>([]);
  const [styleDescription, setStyleDescription] = useState<string>('');
  const [originalSceneModels, setOriginalSceneModels] = useState<any[]>([]);
  const [sceneConfig, setSceneConfig] = useState<ElementConfig | null>(null);

  // 提交相关参数
  const [canSubmit, setCanSubmit] = useState(false);
  const [commiting, setCommiting] = useState(false);

  useEffect(() => {
    init();
    
    // 清理函数，组件卸载时清理定时器
    return () => {
      if (warningTimeoutRef.current) {
        clearTimeout(warningTimeoutRef.current);
        warningTimeoutRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    handlePredict();
    if (referencePicture.length > 0) {
      setCanSmudge( true);
    }
  }, [referencePicture.length])

  // 监听是否可以提交
  useEffect(() => {
    setCanSubmit(validCreateProps());
  }, [referencePicture, clothImage, referenceToolkit0, referenceToolkit1, referenceToolkit2, referenceToolkit3, referenceToolkit4, referenceToolkit5, referenceToolkit6, referenceToolkit7])

  const init = async() => {
    updatePoint();
    isVipUser().then(res => setVipUser(!!res));
    // 在组件初始化时加载默认场景数据
    loadDefaultSceneData();
  }

  // 处理预加载场景模型数据更新
  const handlePreloadedSceneModelsUpdate = useCallback((newModels: any[]) => {
    setPreloadedSceneModels(newModels);
    setOriginalSceneModels(newModels);
  }, []);

  const handlePredict = () => {
    if (referencePicture.length < 1) {
      return;
    }
    predict('CLOTHING_SWAP', referencePicture.length, null).then((result) => {
      if (result) {
        setPredictVO(result);
      }
    });
  };

  /**
   * 验证上传图片数量 - 严格限制数量在 MAX_IMAGE_NUM 之内
   */
  const validateImageCount = (fileList) => {
    const currentImageCount = referencePicture.length;
    const newFileCount = fileList.length;
    const totalImagesAfterUpload = currentImageCount + newFileCount;

    // 如果上传后的总图片数不超过限制，则允许上传
    if (totalImagesAfterUpload <= MAX_IMAGE_NUM) {
      return { isValid: true, allowedFiles: fileList, needWarning: false };
    }

    // 计算可以上传的文件数量
    const allowedCount = MAX_IMAGE_NUM - currentImageCount;
    
    if (allowedCount <= 0) {
      // 防止重复显示警告
      if (!isShowingWarning) {
        setIsShowingWarning(true);
        message.error(`已达到最大数量限制(${MAX_IMAGE_NUM}张)，无法继续上传`);
        
        // 清除之前的定时器
        if (warningTimeoutRef.current) {
          clearTimeout(warningTimeoutRef.current);
        }
        
        // 重置警告状态
        warningTimeoutRef.current = setTimeout(() => {
          setIsShowingWarning(false);
          warningTimeoutRef.current = null;
        }, 1500);
      }
      return { isValid: false, allowedFiles: [], needWarning: false };
    }

    // 只允许上传部分文件，在这里显示警告
    const allowedFiles = fileList.slice(0, allowedCount);
    const rejectedCount = fileList.length - allowedCount;
    
    // 防止重复显示警告
    if (!isShowingWarning) {
      setIsShowingWarning(true);
      message.warning(`最多只能选择${MAX_IMAGE_NUM}张参考图片，已为您选择前${allowedCount}张图片，${rejectedCount}张图片被忽略`);
      
      // 清除之前的定时器
      if (warningTimeoutRef.current) {
        clearTimeout(warningTimeoutRef.current);
      }
      
      // 重置警告状态
      warningTimeoutRef.current = setTimeout(() => {
        setIsShowingWarning(false);
        warningTimeoutRef.current = null;
      }, 1500);
    }

    return { isValid: true, allowedFiles, needWarning: false };
  };

  /**
   * 多图上传配置 用于上传多张参考图片
   */
  const multiImageUploadProps: UploadProps = {
    accept: 'image/png, image/jpeg',
    showUploadList: false,
    multiple: true,
    name: 'file',
    beforeUpload: (file, fileList) => {
      // 验证总数量是否超过限制
      const validation = validateImageCount(fileList);
      if (!validation.isValid) {
        return false;
      }

      // 获取允许上传的文件列表
      const allowedFiles = validation.allowedFiles;
      
      // 获取当前文件在允许的文件列表中的索引
      const fileIndex = allowedFiles.findIndex(item => item.uid === file.uid);
      
      // 如果当前文件不在允许的文件列表中，则阻止上传
      if (fileIndex === -1) {
        return false;
      }
      
      // 判断当前文件是否是允许文件列表中的最后一个文件
      const isLastFile = fileIndex === allowedFiles.length - 1;

      // 只有在最后一个文件时执行上传逻辑
      if (isLastFile) {
        // 批量处理允许的文件
        const uploadPromises = allowedFiles.map(fileItem => {
          return new Promise(async (resolve, reject) => {
            try {
              // 在上传前先转换为 JPG 格式
              const convertedFile = await convertToJPG(fileItem);
              
              const formData = new FormData();
              formData.append('file', convertedFile);

              fetch(UPLOAD_URL, {
                method: 'POST',
                body: formData,
              })
                .then(response => response.json())
                .then(response => {
                  if (response.success) {
                    resolve({
                      uid: fileItem.uid,
                      status: 'done',
                      response: response,
                    });
                  } else {
                    reject({
                      uid: fileItem.uid,
                      status: 'error',
                      name: fileItem.name,
                    });
                  }
                })
                .catch(error => {
                  reject({
                    uid: fileItem.uid,
                    status: 'error',
                    name: fileItem.name,
                  });
                });
            } catch (conversionError) {
              console.error('图片格式转换失败:', conversionError);
              reject({
                uid: fileItem.uid,
                status: 'error',
                name: fileItem.name,
              });
            }
          });
        });

        // 等待所有上传完成
        Promise.allSettled(uploadPromises)
          .then(results => {
            // 收集成功的上传结果
            const successFiles = results
              .filter((result): result is PromiseFulfilledResult<any> => result.status === 'fulfilled')
              .map(result => result.value);

            // 收集失败的上传结果
            const failedFiles = results
              .filter((result): result is PromiseRejectedResult => result.status === 'rejected')
              .map(result => result.reason);

            // 处理成功上传的文件
            if (successFiles.length > 0) {
              handleUploadSuccess(successFiles);
            }

            // 处理失败的文件
            failedFiles.forEach(file => {
              handleUploadError(file.name);
            });
          });
      }

      // 阻止默认上传行为
      return false;
    },
    // 设置maxCount限制，严格控制上传数量
    maxCount: MAX_IMAGE_NUM,
  };


  // 处理上传成功的文件
  const handleUploadSuccess = (fileList) => {
    // 使用Set去重，避免重复处理同一图片
    const uniqueUrls = new Set();

    // 获取所有上传成功的文件
    const successFiles = fileList
      .filter(f => f.status === 'done' && f.response && f.response.success)
      .filter(f => {
        // 过滤掉重复URL
        if (uniqueUrls.has(f.response.data)) {
          return false;
        }
        uniqueUrls.add(f.response.data);
        return true;
      })
      .map(f => ({
        imageUrl: f.response.data,
        isSelected: true,
        loraPath: '', // 本地上传的图片没有loraPath
        originalImageUrl: '', // 本地上传的图片没有originalImageUrl
        id: `upload_${Date.now()}_${Math.random()}`, // 生成唯一ID
      }));

    // 更新参考图列表（简化逻辑，避免重复警告）
    if (successFiles.length > 0) {
      setReferencePicture(pre => {
        // 过滤掉已存在的URL
        const newFiles = successFiles.filter(file =>
          !pre.some(existing => existing.imageUrl === file.imageUrl),
        );
        
        // 计算可以添加的文件数量
        const currentCount = pre.length;
        const availableSlots = MAX_IMAGE_NUM - currentCount;
        
        if (availableSlots <= 0) {
          // 不显示警告，因为在validateImageCount中已经处理了
          return pre;
        }
        
        // 如果新文件数量超过可用数量，只取前面部分
        const filesToAdd = newFiles.slice(0, availableSlots);
        
        // 只显示成功消息，不显示警告（警告已在前面处理）
        if (filesToAdd.length > 0) {
          message.success(`成功添加${filesToAdd.length}张参考图片`);
        }
        
        return [...pre, ...filesToAdd];
      });
    }
  };

  // 处理上传失败
  const handleUploadError = (fileName) => {
    message.error(`图片 ${fileName} 上传失败，请重试`);
  };

  const handleModalCancel = () => {
    setShowSmudgeModal(false);
    setCurrentImageIndex(0);
    handleClothImageChange('');
    clothSmudgeToolkit.handleLinesChange('');
    referenceToolkits.forEach(item => {
      item.handleLinesChange([]);
      item.handleSizeChange(0, 0);
    })
  };

  const loadDefaultSceneData = useCallback(async () => {
    try {
      // 如果已经有预加载数据，则不重复加载
      if (preloadedSceneModels.length > 0) {
        return;
      }

      // 获取基础换衣配置
      const basicChangeConfig = await getBasicChangeConfig();
      if (!basicChangeConfig || basicChangeConfig.length === 0) {
        console.warn('未获取到基础换衣配置');
        return;
      }

      // 查找场景配置
      const sceneConfigItem = basicChangeConfig.find(config => config.configKey === 'SCENE');
      if (!sceneConfigItem || !sceneConfigItem.children) {
        console.warn('未找到场景配置');
        return;
      }

      // 保存场景配置
      setSceneConfig(sceneConfigItem);

      // 获取所有场景的ID列表
      const sceneIds = sceneConfigItem.children.map(child => child.id);

      // 批量获取所有场景的详细数据
      const elementsDataMap = await queryHasShowImageChildren(sceneIds);

      // 合并场景配置数据和查询到的详细数据，确保不遗漏任何场景
      const allSceneIds = new Set([
        ...sceneIds,
        ...(elementsDataMap ? Object.keys(elementsDataMap).map(id => Number(id)) : [])
      ]);

      const defaultSceneModels = Array.from(allSceneIds).map(sceneId => {
        // 从场景配置中查找基础信息
        const configChild = sceneConfigItem.children.find(child => child.id === sceneId);
        // 从批量查询结果中获取详细数据
        const elementData = elementsDataMap ? elementsDataMap[sceneId] : null;

        // 如果既没有配置信息也没有详细数据，则跳过
        if (!configChild && !elementData) {
          return null;
        }

        const detailedChildren = elementData?.children || [];

        // 判断是否为专属场景（为数字或字符串类型的数字）
        const isExclusiveScene = elementData?.extInfo?.openScope &&
          (typeof elementData.extInfo.openScope === 'number' ||
            (typeof elementData.extInfo.openScope === 'string' && !isNaN(Number(elementData.extInfo.openScope))));

        // 将子图片数据转换为标准格式
        const childItems = Array.isArray(detailedChildren) ?
          detailedChildren
            .filter((elementItem): elementItem is {
                id: number;
                name: string;
                extInfo: { showImage: string, styleImage: string },
                tags: string,
                extTags: string
              } =>
                typeof elementItem?.extInfo?.showImage === 'string' || typeof elementItem?.extInfo?.styleImage === 'string'
            )
            .map((elementItem) => {
              if (isExclusiveScene) {
                return {
                  id: elementItem.id,
                  name: elementItem.name,
                  showImage: elementItem.extInfo.styleImage,
                  extInfo: elementItem.extInfo,
                  tags: elementItem.tags || '',
                  extTags: elementItem.extTags || ''
                };
              } else {
                // 非专属场景：按照原逻辑赋值
                return {
                  id: elementItem.id,
                  name: elementItem.name,
                  showImage: elementItem.extInfo.showImage,
                  extInfo: elementItem.extInfo,
                  tags: elementItem.tags || '',
                  extTags: elementItem.extTags || ''
                };
              }
            }) : [];

        return {
          id: sceneId,
          name: configChild?.name || elementData?.name || `场景${sceneId}`,
          showImage: configChild?.showImage || elementData?.showImage || '',
          children: childItems,
          tags: (configChild as any)?.tags || elementData?.tags || '',
          extTags: (configChild as any)?.extTags || elementData?.extTags || '',
          isExclusiveScene: isExclusiveScene
        };
      }).filter((scene): scene is NonNullable<typeof scene> => scene !== null); // 过滤掉null值并进行类型断言


      // 只保留有详细数据的场景
      const filteredSceneModels = defaultSceneModels.filter(scene => {
        // 检查是否有接口返回的详细数据
        return elementsDataMap && elementsDataMap[scene.id];
      });

      // 设置场景数据（可以选择使用过滤后的数据）
      setPreloadedSceneModels(filteredSceneModels);
      setOriginalSceneModels(filteredSceneModels);
    } catch (error) {
      console.error('加载默认场景数据失败:', error);
    }
  }, [preloadedSceneModels.length]);


  // 获取所有参考图的涂抹结果
  const getAllSmudgeResults = async () => {
    const results: Array<{
      imageId: string;
      originalImageUrl: string;
      maskUrl: string;
      lines: any[];
      hasSmudge: boolean;
      loraId?: number;
      loraPath?: string;
      referenceConfig?: any;
      backTags?: string;
      backExtTags?: string;
    }> = [];
    
    for (let index = 0; index < referencePicture.length; index++) {
      const image = referencePicture[index];
      const toolkit = referenceToolkits[index];
      
      if (!toolkit || !toolkit.editorState) {
        continue;
      }
      
      const lines = toolkit.editorState.lines;
      const size = toolkit.editorState.size;
      const hasSmudge = hasValidSmudgeContent(lines);
      
      try {
        let maskUrl = '';
        if (hasSmudge) {
          // 使用离屏 canvas 方法获取涂抹结果
          maskUrl = await getMaskUrlFromLines(lines, size.width, size.height, false);
        }
        
        results.push({
          imageId: image.id,
          originalImageUrl: image.imageUrl,
          maskUrl: maskUrl,
          lines: lines,
          hasSmudge: hasSmudge,
          loraId: image.loraId,
          loraPath: image.loraPath,
          referenceConfig: image.referenceConfig,
          backTags: image.backTags,
          backExtTags: image.backExtTags
        });
      } catch (error) {
        console.error(`获取参考图${index}涂抹结果失败:`, error);
        results.push({
          imageId: image.id,
          originalImageUrl: image.imageUrl,
          maskUrl: '',
          lines: lines,
          hasSmudge: hasSmudge,
          loraId: image.loraId,
          loraPath: image.loraPath,
          referenceConfig: image.referenceConfig,
          backTags: image.backTags,
          backExtTags: image.backExtTags
        });
      }
    }
    
    return results;
  };

  // 获取单个参考图的涂抹结果
  const getSingleSmudgeResult = async (imageIndex: number) => {
    if (imageIndex >= referencePicture.length || imageIndex >= referenceToolkits.length) {
      return null;
    }
    
    const image = referencePicture[imageIndex];
    const toolkit = referenceToolkits[imageIndex];
    
    if (!toolkit || !toolkit.editorState) {
      return null;
    }
    
    const lines = toolkit.editorState.lines;
    const hasSmudge = hasValidSmudgeContent(lines);
    
    try {
      let maskUrl = '';
      if (hasSmudge) {
        maskUrl = await getMaskUrlFromLines(lines, 560, 560, false);
      }
      
      return {
        imageId: image.id,
        originalImageUrl: image.imageUrl,
        maskUrl: maskUrl,
        lines: lines,
        hasSmudge: hasSmudge,
        loraId: image.loraId,
        loraPath: image.loraPath,
        referenceConfig: image.referenceConfig,
        backTags: image.backTags,
        backExtTags: image.backExtTags
      };
    } catch (error) {
      console.error(`获取参考图${imageIndex}涂抹结果失败:`, error);
      return null;
    }
  };

  // 获取服装图的涂抹结果
  const getClothSmudgeResult = async () => {
    if (!clothImage) {
      return null;
    }
    
    const lines = clothSmudgeToolkit.editorState.lines;
    const hasSmudge = hasValidSmudgeContent(lines);
    
    try {
      let maskUrl = '';
      if (hasSmudge) {
        maskUrl = await getMaskUrl(clothStageRef);
      }
      
      return {
        originalImageUrl: clothImage,
        maskUrl: maskUrl,
        lines: lines,
        hasSmudge: hasSmudge
      };
    } catch (error) {
      console.error('获取服装图涂抹结果失败:', error);
      return null;
    }
  };

  // 提交创作任务
  const handleCreate = async () => {
    if (predictVO && predictVO.needTopup) {
      setShowTopupModal(true);
      return;
    }

    if (commiting) {
      return;
    }

    try {
      setCommiting(true);
      
      // 获取所有参考图的涂抹结果
      // console.log('开始获取涂抹结果...');
      const allSmudgeResults = await getAllSmudgeResults();
      // console.log('所有参考图涂抹结果:', allSmudgeResults);
      
      // 获取服装图的涂抹结果
      const clothSmudgeResult = await getClothSmudgeResult();
      // console.log('服装图涂抹结果:', clothSmudgeResult);
      
      // 过滤出有涂抹内容的参考图
      const smudgedResults = allSmudgeResults.filter(result => result.hasSmudge);
      // console.log('有涂抹内容的参考图:', smudgedResults);
      
      // 检查是否有涂抹内容
      if (smudgedResults.length === 0 && (!clothSmudgeResult || !clothSmudgeResult.hasSmudge)) {
        message.warning('必须涂抹每一张图片');
        return;
      }
      
      // 构建API请求参数
      const requestData = {
        referenceImages: allSmudgeResults.map(result => ({
          imageUrl: result.originalImageUrl,
          maskUrl: result.maskUrl, // 涂抹蒙版URL
        })),
        imageNum: referencePicture.length,
        clothImage: clothImage,
        clothMaskUrl: clothSmudgeResult?.maskUrl || '', // 服装图涂抹蒙版URL
        // 其他参数...
      };
      
      console.log('准备提交的数据:', requestData);
      
      const result = await clothingSwap(requestData);
      if (!result) {
        return;
      }
      message.success('创作任务提交成功！');
      handleModalCancel();
      setIsOpenDrawer(false);
      taskOutputRef.current?.refresh();
    } catch (error) {
      console.error('获取涂抹结果失败:', error);
      message.error('获取涂抹结果失败，请重试');
    } finally {
      setCommiting(false);
    }
  };

  const validCreateProps = (): boolean => {
    let res = true;
    if (referencePicture.length <= 0) {
      // console.log('[提交参数检查]参考图为空');
      res = false;
    }
    for (let i = 0; i < referencePicture.length; i++) {
      if (referenceToolkits[i].editorState.lines.length <= 0) {
        // console.log('[提交参数检查]存在未涂抹的图片, index: ', i);
        res = false;
        break;
      }
    }
    if (!clothImage || clothImage === '') {
      // console.log('[提交参数检查]服装图片为空');
      res = false;
    }
    if (clothSmudgeToolkit.editorState.lines.length <= 0) {
      // console.log('[提交参数检查]服装图片未涂抹');
      res = false;
    }
    // console.log('[提交参数检查]结果: ', res);
    return res;
  }

  // 选择参考图组件
  const SelectReferenceImage = ({ tag }) => {
    // 添加显示数量控制，用于性能优化
    const [displayCount, setDisplayCount] = useState(50); // 初始显示50张图片

    // 获取要显示的图片列表（性能优化）
    const displayedPictures = referencePicture.slice(0, displayCount);
    const hasMorePictures = referencePicture.length > displayCount;

    // 加载更多图片
    const loadMorePictures = () => {
      setDisplayCount(prev => Math.min(prev + 50, referencePicture.length));
    };



    // 空状态展示组件
    const EmptyStateView = () => (
      <Flex vertical align="center" justify="center" className={styles.emptyState}>
        <InboxOutlined className={styles.emptyIcon} />
        <div className={`text14 ${styles.emptyText}`}>未选择</div>
      </Flex>
    );

    // 参考图缩略图组件
    const ReferenceThumbnail = ({ image, index }) => (
      <div key={index} className={styles.thumbnailContainer}>
        <Image
          src={image.imageUrl}
          alt={`参考图${index + 1}`}
          className={styles.thumbnailImage}
          preview={true}
        />
        <Checkbox
          checked={image.isSelected}
          className={`${styles.roundCheckbox} ${styles.checkboxStyle}`}
          onChange={(e) => {
            // 如果取消选中，则直接删除该图片
            if (!e.target.checked) {
              setReferencePicture(pre => pre.filter((_, idx) => idx !== index));
            }
          }}
        />
      </div>
    );

    // 添加按钮组件
    const AddButton = ({ onClick, children = null }) => (
      <div
        className={styles.addButton}
        onClick={onClick}
      >
        <RightOutlined className={styles.addIcon} />
        <span className={styles.addText}>添加</span>
        {children}
      </div>
    );

    // 本地上传按钮
    const UploadButton = () => (
      <Button
        className={styles.uploadButton}
        icon={<IconFont type={'icon-shangchuan'} className={styles.uploadIcon} />}
      >
        <div className={'color-36 weight'}>本地上传</div>
      </Button>
    );

    // 底部操作栏组件
    const BottomBar = () => (
      <Flex
        justify="flex-end"
        align="center"
        gap={16}
        className={styles.bottomBar}
      >
        <div className={`text14 ${styles.selectCount}`}>
          已选 {referencePicture.length} 张
        </div>
        <Popconfirm
          title="确认删除"
          description="确定要删除所有已选择的参考图吗？"
          onConfirm={() => {
            setReferencePicture([]);
          }}
          okText="确认"
          cancelText="取消"
        >
          <div
            className="text14"
            style={{ color: 'red', cursor: 'pointer' }}
          >
            全部删除
          </div>
        </Popconfirm>
        {/* 添加加入收藏按钮，并设置合理颜色 */}
        <Popconfirm
          title="加入收藏"
          description="确定要将当前选择的参考图加入收藏吗？"
          onConfirm={() => {
            // 打开完善收藏信息弹窗
            setShowCollectionInfoModal(true);
          }}
          okText="确认"
          cancelText="取消"
        >
          <div
            className={`text14 ${styles.deleteButton} color-36`}
            style={{ cursor: 'pointer' }}
          >
            加入收藏
          </div>
        </Popconfirm>
      </Flex>
    );
    return (
      <Flex vertical gap={8} justify={'flex-start'} className={`work-item-container ${styles.selectReferenceImage}`}>
        {/* 一级标题 */}
        <div className={`text16 font-pf color-n weight ${styles.referenceTitle}`}>
          <span>{tag}</span>
        </div>

        {/* 参考图上传方式 */}
        <Segmented
          value={currentReferenceType}
          className={styles.referenceSegmented}
          options={['系统生成参考图', '本地上传参考图', '我收藏的模板']}
          onChange={(value) => {
            if (value === '我收藏的模板') {
              // 点击我收藏的模板时打开弹窗，不切换标签
              setShowFavoriteModal(true);
            } else {
              // 设置当前参考图类型
              setCurrentReferenceType(value as ReferenceType);
              // 切换时清空参考图集合
              setReferencePicture([]);
              // 切换时关闭抽屉
              setIsOpenDrawer(false);
            }
          }}
        />

        {/* 参考图容器 */}
        <div className={styles.referenceContainer}>
          {/* 系统生成参考图 - 空状态 */}
          {currentReferenceType === '系统生成参考图' && referencePicture.length === 0 && !isOpenDrawer && (
            <Flex
              justify="space-between"
              align="center"
              className={styles.systemSelectContainer}
              onClick={() => setIsOpenDrawer(true)}
            >
              <Flex justify="flex-start" align="center" className={styles.systemImage}>
                <img src={StaticImage.basicReplace.referencePic} className={styles.referenceImage} alt="系统生成参考图" />
                <Flex vertical justify="center" align="flex-start" className={styles.systemTextContainer}>
                  <div className="text16" style={{ marginBottom: '8px' }}>选择参考图</div>
                  <div className="text14 color-96">可选择多张参考图</div>
                </Flex>
              </Flex>
              {/* 右侧图标 */}
              <span className={styles.systemArrow}><RightOutlined /></span>
            </Flex>
          )}

          {/* 本地上传 - 空状态 */}
          {currentReferenceType === '本地上传参考图' && referencePicture.length === 0 && (
            <Dragger
              style={{ height: '100%', cursor: 'pointer' }}
              {...multiImageUploadProps}
            >
              <UploadButton />
              <div className={`text14 color-96 ${styles.uploadHint}`}>
                点击/粘贴/拖拽图片至此，支持png、jpg格式
              </div>
            </Dragger>
          )}

          {/* 展示已选择的参考图或系统生成的参考图 */}
          {(referencePicture.length !== 0 || isOpenDrawer) && (
            <div className={`${styles.referenceResultBox} ${styles.resultBox}`}>
              <Flex wrap="wrap" gap={12} align="flex-start" style={{ minHeight: '66px' }}>
                {/* 系统生成参考图模式下的展示 - 空状态 */}
                {currentReferenceType === '系统生成参考图' && referencePicture.length === 0 && isOpenDrawer && (
                  <EmptyStateView />
                )}

                {/* 展示已选择的参考图 */}
                {displayedPictures.length > 0 && displayedPictures.map((image, index) => (
                  <ReferenceThumbnail key={index} image={image} index={index} />
                ))}

                {/* 加载更多按钮 */}
                {hasMorePictures && (
                  <div
                    className={styles.addButton}
                    onClick={loadMorePictures}
                    style={{
                      backgroundColor: '#f0f8ff',
                      border: '2px dashed #1890ff',
                      color: '#1890ff'
                    }}
                  >
                      <span className={styles.addText}>
                        加载更多<br/>
                        ({referencePicture.length - displayCount}张)
                      </span>
                  </div>
                )}

                {/* 系统生成参考图模式下的添加按钮 */}
                {currentReferenceType === '系统生成参考图' && referencePicture.length > 0 && !isOpenDrawer && (
                  <AddButton onClick={() => setIsOpenDrawer(true)} />
                )}

                {/* 本地上传模式下的添加按钮 */}
                {currentReferenceType === '本地上传参考图' && !isOpenDrawer && (
                  <Upload
                    {...multiImageUploadProps}
                  >
                    <AddButton onClick={() => {
                    }} />
                  </Upload>
                )}
              </Flex>

              {/* 底部操作栏 */}
              {!(currentReferenceType === '系统生成参考图' && referencePicture.length === 0) && (
                <BottomBar />
              )}
            </div>
          )}
        </div>
      </Flex>
    );
  };

  const handleClothImageChange = (value) => {
    setIsPreloading(false);
    clothSmudgeToolkit.handleLinesChange([]);
    // 如果有值，先设置淡入效果的状态为false，然后更新数据，最后设置淡入效果
    if (value) {
      setFadeIn(false);
      setClothImage(value);
      // 延迟设置淡入效果，给渲染留出时间
      setTimeout(() => {
        setFadeIn(true);
      }, 50);
    } else {
      setClothImage(value);
    }
  };

  const ModalFooter = () => {
    return (
      <Flex
        gap={10}
        style={{
          width: 'auto',
          paddingTop: 8,
        }}
        justify={'flex-end'}
      >
        <Button className="repair-detail-footer-button" onClick={handleModalCancel}>
          取消
        </Button>
        <Button
          className="repair-detail-submit-button"
          disabled={!(canSubmit || (predictVO && predictVO.needTopup))}
          type="primary"
          style={{ width: 'auto', justifyContent: 'center' }}
          onClick={handleCreate}
          icon={
            commiting ? (
              <LoadingOutlined style={{fontSize: 16, color: 'fff'}} />
            ) : (
              ''
            )
          }
        >
          <div style={{ width: 139 }}>
            {!predictVO?.needTopup
              ? '确认涂抹区域并生成'
              : '余额不足，去充值'}
          </div>
          {canSubmit && (
            <>
              <IconFont type={'icon-icon_mousidian'} style={{ fontSize: 16 }} />
              {predictVO?.musePoint}
              <Tooltip title={'每张参考图消耗0.4缪斯点'} >
                <QuestionCircleOutlined
                  style={{ fontSize: 16 }}
                  className={'margin-left-4 color-96'}
                />
              </Tooltip>
            </>
          )}
        </Button>
      </Flex>
    );
  };


  return (
    <PageContainer>
      <Flex className={styles.rowContainer}>
        <div className={styles.workBlock} style={{padding: '16px 8px 0 8px'}}>
          <SelectReferenceImage tag={'选择参考图'} />

          <div className={styles.numberInputContainer}>
            <div className={'text16 font-pf color-n weight'}>
              每张参考图生成&nbsp;&nbsp;
              <InputNumber value={4} disabled={true} />&nbsp;&nbsp;
              张
            </div>
          </div>
        </div>
        <div ref={outputBlockRef} className={styles.outPutBlock}
             style={{ width: `calc((100% - 644px) * ${sliderValue / 12})`, maxWidth: '100%' }}
        >
          {/* 添加抽屉组件 */}
          {isOpenDrawer &&
            <StyleLoraDrawer
              isVipUser={vipUser}
              expand={isOpenDrawer}
              currentType={currentType}
              changeExpand={setIsOpenDrawer}
              selectedReferences={referencePicture}
              preloadedSceneModels={preloadedSceneModels}
              styleDescription={styleDescription || ''}
              onPreloadedSceneModelsUpdate={handlePreloadedSceneModelsUpdate}
              sceneConfig={sceneConfig}
              maxSelectCount={MAX_IMAGE_NUM} // 设置为null表示无数量限制
              loadingText="正在拉取数据，请稍等..." // 自定义加载提示文案
              onSelect={(images: SelectedItem[]) => {
                setReferencePicture(images.map(image => ({
                  imageUrl: image.image,
                  isSelected: true,
                  referenceConfig: image.referenceConfig,
                  backTags: image.backTags,
                  backExtTags: image.backExtTags,
                  loraId: image.loraId, // 使用StyleLoraDrawer传递的loraId
                  loraPath: image.referenceConfig?.loraPath || '', // 从referenceConfig中获取loraPath
                  originalImageUrl: image.referenceConfig?.styleImage || '', // 从referenceConfig中获取styleImage
                  id: `scene_${image.loraId}_${Date.now()}_${Math.random()}`, // 生成唯一ID
                })));
              }}
              width={'calc(100% - 68px)'}
              className={styles.referenceDrawer}
            />
          }
          <TaskOutputBlock
            sliderValue={sliderValue}
            changeSliderValue={setSliderValue}
            types={['CLOTHING_SWAP']}
            ref={taskOutputRef}
            pollingTimeout={3000}
            isSelectTask
          />
        </div>
      </Flex>
      <footer className={'toolkit-footer'}>
        <div className={'toolkit-footer-content'}>
          <PredictBlock
            creativeType={'CLOTHING_SWAP'}
            predictVO={predictVO}
            data={{imageNum: referencePicture.length}}
            type={type}
          />
          <Button
            type="primary"
            className={'toolkit-submit-btn'}
            disabled={ !((predictVO?.needTopup) || (!commiting && canSmudge))}
            icon={ commiting ? ( <LoadingOutlined style={{fontSize: 16, color: 'fff'}} /> ) : ('') }
            onClick={() => {setShowSmudgeModal(true)}}
          >
            {(predictVO?.needTopup) ? '余额不足，去充值' : (commiting ? '生成中' : '开始涂抹')}
          </Button>
        </div>
      </footer>
      {showTopupModal && (
        <TopupModal
          visible={showTopupModal}
          onClose={() => setShowTopupModal(false)}
          onPaySuccess={() => {
            setShowTopupModal(false);
            updatePoint();
          }}
        />
      )}

      {/* 涂抹弹窗 */}
      <Modal
        open={showSmudgeModal}
        className={styles.clothingSwapSmudgeModel}
        width={1284}
        centered
        title=""
        onCancel={handleModalCancel}
        onOk={() => {
          setShowSmudgeModal(false);
          setCurrentImageIndex(0);
        }}
        footer={<ModalFooter />}
      >
        <Flex vertical gap={16} justify={'center'} align={'center'}>
          {
            (canSmudge && clothImage === '') ? (
              <>
                {/* 标题 */}
                <div
                  className={'text24 weight color-1a'}
                  style={{ lineHeight: '32px' }}
                >
                  第二步 选择服装图
                </div>
                {/* 主体 */}
                <Flex gap={16} style={{ height: 622 }} align={'center'}>
                  {/* Left panel - Original image section */}
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      gap: 4,
                      justifyContent: 'space-between',
                    }}
                  >
                    <div
                      style={{
                        height: '560px',
                        width: '560px',
                        position: 'relative',
                        background: '#F5F6F9',
                        borderRadius: 8,
                      }}
                    >
                      {/* 图片展示 */}
                      {referencePicture.length > 0 && (
                        <img
                          className={styles.showImage}
                          src={referencePicture[currentImageIndex]?.imageUrl}
                          alt={`参考图 ${currentImageIndex + 1}`}
                        />
                      )}
                      {/* 标签 */}
                      <span className={'repair-detail-tag'}>参考图</span>
                    </div>
                    {/* 底部缩略图预览 */}
                    {referencePicture.length > 0 && (
                      <div className={styles.thumbnailContainer}>
                        <Button
                          icon={<LeftOutlined style={{color: '#d9d9d9'}} />}
                          onClick={() => setCurrentImageIndex(prev => prev > 0 ? prev - 1 : referencePicture.length - 1)}
                          className={styles.navButton}
                        />
                        <div className={styles.thumbnailList}>
                          {referencePicture.map((image, index) => (
                            <div
                              key={index}
                              className={`${styles.thumbnail} ${index === currentImageIndex ? styles.activeThumbnail : ''}`}
                              onClick={() => setCurrentImageIndex(index)}
                            >
                              <Image
                                src={image.imageUrl}
                                alt={`缩略图 ${index + 1}`}
                                className={styles.thumbnailImage}
                                preview={false}
                              />
                            </div>
                          ))}
                        </div>
                        <Button
                          icon={<RightOutlined style={{color: '#d9d9d9'}} />}
                          onClick={() => setCurrentImageIndex(prev => prev < referencePicture.length - 1 ? prev + 1 : 0)}
                          className={styles.navButton}
                        />
                      </div>
                    )}
                  </div>

                  {/* Right panel - Cloth image section */}
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      gap: 4,
                      justifyContent: 'space-between',
                    }}
                  >
                    <div
                      style={{
                        height: '560px',
                        width: '560px',
                        position: 'relative',
                      }}
                    >
                      <div
                        style={{
                          position: 'relative',
                          display: 'flex',
                          height: '100%',
                          width: '100%',
                        }}
                      >
                        <ClothImageUploader
                          image={clothImage}
                          onImageChange={handleClothImageChange}
                          render={
                            <div>选择服装图</div>
                          }
                          originImage={''}
                          setLoading={setIsPreloading}
                          desc={''}
                        />
                        {isPreloading && (
                          <div className="repair-detail-loading-overlay">
                            <LoadingOutlined
                              style={{ fontSize: 48, color: 'fff' }}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                    {/* 填充块 */}
                    <div style={{height: 60}}></div>
                  </div>
                </Flex>
              </>
            ) : (
              <>
                <div
                  className={'text24 weight color-1a'}
                  style={{ lineHeight: '32px' }}
                >
                  第三步 涂抹
                </div>
                {/* 主体 */}
                <Flex gap={16} style={{ height: 622 }} align={'center'}>
                  {/* Left panel - Original image section */}
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      gap: 4,
                      justifyContent: 'space-between',
                    }}
                  >
                    <div
                      style={{
                        height: '560px',
                        width: '560px',
                        position: 'relative',
                        background: '#F5F6F9',
                        borderRadius: 8,
                      }}
                    >
                      <div
                        style={{
                          backgroundColor: '#ffffff',
                          borderRadius: 8,
                          opacity: fadeIn ? 1 : 0,
                          transition: 'opacity 0.3s ease-in-out',
                          width: '100%',
                          height: '100%',
                          position: 'relative',
                        }}
                      >
                        {/* 只渲染当前可见的SmudgeCanvas */}
                        {referencePicture.length > 0 && (
                          <SmudgeCanvas
                            key={'image'}
                            imageUrl={referencePicture[currentImageIndex].imageUrl}
                            lines={getCurrentToolkit().editorState.lines}
                            stageCmtRef={getCurrentStageRef()}
                            editorState={getCurrentToolkit().editorState}
                            handleLinesChange={getCurrentToolkit().handleLinesChange}
                            onScaleChange={getCurrentToolkit().onScaleChange}
                            onPositionChange={getCurrentToolkit().onPositionChange}
                            handleSizeChange={getCurrentToolkit().handleSizeChange}
                            keep
                          />
                        )}
                        {/* 标签 */}
                        <span className={'repair-detail-tag'}>参考图</span>
                      </div>
                    </div>

                    <div
                      style={{
                        width: '100%',
                        opacity: fadeIn ? 1 : 0,
                        transition: 'opacity 0.3s ease-in-out',
                      }}
                    >
                      {referencePicture.length > 0 && (
                        <SmudgeToolkit
                          useEditorState={[getCurrentToolkit().editorState, getCurrentToolkit().setEditorState]}
                          gap={2}
                        />
                      )}
                    </div>


                    {/* 底部缩略图预览 */}
                    {referencePicture.length > 0 && (
                      <div className={styles.thumbnailContainer}>
                        <Button
                          icon={<LeftOutlined style={{color: '#d9d9d9'}} />}
                          onClick={() => setCurrentImageIndex(prev => prev > 0 ? prev - 1 : referencePicture.length - 1)}
                          className={styles.navButton}
                        />
                        <div className={styles.thumbnailList}>
                          {referencePicture.map((image, index) => (
                            <div
                              key={index}
                              className={`${styles.thumbnail} ${index === currentImageIndex ? styles.activeThumbnail : ''}`}
                              onClick={() => setCurrentImageIndex(index)}
                            >
                              <Image
                                src={image.imageUrl}
                                alt={`缩略图 ${index + 1}`}
                                className={styles.thumbnailImage}
                                preview={false}
                              />
                            </div>
                          ))}
                        </div>
                        <Button
                          icon={<RightOutlined style={{color: '#d9d9d9'}} />}
                          onClick={() => setCurrentImageIndex(prev => prev < referencePicture.length - 1 ? prev + 1 : 0)}
                          className={styles.navButton}
                        />
                      </div>
                    )}

                  </div>

                  {/* Right panel - Cloth image section */}
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      gap: 4,
                      justifyContent: 'space-between',
                    }}
                  >
                    <div
                      style={{
                        height: '560px',
                        width: '560px',
                        position: 'relative',
                      }}
                    >
                      <div
                        style={{
                          backgroundColor: '#ffffff',
                          borderRadius: 8,
                          transition: 'opacity 0.3s ease-in-out',
                          width: '100%',
                          height: '100%',
                          position: 'relative',
                        }}
                      >
                        <SmudgeCanvas
                          key={'cloth'}
                          imageUrl={clothImage}
                          stageCmtRef={clothStageRef}
                          lines={clothSmudgeToolkit.editorState.lines}
                          editorState={clothSmudgeToolkit.editorState}
                          handleLinesChange={clothSmudgeToolkit.handleLinesChange}
                          onScaleChange={clothSmudgeToolkit.onScaleChange}
                          onPositionChange={clothSmudgeToolkit.onPositionChange}
                        />
                        <ImageUploaderButton
                          onImageChange={handleClothImageChange}
                        />
                        <span className={'repair-detail-tag'}>正确图</span>
                      </div>
                    </div>
                    <div
                      style={{
                        width: '100%',
                        opacity: fadeIn ? 1 : 0,
                        transition: 'opacity 0.3s ease-in-out',
                      }}
                    >
                      <SmudgeToolkit
                        useEditorState={[clothSmudgeToolkit.editorState, clothSmudgeToolkit.setEditorState]}
                        gap={2}
                      />
                    </div>
                    {/* 填充块 */}
                    <div style={{height: 60}}></div>
                  </div>
                </Flex>
              </>
            )
          }
        </Flex>
      </Modal>
      
      {/* 我收藏的模板弹窗 */}
      <FavoriteTemplateModal
        open={showFavoriteModal}
        onCancel={() => setShowFavoriteModal(false)}
        onSelect={(templates) => {
          // 将选择的模板转换为参考图格式并添加到参考图列表中
          const newReferencePictures = templates.map(template => ({
            imageUrl: template.imageUrl,
            isSelected: template.isSelected,
            referenceConfig: template.referenceConfig,
            backTags: template.backTags,
            backExtTags: template.backExtTags,
            loraId: template.loraId,
            loraPath: template.referenceConfig?.loraPath || '', // 从referenceConfig中获取loraPath
            originalImageUrl: template.referenceConfig?.styleImage || '', // 从referenceConfig中获取styleImage
            id: `favorite_${template.loraId}_${Date.now()}_${Math.random()}`, // 生成唯一ID
          }));
          
          // 更新参考图列表
          setReferencePicture(prev => {
            // 过滤掉已存在的URL，避免重复
            const existingUrls = new Set(prev.map(item => item.imageUrl));
            const filteredNewPictures = newReferencePictures.filter(
              item => !existingUrls.has(item.imageUrl)
            );
            return [...prev, ...filteredNewPictures];
          });
          
          // 关闭弹窗
          setShowFavoriteModal(false);
          
          message.success(`已添加 ${templates.length} 个收藏模板到参考图`);
        }}
      />

      {/* 完善收藏信息弹窗 */}
      <Modal
        open={showCollectionInfoModal}
        title="完善收藏信息"
        onCancel={() => {
          setShowCollectionInfoModal(false);
          setCollectionTemplateName('');
        }}
        onOk={() => {
          if (!collectionTemplateName.trim()) {
            message.error('请输入模板名称');
            return;
          }
          
          // 获取选中的参考图
          const selectedImages = referencePicture.filter(img => img.isSelected);
          if (selectedImages.length === 0) {
            message.error('请至少选择一张参考图');
            return;
          }
          
          // 构建referenceInfoList数据
          const referenceInfoList = selectedImages.map(image => ({
            imageUrl: image.imageUrl,
            referenceConfig: image.referenceConfig || {}, // 直接传递JSONObject，如果为空则传递空对象
            backTags: image.backTags || '',
            backExtTags: image.backExtTags || '',
            loraId: image.loraId || 0, // 使用参考图中的loraId字段
            loraPath: image.loraPath || (image.referenceConfig?.loraPath || ''), // lora路径，优先使用loraPath字段，否则从referenceConfig中获取
            originalImageUrl: image.originalImageUrl || (image.referenceConfig?.styleImage || '') // 原始图片地址，优先使用originalImageUrl字段，否则从referenceConfig中获取styleImage
          }));
          
          // 调用创建固定姿势模板API
          const templateData = {
            templateName: collectionTemplateName.trim(),
            userId: userInfo.userId || userInfo.id, // 添加当前用户ID
            referenceInfoList: referenceInfoList
          };
          
          createFixedCreativeTemplate(templateData)
            .then((result) => {
              if (result) {
                message.success('收藏成功');
                setShowCollectionInfoModal(false);
                setCollectionTemplateName('');
              } else {
                message.error('收藏失败，请重试');
              }
            })
            .catch((error) => {
              console.error('收藏失败:', error);
              message.error('收藏失败，请重试');
            });
        }}
        okText="保存"
        cancelText="取消"
        width={800}
        centered
      >
        <Flex vertical gap={16}>
          {/* 模板名称输入框 */}
          <div>
            <div className="text16 font-pf color-n weight margin-bottom-8">模板名称</div>
            <Input
              placeholder="请输入模板名称"
              value={collectionTemplateName}
              onChange={(e) => setCollectionTemplateName(e.target.value)}
              maxLength={20}
              showCount
            />
          </div>

          {/* 收藏的图片展示 */}
          <div>
            <div className="text16 font-pf color-n weight margin-bottom-8">
              收藏姿势 ({referencePicture.filter(img => img.isSelected).length})
            </div>
            <div style={{ 
              display: 'flex', 
              flexWrap: 'wrap', 
              gap: '12px',
              maxHeight: '400px',
              overflowY: 'auto',
              padding: '8px',
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
              backgroundColor: '#fafafa'
            }}>
              {referencePicture.filter(img => img.isSelected).length === 0 ? (
                <div style={{ 
                  width: '100%', 
                  textAlign: 'center', 
                  padding: '40px 0',
                  color: '#999'
                }}>
                  暂无选中的参考图
                </div>
              ) : (
                referencePicture
                  .filter(img => img.isSelected)
                  .map((image, index) => (
                    <div key={index} style={{ position: 'relative' }}>
                      <Image
                        src={image.imageUrl}
                        alt={`收藏图片${index + 1}`}
                        width={120}
                        height={120}
                        style={{ 
                          objectFit: 'cover',
                          borderRadius: '6px',
                          border: '1px solid #d9d9d9'
                        }}
                        preview={true}
                      />
                    </div>
                  ))
              )}
            </div>
          </div>
        </Flex>
      </Modal>
    </PageContainer>
  );
};

export default ClothingSwap;