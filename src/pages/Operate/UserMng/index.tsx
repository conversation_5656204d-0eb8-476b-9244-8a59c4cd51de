import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
	Bad<PERSON>,
	Button, Cascader, Checkbox, DatePicker, Descriptions, Dropdown,
	Flex,
	Form,
	Input,
	InputNumber,
	message,
	Modal,
	notification,
	Pagination,
	Popconfirm,
	Radio,
	Select, Space,
	Table,
	Tooltip,
} from 'antd';
import dayjs from 'dayjs';

import {
	adjustMuse,
	assignCustomer2Distributor,
	createUser,
	deleteUser,
	getPageUser,
	getUserCount,
	givePoint,
	queryAllMaster,
	reviewUser,
	ROLE_TYPE, RoleTypesItems,
	topup2User,
	updateUser,
	UserVO,
	createUserProfile, genTestMobile, queryAllSubByUserId, modifyContractDate,
} from '@/services/UserController';
import InputWithTags from '@/components/InputWithTags';
import { TagType } from '@/services/TagsController';
import { backRestPassword, fetchSalt } from '@/services/LoginController';
import cryptoStr from '@/utils/cryptojs';
import { PASSWORD_REGEX } from '@/constants';
import { getPricePlan, PricePlan } from '@/services/OrderInfoController';
import TopupModal from '@/pages/Topup/Topup';
import {
	DeleteOutlined, EditOutlined,
	EyeOutlined,
	PlusOutlined,
	KeyOutlined,
	GiftOutlined, SignatureOutlined,
} from '@ant-design/icons';
import { formatDateToCompactString } from '@/utils/utils';
import IconFont from '@/components/IconFont';
import {
	OrganizationVO,
	queryDistributorOrganizationTrees, SalesType,
} from '@/services/OrganizationController';
import { salesTypeMap } from '@/services/OrganizationController';
import { NaviType } from '@/app';

interface UserModalConfig {
	title: string,
	uid?: number | undefined,
	// eslint-disable-next-line @typescript-eslint/ban-types
	onOk: Function,
	// eslint-disable-next-line @typescript-eslint/ban-types
	onCancel: Function
}

export const UserMng: React.FC<{naviType?: NaviType}> = ({naviType = 'MANAGE'}) => {
	const [currentPage, setCurrentPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [totalCount, setTotalCount] = useState(0);
	const [data, setData] = useState<Array<UserVO>>([]);
	const [showTopup, setShowTopup] = useState(false);
	const [showGive, setShowGive] = useState(false);
	const [adjustPoint, setAdjustPoint] = useState<number>(0);

	const [adjustGivePoint, setAdjustGivePoint] = useState<number>(0);
	const [adjustExperiencePoint, setAdjustExperiencePoint] = useState<number>(0);

	const [searchId, setSearchId] = useState<string>();
	const [nameLike, setNameLike] = useState<string>();
	const [corpNameLike, setCorpNameLike] = useState<string>();
	const [memoLike, setMemoLike] = useState<string>();
	const [mobileLike, setMobileLike] = useState<string>();

	//选中的渠道商
	const [selectedDistributorMasterId, setSelectedDistributorMasterId] = useState<number | null>(null);
	const [selectedDistributorSalesId, setSelectedDistributorSalesId] = useState<number | null>(null);
	//选中的prompt工程师
	const [selectedPromptUserId, setSelectedPromptUserId] = useState<number | null>(null);

	const [isDistributor, setIsDistributor] = useState(false);
	const [salesType, setSalesType] = useState<SalesType | null>(null);

	const [isMerchant, setIsMerchant] = useState(false);

	const [showUserModal, setShowUserModal] = useState(false);
	const [resetPswdUser, setResetPswdUser] = useState<UserVO | null>(null);
	const [salt, setSalt] = useState<string | null>(null);
	const [pswd, setPswd] = useState<string | null>(null);
	const [showUserModalConfig, setShowUserModalConfig] = useState<UserModalConfig>();

	const [showUserReviewModal, setShowUserReviewModal] = useState(false);
	const [reviewingUser, setReviewingUser] = useState<UserVO>();
	const [reviewUserModalReadonly, setReviewUserModalReadonly] = useState(false);

	const [checkedUserStatus, setCheckedUserStatus] = useState<string>('ALL');

	// 角色
	const [checkedUserRole, setCheckedUserRole] = useState<string>('ALL');

	const [userForm] = Form.useForm();
	const [userReviewForm] = Form.useForm();
	const [topupForm] = Form.useForm();
	const [giveForm] = Form.useForm();
	const [contractForm] = Form.useForm();
	const [assignDistributorform] = Form.useForm();
	const [assignPromptUserform] = Form.useForm();

	const [underReviewUserCount, setUnderReviewUserCount] = useState(0);

  //  所有渠道商组织（包含子组织）
  const [allDistributorOrgTrees, setAllDistributorOrgTrees] = useState([]);

	const [allDistributorMasters, setAllDistributorMasters] = useState<Array<UserVO>>([]);
	const [allAdmins, setAllAdmins] = useState<Array<UserVO>>([]);
	const [showAssignDistributorModal, setShowAssignDistributorModal] = useState(false);
	const [showAssignPromptUserModal, setShowAssignPromptUserModal] = useState(false);

	// 线下转账充值
	const [showTopup2UserModal, setShowTopup2UserModal] = useState(false);
	const [topup2UserForm] = Form.useForm();
	//selected plan code
	const [selectedPlan, setSelectedPlan] = useState<PricePlan>();
	const [plans, setPlans] = useState<PricePlan[]>();

	const [showTopupModal2CollectMoney, setShowTopupModal2CollectMoney] = useState(false);

	const [currentUserRoleType, setCurrentUserRoleType] = useState<ROLE_TYPE | undefined>(undefined);

	const [showOrgDetailModal, setShowOrgDetailModal] = useState(false);
	const [currentOrgDetail, setCurrentOrgDetail] = useState<any>(null);

	const [userMobile, setUserMobile] = useState<string>();

	const [onlyNeedPromptEngineer, setOnlyNeedPromptEngineer] = useState(false);

	const [onlyNeedContract, setOnlyNeedContract] = useState(false);

  //所有销售/渠道商账号
  const [allSalesOptions, setAllSalesOptions] = useState<any[]>([]);
	// 录入签约时间
	const [showContract, setShowContract] = useState(false);

	const onUserFormValuesChange = (changedValues, allValues) => {
		console.log('Changed values:', changedValues);
		console.log('All values:', allValues);

		if (changedValues.roleType) {
			setIsDistributor(changedValues.roleType === 'DISTRIBUTOR');
			setIsMerchant(changedValues.roleType === 'MERCHANT');

			setCurrentUserRoleType(changedValues.roleType);

			// 当选择渠道商角色时，自动设置默认的销售类型
			if (changedValues.roleType === 'DISTRIBUTOR') {
				userForm.setFieldsValue({
					salesType: 'AGENT_NORMAL'
				});
			}
		}

		if (changedValues.mobile) {
			setUserMobile(changedValues.mobile);
		}
	};

	function textWithMaxLength(content, maxLen) {
		return content?.length > maxLen ? content.slice(0, maxLen) + '...' : content;
	}

	const columns = [
		{
			title: '客户ID',
			dataIndex: 'id',
			width: 60,
			key: 'id',
			render: (text) => <span style={{ color: '#409EFF' }}>{text}</span>,
		},
		{
			title: '昵称',
			key: 'nickName',
			width: 120,
			render: (_, record) =>
				<Tooltip title={record.nickName || record.corpName}>
					<span style={{ cursor: 'pointer' }} onClick={() => {
						navigator.clipboard.writeText(record.nickName || record.corpName);
						message.success('已复制');
					}}>
						{textWithMaxLength(record.nickName || record.corpName, 10)}
					</span>
				</Tooltip>,
		},
		{
			title: '手机号',
			key: 'mobile',
			dataIndex: 'mobile',
			width: 90,
		},
		{
			title: '客户身份',
			dataIndex: 'roleType',
			key: 'roleType',
			width: 100,
			render: (text: ROLE_TYPE, record) => <span
				style={{ color: text === 'ADMIN' || text === 'OPERATOR' ? '#409EFF' : '' }}>{getRoleTypeName(record)}</span>,
		},
		{
			title: '公司名称',
			key: 'corpName',
			width: 100,
			render: (_, record) => {
				if (!record.corpName) {
					return null;
				}
				return <Tooltip title={record.corpName}><span>{record.corpName}</span></Tooltip>;
			},
			ellipsis: {
				showTitle: false,
			},
		},
    {
      title: <Tooltip title={'客户通过邀请人的邀请码完成注册'} style={{cursor: 'pointer'}}>邀请人<IconFont type={'icon-yiwen1'}/></Tooltip>,
      key: 'inviteRegister',
      width: 100,
      render: (_, record) => {
        if (!record.inviteRegister) {
          return null;
        }
        return <Tooltip title={record.inviteRegister}><span style={{color: 'green'}}>{record.inviteRegister}</span></Tooltip>;
      },
      ellipsis: {
        showTitle: false,
      },
    },
		{
			title: '销售/渠道商',
			key: 'distributor',
			width: 100,
			render: (_, record) => {
				const distributorInfo = getRelatedDistributor(record);
				if (!distributorInfo) {
					return null;
				}
				return <Tooltip title={distributorInfo}><span>{distributorInfo}</span></Tooltip>;
			},
			ellipsis: {
				showTitle: false,
			},
		},
		{
			title: '工程师',
			key: 'promptUser',
			width: 80,
			render: (_, record: UserVO) => {
				const promptUser = allAdmins.find(admin => String(admin.id) === record.profiles?.promptUserId?.profileVal)?.nickName ?? "";
				return <Tooltip title={promptUser}><span>{promptUser}</span></Tooltip>;
			},
			ellipsis: {
				showTitle: false,
			},
		},
		{
			title: '状态',
			dataIndex: 'statusDesc',
			key: 'statusDesc',
			width: 40,
			render: (text: string, record) => {
				let color = '#000000';
				switch (record.status) {
					case 'ENABLED':
						color = '#52c41a'; // 正常状态 - 绿色
						break;
					case 'UNDER_REVIEW':
						color = '#faad14'; // 审核中 - 橙色
						break;
					case 'REJECT':
						color = '#ff4d4f'; // 审核拒绝 - 红色
						break;
					case 'DISABLED':
						color = '#d9d9d9'; // 停用 - 灰色
						break;
					default:
						color = '#000000';
				}
				return <span style={{ color }}>{text}</span>;
			},
		},
		{
			title: '标签',
			width: 80,
			key: 'memo',
			render: (_, record) => {
				if (!record.memo) {
					return null;
				}
				return <Tooltip title={record.memo}><span>{record.memo}</span></Tooltip>;
			},
			ellipsis: {
				showTitle: false,
			},
		},
		{
			title: '创建时间',
			width: 70,
			key: 'createTime',
			render: (_, record) => {
				if (!record.createTime) {
					return null;
				}
				return <Tooltip title={record.createTime}><span>{formatDateToCompactString(record.createTime)}</span></Tooltip>;
			},
			ellipsis: {
				showTitle: false,
			},
		},
		{
			title: '缪斯点',
			width: 60,
			dataIndex: 'imagePoint',
			key: 'imagePoint',
			render: (text: ROLE_TYPE) => <span style={{ color: 'red', textAlign: 'right' }}>{text ? text : 0}</span>,
		},
		{
			title: '操作',
			key: 'action',
			width: 160,
			fixed: 'right' as const,
			className: 'action-column',
			render: (text, record: UserVO) => (
				<Flex className="action-buttons">
					{((record.roleType === 'MERCHANT' || record.roleType === 'DISTRIBUTOR') && record.userType === 'MASTER' || record.roleType === 'OPERATOR' || record.roleType === 'DEMO_ACCOUNT') && (
						<>
							<a style={{ color: 'red' }} onClick={() => showTopupModal(record)}>调缪斯点</a>
							{naviType  === 'MANAGE' && <a style={{ color: 'red' }} onClick={() => showGiveModal(record)}>赠送</a>}
						</>
					)}

					{naviType === 'BUSINESS' && record.status === 'ENABLED' && record?.roleType === 'MERCHANT' && (
						<a onClick={() => {
							setShowAssignDistributorModal(true);
							assignDistributorform.resetFields();
							assignDistributorform.setFieldsValue({
								userId: record.id,
								userName: record.nickName,
								userCorpName: record.corpName,
								distributorUserId: undefined,
							});
						}}>
							{record?.relatedDistributorCorpId != null ? '变更销售/渠道商' : '指派销售/渠道商'}
						</a>
					)}

					{naviType  === 'MANAGE' && record.status === 'ENABLED' && record?.roleType === 'MERCHANT' && (
						<a onClick={() => {
							setShowAssignPromptUserModal(true);
							assignPromptUserform.resetFields();
							assignPromptUserform.setFieldsValue({
								uid: record.id,
								profileKey: 'promptUserId',
								profileVal: record.profiles?.promptUserId?.profileVal,
							});
						}}>
							{record?.profiles?.promptUserId?.profileVal ? '变更工程师' : '指派工程师'}
						</a>
					)}
					{ naviType === 'BUSINESS' &&
						<Dropdown
							menu={{
								items: [
									{
										key: 'editUser',
										label: '编辑',
										icon: <EditOutlined />,
									},
									{
										key: 'changePwd',
										label: '改密码',
										icon: <KeyOutlined />,
									},
									{
										key: 'give',
										label: '赠送',
										icon: <GiftOutlined />,
										disabled: !((record.roleType === 'MERCHANT' || record.roleType === 'DISTRIBUTOR') && record.userType === 'MASTER' || record.roleType === 'OPERATOR' || record.roleType === 'DEMO_ACCOUNT')
									},
									{
										key: 'topup',
										label: '录入转账',
										icon: <PlusOutlined />,
										disabled: record.userType !== 'MASTER'
									},
									{
										key: 'contract',
										label: '签约',
										icon: <SignatureOutlined />,
										disabled: (record.roleType !== 'DISTRIBUTOR' || record.customRole === 'OPERATOR'),
									},
									{
										key: 'delUser',
										label: '删除',
										icon: <DeleteOutlined />,
										danger: true
									},
								],
								onClick: ({ key }) => {
									if (key === 'changePwd') {
										showResetPswdModal(record);
									} else if (key === 'editUser') {
										showUpdateUserModal(record);
									} else if (key === 'delUser') {
										showDeleteUserModal(record);
									} else if (key === 'give') {
										showGiveModal(record);
									} else if (key === 'topup') {
										setShowTopup2UserModal(true);
										topup2UserForm.resetFields();
										topup2UserForm.setFieldsValue({
											userId: record.id,
											userName: record.nickName,
											...selectedPlan,
											memo: '线下转账' + selectedPlan?.amount,
	collectMoneyType: 'offlinePayment',									});
									} else if (key === 'contract') {
										showContractModal(record);
									}
								},
							}}
						>
							<a onClick={(e) => e.preventDefault()}>
								更多
							</a>
						</Dropdown>
					}
				</Flex>
			),
		},
	];

	function getRelatedDistributor(u: UserVO) {
		if (u.roleType === 'MERCHANT') {
			if (u.relatedDistributorCorpName) {
				if (u.relatedSalesUserName) {
					return u.relatedSalesUserName + '@' + u.relatedDistributorCorpName;
				} else {
					return u.relatedDistributorCorpName;
				}
			}

		} else if (u.roleType === 'DISTRIBUTOR') {
			return u.masterNick + '@' + u?.corpName;
		}

		return '';
	}

	async function fetchData() {

		let query = {
			pageNum: currentPage,
			pageSize: pageSize,
			needPoint: true,
			id: searchId ? Number(searchId.trim()) : null,
			nickNameLike: (nameLike || '').trim(),
			corpNameLike: (corpNameLike || '').trim(),
			memoLike: (memoLike || '').trim(),
			mobileLike: (mobileLike || '').trim(),
			status: checkedUserStatus === 'ALL' ? null : checkedUserStatus,
			roleType: checkedUserRole === 'ALL' ? null : checkedUserRole.indexOf('-') >= 0 ? checkedUserRole.split('-')[0] : checkedUserRole,
			userType: checkedUserRole.indexOf('-') >= 0 ? checkedUserRole.split('-')[1] : null,
			relatedPromptEngineerUserId: selectedPromptUserId || null,
			relatedDistributorMasterUserId: selectedDistributorMasterId || null,
      relatedDistributorSalesUserId: selectedDistributorSalesId || null,
			onlyNeedPromptEngineer: onlyNeedPromptEngineer,
			onlyNeedContract: onlyNeedContract,
		};

		getPageUser(query).then(res => {
			if (res) {
				console.log('fetch data list response:', res);

				if (res.list) {
					let newData = res.list.map(item => ({ ...item, key: item.id }));
					setData(newData);
				}

				// @ts-ignore
				setTotalCount(res.totalCount);
			}
		});
	}

  function fetchAndSetAllSalesByMasterId(masterId: number){
    queryAllSubByUserId(masterId).then(res => {
      if (res) {
        let sales = res.filter(item => item.roleType === 'DISTRIBUTOR' && item.customRole !== 'OPS_MEMBER').map(sales => {
          return {
            label: sales.nickName,
            value: sales.id
          };
        });

        console.log('sales:', sales);

        setAllSalesOptions(sales);
      } else {
        setAllSalesOptions([]);
      }
    })
  }

	const fetchAllDistributor = () => {
		queryAllMaster(['DISTRIBUTOR']).then(res => {
			if (res) {
				setAllDistributorMasters(res);
			} else {
				setAllDistributorMasters([]);
			}
		});
	}

	const fetchAllAdmin = () => {
		queryAllMaster(['ADMIN']).then(res => {
			if (res) {
				setAllAdmins(res);
			} else {
				setAllAdmins([]);
			}
		});
	}

  const convertToTree = (data: OrganizationVO[]): { key: number; label: string; value: number; children?: { key: number; label: string; value: number }[] }[] => {
    return data.map(item => ({
      key: item.id,
      label: item.name,
      value: item.id,
      children: item.children?.map(child => ({
        key: child.id,
        label: child.name,
        value: child.id,
      })),
    }));
  };

  useEffect(() => {
		fetchData();
	}, [currentPage, pageSize,]);

	useEffect(() => {
    setCurrentPage(1);
    fetchData();
  }, [
    nameLike,
    memoLike,
    checkedUserStatus,
    checkedUserRole,
    mobileLike,
    searchId,
    corpNameLike,
    selectedPromptUserId,
    selectedDistributorMasterId,
    onlyNeedPromptEngineer,
    selectedDistributorSalesId,
		onlyNeedContract,
  ]);

	useEffect(() => {

		getUserCount({
			status: 'UNDER_REVIEW',

		}).then(res => {
			if (res !== undefined && Number(res) !== null) {
				setUnderReviewUserCount(res);
			}
		});

		fetchAllDistributor();
		fetchAllAdmin();

    queryDistributorOrganizationTrees().then(res => {
      if (res && Array.isArray(res)) {
        //@ts-ignore
        setAllDistributorOrgTrees(convertToTree(res));
      } else {
        setAllDistributorOrgTrees([]);
      }

    });

		getPricePlan().then(res => {
			if (res) {
				setPlans([...res, {
					code: '转账充值', name: '自定义',
					amount: '',
					musePoint: 0,
					creativeImgCountGave: 0,
					creativeImgAmountGave: '',
					customModelCountGave: 0,
					customModelAmountGave: '',
					customSceneCountGave: 0,
					customSceneAmountGave: '',
					shortVideoCountGave: 0,
					shortVideoAmountGave: '',
					clothCount: 0,
					creativeImgCountPerCloth: 0,
					exampleImgCountPerCloth: 0,
					maxCreativeImgCount: 0,
					totalGaveAmount: '',
				}]);

				setSelectedPlan(res[0]);

			} else {
				console.log('查询支付配置为空');
			}
		});

	}, []);

  useEffect(()=>{
    if (selectedDistributorMasterId) {
      fetchAndSetAllSalesByMasterId(selectedDistributorMasterId);
    } else {
      setAllSalesOptions([]);
    }

  }, [selectedDistributorMasterId]);

	function showTopupModal(u: UserVO) {
		console.log('topup', u);
		topupForm.resetFields();
		setAdjustPoint(0);
		setShowTopup(true);
		topupForm.setFieldsValue(u);
	}

	function showGiveModal(u: UserVO) {
		console.log('give', u);
		giveForm.resetFields();
    setAdjustExperiencePoint(0);
    setAdjustGivePoint(0);
		setShowGive(true);
		giveForm.setFieldsValue(u);
	}

	function showContractModal(u: UserVO) {
		console.log('contract', u);
		contractForm.resetFields();
		setShowContract(true);
		// 设置表单值，包括可能存在的签约日期
		contractForm.setFieldsValue({
			...u,
			// 如果用户对象中有contractDate字段，这里会设置，如果没有则为undefined
			contractDate: u.contractDate ? dayjs(u.contractDate) : undefined
		});
	}

	function getRoleTypeName(record: UserVO) {
		if (record.roleType === 'MERCHANT') {
			if (record.userType === 'MASTER') {
				return '客户(主)';
			} else {
				return '客户(子)';
			}
		}

		if (record.roleType === 'DISTRIBUTOR') {
			return '销售/渠道商' + (`(${record.customRoleName})`);
		}

		let find = RoleTypesItems.find(e => e.key === record.roleType);
		if (find) {
			return find.label;
		}
		return '未知';
	}

	// 删除用户弹窗确认
	function showDeleteUserModal(u: UserVO) {
		Modal.confirm({
			title: '确认删除',
			content: '删除后不可恢复，请谨慎操作',
			okText: '确认删除',
			cancelText: '取消',
			okType: 'danger',
			icon: null,
			onOk() {
				deleteUser(u.id).then(res => {
					if (res) {
						message.success('操作成功');
						fetchData();
					}
				});
			},
		});
	}

	const handleAdjustMuse = (values) => {
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		const { name, ...submitValues } = values;

		adjustMuse(submitValues.id, submitValues.adjustMuse, submitValues.memo).then(res => {
			if (res) {
				message.success('调整缪斯点成功');
				setShowTopup(false);
				fetchData();

			}
		});
	};

	const handleGive = (values) => {
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		const { name, ...submitValues } = values;

		givePoint(submitValues.id, submitValues.adjustExperiencePoint, submitValues.adjustGivePoint).then(res => {
			if (res) {
				message.success('赠送成功');
				setShowGive(false);
        setAdjustGivePoint(0);
        setAdjustExperiencePoint(0);

				fetchData();
			}
		});
	};

	// 录入签约时间, 表单提交
	const handleContract = async (values) => {
		try {
			const formattedDate = values.contractDate.format('YYYY-MM-DD');
			const result = await modifyContractDate(values.id, formattedDate);
			if (!result) {
				message.error('签约时间设置失败');
				return;
			}
			message.success('签约时间设置成功');
			setShowContract(false);
			contractForm.resetFields();
			fetchData(); // 刷新数据
		} catch (error) {
			message.error('签约时间设置失败');
		}
	}

	const handlePageChange = (page, pageSize?: number) => {
		setCurrentPage(page);
		if (pageSize) {
			setPageSize(pageSize);
		}
	};
	const handleCancel = () => {
		setShowUserModal(false);
		setIsDistributor(false);
		userForm.resetFields();
	};

	const handleNewUser = () => {

		userForm.validateFields()
			.then(values => {
				// 在这里处理表单提交
				console.log('表单值: ', values);

				createUser({ ...values, settleRate: values.settleRate / 100 }).then(res => {
					console.log('res:', res);
					if (res) {
						const { distributorOrgIdPath, corpName } = values;
						if (distributorOrgIdPath && Number(res) > 0) {
							assignCustomer2Distributor({
								userId: Number(res),
								userCorpName: corpName ? corpName?.trim() : null,
                distributorOrgIdPath: distributorOrgIdPath,
							}).then(res => {
								if (res) {
									message.success('操作成功');
									fetchData();
								} else {
									message.error('操作失败');
								}
							});
						} else {
							message.success('操作成功');
							fetchData();
						}

						fetchAllDistributor();
					} else {
						message.error('操作失败');
					}
				});

				setShowUserModal(false);
				setIsDistributor(false);
				setAdjustPoint(0);
				userForm.resetFields();
			})
			.catch(info => {
				console.log('验证失败:', info);
			});
	};

	const handleUpdateUser = (uid: number) => {
		userForm.validateFields()
			.then(values => {
				values.userId = uid;
				// 在这里处理表单提交
				console.log('表单值: ', values);

				updateUser(values).then(res => {
					console.log('res:', res);
					if (res) {
						message.success('操作成功');
						fetchData();
					}
				});

				setShowUserModal(false);
				setIsDistributor(false);
				userForm.resetFields();
			})
			.catch(info => {
				console.log('验证失败:', info);
			});
	};

	const showNewUserModal = () => {
		setShowUserModal(true);
		setShowUserModalConfig({
			title: '新增客户',
			uid: undefined,
			onOk: handleNewUser,
			onCancel: handleCancel,
		});
		userForm.resetFields();
		userForm.setFieldsValue({ roleType: 'MERCHANT', needInitMusePoints: 'N' });
		setIsMerchant(true);
		setUserMobile(undefined);
	};

	const showUpdateUserModal = (u: UserVO) => {
		setShowUserModal(true);
		setShowUserModalConfig({
			title: '编辑客户',
			onOk: () => handleUpdateUser(u.id),
			onCancel: handleCancel,
			uid: u.id,
		});

		userForm.setFieldsValue(u);
		setIsDistributor(u.roleType === 'DISTRIBUTOR');
		setIsMerchant(u.roleType === 'MERCHANT');
		setUserMobile(u.mobile);
	};

	const showResetPswdModal = (u: UserVO) => {
		fetchSalt().then(res => {
			if (!res) return;
			setSalt(res);
			setResetPswdUser(u);
		});
	};

	const clearPswd = () => {
		setPswd('');
		setResetPswdUser(null);
		setSalt(null);
	};

	const commitResetPswd = () => {
		if (!resetPswdUser) {
			return;
		}
		if (!pswd || !PASSWORD_REGEX.test(pswd)) {
			message.warning('密码格式不正确');
			return;
		}
		//@ts-ignore
		const password = cryptoStr.aesEncrypt(pswd, salt);
		backRestPassword(resetPswdUser?.id, password).then((r) => {
			if (!r) {
				return;
			}

			clearPswd();
		});
	};

	function getCollecMoneyStatusName(collecMoneyStatus) {
		switch (collecMoneyStatus) {
			case 'paying':
				return '支付中';
			case 'success':
				return '客户支付成功';
			case 'cancel':
				return '支付取消';
			default:
				return '';
		}
	}

	function getLastVisitDate(record) {
		if (record?.lastVisitDate) {
			return record.lastVisitDate;
		} else if (record?.lastLoginTime) {
			return formatDateToCompactString(record.lastLoginTime);
		} else {
			return '';
		}
	}

	function getCurrentDate() {
		const date = new Date();

		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需加 1
		const day = String(date.getDate()).padStart(2, '0');

		return `${year}-${month}-${day}`;
	}

	function msg4AccountType() {
		if (!showUserModalConfig?.uid) {
			if (currentUserRoleType === 'DEMO_ACCOUNT') {
				return <span style={{ color: 'red' }}>演示账号将自动初始化密码为bb123456，并充值10000点</span>;
			}

			if (currentUserRoleType === 'DISTRIBUTOR') {
				return <span style={{ color: 'red' }}>将自动创建关联的商家账号和演示账号（密码为bb123456）并充值10000点</span>;
			}
		}

		return '';
	}

  const validateAtLeastOne = (rule, values, callback) => {
    const { adjustExperiencePoint, adjustGivePoint } = values;
    if (!adjustExperiencePoint && !adjustGivePoint) {
      callback(new Error('调整体验点和调整赠送图片至少填写一个'));
    } else {
      callback();
    }
  };

	return (
		<PageContainer>
			<Flex vertical={true} gap={8} style={{ padding: 16 }}>
				<Flex gap={8} justify={'flex-start'} align={'center'}>
					<Input allowClear placeholder={'id搜索'}
						onChange={(e) => setSearchId(e.target.value.trim())}
						value={searchId?.toString()} style={{ width: '100px' }} />
					<Input allowClear placeholder={'昵称搜索'}
						onChange={(e) => setNameLike(e.target.value)}
						value={nameLike} style={{ width: '120px' }} />
					<Input allowClear placeholder={'公司名搜索'}
						onChange={(e) => setCorpNameLike(e.target.value)}
						value={corpNameLike} style={{ width: '120px' }} />
					<Input allowClear placeholder={'手机号搜索'}
						onChange={(e) => setMobileLike(e.target.value)}
						value={mobileLike} style={{ width: '120px' }} />
					<Input allowClear placeholder={'标签搜索'}
						onChange={(e) => setMemoLike(e.target.value)}
						value={memoLike} style={{ width: '100px' }} />

					<Select
						value={selectedDistributorMasterId}
						placeholder="选择销售/渠道商"
						style={{ width: 200 }}
						allowClear={true}
            showSearch
            optionFilterProp="label"
						onChange={e => setSelectedDistributorMasterId(e)}
					>
						{allDistributorMasters?.map(s => (
							<Select.Option key={s.id} value={s.id} label={s.corpName ? `${s.nickName}@${s.corpName}` : s.nickName}>
								{s.corpName ? `${s.nickName}@${s.corpName}` : s.nickName}
							</Select.Option>
						))}
					</Select>

          {selectedDistributorMasterId &&
            <Select
              value={selectedDistributorSalesId}
              placeholder="选择销售"
              style={{ width: 200 }}
              allowClear={true}
              showSearch
              optionFilterProp="label"
              options={allSalesOptions}
              onChange={e => setSelectedDistributorSalesId(e)}
            />
          }

					<Select
						placeholder="选择工程师"
						style={{ width: 120 }}
						allowClear={true}
						value={selectedPromptUserId}
						onChange={(e) => setSelectedPromptUserId(e)}
					>
						{allAdmins?.map(s => (
							<Select.Option key={s.id} value={s.id}>
								{s.nickName}
							</Select.Option>
						))}
					</Select>

					<Radio.Group defaultValue={'ALL'} optionType="button"
						value={checkedUserStatus}
						onChange={e => setCheckedUserStatus(e.target.value)}
						options={[
							{ label: '全部', value: 'ALL' }, { label: '正常', value: 'ENABLED' },
							{
								label: (
									<Badge count={underReviewUserCount}
										style={{
											width: 15,
											fontSize: 10,
											backgroundColor: 'red',
											position: 'absolute',
											top: -6,
											right: -20,
											transform: 'translateY(-50%)',
										}}>
										审核中
									</Badge>
								),
								value: 'UNDER_REVIEW',
							},
							{ label: '审核拒绝', value: 'REJECT' },
							{ label: '停用', value: 'DISABLED' },
						]}
					/>

				</Flex>
				<Flex gap={8} align={'center'} justify={'flex-start'}>

					<Flex align={'center'}>
						<Radio.Group defaultValue={'ALL'} optionType="button" value={checkedUserRole}
							onChange={e => setCheckedUserRole(e.target.value)}
							options={[{ label: '全部', value: 'ALL' }, { label: '商家-主', value: 'MERCHANT-MASTER' },
							{ label: '商家-子', value: 'MERCHANT-SUB' },
							...RoleTypesItems.filter(i => i.key !== 'MERCHANT')]}
						/>
					</Flex>
					{ naviType === 'BUSINESS' &&
						<Button type="primary"
							icon={<PlusOutlined />}
							className="members-add-user-button"
							onClick={() => showNewUserModal()}>
							新增客户
						</Button>
					}
					{ naviType === 'MANAGE' &&
						<Tooltip title={'充值3999及以上未分配工程师'}>
							<Checkbox checked={onlyNeedPromptEngineer} onChange={e => {
								setOnlyNeedPromptEngineer(e.target.checked);
							}}>待分配工程师</Checkbox>
						</Tooltip>
					}

					{ naviType === 'BUSINESS' &&
						<Tooltip title={'尚未签约的销售/渠道商'}>
							<Checkbox checked={onlyNeedContract} onChange={e => {
								setOnlyNeedContract(e.target.checked);
							}}>待签约</Checkbox>
						</Tooltip>
					}

				</Flex>
				<Table
					columns={columns}
					dataSource={data}
					pagination={false}
					className="members-user-table"
					scroll={{ x: 1300 }}
					expandable={{
						expandedRowRender: (record) => (
							<div style={{ margin: 0, padding: '12px 16px', background: '#fafafa' }}>
								<Flex gap={12} wrap="wrap">
									{/* 企业认证信息卡片 */}
									<div style={{
										background: '#fff',
										padding: '12px',
										borderRadius: '4px',
										boxShadow: '0 1px 2px rgba(0,0,0,0.06)',
										maxWidth: '300px',
										flex: 1
									}}>
										<div style={{
											color: '#262626',
											fontSize: '13px',
											fontWeight: 500,
											marginBottom: '8px',
											paddingBottom: '4px',
											borderBottom: '1px solid #f0f0f0',
											display: 'flex',
											alignItems: 'center',
											justifyContent: 'space-between'
										}}>
											<span>企业认证信息</span>
											{record.organizationVO?.extInfo && (
												<EyeOutlined
													style={{
														cursor: 'pointer',
														color: '#1890ff',
														fontSize: '14px'
													}}
													onClick={() => {
														setShowOrgDetailModal(true);
														setCurrentOrgDetail(record.organizationVO);
													}}
												/>
											)}
										</div>
										{record.organizationVO?.extInfo ? (
											<Flex vertical gap={6}>
												<Flex justify="space-between" style={{ padding: '0 4px' }}>
													<span style={{ color: '#8c8c8c', fontSize: '12px' }}>认证状态</span>
													<span style={{
														color: record.organizationVO?.extInfo?.status ? '#52c41a' : '#ff4d4f',
														fontWeight: 500,
														fontSize: '12px'
													}}>
														{record.organizationVO?.extInfo?.status || '未认证'}
													</span>
												</Flex>
												<Flex justify="space-between" style={{ padding: '0 4px' }}>
													<span style={{ color: '#8c8c8c', fontSize: '12px' }}>企业名称</span>
													<span style={{ fontWeight: 500, fontSize: '12px' }}>
														{record.organizationVO?.extInfo?.name || '无'}
													</span>
												</Flex>
												<Flex justify="space-between" style={{ padding: '0 4px' }}>
													<span style={{ color: '#8c8c8c', fontSize: '12px' }}>法定代表人</span>
													<span style={{ fontWeight: 500, fontSize: '12px' }}>
														{record.organizationVO?.extInfo?.operName || '无'}
													</span>
												</Flex>
											</Flex>
										) : (
											<div style={{
												color: '#8c8c8c',
												fontSize: '12px',
												padding: '8px 4px',
												textAlign: 'center'
											}}>
												该用户尚未通过企业认证
											</div>
										)}
									</div>

									{/* 时间信息卡片 */}
									<div style={{
										background: '#fff',
										padding: '12px',
										borderRadius: '4px',
										boxShadow: '0 1px 2px rgba(0,0,0,0.06)',
										maxWidth: '300px',
										flex: 1
									}}>
										<div style={{
											color: '#262626',
											fontSize: '13px',
											fontWeight: 500,
											marginBottom: '8px',
											paddingBottom: '4px',
											borderBottom: '1px solid #f0f0f0'
										}}>时间信息</div>
										<Flex vertical gap={6}>
											<Flex justify="space-between" style={{ padding: '0 4px' }}>
												<span style={{ color: '#8c8c8c', fontSize: '12px' }}>最近访问</span>
												<span style={{ fontWeight: 500, fontSize: '12px' }}>{getLastVisitDate(record)}</span>
											</Flex>
											<Flex justify="space-between" style={{ padding: '0 4px' }}>
												<span style={{ color: '#8c8c8c', fontSize: '12px' }}>创建时间</span>
												<span style={{ fontWeight: 500, fontSize: '12px' }}>{record.createTime}</span>
											</Flex>
											<Flex justify="space-between" style={{ padding: '0 4px' }}>
												<span style={{ color: '#8c8c8c', fontSize: '12px' }}>标签</span>
												<span style={{ fontWeight: 500, fontSize: '12px' }}>{record.memo || '无'}</span>
											</Flex>
										</Flex>
									</div>

									{/* 点数信息卡片 */}
									<div style={{
										background: '#fff',
										padding: '12px',
										borderRadius: '4px',
										boxShadow: '0 1px 2px rgba(0,0,0,0.06)',
										maxWidth: '300px',
										flex: 1
									}}>
										<div style={{
											color: '#262626',
											fontSize: '13px',
											fontWeight: 500,
											marginBottom: '8px',
											paddingBottom: '4px',
											borderBottom: '1px solid #f0f0f0'
										}}>点数信息</div>
										<Flex vertical gap={6}>
											<Flex justify="space-between" style={{ padding: '0 4px' }}>
												<span style={{ color: '#8c8c8c', fontSize: '12px' }}>缪斯点</span>
												<span style={{ fontWeight: 500, fontSize: '12px' }}>{record.imagePoint || 0}</span>
											</Flex>
											<Flex justify="space-between" style={{ padding: '0 4px' }}>
												<span style={{ color: '#8c8c8c', fontSize: '12px' }}>体验点</span>
												<span style={{ fontWeight: 500, fontSize: '12px' }}>{record.experiencePoint || 0}</span>
											</Flex>
											<Flex justify="space-between" style={{ padding: '0 4px' }}>
												<span style={{ color: '#8c8c8c', fontSize: '12px' }}>赠送图片</span>
												<span style={{ fontWeight: 500, fontSize: '12px' }}>{record.givePoint || 0}</span>
											</Flex>
										</Flex>
									</div>

                  {/* 邀请注册信息卡片 */}
                  {record?.inviteRegister &&
                    <div style={{
                      background: '#fff',
                      padding: '12px',
                      borderRadius: '4px',
                      boxShadow: '0 1px 2px rgba(0,0,0,0.06)',
                      maxWidth: '300px',
                      flex: 1
                    }}>
                      <div style={{
                        color: '#262626',
                        fontSize: '13px',
                        fontWeight: 500,
                        marginBottom: '8px',
                        paddingBottom: '4px',
                        borderBottom: '1px solid #f0f0f0'
                      }}>邀请注册信息</div>
                      <Flex vertical gap={6}>
                        <Flex justify="space-between" style={{ padding: '0 4px' }}>
                          <span style={{ color: '#8c8c8c', fontSize: '12px' }}>邀请人</span>
                          <span style={{ fontWeight: 500, fontSize: '12px' }}>{record?.inviteRegister}</span>
                        </Flex>
                      </Flex>
                    </div>
                  }


								</Flex>
							</div>
						),
						rowExpandable: (record) => true,
					}}
				/>

				<style>
					{`
						.members-user-table .ant-table-row-expand-icon-cell {
							width: 113px !important;
						}
					`}
				</style>

				<div className={'stick-bottom-pagination'}>
					<Pagination
						current={currentPage}
						pageSize={pageSize}
						total={totalCount}
						onChange={handlePageChange}
						showTotal={(total) => `共 ${total} 条记录`}
						showSizeChanger // 允许用户更改每页显示条数
						pageSizeOptions={[10, 20, 50, 100]}
						showQuickJumper // 允许用户快速跳转到某一页
						style={{ textAlign: 'center' }}
					/>
				</div>

			</Flex>

			{/*添加用户和编辑用户的弹窗*/}
			<Modal
				className="members-user-modal"
				title={showUserModalConfig?.title}
				open={showUserModal}
				onOk={() => showUserModalConfig?.onOk()}
				onCancel={handleCancel}
				okText="确定"
				cancelText="取消"
				width={700}
				maskClosable={false}
			>
				<Form form={userForm} layout="horizontal" name="user_form" onValuesChange={onUserFormValuesChange}>

					<Form.Item
						name="roleType"
						label="类型"
						rules={[{ required: true, message: '请选择角色类型!' }]}
						style={{ padding: '0 6px' }}
						labelCol={{ span: 6 }}
						wrapperCol={{ span: 18 }}
						colon={false}
						help={msg4AccountType()}
					>
						<Radio.Group options={RoleTypesItems.filter(e => e.key !== 'SYSTEM')} optionType="button" />
					</Form.Item>

					<Form.Item
						name="nickName"
						label="昵称"
						rules={[{ required: true, message: '请输入昵称!' }]}
						style={{ padding: '0 12px' }}
						labelCol={{ span: 6 }}
						wrapperCol={{ span: 18 }}
						colon={false}
					>
						<Input placeholder="请输入" autoFocus style={{ width: 310 }} allowClear />
					</Form.Item>

					<Form.Item
						name="mobile"
						label="手机号"
						rules={[{ required: true, message: '请输入手机号!' }]}
						style={{ padding: '0 12px' }}
						labelCol={{ span: 6 }}
						wrapperCol={{ span: 18 }}
						colon={false}
					>
						<Space>
							<Input value={userMobile} placeholder="请输入" style={{ width: 310 }} maxLength={11} allowClear showCount />
							{!showUserModalConfig?.uid && (currentUserRoleType === 'DEMO_ACCOUNT' || currentUserRoleType === 'OPERATOR') &&
								<Tooltip title={currentUserRoleType === 'DEMO_ACCOUNT' ? `点击将创建虚拟手机号，默认密码为'bb123456'` : '点击将创建虚拟手机号，请自行设置各自密码'}>
									<Button size={'small'} onClick={() => {
										genTestMobile().then(res => {
											if (res) {
												userForm.setFieldValue('mobile', res);
												setUserMobile(res);
											}
										})
									}}>创建虚拟手机号</Button>
								</Tooltip>
							}
						</Space>
					</Form.Item>

					{isDistributor &&
						<Form.Item
							name="salesType"
							label="渠道类型"
							rules={[{ required: true, message: '请选择渠道类型!' }]}
							style={{ padding: '0 6px' }}
							labelCol={{ span: 6 }}
							wrapperCol={{ span: 18 }}
							colon={false}
							initialValue="AGENT_NORMAL"
						>
							<Radio.Group
								defaultValue={'AGENT_NORMAL'}
								optionType="button"
								buttonStyle={'solid'}
								options={Object.entries(salesTypeMap).map(([_, e]) => {
									return {
										label: e.desc,
										value: e.code,
									}
								})} />
						</Form.Item>
					}

					{showUserModalConfig?.uid &&
						<Form.Item
							name="status"
							label="状态"
							rules={[{ required: true, message: '请选择客户状态!' }]}
							style={{ padding: '0 6px' }}
							labelCol={{ span: 6 }}
							wrapperCol={{ span: 18 }}
							colon={false}
						>
							<Radio.Group options={[
								{ label: '正常', value: 'ENABLED' },
								{ label: '停用', value: 'DISABLED' }
							]} optionType="button" />

						</Form.Item>
					}

					{/*新增商家*/}
					{isMerchant && !showUserModalConfig?.uid &&
            <Form.Item
              name="distributorOrgIdPath"
              label={'销售/渠道商'}
              style={{ padding: '0 12px' }}
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 18 }}
              colon={false}
            >
              <Cascader
                options={allDistributorOrgTrees}
                changeOnSelect
                style={{ width: 310 }}
                placeholder="选择销售/渠道商"
              />
            </Form.Item>
					}

					{/*新增/修改渠道商或商家账号时*/}
					{(isDistributor || isMerchant) &&
						<Form.Item
							label="公司名称"
							name="corpName"
							rules={[{ required: false, message: '请输入公司名称!' }]}
							style={{ padding: '0 12px' }}
							labelCol={{ span: 6 }}
							wrapperCol={{ span: 18 }}
							colon={false}
						>
							<Input placeholder="指派渠道商时建议填写，用于客户页面展示" style={{ width: 310 }} />
						</Form.Item>
					}

					<Form.Item
						label="标签/备注"
						name="memo"
						rules={[{ required: false, message: '请输入备注!' }]}
						style={{ padding: '0 12px' }}
						labelCol={{ span: 6 }}
						wrapperCol={{ span: 18 }}
						colon={false}
					>
						<InputWithTags rows={1} onlyTagName={true}
							tagsType={TagType.MERCHANT_MEMO}
							placeholder={'请输入备注'} />
					</Form.Item>

					{!showUserModalConfig?.uid && isMerchant &&
						<Form.Item
							label="是否送缪斯点"
							name="needInitMusePoints"
							style={{ padding: '0 12px' }}
							labelCol={{ span: 6 }}
							wrapperCol={{ span: 18 }}
							colon={false}
							help={<span style={{ color: 'red' }}>说明：送120缪斯点后，客户不再能充值99元体验套餐</span>}
						>
							<Radio.Group options={[
								{ label: '不送', value: 'N' },
								{ label: '送120点', value: 'Y' }
							]} optionType="button" buttonStyle={'solid'} />
						</Form.Item>
					}

				</Form>
			</Modal>

			<Modal
				className="members-user-modal"
				title={'调整缪斯点'}
				open={showTopup}
				onCancel={() => setShowTopup(false)}
				okText={<Popconfirm title={`请确认缪斯点数`}
					description={<div>调整后该账户缪斯点将从<span
						style={{ color: 'red' }}>{topupForm.getFieldValue('imagePoint')}</span>点调整为<span
							style={{ color: 'red' }}>{Number(topupForm.getFieldValue('imagePoint')) + Number(adjustPoint)}</span>点
					</div>}
					onConfirm={() => {
						topupForm.validateFields();
						topupForm.submit();
					}}>确定</Popconfirm>}
				cancelText="取消"
				width={375}
				maskClosable={false}
			>
				<Form form={topupForm} layout="horizontal" name="user_form" onFinish={handleAdjustMuse}>
					<Form.Item label="id" name="id" style={{ padding: '0 12px' }}
						labelCol={{ span: 6 }}
						wrapperCol={{ span: 18 }}
						colon={false}>
						<Input disabled={true} style={{ width: 200 }} />
					</Form.Item>
					<Form.Item
						name="nickName"
						label="昵称"
						rules={[{ required: false, message: '请输入昵称!' }]}
						style={{ padding: '0 12px' }}
						labelCol={{ span: 6 }}
						wrapperCol={{ span: 18 }}
						colon={false}
					>
						<Input disabled={true} style={{ width: 200 }} />
					</Form.Item>
					<Form.Item
						name="imagePoint"
						label="原缪斯点"
						rules={[{ required: false, message: '请输入缪斯点!' }]}
						style={{ padding: '0 12px' }}
						labelCol={{ span: 6 }}
						wrapperCol={{ span: 18 }}
						colon={false}
					>
						<Input disabled={true} style={{ width: 200 }} />
					</Form.Item>
					<Form.Item
						name="adjustMuse"
						label="调整缪斯点"
						rules={[{ required: true, message: '请输入缪斯点!' }]}
						style={{ padding: '0 12px' }}
						labelCol={{ span: 6 }}
						wrapperCol={{ span: 18 }}
						colon={false}
					>
						<Input placeholder="正数为增加、负数为减少" style={{ width: 200 }}
							onChange={e => setAdjustPoint(Number(e.target.value))} />
					</Form.Item>
					<Form.Item
						name="memo"
						label="备注"
						rules={[{ required: true, message: '请输入备注!' }]}
						style={{ padding: '0 12px' }}
						labelCol={{ span: 6 }}
						wrapperCol={{ span: 18 }}
						colon={false}
					>
						<Input.TextArea rows={3} placeholder="请输入" style={{ width: 200 }} />
					</Form.Item>
				</Form>
			</Modal>

			<Modal
				className="members-user-modal"
				title={'赠送'}
				open={showGive}
				onCancel={() => setShowGive(false)}
				okText={(
          <Popconfirm title={`调整后该账户`}
                      style={{width: 300}}
                      description={(
                        <Flex vertical={true}>
                          {Number(adjustExperiencePoint) != 0 && <div>体验点将从
                              <span style={{ color: 'red' }}>{giveForm.getFieldValue('experiencePoint')}</span>点调整为
                            <span style={{ color: 'red' }}>{Number(giveForm.getFieldValue('experiencePoint')) + Number(adjustExperiencePoint)}</span>点
                          </div>}

                          {Number(adjustGivePoint) != 0 && <div>赠送图片数将从
                            <span style={{ color: 'red' }}>{giveForm.getFieldValue('givePoint')}</span>点调整为
                            <span style={{ color: 'red' }}>{Number(giveForm.getFieldValue('givePoint')) + Number(adjustGivePoint)}</span>点
                          </div>}
                        </Flex>
                      )}
                      onConfirm={() => {
                        giveForm.validateFields();
                        giveForm.submit();
                      }}>确定</Popconfirm>
        )}
				cancelText="取消"
				width={475}
				maskClosable={false}
			>
				<Form form={giveForm} layout="horizontal" name="user_form" onFinish={handleGive}>
					<Form.Item label="id" name="id" style={{ padding: '0 12px' }}
						labelCol={{ span: 6 }}
						wrapperCol={{ span: 18 }}
						colon={false}>
						<Input disabled={true} style={{width: 100}}/>
					</Form.Item>
					<Form.Item
						name="nickName"
						label="昵称"
						rules={[{ required: false, message: '请输入昵称!' }]}
						style={{ padding: '0 12px' }}
						labelCol={{ span: 6 }}
						wrapperCol={{ span: 18 }}
						colon={false}
					>
						<Input disabled={true} style={{ width: 200 }} />
					</Form.Item>
          <Form.Item
            name="experiencePoint"
            label="当前体验点"
            rules={[{ required: true, message: '请输入体验点!' }]}
            style={{ padding: '0 12px' }}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            colon={false}
          >
            <Input disabled={true} style={{ width: 200 }} />
          </Form.Item>
          <Form.Item
            name="givePoint"
            label="当前赠送图片"
            rules={[{ required: true, message: '请输入赠送图片数!' }]}
            style={{ padding: '0 12px' }}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            colon={false}
          >
            <Input disabled={true} style={{ width: 200 }} />
          </Form.Item>
					<Form.Item
						name="adjustExperiencePoint"
						label="调整体验点"
            rules={[
              {
                validator: (rule, value, callback) => {
                  const values = giveForm.getFieldsValue();
                  validateAtLeastOne(rule, values, callback);
                }
              }
            ]}
						style={{ padding: '0 12px' }}
						labelCol={{ span: 6 }}
						wrapperCol={{ span: 18 }}
						colon={false}
					>
						<Input placeholder="正数为增加、负数为减少" style={{ width: 200 }} onChange={e => setAdjustExperiencePoint(Number(e.target.value))}/>
					</Form.Item>
					<Form.Item
						name="adjustGivePoint"
						label="调整赠送图片"
            rules={[
              {
                validator: (rule, value, callback) => {
                  const values = giveForm.getFieldsValue();
                  validateAtLeastOne(rule, values, callback);
                }
              }
            ]}
						style={{ padding: '0 12px' }}
						labelCol={{ span: 6 }}
						wrapperCol={{ span: 18 }}
						colon={false}
					>
						<Input placeholder="正数为增加、负数为减少" style={{ width: 200 }} onChange={e => setAdjustGivePoint(Number(e.target.value))}/>
					</Form.Item>
				</Form>
			</Modal>

			{/* 录入签约时间 */}
			<Modal
				title={contractForm.getFieldValue('contractDate') ? "查看签约信息" : "录入签约时间"}
				open={showContract}
				onCancel={() => setShowContract(false)}
				onOk={() => {
					contractForm.validateFields().then(values => {
						handleContract(values);
					}).catch(info => {
						console.log('验证失败:', info);
					});
				}}
				okText={contractForm.getFieldValue('contractDate') ? "关闭" : "确定"}
				cancelText={contractForm.getFieldValue('contractDate') ? null : "取消"}
				width={450}
				centered
			>
				<Form
					form={contractForm}
					layout="horizontal"
					name="contract_form"
					labelCol={{ span: 6 }}
					wrapperCol={{ span: 18 }}
				>
					{/* 用户信息展示 */}
					<div style={{
						padding: '16px 0',
						borderBottom: '1px solid #f0f0f0',
						marginBottom: '16px'
					}}>
						<div style={{ marginBottom: '8px' }}>
							<span style={{ color: '#666', fontSize: '14px' }}>用户昵称：</span>
							<span style={{ fontWeight: 500 }}>{contractForm.getFieldValue('nickName')}</span>
						</div>
						<div>
							<span style={{ color: '#666', fontSize: '14px' }}>公司名称：</span>
							<span style={{ fontWeight: 500 }}>{contractForm.getFieldValue('corpName') || '未填写'}</span>
						</div>
					</div>

					{/* 隐藏字段 */}
					<Form.Item name="id" hidden>
						<Input />
					</Form.Item>
					<Form.Item name="nickName" hidden>
						<Input />
					</Form.Item>
					<Form.Item name="corpName" hidden>
						<Input />
					</Form.Item>

					{/* 签约日期选择 */}
					<Form.Item
						name="contractDate"
						label="签约日期"
						rules={[{ required: true, message: '请选择签约日期!' }]}
						extra={contractForm.getFieldValue('contractDate') ?
							<span style={{ color: '#999' }}>签约日期已设定，不可修改</span> :
							<span style={{ color: '#666' }}>请选择签约日期</span>
						}
						help={<span style={{ color: 'red' }}>将自动创建考核计划</span>}
					>
						<DatePicker
							style={{ width: '100%' }}
							placeholder="请选择签约日期"
							format="YYYY-MM-DD"
							disabled={!!contractForm.getFieldValue('contractDate')}
						/>
					</Form.Item>
				</Form>
			</Modal>

			<Modal title={'客户审核'}
				open={showUserReviewModal}
				onCancel={() => setShowUserReviewModal(false)}
				okButtonProps={{ style: { display: reviewUserModalReadonly ? 'none' : '' } }}
				onOk={() => {
					userReviewForm.validateFields().then(values => {
						// @ts-ignore
						reviewUser({ ...values, id: reviewingUser.id }).then(res => {
							if (res) {
								message.success('操作成功');
								setShowUserReviewModal(false);
								userReviewForm.resetFields();
								window.location.reload();
							} else {
								message.error('操作失败');
							}
						});
					});
				}}
			>

				{reviewingUser && (
					<Descriptions bordered column={1}>
						<Descriptions.Item label="公司名称">{reviewingUser.corpName}</Descriptions.Item>
						<Descriptions.Item label="昵称">{reviewingUser.nickName}</Descriptions.Item>
						<Descriptions.Item label="手机号">{reviewingUser.mobile}</Descriptions.Item>
					</Descriptions>
				)
				}

				<Form form={userReviewForm} style={{ marginTop: 16 }}>
					<Form.Item
						name="reviewAction"
						label="是否通过"
						rules={[{ required: true, message: '请审批!' }]}
					>
						<Radio.Group disabled={reviewUserModalReadonly}>
							<Radio value={'PASS'}>通过</Radio>
							<Radio value={'REJECT'}>拒绝</Radio>
						</Radio.Group>
					</Form.Item>
					<Form.Item
						name="reviewMemo"
						label="备注"
						rules={[{ required: false, message: '请输入备注!' }]}
					>
						<Input readOnly={reviewUserModalReadonly} />
					</Form.Item>
				</Form>
			</Modal>

			{resetPswdUser &&
				<Modal open={true} onCancel={clearPswd} title={'修改密码'} onOk={commitResetPswd}>
					<Flex vertical gap={8}>
						<div>新密码：</div>
						<Input.Password type="text" placeholder="请输入新密码" onChange={e => setPswd(e.target.value)} />
						<div className={'color-a0'}>请设置8-16位新密码，须为数字与字母组合</div>
					</Flex>
				</Modal>
			}

			{showTopup2UserModal &&
				<Modal open={showTopup2UserModal}
					title={'录入转账'}
					width={1000} okButtonProps={{hidden: topup2UserForm.getFieldValue('collectMoneyType')!=='offlinePayment'}}
					onOk={() => {
						topup2UserForm.validateFields().then(values => {
							topup2User(values).then(res => {
								if (res) {
									setShowTopup2UserModal(false);
									topup2UserForm.resetFields();
									notification.success({ message: '操作成功' });
									fetchData();
								}
							});
						});
					}}
					onCancel={() => {
						setShowTopup2UserModal(false);
						topup2UserForm.resetFields();
					}}>

					<Form form={topup2UserForm}
						style={{ marginTop: 16, marginLeft: 10 }}
						labelCol={{ span: 4 }}
						wrapperCol={{ span: 20 }}
						colon={false}>
						<Form.Item
							name="userId"
							label={'客户ID'}
							hidden={true}
						>
							<Input />
						</Form.Item>
						<Form.Item
							label="客户"
							name={'userName'}
						>
							<span>{topup2UserForm.getFieldValue('userName')}</span>
						</Form.Item>

						<Form.Item label={'产品套餐'} name={'productCode'} initialValue={selectedPlan?.code}>
							<Radio.Group options={plans?.map(p => ({ label: `${p.name} ${p.amount}`, value: p.code }))}
								optionType={'button'}
								value={selectedPlan?.code}
								onChange={(e) => {
									let plan = plans?.find(p => p.code === e.target.value);
									setSelectedPlan(plan);
									topup2UserForm.setFieldsValue({ ...plan, memo: '线下转账' + plan?.amount });
								}}>
							</Radio.Group>
						</Form.Item>

						{selectedPlan && (<>
							<Form.Item
								name={'amount'}
								label="金额"
								rules={[{ required: true, message: '请输入金额!' }]}
							>
								<InputNumber addonAfter={'元'} style={{ width: '100%' }} min={0.01} />
							</Form.Item>
							<Form.Item
								name={'musePoint'}
								label="充值muse点"
								help={<span style={{color: 'red'}}>如果之前已经手工充值点数的，请将上面改为0，如果创建二维码收款，客户充值完成将自动相应加点</span>}
							>
								<InputNumber style={{ width: '100%' }} min={0} step={1} />
							</Form.Item>
							<Form.Item
								name={'creativeImgCountGave'}
								label="充值赠送图片数"
                help={<span style={{color: 'red'}}>如果之前已经手工充值点数的，请将上面改为0，如果创建二维码收款，客户充值完成将自动相应加点</span>}
								rules={[
									{
										validator: (_, value) => {
											if (value === undefined || value === null) {
												return Promise.resolve(); // 允许空值
											}
											if (Number.isInteger(value)) {
												return Promise.resolve(); // 验证通过
											} else {
												return Promise.reject(new Error('请输入整数')); // 报错提醒
											}
										},
									},
								]}
							>
								<InputNumber style={{ width: '100%' }} min={0} step={1} />
							</Form.Item>
							<Form.Item
								name={'topupDate'}
								label={'转账发生日期'}
								initialValue={getCurrentDate()}
								rules={[
									{ required: true, message: '请输入转账发生日期' },
									{
										pattern: /^\d{4}-\d{2}-\d{2}$/,
										message: '日期格式无效，必须是 YYYY-MM-DD',
									},
									{
										validator: (_, value) => {
											const date = new Date(value);
											if (!isNaN(date.getTime()) && value === date.toISOString().slice(0, 10)) {
												return Promise.resolve();
											}
											return Promise.reject(new Error('请输入有效的日期'));
										},
									},
								]}
							>
								<Input />
							</Form.Item>

							<Form.Item
								name={'collectMoneyType'}
								label={'收款方式'}
                help={<span style={{color: 'red'}}>如果创建二维码收款，客户充值完成将自动相应加点</span>}
								initialValue={'offlinePayment'}>
								<Radio.Group
									options={[
                    { label: '已经收到线下打款', value: 'offlinePayment' },
                    { label: '创建二维码收款', value: 'qrCode' },
                  ]}
									optionType={'button'}
									onChange={e => {
										if (e.target.value === 'qrCode') {
											let amount = topup2UserForm.getFieldValue('amount');
											if (!amount) {
												message.error('请先输入金额');
												return;
											}
											if (amount < 0.01) {
												message.error('金额不能小于0.01元');
												return;
											}

											topup2UserForm.setFieldValue('memo', '后台二维码收款');
                      setShowTopupModal2CollectMoney(true);

										} else if (e.target.value === 'offlinePayment'){
											topup2UserForm.setFieldValue('memo', '线下转账');
										}
									}}
								>
								</Radio.Group>
							</Form.Item>

							<Form.Item
								name={'memo'}
								label="备注"
							>
								<Input />
							</Form.Item>
						</>)}
					</Form>
				</Modal>
			}

			{showTopupModal2CollectMoney &&
				<TopupModal
          visible={showTopupModal2CollectMoney}
					onClose={() => {
						setShowTopupModal2CollectMoney(false);
					}}
					onPaySuccessWithOrderNo={(orderNo: string) => {
						console.log('onPaySuccessWithOrderNo', orderNo);
						setShowTopupModal2CollectMoney(false);
						topup2UserForm.setFieldValue('collectedMoneyOrderNo', orderNo);
					}}
					backroleAgentOrder={true}
					payMasterId={topup2UserForm.getFieldValue('userId')}
					customOrderPlan={{
            ...selectedPlan,
            amount: topup2UserForm.getFieldValue('amount'),
            musePoint: topup2UserForm.getFieldValue('musePoint'),
            creativeImgCountGave: topup2UserForm.getFieldValue('creativeImgCountGave')
        }}
				/>
			}

			{showAssignDistributorModal &&
				<Modal open={showAssignDistributorModal}
					onCancel={() => {
						setShowAssignDistributorModal(false);
						assignDistributorform.resetFields();
					}}
					title={'指派销售/渠道商'}
					onOk={() => {
						assignDistributorform.validateFields().then(values => {
							assignCustomer2Distributor(values).then(res => {
								if (res) {
									message.success('操作成功');
									setShowAssignDistributorModal(false);
									assignDistributorform.resetFields();
									window.location.reload();
								} else {
									message.error('操作失败');
								}
							});
						});
					}}>
					<Form form={assignDistributorform} style={{ marginTop: 16 }}>
						<Form.Item
							name="userId"
							hidden={true}
						>
							<Input readOnly={true} />
						</Form.Item>
						<Form.Item
							name="userName"
							label={'商家昵称'}
						>
							<span>{assignDistributorform.getFieldValue('userName')}</span>
						</Form.Item>
						<Form.Item
							name="userCorpName"
							label={'商家公司名'}
							rules={[{ required: false, message: '请输入商家公司名!' }]}
						>
							<Input disabled={!!assignDistributorform.getFieldValue('userCorpName')} />
						</Form.Item>
						<Form.Item
							name="distributorOrgIdPath"
							label={'销售/渠道商'}
						>
              <Cascader
                options={allDistributorOrgTrees}
                changeOnSelect
                placeholder="选择销售/渠道商"
              />
						</Form.Item>
					</Form>
				</Modal>
			}

			{showAssignPromptUserModal &&
				<Modal open={showAssignPromptUserModal}
					onCancel={() => {
						setShowAssignPromptUserModal(false);
						assignPromptUserform.resetFields();
					}}
					title={'指派工程师'}
					onOk={() => {
						assignPromptUserform.validateFields().then(values => {
							createUserProfile(values).then(res => {
								if (res) {
									message.success('操作成功');
									setShowAssignPromptUserModal(false);
									assignPromptUserform.resetFields();
									fetchData();
								} else {
									message.error('操作失败');
								}
							});
						});
					}}>
					<Form form={assignPromptUserform} style={{ marginTop: 16 }}>
						<Form.Item
							name="uid"
							hidden={true}
						>
							<Input readOnly={true} />
						</Form.Item>
						<Form.Item
							name="profileKey"
							hidden={true}
						>
							<Input readOnly={true} />
						</Form.Item>
						<Form.Item
							name="profileVal"
							label={'工程师'}
							rules={[{ required: true, message: '请选择工程师!' }]}
						>
							<Select
								placeholder="选择工程师"
								style={{ width: '100%' }}
								value={assignPromptUserform.getFieldValue('promptUserId')}
								onChange={(value) => {
									assignPromptUserform.setFieldValue('promptUserId', value);
								}}
							>
								{allAdmins?.map(s => (
									<Select.Option key={s.id} value={String(s.id)}>
										{s.nickName}
									</Select.Option>
								))}
							</Select>
						</Form.Item>
					</Form>
				</Modal>
			}

			{showOrgDetailModal &&
				<Modal open={showOrgDetailModal}
					centered
					title={'企业详情'}
					onCancel={() => {
						setShowOrgDetailModal(false);
						setCurrentOrgDetail(null);
					}}
					width={800}
					footer={null}>
					<Descriptions
						bordered
						column={1}
						contentStyle={{ padding: '8px 16px' }}
						labelStyle={{
							padding: '8px 16px',
							width: '160px',
							backgroundColor: '#fafafa'
						}}
						size="small"
					>
						{currentOrgDetail?.extInfo && (
							<>
								<Descriptions.Item label="企业名称">{currentOrgDetail.extInfo.name}</Descriptions.Item>
								<Descriptions.Item label="统一社会信用代码">{currentOrgDetail.extInfo.creditCode}</Descriptions.Item>
								<Descriptions.Item label="注册号">{currentOrgDetail.extInfo.no}</Descriptions.Item>
								<Descriptions.Item label="所在省份">{currentOrgDetail.extInfo.provinceInfo}</Descriptions.Item>
								<Descriptions.Item label="企业注册地">{currentOrgDetail.extInfo.address}</Descriptions.Item>
								<Descriptions.Item label="法定代表人">{currentOrgDetail.extInfo.operName}</Descriptions.Item>
								<Descriptions.Item label="企业状态">{currentOrgDetail.extInfo.status}</Descriptions.Item>
								<Descriptions.Item label="成立时间">{dayjs(currentOrgDetail.extInfo.startDate).format('YYYY-MM-DD')}</Descriptions.Item>
							</>
						)}
					</Descriptions>
				</Modal>
			}

		</PageContainer>
	);
};

