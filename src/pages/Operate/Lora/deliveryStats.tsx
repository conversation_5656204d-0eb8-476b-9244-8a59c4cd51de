import React, { useEffect, useState } from 'react';
import { Column } from '@ant-design/charts';
import { PageContainer } from '@ant-design/pro-components';
import { statsDelivery } from '@/services/DataController';
import { DatePicker, Flex } from 'antd';

const { RangePicker } = DatePicker;

const DeliveryStats: React.FC<unknown> = () => {
  const [data, setData] = useState<Array<any>>([]);
  const [dates, setDates] = useState([]);

  useEffect(() => {
    let startDate = null;
    let endDate = null;
    if (dates && dates.length > 1) {
      // @ts-ignore
      startDate = dates[0].format('YYYY-MM-DD');
      // @ts-ignore
      endDate = dates[1].format('YYYY-MM-DD');
    }
    fetchData(startDate, endDate);
  }, [dates]);

  function fetchData(startDate: string | null, endDate: string | null) {
    statsDelivery(startDate, endDate).then(res => {
      if (res != null) {
        // 转换数据格式
        const data = res.flatMap(item => [
          {
            date: item.date,
            type: 'VIP',
            status: '24小时交付',
            value: item.vip.in24 + item.vip.unknown,
            rate: item.vip.total === 0 ? 0 : (item.vip.in24 + item.vip.unknown) / item.vip.total,
          },
          {
            date: item.date,
            type: 'VIP',
            status: '48小时交付',
            value: item.vip.in48,
            rate: item.vip.total === 0 ? 0 : item.vip.in48 / item.vip.total,
          },
          {
            date: item.date,
            type: 'VIP',
            status: '48小时以上',
            value: item.vip.other,
            rate: item.vip.total === 0 ? 0 : item.vip.other / item.vip.total,
          },
          {
            date: item.date,
            type: 'VIP',
            status: '未交付',
            value: item.vip.todo,
            rate: item.vip.total === 0 ? 0 : item.vip.todo / item.vip.total,
          },
          {
            date: item.date,
            type: '普通',
            status: '24小时交付',
            value: item.normal.in24 + item.normal.unknown,
            rate: item.normal.total === 0 ? 0 : (item.normal.in24 + item.normal.unknown) / item.normal.total,
          },
          {
            date: item.date,
            type: '普通',
            status: '48小时交付',
            value: item.normal.in48,
            rate: item.normal.total === 0 ? 0 : item.normal.in48 / item.normal.total,
          },
          {
            date: item.date,
            type: '普通',
            status: '48小时以上',
            value: item.normal.other,
            rate: item.normal.total === 0 ? 0 : item.normal.other / item.normal.total,
          },
          {
            date: item.date,
            type: '普通',
            status: '未交付',
            value: item.normal.todo,
            rate: item.normal.total === 0 ? 0 : item.normal.todo / item.normal.total,
          },
          {
            date: item.date,
            type: '方式',
            status: '人工交付',
            value: item.total - item.autoDelivery,
            rate: (item.total - item.autoDelivery) / item.total,
          },
          {
            date: item.date,
            type: '方式',
            status: '自动交付',
            value: item.autoDelivery,
            rate: item.autoDelivery / item.total,
          },
        ]);
        setData(data);
      }
    });
  }

  const handleDateChange = (newDates) => {
    console.log('handleDateChange:', newDates);
    setDates(newDates);
  };

  const config = {
    data: data,
    isStack: false,
    xField: 'date',
    yField: 'value',
    seriesField: 'type',
    colorField: 'status',
    groupField: 'type',
    stack: {
      groupBy: ['x', 'series'],
      series: true,
    },
    // 图表的颜色可以通过 color 配置
    color: ['#1890ff', '#facc14', '#2fc25b', '#f04864'],
    label: {
      // position: 'middle', // 数据标签显示在柱子中间
      style: {
        fill: '#000000',
        opacity: 0.6,
      },
      text: (d) => `${d.value} (${(d.rate * 100).toFixed(0)}%)`,
      // textBaseline: 'bottom',
      position: 'inside',
      // formatter: (item) => `${item.status}%`,  // 在 value 后面添加百分比符号
      // text: (d) => `${d.status}`,
    },
    tooltip: (item) => {
      return { origin: item };
    },
    emptyContent: '暂无数据',
    height: window.innerHeight - 200,
    interactions: [{ type: 'active-region' }],
    interaction: {
      tooltip: {
        shared: true,
        showMarkers: true,
        render: (e, { title, items }) => {
          return (
            <div>
              <h4>{title}</h4>
              {items.map((item) => {
                const { name, color, origin } = item;
                return (
                  <div>
                    <div style={{ margin: 0, display: 'flex', justifyContent: 'space-between', gap: 8 }}>
                      <div>
                        <span
                          style={{
                            display: 'inline-block',
                            width: 6,
                            height: 6,
                            borderRadius: '50%',
                            backgroundColor: color,
                            marginRight: 6,
                          }}
                        ></span>
                        <span>
                          {name}-{origin['status']}
                        </span>
                      </div>
                      <b>{origin['value']}</b>
                    </div>
                  </div>
                );
              })}
            </div>
          );
        },
      },
    },

    legend: {
      position: 'top-left',
    },
  };

  return (
    <PageContainer style={{ padding: 12 }}>
      <Flex vertical gap={16}>
        <div className={'text16 weight color-1a'}>交付数据统计</div>
        <Flex gap={8} align={'center'}>
          统计周期：<RangePicker onChange={handleDateChange} placeholder={['开始日期', '结束日期']} />
        </Flex>
        {data.length <= 0 &&
          <div>暂无数据</div>
        }
        {data.length > 0 &&
          <Column key={'delivery'} {...config} />
        }
      </Flex>
    </PageContainer>
  );

};

export default DeliveryStats;