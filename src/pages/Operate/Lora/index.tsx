import { <PERSON><PERSON>ontainer } from '@ant-design/pro-components';
import React, { CSSProperties, useEffect, useRef, useState, useCallback } from 'react';
import {
  addLora2Vip,
  addModelDemoTag,
  addSystemModel,
  AllModelStatus,
  assignLoraTo,
  assignPlatformOperator,
  cloneLora,
  confirmCanDeliver,
  confirmTrainLora,
  copyModelToSystem,
  cutoutAgain,
  delModel,
  FileVO, getMaterialModelById,
  getSubTrainStatusDesc,
  getTrainDetail,
  isDemoTag,
  MaterialModelWithBlogs,
  MODEL_LABEL_TYPES,
  ModelTrainDetailVO,
  modifyModelName,
  queryMaterialModelListWithBlogs, queryMaterialSubIds,
  syncToImageCase,
  updateExtInfo,
  updateLabelFiles,
  updateMaterialModel,
  updateModelReviewer,
  UpdateReviewerRequest,
} from '@/services/MaterialModelController';
import IconFont from '@/components/IconFont';
import {
  Al<PERSON>,
  AutoComplete,
  Button,
  Checkbox,
  Col,
  Dropdown,
  Flex,
  Form,
  Image,
  Input,
  InputNumber,
  message,
  Modal,
  notification,
  Pagination,
  Popconfirm,
  Radio,
  Row,
  Segmented,
  Select,
  Space,
  Switch,
  Tabs,
  Tooltip,
  Typography,
  Upload,
  UploadFile,
  UploadProps,
} from 'antd';
import './index.less';
import TextArea from 'antd/lib/input/TextArea';
import {
  deepCopy,
  download,
  FileType,
  getBase64, getUserInfo,
  isProdEnv,
  objectToPathObject,
  pathObjectToObject,
} from '@/utils/utils';
import {
  CopyOutlined,
  DeleteOutlined,
  DiffOutlined,
  DownOutlined,
  LogoutOutlined,
  MinusOutlined,
  PlusOutlined,
  RedoOutlined,
  RollbackOutlined,
  ThunderboltOutlined,
  UploadOutlined,
  EditOutlined,
  ExperimentOutlined, HighlightOutlined,
} from '@ant-design/icons';
import { UPLOAD_LORA_URL, UPLOAD_PRO_URL } from '@/constants';
import { compressFile } from '@/utils/imageUtils';
import ImgPreview from '@/components/ImgPreview';
import { helix } from 'ldrs';
import { ClothTypes } from '@/services/MaterialInfoController';
import { queryAllMaster, UserVO } from '@/services/UserController';
import { LoraType } from '@/services/CreativeController';
import {
  AgeRanges,
  AllClothTypeCheckBoxOptions,
  AllClothTypes,
  ClothCategoryGroup,
  ElementConfig,
  getClothCategoryCfg,
  getElementConfig,
  getMerchantRecentElement,
} from '@/services/ElementController';
import LoraImageSelector from '@/components/Operate/LoraImageSelector';
import { batchDownload, uploadFile } from '@/services/FileController';
import {
  MerchantPreference,
  queryAllOperators,
  queryConfigByKeys,
  queryLoraVipCfg,
} from '@/services/SystemController';
import ChildrenTabCard, { processChildrenByName } from '@/components/Operate/ChildrenTabCard';
import MerchantPreferenceSetting from '@/components/Operate/MerchantPreferenceSetting';
import { ClothColorTitle, RetrainBtn } from '@/components/Operate/LoraComponent';
import { queryCustomersOptionsByDistributorMasterId } from '@/services/DistributorController';
import { queryGarmentList } from '@/services/PromptDictController';
import type { SelectProps } from 'antd/es/select';
import ExperienceOpenCfgSetting from '@/components/Operate/ExperienceOpenCfgSetting';
import LoraImageModal from '@/components/Operate/LoraImageModal';
import SelectTrainingMaterialModal from './SelectTrainingMaterialModal';
import LoraPromptsSwitch, { formatPrompt } from '@/components/Lora/LoraPromptsSwitch';
import { MiniTag } from '@/components/Common/CommonComponent';
import SystemConfigSwitch from '@/components/Common/SystemConfigSwitch';
import { SELECT_AGE_RANGE_OPTIONS } from '@/components/AgeRangeSelector';
import {
  TestImageStatus,
  PromptEngineerNick,
  ReviewerNick,
  ReviewStatus,
} from '@/components/Lora/WorkflowTask';
import { useExperimental } from '@/components/Operate/Experimental';
import ClothCategorySelect from '@/components/Operate/ClothCategory';
import debounce from 'lodash/debounce';

const { Option } = Select;

helix.register();

const clothStyle: CSSProperties = { width: 90, padding: 0, textAlign: 'center' };
const clothStyleFlux: CSSProperties = { ...clothStyle, color: 'red' };
const AllLoraClothTypes: Array<{ key: string; value: string; label: string, style: CSSProperties }> = [
  { key: 'v_1,front view,whole body', value: 'v_1,front view,whole body', label: 'sdxl正面全身', style: clothStyle },
  { key: 'v_1,front view,lower body', value: 'v_1,front view,lower body', label: 'sdxl正面下装', style: clothStyle },
  { key: 'v_1,back view,whole body', value: 'v_1,back view,whole body', label: 'sdxl背面全身', style: clothStyle },
  { key: 'v_1,back view,lower body', value: 'v_1,back view,lower body', label: 'sdxl背面下装', style: clothStyle },
  {
    key: 'v_2,front view,whole body',
    value: 'v_2,front view,whole body',
    label: 'flux正面全身',
    style: clothStyleFlux,
  },
  {
    key: 'v_2,front view,lower body',
    value: 'v_2,front view,lower body',
    label: 'flux正面下装',
    style: clothStyleFlux,
  },
  {
    key: 'v_2,front view,upper body',
    value: 'v_2,front view,upper body',
    label: 'flux正面上装',
    style: clothStyleFlux,
  },
  { key: 'v_2,back view,whole body', value: 'v_2,back view,whole body', label: 'flux背面全身', style: clothStyleFlux },
  { key: 'v_2,back view,lower body', value: 'v_2,back view,lower body', label: 'flux背面下装', style: clothStyleFlux },
  { key: 'v_2,back view,upper body', value: 'v_2,back view,upper body', label: 'flux背面上装', style: clothStyleFlux },
];
const internalDisableReasonOptions: string[] = [
  '拍摄不规范',
  '上传素材不规范',
  '客户要求返点',
  '衣服还原不好',
];

const sortClothType = () => {
  return (a, b) => {
    const aHasV2 = a.type.includes('front view');
    const bHasV2 = b.type.includes('front view');
    if (aHasV2 && !bHasV2) {
      return -1;
    } else if (!aHasV2 && bHasV2) {
      return 1;
    } else {
      const aHasWholeBody = a.type.includes('whole body');
      const bHasWholeBody = b.type.includes('whole body');

      if (aHasWholeBody && !bHasWholeBody) {
        return -1;
      } else if (!aHasWholeBody && bHasWholeBody) {
        return 1;
      } else {
        return 0;
      }
    }
  };
};

const DEFAULT_TEST_NUM = 1;
const Lora: React.FC<unknown> = () => {
  const userInfo = getUserInfo();
  // @ts-ignore
  const [models, setModels] = useState<Array<MaterialModelWithBlogs>>([]);
  const [showDialog, setShowDialog] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [imageList, setImageList] = useState<UploadFile[]>([]);
  const [add, setAdd] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [deleteId, setDeleteId] = useState<number>(0);
  const [opItem, setOpItem] = useState<MaterialModelWithBlogs | null>(null);
  const [loraUpload, setLoraUpload] = useState(true);
  const [form] = Form.useForm();

  //训练详情页
  const [showTrainDetailModal, setShowTrainDetailModal] = useState(false);
  const [selectedTrainModelDetail, setSelectedTrainModelDetail] = useState<ModelTrainDetailVO>();

  const [groupedLabelFiles, setGroupedLabelFiles] = useState<Map<string, FileVO[]>>(new Map());
  const [showCutout, setShowCutout] = useState(false);
  const [showSplitTags, setShowSplitTags] = useState(false);
  const [showOriginal, setShowOriginal] = useState(true);

  //更新打标词
  const [updateTxtBtnEnabled, setUpdateTxtBtnEnabled] = useState(false);
  const [updateTxtFiles, setUpdateTxtFiles] = useState<Map<string, string>>(new Map());

  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewIdx, setPreviewIdx] = useState();
  const [previewImgs, setPreviewImgs] = useState();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(24);
  const [masterOptions, setMasterOptions] = useState<Array<any>>([]);

  const [allDistributorMasters, setAllDistributorMasters] = useState<Array<UserVO>>([]);
  const [allAdmins, setAllAdmins] = useState<Array<UserVO>>([]);
  const [selectedDistributorMasterId, setSelectedDistributorMasterId] = useState<number | null>(null);
  const [selectedReviewerId, setSelectedReviewerId] = useState<number | null>(null);
  const [updateReviewer, setUpdateReviewer] = useState<UpdateReviewerRequest>({ id: 0, reviewerId: 0 });
  const [selectedPromptUserId, setSelectedPromptUserId] = useState<number | null>(null);

  const [selectedGarment, setSelectedGarment] = useState<string[] | null>(null);
  const [searchLabelType, setSearchLabelType] = useState<string[] | null>(null);
  const [garmentItems, setGarmentItems] = useState<any[]>([]);

  const [assignLora, setAssignLora] = useState<MaterialModelWithBlogs | null>(null);
  const [assignUserId, setAssignUserId] = useState<number | null>(null);
  const [loraType, setLoraType] = useState<LoraType>('CUSTOM');
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [searchUserId, setSearchUserId] = useState<number | null>(null);
  const [searchClothType, setSearchClothType] = useState<string>('All');
  const [searchAgeRange, setSearchAgeRange] = useState<string>('ALL');
  const [nameLike, setNameLike] = useState<string>();
  const [searchId, setSearchId] = useState<number>();
  // 添加一个新的状态来存储输入框的显示值
  const [searchInputValue, setSearchInputValue] = useState<string>('');
  const [onlyShowHasCase, setOnlyShowHasCase] = useState(false);
  const [customerCase, setCustomerCase] = useState<Array<string>>([]);
  const [onlyShowDemo, setOnlyShowDemo] = useState(false);
  const [canShowTrainParams, setCanShowTrainParams] = useState(false);

  const [loadingTime, setLoadingTime] = useState<number>(0);

  //指定平台运营
  const [relatedOperatorType, setRelatedOperatorType] = useState('all');
  const [relatedOperatorMobile, setRelatedOperatorMobile] = useState<string>('');
  const [allOperators, setAllOperators] = useState<any[]>([]);
  const [relatedOperatorTypeItems, setRelatedOperatorTypeItems] = useState<any[]>([]);

  //打标图片编辑
  const [labelImgFiles, setLabelImgFiles] = useState<Map<string, {
    filePath: string,
    url: string,
    deleted: boolean,
    isTrainingMaterial?: boolean
  }>>(new Map());
  const labelImgInputRefs = useRef({});

  //替换打标词输入框
  const replaceLabelTagsInputRefs = useRef([]);
  //替换 detail_garment_type输入框
  const replaceLabelGarmentTypesInputRefs = useRef([]);

  //关联的测试脸和场景，用于训练完成后自动生成图片
  const [relatedTestFaces, setRelatedTestFaces] = useState<number[]>();
  const [relatedTestScenes, setRelatedTestScenes] = useState<number[]>();
  const [relatedTestNum, setRelatedTestNum] = useState(0);
  const [testClothCollocation, setTestClothCollocation] = useState({});
  const [copyByPreference, setCopyByPreference] = useState(false);

  const [faceList, setFaceList] = useState<ElementConfig[]>([]);
  const [sceneList, setSceneList] = useState<ElementConfig[]>([]);

  //是否需要重新抠图
  const [needCutoutAgain, setNeedCutoutAgain] = useState(false);
  //抠图关键词数组
  const [cutoutKeyword, setCutoutKeyword] = useState<string[]>([]);
  //重新抠图时，是否重新进行图片分析预处理
  const [prepareViewAgainWhenCutoutAgain, setPrepareViewAgainWhenCutoutAgain] = useState(false);
  const [cutoutType, setCutoutType] = useState('default');
  const [clothDetailsPrompt, setClothDetailsPrompt] = useState('');
  const [labelType, setLabelType] = useState<string | undefined>(undefined);

  //抠图时，是否让衣服在图片中占更高比例（实验参数）
  const [cut4ScaleUp, setCut4ScaleUp] = useState('N');

  //缺省v1: sam_vit_h_cloth
  //v2: sam_hq_h_cloth
  const [cutoutModel, setCutoutModel] = useState('sam_vit_h_cloth');

  const [imageSize, setImageSize] = useState('1024');
  const [isSquare, setIsSquare] = useState(false);

  const [loraTrainType, setLoraTrainType] = useState('flux');

  const [trainParams, setTrainParams] = useState({});

  //是否只显示未确认
  const [onlyUnconfirmedLora, setOnlyUnconfirmedLora] = useState(false);
  const [onlyNearingDelivery, setOnlyNearingDelivery] = useState(false);

  //是否只显示VIP和客户
  const [onlyVIPOrCustomerLora, setOnlyVIPOrCustomerLora] = useState(false);

  //是否只显示和我相关的
  const [relatedToMe, setRelatedToMe] = useState(false);

  const [onlyMultiColorSplit, setOnlyMultiColorSplit] = useState(false);

  //是否显示确认训练的弹窗
  const [showConfirmLoraModal, setShowConfirmLoraModal] = useState(false);
  const [merchantPreferenceSetting, setMerchantPreferenceSetting] = useState<MaterialModelWithBlogs | null>(null);
  const [selectedPreferenceIndex, setSelectedPreferenceIndex] = useState(0);
  const [deliveryModel, setDeliveryModel] = useState<MaterialModelWithBlogs | null>(null);

  const [maxTrainStep, setMaxTrainStep] = useState<number>();

  const [recordModelId, setRecordModelId] = useState<number | null>(null);
  const [subModelIds, setSubModelIds] = useState<Array<number>>([]);

  const [onlyExperimental, setOnlyExperimental] = useState(false);
  const { getExperimentalLabel, setExperimental, ExperimentalLabel } = useExperimental();

  // 是否需要手工替换文件（默认为 False）
  const [isManualReplacement, setIsManualReplacement] = useState<number>(0);

  const [confirmLoraForm] = Form.useForm();
  const [deliveryForm] = Form.useForm();

  const defaultFluxSteps = 2500;
  const defaultFluxSteps4MultiColor = 3000;

  const creationSizeOptions: SelectProps['options'] = [
    { label: '768x1024', value: 'THREE_FOUR' },
    { label: '1024x1024', value: 'ONE_ONE' },
    { label: '1340x1785', value: 'THREE_FOUR_LG_N' },
    { label: '1536x1536', value: 'ONE_ONE_LG' },
    { label: '1080x1920', value: 'NINE_SIXTEEN_2K' },
    { label: '1152x1536', value: 'P_1152_1536' },
    { label: '1620x2100', value: 'P_1620_2100' },
    { label: '1200x600', value: 'P_1200_600' },
  ];

  //https://conrain.yuque.com/org-wiki-conrain-pcgdb4/cvib8a/dirymm4xnxl5ghht
  const cutoutKeywordOptions: SelectProps['options'] = [
    { label: 'clothing（通用，默认都带上，不包括帽子、鞋子）', value: 'clothing' },
    { label: 'T-shirt（T恤）', value: 'T-shirt' },
    { label: 'shirt（衬衫）', value: 'shirt' },
    { label: 'sweater（毛衣）', value: 'sweater' },
    { label: 'hoodie（连帽衫/卫衣）', value: 'hoodie' },
    { label: 'vest（背心）', value: 'vest' },
    { label: 'bra（内衣，包括运动内衣)', value: 'bra' },
    { label: 'jacket（夹克）', value: 'jacket' },
    { label: 'coat（外套，比jacket长、厚）', value: 'coat' },
    { label: 'trousers（长裤）', value: 'trousers' },
    { label: 'shorts（短裤）', value: 'shorts' },
    { label: 'leggings（瑜伽裤）', value: 'leggings' },
    { label: 'skirt（短裙/半身裙）', value: 'skirt' },
    { label: 'panties（内裤）', value: 'panties' },
    { label: 'dress（连衣裙）', value: 'dress' },
    { label: 'hat（帽子）', value: 'hat' },
    { label: 'scarf（围巾）', value: 'scarf' },
    { label: 'shoes（鞋子）', value: 'shoes' },
  ];

  const [downloading, setDownloading] = useState(false);

  // 是否显示选择训练素材的弹窗
  const [showSelectTrainingMaterialModal, setShowSelectTrainingMaterialModal] = useState(false);

  //lora_vip配置，训练加急白名单
  const [loraVipCfg, setLoraVipCfg] = useState<number[]>([]);

  const [reviewerSelectorVisible, setReviewerSelectorVisible] = useState<boolean>(false);

  const [clothCategoryCfg, setClothCategoryCfg] = useState<ClothCategoryGroup[]>([]);

  // 创建防抖函数
  const debouncedSearchId = useCallback(
    debounce((value: number | undefined) => {
      setSearchId(value);
    }, 100),
    []
  );

  const debouncedNameLike = useCallback(
    debounce((value: string) => {
      setNameLike(value);
    }, 300),
    []
  );

  useEffect(() => {
    if (relatedTestFaces && relatedTestFaces?.length > 0 && relatedTestScenes && relatedTestScenes?.length > 0) {
      setRelatedTestNum(DEFAULT_TEST_NUM);
    } else {
      setRelatedTestNum(0);
    }
  }, [relatedTestFaces, relatedTestScenes]);

  useEffect(() => {
    fetchData(page, pageSize);
  }, [page]);

  useEffect(() => {
    initMasterOptions();
  }, [selectedDistributorMasterId]);

  useEffect(() => {
    setLoraTrainType('flux');
    setMaxTrainStep(getDefaultFluxSteps());

    setTrainParams({
      lr: 0.00020,
      contentOrStyle: 'content',
      rank: 32,
      alpha: 16,
      dropout: 0.2,
      resolution: '1024',
      trainExtInfo: selectedTrainModelDetail?.clothLoraTrainDetail?.trainExtInfo,
    });

  }, [showConfirmLoraModal, selectedTrainModelDetail]);

  useEffect(() => {
    setPage(1);
    fetchData(1, pageSize);
  }, [pageSize, loraType, selectedStatus, nameLike, searchUserId, relatedOperatorType, searchClothType, onlyUnconfirmedLora, onlyVIPOrCustomerLora, searchId, selectedDistributorMasterId, onlyShowHasCase, onlyShowDemo, selectedGarment, searchLabelType, onlyNearingDelivery, relatedToMe, selectedReviewerId, selectedPromptUserId, onlyExperimental, searchAgeRange, onlyMultiColorSplit]);

  useEffect(() => {
    if (showTrainDetailModal && selectedTrainModelDetail && selectedTrainModelDetail.clothLoraTrainDetail) {
      setUpdateTxtFiles(new Map());
      setLabelImgFiles(new Map());
      setUpdateTxtBtnEnabled(false);

      if (selectedTrainModelDetail.clothLoraTrainDetail.relatedOperatorMobile) {
        setRelatedOperatorMobile(selectedTrainModelDetail.clothLoraTrainDetail.relatedOperatorMobile);
      } else {
        setRelatedOperatorMobile('');
      }
    }

  }, [showTrainDetailModal, selectedTrainModelDetail]);

  useEffect(() => {
    queryAllOperators().then(res => {
      if (res && res.length > 0) {
        setAllOperators(res);

        let items = [{
          label: '全部',
          value: 'all',
          style: { width: 40, padding: 0, textAlign: 'center' },
        }, { label: '未设置', value: 'unset', style: { width: 50, padding: 0, textAlign: 'center' } }];
        res.forEach(operator => {
          items.push({
            label: operator.nickName,
            value: operator.mobile,
            style: { width: 40, padding: 0, textAlign: 'center' },
          });
        });

        setRelatedOperatorTypeItems(items);
      }
    });
    initMasterOptions();

    //获取脸和场景
    getElementConfig('CREATE_IMAGE').then(cfg => {
      if (cfg) {
        for (let c of cfg) {
          if (c.configKey === 'FACE') {
            //所有level=2的脸
            setFaceList(c.children);
          }
          if (c.configKey === 'SCENE') {
            //所有level=2的场景
            setSceneList(c.children);
          }
        }
      }
    });

    queryAllMaster(['DISTRIBUTOR']).then(res => {
      if (res) {
        setAllDistributorMasters(res);
      } else {
        setAllDistributorMasters([]);
      }
    });

    queryAllMaster(['ADMIN']).then(res => {
      if (res) {
        setAllAdmins(res);
      } else {
        setAllAdmins([]);
      }
    });

    queryGarmentList().then(res => {
      if (res && res.length > 0) {
        const map = new Map();
        res.forEach(item => {
          let value = [];
          if (map.has(item.word)) {
            value = map.get(item.word);
          }
          map.set(item.word, [...value, item.prompt]);
        });

        const items = Array.from(map.keys()).map(key => ({
          label: key,
          // value是map.get(key)转换成以逗号分割的字符串
          value: map.get(key).join(','),
        })).sort((a, b) => a.label > b.label ? 1 : -1);

        setGarmentItems(items);
      }
    });

    queryLoraVipCfg().then(res => {
      if (res && Array.isArray(res)) {
        setLoraVipCfg(res);
      }
    });

    if (isProdEnv() && ![100155,100158].includes(userInfo?.id || 0)) {
      queryMaterialModelListWithBlogs({
        pageNum: 1,
        pageSize: 10,
        onlyNearingDelivery: true,
        type: 'CUSTOM',
        materialType: 'cloth',
      }).then(res => {
        if (res && res?.totalCount && res?.totalCount > 0) {
          Modal.confirm({
            title: '交付超时提醒',
            content: `有${res?.totalCount}件客户服装交付即将/已经超时，是否查看？`,
            okText: '查看超时服装',
            cancelText: '先忽略',
            centered: true,
            onOk: () => {
              setOnlyNearingDelivery(true);
            },
          });
        }
      });
    }

    if (!isProdEnv()){
      setCanShowTrainParams(true);
    } else {
      queryConfigByKeys(['SHOW_TRAIN_PARAMS_WHITELIST']).then(res=>{
        if(!res) return;
        setCanShowTrainParams(JSON.parse(res['SHOW_TRAIN_PARAMS_WHITELIST']).includes(userInfo?.id || 0));
      })
    }

    getClothCategoryCfg().then(cfg => {
      if (cfg) {
        setClothCategoryCfg(cfg);
      }
    });
  }, []);

  function getDefaultFluxSteps() {
    if (selectedTrainModelDetail && selectedTrainModelDetail.clothLoraTrainDetail && selectedTrainModelDetail.clothLoraTrainDetail.multiColors === 'Y') {
      return defaultFluxSteps4MultiColor;
    } else {
      return defaultFluxSteps;
    }
  }

  async function fetchData(pageNum: number | undefined, pageSize: number | undefined) {
    const statusList = selectedStatus ? [selectedStatus] : null;
    let garmentTypeList: string[] = [];
    if (selectedGarment) {
      selectedGarment.forEach(item => {
        garmentTypeList = [...garmentTypeList, ...(item.split(','))];
      });
    }

    let query = {
      pageNum, pageSize, type: loraType, statusList, nameOrUserLike: nameLike ? nameLike.trim() : null,
      userId: searchUserId ? searchUserId : null, relatedOperatorType: relatedOperatorType ? relatedOperatorType : null,
      clothStyleType: searchClothType && searchClothType !== 'All' ? searchClothType.toLowerCase() : null,
      onlyUnconfirmedLora: onlyUnconfirmedLora, onlyVIPOrCustomerLora: onlyVIPOrCustomerLora,
      onlyNearingDelivery,
      id: searchId ? searchId : null,
      distributorMasterId: selectedDistributorMasterId || null,
      onlyShowHasCase,
      materialType: 'cloth',
      garmentTypeList,
      labelType: searchLabelType,
      onlyShowDemo,
      relatedToMe,
      reviewerId: selectedReviewerId,
      promptUserId: selectedPromptUserId,
      onlyExperimental,
      onlyMultiColorSplit,
      ageRange: searchAgeRange === 'ALL' ? null : searchAgeRange,
    };

    let res = await queryMaterialModelListWithBlogs(query);
    if (res) {
      setModels(res?.list || []);
      if (res?.totalCount !== undefined) {
        setTotal(res?.totalCount);
      }
    }
    return res?.list || [];
  }

  const getServerUrl = (key) => {
    if (!selectedTrainModelDetail || !selectedTrainModelDetail?.clothLoraTrainDetail) return '';

    const target = selectedTrainModelDetail?.clothLoraTrainDetail[key];
    return target && target.serverUrl ? target.serverUrl : '';
  };

  const handleChangeClothPrompt = (value: string, tag: string | undefined) => {
    setClothDetailsPrompt(value);
    setLabelType(tag);
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setPage(page);
    if (pageSize) {
      setPageSize(pageSize);
    }
  };

  // 处理时间，增加24小时并保留到小时返回
  const add24HoursCustomFormat = (timeStr: string): string => {
    // 解析传入的时间字符串为 Date 对象
    const date = new Date(timeStr);

    // 在当前时间的基础上加上24小时
    date.setTime(date.getTime() + 24 * 60 * 60 * 1000);

    // 获取日期和小时
    const day = date.getDate(); // 获取日期部分
    const hour = date.getHours(); // 获取小时部分

    // 返回自定义格式的字符串：13日16时
    return `${day}日${hour}时`;
  };

  const handleAdd = () => {
    //先清空一遍表单数据
    form.resetFields();
    setFileList([]);
    setImageList([]);
    setAdd(true);
    setLoraUpload(true);
    setShowDialog(true);
    setOpItem(null);
    setCustomerCase([]);
    //设置加载时间，用于判断当前页面是否需要强制刷新，以缓解同时有多用户操作问题
    setLoadingTime(new Date().getTime());
  };

  const handleModify = async (item: MaterialModelWithBlogs) => {
    // const models = await fetchData(page, pageSize);

    const res = await getMaterialModelById(item.id);
    // const res = models.find(model => model.id === item.id);
    if (!res) return;

    //先清空一遍表单数据
    form.resetFields();
    setAdd(false);
    setLoraUpload(true);
    setCustomerCase([]);
    setOpItem(res);

    setImageList([{
      uid: '-1', // 文件唯一标识，负数表示不是用户新上传的
      name: 'image.png', // 文件名
      status: 'done', // 状态有：uploading, done, error, removed
      url: res.showImage, // 图片路径
    }]);

    setFileList([{
      uid: '-1', // 文件唯一标识，负数表示不是用户新上传的
      // @ts-ignore
      name: res.loraName, // 文件名
      status: 'done', // 状态有：uploading, done, error, removed
    }]);

    //展开扩展信息
    const extInfoExtend = objectToPathObject(res.extInfo, 'extInfo');

    //用户创作偏好-将json string转成json object
    let aiGenFeatures = undefined;
    let userPreferFeatures = undefined;
    if (res.extInfo instanceof Map) {
      aiGenFeatures = safeParse(res.extInfo.get('aiGenFeatures'));
      userPreferFeatures = safeParse(res.extInfo.get('userPreferFeatures'));
      console.log('res.extInfo is Map');
    } else if (res.extInfo) {
      aiGenFeatures = safeParse(res.extInfo['aiGenFeatures']);
      userPreferFeatures = safeParse(res.extInfo['userPreferFeatures']);
      console.log('res.extInfo is NOT Map');
    }

    if (res.clothTypeConfigs) {
      //基于正面front view>背面back view进行排序
      res.clothTypeConfigs = res.clothTypeConfigs.sort(sortClothType());
      for (let i = 0; i < res.clothTypeConfigs.length; i++) {
        const innerExtInfoExtend = objectToPathObject(res.clothTypeConfigs[i].extInfo, 'extInfo');
        res.clothTypeConfigs[i] = { ...res.clothTypeConfigs[i], ...innerExtInfoExtend };
      }
    }

    form.setFieldsValue({ ...res, ...extInfoExtend, aiGenFeatures, userPreferFeatures });

    //设置加载时间，用于判断当前页面是否需要强制刷新，以缓解同时有多用户操作问题
    setLoadingTime(new Date().getTime());
    setShowDialog(true);

    if (res.extInfo && res.extInfo['customerCase']) {
      setCustomerCase(res.extInfo['customerCase'].split(','));
    }
  };

  const handleFileChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    const file = newFileList[0];

    console.log('success:', file);

    if (file && file.response && !file.response.success) {
      console.log('event:', file);
      notification.error({ message: '上传文件异常，请重试' });
      setFileList([]);
      return;
    }
    setFileList(newFileList);
  };

  const beforeUpload = async (file) => {
    return await compressFile(file) as File;
  };

  const handleCommit = (values: Record<string, any>) => {
    console.log('checkTimeExpire', ((new Date().getTime() - loadingTime) / 1000 / 60).toFixed(2) + 'm');
    if (new Date().getTime() - loadingTime > 60 * 60 * 1000) {
      message.warning('当前页面停留时间超过1小时，请刷新页面后重试');
      return;
    }

    console.log('handleCommit values:', values);

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const extInfoMap = pathObjectToObject(values);

    if (extInfoMap.loraName) {
      if (typeof extInfoMap.loraName !== 'string') {
        if (!extInfoMap.loraName.file) {
          notification.error({ message: '请上传lora' });
          return;
        }
        console.log('handleCommit:', extInfoMap.loraName.file.name);
        extInfoMap.loraName = 'product/' + extInfoMap.loraName.file.name;
      } else if (extInfoMap.loraName.indexOf('/models/loras/') >= 0) {
        extInfoMap.loraName = extInfoMap.loraName.split('/models/loras/')[1];
        console.log('lora地址中包含/models/loras/，直接去除', extInfoMap.loraName);
      } else if (extInfoMap.loraName.startsWith('/')) {
        extInfoMap.loraName = extInfoMap.loraName.substr(1, extInfoMap.loraName.length);
      }
    }

    if (extInfoMap.showImage) {
      if (typeof extInfoMap.showImage !== 'string') {
        if (!extInfoMap.showImage.file.response) {
          notification.error({ message: '请上传图片' });
          return;
        }
        extInfoMap.showImage = extInfoMap.showImage.file.response.data;
      }
    }

    if (add && !extInfoMap.showImage) {
      notification.error({ message: '请重新上传图片' });
      return;
    }

    if (extInfoMap.status === 'ENABLED' && (!extInfoMap.loraName || extInfoMap.loraName.trim() === '')) {
      const model = models.find(e=>e.id === extInfoMap.id);
      if (model?.mainType !== 'MAIN' || !extInfoMap.extInfo['subLoraNames'] || extInfoMap.extInfo['subLoraNames']?.length === 0){
        notification.error({ message: '当前lora未完成训练或未填写lora地址' });
        return;
      }
    }

    if (!extInfoMap.extInfo['cfg']) {
      extInfoMap.extInfo['cfg'] = 3.5;
    }

    let aiGenFeatures = extInfoMap.aiGenFeatures;
    if (aiGenFeatures) {
      extInfoMap.extInfo['aiGenFeatures'] = JSON.stringify(aiGenFeatures);
    }

    let userPreferFeatures = extInfoMap.userPreferFeatures;
    if (userPreferFeatures) {
      extInfoMap.extInfo['userPreferFeatures'] = JSON.stringify(userPreferFeatures);
    }

    if (extInfoMap.extInfo['memo'] && extInfoMap.extInfo['memo'] !== '') {
      console.log('清除审核不通过原因,memo:', extInfoMap.extInfo['memo']);
      extInfoMap.extInfo['disableReason'] = '';
    }

    if (extInfoMap.extInfo['disableReason'] && extInfoMap.extInfo['disableReason'] !== '') {
      console.log('清除备注信息,disableReason:', extInfoMap.extInfo['disableReason']);
      extInfoMap.extInfo['memo'] = '';
    }

    processChildrenByName(extInfoMap, 'clothTypeConfigs', AllLoraClothTypes, '服装款式（正/反/全身/下装）');

    // delete extInfoMap.clothLoraTrainDetail;

    console.log('handleCommit:', extInfoMap);
    const method = add ? addSystemModel : updateMaterialModel;
    method(extInfoMap).then((res) => {
      if (res) {
        notification.success({ message: '修改成功' });
        setShowDialog(false);
        fetchData(page, pageSize);
        setOpItem(null);
      }
    });
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
  };

  const handleImageChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    const file = newFileList[0];

    if (file && file.response && !file.response.success) {
      notification.error({ message: '上传图片异常，请重试' });
      setImageList([]);
      return;
    }
    setImageList(newFileList);
  };

  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  const handleDelete = (item: MaterialModelWithBlogs) => {
    setDeleteId(item.id);
  };

  const commitDel = () => {
    delModel(deleteId).then((res) => {
      if (res) {
        setDeleteId(0);
        fetchData(page, pageSize);
      }
    });
  };

  const handleCopyToSystem = (id: number) => {
    copyModelToSystem(id).then((res) => {
      if (res) {
        message.success('复制成功');
        setLoraType('SYSTEM');
      }
    });
  };
  const handleShowSelectTrainingMaterialModal = (selectedTrainModelDetail) => {
    if (selectedTrainModelDetail && selectedTrainModelDetail?.type === 'SYSTEM' && selectedTrainModelDetail?.extInfo && selectedTrainModelDetail?.extInfo['originModel']) {
      message.warning('无法修改复制自客户的官方试用，可能会影响客户的服装，如需修改请单独复制一份');
      return;
    }

    setShowSelectTrainingMaterialModal(true);
  };

  const handlePreviewUrl = (url, idx = -1, imgList: string[] | [] = []) => {
    setPreviewImage(url);

    if (idx >= 0) {
      //@ts-ignore
      setPreviewIdx(idx);
      //@ts-ignore
      setPreviewImgs(imgList);
    } else {
      setPreviewIdx(undefined);
      setPreviewImgs(undefined);
    }

    setPreviewVisible(true);
  };

  const handleCancelPreview = () => {
    setPreviewVisible(false);
  };

  const showTrainDetail = (id: number, needInitDefaultTest = false) => {
    getTrainDetail({ id: id }).then(res => {
      if (res) {
        setSelectedTrainModelDetail(res);
        setShowCutout(false);
        setShowSplitTags(false);
        if (res.labelRetFiles) {
          setGroupedLabelFiles(groupFilesByFileDir(res.labelRetFiles));
        }
        setShowTrainDetailModal(true);
        setRelatedTestFaces(res.clothLoraTrainDetail.testFaces || undefined);
        setRelatedTestScenes(res.clothLoraTrainDetail.testScenes || undefined);
        setMaxTrainStep(res.clothLoraTrainDetail.maxTrainStep || (loraTrainType === 'sdxl' ? 4000 : defaultFluxSteps));
        replaceLabelTagsInputRefs.current = [];
        replaceLabelGarmentTypesInputRefs.current = [];

        if (needInitDefaultTest) {
          initDefaultRelatedTestConfig(res);
        }
        setCutoutType(res.clothLoraTrainDetail.cutoutType || 'default');

        setClothDetailsPrompt(formatPrompt(res.clothLoraTrainDetail.clothDetailsPrompt));
        setLabelType(res.extInfo && res.extInfo['labelType']);
      } else {
        setSelectedTrainModelDetail(undefined);
        setShowTrainDetailModal(false);
      }
    });
  };

  function onShowTrainDetail(id: number, needInitDefaultTest = false, mainType: string) {
    //主子模型的情况下，查询所有的子模型id
    if (mainType === 'MAIN') {
      queryMaterialSubIds(id).then(res=>{
        if(!res) return;

        setSubModelIds(res);
        showTrainDetail(res[0], needInitDefaultTest);
      })
      return;
    }

    setSubModelIds([]);
    showTrainDetail(id, needInitDefaultTest);
  }

  function loraConfirmedByModel(model: any) {

    if (model && model.clothLoraTrainDetail && model.clothLoraTrainDetail['loraConfirmed'] === 'Y') {
      return true;
    }

    return !!(model && model.extInfo && model.extInfo['loraConfirmed'] === 'Y');
  }

  function initMasterOptions() {
    if (selectedDistributorMasterId) {
      queryCustomersOptionsByDistributorMasterId(selectedDistributorMasterId).then(res => {
        if (res && Array.isArray(res)) {
          const masterOptions = [];
          res.forEach(item => {
            // @ts-ignore
            masterOptions.push({ label: item.nickName, value: item.id + '' });
          });

          setMasterOptions(masterOptions);
        }
      });
    } else {
      queryAllMaster(['MERCHANT', 'OPERATOR', 'ADMIN', 'DISTRIBUTOR']).then(res => {
        if (res && Array.isArray(res)) {
          const masterOptions = [];
          res.forEach(item => {
            // @ts-ignore
            masterOptions.push({
              label: item.nickName + (item.corpName ? '@' + item.corpName : ''),
              value: item.id + '',
            });
          });

          setMasterOptions(masterOptions);
        }
      });
    }
  }

  function handleAssignTo(item) {
    setAssignLora(item);
    initMasterOptions();
  }

  function confirmAssignTo() {
    if (!assignUserId || !assignLora) {
      return;
    }
    assignLoraTo(assignLora.id, assignUserId).then(res => {
      if (res) {
        message.success('转移成功');
        setAssignLora(null);
        fetchData(page, pageSize);
      }
    });
  }

  const fullCloneLora = (item) => {
    cloneLora(item.id, true).then(res => {
      if (res) {
        message.success('克隆成功');
        fetchData(page, pageSize);
      }
    });
  };

  function getTaskStatusTitle(task) {
    if (!task || !task?.status) {
      return '未开始';
    }

    return getSubTrainStatusDesc(task?.status);
  }

  function getTrainingStatusTitle(item) {

    //训练中
    if (item.status === 'IN_TRAINING') {

      //获取label和lora的运行状态
      let preprocessStatus = getTaskStatusTitle(item?.clothLoraTrainDetail?.prepareView);
      let cutoutStatus = getTaskStatusTitle(item?.clothLoraTrainDetail?.cutout);
      let labelStatus = getTaskStatusTitle(item?.clothLoraTrainDetail?.label);
      let loraStatus = item?.clothLoraTrainDetail?.lora?.statusDesc || '未开始';

      //lora已确认
      if (loraConfirmedByModel(item)) {
        if (isLoraRunning(item)) {
          return '训练中';
        } else {
          return '训练' + loraStatus;
        }

        //lora未确认
      } else {

        //在打标环节
        if (item?.clothLoraTrainDetail?.label?.status) {

          if (item?.clothLoraTrainDetail?.label?.status === 'COMPLETED') {
            return `打标完成-未确认`;
          } else {
            return `打标${labelStatus}`;
          }
        }

        //在打标环节
        if (item?.clothLoraTrainDetail?.cutout?.status) {
          return `抠图${cutoutStatus}`;
        }

        //在预处理环节
        return `预处理${preprocessStatus}`;
      }
    }

    if (item.status === 'TESTING') {
      if (item?.clothLoraTrainDetail?.autoGenImg && item?.clothLoraTrainDetail?.confirmCanDeliverInfo?.confirmCanDeliver === 'Y') {
        return '出图交付中';
      }

      return '审核中';
    }
    if (item.status === 'DISABLED') {
      return '审核不通过';
    }
  }

  function getMerchantPrefix(item: MaterialModelWithBlogs) {
    if (item.vipCustomer) {
      return 'VIP';
    }
    if (item.paidCustomer) {
      return '付费';
    }
    return '';
  }

  function isLoraRunning(item: MaterialModelWithBlogs) {
    return item && item.clothLoraTrainDetail && item.clothLoraTrainDetail.lora && item.clothLoraTrainDetail.lora.status === 'RUNNING';
  }

  function colorNumberDesc(item: MaterialModelWithBlogs) {
    if (item.clothLoraTrainDetail && item.clothLoraTrainDetail?.colorNumber) {
      if (item.clothLoraTrainDetail.colorNumber === '1') {
        return '单色';
      }
      return Number(item.clothLoraTrainDetail.colorNumber).toString() + '色';

    } else {
      if (item.clothLoraTrainDetail && item.clothLoraTrainDetail?.multiColors === 'Y') {
        return '多色';
      }
    }

    return '单色';
  }

  const handleAddDemoTag = (item) => {
    addModelDemoTag(item.id).then(res => {
      if (!res) return;

      fetchData(page, pageSize);
      message.success('设置成功');
    });
  };

  const reviewerSelector = (
    <Modal
      open={reviewerSelectorVisible}
      title="选择审核员"
      onCancel={() => setReviewerSelectorVisible(false)}
      onOk={() => {
        updateModelReviewer(updateReviewer).then(res => {
          if (res) {
            fetchData(page, pageSize);
            message.success('审核员更新成功');
          }
        }).finally(() => {
          setReviewerSelectorVisible(false);
        })
      }}
      width={360}
      centered
    >
      <Select
        style={{ width: '100%', marginBottom: 16 }}
        placeholder="请选择审核员"
        value={updateReviewer.reviewerId}
        onChange={value => setUpdateReviewer({
          id: updateReviewer.id,
          reviewerId: value,
        })}
        options={allAdmins.map(admin => ({ label: admin.nickName, value: admin.id }))}
        showSearch
        allowClear
        optionFilterProp="label"
      />
    </Modal>
  );

  // @ts-ignore
  const ImageCard = (item: MaterialModelWithBlogs) => (
    <div className={'models-image-card-new'}>
      <ExperimentalLabel {...item} />
      <div className={'models-img-cover-new' + (item.type === 'SYSTEM' ? ' lora-system-block' : '')}>
        <img src={item.showImage} alt={item.name} style={{ objectFit: 'contain' }} onClick={() => {
          onShowTrainDetail(item.id, false, item.mainType);
        }} />
        {(item.status === 'IN_TRAINING' || item.status === 'TESTING' || item.status === 'DISABLED') &&
          (
            <div className="models-traing-mask" style={{ cursor: 'pointer' }} onClick={() => {
              onShowTrainDetail(item.id, false, item.mainType);
            }}>

              <div style={{ marginTop: 74 }}>
                <l-helix size="45" speed="2.5" color={isLoraRunning(item) ? 'blue' : '#D88FFF'} />
              </div>
              <div
                className={'models-training-title' + (isLoraRunning(item) ? ' lora-running-text' : '')}>{getTrainingStatusTitle(item)}</div>

              {(item.status === 'DISABLED' && item.extInfo && item.extInfo['disableOperatorNick']) && (
                <div
                  className="models-cancel-training-user">(操作人：{item.extInfo['disableOperatorNick']})</div>
              )}

              {(item.status === 'IN_TRAINING' || item.status === 'TESTING') && (
                <div
                  className="models-training-finish-time">预计完成时间{add24HoursCustomFormat(item.createTime)}</div>
              )}

              {(item.status === 'DISABLED' && item.extInfo && item.extInfo['disableReason']) && (
                <div
                  className="models-cancel-training-user">{item.extInfo['disableReason']}
                </div>
              )}
              {(item.status === 'TESTING' && item.extInfo && item.extInfo['memo']) && (
                <div
                  className="models-cancel-training-user">{item.extInfo['memo']}
                </div>
              )}
            </div>
          )
        }
        {(item.status === 'ENABLED') && (
          <div className="models-cancel-training-user" style={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }}>{item.extInfo['memo']}</div>
        )}
        {item.extInfo && item.extInfo['labelType'] &&
          <MiniTag defaultValue={'默认'}
                   title={MODEL_LABEL_TYPES.find(e => item && item.extInfo && item.extInfo['labelType'] && e.value === item.extInfo['labelType'].toLowerCase())?.label} />
        }
        {item.mainType === 'MAIN' && item.status === 'IN_TRAINING' &&
          <MiniTag defaultValue={'已完成'} position={'rightBottom'}
                   title={<Flex vertical gap={8}>
                     <Flex className={'width-100'} justify={'flex-end'}>待确认：{item.subNeedConfirmCnt}/{item.subTotal}</Flex>
                     <Flex className={'width-100'} justify={'flex-end'}>训练完成：{item.subTestingCnt}/{item.subTotal}</Flex>
                   </Flex>} />
        }
      </div>

      <div className="lora-card-content">
        {/*模型名称*/}
        <div className="loras-image-card-name" style={{ color: item.type === 'SYSTEM' ? 'red' : '' }}>
          <Tooltip title={item.name}>
						<span
              className="lora-card-name-text"
              style={{
                fontSize: 14,
                fontWeight: 600,
                color: loraVipCfg?.includes(item?.id) && item?.status === 'IN_TRAINING' ? 'red' : '',
                cursor: 'pointer',
              }}
              onClick={() => {
                navigator.clipboard.writeText(item.name);
                message.success('已复制服装名称');
              }}
            >
							{item.name.length > 10 ? item.name.slice(0, 10) + '...' : item.name}
						</span>
          </Tooltip>

          {item.type === 'SYSTEM' ? '(官方试用)' : ''}
          <Tooltip title={`复制ID: ${item?.id}`}>
						<span
              style={{ color: '#727375', fontSize: 12, cursor: 'pointer' }}
              onClick={() => {
                navigator.clipboard.writeText(item?.id?.toString());
                message.success('已复制ID');
              }}
            >
							{`(${item?.id})`}
						</span>
          </Tooltip>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'editName',
                  label: '修改名称',
                  icon: <EditOutlined />,
                },
                ...(item.type !== 'SYSTEM' && ['ENABLED', 'TESTING'].includes(item.status) ? [
                  {
                    key: 'copyToSystem',
                    label: '复制到官方试用',
                    icon: <DiffOutlined />,
                  },
                ] : []),
              ],
              onClick: ({ key }) => {
                if (key === 'editName') {
                  // 使用对话框手动实现名称修改功能
                  Modal.confirm({
                    title: '修改服装名称',
                    icon: <EditOutlined />,
                    content: (
                      <Input
                        defaultValue={item.name}
                        id="newModelName"
                        placeholder="请输入新的服装名称"
                      />
                    ),
                    onOk: () => {
                      const inputElement = document.getElementById('newModelName') as HTMLInputElement;
                      const newName = inputElement?.value;
                      if (!newName) {
                        message.error('名称不能为空');
                        return Promise.reject();
                      }

                      if (!/^[\u4e00-\u9fa5a-zA-Z0-9-_]+$/.test(newName)) {
                        message.error('名称只能包含中文、英文、数字、下划线或中划线!');
                        return Promise.reject();
                      }

                      return modifyModelName(item.id, newName).then(res => {
                        if (res) {
                          message.success('修改成功');
                          fetchData(page, pageSize);
                        }
                        return res;
                      });
                    },
                  });
                } else if (key === 'copyToSystem') {
                  // 复制到官方试用
                  Modal.confirm({
                    title: '复制到官方试用',
                    content: `是否要将"${item?.name}"复制一份到官方试用`,
                    onOk: () => {
                      return handleCopyToSystem(item?.id);
                    },
                  });
                }
              },
            }}
          >
            <a style={{ marginLeft: 4, fontSize: 12 }} onClick={(e) => e.preventDefault()}>
              <Space>
                操作
                <DownOutlined style={{ fontSize: 10 }} />
              </Space>
            </a>
          </Dropdown>
        </div>
        <div className="lora-card-info-container">
          <div className="loras-image-card-info">
            <span style={{ color: 'black' }}>上传时间：</span>
            <div className={'text10'}>{item.createTime}</div>
            <Popconfirm title={`是否将当前创作记录${isDemoTag(item) ? '从演示数据中删除' : '添加到演示数据'}`}
                        onConfirm={() => handleAddDemoTag(item)}>
              <div className={'image-demo-block' + (isDemoTag(item) ? ' image-demo-block-selected' : '')}>
                <Tooltip title={`点击${isDemoTag(item) ? '从演示数据中删除' : '添加到演示数据'}`}>
                  {isDemoTag(item) ? <MinusOutlined /> : <PlusOutlined />}演示
                </Tooltip>
              </div>
            </Popconfirm>
          </div>

          <ReviewerNick item={item} users={allAdmins} onclick={() => {
            setReviewerSelectorVisible(true);
            setUpdateReviewer({
              id: item?.id,
              reviewerId: item?.extInfo?.reviewerId,
            });
          }} />
          <PromptEngineerNick item={item} />

          <div className="loras-image-card-info">
            {getMerchantPrefix(item) && <span style={{ color: 'red' }}>{getMerchantPrefix(item)}</span>}
            <span style={{ color: 'black' }}>商户：</span>
            <a onClick={() => {
              setSearchInputValue(item.userNick);
              setNameLike(item.userNick);
            }}>{item.userNick}</a>&nbsp;
            {item.extInfo && item.extInfo['usageMemo'] ? `(${item.extInfo['usageMemo']})` : ''}
          </div>

          <div className="loras-image-card-info"><span style={{ color: 'black' }}>公司：</span>

            <Typography.Text
              copyable={!!item?.userCorpName}
              className={'text12'}
            >
              <Tooltip title={item?.userCorpName || ''}>
                {item?.userCorpName?.length > 12 ? item?.userCorpName?.slice(0, 12) + '...' : item?.userCorpName || ''}
              </Tooltip>
            </Typography.Text>

          </div>

          <div className="loras-image-card-info"><span
            style={{ color: 'black' }}>销售/渠道商：</span>{item.relatedDistributorCorpName || ''}</div>

          <div className="loras-image-card-info">
            <span style={{ color: 'black' }}>服装：</span>
            {item?.clothLoraTrainDetail?.multiColors === 'Y' ?
              <span style={{ color: 'red' }}>{colorNumberDesc(item)}</span> : '单色'}
            {item.clothTypeDesc &&
              <span>{item.clothTypeDesc}</span>
            }
          </div>

        </div>

        <Flex justify={'space-between'} align={'center'} className="lora-card-actions">
          <Button className={'operate-btn'} size={'small'} onClick={() => handleModify(item)}>修改</Button>
          {/*{item.type === 'SYSTEM' &&*/}
          <ExperienceOpenCfgSetting id={item.id} title={item.name} faceList={faceList} sceneList={sceneList} />
          {/*}*/}

          <Tooltip title={'克隆当前服装素材，新建模型'}>
            <Button className={'operate-btn'} size={'small'}
                    onClick={() => {
                      const creationDate = new Date(item.createTime);
                      const cutoffDate = new Date('2024-12-03');

                      if (creationDate < cutoffDate) {
                        Modal.confirm({
                          title: '无法克隆',
                          centered: true,
                          content: `数据太久远已无法克隆，请去训练页面「批量下载原图」后重新上传`,
                        });
                        return; // 终止操作
                      }

                      Modal.confirm({
                        title: '克隆',
                        centered: true,
                        content: `克隆素材后，将生成名为「${item?.name}_copy」的模型，并自动关联你名下`,
                        okText: '克隆',
                        cancelText: '取消',
                        onOk: () => {
                          cloneLora(item?.id).then(res => {
                            if (res) {
                              message.success('克隆成功');
                              fetchData(page, pageSize);
                            }
                          });
                        },
                      });
                    }}>
              克隆
            </Button>
          </Tooltip>

          <RetrainBtn id={item.id} materialType={item.clothLoraTrainDetail?.materialType} onlyLabel={false}
                      onChange={() => fetchData(page, pageSize)} />

          <RetrainBtn id={item.id} materialType={item.clothLoraTrainDetail?.materialType} onlyLabel={true}
                      onChange={() => fetchData(page, pageSize)} />

          <Tooltip title={'查看历史创作记录'}>
            <IconFont type={'icon-lishirenwu_weixuanzhong1'} style={{ color: '#727375', fontSize: 16 }}
                      onClick={() => setRecordModelId(item.id)} />
          </Tooltip>

          <Dropdown
            menu={{
              items: [
                {
                  key: 'loraVip',
                  label: '加急',
                  icon: <ThunderboltOutlined />,
                  disabled: loraVipCfg?.includes(item?.id),
                },
                {
                  key: 'upscaleAndRelabel',
                  label: <RetrainBtn id={item.id} onlyLabel={true} needUpscale={true}
                                      materialType={item.clothLoraTrainDetail?.materialType}
                                      onChange={() => fetchData(page, pageSize)}
                                      showTitle={'放大细节打标'} />,
                  icon: <HighlightOutlined />,
                },
                {
                  key: 'transfer2Merchant',
                  label: '转交客户',
                  icon: <LogoutOutlined />,
                  disabled: item.type === 'SYSTEM',
                },
                {
                  key: 'fullClone',
                  label: <Popconfirm title={'确定要完整克隆当前服装吗？'}
                                     onConfirm={() => fullCloneLora(item)}>完全克隆</Popconfirm>,
                  icon: <CopyOutlined />,
                  disabled: item.type === 'SYSTEM',
                },
                ...(item.type !== 'SYSTEM' && ['ENABLED', 'TESTING'].includes(item.status) ? [
                  {
                    key: 'copyToSystem',
                    label: '复制到官方试用',
                    icon: <DiffOutlined />,
                    disabled: item.type === 'SYSTEM',
                  },
                ] : []),
                {
                  key: 'handleExperimental',
                  label: getExperimentalLabel({ ...item }),
                  icon: <ExperimentOutlined />,
                },
                {
                  key: 'delLora',
                  label: '删除',
                  icon: <DeleteOutlined />,
                },
              ],
              onClick: ({ key }) => {
                if (key === 'loraVip') {
                  addLora2Vip(item?.id).then(res => {
                    if (res) {
                      message.success('加急成功');
                      queryLoraVipCfg().then(res => {
                        if (res && Array.isArray(res)) {
                          setLoraVipCfg(res);
                        }
                      });
                    }
                  });
                } else if (key === 'delLora') {
                  handleDelete(item);
                } else if (key === 'transfer2Merchant') {
                  handleAssignTo(item);
                } else if (key === 'copyToSystem') {
                  // 复制到官方试用
                  Modal.confirm({
                    title: '复制到官方试用',
                    content: `是否要将"${item?.name}"复制一份到官方试用`,
                    onOk: () => {
                      return handleCopyToSystem(item?.id);
                    },
                  });
                } else if (key === 'handleExperimental') {
                  setExperimental({ ...item, type: 'MaterialModel' }, () => {
                    fetchData(page, pageSize);
                  });
                }
              },
            }}
          >
            <a onClick={(e) => e.preventDefault()}>
              <Flex gap={4}>
                更多 <DownOutlined />
              </Flex>
            </a>
          </Dropdown>
        </Flex>
      </div>
    </div>
  );

  const changeTestClothCollocation = (key, value) => {
    // const copy = { ...testClothCollocation };
    testClothCollocation[key] = value;
    // setTestClothCollocation(copy);
  };

  const handleChangeMerchantPreference = () => {
    console.log('handleChangeMerchantPreference');
    if (!selectedTrainModelDetail) return;
    onShowTrainDetail(selectedTrainModelDetail.id, true, selectedTrainModelDetail.mainType);
  };

  function onConfirmLora() {
    if (selectedTrainModelDetail) {
      let trainExtInfo = trainParams['trainExtInfo'].toString();
      trainExtInfo = trainExtInfo && trainExtInfo.length > 0 ? JSON.parse(trainExtInfo) : {};

      confirmTrainLora({
        id: selectedTrainModelDetail.id,
        maxTrainStep: maxTrainStep,
        // testFaces: relatedTestFaces,
        // testScenes: relatedTestScenes,
        // testNum: relatedTestNum,
        // testClothCollocation,
        loraType: loraTrainType,

        lr: trainParams['lr'].toString(),
        contentOrStyle: trainParams['contentOrStyle'],
        rank: trainParams['rank'].toString(),
        alpha: trainParams['alpha'].toString(),
        dropout: trainParams['dropout'].toString(),
        resolution: trainParams['resolution'].toString(),
        trainExtInfo: canShowTrainParams? trainExtInfo : null,

      }).then(res => {
        if (res) {

          if (selectedTrainModelDetail.mainType === 'SUB') {
            getTrainDetail({ id: selectedTrainModelDetail.id }).then(res => {
              if (res) {
                setSelectedTrainModelDetail(res);
              }
            });
          } else{
            setShowTrainDetailModal(false);
            setSelectedTrainModelDetail(undefined);
            setShowConfirmLoraModal(false);
          }

          message.info('提交成功，将继续训练');
          resetTrainParams();
        }
      });
    }
  }

  function onCutoutAgain() {
    if (selectedTrainModelDetail && needCutoutAgain) {
      cutoutAgain({
        id: selectedTrainModelDetail.id,
        cutoutKeyword: cutoutKeyword ? cutoutKeyword.join(' . ') : '',
        imageSize: imageSize || '',
        isSquare: isSquare,
        prepareViewAgainWhenCutoutAgain,
        cutoutType,
        clothDetailsPrompt,
        labelType,
        cut4ScaleUp,
        cutoutModel
      }).then(res => {
        if (res) {

          if (selectedTrainModelDetail.mainType === 'SUB') {
            getTrainDetail({ id: selectedTrainModelDetail.id }).then(res => {
              if (res) {
                setSelectedTrainModelDetail(res);
              }
            });
          } else {
            setShowTrainDetailModal(false);
            setSelectedTrainModelDetail(undefined);
          }

          setShowConfirmLoraModal(false);
          setNeedCutoutAgain(false);
          setCutoutKeyword([]);

          message.info('提交成功，将重新抠图打标');

          // 更新扩展信息
          const modelId = selectedTrainModelDetail?.id;
          if (modelId) {
            updateExtInfo({
              id: modelId,
              isLoraSystemReload: true,
            });
          }
          resetTrainParams();
        }
      });
    }
  }

  const resetTrainParams = () => {
    setNeedCutoutAgain(false);
    confirmLoraForm.setFieldValue('needCutoutAgain', false);
    setPrepareViewAgainWhenCutoutAgain(false);
    confirmLoraForm.setFieldValue('prepareViewAgainWhenCutoutAgain', false);
    setCutoutType('default');
    setCut4ScaleUp('N');
    setCutoutModel('sam_vit_h_cloth');
  };

  const checkOtherField = (fieldName, value, errorMessage) => {
    // 获取另一个字段的值
    const otherValue = form.getFieldValue(fieldName);
    // 如果当前字段为空而另一个字段不为空，返回错误
    if ((!value || value.length === 0) && (otherValue && otherValue.length > 0)) {
      return Promise.reject(new Error(errorMessage));
    }
    return Promise.resolve();
  };


  function loraConfirmed() {
    return loraConfirmedByModel(selectedTrainModelDetail);
  }

  function loraConfirmedTime() {

    if (selectedTrainModelDetail && selectedTrainModelDetail.clothLoraTrainDetail && selectedTrainModelDetail.clothLoraTrainDetail['loraConfirmedTime']) {
      return selectedTrainModelDetail.clothLoraTrainDetail['loraConfirmedTime'];
    }

    if (selectedTrainModelDetail && selectedTrainModelDetail.extInfo && selectedTrainModelDetail.extInfo['loraConfirmedTime']) {
      return selectedTrainModelDetail.extInfo['loraConfirmedTime'];
    }

    return '';
  }

  function findLabelFailTxtFileName() {
    let ret: string[] = [];
    if (selectedTrainModelDetail && selectedTrainModelDetail?.labelRetFiles) {
      for (const f of selectedTrainModelDetail?.labelRetFiles) {
        let emptyTextContent = !f.fileDir.endsWith('/label') && (!f.textContent || f.textContent === '' || f.textContent.toLowerCase().includes('error'));

        if (f.type === 'text' && (emptyTextContent || f.textContent.toLowerCase().includes('a garment'))) {
          ret.push(f.fileName);
        }
      }
    }
    return ret;
  }

  function loraTrainDetailOkText() {

    if (selectedTrainModelDetail?.status === 'ENABLED') {
      return 'lora训练完成';
    }

    if (selectedTrainModelDetail?.status === 'TESTING') {
      return '内部测试中，可重新训练';
    }

    if (selectedTrainModelDetail?.status === 'DISABLED') {
      return '审核不通过';
    }

    if (loraConfirmed()) {
      return '正在训练lora';
    }

    if (findLabelFailTxtFileName().length > 0) {
      return '需要手工处理打标失败文件';
    }

    return '设置lora参数';
  }

  function getAllUploadImgs() {
    let ret: string[] = [];

    if (selectedTrainModelDetail?.materialDetail && selectedTrainModelDetail?.materialDetail?.imgUrls) {
      ret = ret.concat(selectedTrainModelDetail.materialDetail?.imgUrls);
    } else {
      if (selectedTrainModelDetail?.materialDetail && selectedTrainModelDetail?.materialDetail?.fullShotImgList) {
        ret = ret.concat(selectedTrainModelDetail.materialDetail.fullShotImgList.map(item => item.imgUrl));
      }
      if (selectedTrainModelDetail?.materialDetail && selectedTrainModelDetail?.materialDetail?.detailShotImgList) {
        ret = ret.concat(selectedTrainModelDetail.materialDetail.detailShotImgList.map(item => item.imgUrl));
      }
      if (selectedTrainModelDetail?.materialDetail && selectedTrainModelDetail?.materialDetail?.moreImgList) {
        ret = ret.concat(selectedTrainModelDetail.materialDetail.moreImgList.map(item => item.imgUrl));
      }
    }

    return ret;
  }

  function onLabelTextChange(e: React.ChangeEvent<HTMLTextAreaElement>, f: FileVO) {
    const newMap = new Map(updateTxtFiles);
    const key = f.fileDir + '/' + f.fileName;
    newMap.set(key, e.target.value);
    setUpdateTxtFiles(newMap);

    setUpdateTxtBtnEnabled(true);
  }

  // 确认选择训练素材
  const handleConfirmTrainingMaterial = async () => {
    // 设置更新打标按钮为禁用状态
    setUpdateTxtBtnEnabled(false);

    // 更新打标文件
    let labelFileEditItems = [];

    // 遍历更新打标文件
    for (const [filePath, content] of updateTxtFiles) {
      // @ts-ignore
      labelFileEditItems.push({
        filePath: filePath,
        updateTextContent: content,
        deleted: false,
      });
    }

    // 遍历图片文件
    for (const [filePath, item] of labelImgFiles) {
      // @ts-ignore
      labelFileEditItems.push({
        filePath: filePath,
        updateImgUrl: item.url,
        deleted: item.deleted,
        // 设置为训练素材
        isTrainingMaterial: item.isTrainingMaterial === undefined ? false : item.isTrainingMaterial,
      });
    }

    console.log('labelFileEditItems', labelFileEditItems);

    // 更新打标文件
    if (labelFileEditItems) {
      updateLabelFiles({
        labelId: selectedTrainModelDetail?.clothLoraTrainDetail && selectedTrainModelDetail?.clothLoraTrainDetail.label ? selectedTrainModelDetail?.clothLoraTrainDetail.label.taskId : null,
        items: labelFileEditItems,
      }).then(res => {
        if (res) {
          message.success('更新成功', 3);
          setUpdateTxtBtnEnabled(false);
        } else {
          message.error('更新失败');
          setUpdateTxtBtnEnabled(true);
        }
      });
    }

    // 获取模型id
    const modelId = selectedTrainModelDetail?.id;

    // 更新扩展信息
    if (modelId) {
      updateExtInfo({
        id: modelId,
        isManualReplacement: isManualReplacement,
      });
    }

    // 关闭弹窗
    setShowSelectTrainingMaterialModal(false);
  };

  function groupFilesByFileDir(labelRetFiles: FileVO[]): Map<string, FileVO[]> {
    const groupedFiles = new Map<string, FileVO[]>();

    for (const file of labelRetFiles) {
      if (!groupedFiles.has(file.fileDir)) {
        groupedFiles.set(file.fileDir, []);
      }
      groupedFiles.get(file.fileDir)!.push(file);
    }

    return groupedFiles;
  }

  function getLabelRetDirShowName(fileDir: string) {
    if (fileDir.includes('half')) {
      return '半身图';
    } else if (fileDir.includes('full')) {
      return '全身图';
    } else {
      return '特征';
    }
  }

  function getShowTestFaceListByModel(model: MaterialModelWithBlogs | undefined) {
    let clothStyleType = 'female';
    let version = loraTrainType === 'flux' ? 'v_2' : 'v_1';
    if (model && model?.extInfo && model?.extInfo['clothStyleType']) {
      clothStyleType = model.extInfo['clothStyleType'];
    }

    return faceList.filter(f => f.type.includes(clothStyleType + '-model')).filter(f => f.type.includes(version));
  }

  function getShowTestSceneListByModel(model: MaterialModelWithBlogs | undefined) {
    let clothStyleType = 'female';
    let version = loraTrainType === 'flux' ? 'v_2' : 'v_1';
    if (model && model?.extInfo && model?.extInfo['clothStyleType']) {
      clothStyleType = model.extInfo['clothStyleType'];
    }

    return sceneList.filter(f => f.type.some(i => i.toLowerCase() === clothStyleType.toLowerCase())).filter(f => f.type.includes(version));
  }


  // 处理图片文件上传
  async function handleLabelImgChange(file: File, f: FileVO) {
    if (file) {

      // 上传图片文件
      uploadFile(file).then(res => {
        if (res) {
          let filePath = f.fileDir + '/' + f.fileName;

          // 设置为训练素材
          const newLabelImgFiles = new Map(labelImgFiles);

          // 设置为训练素材
          newLabelImgFiles.set(filePath, {
            filePath: f.fileDir + '/' + f.fileName,
            url: res,
            deleted: false,
          });
          // 设置为训练素材
          setLabelImgFiles(newLabelImgFiles);

          // 获取模型id
          const modelId = selectedTrainModelDetail?.id;
          // 同步到图片案例
          if (modelId) {
            syncToImageCase({
              id: modelId,
              filePath: f.fileDir + '/' + f.fileName,
              url: res,
              fileDir: f.fileDir,
              fileName: f.fileName,
              imgUrl: f.imgUrl,
              textContent: f.textContent,
              type: f.type,
            });
          }

          // 设置为手动替换
          setIsManualReplacement(isManualReplacement + 1);
          // 设置更新打标按钮为启用状态
          setUpdateTxtBtnEnabled(true);
        }
      });
    }
  }

  function getImgFileColorByFile(f: FileVO) {
    let filePath = f.fileDir + '/' + f.fileName;
    if (labelImgFiles.has(filePath)) {
      if (labelImgFiles.get(filePath)?.deleted === true) {
        return 'red';
      }
      return 'blue';
    }
    return '';
  }

  function getTxtFileColorByFile(f: FileVO) {
    let filePath = f.fileDir + '/' + f.fileName;
    if (updateTxtFiles.has(filePath)) {
      return 'blue';
    }
    if (f.textContent && f.textContent.toLowerCase().includes('a garment')) {
      return 'red';
    }
    return '';
  }

  function safeParse(str: string | undefined) {
    if (str) {
      return JSON.parse(str);
    }
    return {};
  }

  const getClothTypes = (item: MaterialModelWithBlogs | null) => {
    let typeList = AllLoraClothTypes;
    if (item && !item.hasBackView) {
      typeList = typeList.filter(e => !e.key.includes('back view'));
    }

    if (item && !item.lowerBody) {
      typeList = typeList.filter(e => !e.key.includes('lower body'));
    }

    if (item && item.version) {
      typeList = typeList.filter(e => e.key.includes(item.version));
    }
    return typeList;
  };

  function initDefaultRelatedTestConfig(model: MaterialModelWithBlogs | undefined, index = -1) {
    setCopyByPreference(false);

    if (index < 0) {
      index = selectedPreferenceIndex;
    }

    if (model?.merchantPreference && model?.merchantPreference.preferences) {
      const merchantSubPreference = model?.merchantPreference.preferences[index];
      let faceList = getShowTestFaceListByModel(model);

      if (merchantSubPreference?.faces) {
        faceList = faceList.filter(f => merchantSubPreference?.faces.some(i => Number(i) === f.id));
      }

      if (merchantSubPreference?.faces && faceList.length > 0) {
        setRelatedTestFaces(faceList.map(e => e.id));
        setCopyByPreference(true);
      } else {
        getMerchantRecentElement({
          key: 'FACE',
          userId: model?.userId,
        }).then(res => {
          if (res && res.length > 0) {
            faceList = faceList.filter(f => res.some(i => i.id === f.id));
            setRelatedTestFaces(faceList.slice(0, 5).map(e => e.id));
          }
        });
      }

      let sceneList = getShowTestSceneListByModel(model);
      if (merchantSubPreference?.scenes) {
        sceneList = sceneList.filter(f => merchantSubPreference?.scenes.some(i => Number(i) === f.id));

        if (sceneList.length <= 0) {
          getMerchantRecentElement({
            key: 'SCENE',
            userId: model?.userId,
          }).then(res => {
            if (res && res.length > 0) {
              sceneList = sceneList.filter(f => res.some(i => i.id === f.id));
              setRelatedTestScenes(sceneList.slice(0, 5).map(e => e.id));
            }
          });
        }

        setRelatedTestScenes(sceneList.map(e => e.id));
      }

      const copy = deepCopy(testClothCollocation);
      copy.tops = merchantSubPreference?.clothCollocation && merchantSubPreference?.clothCollocation.tops ? merchantSubPreference?.clothCollocation.tops : null;
      copy.bottoms = merchantSubPreference?.clothCollocation && merchantSubPreference?.clothCollocation.bottoms ? merchantSubPreference?.clothCollocation.bottoms : null;
      copy.shoe = merchantSubPreference?.clothCollocation && merchantSubPreference?.clothCollocation.shoe ? merchantSubPreference?.clothCollocation.shoe : null;
      copy.others = merchantSubPreference?.clothCollocation && merchantSubPreference?.clothCollocation.others ? merchantSubPreference?.clothCollocation.others : null;
      setTestClothCollocation(copy);

      confirmLoraForm.setFieldValue('testClothCollocation', copy);
      deliveryForm.setFieldValue('testClothCollocation', copy);
    }
  }

  function replaceTag(str: string, from: string, to: string): string {
    // 按逗号拆分字符串，并去除每个标签词的前后空格
    const tags = str.split(',').map(tag => tag.trim());

    // 检查是否包含 from 标签词
    const containsFrom = tags.some(tag => tag === from);

    // 如果不包含 from 标签词，直接返回原始字符串
    if (!containsFrom) {
      return str;
    }

    // 遍历标签词数组，替换包含 from 的标签词
    const replacedTags = tags.map(tag => {
      if (tag === from) {
        return to;
      }
      return tag;
    });

    // 将替换后的标签词数组重新用逗号拼接成新的字符串
    return replacedTags.join(', ');
  }

  function replaceDetailGarmentType(json_string: string, from: string, to: string): string {
    console.log('replaceDetailGarmentType,', json_string, from, to);

    try {
      // 将 JSON 字符串解析为对象
      const jsonObject = JSON.parse(json_string);

      // 检查并替换 c1 和 c2 的 detail_garment_type 字段
      if (jsonObject.c1 && typeof jsonObject.c1.detail_garment_type === 'string' && jsonObject.c1.detail_garment_type === from) {
        jsonObject.c1.detail_garment_type = to;
      }
      if (jsonObject.c2 && typeof jsonObject.c2.detail_garment_type === 'string' && jsonObject.c2.detail_garment_type === from) {
        jsonObject.c2.detail_garment_type = to;
      }
      if (jsonObject.c3 && typeof jsonObject.c3.detail_garment_type === 'string' && jsonObject.c3.detail_garment_type === from) {
        jsonObject.c3.detail_garment_type = to;
      }

      // 将修改后的对象重新转换为 JSON 字符串
      return JSON.stringify(jsonObject);
    } catch (error) {
      console.info('Error parsing JSON:', error);
      // 捕获解析错误
      throw new Error('Invalid JSON string');
    }
  }

  function setReplaceInputRef(index: number, el: HTMLInputElement | null) {
    // @ts-ignore
    return replaceLabelTagsInputRefs.current[index] = el;
  }

  function setReplaceLabelGarmentTypesInputRefs(index: number, el: HTMLInputElement | null) {
    // @ts-ignore
    return replaceLabelGarmentTypesInputRefs.current[index] = el;
  }

  const handleChangeColor = () => {
    if (opItem) {
      handleModify(opItem);
    }
  };

  const handleChangePreferenceIndex = (index: number, model: MaterialModelWithBlogs) => {
    setSelectedPreferenceIndex(index);

    initDefaultRelatedTestConfig(model, index);
  };

  const MerchantPreferenceOption: React.FC<{
    merchantPreference: MerchantPreference | null;
    model: MaterialModelWithBlogs;
  }> = ({ merchantPreference, model }) => {
    return <>
      <div style={{ marginTop: -16 }} className={'text12 color-error font-pf weight'}>
        本服装商家备注：{model?.clothLoraTrainDetail.matchPrefer}
      </div>
      <div className={'text12 color-error font-pf weight'}>
        商家偏好<Tooltip title={'变更商家运营偏好'}>
        <IconFont type={'icon-bianji'} className={'color-error pointer'} style={{ fontSize: 14 }}
                  onClick={() => setMerchantPreferenceSetting(model)} />
      </Tooltip>：{merchantPreference ? merchantPreference.memo : ''}
      </div>

      {merchantPreference?.preferences &&
        <Tabs defaultActiveKey="0" type={'card'}
              items={merchantPreference?.preferences.map((item, index) => ({
                key: index + '',
                label: `偏好${index + 1}`,
              }))}
              activeKey={selectedPreferenceIndex + ''}
              onChange={(key) => handleChangePreferenceIndex(Number(key), model)} size={'large'}
              indicator={{ size: 102 }} />
      }

      <Form.Item label={`测试模特：当前${copyByPreference ? '系统匹配' : '商家历史偏好'}`}
                 rules={[
                   {
                     validator: (_, value) => checkOtherField('relatedTestScenes', value, '测试模特和测试场景必须同时填写'),
                   },
                 ]}>
        <Select style={{ width: '100%' }}
                mode="tags"
                value={relatedTestFaces}
                showSearch={true}
                filterOption={(input, option) => {
                  if (option && option.children) {
                    return option.children['props'].children[1].toLowerCase().includes(input.toLowerCase());
                  }
                  return false;
                }}
                optionLabelProp="label"
                onChange={value => {
                  setRelatedTestFaces(value);
                }}>
          {getShowTestFaceListByModel(model).map((item) => (
            <Select.Option key={item.id} value={item.id}
                           label={(
                             <div style={{ display: 'flex', alignItems: 'center' }}>
                               <img
                                 src={item.showImage}
                                 alt={item.name}
                                 style={{ width: 20, height: 20, marginRight: 8 }}
                               />
                               {item.name}
                             </div>
                           )}>
              <div style={{ display: 'flex', alignItems: 'center', height: 40 }}>
                <img
                  src={item.showImage}
                  alt={item.name}
                  style={{ width: 60, height: 60, marginRight: 8, objectFit: 'contain' }}
                />
                {item.name}
              </div>
            </Select.Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item label="测试场景：" style={{ marginTop: -12 }}
                 rules={[
                   {
                     validator: (_, value) => checkOtherField('relatedTestFaces', value, '测试模特和测试场景必须同时填写'),
                   },
                 ]}>
        <Select style={{ width: '100%' }}
                mode="tags"
                showSearch={true}
                filterOption={(input, option) => {
                  if (option && option.children) {
                    return option.children['props'].children[1].toLowerCase().includes(input.toLowerCase());
                  }
                  return false;
                }}
                value={relatedTestScenes}
                optionLabelProp="label"
                onChange={value => {
                  setRelatedTestScenes(value);
                }}>
          {getShowTestSceneListByModel(model).map((item) => (
            <Select.Option key={item.id} value={item.id}
                           label={(
                             <div style={{ display: 'flex', alignItems: 'center' }}>
                               <img
                                 src={item.showImage}
                                 alt={item.name}
                                 style={{ width: 20, height: 20, marginRight: 8 }}
                               />
                               {item.name}
                             </div>
                           )}>
              <div style={{ display: 'flex', alignItems: 'center', height: 40 }}>
                <img
                  src={item.showImage}
                  alt={item.name}
                  style={{ width: 60, height: 60, marginRight: 8, objectFit: 'contain' }}
                />
                {item.name}
              </div>
            </Select.Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item
        label="测试图尺寸："
        name="testImgProportions"
      >
        <Select
          options={creationSizeOptions}
          placeholder="选择出图尺寸"
        />
      </Form.Item>

      <Form.Item label="测试图组数：(最多5组，20张/组)" style={{ marginTop: -12 }}>
        <InputNumber value={relatedTestNum} style={{ width: '20%' }} min={0} max={5} addonAfter={'组'}
                     onChange={value => setRelatedTestNum(value ? value : 1)} />
      </Form.Item>

      <Row style={{ marginTop: -12 }}>
        <Col span={12}>
          <Form.Item
            name={['testClothCollocation', 'tops']}
            label="上装"
            initialValue={merchantPreference && merchantPreference.preferences && merchantPreference.preferences.length > 0 && merchantPreference.preferences[selectedPreferenceIndex] && merchantPreference.preferences[selectedPreferenceIndex].clothCollocation ? merchantPreference.preferences[selectedPreferenceIndex].clothCollocation?.tops : null}
          >
            <Input placeholder="输入上装(英文)"
                   onChange={e => changeTestClothCollocation('tops', e.target.value)} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name={['testClothCollocation', 'bottoms']}
            label="下装"
            initialValue={merchantPreference && merchantPreference.preferences && merchantPreference.preferences.length > 0 && merchantPreference.preferences[selectedPreferenceIndex] && merchantPreference.preferences[selectedPreferenceIndex].clothCollocation ? merchantPreference.preferences[selectedPreferenceIndex].clothCollocation?.bottoms : null}
          >
            <Input placeholder="输入下装(英文)"
                   onChange={e => changeTestClothCollocation('bottoms', e.target.value)} />
          </Form.Item>
        </Col>
      </Row>
      <Row style={{ marginTop: -12 }}>
        <Col span={12}>
          <Form.Item
            name={['testClothCollocation', 'shoe']}
            label="鞋子"
            initialValue={merchantPreference && merchantPreference.preferences && merchantPreference.preferences.length > 0 && merchantPreference.preferences[selectedPreferenceIndex] && merchantPreference.preferences[selectedPreferenceIndex].clothCollocation ? merchantPreference.preferences[selectedPreferenceIndex].clothCollocation?.shoe : null}
          >
            <Input placeholder="输入鞋子(英文)"
                   onChange={e => changeTestClothCollocation('shoe', e.target.value)} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name={['testClothCollocation', 'others']}
            label="其它"
            initialValue={merchantPreference && merchantPreference.preferences && merchantPreference.preferences.length > 0 && merchantPreference.preferences[selectedPreferenceIndex] && merchantPreference.preferences[selectedPreferenceIndex].clothCollocation ? merchantPreference.preferences[selectedPreferenceIndex].clothCollocation?.others : null}
          >
            <Input placeholder="输入其他(英文)"
                   onChange={e => changeTestClothCollocation('others', e.target.value)} />
          </Form.Item>
        </Col>
      </Row>
    </>;
  };

  return (
    <PageContainer>
      <Flex vertical gap={8} style={{ padding: 16 }}>
        <Flex vertical justify={'flex-start'} gap={8}>

          <Flex justify={'flex-start'} gap={8} align={'center'}>
            <Segmented defaultValue={'CUSTOM'} value={loraType}
                       style={{ backgroundColor: '#E1E3EB', width: 'auto', fontSize: 12 }}
                       onChange={(value: LoraType) => setLoraType(value)}
                       options={[{ label: '客户模型', value: 'CUSTOM' }, { label: '官方试用', value: 'SYSTEM' }]}
            />

            {/* 年龄区段 */}
            <Radio.Group options={AgeRanges}
                         defaultValue={'ALL'} optionType="button" onChange={e => setSearchAgeRange(e.target.value)} />

            {/* 服装类型 */}
            <Radio.Group options={AllClothTypeCheckBoxOptions.filter(item => item.value !== 'Common')}
                         defaultValue={'All'} optionType="button" onChange={e => setSearchClothType(e.target.value)} />


            {/* 运营账号 */}
            {/*<Radio.Group options={relatedOperatorTypeItems}*/}
            {/*             defaultValue={'all'} optionType="button"*/}
            {/*             onChange={e => setRelatedOperatorType(e.target.value)} />*/}

            {/* 只看演示 */}
            <Checkbox onChange={e => setOnlyShowDemo(e.target.checked)}>演示数据</Checkbox>

            {/* 只看客户案例 */}
            <Checkbox onChange={e => setOnlyShowHasCase(e.target.checked)}>客户案例</Checkbox>

            <Checkbox style={{ fontSize: 12 }} onChange={e => {
              setOnlyVIPOrCustomerLora(e.target.checked);
            }}>VIP和付费</Checkbox>

            <Checkbox style={{ fontSize: 12 }} onChange={e => {
              setOnlyUnconfirmedLora(e.target.checked);
            }}>未确认</Checkbox>

            <Checkbox style={{ fontSize: 12 }} checked={onlyNearingDelivery} onChange={e => {
              setOnlyNearingDelivery(e.target.checked);
            }}>20小时未交付</Checkbox>

            <Checkbox style={{ fontSize: 12 }} checked={onlyExperimental} onChange={e => {
              setOnlyExperimental(e.target.checked);
            }}>只看实验</Checkbox>

            <Checkbox style={{ fontSize: 12 }} onChange={e => {
              setOnlyMultiColorSplit(e.target.checked);
            }}>多色拆分</Checkbox>

            <Checkbox style={{ fontSize: 12 }} onChange={e => {
              setRelatedToMe(e.target.checked);
            }}>我相关的</Checkbox>

            <Button style={{ padding: 4, fontSize: 12 }} size={'small'} icon={<PlusOutlined />}
                    onClick={handleAdd}>新增服装</Button>

          </Flex>

          <Flex gap={8} align={'center'} justify={'flex-start'}>
            <Tooltip title={'刷新页面数据'}>
              <Button icon={<RedoOutlined />} onClick={() => {
                fetchData(page, pageSize);
                message.success('刷新成功');
              }} />
            </Tooltip>

            <InputNumber
              controls={false}
              placeholder="服装id"
              onChange={(e) => debouncedSearchId(e || undefined)}
              style={{ width: 80 }} />

            <Select
              placeholder="选择销售/渠道商"
              style={{ width: 150 }}
              showSearch
              allowClear
              optionFilterProp="label"
              defaultActiveFirstOption={true}
              value={selectedDistributorMasterId}
              onChange={(e) => setSelectedDistributorMasterId(e)}
            >
              {allDistributorMasters?.map(s => (
                <Select.Option key={s.id} value={s.id} label={s.corpName}>
                  {s.corpName}
                </Select.Option>
              ))}
            </Select>

            <Select options={masterOptions} style={{ width: 140 }} showSearch allowClear
                    placeholder={'选择商家'}
                    optionFilterProp="label" defaultActiveFirstOption={true}
                    value={searchUserId}
                    onChange={(e) => setSearchUserId(e)} />

            <Input 
                   allowClear
                   placeholder="服装或商家名称"
                   value={searchInputValue}
                   onChange={(e) => {
                     const value = e.target.value;
                     setSearchInputValue(value);
                     debouncedNameLike(value);
                   }}
                   onClear={() => {
                     setSearchInputValue('');
                     setNameLike(undefined);
                   }}
                   style={{ width: 140 }} />

            <Select value={selectedGarment} placeholder="选择服装类型(可多选)"
                    onChange={(value) => setSelectedGarment(value)} mode={'multiple'}
                    style={{ width: 200 }} allowClear showSearch optionFilterProp="label"
                    options={garmentItems} />

            <Select value={searchLabelType} placeholder="打标方式" onChange={e => setSearchLabelType(e)}
                    style={{ width: 110 }} allowClear>
              {MODEL_LABEL_TYPES.map((item, index) => (
                <Option key={index} value={item.value}>{item.label}打标</Option>
              ))}
            </Select>

            <Select value={selectedStatus} placeholder="状态" onChange={e => setSelectedStatus(e)}
                    style={{ width: 110 }} allowClear>
              {AllModelStatus.map((item, index) => (
                <Option key={index} value={item.value}>{item.label}</Option>
              ))}
            </Select>


            {allDistributorMasters?.find(s => s.corpName === '华东大客户部') !== null &&
              <Checkbox style={{ fontSize: 12 }} onChange={e => {
                if (e.target.checked) {
                  setSelectedDistributorMasterId(allDistributorMasters?.find(s => s.corpName === '华东大客户部')?.id || null);
                } else {
                  setSelectedDistributorMasterId(null);
                }
              }}>华东大客户</Checkbox>
            }

            <Select
              placeholder="选择审核员"
              style={{ width: 120 }}
              allowClear={true}
              value={selectedReviewerId}
              onChange={(e) => setSelectedReviewerId(e)}
            >
              {allAdmins?.map(s => (
                <Select.Option key={s.id} value={s.id}>
                  {s.nickName}
                </Select.Option>
              ))}
            </Select>

            <Select
              placeholder="选择工程师"
              style={{ width: 120 }}
              allowClear={true}
              value={selectedPromptUserId}
              onChange={(e) => setSelectedPromptUserId(e)}
            >
              {allAdmins?.map(s => (
                <Select.Option key={s.id} value={s.id}>
                  {s.nickName}
                </Select.Option>
              ))}
            </Select>

          </Flex>
        </Flex>
        <Row gutter={[8, 8]}>
          {models.map((model, index) => (
            <Col key={index}>
              <ImageCard {...model} />
            </Col>
          ))}
        </Row>
        <div className={'stick-bottom-pagination'}>
          <Pagination
            current={page}
            pageSize={pageSize}
            total={total}
            onChange={handlePageChange}
            showTotal={(total) => `共 ${total} 个模型`}
            showSizeChanger // 允许用户更改每页显示条数
            pageSizeOptions={[24, 48, 96]}
            showQuickJumper // 允许用户快速跳转到某一页
            style={{ marginTop: '16px', textAlign: 'center' }}
          />
        </div>

      </Flex>
      {showDialog &&
        <Modal title={''} open={showDialog} centered mask={true} closable={false} width={'auto'}
               onCancel={() => setShowDialog(false)}
               styles={{
                 footer: { display: 'flex', flexDirection: 'row', justifyContent: 'center' },
               }}
               maskClosable={false}
               footer={[
                 <Button key="cancel" onClick={() => setShowDialog(false)}>
                   取消
                 </Button>,
                 <Button key="submit" type="primary" onClick={form.submit}>
                   保存
                 </Button>,
                 <Popconfirm title={`确认可交付了吗？`} key={form.getFieldValue('id')}
                             description={`点击确认，系统将自动出图，完成后系统会自动审核通过`}
                             onConfirm={() => {
                               confirmCanDeliver(form.getFieldValue('id')).then(res => {
                                 if (res) {
                                   message.success('开始启动自动出图并交付流程');
                                   setShowDialog(false);
                                 } else {
                                   message.error('操作失败');
                                 }
                               });
                             }}
                 >
                   <Button key="autoGenImgAndDeliver"
                           type="primary"
                           hidden={!(form.getFieldValue(['clothLoraTrainDetail', 'autoGenImg']) && form.getFieldValue('status') === 'TESTING')}
                           disabled={!(form.getFieldValue('status') === 'TESTING'
                             && form.getFieldValue(['clothLoraTrainDetail', 'autoGenImg'])
                             && form.getFieldValue(['clothLoraTrainDetail', 'confirmCanDeliverInfo', 'confirmCanDeliver']) !== 'Y')}
                   >
                     {form.getFieldValue('status') === 'TESTING'
                     && form.getFieldValue(['clothLoraTrainDetail', 'autoGenImg'])
                     && form.getFieldValue(['clothLoraTrainDetail', 'confirmCanDeliverInfo', 'confirmCanDeliver']) === 'Y' ? '自动出图中...' : '自动出图并交付'
                     }
                   </Button>
                 </Popconfirm>,

               ]}>

          <Form className={'lora-dialog-block'}
                labelCol={{ span: 2 }}
                wrapperCol={{ span: 22 }}
                initialValues={{ remember: true }}
                onFinish={handleCommit}
                autoComplete="off"
                form={form}
          >
            <Form.Item hidden label="id" name="id">
              <Input />
            </Form.Item>
            <Form.Item hidden label="opVersion" name="opVersion">
              <Input />
            </Form.Item>
            <Form.Item hidden label="type" name="type">
              <Input />
            </Form.Item>
            <Form.Item label="模型名称" name="name" style={{ width: '100%' }}
                       rules={[{
                         required: true,
                         message: '请输入名称!',
                       },
                         {
                           pattern: /^[\u4e00-\u9fa5a-zA-Z0-9-_]+$/,
                           message: '名称只能包含中文、英文、数字、下划线或中划线!',
                         },
                       ]}
            >
              <Input style={{ width: 600 }} />
            </Form.Item>
            {(add || form.getFieldValue('type') === 'SYSTEM') &&
              <Form.Item
                label="展示图片"
                name="showImage"
                rules={[{ required: true, message: '请上传展示图片' }]}
                style={{ width: '100%' }}
              >
                <Upload
                  action={UPLOAD_PRO_URL}
                  listType="picture-card"
                  fileList={imageList}
                  onPreview={handlePreview}
                  beforeUpload={beforeUpload}
                  onChange={handleImageChange}
                >
                  {imageList.length >= 1 ? null : uploadButton}
                </Upload>
              </Form.Item>
            }
            <Form.Item label="模型上传类型" name="loraUpload" style={{ width: '100%' }}
                       tooltip={'填写的模型文件地址，系统已默认/home/<USER>/aigc/ComfyUI/models/loras/，请填写之后的路径'}>
              <Switch checked={loraUpload} onChange={(e) => setLoraUpload(e)} checkedChildren={'上传文件'}
                      unCheckedChildren={'填写模型地址'} />
            </Form.Item>
            {form.getFieldValue('mainType') === 'MAIN' &&
              <Form.List name="extInfo.subLoraNames">
                {(fields) => (
                  <>
                    {fields.map(({ key, name }) => (
                      <Form.Item
                        label={`模型文件${name+1}`}
                        key={key}
                        name={[name]}  // 注意这里用的是 [name]
                        rules={[{ required: true, message: '请上传模型文件' }]}
                        style={{ width: '100%', marginBottom: 4, marginTop: 0 }}
                      >
                        <Input disabled={loraUpload} placeholder={'请输入lora的地址，如：0529/luwei/retrain/jeans2/lv2jeans2simple.safetensors'} />
                      </Form.Item>
                    ))}
                  </>
                )}
              </Form.List>
            }

            {form.getFieldValue('mainType') !== 'MAIN' &&
              <Form.Item
                label="模型文件"
                name="loraName"
                rules={[{ required: false, message: '请上传模型文件' }]}
                style={{ width: '100%' }}
              >
                {loraUpload &&
                  <Upload action={UPLOAD_LORA_URL} fileList={fileList} onChange={handleFileChange}>
                    {fileList.length >= 1 ? null : <Button icon={<UploadOutlined />}>上传模型文件</Button>}
                  </Upload>
                }
                {!loraUpload &&
                  <Input maxLength={256}
                         placeholder={'请输入lora的地址，如：0529/luwei/retrain/jeans2/lv2jeans2simple.safetensors'} />
                }
              </Form.Item>
            }

            <Form.Item
              label="服装款式"
              name="extInfo.clothStyleType"
              rules={[{ required: true, message: '请选择正确的服装款式' }]}
              style={{ width: '100%' }}
            >
              <Radio.Group options={AllClothTypes.filter(item => item.value !== 'Common').map(item => ({
                value: item.value.toLowerCase(), label: item.label,
              }))} optionType="button" buttonStyle="solid" />
            </Form.Item>

            <Form.Item
              label="年龄区段"
              name="extInfo.ageRange"
              rules={[{ required: true, message: '请选择年龄区段' }]}
              style={{ width: '100%' }}
            >
              <Select
                key={'checkedAgeRange'}
                value={form.getFieldValue('extInfo.ageRange')}
                style={{ width: 600 }}
                allowClear={true}
              >
                {SELECT_AGE_RANGE_OPTIONS.map(group => (
                  <Select.OptGroup key={group.title} label={group.title}>
                    {group.options.map(option => (
                      <Select.Option key={option.value} value={option.value}>
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select.OptGroup>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              label="服装种类"
              name={'clothType'}
              style={{ width: '100%' }}
            >
              <Radio.Group
                options={Object.values(ClothTypes).map(item => ({ value: item.key, label: item.desc }))}
                optionType="button"
                buttonStyle="solid"
              />
            </Form.Item>
            <Form.Item
              label="服装款式"
              name={'extInfo.clothCategory'}
              style={{ width: '100%' }}
            >
              <ClothCategorySelect width={600} clothCategoryCfg={clothCategoryCfg} />
            </Form.Item>

            <Flex className={'width-100'}>
              <Form.Item
                label="是否多色"
                rules={[{ required: false }]}
                style={{ width: '30%' }}
                labelCol={{ span: 7 }}
                initialValue={'N'}
              >
                <Radio.Group
                  options={[{ label: '单色', value: 'N' }, { label: '多色', value: 'Y' }]}
                  optionType="default"
                  value={form.getFieldValue(['clothLoraTrainDetail', 'multiColors'])}
                  disabled={true}
                />
              </Form.Item>

              {/*<Form.Item*/}
              {/*  name="extInfo.includesBra"*/}
              {/*  label="是否包含内衣"*/}
              {/*  rules={[{ required: true }]}*/}
              {/*  style={{ width: '20%' }}*/}
              {/*  labelCol={{ span: 11 }}*/}
              {/*  initialValue={'N'}*/}
              {/*>*/}
              {/*  <Radio.Group*/}
              {/*    options={[{ label: '是', value: 'Y' }, { label: '否', value: 'N' }]}*/}
              {/*    optionType="button" buttonStyle="solid"*/}
              {/*  />*/}
              {/*</Form.Item>*/}

              {/*<Form.Item*/}
              {/*  name="extInfo.increaseStrength"*/}
              {/*  label="增强出图"*/}
              {/*  rules={[{ required: true }]}*/}
              {/*  style={{ width: '20%' }}*/}
              {/*  labelCol={{ span: 11 }}*/}
              {/*  initialValue={'N'}*/}
              {/*>*/}
              {/*  <Radio.Group*/}
              {/*    options={[{ label: '是', value: 'Y' }, { label: '否', value: 'N' }]}*/}
              {/*    optionType="button" buttonStyle="solid"*/}
              {/*  />*/}
              {/*</Form.Item>*/}

            </Flex>

            {/*用户原始输入，仅查看，不可编辑*/}
            <Form.Item label="客户搭配解析" style={{ width: '100%', margin: 4 }}>
              <Row gutter={[16, 16]}>
                {/* 第一部分：描述解析 */}
                <Col span={24}>
                  <h4>描述解析</h4>
                  <Row>
                    <Col span={6}>
                      <Form.Item
                        name={['userPreferFeatures', 'originClothCollocation', 'tops']}
                        label="上装"
                      >
                        <Input placeholder="输入上装" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        name={['userPreferFeatures', 'originClothCollocation', 'bottoms']}
                        label="下装"
                      >
                        <Input placeholder="输入下装" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        name={['userPreferFeatures', 'originClothCollocation', 'shoe']}
                        label="鞋子"
                      >
                        <Input placeholder="输入鞋子" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        name={['userPreferFeatures', 'originClothCollocation', 'others']}
                        label="其它"
                      >
                        <Input placeholder="输入其他" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        name={['userPreferFeatures', 'originClothCollocation', 'props']}
                        label="道具"
                      >
                        <Input placeholder="输入道具" />
                      </Form.Item>
                    </Col>
                  </Row>
                </Col>

                {/* 第二部分：提示词 */}
                <Col span={24} style={{ marginTop: -30 }}>
                  <h4>提示词</h4>
                  <Row>
                    <Col span={6}>
                      <Form.Item
                        name={['userPreferFeatures', 'transClothCollocation', 'tops']}
                        label="上装"
                      >
                        <Input placeholder="输入上装" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        name={['userPreferFeatures', 'transClothCollocation', 'bottoms']}
                        label="下装"
                      >
                        <Input placeholder="输入下装" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        name={['userPreferFeatures', 'transClothCollocation', 'shoe']}
                        label="鞋子"
                      >
                        <Input placeholder="输入鞋子" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        name={['userPreferFeatures', 'transClothCollocation', 'others']}
                        label="其它"
                      >
                        <Input placeholder="输入其它" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        name={['userPreferFeatures', 'transClothCollocation', 'props']}
                        label="道具"
                      >
                        <Input placeholder="输入道具" />
                      </Form.Item>
                    </Col>
                  </Row>
                </Col>
              </Row>
            </Form.Item>

            {/*GPT解析和用户创作时输入，以及平台管理员后台手工修改，需要可编辑*/}
            <Form.Item label="AI生成搭配" style={{ width: '100%', margin: 4, marginTop: -12 }}>
              <Row gutter={4}>
                <Col span={24}>
                  <Row gutter={[8, 4]}>
                    <Col span={10}>
                      <Form.Item
                        name={['aiGenFeatures', 'tops']}
                        label="上装"
                      >
                        <Input placeholder="输入上装" />
                      </Form.Item>
                    </Col>
                    <Col span={10}>
                      <Form.Item
                        name={['aiGenFeatures', 'bottoms']}
                        label="下装"
                      >
                        <Input placeholder="输入下装" />
                      </Form.Item>
                    </Col>
                    <Col span={10}>
                      <Form.Item
                        name={['aiGenFeatures', 'shoe']}
                        label="鞋子"
                      >
                        <Input placeholder="输入鞋子" />
                      </Form.Item>
                    </Col>
                    <Col span={10}>
                      <Form.Item
                        name={['aiGenFeatures', 'others']}
                        label="其它"
                      >
                        <Input placeholder="输入其它" />
                      </Form.Item>
                    </Col>
                    <Col span={10}>
                      <Form.Item
                        name={['aiGenFeatures', 'props']}
                        label="道具"
                      >
                        <Input placeholder="输入道具" />
                      </Form.Item>
                    </Col>
                  </Row>
                </Col>
              </Row>
            </Form.Item>

            <Form.Item
              label="补充特征"
              name="extInfo.features"
              rules={[{ required: false, message: '' }]}
              style={{ width: '100%' }}
              help={<span style={{
                color: 'red',
                fontSize: 10,
              }}>说明：补充特征兼容展示旧服装特征，新服装请使用上面的「AI生成搭配」</span>}
            >
              <TextArea rows={1} disabled={true} />
            </Form.Item>

            <Form.Item
              label="运营备注"
              name="extInfo.opsMemo"
              rules={[{ required: false }]}
              style={{ width: '100%' }}
            >
              <TextArea rows={1} />
            </Form.Item>

            {/*<Form.Item*/}
            {/*  label="客户案例"*/}
            {/*  name="extInfo.customerCase"*/}
            {/*  rules={[{ required: false }]}*/}
            {/*  style={{ width: '100%' }}*/}
            {/*>*/}
            {/*  <TextArea rows={1} placeholder="输入客户案例地址，http://或https://开头，多个以英文逗号隔开"*/}
            {/*            onChange={changeCustomerCase} />*/}
            {/*</Form.Item>*/}

            {/*{customerCase &&*/}
            {/*  <div className={'text12 font-pf width-100 color-1a'} style={{ margin: '-24px 0 8px 60px' }}>*/}
            {/*    快捷跳转 ：*/}
            {/*    {customerCase.map(item => <span key={item}>*/}
            {/*      <span className={'pointer color-brand'} style={{ textDecoration: 'underline' }}*/}
            {/*            onClick={() => window.open(item, '_blank')}>{item}</span>*/}
            {/*      &nbsp;&nbsp;&nbsp;&nbsp;*/}
            {/*    </span>)}*/}
            {/*  </div>*/}
            {/*}*/}

            <Form.Item
              label="客户搭配描述"
              rules={[{ required: false }]}
              name={['clothLoraTrainDetail', 'matchPrefer']}
              style={{ width: '100%' }}
            >
              <TextArea rows={1} disabled={true} />
            </Form.Item>

            <div className={'text12 color-error font-pf weight width-100'} style={{ margin: '-24px 0 8px 60px' }}>
              商家偏好
              ：{selectedTrainModelDetail?.merchantPreference ? selectedTrainModelDetail?.merchantPreference.memo : ''}
            </div>

            <ChildrenTabCard name={'clothTypeConfigs'} children={form.getFieldValue('clothTypeConfigs')}
                             opItemId={form.getFieldValue('id')} allTabList={getClothTypes(opItem)} form={form}
                             onChangeChildren={children => form.setFieldValue('clothTypeConfigs', children)}
                             needCopyValue={true}
                             formItems={(mainName, restField) => (<>
                               <Form.Item
                                 {...restField}
                                 label="激活词"
                                 name={[mainName, 'tags']}
                                 rules={[{ required: true, message: '请输入正确的激活词' }]}
                                 style={{ width: '100%' }}
                               >
                                 <Input maxLength={128} placeholder={'请输入激活词，如：tc2ss'} />
                               </Form.Item>
                               <Form.List name={[mainName, 'colorList']}>
                                 {fields => (
                                   <>
                                     {fields.map(({ key, name, ...restField }) => (
                                       <Form.Item key={key} {...restField} label={<ClothColorTitle
                                         item={form.getFieldValue('clothTypeConfigs')[mainName]['colorList'][name]}
                                         model={opItem} onChange={() => handleChangeColor()} />}
                                                  name={[name, 'value']}
                                                  rules={[{ required: false, message: '请输入正确的补充激活词' }]}
                                                  style={{ width: '100%' }}
                                       >
                                         <TextArea rows={4} placeholder={'请输入补充激活词，如：wearing xxxxxx'} />
                                       </Form.Item>
                                     ))}
                                   </>
                                 )}
                               </Form.List>

                               {/*<Form.Item*/}
                               {/*  {...restField}*/}
                               {/*  label="负向提示词"*/}
                               {/*  name={[mainName, 'extInfo.negative']}*/}
                               {/*  rules={[{ required: false, message: '请输入正确的负向提示词' }]}*/}
                               {/*  style={{ width: '100%' }}*/}
                               {/*>*/}
                               {/*  <InputWithTags rows={4}*/}
                               {/*                 tagsType={TagType.NEGATIVE}*/}
                               {/*                 placeholder={'请输入负向提示词'} />*/}
                               {/*</Form.Item>*/}

                               <Form.Item label="cfg" {...restField} name={[mainName, 'extInfo.cfg']}
                                          rules={[{ required: false, message: '请输入正确的cfg' }]}
                                          style={{ width: '100%' }} initialValue={3.5}>
                                 <InputNumber style={{ width: 600 }} />
                               </Form.Item>
                             </>)}
            />

            {form.getFieldValue('type') === 'CUSTOM' && form.getFieldValue('status') !== 'IN_TRAINING' &&
              <Form.Item label={<div>精选图{form.getFieldValue('userId') === 100128 ? ''
                : (form.getFieldValue('extInfo')['TEN_EXAMPLE_IMAGES'] ?
                  <span style={{ color: 'red' }}>10张</span> : '5张')}</div>}
                         name="exampleImages" style={{ width: '100%' }}
                         rules={[{ required: false, message: '请输入正确的精选图' }]}>
                <LoraImageSelector modelId={form.getFieldValue('id')} maxChoose={50} />
              </Form.Item>
            }

            <Form.Item
              label="状态"
              name="status"
              style={{ width: '100%', margin: '12px 0' }}
              rules={[{ required: true, message: '请选择正确的状态' }]}
              help={form.getFieldValue('status') !== 'ENABLED' && form.getFieldValue(['clothLoraTrainDetail', 'autoGenImg']) ?
                <span style={{
                  color: 'red',
                  fontSize: 10,
                }}>说明：商家选择了自动出图，当确认服装可交付后，请点击下方的「自动出图并交付」，系统在完成出图后自动审核通过</span> : ''}
            >
              <Radio.Group
                onChange={(e) => {
                  if (e.target.value === 'DISABLED' && form.getFieldValue('extInfo.needRefundMusePoint') !== 'Y') {
                    Modal.confirm({
                      title: '是否需要同时退还相应缪斯点？',
                      centered: true,
                      okText: '需要退点',
                      cancelText: '不退点',
                      onOk: () => {
                        form.setFieldValue('extInfo.needRefundMusePoint', 'Y');
                        form.setFieldValue('status', 'DISABLED');
                      },
                      onCancel: () => {
                        form.setFieldValue('status', e.target.value);
                      },
                    });

                    // 清除备注信息
                    form.setFieldValue('extInfo.memo', '');
                  } else if (e.target.value === 'ENABLED') {
                    // 清除审核不通过原因
                    form.setFieldValue('extInfo.disableReason', '');
                  } else if (e.target.value === 'TESTING') {
                    // 清除审核不通过原因
                    form.setFieldValue('extInfo.disableReason', '');
                  } else {
                    form.setFieldValue('status', e.target.value);
                  }
                }}
                options={[
                  { label: '训练中', value: 'IN_TRAINING' },
                  { label: '审核中', value: 'TESTING' },
                  {
                    label: (
                      <Tooltip title="商家选择了自动出图，需要确认可交付后，完成出图后自动审核通过">
                        审核通过
                      </Tooltip>
                    ),
                    value: 'ENABLED',
                    disabled: form.getFieldValue('status') !== 'ENABLED' && form.getFieldValue(['clothLoraTrainDetail', 'autoGenImg']),
                  },
                  {
                    label: form.getFieldValue('extInfo.refundMusePoint') === 'Y' ? '审核不通过(已退点)' : '审核不通过',
                    value: 'DISABLED',
                  },
                ]}
                optionType="button"
                buttonStyle="solid"
              />
            </Form.Item>

            {/*需要定义一个隐藏字段，否则上面退还时的设置form字段无法生效*/}
            <Form.Item
              label="退点"
              hidden={true}
              name={'extInfo.needRefundMusePoint'}  // 修改为数组形式
              style={{ width: '100%' }}
            >
              <Input disabled />
            </Form.Item>

            <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => prevValues.status !== currentValues.status}>
              {() => (
                <>
                  {/* 审核不通过原因 */}
                  {
                    form.getFieldValue('status') === 'DISABLED' &&
                    <>
                    <Form.Item
                      label="审核不通过原因"
                      name="extInfo.disableReason"
                      style={{ width: '100%' }}
                    >
                      <AutoComplete
                        placeholder="审核不通过时选填，如果填写前台将展示填写的原因"
                        options={internalDisableReasonOptions.map(option => ({
                          value: option,
                          label: option
                        }))}
                        allowClear
                      />
                    </Form.Item>
                    </>
                  }

                  {/* 备注信息 */}
                  {
                    ['TESTING', 'ENABLED'].includes(form.getFieldValue('status')) &&
                    <Form.Item
                      label="备注信息"
                      name="extInfo.memo"
                      style={{ width: '100%' }}
                    >
                      <AutoComplete
                        placeholder={'备注信息，客户可见'}
                        options={[
                          { value: '该服装花纹比较复杂，ai还原效果不理想，您可以使用细节修补进行修复～' },
                          { value: '由于拍摄带有人台线，出图也会有，您可以使用细节修补进行修复～' },
                          { value: 'ai目前对于格子、条纹、波点类的服装还原概率不理想，该服装正确率为60%，您可以多出图挑选一下～' }
                        ]}
                        allowClear
                      />
                    </Form.Item>
                  }
                </>
              )}
            </Form.Item>

            {/*商家上传服装时选的自动出图配置*/}
            {form.getFieldValue(['clothLoraTrainDetail', 'autoGenImg'])
              && form.getFieldValue(['clothLoraTrainDetail', 'autoGenImgParam', 'autoGenFaceIds'])
              && form.getFieldValue(['clothLoraTrainDetail', 'autoGenImgParam', 'autoGenSceneIds'])
              && form.getFieldValue(['clothLoraTrainDetail', 'autoGenImgParam', 'autoGenImgProportions'])
              && <>

                <Form.Item label="自动出图模特" style={{ width: '100%' }} name={['clothLoraTrainDetail', 'autoGenImgParam', 'autoGenFaceIds']}>
                  <Select
                    mode="tags" showSearch optionFilterProp="name"
                    options={faceList?.map((c: ElementConfig) => ({
                      key: c.id,
                      label: <Flex gap={4} align={'center'}>
                        <img src={c.showImage} alt={c.name} style={{ width: 24, height: 24 }} />
                        <span style={{ fontSize: 14 }}>{c.name}</span>
                      </Flex>,
                      value: c.id,
                      name: c.name,
                    }))}
                    suffixIcon={
                      <DownOutlined />
                    }
                    placeholder="点击选择模特"
                    style={{ width: '80%' }}
                  />
                </Form.Item>

                <Form.Item label="自动出图场景" style={{ width: '100%' }} name={['clothLoraTrainDetail', 'autoGenImgParam', 'autoGenSceneIds']}>
                  <Select
                    mode="tags" showSearch optionFilterProp="name"
                    options={sceneList?.map((c: ElementConfig) => ({
                      key: c.id,
                      label: <Flex gap={4} align={'center'}>
                        <img src={c.showImage} alt={c.name} style={{ width: 24, height: 24 }} />
                        <span style={{ fontSize: 14 }}>{c.name}</span>
                      </Flex>,
                      value: c.id,
                      name: c.name,
                    }))}
                    // open={false} // 强制关闭原生下拉菜单
                    suffixIcon={
                      <DownOutlined />
                    }
                    placeholder="点击选择场景"
                    style={{ width: '80%' }}
                  />
                </Form.Item>

                <Form.Item label="自动出图尺寸" style={{ width: '100%' }} name={['clothLoraTrainDetail', 'autoGenImgParam', 'autoGenImgProportions']}>
                  <span>{creationSizeOptions?.find(s => s.value === form.getFieldValue(['clothLoraTrainDetail', 'autoGenImgParam', 'autoGenImgProportions'])[0])?.label}</span>
                </Form.Item>
              </>

            }

          </Form>
        </Modal>
      }
      <Modal title="是否确认删除？" open={deleteId > 0} centered mask={true} closable={false} width={'300px'}
             styles={{
               header: { display: 'flex', flexDirection: 'row', justifyContent: 'center' },
               footer: { display: 'flex', flexDirection: 'row', justifyContent: 'center' },
             }} onCancel={() => setDeleteId(0)} onOk={() => commitDel()} maskClosable={false}>
        <div className={'row-center-block margin-top-12 margin-bottom-24'}
             style={{ color: 'red' }}>删除后将不能恢复，请谨慎操作！
        </div>
      </Modal>

      {previewImage && (
        <Image
          wrapperStyle={{ display: 'none' }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            afterOpenChange: (visible) => !visible && setPreviewImage(''),
          }}
          src={previewImage}
        />
      )}

      {/*查看lora训练详情，看抠图、打标结果，确认推进lora训练，浮层弹窗页面*/}

      {showTrainDetailModal && selectedTrainModelDetail?.clothLoraTrainDetail &&
        <Modal
          open={true}
          centered={true}
          onCancel={() => {
            // 取消打标变更
            setShowTrainDetailModal(false);

            // 设置需要手工替换文件为 false
            setIsManualReplacement(0);

            // 设置更新打标按钮为禁用状态
            setUpdateTxtBtnEnabled(false);

            // 清理选中的训练模型详情
            setSelectedTrainModelDetail(undefined);
          }}
          width={'auto'}
          closable={false}
          footer={[
            <Button key="cancel" style={{ marginRight: 8 }}
                    onClick={() => {
                      // 取消打标变更
                      setShowTrainDetailModal(false);

                      // 设置需要手工替换文件为 false
                      setIsManualReplacement(0);

                      // 设置更新打标按钮为禁用状态
                      setUpdateTxtBtnEnabled(false);

                      // 清理选中的训练模型详情
                      setSelectedTrainModelDetail(undefined);
                    }}>
              取消
            </Button>,

            <Flex gap={8} key={'retrain'} hidden={selectedTrainModelDetail.mainType !== 'SUB'}
                  style={{ display: 'inline-flex', margin: '0 10px' }}>
              <RetrainBtn id={selectedTrainModelDetail.id} onlyLabel={false}
                          materialType={selectedTrainModelDetail.clothLoraTrainDetail?.materialType}
                          onChange={() => fetchData(page, pageSize)} />

              <RetrainBtn id={selectedTrainModelDetail.id} onlyLabel={true}
                          materialType={selectedTrainModelDetail.clothLoraTrainDetail?.materialType}
                          onChange={() => fetchData(page, pageSize)} />
            </Flex>,

            <Flex key={'operator'} style={{ display: 'inline-flex', margin: '0 10px', alignItems: 'center' }}>
              <div>负责人：</div>
              <Select style={{ width: '80px' }}
                      value={relatedOperatorMobile}
                      onChange={value => {
                        setRelatedOperatorMobile(value);
                        assignPlatformOperator({ id: selectedTrainModelDetail?.id, operatorMobile: value }).then(res => {
                          if (res) {
                            message.success('设置成功');
                          } else {
                            message.error('操作失败');
                          }
                        });
                      }}>
                {allOperators.length > 0 && allOperators.map((item) => (
                  <Select.Option key={item.id} value={item.mobile}>{item.nickName}</Select.Option>
                ))}
              </Select>
            </Flex>,

            <Button key="updateTxt" disabled={!updateTxtBtnEnabled} type="primary"
                    onClick={() => (handleShowSelectTrainingMaterialModal(selectedTrainModelDetail))}>
              提交打标变更
            </Button>,

            <Button
              key="submit"
              type="primary"
              onClick={() => {
                setShowConfirmLoraModal(true);
                setLoraTrainType('flux');
                setMaxTrainStep(2500);
                setCutoutKeyword(['']);
                initDefaultRelatedTestConfig(selectedTrainModelDetail);
                if (selectedTrainModelDetail?.merchantPreference && selectedTrainModelDetail.merchantPreference.preferences && selectedTrainModelDetail.merchantPreference.preferences.length > 0) {
                  setTestClothCollocation(selectedTrainModelDetail.merchantPreference.preferences[0].clothCollocation);
                }
              }}
              disabled={(loraConfirmed() && selectedTrainModelDetail?.status !== 'TESTING') || !selectedTrainModelDetail?.labelRetFiles || updateTxtBtnEnabled || findLabelFailTxtFileName().length > 0}
            >
              {loraTrainDetailOkText()}
            </Button>,
          ]}
          styles={{
            mask: {
              backgroundColor: 'rgba(0,0,0,0.4)',
            },
          }}
        >
          <Flex vertical>
            {subModelIds.length > 1 &&
              <Flex>
                <Tabs defaultActiveKey="1" items={subModelIds.map((item,idx) => {return { key: String(item), label: `${models.find(e=>e.id === selectedTrainModelDetail.mainId)?.name}_颜色${idx+1}` }})} onChange={key => showTrainDetail(Number(key))}
                      indicator={{ size: 300 }} size={'large'} style={{ justifyContent: 'center',marginBottom: -16 }} type={'card'} />
              </Flex>
            }
            <div className="lora-detail-content">

            {/* 左半部分 */}
            <div className={'lora-detail-left'}>
              <div className={'lora-detail-left-inner'}>
                <div className={'lora-left-inner-top'}>
                  <div className="font-pf text14 weight" style={{ color: 'black' }}>模型</div>
                  <img alt="cloth model"
                       style={{ cursor: 'pointer' }}
                       onClick={() => handlePreviewUrl(selectedTrainModelDetail?.showImage)}
                       src={selectedTrainModelDetail?.showImage} />
                  <div className="font-pf text14" style={{ color: 'black' }}>{selectedTrainModelDetail?.name}</div>
                </div>

                <div className="lora-detail-section">
                  <div className="lora-detail-pair lora-detail-pair-row">
                    <div className="font-pf text14 weight" style={{ width: '80px', color: 'black' }}>操作人</div>
                    <div className="font-pf text12"
                         style={{ textAlign: 'left', paddingLeft: '4px' }}>{selectedTrainModelDetail?.operatorNick}</div>
                  </div>
                  <div className="lora-detail-pair lora-detail-pair-row">
                    <div className="font-pf text14 weight" style={{ width: '80px', color: 'black' }}>创建时间</div>
                    <div className="font-pf text12"
                         style={{ textAlign: 'left', paddingLeft: '4px' }}>{selectedTrainModelDetail?.createTime}</div>
                  </div>
                </div>

                {selectedTrainModelDetail?.clothLoraTrainDetail?.materialType !== 'face' && (
                  <div className="lora-detail-section">
                    <div className="lora-detail-pair">
                      <div className="font-pf text14 weight" style={{ color: 'black' }}>预处理
                        <span className={'text10'} style={{ marginLeft : 4 }}>
                          {selectedTrainModelDetail?.clothLoraTrainDetail?.prepareView?.taskId}
                        </span>
                        <span className={'text10 color-a0 normal margin-left-4'}>{getServerUrl('prepareView')}</span>
                      </div>
                      <div className="font-pf text12">
                        {selectedTrainModelDetail?.clothLoraTrainDetail?.prepareView?.status === 'COMPLETED'
                          ? (<>{selectedTrainModelDetail?.prepareViewFinishTime} <span
                            style={{ color: '#52c41a' }}>完成</span></>)
                          : <span style={{ color: '#666' }}>未完成</span>}
                      </div>
                    </div>
                  </div>
                )}

                <div className="lora-detail-section">
                  <div className="lora-detail-pair">
                    <div className="font-pf text14 weight" style={{ color: 'black' }}>抠图
                      <span className={'text10'} style={{ marginLeft : 4 }}>
                        {selectedTrainModelDetail?.clothLoraTrainDetail?.cutout?.taskId}
                      </span>
                      <span className={'text10 color-a0 normal margin-left-4'}>{getServerUrl('cutout')}</span>
                    </div>
                    <div className="font-pf text12">
                      {selectedTrainModelDetail?.cutoutFiles && selectedTrainModelDetail?.cutoutFiles?.length > 0
                        ? (<>{selectedTrainModelDetail?.cutoutFinishTime} <span
                          style={{ color: '#52c41a' }}>完成</span></>)
                        : <span style={{ color: '#666' }}>未完成</span>}
                    </div>
                  </div>
                  <div className="lora-detail-pair">
                    <div className="font-pf text14 weight" style={{ color: 'black' }}>打标
                      <span className={'text10'} style={{ marginLeft : 4 }}>
                        {selectedTrainModelDetail?.clothLoraTrainDetail?.label?.taskId}
                      </span>
                      <span className={'text10 color-a0 normal margin-left-4'}>{getServerUrl('label')}</span>
                    </div>
                    <div className="font-pf text12">
                      {selectedTrainModelDetail?.labelRetFiles && selectedTrainModelDetail?.labelRetFiles?.length > 0
                        ? (<>{selectedTrainModelDetail?.labelFinishTime} <span
                          style={{ color: '#52c41a' }}>完成</span></>)
                        : <span style={{ color: '#666' }}>未完成</span>}
                    </div>
                  </div>
                </div>

                <div className="lora-detail-section">
                  <div className="lora-detail-pair">
                    <div className="font-pf text14 weight" style={{ color: 'black' }}>训练
                      <span className={'text10'} style={{ marginLeft : 4 }}>
                        {selectedTrainModelDetail?.clothLoraTrainDetail?.lora?.taskId}
                      </span>
                      <span className={'text10 color-a0 normal margin-left-4'}>{getServerUrl('lora')}</span>
                    </div>
                    <div className="font-pf text12">
                      {selectedTrainModelDetail && !selectedTrainModelDetail?.loraStatus &&
                        <span style={{ color: '#666' }}>状态：未开始</span>
                      }
                      {loraConfirmed() &&
                        <>
                          <div className="font-pf text12" style={{ margin: '3px 0' }}>{loraConfirmedTime()}<span
                            style={{ color: '#52c41a', marginLeft: '8px' }}>已确认</span></div>
                          <div style={{
                            backgroundColor: '#f9f9f9',
                            border: '1px solid #f0f0f0',
                            borderRadius: '2px',
                            padding: '3px 6px',
                            marginBottom: '2px',
                            fontSize: '12px',
                            display: 'grid',
                            gridTemplateColumns: '1fr 1fr',
                            gridGap: '2px 8px',
                          }}>
                            <div><b>类型:</b> {selectedTrainModelDetail?.clothLoraTrainDetail?.loraType || 'sdxl'}</div>
                            <div><b>次数:</b> {selectedTrainModelDetail?.clothLoraTrainDetail?.maxTrainStep || 4000}</div>

                            {selectedTrainModelDetail?.clothLoraTrainDetail?.lr &&
                              <div><b>学习率:</b> {selectedTrainModelDetail?.clothLoraTrainDetail?.lr}</div>
                            }
                            {selectedTrainModelDetail?.clothLoraTrainDetail?.contentOrStyle &&
                              <div><b>内容:</b> {selectedTrainModelDetail?.clothLoraTrainDetail?.contentOrStyle}</div>
                            }

                            {selectedTrainModelDetail?.clothLoraTrainDetail?.rank &&
                              <div><b>Rank:</b> {selectedTrainModelDetail?.clothLoraTrainDetail?.rank}</div>
                            }
                            {selectedTrainModelDetail?.clothLoraTrainDetail?.alpha &&
                              <div><b>Alpha:</b> {selectedTrainModelDetail?.clothLoraTrainDetail?.alpha}</div>
                            }

                            {selectedTrainModelDetail?.clothLoraTrainDetail?.dropout &&
                              <div><b>Dropout:</b> {selectedTrainModelDetail?.clothLoraTrainDetail?.dropout}</div>
                            }
                            {selectedTrainModelDetail?.clothLoraTrainDetail?.resolution &&
                              <div><b>Res:</b> {selectedTrainModelDetail?.clothLoraTrainDetail?.resolution}</div>
                            }
                          </div>
                        </>
                      }
                      {selectedTrainModelDetail && selectedTrainModelDetail?.loraStatus === 'QUEUED' &&
                        <span style={{ color: '#1890ff' }}>状态：排队中</span>
                      }
                      {selectedTrainModelDetail && selectedTrainModelDetail?.loraStatus === 'RUNNING' && (
                        <>
                          <div>{selectedTrainModelDetail?.loraStartTime}<span
                            style={{ color: '#fa8c16', marginLeft: '8px' }}>开始</span></div>
                          <div style={{ marginTop: 4 }}><span style={{ color: '#fa8c16' }}>任务状态：运行中</span></div>
                        </>
                      )}
                      {selectedTrainModelDetail && selectedTrainModelDetail?.loraStatus === 'COMPLETED' &&
                        <>
                          <div>{selectedTrainModelDetail?.loraStartTime}<span
                            style={{ color: '#52c41a', marginLeft: '8px' }}>开始</span></div>
                          <div style={{ marginTop: 4 }}>{selectedTrainModelDetail?.loraFinishTime}<span
                            style={{ color: '#52c41a', marginLeft: '8px' }}>完成</span></div>
                          <div style={{ marginTop: 4 }}><span style={{ color: '#52c41a' }}>状态：已完成</span></div>
                        </>
                      }
                    </div>
                  </div>
                </div>

                <TestImageStatus item={selectedTrainModelDetail} />

                <ReviewStatus
                  model={selectedTrainModelDetail}
                  users={allAdmins}
                />

                <div className="lora-detail-section">
                  <div className="lora-detail-pair lora-detail-pair-row">
                    <div className="font-pf text14 weight" style={{ color: 'black' }}>交付时间</div>
                    <div className="font-pf text12">
                      {selectedTrainModelDetail && selectedTrainModelDetail?.extInfo ? selectedTrainModelDetail?.extInfo['deliveryTime'] : ''}
                    </div>
                  </div>
                  <div className="lora-detail-pair lora-detail-pair-row">
                    <div className="font-pf text14 weight" style={{ color: 'black' }}>模型ID</div>
                    <div className="font-pf text12">{selectedTrainModelDetail?.id}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* 右半部分 */}
            <div className="lora-detail-right">

              {/* 原图 */}
              <Flex align={'baseline'}>
                <div className="font-pf text14 weight"
                     style={{ marginTop: 10, cursor: 'pointer', display: 'flex', alignItems: 'center' }} onClick={() => {
                  navigator.clipboard.writeText(selectedTrainModelDetail?.clothLoraTrainDetail?.clothDir).then(() => {
                    message.info('目录已复制到剪贴板');
                  });
                }}>
                  原图：<span style={{
                  fontSize: 12,
                  textDecoration: 'underline',
                }}>{selectedTrainModelDetail?.clothLoraTrainDetail?.clothDir}</span>
                  <IconFont type={'icon-icon_fuzhi'} style={{ fontSize: 18, marginLeft: 5 }} />
                </div>

                <Button type={'default'} size={'small'} style={{ borderRadius: 8, fontSize: 12, marginLeft: 8 }}
                        onClick={() => setShowOriginal(!showOriginal)}>{showOriginal ? '隐藏' : '展开'}</Button>

                <Button type={'default'} size={'small'}
                        loading={downloading}
                        style={{ borderRadius: 8, fontSize: 12, marginLeft: 8 }}
                        onClick={() => {
                          setDownloading(true);

                          let urls = getAllUploadImgs();
                          batchDownload(urls, selectedTrainModelDetail?.name + '_' + new Date().getTime()).then(res => {
                            if (res) {
                              download(res);
                              message.success('下载完成');
                            }
                          }).finally(() => setDownloading(false));

                        }}>批量下载原图
                </Button>

              </Flex>
              <div className="lora-img-list">
                {showOriginal && getAllUploadImgs().map((f: string, index: number) => (
                  <div key={index}
                       className="lora-detail-img-item">
                    <img src={f} alt={''}
                         onClick={() => handlePreviewUrl(f, index, getAllUploadImgs())} />
                  </div>
                ))}
              </div>

              {/*抠图结果*/}
              {selectedTrainModelDetail?.cutoutFiles && selectedTrainModelDetail?.cutoutFiles?.length > 0 && (
                <>
                  <Flex align={'baseline'}>
                    <div className="font-pf text14 weight"
                         style={{ marginTop: 10, cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                         onClick={() => {
                           navigator.clipboard.writeText(selectedTrainModelDetail?.clothLoraTrainDetail?.clothDir).then(() => {
                             message.info('目录已复制到剪贴板');
                           });
                         }}
                    >
                      <span style={{ color: 'black' }}>抠图结果：</span>
                      <span style={{
                        fontSize: 12,
                        textDecoration: 'underline',
                      }}>{selectedTrainModelDetail?.clothLoraTrainDetail?.cutoutRetDir}</span>
                      <IconFont type={'icon-icon_fuzhi'} style={{ fontSize: 18, marginLeft: 5, marginRight: 5 }} />
                    </div>
                    <Button type={'default'} size={'small'} style={{ borderRadius: 8, fontSize: 12 }}
                            onClick={() => setShowCutout(!showCutout)}>{showCutout ? '隐藏' : '展开'}</Button>
                  </Flex>

                  <div className="lora-img-list">
                    {showCutout && selectedTrainModelDetail && selectedTrainModelDetail?.cutoutFiles && selectedTrainModelDetail?.cutoutFiles.map((f: FileVO, index: number) => {
                      if (f.type === 'img') {
                        return (
                          <div key={index} className="lora-detail-img-item">
                            <img
                              src={f.imgUrl}
                              alt={f.fileName}
                              onClick={() => handlePreviewUrl(f.imgUrl, index, selectedTrainModelDetail?.cutoutFiles.map((f: FileVO) => (f.imgUrl)))}
                            />
                            <div style={{ maxWidth: 160, fontSize: 12 }}>{f.fileName}</div>
                          </div>
                        );
                      } else {
                        return (
                          <div key={index} className="lora-detail-img-item">
                            <textarea
                              value={updateTxtFiles.get(f.fileDir + '/' + f.fileName) || f.textContent}
                              style={{ fontSize: 12 }}
                              disabled={true}
                              onChange={e => onLabelTextChange(e, f)}
                            />
                            <div
                              style={{
                                maxWidth: 160,
                                fontSize: 12,
                                color: getTxtFileColorByFile(f),
                              }}
                            >
                              {f.fileName}
                            </div>
                          </div>
                        );
                      }
                    })}
                  </div>
                </>
              )}

              {/*全局快速替换打标词*/}
              {selectedTrainModelDetail && selectedTrainModelDetail?.splitTags &&
                <>
                  <Flex align={'baseline'}>
                    <div className="font-pf text14 weight"
                         style={{ display: 'flex', alignItems: 'center' }}>
                      <span style={{ color: 'black' }}>全局快速替换打标词：</span>
                    </div>
                    <Button type={'default'} size={'small'} style={{ borderRadius: 8, fontSize: 12 }}
                            onClick={() => setShowSplitTags(!showSplitTags)}>{showSplitTags ? '隐藏' : '展开'}</Button>
                  </Flex>

                  {showSplitTags &&
                    <div style={{
                      border: '1px solid #d9d9d9',
                      padding: '16px',
                      backgroundColor: '#f5f5f5',
                      borderRadius: '8px',
                    }}>
                      <h4 style={{ color: 'black' }}>全局快速替换打标词</h4>
                      <Row gutter={[16, 16]} style={{ display: 'flex', flexWrap: 'wrap' }}>
                        {selectedTrainModelDetail && selectedTrainModelDetail.splitTags.map((tag, index) => (
                          <Col
                            key={index}
                            style={{
                              flex: '1 1 auto', // 使列宽度自适应
                              borderRight: index !== selectedTrainModelDetail.splitTags.length - 1 ? '1px solid #d9d9d9' : 'none',
                              textAlign: 'center',
                              minWidth: '150px',
                              maxWidth: '250px',
                            }}
                          >
                            <div style={{
                              display: 'flex',
                              flexDirection: 'column',
                              justifyContent: 'space-between',
                              height: '100%',
                            }}>
                              {/* 第一行：展示所有原始的 tag */}
                              <div style={{ padding: '8px', borderBottom: '1px solid #d9d9d9' }}>
                                <span>{tag}</span>
                              </div>

                              {/* 第二行：Input 输入框 */}
                              <div style={{ padding: '8px', borderBottom: '1px solid #d9d9d9' }}>
                                <input defaultValue={''} style={{ width: '100%' }} placeholder={`替换 ${tag}`}
                                       ref={el => setReplaceInputRef(index, el)} />
                              </div>

                              {/* 第三行：确定替换按钮 */}
                              <div style={{ padding: '8px' }}>
                                <Button style={{ width: '100%' }}
                                        onClick={() => {
                                          //@ts-ignore
                                          const inputValue = replaceLabelTagsInputRefs.current[index].value;
                                          if (inputValue) {
                                            const newMap = new Map(updateTxtFiles);
                                            for (const f of selectedTrainModelDetail?.labelRetFiles) {
                                              if (f.textContent && !f.fileDir.endsWith('label')) {
                                                const key = f.fileDir + '/' + f.fileName;
                                                let oldVal = updateTxtFiles.get(key) || f.textContent;
                                                let newVal = replaceTag(oldVal, selectedTrainModelDetail.splitTags[index], inputValue);
                                                if (newVal !== oldVal) {
                                                  newMap.set(key, newVal);
                                                }
                                              }
                                            }

                                            setUpdateTxtFiles(newMap);
                                            setUpdateTxtBtnEnabled(true);
                                          }

                                        }}>全局替换
                                </Button>
                              </div>
                            </div>
                          </Col>
                        ))}
                      </Row>
                    </div>
                  }
                </>
              }


              {/*打标结果*/}
              {selectedTrainModelDetail && selectedTrainModelDetail?.labelRetFiles && groupedLabelFiles && Array.from(groupedLabelFiles.entries()).map(([fileDir, files]) => (
                <>
                  <div className="font-pf text14 weight"
                       style={{ marginTop: 10, cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                       onClick={() => {
                         navigator.clipboard.writeText(fileDir).then(() => {
                           message.info('目录已复制到剪贴板');
                         });
                       }}>
                    <span style={{ color: 'black' }}>{getLabelRetDirShowName(fileDir)}打标结果：</span><span
                    style={{ fontSize: 12, textDecoration: 'underline' }}>{fileDir}</span>
                    <IconFont type={'icon-icon_fuzhi'} style={{ fontSize: 18, marginLeft: 5 }} />
                  </div>

                  {findLabelFailTxtFileName().length > 0 &&
                    <div style={{ color: 'red', fontSize: 16, fontWeight: 500 }}>
                      请手工更新打标失败文件后，提交打标变更：{findLabelFailTxtFileName().join(', ')}
                    </div>
                  }

                  <div className="lora-img-list">
                    {files.sort((a, b) => a.fileName.localeCompare(b.fileName)).map((f: FileVO, index: number) => (
                      <div key={index} className="label-ret-item"
                           onDrop={(event) => {
                             event.preventDefault();
                             //只响应图片控件的拖拽事件
                             if (f.type !== 'img') {
                               return;
                             }
                             let filePath = f.fileDir + '/' + f.fileName;
                             let deleted = labelImgFiles.get(filePath)?.deleted;
                             if (deleted) {
                               message.error('该图片已被删除，无法替换');
                             } else {
                               const file = event.dataTransfer.files[0];
                               handleLabelImgChange(file, f);
                             }
                           }}
                           onDragOver={(e) => {
                             e.preventDefault();
                             if (f.type !== 'img') {
                               return;
                             }
                           }}>
                        {(() => {
                          // 如果是图片，则显示图片
                          if (f.type === 'img') {
                            let filePath = f.fileDir + '/' + f.fileName;

                            // 如果文件被删除，则不显示
                            let deleted = labelImgFiles.get(filePath)?.deleted;

                            return <>
                              <div style={{ position: 'relative' }}>
                                <img
                                  src={labelImgFiles.get(filePath)?.url ? labelImgFiles.get(filePath)?.url : f.imgUrl}
                                  alt={''}
                                  onClick={() => handlePreviewUrl(labelImgFiles.get(filePath)?.url ? labelImgFiles.get(filePath)?.url : f.imgUrl)} />

                                {/*上传新的打标图片*/}
                                <Tooltip title={'点击或拖拽至此，替换图片'}>
                                  <IconFont type={'icon-icon_qiehuan'}
                                            style={{
                                              position: 'absolute',
                                              top: 0,
                                              right: 0,
                                              cursor: 'pointer',
                                              fontSize: 24,
                                            }}
                                            onClick={() => {
                                              if (deleted) {
                                                message.error('该图片已被删除，无法替换');
                                              } else {
                                                labelImgInputRefs.current[filePath].click();
                                              }
                                            }} />
                                </Tooltip>

                                {/*删除打标图片*/}
                                {!deleted && (
                                  <Tooltip title={'删除图片'} placement="bottom">
                                    <IconFont
                                      style={{ position: 'absolute', top: 36, right: 0, cursor: 'pointer', fontSize: 24 }}
                                      type={'icon-icon_shanchu'}
                                      onClick={() => {
                                        const newLabelImgFiles = new Map(labelImgFiles);
                                        newLabelImgFiles.set(filePath, {
                                          filePath: f.fileDir + '/' + f.fileName,
                                          url: '',
                                          deleted: true,
                                        });
                                        setLabelImgFiles(newLabelImgFiles);
                                        setUpdateTxtBtnEnabled(true);
                                      }} />
                                  </Tooltip>
                                )}

                                {/*恢复删除的图片*/}
                                {deleted && (
                                  <Tooltip title={'撤销删除'} placement="bottom">
                                    <RollbackOutlined
                                      style={{
                                        position: 'absolute',
                                        top: 36,
                                        right: 4,
                                        cursor: 'pointer',
                                        fontSize: 18,
                                        color: 'red',
                                      }}
                                      onClick={() => {
                                        const newLabelImgFiles = new Map(labelImgFiles);
                                        newLabelImgFiles.delete(filePath);
                                        setLabelImgFiles(newLabelImgFiles);
                                        setUpdateTxtBtnEnabled(true);
                                      }} />
                                  </Tooltip>
                                )}

                                <input
                                  type="file"
                                  ref={(el) => labelImgInputRefs.current[filePath] = el}
                                  onChange={(e) => {
                                    // @ts-ignore
                                    const file = e.target.files[0];
                                    handleLabelImgChange(file, f);
                                  }}
                                  accept="image/png"
                                  style={{ display: 'none' }}
                                />
                              </div>

                              {deleted && <div style={{ maxWidth: 160, fontSize: 12, color: getImgFileColorByFile(f) }}>
                                <del>{f.fileName}</del>
                              </div>}
                              {!deleted && <div style={{
                                maxWidth: 160,
                                fontSize: 12,
                                color: getImgFileColorByFile(f),
                              }}>{f.fileName}</div>}

                            </>;

                            // txt文本
                          } else {
                            return <>
                              <textarea value={updateTxtFiles.get(f.fileDir + '/' + f.fileName) || f.textContent}
                                        style={{ fontSize: 12 }}
                                        onChange={e => onLabelTextChange(e, f)}
                              />
                              <div style={{
                                maxWidth: 160,
                                fontSize: 12,
                                color: getTxtFileColorByFile(f),
                              }}>{f.fileName}</div>
                            </>;
                          }
                        })()}

                      </div>
                    ))}

                  </div>

                </>

              ))}

              {selectedTrainModelDetail && selectedTrainModelDetail?.detailGarmentTypes &&
                <div style={{
                  border: '1px solid #d9d9d9',
                  padding: '16px',
                  backgroundColor: '#f5f5f5',
                  borderRadius: '8px',
                }}>
                  <h4 style={{ color: 'black' }}>快速替换服装类型描述（detail_garment_type）</h4>
                  <Row gutter={[16, 16]} style={{ display: 'flex', flexWrap: 'wrap' }}>
                    {selectedTrainModelDetail && selectedTrainModelDetail.detailGarmentTypes.map((tag, index) => (
                      <Col
                        key={index}
                        style={{
                          flex: '1 1 auto', // 使列宽度自适应
                          borderRight: index !== selectedTrainModelDetail.splitTags.length - 1 ? '1px solid #d9d9d9' : 'none',
                          textAlign: 'center',
                          minWidth: '150px',
                          maxWidth: '250px',
                        }}
                      >
                        <div style={{
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'space-between',
                          height: '100%',
                        }}>
                          {/* 第一行：展示所有原始的 tag */}
                          <div style={{ padding: '8px', borderBottom: '1px solid #d9d9d9' }}>
                            <span>{tag}</span>
                          </div>

                          {/* 第二行：Input 输入框 */}
                          <div style={{ padding: '8px', borderBottom: '1px solid #d9d9d9' }}>
                            <input defaultValue={''} style={{ width: '100%' }} placeholder={`替换 ${tag}`}
                                   ref={el => setReplaceLabelGarmentTypesInputRefs(index, el)} />
                          </div>

                          {/* 第三行：确定替换按钮 */}
                          <div style={{ padding: '8px' }}>
                            <Button style={{ width: '100%' }}
                                    onClick={() => {
                                      //@ts-ignore
                                      const inputValue = replaceLabelGarmentTypesInputRefs.current[index].value;
                                      if (inputValue) {
                                        const newMap = new Map(updateTxtFiles);
                                        for (const f of selectedTrainModelDetail?.labelRetFiles) {
                                          if (f.textContent && f.fileDir.endsWith('label') && f.textContent.includes('detail_garment_type')) {
                                            const key = f.fileDir + '/' + f.fileName;
                                            let oldVal = updateTxtFiles.get(key) || f.textContent;
                                            let newVal = replaceDetailGarmentType(oldVal, selectedTrainModelDetail?.detailGarmentTypes[index], inputValue);
                                            if (newVal !== oldVal) {
                                              newMap.set(key, newVal);
                                            }
                                          }
                                        }

                                        setUpdateTxtFiles(newMap);
                                        setUpdateTxtBtnEnabled(true);
                                      }

                                    }}>全局替换
                            </Button>
                          </div>
                        </div>
                      </Col>
                    ))}
                  </Row>
                </div>
              }
            </div>
          </div>
          </Flex>
        </Modal>
      }

      {assignLora &&
        <Modal open={true} title={'设置指定用户'} onCancel={() => {
          setAssignLora(null);
          setAssignUserId(null);
        }} onOk={confirmAssignTo}>
          <Flex vertical gap={16}>
            {assignLora.userRole !== 'OPERATOR' &&
              <Alert message={'当前模型已归属于商家"' + assignLora.userNick + '"，请谨慎操作！！！'}
                     type={'error'} />
            }
            <div>目标模型：{assignLora.name}</div>
            <div>
              指定商家：<Select options={masterOptions} style={{ width: 176 }} showSearch
                               optionFilterProp="label" defaultActiveFirstOption={true} value={assignUserId}
                               onChange={(e) => setAssignUserId(e)} />
            </div>
            <div style={{ color: 'red' }}>注意：指定给商户后，将自动在该商户下生成100张套内免费图片数</div>
          </Flex>
        </Modal>
      }

      {showConfirmLoraModal &&
        <Modal open={showConfirmLoraModal} width={800} centered
               title={'设置lora训练参数'}
               onCancel={() => {
                 resetTrainParams();
                 setShowConfirmLoraModal(false);
               }}
               okText={needCutoutAgain ? '重新抠图打标' : '提交lora训练'}
               onOk={() => {
                 confirmLoraForm.validateFields()
                   .then(() => {
                     //重新抠图
                     if (needCutoutAgain) {
                       onCutoutAgain();

                     } else {
                       onConfirmLora();
                     }
                   })
                   .catch(info => {
                     console.log('Validate Failed:', info);
                   });
               }}>

          <Form layout="vertical" style={{ paddingLeft: 12 }} form={confirmLoraForm}>
            <Form.Item
              label="是否需要重新抠图："
              name="needCutoutAgain"
              style={{ width: '100%' }}
              initialValue={false}
            >
              <Radio.Group value={needCutoutAgain} onChange={e => setNeedCutoutAgain(e.target.value)}>
                <Radio value={false}>不用，直接训练</Radio>
                <Radio value={true}>重新抠图</Radio>
              </Radio.Group>
            </Form.Item>

            {needCutoutAgain && <>
              <Form.Item label="抠图关键词（可空，可多选）："
                         name="cutoutKeyword"
                         style={{ width: '100%' }}>
                <Select
                  key={'cutoutKeyword'}
                  mode="tags"
                  placeholder="请选择（可多选）"
                  options={cutoutKeywordOptions}
                  value={cutoutKeyword}
                  allowClear={true}
                  onChange={value => setCutoutKeyword(value)}
                />
              </Form.Item>

              <Form.Item label="是否需要重新分析图片："
                         name="prepareViewAgainWhenCutoutAgain"
                         style={{ width: '100%' }}>
                <Radio.Group value={prepareViewAgainWhenCutoutAgain} defaultValue={false}
                             onChange={e => setPrepareViewAgainWhenCutoutAgain(e.target.value)}>
                  <Radio value={false}>不用，直接抠图（默认）</Radio>
                  <Radio value={true}>重新分析图片，再抠图</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item label="是否需要抠图：" name="cutoutType" style={{ width: '100%' }}
                         valuePropName={'cutoutType'}>
                <Radio.Group value={cutoutType} defaultValue={'N'}
                             onChange={e => setCutoutType(e.target.value)}>
                  <Radio value={'uncut'}>不抠图</Radio>
                  <Radio value={'default'}>抠服装</Radio>
                  <Radio value={'clothAndMannequin'}>抠服装和人台</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item label="是否大比例抠图剪裁（实验参数）："
                         name="cut4ScaleUp"
                         style={{ width: '100%' }}
                         valuePropName={'cut4ScaleUp'}>
                <Radio.Group value={cut4ScaleUp}
                             defaultValue={'N'}
                             onChange={e => setCut4ScaleUp(e.target.value)}>
                  <Radio value={'N'}>不用，保持现状</Radio>
                  <Radio value={'Y'}>需要，抠图结果服装像素占比更大</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item label="抠图模型："
                         name="cutoutModel"
                         style={{ width: '100%' }}
                         valuePropName={'cutoutModel'}>
                <Radio.Group value={cutoutModel}
                             defaultValue={'sam_vit_h_cloth'}
                             onChange={e => setCutoutModel(e.target.value)}>
                  <Radio value={'sam_vit_h_cloth'}>v1(默认sam_vit_h_cloth)</Radio>
                  <Radio value={'sam_hq_h_cloth'}>v2(sam_hq_h_cloth)</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item label={<>打标prompt：<LoraPromptsSwitch value={clothDetailsPrompt} tag={labelType}
                                                                onChange={handleChangeClothPrompt} /></>}
                         name="clothDetailsPrompt" style={{ width: '100%' }}
                         valuePropName={'clothDetailsPrompt'}>
                <TextArea rows={4} value={clothDetailsPrompt} />
              </Form.Item>

              <Form.Item label="抠图样本保存尺寸："
                         name="imageSize"
                         style={{ width: '100%' }}>
                <Input style={{ width: '100%' }}
                       defaultValue={'1024'}
                       value={imageSize}
                       onChange={(e) => {
                         setImageSize(e.target.value);
                       }} />
              </Form.Item>

              <Form.Item label="抠图样本是否保存为方形（默认否）："
                         name="isSquare"
                         style={{ width: '100%' }}
                         hidden={true}>
                <Radio.Group defaultValue={false} value={isSquare} onChange={e => setIsSquare(e.target.value)}>
                  <Radio value={false}>否，按原始比例</Radio>
                  <Radio value={true}>是，扩展为方形</Radio>
                </Radio.Group>
              </Form.Item>
            </>}

            {!needCutoutAgain && canShowTrainParams && <>

              <Row style={{ marginTop: -4 }}>
                <Col span={4}>
                  <Form.Item label="训练类型：" style={{ marginTop: -12 }}
                             rules={[{ required: true, message: '请上传展示图片' }]}>
                    <Radio.Group value={loraTrainType}
                                 onChange={e => {
                                   setLoraTrainType(e.target.value);
                                   setMaxTrainStep(e.target.value === 'flux' ? getDefaultFluxSteps() : 4000);
                                 }}>
                      <Radio.Button value={'flux'}>FLUX</Radio.Button>
                    </Radio.Group>
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item label="训练步数：" rules={[{ required: true, message: '请指定步数' }]}
                             style={{ marginTop: -12 }}>
                    <InputNumber min={1} precision={0} style={{ width: '60%' }}
                                 value={maxTrainStep}
                                 onChange={(e) => {
                                   setMaxTrainStep(e || (loraTrainType === 'flux' ? getDefaultFluxSteps() : 4000));
                                 }}
                    />
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item label="学习率：" rules={[{ required: true }]}
                             style={{ marginTop: -12 }}>
                    <InputNumber min={0.00005} max={0.0005} style={{ width: '80%' }}
                                 value={trainParams['lr']}
                                 onChange={(e) => {
                                   setTrainParams({ ...trainParams, lr: e || 0.00020 });
                                 }}
                    />
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item label="学习内容：" rules={[{ required: true }]}
                             style={{ marginTop: -12 }}>
                    <Select style={{ width: '60%' }}
                            options={[{ label: '内容', value: 'content' }, { label: '风格', value: 'style' }]}
                            value={trainParams['contentOrStyle']}
                            onChange={(e) => {
                              setTrainParams({ ...trainParams, contentOrStyle: e || 'content' });
                            }}
                    />
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item label="rank：" rules={[{ required: true }]}
                             style={{ marginTop: -12 }}>
                    <InputNumber min={8} max={256} precision={0} style={{ width: '50%' }}
                                 value={trainParams['rank']}
                                 onChange={(e) => {
                                   setTrainParams({ ...trainParams, rank: e || 32 });
                                 }}
                    />
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item label="alpha：" rules={[{ required: true }]}
                             style={{ marginTop: -12 }}>
                    <InputNumber min={1} max={256} precision={0} style={{ width: '50%' }}
                                 value={trainParams['alpha']}
                                 onChange={(e) => {
                                   setTrainParams({ ...trainParams, alpha: e || 16 });
                                 }}
                    />
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item label="dropout：" rules={[{ required: true }]}
                             style={{ marginTop: -12 }}>
                    <InputNumber min={0.01} max={0.3} style={{ width: '50%' }}
                                 value={trainParams['dropout']}
                                 onChange={(e) => {
                                   setTrainParams({ ...trainParams, dropout: e || 0.2 });
                                 }}
                    />
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item label="分辨率：" rules={[{ required: true }]}
                             style={{ marginTop: -12 }}>
                    <Input style={{ width: '80%' }} value={trainParams['resolution']}
                           onChange={(e) => {
                             setTrainParams({ ...trainParams, resolution: e.target.value });
                           }}
                    />
                  </Form.Item>
                </Col>
                <Col span={16}>
                  <Form.Item label={<>分层设置：
                    <SystemConfigSwitch value={trainParams['trainExtInfo']}
                                        keyLabels={[
                                          { key: 'NONE', label: '不分层' },
                                          { key: 'LORA_TRAIN_EXT_INFO.cloth', label: '默认分层' }]}
                                        onChange={(value) => setTrainParams({
                                          ...trainParams,
                                          trainExtInfo: value,
                                        })} />
                  </>} rules={[{ required: false }]} style={{ marginTop: -12 }}>
                    <TextArea rows={1} style={{ width: '100%' }} value={trainParams['trainExtInfo']}
                              onChange={(e) => {
                                setTrainParams({ ...trainParams, trainExtInfo: e.target.value });
                              }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/*{selectedTrainModelDetail &&*/}
              {/*  <MerchantPreferenceOption merchantPreference={selectedTrainModelDetail?.merchantPreference}*/}
              {/*                            model={selectedTrainModelDetail} />*/}
              {/*}*/}
            </>}

          </Form>
        </Modal>
      }

      {deliveryModel &&
        <Modal open={true} width={800} centered
               title={'交付给商家X张图片'}
               onCancel={() => setDeliveryModel(null)}
               onOk={() => {
                 deliveryForm.validateFields()
                   .then(() => {
                     //重新抠图
                     if (needCutoutAgain) {
                       onCutoutAgain();

                     } else {
                       onConfirmLora();
                     }
                   })
                   .catch(info => {
                     console.log('Validate Failed:', info);
                   });
               }}>

          <Form layout="vertical" style={{ paddingLeft: 12, marginTop: 16 }} form={deliveryForm}>
            <MerchantPreferenceOption merchantPreference={deliveryModel?.merchantPreference}
                                      model={deliveryModel} />
          </Form>
        </Modal>
      }

      {merchantPreferenceSetting &&
        <MerchantPreferenceSetting userId={merchantPreferenceSetting.userId}
                                   userNick={merchantPreferenceSetting.userNick}
                                   faceList={faceList} sceneList={sceneList}
                                   merchantPreference={merchantPreferenceSetting.merchantPreference}
                                   onCancel={() => setMerchantPreferenceSetting(null)}
                                   changeMerchantPreference={() => handleChangeMerchantPreference()} />
      }

      {/* 图片预览 */}
      <ImgPreview
        previewVisible={previewVisible}
        handleCancelPreview={handleCancelPreview}
        previewImage={previewImage}
        needSwitch={!!previewImgs}
        previewIdx={previewIdx}
        previewImgs={previewImgs}
        needWatermark={false}
      />

      {recordModelId &&
        <LoraImageModal modelId={recordModelId} queryAll={true} onClose={() => setRecordModelId(null)} />
      }

      {/* 选择训练素材的弹窗 */}
      <SelectTrainingMaterialModal
        // 是否显示
        open={showSelectTrainingMaterialModal}
        // 取消
        onCancel={() => setShowSelectTrainingMaterialModal(false)}
        // 确认
        onConfirm={handleConfirmTrainingMaterial}
        // 训练素材
        labelImgFiles={labelImgFiles}
        // 设置训练素材
        setLabelImgFiles={setLabelImgFiles}
        // 模型
        selectedTrainModelDetail={selectedTrainModelDetail}
        // 分组标签文件
        groupedLabelFiles={groupedLabelFiles}
        // 预览图片
        handlePreviewUrl={handlePreviewUrl}
        // 获取标签文件夹显示名称
        getLabelRetDirShowName={getLabelRetDirShowName}
      />

      {reviewerSelectorVisible && reviewerSelector}

    </PageContainer>
  );
};

export default Lora;
