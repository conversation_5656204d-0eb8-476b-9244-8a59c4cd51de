.lora-dialog-block {
  width: 1300px;
  height: auto;
  border-radius: 8px;
  opacity: 1;

  display: flex;
  gap: 0px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 10px 15px;
  box-shadow: 0 4px 28px 0 rgba(99, 102, 114, 0.3);
}

.lora-system-block {
  border-radius: 8px;
  border: 1px solid red;
}

.lora-detail-content {
  display: flex;
  flex-direction: row;
  padding: 0;
  border-radius: 24px;
}

.lora-detail-left {
  width: 264px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 24px;
  gap: 16px;
  align-self: stretch;
  z-index: 0;
  overflow: auto;

  img:not(.no-inherit) {
    width: auto;
    height: 160px;
    object-fit: contain;
  }
}

.lora-detail-left-inner {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0;
  gap: 4px;
  z-index: 0;
  width: 100%;
}

.lora-left-inner-top {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0;
  gap: 8px;
  width: 100%;
  z-index: 0;
  
  /* 标题元素靠左对齐 */
  > div.font-pf.text14.weight {
    align-self: flex-start;
    width: 100%;
    text-align: left;
  }

  img:not(.no-inherit) {
    width: 160px;
    height: 160px;
    object-fit: contain;
    border-radius: 5.16px;
    box-sizing: border-box;
    border: 0.65px solid #E0E0E0;
    margin: 0 auto;
  }
}

.lora-detail-pair {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  padding: 6px 0;
  border-bottom: 1px dashed #E0E0E0;
  
  > div.font-pf.text14.weight {
    font-size: 13px;
    font-weight: 500;
    color: #333;
    width: 100%;
    margin-bottom: 4px;
  }
  
  > div.font-pf.text12 {
    font-size: 12px;
    color: #666;
    text-align: left;
    width: 100%;
    word-break: break-word;
  }
}

.lora-detail-pair:last-child {
  border-bottom: none;
}

.lora-detail-pair-row {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  
  > div.font-pf.text14.weight {
    margin-bottom: 0;
    flex: 0 0 auto;
    width: auto;
  }
  
  > div.font-pf.text12 {
    text-align: right;
    flex: 1;
    width: auto;
    padding-left: 8px;
  }
}

.lora-train-param {
  margin-top: 4px;
  padding: 4px 8px;
  background-color: #f9f9f9;
  border-left: 2px solid #d9d9d9;
  border-radius: 0 2px 2px 0;
}

.lora-train-param-item {
  display: flex;
  flex-direction: row;
  margin-top: 2px;
  line-height: 1.4;
}

.lora-train-param-item:first-child {
  margin-top: 0;
}

.lora-train-param-label {
  font-weight: 500;
  color: #555;
  min-width: 70px;
}

.lora-train-status {
  padding: 2px 6px;
  border-radius: 2px;
  display: inline-block;
  margin-bottom: 2px;
}

.lora-train-status-init {
  background-color: #f0f0f0;
  color: #666;
}

.lora-train-status-queue {
  background-color: #e6f7ff;
  color: #1890ff;
}

.lora-train-status-running {
  background-color: #fff7e6;
  color: #fa8c16;
}

.lora-train-status-completed {
  background-color: #f6ffed;
  color: #52c41a;
}

.lora-detail-section {
  padding-bottom: 3px;
}

.lora-detail-right {
  width: 1400px;
  height: 686px;
  border-radius: 0px 24px 24px 0px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  overflow-y: auto;
  will-change: transform;
}

.lora-img-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  margin-top: 10px;
}

.lora-detail-img-item {
  display: flex;
  flex-direction: column;

  img {
    width: 160px;
    height: 160px;
    object-fit: contain;
    border: 0.65px solid #E0E0E0;
    cursor: pointer;
  }

  textarea {
    width: 160px;
    height: 160px;
  }
}

.label-ret-item {
  display: flex;
  flex-direction: column;

  img {
    width: 160px;
    height: 160px;
    object-fit: contain;
    border: 0.65px solid #E0E0E0;
    cursor: pointer;
  }

  textarea {
    width: 160px;
    height: 160px;
  }

  &:hover {
    border-color: #40a9ff !important;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

.loras-image-card-name {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  letter-spacing: 0px;
  font-variation-settings: "opsz" auto;
  color: #1A1B1D;
}

.loras-image-card-info {
  height: 14px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0;

  font-family: PingFang SC;
  font-size: 12px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0em;

  font-variation-settings: "opsz" auto;
  color: #727375;
}

.operate-btn {
  width: auto !important;
  height: 28px !important;
  font-size: 12px;
}

.lora-running-text {
  font-weight: bold;
  color: blue;
}

.models-image-card-new {
  position: relative;
  width: 269px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

.models-img-cover-new {
  position: relative;
  width: 100%;
  height: 269px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #F5F6F9;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    cursor: pointer;
  }
}

.lora-card-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.lora-card-info-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.lora-card-actions {
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
  margin: 0 -12px;
}

.lora-card-name-text {
  max-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  font-weight: 600;
  vertical-align: middle;
  margin-right: 4px;
  
  &:hover {
    color: #1890ff;
  }
}
