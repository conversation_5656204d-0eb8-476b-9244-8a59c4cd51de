import { <PERSON><PERSON>ontainer } from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';
import {
  Button,
  Checkbox,
  Flex,
  Form,
  Input,
  Modal,
  notification,
  Radio,
  Select,
  Upload,
  UploadFile,
  UploadProps,
  Tooltip,
  message,
} from 'antd';
import { FileType, getBase64, objectToPathObject, pathObjectToObject } from '@/utils/utils';
import {
  addElement,
  deleteElement,
  ElementConfigWithBlobs,
  getElementConfigByKey,
  getElementTypes,
  resetOrder,
  SceneType,
  updateElement,
} from '@/services/ElementController';
import './index.less';
import DraggableCardList, { DraggableData } from '@/components/DraggableCard';
import ElementEditModal from '@/components/Operate/ElementEditModal';
import OpenScopeSelector from '@/components/Operate/OpenScopeSelector';
import UploadWithTags, { UploadFilesModel } from '@/components/UploadWithTags';
import { UPLOAD_URL } from '@/constants';
import { PlusOutlined } from '@ant-design/icons';

const uploadButton = (
  <button style={{ border: 0, background: 'none' }} type="button">
    <PlusOutlined />
    <div style={{ marginTop: 8 }}>Upload</div>
  </button>
);

const ClothStylePage: React.FC = () => {
  const [datas, setDatas] = useState<Array<ElementConfigWithBlobs>>([]);
  const [showDialog, setShowDialog] = useState(false);
  const [add, setAdd] = useState(false);
  const [deleteId, setDeleteId] = useState<number>(0);
  const [form] = Form.useForm();

  //场景列表，scene type code array
  const [checkedTypes, setCheckedTypes] = useState<string[]>();
  const [categoryTypeList, setCategoryTypeList] = useState<SceneType[]>([]);
  const [selectType, setSelectType] = useState<string>('all');
  const [showEditType, setShowEditType] = useState<boolean>(false);
  const [showOpenScope, setShowOpenScope] = useState(false);
  const [logoLocationTypes, setLogoLocationTypes] = useState<SceneType[]>([]);

  //图片上传相关
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [maskFileList, setMaskFileList] = useState<UploadFile[]>([]);

  async function fetchData() {
    const res = await getElementConfigByKey('CLOTH_STYLE');
    if (res && Array.isArray(res)) {
      setDatas(res);
    }
  }

  useEffect(() => {
    fetchData();

    getElementTypes('CLOTH_STYLE').then(types => {
      if (types) {
        setCategoryTypeList(types);
      }
    });

    getElementTypes('LOGO_POSITION').then(res => {
      if (res) {
        setLogoLocationTypes(res);
      }
    });

  }, []);

  const handleAdd = () => {
    //先清空一遍表单数据
    form.resetFields();
    setAdd(true);
    setShowDialog(true);
  };

  const handleModify = (item: ElementConfigWithBlobs) => {
    //先清空一遍表单数据
    form.resetFields();
    setAdd(false);

    //展开扩展信息
    const extInfoExtend = objectToPathObject(item.extInfo, 'extInfo');

    let values = { ...item, ...extInfoExtend };

    if (item?.children) {
      values['clothStyleImgs'] = {
        files: item.children.map((child) => {
          return {
            id: child.id,
            imgUrl: child.showImage,
            clothColor: child.extInfo.clothColor,
          };
        })
      };
    }

    // @ts-ignore
    if (item.extInfo && item.extInfo['maskImage']) {
      setMaskFileList([{
        uid: '-1', // 文件唯一标识，负数表示不是用户新上传的
        name: 'image.png', // 文件名
        status: 'done', // 状态有：uploading, done, error, removed
        url: item.extInfo['maskImage'], // 图片路径
      }]);
    } else {
      setMaskFileList([]);
    }

    form.setFieldsValue(values);

    console.log('handleModify, init values:', values);

    setShowDialog(true);
  };

  const handleDelete = (item: ElementConfigWithBlobs) => {
    setDeleteId(item.id);
  };

  const commitDel = () => {
    deleteElement(deleteId).then((res) => {
      if (res) {
        setDeleteId(0);
        fetchData();
      }
    });
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
  };

  const handleChangeMaskImage: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    const file = newFileList[0];

    if (file && file.response && !file.response.success) {
      notification.error({ message: '上传图片异常，请重试' });
      setMaskFileList([]);
      return;
    }
    setMaskFileList(newFileList);
  };

  const handleCommit = (values) => {

    const extInfoMap = pathObjectToObject(values);
    extInfoMap.configKey = 'CLOTH_STYLE';
    extInfoMap.level = 2;

    const method = add ? addElement : updateElement;

    let uploadFilesModel = (values.clothStyleImgs as UploadFilesModel);

    if (uploadFilesModel) {
      let uploadFiles = uploadFilesModel.files.filter(item=> item.fileList && item.fileList.length > 0 || item.imgUrl != null);
      if (uploadFiles.length === 0){
        notification.error({ message: '请上传图片' });
        return;
      }

      uploadFiles.filter(item => item.imgUrl === '').forEach((item) => {
        if (item.fileList != null && !item.fileList[0].response) {
          notification.error({ message: '请上传图片' });
          return;
        }
        // @ts-ignore
        item.imgUrl = item.fileList[0].response.data;
        item.fileList = undefined;
      })
      extInfoMap.showImage = uploadFiles[0].imgUrl;
    }

    if (extInfoMap.extInfo.maskImage) {
      if (typeof extInfoMap.extInfo.maskImage !== 'string') {
        if (!extInfoMap.extInfo.maskImage.file.response) {
          notification.error({ message: '请上传蒙版图片' });
          return;
        }
        extInfoMap.extInfo['maskImage'] = extInfoMap.extInfo.maskImage.file.response.data;
      }
    }

    extInfoMap.type = [extInfoMap.extInfo.logoLocation, extInfoMap.extInfo.clothStyleType];

    console.log('handleCommit主元素:', extInfoMap);
    method(extInfoMap).then((res) => {
      if (res) {

        let allPromise = [];

        let parentId = res.id;

        uploadFilesModel.files.forEach((item) => {
          let element = {
            ...extInfoMap,
            level: 3,
            parentId: parentId,
            id: item.id,
            showImage: item.imgUrl,
            extInfo: {
              clothColor: item.clothColor
            }
          }

          const subMethod = (add || !item.id) ? addElement : updateElement;

          // @ts-ignore
          allPromise.push(subMethod(element));
        })

        if (uploadFilesModel.deletedFiles) {
          uploadFilesModel.deletedFiles?.forEach((item) => {

            // @ts-ignore
            allPromise.push(deleteElement(item.id));
          });
        }

        Promise.allSettled(allPromise).then((all) => {
          // 检查是否所有操作都成功
          const allSuccess = all.every(result => result.status === 'fulfilled');
          if (allSuccess) {
            notification.success({ message: '操作成功' });
            setShowDialog(false);
            window.location.reload();

          } else {
            notification.success({ message: '操作失败' });
          }
        });
      }
    });
  };

  const ImageCard = (item: ElementConfigWithBlobs) => (
    <div className="models-image-card" style={{
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
      borderRadius: '12px',
      overflow: 'hidden',
      transition: 'all 0.3s ease',
      position: 'relative',
      background: '#FFFFFF',
      padding: 0,
    }} 
    onMouseOver={(e) => {
      e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.12)';
      e.currentTarget.style.transform = 'translateY(-2px)';
    }}
    onMouseOut={(e) => {
      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.08)';
      e.currentTarget.style.transform = 'translateY(0)';
    }}
    >
      <div className="models-img-cover" style={{
        width: '100%',
        height: '200px',
        background: '#F5F6F9',
        borderRadius: '8px 8px 0 0',
        border: 'none'
      }}>
        <img src={item.showImage} alt={item.name} style={{objectFit: 'contain'}} />
      </div>

      <div style={{padding: '16px', display: 'flex', flexDirection: 'column', gap: '8px'}}>
        <div className="models-image-card-name" style={{fontSize: 14, fontWeight: '600'}}>
          <Tooltip title={item.name}>
            <span
              style={{
                maxWidth: '180px', 
                whiteSpace: 'nowrap', 
                overflow: 'hidden', 
                textOverflow: 'ellipsis',
                display: 'inline-block',
                verticalAlign: 'middle',
                marginRight: '4px',
                cursor: 'pointer'
              }}
              onClick={() => {
                navigator.clipboard.writeText(item.name);
                message.success('已复制款式名称');
              }}
            >
              {item.name.length > 18 ? item.name.slice(0, 18) + '...' : item.name}
            </span>
          </Tooltip>
          {item.id && 
            <Tooltip title={`复制ID: ${item?.id}`}>
              <span 
                style={{ color: '#727375', fontSize: 12, cursor: 'pointer' }}
                onClick={() => {
                  navigator.clipboard.writeText(item?.id?.toString());
                  message.success('已复制ID');
                }}
              >
                {`(${item?.id})`}
              </span>
            </Tooltip>
          }
        </div>
        <div className={'row-space-block'} style={{paddingTop: '8px', borderTop: '1px solid #f0f0f0'}}>
          <Button className={'operate-btn'} size={'small'} onClick={() => handleModify(item)}>修改配置</Button>
          <Button className={'operate-btn'} size={'small'} onClick={() => handleDelete(item)}>删除</Button>
        </div>
      </div>
    </div>
  );

  const changeOrder = (data: Array<DraggableData>) => {
    console.log('changeOrder', data);
    resetOrder({ items: data }).then((res) => {
      if (res) {
        fetchData();
      }
    });
  };

  const getSearchList = (typeList: SceneType[]) => {
    const res = [];
    if (Array.isArray(typeList)) {
      typeList.forEach(item => {
        // @ts-ignore
        res.push({ label: item.name, value: item.code });
      });
    }
    return [{ label: '全部', value: 'all' }, { label: '未设置', value: 'unset' }, ...res];
  };

  const handleChangeType = (e) => {
    setSelectType(e.target.value);
  };

  const filterType = (datas) => {
    return datas.filter((item: ElementConfigWithBlobs) => {
      if (selectType === 'all') {
        return true;
      }
      if (selectType === 'unset') {
        return !item.type;
      }
      return item.type && item.type.some(type => type == selectType);
    }).filter(item => {
      if (showOpenScope) {
        console.log('item.extInfo.openScope', item.extInfo.openScope);
        return item.extInfo.openScope && item.extInfo.openScope !== '' && item.extInfo.openScope !== 'ALL';
      }
      return true;
    });
  };

  return (
    <PageContainer>

      <Flex vertical gap={8} style={{padding: 16}}>
        <div className="models-filter-row margin-bottom-12">
          <Flex align={'center'}>
            <Checkbox onChange={e => setShowOpenScope(e.target.checked)}>仅展示限制开放</Checkbox>
          </Flex>
          <Radio.Group options={getSearchList(categoryTypeList)}
                       defaultValue={'all'} optionType="button" onChange={e => handleChangeType(e)} />
          <Button className="models-image-card-button"
                  onClick={() => setShowEditType(true)}>编辑标签</Button>
          <Button className="models-image-card-button"
                  onClick={handleAdd}>新增服装款式</Button>
        </div>
        <DraggableCardList data={filterType(datas)} ShowCard={ImageCard}
                           onChange={(data: DraggableData[]) => changeOrder(data)} />
      </Flex>
      <Modal title={''} open={showDialog} centered mask={true} keyboard={false}
             styles={{
               footer: { display: 'flex', flexDirection: 'row', justifyContent: 'center' },
             }} maskClosable={false}
             onCancel={() => setShowDialog(false)} onOk={form.submit}
             closable={false} width={'auto'}>

        <Form className={'scene-dialog-block'}
              labelCol={{ span: 2 }}
              wrapperCol={{ span: 22 }}
              initialValues={{ remember: true }}
              onFinish={handleCommit}
              autoComplete="off"
              form={form}
        >
          <Form.Item hidden label="id" name="id">
            <Input />
          </Form.Item>
          <Form.Item label="名称" name="name" style={{ width: '100%' }}
                     rules={[{ required: true, message: '请输入正确的名称' }]}>
            <Input placeholder={'请输入名称'} style={{ width: 600 }} />
          </Form.Item>

          <Form.Item label="印花位置"
                     name="extInfo.logoLocation"
                     style={{ width: '100%' }}
                     rules={[{ required: true, message: '请选择印花位置' }]}>

            {logoLocationTypes && logoLocationTypes.length > 0 && (
              <Select
                placeholder="选择印花位置"
                style={{ width: 600 }}
              >
                {logoLocationTypes.map(s => (
                  <Select.Option key={s.code} value={s.code}>
                    {s.name}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>

          <Form.Item label="款式类型"
                     name="extInfo.clothStyleType"
                     style={{ width: '100%' }}
                     rules={[{ required: true, message: '请选择款式类型' }]}>

            {categoryTypeList && categoryTypeList.length > 0 && (
              <Select
                placeholder="选择款式"
                style={{ width: 600 }}
              >
                {categoryTypeList.map(s => (
                  <Select.Option key={s.code} value={s.code}>
                    {s.name}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>

          <Form.Item
            label="图片"
            name='clothStyleImgs'
            style={{width: '100%'}}
            rules={[{ required: true, message: '请上传文件' }]}
          >
            <UploadWithTags />
          </Form.Item>

          <Form.Item
            label="蒙版图"
            name='extInfo.maskImage'
            style={{width: '100%'}}
            rules={[{ required: true, message: '请上传蒙版图' }]}
          >
            <Upload
              action={UPLOAD_URL}
              listType="picture-card"
              fileList={maskFileList}
              onPreview={handlePreview}
              onChange={handleChangeMaskImage}
            >
              {maskFileList.length >= 1 ? null : uploadButton}
            </Upload>
          </Form.Item>

          <Form.Item
            label="状态"
            name="status"
            rules={[{ required: true, message: '请选择正确的状态' }]}
            style={{ width: '100%' }}
          >
            <Radio.Group options={[{ label: '仅供测试', value: 'TEST' },
              { label: '发布线上', value: 'PROD' },
            ]} optionType="button" buttonStyle="solid" />
          </Form.Item>
          <OpenScopeSelector />
        </Form>
      </Modal>
      <Modal title="是否确认删除？" open={deleteId > 0} centered mask={true} closable={false} width={'300px'}
             styles={{
               header: { display: 'flex', flexDirection: 'row', justifyContent: 'center' },
               footer: { display: 'flex', flexDirection: 'row', justifyContent: 'center' },
             }} maskClosable={false}
             onCancel={() => setDeleteId(0)} onOk={() => commitDel()}
      >
        <div className={'row-center-block'}
             style={{ color: 'red' }}>删除后将不能恢复，请谨慎操作！
        </div>
      </Modal>

      {showEditType &&
        <ElementEditModal elementKey={'CLOTH_STYLE'} title={'编辑场景类型'} visible={showEditType} onCancel={() => setShowEditType(false)}
                          onSave={() => {
                            setShowEditType(false);
                            fetchData();
                          }}
                          typeList={categoryTypeList}
        />
      }
    </PageContainer>
  );
};

export default ClothStylePage;