import React, { useEffect, useState } from 'react';
import {
    Button,
    Card,
    Form,
    Input,
    message,
    Modal,
    Select,
    Table,
    Typography,
    Space,
    Tag,
    Row,
    Col,
    Flex,
    Tooltip,
    Radio, Checkbox,
} from 'antd';
import { PageContainer } from '@ant-design/pro-components';
import {
  PlusOutlined,
  EditOutlined,
  RocketOutlined,
  EyeOutlined,
  DatabaseOutlined,
  DownloadOutlined,
  CheckCircleOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import {
    ComfyuiWorkflowTemplateVO,
    createComfyuiWorkflowTemplate,
    updateComfyuiWorkflowTemplateById,
    getAllTemplateKeys,
    queryComfyuiWorkflowTemplatesByPage,
    queryComfyuiWorkflowTemplateList,
    type ComfyuiWorkflowTemplateQuery, activeTemplate, deleteTemplate,
} from '@/services/ComfyuiWorkflowTemplateController';
import { ROLE_TYPE, RoleTypesItems } from '@/services/UserController';
import TextArea from 'antd/lib/input/TextArea';
import { downloadJson, formatText, getUserInfo, isProdEnv, isValidJsonObject } from '@/utils/utils';
import './workflow.less';
import { canPublishComfyuiWorkflow } from '@/services/SystemController';

const { Title, Text } = Typography;

interface PublishModalProps {
    visible: boolean;
    onCancel: () => void;
    onOk: (values: any) => void;
    templateKeys: { label: string, value: string }[];
    templates: ComfyuiWorkflowTemplateVO[];
}

// 发布工作流模板弹窗
const PublishModal: React.FC<PublishModalProps> = ({ visible, onCancel, onOk, templateKeys, templates }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [generatedVersion, setGeneratedVersion] = useState('');
    const [isNewTemplate, setIsNewTemplate] = useState(false);
    const [isTestMode, setIsTestMode] = useState(false);

    // 生成版本号的函数
    const generateVersion = async (templateKey: string) => {
        if (!templateKey) return '';

        const today = new Date().toISOString().slice(0, 10).replace(/-/g, '');
        // 使用新的查询函数替换 findAllComfyuiWorkflowTemplates
        const existingTemplates = await queryComfyuiWorkflowTemplateList({ templateKey, needDeleted: true });
        const sameKeyTemplates = existingTemplates || [];
        const todayVersions = sameKeyTemplates
            .filter(t => t.version.startsWith(today))
            .map(t => parseInt(t.version.split('.')[1] || '0'))
            .sort((a, b) => b - a);

        const nextIndex = todayVersions.length > 0 ? todayVersions[0] + 1 : 0;
        return `${today}.${nextIndex}`;
    };

    // 当模板Key改变时自动生成版本号
    const handleTemplateKeyChange = async (templateKey: string) => {
        const version = await generateVersion(templateKey);
        setGeneratedVersion(version);
        form.setFieldsValue({ version });
    };

    // 当切换模式时重置相关字段
    const handleModeChange = (checked: boolean) => {
        setIsNewTemplate(checked);
        form.setFieldsValue({
            templateKey: undefined,
            templateDesc: undefined,
            version: ''
        });
        setGeneratedVersion('');
    };

    // 开放范围验证函数
    const validateOpenScope = (_: any, value: any) => {
        const userIds = form.getFieldValue('userIds');
        const roleTypes = form.getFieldValue('roleTypes');
        const logic = form.getFieldValue('logic');

        // 如果同时设置了用户ID和角色类型，必须选择逻辑词
        if (userIds && userIds.length > 0 && roleTypes && roleTypes.length > 0 && !logic) {
            return Promise.reject(new Error('同时设置用户ID和角色时，必须选择逻辑关系'));
        }
        return Promise.resolve();
    };



    // 当弹窗打开时重置表单
    useEffect(() => {
        if (visible) {
            form.resetFields();
            setGeneratedVersion('');
            setIsNewTemplate(false); // 默认为选择现有模板
            setIsTestMode(false);
        }
    }, [visible, form]);

    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            setLoading(true);

            let finalValues = { ...values };

            if (isNewTemplate) {
                // 新模板模式：使用用户输入的描述
                finalValues.templateDesc = values.templateDesc;
            } else {
                // 现有模板模式：获取选中模板的描述
                const selectedTemplate = templates.find(t => t.templateKey === values.templateKey);
                finalValues.templateDesc = selectedTemplate?.templateDesc || '';
            }

            // 处理开放范围数据
            if (values.isTest && (values.userIds || values.roleTypes)) {
                const openScope = {
                    userIds: values.userIds ? values.userIds.map(id => parseInt(id, 10)).filter(id => !isNaN(id)) : [],
                    roleTypes: values.roleTypes || [],
                    logic: values.logic || 'AND'
                };
                finalValues.openScope = openScope;
            }

            // 清除表单中的临时字段
            delete finalValues.userIds;
            delete finalValues.roleTypes;
            delete finalValues.logic;

            await onOk(finalValues);
            form.resetFields();
            setGeneratedVersion('');
            setIsNewTemplate(false);
            setIsTestMode(false);
        } catch (error) {
            console.error('发布失败:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <Modal
            title={
                <Flex align="center" gap={8}>
                    <RocketOutlined style={{ color: '#1890ff' }} />
                    <span>发布工作流模板</span>
                </Flex>
            }
            open={visible}
            onCancel={() => {
                setIsTestMode(false);
                onCancel();
            }}
            onOk={handleOk}
            confirmLoading={loading}
            width={700}
            centered
            styles={{
                body: { padding: '24px' },
            }}
        >
            <Form form={form} layout="vertical" size="large">
                <Row gutter={16}>
                    <Col span={24}>
                        <Form.Item label={<Text strong>发布模式</Text>}>
                            <Radio.Group
                                value={isNewTemplate ? 'new' : 'existing'}
                                onChange={(e) => handleModeChange(e.target.value === 'new')}
                                size="large"
                                style={{ width: '100%' }}
                            >
                                <Radio.Button value="existing" style={{ width: '50%', textAlign: 'center' }}>
                                    <Space>
                                        <DatabaseOutlined />
                                        选择现有模板
                                    </Space>
                                </Radio.Button>
                                <Radio.Button value="new" style={{ width: '50%', textAlign: 'center' }}>
                                    <Space>
                                        <PlusOutlined />
                                        创建新模板
                                    </Space>
                                </Radio.Button>
                            </Radio.Group>
                        </Form.Item>
                    </Col>
                </Row>

                <Row gutter={16}>
                    <Col span={isNewTemplate ? 12 : 24}>
                        <Form.Item
                            name="templateKey"
                            label={<Text strong>模板Key</Text>}
                            rules={[
                                { required: true, message: isNewTemplate ? '请输入模板Key' : '请选择模板Key' },
                                // 新增校验规则：以字母开头，只支持字母、数字和下划线
                                ...(isNewTemplate ? [
                                    {
                                        pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/,
                                        message: '模板Key必须以字母开头，只支持字母、数字和下划线'
                                    }
                                ] : [])
                            ]}
                        >
                            {isNewTemplate ? (
                                <Input
                                    placeholder="请输入新的模板Key（以字母开头，支持字母、数字、下划线）"
                                    size="large"
                                    onChange={(e) => {
                                        if (e.target.value) {
                                            handleTemplateKeyChange(e.target.value);
                                        }
                                    }}
                                />
                            ) : (
                                <Select
                                    placeholder="请选择模板Key"
                                    allowClear
                                    showSearch
                                    style={{ width: 600 }}
                                    optionFilterProp="label"
                                    options={templateKeys}
                                    onChange={handleTemplateKeyChange}
                                />
                            )}
                        </Form.Item>
                    </Col>

                    {/* 新模板模式下显示描述字段 */}
                    {isNewTemplate && (
                        <Col span={12}>
                            <Form.Item
                                name="templateDesc"
                                label={<Text strong>模板描述</Text>}
                                rules={[{ required: true, message: '请输入模板描述' }]}
                            >
                                <Input
                                    placeholder="请输入模板描述"
                                    size="large"
                                />
                            </Form.Item>
                        </Col>
                    )}
                </Row>

                {/* 版本号字段 */}
                <Row gutter={16}>
                    <Col span={24}>
                        <Form.Item
                            name="version"
                            label={<Text strong>版本号</Text>}
                            rules={[{ required: true, message: '请输入版本号' }]}
                        >
                            <Input
                                placeholder="自动生成版本号"
                                size="large"
                                suffix={
                                    <Tooltip title="版本号格式：YYYYMMDD.序号">
                                        <Text type="secondary" style={{ fontSize: '12px' }}>自动生成</Text>
                                    </Tooltip>
                                }
                            />
                        </Form.Item>
                    </Col>
                </Row>

                {/* 模板数据字段保持不变 */}
                <Row gutter={16}>
                    <Col span={24}>
                        <Form.Item
                            name="templateData"
                            label={<Text strong>模板数据</Text>}
                            rules={[
                                { required: true, message: '请输入模板数据' },
                                {
                                    validator: (_, value) => {
                                        if (value && !isValidJsonObject(value)) {
                                            return Promise.reject(new Error('请输入有效的JSON格式'));
                                        }
                                        return Promise.resolve();
                                    },
                                },
                            ]}
                        >
                            <TextArea
                                rows={12}
                                placeholder="请输入JSON格式的模板数据"
                                style={{ fontFamily: 'Monaco, Consolas, monospace' }}
                            />
                        </Form.Item>
                    </Col>
                </Row>

                {!isNewTemplate &&
                  <Row gutter={16} style={{marginBottom: -24}}>
                      <Col span={24}>
                          <Form.Item
                            name="isTest"
                            label={<Text strong>线上测试</Text>}
                            valuePropName="checked"
                            rules={[{ required: false, message: '请输入是否测试' }]}
                          >
                              <Checkbox onChange={(e) => {
                                  setIsTestMode(e.target.checked);
                                  // 取消测试模式时清空开放范围相关字段
                                  if (!e.target.checked) {
                                      form.setFieldsValue({
                                          userIds: undefined,
                                          roleTypes: undefined,
                                          logic: 'AND'
                                      });
                                  }
                              }}>
                                  勾选后将对客户不生效，用于线上规模测试，测试通过后可在该版本上进行生效设置
                              </Checkbox>
                          </Form.Item>
                      </Col>
                  </Row>
                }

                {/* 开放范围编辑区域 */}
                {!isNewTemplate && isTestMode && (
                    <Row gutter={16} style={{ marginTop: 8, padding: '12px', backgroundColor: '#f8f9fa', borderRadius: '6px', border: '1px solid #e9ecef' }}>
                        <Col span={24} style={{ marginBottom: '8px' }}>
                            <Text strong style={{ color: '#1890ff', fontSize: '14px' }}>
                                测试开放范围（可选）
                            </Text>
                        </Col>
                        
                        <Col span={10}>
                            <Form.Item
                                name="userIds"
                                label={<Text strong style={{ fontSize: '13px' }}>用户ID</Text>}
                                rules={[{ validator: validateOpenScope }]}
                                style={{ marginBottom: '12px' }}
                                normalize={(value) => {
                                    if (typeof value === 'string') {
                                        return value.split(/[,，\s]+/).filter(id => id.trim()).map(id => id.trim());
                                    }
                                    return value;
                                }}
                            >
                                <Select
                                    mode="tags"
                                    placeholder="输入用户ID"
                                    size="small"
                                    style={{ width: '100%' }}
                                    tokenSeparators={[',', '，', ' ']}
                                    options={[]}
                                />
                            </Form.Item>
                        </Col>

                        <Col span={10}>
                            <Form.Item
                                name="roleTypes"
                                label={<Text strong style={{ fontSize: '13px' }}>角色类型</Text>}
                                rules={[{ validator: validateOpenScope }]}
                                style={{ marginBottom: '12px' }}
                            >
                                <Select
                                    mode="multiple"
                                    placeholder="选择角色"
                                    size="small"
                                    style={{ width: '100%' }}
                                    options={RoleTypesItems}
                                />
                            </Form.Item>
                        </Col>

                        <Col span={4}>
                            <Form.Item
                                name="logic"
                                label={<Text strong style={{ fontSize: '13px' }}>逻辑关系</Text>}
                                initialValue="AND"
                                rules={[{ validator: validateOpenScope }]}
                                style={{ marginBottom: '12px' }}
                            >
                                <Radio.Group size="small">
                                    <Radio value="AND">AND</Radio>
                                    <Radio value="OR">OR</Radio>
                                </Radio.Group>
                            </Form.Item>
                        </Col>
                    </Row>
                )}
            </Form>
        </Modal>
    );
};

// 编辑模板弹窗
interface EditModalProps {
    visible: boolean;
    onCancel: () => void;
    onOk: (values: ComfyuiWorkflowTemplateVO) => void;
    initialValues?: ComfyuiWorkflowTemplateVO;
}

const EditModal: React.FC<EditModalProps> = ({ visible, onCancel, onOk, initialValues }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (visible && initialValues) {
            form.setFieldsValue(initialValues);
        }
    }, [visible, initialValues, form]);

    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            setLoading(true);
            await onOk({ ...initialValues, ...values });
            form.resetFields();
        } catch (error) {
            console.error('更新失败:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <Modal
            title={
                <Flex align="center" gap={8}>
                    <EditOutlined style={{ color: '#52c41a' }} />
                    <span>编辑工作流模板</span>
                </Flex>
            }
            open={visible}
            onCancel={onCancel}
            onOk={handleOk}
            confirmLoading={loading}
            width={900}
            styles={{
                body: { padding: '24px' },
            }}
        >
            <Form form={form} layout="vertical" size="large">
                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item
                            name="templateKey"
                            label={<Text strong>模板Key</Text>}
                            rules={[{ required: true, message: '请输入模板Key' }]}
                        >
                            <Input
                                placeholder="请输入模板Key"
                                size="large"
                            />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            name="version"
                            label={<Text strong>版本</Text>}
                            rules={[{ required: true, message: '请输入版本' }]}
                        >
                            <Input
                                placeholder="请输入版本"
                                disabled
                                size="large"
                                style={{ backgroundColor: '#f5f5f5' }}
                            />
                        </Form.Item>
                    </Col>
                </Row>



                <Row gutter={16}>
                    <Col span={24}>
                        <Form.Item
                            name="templateData"
                            label={<Text strong>模板数据</Text>}
                            rules={[
                                { required: true, message: '请输入模板数据' },
                                {
                                    validator: (_, value) => {
                                        if (value && !isValidJsonObject(value)) {
                                            return Promise.reject(new Error('请输入有效的JSON格式'));
                                        }
                                        return Promise.resolve();
                                    },
                                },
                            ]}
                        >
                            <TextArea
                                rows={18}
                                placeholder="请输入JSON格式的模板数据"
                                style={{ fontFamily: 'Monaco, Consolas, monospace' }}
                            />
                        </Form.Item>
                    </Col>
                </Row>
            </Form>
        </Modal>
    );
};

const WorkflowManagement: React.FC = () => {
    const [templates, setTemplates] = useState<ComfyuiWorkflowTemplateVO[]>([]);
    const [templateKeys, setTemplateKeys] = useState<{ label: string, value: string }[]>([]);
    const [loading, setLoading] = useState(false);
    const [publishModalVisible, setPublishModalVisible] = useState(false);
    const [editModalVisible, setEditModalVisible] = useState(false);
    const [editingTemplate, setEditingTemplate] = useState<ComfyuiWorkflowTemplateVO>();
    const [viewModalVisible, setViewModalVisible] = useState(false);
    const [viewingTemplate, setViewingTemplate] = useState<ComfyuiWorkflowTemplateVO>();
    const [selectedTemplateKey, setSelectedTemplateKey] = useState<string>(''); // 选中的模板key
    const [showTestVersion, setShowTestVersion] = useState<boolean>(false);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });
    const user = getUserInfo();
    const [userCanPublishComfyuiWorkflow, setUserCanPublishComfyuiWorkflow] = useState(false);

    // 复制到剪贴板功能
    const copyToClipboard = async (text: string) => {
        try {
            await navigator.clipboard.writeText(text);
            message.success('已复制到剪贴板');
        } catch (error) {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            message.success('已复制到剪贴板');
        }
    };

    // 模板key选择过滤功能 - 修改为触发后端请求
    const handleTemplateKeyChange = (value: string) => {
        setSelectedTemplateKey(value);
    };

    useEffect(() => {
        // Separately get all template keys (for dropdown selection and total count display)
        getAllTemplateKeys().then(res => {
            if (res && res.length > 0) {
                setTemplateKeys(res.map(item => {
                    return {
                        label: item.templateDesc + ' (' + item.templateKey + ')',
                        value: item.templateKey,
                    };
                }));
            }
        });

        canPublishComfyuiWorkflow().then(res => {
            if (res != null){
                setUserCanPublishComfyuiWorkflow(res);
            }
        });
    }, []);

    // 加载模板列表 - 修改为完全依赖后端分页和过滤
    const loadTemplates = async (page?: number, pageSize?: number, templateKey?: string, showTestVersion?: boolean) => {
        try {
            setLoading(true);
            // 构建查询参数
            const query: ComfyuiWorkflowTemplateQuery = {
                pageSize: pageSize || pagination.pageSize,
                pageNum: page || pagination.current,
                orderBy: 'id desc', // 默认按ID倒序
                ...(templateKey !== undefined ? { templateKey } : selectedTemplateKey && { templateKey: selectedTemplateKey }),
                onlyShowTestVersion: showTestVersion,
            };

            const result = await queryComfyuiWorkflowTemplatesByPage(query);

            if (result) {
                setTemplates(result?.list || []);
                setPagination(prev => ({
                    ...prev,
                    current: page || prev.current,
                    pageSize: pageSize || prev.pageSize,
                    total: result?.totalCount || 0
                }));
            }

        } catch (error) {
            message.error('加载模板列表失败');
        } finally {
            setLoading(false);
        }
    };

    // 修改 useEffect，移除 pagination 依赖
    useEffect(() => {
        loadTemplates(1, pagination.pageSize, selectedTemplateKey, showTestVersion);
    }, [selectedTemplateKey, showTestVersion]);

    // 发布新版本
    const handlePublish = async (values: any) => {
        try {
            await createComfyuiWorkflowTemplate(values);
            message.success('发布成功');
            setPublishModalVisible(false);
            loadTemplates();
        } catch (error) {
            message.error('发布失败');
        }
    };

    // 更新模板
    const handleUpdate = async (values: ComfyuiWorkflowTemplateVO) => {
        try {
            await updateComfyuiWorkflowTemplateById(values);
            message.success('更新成功');
            setEditModalVisible(false);
            setEditingTemplate(undefined);
            loadTemplates();
        } catch (error) {
            message.error('更新失败');
        }
    };

    const columns = [
        {
            title: 'ID',
            dataIndex: 'id',
            key: 'id',
            width: 50,
            align: 'center' as const,
            render: (text: number) => (
                <Text strong style={{ color: '#666' }}>{text}</Text>
            ),
        },
        {
            title: '模板Key',
            dataIndex: 'templateKey',
            key: 'templateKey',
            width: '30%',
            render: (text: string, record) => (
              <Flex gap={4}>
                <Tag color="blue" style={{ margin: 0, padding: '4px 8px' }}>
                    {text}
                </Tag>
                { record.isActive &&
                    <Tag color="green" style={{ margin: 0, padding: '4px 8px' }}>
                        激活
                    </Tag>
                }

                { record.isTest &&
                    <Tag color="magenta" style={{ margin: 0, padding: '4px 8px' }}>
                        测试
                    </Tag>
                }
                </Flex>),
        },
        {
            title: '模板描述',
            dataIndex: 'templateDesc',
            key: 'templateDesc',
            width: 200,
            ellipsis: true,
            render: (text: string) => (
              <Tooltip title={text}>
                <Text style={{ fontSize: '14px' }}>{formatText(text, 16)}</Text>
              </Tooltip>
            ),
        },
        {
            title: '模板配置',
            dataIndex: 'templateData',
            key: 'templateData',
            ellipsis: true,
            width: 160,
            render: (text: string) => (
                <Tooltip title="双击复制到剪贴板">
                    <div
                        className="template-config-cell"
                        onDoubleClick={() => copyToClipboard(text)}
                    >
                        <Text className="config-text">
                            {text}
                        </Text>
                    </div>
                </Tooltip>
            ),
        },
        {
            title: '版本',
            dataIndex: 'version',
            key: 'version',
            width: 100,
            align: 'center' as const,
            render: (text: string) => (
                <Tag color="green" style={{ margin: 0, padding: '4px 8px' }}>
                    {text}
                </Tag>
            ),
        },
        {
            title: '时间',
            dataIndex: 'createTime',
            key: 'createTime',
            width: 140,
            render: (text: string) => (
                <Text style={{ fontSize: '13px', color: '#666' }}>{text}</Text>
            ),
        },
        {
            title: '操作',
            key: 'action',
            width: 200,
            align: 'center' as const,
            render: (_, record: ComfyuiWorkflowTemplateVO) => (
              <>
                <Button
                    type="link"
                    size="small"
                    icon={<EyeOutlined />}
                    onClick={() => handleView(record)}
                    style={{ padding: '2px 8px', height: 'auto' }}
                >
                    查看
                </Button>
              {record.isTest &&
                <>
                    <Button
                      type="link"
                      size="small"
                      icon={<CheckCircleOutlined />}
                      onClick={() => activeVersion(record)}
                      style={{ padding: '2px 8px', height: 'auto' }}
                    >
                        激活
                    </Button>
                    <Button
                      type="link"
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={() => deleteVersion(record)}
                      style={{ padding: '2px 8px', height: 'auto' }}
                    >
                        废弃
                    </Button>
                </>
              }
                <Button
                  type="link"
                  size="small"
                  icon={<DownloadOutlined />}
                  onClick={() => downloadWorkFlow(record)}
                  style={{ padding: '2px 8px', height: 'auto' }}
                >
                    工作流
                </Button>
              </>
            ),
        },
    ];

    // 查看模板
    const handleView = (record: ComfyuiWorkflowTemplateVO) => {
        setViewingTemplate(record);
        setViewModalVisible(true);
    };

  const downloadWorkFlow = (record: ComfyuiWorkflowTemplateVO) => {
    try {
      // 解析JSON配置值
      const config = JSON.parse(record.templateData);

      // 获取workflow数据
      const workflow = config?.extra_data?.extra_pnginfo?.workflow;

      if (workflow) {
        // 格式化并下载
        const workflowJSON = JSON.stringify(workflow, null, 2);
        downloadJson(workflowJSON, `${record.templateKey}.${record.version}.json`);
        message.success('工作流下载成功');
      } else {
        message.error('未找到workflow配置');
      }
    } catch (error) {
      console.error('下载失败:', error);
      message.error('工作流解析失败，请检查配置格式');
    }
  };
  const activeVersion = async (record: ComfyuiWorkflowTemplateVO) => {
      if(!record) return;
      activeTemplate(record.id).then(res => {
         if (!res) return;
         message.success('模板激活成功');
          loadTemplates(1, pagination.pageSize, selectedTemplateKey, showTestVersion);
      });
  };

  const deleteVersion = async (record: ComfyuiWorkflowTemplateVO) => {
      if(!record) return;
      deleteTemplate(record.id).then(res => {
         if (!res) return;
         message.success('模板废弃成功');
         loadTemplates(1, pagination.pageSize, selectedTemplateKey, showTestVersion);
      });
  };

  // 渲染开放范围信息
  const renderOpenScope = (openScope: any) => {
      if (!openScope) return <Text type="secondary">无限制</Text>;

      const { userIds = [], roleTypes = [], logic = 'AND' } = openScope;
      
      if (userIds.length === 0 && roleTypes.length === 0) {
          return <Text type="secondary">无限制</Text>;
      }

      const parts: string[] = [];
      
      if (userIds.length > 0) {
          parts.push(`用户ID: ${userIds.join(', ')}`);
      }

      if (roleTypes.length > 0) {
          const roleNames = roleTypes.map(role => {
              const roleItem = RoleTypesItems.find(item => item.value === role);
              return roleItem ? roleItem.label : role;
          });
          parts.push(`角色: ${roleNames.join(', ')}`);
      }

      const connector = parts.length > 1 ? ` ${logic} ` : '';
      
      return (
          <Text style={{ fontSize: '13px' }}>
              {parts.join(connector)}
          </Text>
      );
  };

    return (
        <div className="workflow-management">
        <PageContainer
            title={
                <Flex align="center" gap={12}>
                    <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
                        工作流模板管理
                    </Title>
                </Flex>
            }
            extra={
                <Space size="middle" direction="vertical" style={{ alignItems: 'flex-end' }}>
                    <Space size="small">
                        <Button
                            type="primary"
                            icon={<RocketOutlined />}
                            disabled={!userCanPublishComfyuiWorkflow}
                            onClick={() => setPublishModalVisible(true)}
                            size="large"
                            style={{
                                background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
                                border: 'none',
                                boxShadow: '0 2px 8px rgba(24, 144, 255, 0.3)',
                            }}
                        >
                            发新版工作流
                        </Button>
                    </Space>
                </Space>
            }
            style={{ padding: '0 24px', display: 'flex', flexDirection: 'column' }}
        >
            <div style={{ marginBottom: '16px' }}>
                <Card
                    bordered={false}
                    style={{
                        borderRadius: '8px',
                        boxShadow: '0 1px 4px rgba(0, 0, 0, 0.06)',
                    }}
                    bodyStyle={{ padding: '16px' }}
                >
                    <Space align="center" size="middle">
                        <Text strong style={{ color: '#1890ff' }}>筛选模板：</Text>
                        <Select
                            placeholder="请选择模板Key进行筛选"
                            allowClear
                            showSearch
                            optionFilterProp="label"
                            style={{ minWidth: '600px' }}
                            value={selectedTemplateKey || undefined}
                            onChange={handleTemplateKeyChange}
                            options={templateKeys}
                        />
                        {selectedTemplateKey && (
                            <Button
                                type="link"
                                onClick={() => handleTemplateKeyChange('')}
                                style={{ padding: 0 }}
                            >
                                清除筛选
                            </Button>
                        )}
                    </Space>
                    <Space align="center" size="middle" style={{marginLeft: 4}}>
                        <Checkbox onChange={e=> setShowTestVersion(e.target.checked)}>仅看测试版本</Checkbox>
                    </Space>
                </Card>
            </div>

            <div style={{ marginBottom: '24px' }}>
                <Card
                    bordered={false}
                    style={{
                        borderRadius: '12px',
                        boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)',
                    }}
                    bodyStyle={{ padding: '24px' }}
                >
                    <Table
                        columns={columns}
                        dataSource={templates} // 直接使用 templates，移除 filteredTemplates
                        rowKey="id"
                        loading={loading}
                        size="middle"
                        scroll={{ x: 1000 }}
                        rowClassName={(record, index) =>
                            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
                        }
                        pagination={{
                            ...pagination,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total, range) => (
                                <Text style={{ color: '#666' }}>
                                    第 {range[0]}-{range[1]} 条/总共 {total} 条
                                </Text>
                            ),
                            onChange: (page, pageSize) => {
                                setPagination(prev => ({
                                    ...prev,
                                    current: page,
                                    pageSize: pageSize || 10,
                                }));
                                // 分页变化时重新加载数据 - 修复：传入正确的页码和页面大小
                                loadTemplates(page, pageSize);
                            },
                            style: { marginTop: '24px', textAlign: 'center' },
                        }}
                    />
                </Card>
            </div>

            <PublishModal
                visible={publishModalVisible}
                onCancel={() => setPublishModalVisible(false)}
                onOk={handlePublish}
                templateKeys={templateKeys}
                templates={templates}
            />

            <EditModal
                visible={editModalVisible}
                onCancel={() => {
                    setEditModalVisible(false);
                    setEditingTemplate(undefined);
                }}
                onOk={handleUpdate}
                initialValues={editingTemplate}
            />

            <Modal
                title={
                    <Flex align="center" gap={8}>
                        <EyeOutlined style={{ color: '#1890ff' }} />
                        <span>查看工作流模板</span>
                    </Flex>
                }
                open={viewModalVisible}
                onCancel={() => {
                    setViewModalVisible(false);
                    setViewingTemplate(undefined);
                }}
                footer={[
                    <Button key="close" onClick={() => {
                        setViewModalVisible(false);
                        setViewingTemplate(undefined);
                    }}>
                        关闭
                    </Button>
                ]}
                width={800}
                styles={{
                    body: { padding: '24px' },
                }}
            >
                {viewingTemplate && (
                    <div>
                        <Row gutter={[16, 16]}>
                            <Col span={12}>
                                <div>
                                    <Text strong style={{ color: '#666' }}>模板Key：</Text>
                                    <Tag color="blue" style={{ marginLeft: 8 }}>
                                        {viewingTemplate.templateKey}
                                    </Tag>
                                </div>
                            </Col>
                            <Col span={12}>
                                <div>
                                    <Text strong style={{ color: '#666' }}>版本：</Text>
                                    <Tag color="green" style={{ marginLeft: 8 }}>
                                        {viewingTemplate.version}
                                    </Tag>
                                </div>
                            </Col>
                            <Col span={24}>
                                <div style={{ marginBottom: 16 }}>
                                    <Text strong style={{ color: '#666' }}>模板描述：</Text>
                                    <div style={{ marginTop: 8, padding: '8px 12px', backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                                        <Text>{viewingTemplate.templateDesc}</Text>
                                    </div>
                                </div>
                            </Col>
                            
                            {/* 如果是测试状态，显示开放范围 */}
                            {viewingTemplate.isTest && (
                                <Col span={24}>
                                    <div style={{ marginBottom: 16 }}>
                                        <Text strong style={{ color: '#666' }}>开放范围：</Text>
                                        <span style={{ marginLeft: 8 }}>
                                            {renderOpenScope(viewingTemplate.openScope)}
                                        </span>
                                    </div>
                                </Col>
                            )}
                            <Col span={24}>
                                <div>
                                    <Text strong style={{ color: '#666' }}>模板配置：</Text>
                                    <div style={{ marginTop: 8 }}>
                                        <TextArea
                                            value={viewingTemplate.templateData}
                                            readOnly
                                            rows={15}
                                            style={{
                                                fontFamily: 'Monaco, Consolas, monospace',
                                                backgroundColor: '#f8f9fa',
                                                border: '1px solid #e9ecef'
                                            }}
                                        />
                                    </div>
                                </div>
                            </Col>
                            <Col span={12}>
                                <div>
                                    <Text strong style={{ color: '#666' }}>创建时间：</Text>
                                    <Text style={{ marginLeft: 8, color: '#666' }}>
                                        {viewingTemplate.createTime}
                                    </Text>
                                </div>
                            </Col>
                            <Col span={12}>
                                <div>
                                    <Text strong style={{ color: '#666' }}>更新时间：</Text>
                                    <Text style={{ marginLeft: 8, color: '#666' }}>
                                        {viewingTemplate.modifyTime}
                                    </Text>
                                </div>
                            </Col>
                        </Row>
                    </div>
                )}
            </Modal>
        </PageContainer>
        </div>
    );
};

export default WorkflowManagement;