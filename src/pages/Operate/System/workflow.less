.workflow-management {
  .table-row-light {
    background-color: #fafafa;
  }

  .table-row-dark {
    background-color: #ffffff;
  }

  .table-row-light:hover,
  .table-row-dark:hover {
    background-color: #e6f7ff !important;
  }

  .ant-table-thead > tr > th {
    background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
    border-bottom: 2px solid #1890ff;
    font-weight: 600;
    color: #1890ff;
  }

  .ant-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  // 模板配置复制功能样式
  .template-config-cell {
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: #f5f5f5;
    border: 1px dashed #d9d9d9;
    transition: all 0.3s;
    position: relative;

    &:hover {
      background-color: #e6f7ff;
      border-color: #1890ff;
    }

    .copy-icon {
      position: absolute;
      top: 2px;
      right: 4px;
      font-size: 10px;
      color: #999;
      opacity: 0.6;
    }

    .config-text {
      font-size: 13px;
      font-family: Monaco, Consolas, monospace;
      color: #666;
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 200px;
    }
  }
} 