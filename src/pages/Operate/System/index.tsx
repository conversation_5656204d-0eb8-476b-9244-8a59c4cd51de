import {
  Button,
  Card, Divider,
  Flex,
  Form,
  Input,
  message,
  Modal,
  notification,
  Popconfirm,
  Table,
  theme,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { addSys, delSys, queryAllSys, resizeShowImage, SystemVO, updateSys } from '@/services/SystemController';
import { PageContainer } from '@ant-design/pro-components';
import TextArea from 'antd/lib/input/TextArea';
import { BatchProcessBtn, ModifyColorConfig } from '@/components/Operate/LoraComponent';
import ReactJson from 'react-json-view';
import {
  batchAutoCreateImages,
  batchAutoDelivery,
  batchCreateTestImages,
  batchInitTrainedModel,
  batchRelabelLora,
  batchRetrainAndAutoComplete,
  batchRetrainLora,
  batchTrainFolderSync,
  batchUploadOss,
  fetchWorkflowByComfyuiTaskId,
} from '@/services/MaterialModelController';
import { batchCorrectStyle, batchCorrectType, batchInitTrainType, batchSetScenePose } from '@/services/ElementController';
import { RefreshButton } from '@/components/Common/CommonComponent';
import { isValidJsonObject, downloadJson } from '@/utils/utils';
import { syncFile } from '@/services/FileController';
import { fetchWorkflow } from '@/services/CreativeController';

// 常量定义
const EXCLUDED_CONFIGS = [
  'SCENE_TYPE_CFG',
  'AUTO_TRAIN_MERCHANT',
  'AUTO_DELIVERY_MERCHANT',
  'FLUX_CREATIVE_PURE_BG_FLOW',
  'FLUX_CREATIVE_BACK_FLOW',
  'FLUX_CREATIVE_PURE_BG_BACK_FLOW',
  'MERCHANT_PREFERENCE',
  'COMFYUI_PORT_CFG',
  'FACE_TYPE_CFG',
  'EXCLUDE_SYSTEM_COLLOCATION',
  'DEVICE_INFO_CONFIG',
];

// 状态映射配置
const STATUS_CONFIG = {
  ACTIVE: {
    text: '正常',
    getStyle: (token: any) => ({
      color: token.colorSuccess,
      bgColor: token.colorSuccessBg,
    }),
  },
  INVALID: {
    text: '失效',
    getStyle: (token: any) => ({
      color: token.colorError,
      bgColor: token.colorErrorBg,
    }),
  },
  PENDING_CHANGE: {
    text: '待变更',
    getStyle: (token: any) => ({
      color: token.colorWarning,
      bgColor: token.colorWarningBg,
    }),
  },
};

// JSON 预览组件
interface JsonPreviewProps {
  value: any;
  onChange?: (value: any) => void;
  readOnly?: boolean;
  style?: React.CSSProperties;
}

// JSON 预览组件
const JsonPreview: React.FC<JsonPreviewProps> = ({ value, onChange, readOnly = false, style }) => {
  // 如果值为空，则返回提示信息
  if (!value) return <Typography.Text type="secondary">请输入有效的 JSON 格式数据</Typography.Text>;

  try {
    // 确保值是有效的 JSON
    const jsonValue = typeof value === 'string' ? JSON.parse(value) : value;

    return (
      <ReactJson
        src={jsonValue}
        name={false}
        theme="rjv-default"
        displayDataTypes={false}
        displayObjectSize={true}
        enableClipboard={true}
        collapsed={2}
        indentWidth={2}
        collapseStringsAfterLength={50}
        onEdit={!readOnly ? (edit) => onChange?.(edit.updated_src) : undefined}
        onAdd={!readOnly ? (add) => onChange?.(add.updated_src) : undefined}
        onDelete={!readOnly ? (del) => onChange?.(del.updated_src) : undefined}
        style={{
          backgroundColor: 'transparent',
          fontSize: 'inherit',
          ...style,
        }}
      />
    );
  } catch (error) {
    return <Typography.Text type="secondary">无效的 JSON 格式</Typography.Text>;
  }
};

// 状态标签组件
interface StatusTagProps {
  status: string;
}

const StatusTag: React.FC<StatusTagProps> = ({ status }) => {

  // 获取主题
  const { token } = theme.useToken();
  // 获取状态配置
  const config = STATUS_CONFIG[status as keyof typeof STATUS_CONFIG] || {
    text: status,
    getStyle: () => ({
      color: token.colorTextSecondary,
      bgColor: token.colorBgContainer,
    }),
  };

  // 获取样式
  const { color, bgColor } = config.getStyle(token);

  // 返回状态标签
  return (
    <Typography.Text style={{
      color,
      background: bgColor,
      padding: '4px 12px',
      borderRadius: token.borderRadius,
      display: 'inline-block',
    }}>
      {config.text}
    </Typography.Text>
  );
};

// 主页面组件
const SystemPage: React.FC = () => {
  const { token } = theme.useToken();
  const [data, setData] = useState<Array<SystemVO>>([]);
  const [update, setUpdate] = useState<null | SystemVO>(null);
  const [add, setAdd] = useState(false);
  const [deleteItem, setDeleteItem] = useState<null | SystemVO>(null);
  const [viewItem, setViewItem] = useState<null | SystemVO>(null);
  const [jsonPreview, setJsonPreview] = useState<any>(null);
  const [form] = Form.useForm();

  // 数据处理函数
  const formatText = (text: string) => {
    return text.length > 256 ? `${text.substr(0, 255)}...` : text;
  };

  // 处理 JSON 变化
  const handleJsonChange = (value: string) => {
    try {
      // 解析 JSON
      const parsedJson = value ? JSON.parse(value) : null;
      // 设置 JSON 预览
      setJsonPreview(parsedJson);
    } catch (e) {
      // 设置 JSON 预览为 null
      setJsonPreview(null);
    }
  };

  const downloadWorkFlow = (record: SystemVO) => {
    try {
      // 解析JSON配置值
      const config = JSON.parse(record.confValue);

      // 获取workflow数据
      const workflow = config?.extra_data?.extra_pnginfo?.workflow;

      if (workflow) {
        // 格式化并下载
        const workflowJSON = JSON.stringify(workflow, null, 2);
        downloadJson(workflowJSON, `${record.confKey}.json`);
        message.success('工作流下载成功');
      } else {
        message.error('未找到workflow配置');
      }
    } catch (error) {
      console.error('下载失败:', error);
      message.error('工作流解析失败，请检查配置格式');
    }
  };

  // API 调用函数
  const fetchData = () => {

    // 查询所有系统配置
    queryAllSys().then(res => {
      if (res) {
        // 过滤掉不包含的配置项
        const filteredData = res
          .filter(item => !EXCLUDED_CONFIGS.includes(item.confKey) && !item.confValue.includes('{"client_id"'))
          .map(item => ({
            ...item,
            showConfValue: formatText(item.confValue),
          }));
        setData(filteredData);
      }
    });
  };

  // 压缩展示图片
  const handleResizeImage = () => {
    // 调用压缩图片接口
    resizeShowImage({}).then(res => {
      if (res) {
        notification.success({ message: '压缩成功' });
      }
    });
  };

  // 表单处理函数
  const handleSubmit = (values: Record<string, any>) => {
    // 如果既不是新增也不是修改，则提示请设置要修改的配置项
    if (!add && !update) {
      message.warning('请设置要修改的配置项');
      return;
    }

    // 尝试解析 JSON
    try {
      JSON.parse(values.confValue);
      // 提交值
      submitValues(values);
    } catch (e) {
      // 弹出确认框
      Modal.confirm({
        centered: true,
        title: '确认提交',
        content: '配置值不符合 JSON 格式要求，是否继续提交？',
        cancelText: '返回修改',
        okText: '继续提交',
        onOk: () => submitValues(values),
      });
    }
  };

  // 提交值
  const submitValues = (values: Record<string, any>) => {
    // 获取方法
    const method = add ? addSys : updateSys;
    // 处理值
    const processedValues = {
      ...values,
      confValue: processConfigValue(values.confValue),
    };

    // 调用方法
    method(processedValues).then(res => {
      if (res) {
        notification.success({ message: '修改成功' });
        resetForm();
        fetchData();
      }
    });
  };

  // 处理配置值
  const processConfigValue = (value: string) => {
    if (!value) return value;

    try {
      // 尝试解析 JSON
      const numValue = Number(value);
      // 如果解析成功，并且值是数字，则返回值
      if (!isNaN(numValue) && String(numValue) === value.trim()) {
        return value.trim();
      }
      // 否则返回 JSON 字符串
      return JSON.stringify(JSON.parse(value));
    } catch (e) {
      return value;
    }
  };

  // 重置表单
  const resetForm = () => {
    setAdd(false);
    setUpdate(null);
    setJsonPreview(null);
    form.resetFields();
  };

  // 初始化
  useEffect(() => {
    fetchData();
  }, []);

  // 复制文本的公共方法
  const copyText = async (text: string) => {
    try {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(text);
        message.success('复制成功');
      } else {
        // 降级方案：使用 document.execCommand
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        message.success('复制成功');
      }
    } catch (error) {
      console.error('复制失败:', error);
      message.error('复制失败');
    }
  };

  // 配置项列渲染
  const renderConfKey = (text: string) => (
    <Typography.Text
      strong
      style={{
        color: token.colorPrimary,
        cursor: 'pointer',
      }}
      onClick={() => copyText(text)}
    >
      {text}
    </Typography.Text>
  );

  // 说明列渲染
  const renderMemo = (text: string) => (
    <Typography.Text type="secondary" onClick={() => copyText(text)}>
      {text}
    </Typography.Text>
  );

  // 配置值列渲染
  const renderConfValue = (text: string) => (
    <div style={{
      maxHeight: '100px',
      overflowY: 'auto',
      padding: '12px',
      background: token.colorBgContainer,
      border: `1px solid ${token.colorBorderSecondary}`,
      borderRadius: token.borderRadiusLG,
      fontSize: token.fontSize,
      lineHeight: '1.5',
    }} onClick={() => copyText(text)}>
      <Typography.Text>{formatText(text)}</Typography.Text>
    </div>
  );

  // 表格列配置
  const getColumns = () => [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: '5%',
    },
    {
      title: '配置项',
      dataIndex: 'confKey',
      key: 'confKey',
      width: '20%',
      render: renderConfKey,
    },
    {
      title: '说明',
      dataIndex: 'memo',
      key: 'memo',
      width: '20%',
      render: renderMemo,
    },
    {
      title: '配置值',
      dataIndex: 'confValue',
      width: '40%',
      key: 'confValue',
      render: renderConfValue,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: '8%',
      align: 'center' as const,
      render: (status: string) => <StatusTag status={status} />,
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
      key: 'modifyTime',
      width: '8%',
    },
    {
      title: '操作',
      key: 'action',
      width: '12%',
      align: 'center' as const,
      render: (record: SystemVO) => (
        <Flex gap={4} justify="center">
          <Button
            type="link"
            size="small"
            onClick={() => setViewItem(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            disabled={!isValidJsonObject(record.confValue)}
            onClick={() => downloadWorkFlow(record)}
          >
            下载工作流
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => {
              setUpdate(record);
              setAdd(false);
              form.setFieldsValue({
                id: record.id,
                confKey: record.confKey,
                confValue: record.confValue,
                memo: record.memo,
              });
              handleJsonChange(record.confValue);
            }}
          >
            修改
          </Button>
          <Button
            type="link"
            size="small"
            danger
            onClick={() => setDeleteItem(record)}
          >
            删除
          </Button>
        </Flex>
      ),
    },
  ];

  async function downloadTrainWorkflowJson(ids: string) {
    let taskId = ids.trim();
    if (taskId && Number(taskId) > 0){
      fetchWorkflowByComfyuiTaskId(Number(taskId)).then(res => {
        if (res){
          downloadJson(res, taskId + '.json');
        } else {
          message.warning('未找到该任务的工作流');
        }
      })
    }
  }

  async function downloadCreateWorkflowJson(ids: string) {
    let taskId = ids.trim();
    if (taskId && Number(taskId) > 0){
      fetchWorkflow(Number(taskId)).then(res => {
        if (res){
          downloadJson(res, taskId + '.json');
        } else {
          message.warning('未找到该任务的工作流');
        }
      })
    }
  }

  return (
    <PageContainer style={{ padding: 12 }}>
      <Card
        bordered={false}
        style={{
          borderRadius: token.borderRadiusLG,
          boxShadow: token.boxShadowSecondary,
        }}
      >
        <Flex vertical gap={24}>
          <Flex justify="space-between" align="center">
            <Flex gap={16} align={'center'} wrap={'wrap'}>
              <RefreshButton refresh={fetchData} />

              <Button type="primary" onClick={() => {
                resetForm();
                setAdd(true);
              }}>
                新增系统配置
              </Button>
              <Popconfirm
                title="是否压缩所有模型、模特、场景的展示图片？"
                onConfirm={handleResizeImage}
                okText="确认"
                cancelText="取消"
              >
                <Button>压缩展示图片</Button>
              </Popconfirm>
              <ModifyColorConfig />
              <BatchProcessBtn title={'重新上传lora'} batchMethod={batchUploadOss} />
              <BatchProcessBtn title={'批量生成审核图片'} batchMethod={batchCreateTestImages} />
              <BatchProcessBtn title={'训练文件夹同步'} batchMethod={batchTrainFolderSync} />
              <BatchProcessBtn title={'批量重新打标'} batchMethod={batchRelabelLora} />
              <BatchProcessBtn title={'批量重新打标训练'} batchMethod={batchRetrainLora} />
              <BatchProcessBtn title={'批量重新训练+自动确认'} batchMethod={batchRetrainAndAutoComplete} />
              <BatchProcessBtn title={'批量自动交付'} batchMethod={batchAutoDelivery} />
              <BatchProcessBtn title={'重置训练后服装'} batchMethod={batchInitTrainedModel} />
              <BatchProcessBtn title={'批量服装自动创作'} batchMethod={batchAutoCreateImages} />
              <Divider type={'vertical'} style={{ margin: '0 8px',backgroundColor:'blue',height:20 }} orientation={'center'}/>
              <BatchProcessBtn title={'调整元素标签'} batchMethod={batchCorrectType} />
              <BatchProcessBtn title={'订正元素训练类型'} batchMethod={batchInitTrainType} />
              <BatchProcessBtn title={'订正风格lora参数'} batchMethod={batchCorrectStyle} />
              <Divider type={'vertical'} style={{ margin: '0 8px',backgroundColor:'blue',height:20 }} orientation={'center'}/>
              <BatchProcessBtn title={'手动同步文件'} fieldTitle={'文件地址(绝对路径或相对路径)'} batchMethod={syncFile} />
              <BatchProcessBtn title={'下载创作工作流'} fieldTitle={'单个creative_task#id'} placeholder={'只支持单个taskId'} batchMethod={downloadCreateWorkflowJson} />
              <BatchProcessBtn title={'下载训练工作流'} fieldTitle={'单个comfyui_task#id'} placeholder={'只支持单个taskId'} batchMethod={downloadTrainWorkflowJson} />
              <Divider type={'vertical'} style={{ margin: '0 8px',backgroundColor:'blue',height:20 }} orientation={'center'}/>
              <BatchProcessBtn title={'批量设置场景姿势图'}  placeholder={'可选择多个场景'} batchMethod={batchSetScenePose} />
            </Flex>
          </Flex>

          <Table
            columns={getColumns()}
            dataSource={data}
            pagination={false}
            rowKey="id"
            style={{
              borderRadius: token.borderRadiusLG,
              overflow: 'hidden',
            }}
          />
        </Flex>
      </Card>

      {/* 编辑模态框 */}
      <Modal
        open={!!update || add}
        title={add ? '新增系统配置' : '修改系统配置'}
        width={1800}
        onCancel={resetForm}
        onOk={form.submit}
        maskClosable={false}
        destroyOnClose
        centered
      >
        <Flex gap={16} style={{ padding: '24px 0' }}>
          <Form
            layout="vertical"
            form={form}
            onFinish={handleSubmit}
            style={{ flex: 1 }}
            onValuesChange={(changedValues) => {
              if ('confValue' in changedValues) {
                handleJsonChange(changedValues.confValue);
              }
            }}
          >
            <Form.Item hidden name="id">
              <Input />
            </Form.Item>
            <Form.Item
              label="配置项"
              name="confKey"
              rules={[{ required: true, message: '请输入配置项' }]}
            >
              <Input
                placeholder="请输入配置项"
                style={{ borderRadius: token.borderRadius }}
              />
            </Form.Item>
            <Form.Item
              label={
                <Flex align="center" gap={8}>
                  <span>配置值</span>
                  <Button
                    size="small"
                    onClick={() => {
                      const value = form.getFieldValue('confValue');
                      try {
                        const formatted = JSON.stringify(JSON.parse(value), null, 2);
                        form.setFieldValue('confValue', formatted);
                        setJsonPreview(JSON.parse(formatted));
                      } catch (e) {
                        message.error('当前配置值不是有效的 JSON 格式');
                      }
                    }}
                  >
                    格式化
                  </Button>
                </Flex>
              }
              name="confValue"
              rules={[{ required: true, message: '请输入配置值' }]}
            >
              <TextArea
                rows={25}
                placeholder="请输入配置值"
                style={{
                  borderRadius: token.borderRadius,
                  resize: 'vertical',
                  height: '600px',
                }}
              />
            </Form.Item>
            <Form.Item
              label="说明"
              name="memo"
              rules={[{ required: true, message: '请输入说明' }]}
            >
              <TextArea
                rows={2}
                placeholder="请输入说明"
                style={{
                  borderRadius: token.borderRadius,
                  resize: 'vertical',
                }}
              />
            </Form.Item>
          </Form>

          <div style={{ width: '50%' }}>
            <Typography.Title level={5} style={{ marginBottom: 8 }}>
              配置值 JSON 预览
            </Typography.Title>
            <div style={{
              padding: '12px',
              background: token.colorBgContainer,
              border: `1px solid ${token.colorBorderSecondary}`,
              borderRadius: token.borderRadiusLG,
              overflowY: 'auto',
              height: '800px',
            }}>
              <JsonPreview
                value={jsonPreview}
                onChange={(updated) => {
                  setJsonPreview(updated);
                  form.setFieldValue('confValue', JSON.stringify(updated, null, 2));
                }}
              />
            </div>
          </div>
        </Flex>
      </Modal>

      {/* 删除确认模态框 */}
      <Modal
        open={!!deleteItem}
        title="删除确认"
        onCancel={() => setDeleteItem(null)}
        onOk={() => {
          if (!deleteItem) return;
          delSys(deleteItem.id).then(res => {
            if (res) {
              notification.success({ message: '删除成功' });
              fetchData();
              setDeleteItem(null);
            }
          });
        }}
        maskClosable={false}
        centered
      >
        <Flex vertical gap={16} style={{ padding: '16px 0' }}>
          <Typography.Text type="warning">
            确定要删除以下系统配置吗？
          </Typography.Text>
          <Typography.Text code>
            {deleteItem?.confKey}
          </Typography.Text>
          <Typography.Text type="secondary">
            删除后将无法恢复，请谨慎操作
          </Typography.Text>
        </Flex>
      </Modal>

      {/* 查看详情模态框 */}
      <Modal
        open={!!viewItem}
        title="查看系统配置"
        onCancel={() => setViewItem(null)}
        footer={[
          <Button key="close" onClick={() => setViewItem(null)}>
            关闭
          </Button>,
        ]}
        width={800}
        centered
      >
        <Flex vertical gap={16} style={{ padding: '16px 0' }}>
          <Flex gap={8}>
            <Typography.Text type="secondary" style={{ width: 80 }}>配置项：</Typography.Text>
            <Typography.Text strong>{viewItem?.confKey}</Typography.Text>
          </Flex>
          <Flex gap={8}>
            <Typography.Text type="secondary" style={{ width: 80 }}>说明：</Typography.Text>
            <Typography.Text>{viewItem?.memo}</Typography.Text>
          </Flex>
          <Flex gap={8}>
            <Typography.Text type="secondary" style={{ width: 80 }}>状态：</Typography.Text>
            {viewItem && <StatusTag status={viewItem.status} />}
          </Flex>
          <Flex vertical gap={8}>
            <Typography.Text type="secondary">配置值：</Typography.Text>
            <div style={{
              padding: '12px',
              background: token.colorBgContainer,
              border: `1px solid ${token.colorBorderSecondary}`,
              borderRadius: token.borderRadiusLG,
              maxHeight: '500px',
              overflowY: 'auto',
            }}>
              {(() => {
                // 如果配置值为空，则返回 null
                if (!viewItem?.confValue) return null;
                // 尝试解析为数字
                const numValue = Number(viewItem.confValue);
                // 如果解析成功，并且值是数字，则返回值
                if (!isNaN(numValue) && String(numValue) === viewItem.confValue.trim()) {
                  return <Typography.Text>{viewItem.confValue}</Typography.Text>;
                }
                // 否则返回 JSON 预览
                try {
                  return <JsonPreview value={JSON.parse(viewItem.confValue)} readOnly />;
                } catch (e) {
                  return <Typography.Text>{viewItem.confValue}</Typography.Text>;
                }
              })()}
            </div>
          </Flex>
        </Flex>
      </Modal>
    </PageContainer>
  );
};

export default SystemPage;