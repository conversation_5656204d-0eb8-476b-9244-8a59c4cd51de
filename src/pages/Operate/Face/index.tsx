import { PageContainer } from '@ant-design/pro-components';
import React, { CSSProperties, useEffect, useState, useCallback } from 'react';
import {
  Button, Checkbox, Dropdown,
  Flex,
  Form,
  Image,
  Input,
  InputNumber,
  message,
  Modal,
  notification, Pagination,
  Popconfirm,
  Radio,
  Select, Space, Tooltip,
  Upload,
  UploadFile,
  UploadProps,
} from 'antd';
import {
  FileType,
  getBase64,
  getImageName, getImageNameWithPath,
  getUserInfo,
  isProdEnv,
  objectToPathObject,
  pathObjectToObject,
} from '@/utils/utils';
import {
  addElement,
  AgeRanges,
  cloneElement,
  deleteElement,
  ElementConfigWithBlobs,
  getElementTypes,
  queryElementById, queryElementByPage,
  updateElement,
} from '@/services/ElementController';
import {
  CopyOutlined,
  DeleteOutlined, DownOutlined,
  EditOutlined,
  ExperimentOutlined,
  LogoutOutlined,
  PlusOutlined, ThunderboltOutlined,
} from '@ant-design/icons';
import { UPLOAD_PRO_URL } from '@/constants';
import './index.css';
import InputWithTags from '@/components/InputWithTags';
import { TagType } from '@/services/TagsController';
import { queryLoraVipCfg, queryModelReplaceKey } from '@/services/SystemController';
import ChildrenTabCard, { processChildren } from '@/components/Operate/ChildrenTabCard';
import ElementImageRecord from '@/components/Operate/ElementImageRecord';
import LoraTrainDetail from '@/components/Lora/LoraTrainDetail';
import NewLabel from '@/components/new/NewLabel';
import { RefreshButton } from '@/components/Common/CommonComponent';
import ElementImagesSelector from '@/components/Operate/ElementImagesSelector';
import { RetrainBtn } from '@/components/Operate/LoraComponent';
import AssignModel2User from '@/components/AssignModel2User';
import { queryAllMaster } from '@/services/UserController';
import { addLora2Vip } from '@/services/MaterialModelController';
import { UploadFileStatus } from 'antd/lib/upload/interface';
import { AGE_RANGE_LIST, SELECT_AGE_RANGE_OPTIONS } from '@/components/AgeRangeSelector';
import { useExperimental } from '@/components/Operate/Experimental';
import UploadImageSelector from '@/components/Lora/UploadImageSelector';
import { batchUploadOss2Input } from '@/services/FileController';
import { PromptDictVO, queryDictByKeyAndTags } from '@/services/PromptDictController';
import { getTypeItemsByTagsFromData } from '@/utils/TagsUtils';
import debounce from 'lodash/debounce';

const clothStyle: CSSProperties = { width: 90, padding: 0, textAlign: 'center' };
const clothStyleFlux: CSSProperties = { ...clothStyle, color: 'red' };
const AllFaceClothTypes: Array<{ key: string; value: string; label: string, style: CSSProperties }> = [
  { key: 'v_1,front view', value: 'v_1,front view', label: 'sdxl正面', style: clothStyle },
  { key: 'v_1,back view', value: 'v_1,back view', label: 'sdxl背面', style: clothStyle },
  { key: 'v_2,front view', value: 'v_2,front view', label: 'flux正面', style: clothStyleFlux },
  { key: 'v_2,back view', value: 'v_2,back view', label: 'flux背面', style: clothStyleFlux },
];

export const FaceBaseType = ['male-model', 'female-model'];

export const sortElements = () => {
  return (a, b) => {
    const aHasV2 = a.type.includes('v_2');
    const bHasV2 = b.type.includes('v_2');
    if (aHasV2 && !bHasV2) {
      return -1;
    } else if (!aHasV2 && bHasV2) {
      return 1;
    } else {
      return 0;
    }
  };
};

export const uploadButton = (
  <button style={{ border: 0, background: 'none' }} type="button">
    <PlusOutlined />
    <div style={{ marginTop: 8 }}>Upload</div>
  </button>
);

const Face: React.FC<unknown> = () => {
  const userInfo = getUserInfo();
  // @ts-ignore
  const [datas, setDatas] = useState<Array<ElementConfigWithBlobs>>([]);
  const [showDialog, setShowDialog] = useState(false);
  const [add, setAdd] = useState(false);
  const [deleteId, setDeleteId] = useState<number>(0);
  const [searchId, setSearchId] = useState<number | undefined | null>();
  const [searchKey, setSearchKey] = useState<string | undefined>();
  const [selectType, setSelectType] = useState<string>('all');
  const [selectStyleType, setSelectStyleType] = useState<string>('all');
  const [selectSpecialType, setSelectSpecialType] = useState<string>('all');
  const [selectNationType, setSelectNationType] = useState<string>('all');
  const [searchAgeRange, setSearchAgeRange] = useState<string>('ALL');
  const [form] = Form.useForm();
  const [opItem, setOpItem] = useState<ElementConfigWithBlobs | undefined>();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(70);

  //图片上传相关
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [actualImgFileList, setActualImgFileList] = useState<UploadFile[]>([]);
  const [faceConfig, setFaceConfig] = useState<Array<any>>([]);
  const [showOpenScope, setShowOpenScope] = useState('all');
  const [showTesting, setShowTesting] = useState('all');
  const [showSwapType, setShowSwapType] = useState<string>('all');
  const [modelReplaceKey, setModelReplaceKey] = useState<string>('');
  const [faceSwapType, setFaceSwapType] = useState<string>('IMAGE');
  const [faceRepairFaceType, setFaceRepairFaceType] = useState<string>('v_2');
  const [loraSwapFace, setLoraSwapFace] = useState<boolean>(false);
  const [genderType, setGenderType] = useState<string>();

  const [typeTags, setTypeTags] = useState<Record<string, Array<PromptDictVO>>>();
  const [checkedStyleTypes, setCheckedStyleTypes] = useState<string[]>();
  const [checkedNationTypes, setCheckedNationTypes] = useState<string[]>();
  const [checkedSpecialTypes, setCheckedSpecialTypes] = useState<string[]>();

  const [trainDetailId, setTrainDetailId] = useState<number>();
  const [showTrainDetail, setShowTrainDetail] = useState(false);

  //需要转交客户的模型
  const [assignElementConfig, setAssignElementConfig] = useState<ElementConfigWithBlobs | null>();

  //是否只看客户上传模型
  const [showCustomerOnly, setShowCustomerOnly] = useState(false);

  const [onlyNearingDelivery, setOnlyNearingDelivery] = useState(false);

  const [masterOptions, setMasterOptions] = useState<Array<any>>([]);
  const [searchUserId, setSearchUserId] = useState<number | null>(null);

  //lora_vip配置，训练加急白名单
  const [loraVipCfg, setLoraVipCfg] = useState<number[]>([]);

  const [privatelyOpen2UserRoleType, setPrivatelyOpen2UserRoleType] = useState('all');
  const [searchPrivateOpen2UserId, setSearchPrivateOpen2UserId] = useState<number | null>(null);

  const [checkedAgeRange, setCheckedAgeRange] = useState<string | null>(null);

  const [onlyExperimental, setOnlyExperimental] = useState(false);
  const { getExperimentalLabel, setExperimental, ExperimentalLabel } = useExperimental();

  const [uploadImageModel, setUploadImageModel] = useState<number | null | undefined>(null);

  // 创建防抖函数
  const debouncedSearchId = useCallback(
    debounce((value: number | null) => {
      setSearchId(value);
    }, 100),
    []
  );

  const debouncedSearchKey = useCallback(
    debounce((value: string) => {
      setSearchKey(value);
    }, 300),
    []
  );

  async function fetchData() {
    let types = selectStyleType !== 'all' ? [selectStyleType]: [];
    if (selectNationType !== 'all') {
      types.push(selectNationType);
    }
    if (selectSpecialType !== 'all') {
      types.push(selectSpecialType);
    }
    const query = {
      configKey: 'FACE', openScope: showOpenScope === 'all' ? null : showOpenScope,
      status: showTesting === 'all' ? null : showTesting, swapType: showSwapType === 'all' ? null : showSwapType,
      genderType: selectType === 'all' ? null : selectType, types,
      id: searchId, name: searchKey?.trim(), pageNum: page, pageSize,
      belong: showCustomerOnly ? 'CUSTOM' : null, userId: searchUserId || null,
      onlyNearingDelivery,
      privatelyOpen2UserRoleType: privatelyOpen2UserRoleType === 'all' ? null : privatelyOpen2UserRoleType,
      privatelyOpen2UserId: searchPrivateOpen2UserId || null,
      onlyExperimental,
      filterAgeRange: searchAgeRange === 'ALL' ? null : searchAgeRange,
    };

    queryElementByPage(query).then(res => {
      if (!res) return;
      setTotal(res.totalCount || 0);

      res.list?.forEach(item => {
        item.type = [...new Set(item.type)];
      });

      setDatas(res.list || []);
    });
  }

  const handlePageChange = (page: number, pageSize?: number) => {
    setPage(page);
    if (pageSize) {
      setPageSize(pageSize);
    }
  };

  const refreshVipCfg = () => {
    queryLoraVipCfg().then(res => {
      if (res && Array.isArray(res)) {
        setLoraVipCfg(res);
      }
    });
  };

  const fetchFaceConfig = () => {
    getElementTypes('FACE').then(res => {
      if (res) {
        const arr = [];
        res.forEach((item) => {
          //@ts-ignore
          arr.push({ label: item.name, name: item.name, value: item.code, code: item.code });
        });
        setFaceConfig(arr);
      }
    });
  };

  const fetchTypeTags = () => {
    queryDictByKeyAndTags('FACE_TYPES', [['style','female'],['style','male'],['nation'],['special']]).then(res => {
      if (res) {
        setTypeTags(res);
      }
    });
  };

  const getTypeItemsByTags = (tags:string[]) => {
    return getTypeItemsByTagsFromData(tags, typeTags);
  }

  const getStyleTypeItems = () => {
    if (selectType === 'all'){
      return [...getTypeItemsByTags(['style', 'female']), ...getTypeItemsByTags(['style', 'male'])];
    }
    return getTypeItemsByTags(['style', selectType === 'male-model' ? 'male' : 'female']);
  }

  useEffect(() => {
    fetchData();
    fetchFaceConfig();
    fetchTypeTags();
    queryModelReplaceKey().then(res => {
      if (res) {
        setModelReplaceKey(res);
      }
    });

    queryAllMaster(['MERCHANT', 'OPERATOR', 'ADMIN', 'DISTRIBUTOR']).then(res => {
      if (res && Array.isArray(res)) {
        const masterOptions = [];
        res.forEach(item => {
          // @ts-ignore
          masterOptions.push({
            label: item.nickName + (item.corpName ? '@' + item.corpName : ''),
            value: item.id + '',
          });
        });

        setMasterOptions(masterOptions);
      }
    });

    if (isProdEnv() && ![100155,100158].includes(userInfo?.id || 0)) {
      queryElementByPage({
        pageNum: 1,
        pageSize: 10,
        onlyNearingDelivery: true,
        configKey: 'FACE',

      }).then(res => {
        if (res && res?.totalCount && res?.totalCount > 0) {
          Modal.confirm({
            title: '交付超时提醒',
            content: `有${res?.totalCount}个客户模特交付即将/已经超时，是否查看？`,
            okText: '查看超时模特',
            cancelText: '先忽略',
            centered: true,
            onOk: () => {
              setOnlyNearingDelivery(true);
            },
          });
        }
      });
    }

  }, []);

  useEffect(() => {
    fetchData();
  }, [page, pageSize]);

  useEffect(() => {
    setPage(1);
    fetchData();
  }, [showOpenScope, searchKey, showSwapType, showTesting, selectType, selectStyleType, selectNationType, selectSpecialType, searchId, showCustomerOnly, searchUserId, onlyNearingDelivery, searchPrivateOpen2UserId, privatelyOpen2UserRoleType, onlyExperimental, searchAgeRange]);

  const handleAdd = () => {
    //先清空一遍表单数据
    form.resetFields();
    setFileList([]);
    setAdd(true);
    setShowDialog(true);
    setOpItem(undefined);
    setFaceSwapType('IMAGE');
    setLoraSwapFace(false);
    setFaceRepairFaceType('v_2');
    setCheckedAgeRange(null);
    setActualImgFileList([]);
  };

  const handleModify = (item: ElementConfigWithBlobs) => {
    //先清空一遍表单数据
    form.resetFields();
    setAdd(false);

    queryElementById(item.id).then(res => {
      if (!res) return;

      const target = res;
      //对children进行排序，只要type中带"v_2"的都排在前面
      target.children = target.children.sort(sortElements());
      target.type = [...new Set(target.type)];

      // 从 target.type 中匹配 SELECT_AGE_RANGE_OPTIONS 的数据
      const ageRangeValues = target.type.filter(type =>
        SELECT_AGE_RANGE_OPTIONS.some(group =>
          group.options.some(option => option.value === type),
        ),
      );
      setCheckedAgeRange(ageRangeValues[0] || null);

      setLoraSwapFace(target.extInfo['loraSwapFace'] === 'Y');
      setOpItem(target);

      //展开扩展信息
      const extInfoExtend = objectToPathObject(target.extInfo, 'extInfo');

      if (target.children) {
        for (let i = 0; i < target.children.length; i++) {
          const innerExtInfoExtend = objectToPathObject(target.children[i].extInfo, 'extInfo');
          target.children[i] = { ...target.children[i], ...innerExtInfoExtend };
        }
      }

      let genderType = target.type ? target.type.filter(t => FaceBaseType.includes(t))[0] : null;
      form.setFieldsValue({
        ...target, ...extInfoExtend,
        type: genderType,
        styleType: target.type ? target.type.filter(t => getTypeItemsByTags(['style', genderType === 'male-model' ? 'male' : 'female']).some(e => e.value === t)) : null,
        specialType: target.type ? target.type.filter(t => getTypeItemsByTags(['special']).some(e => e.value === t)) : null,
        nationType: target.type ? target.type.filter(t => getTypeItemsByTags(['nation']).some(e => e.value === t)) : null,
        ageRange: ageRangeValues[0] || null,
      });

      if (genderType){
        setGenderType(genderType);
      }

      setFileList([{
        uid: '-1', // 文件唯一标识，负数表示不是用户新上传的
        name: 'image.png', // 文件名
        status: 'done', // 状态有：uploading, done, error, removed
        url: target.showImage, // 图片路径
      }]);

      //轮播展示图
      const showImgs = JSON.parse(res.extInfo['showImgs'] || '[]') as string[];
      form.setFieldValue(['extInfo', 'showImgs'], showImgs);

      // @ts-ignore
      if (target.extInfo && target.extInfo['actualSwapFaceImg']) {
        const moreFile = target.extInfo['faceImageMoreUrl'] ? (target.extInfo['faceImageMoreUrl'] as string[]).map((e) => {
          return {
            uid: e, // 文件唯一标识，负数表示不是用户新上传的
            name: 'image.png', // 文件名
            status: 'done' as UploadFileStatus, // 状态有：uploading, done, error, removed
            url: e, // 图片路径
          };
        }) : [];
        setActualImgFileList([{
          uid: target.extInfo['actualSwapFaceImg'], // 文件唯一标识，负数表示不是用户新上传的
          name: 'image.png', // 文件名
          status: 'done', // 状态有：uploading, done, error, removed
          url: target.extInfo['actualSwapFaceImg'], // 图片路径
        }, ...moreFile]);
      } else {
        setActualImgFileList([]);
      }

      console.log('handleModify:', target, extInfoExtend);
      setShowDialog(true);
      setFaceSwapType(target.extInfo['swapType'] ? target.extInfo['swapType'] : 'IMAGE');
      setFaceRepairFaceType(target.extInfo['repairFaceType'] ? target.extInfo['repairFaceType'] : 'v_2');
    });
  };

  // 从模特训练的上传图片中获取到的图片
  const handleChangeUploadImages = (images:string[]) => {
    const newFiles = images.map(e => {return {
      uid: e, // 文件唯一标识，负数表示不是用户新上传的
      name: 'image.png', // 文件名
      status: 'done' as UploadFileStatus, // 状态有：uploading, done, error, removed
      url: e, // 图片路径
    }});
    setActualImgFileList(newFiles);
    setUploadImageModel(null);
    form.setFieldValue('extInfo.actualSwapFaceImg', images[0]);
  }

  const getUploadImageValue = () => {
    if(!actualImgFileList) {
      return [];
    }
    return actualImgFileList.filter(e => !!e.url).map(e => e.url) as string[];
  }

  const handleDelete = (item: ElementConfigWithBlobs) => {
    setDeleteId(item.id);
  };

  const handleClone = (item: ElementConfigWithBlobs, fullCopy = false) => {
    cloneElement(item.id, fullCopy).then(res => {
      if (res) {
        message.success('克隆成功');
        fetchData();
      }
    });
  };

  const commitDel = () => {
    deleteElement(deleteId).then((res) => {
      if (res) {
        setDeleteId(0);
        fetchData();
      }
    });
  };

  const handleCommit = async (values) => {
    console.log('执行模特信息修改：', values);
    const extInfoMap = pathObjectToObject(values);
    extInfoMap.configKey = 'FACE';
    extInfoMap.level = 2;
    extInfoMap.opChildren = true;

    let faceImagePath = '';
    let faceImageMorePath: string[] = [];

    if (extInfoMap.showImage) {
      if (typeof extInfoMap.showImage !== 'string') {
        if (!extInfoMap.showImage.file.response) {
          notification.error({ message: '请上传展示图片' });
          return;
        }
        extInfoMap.showImage = extInfoMap.showImage.file.response.data;
        faceImagePath = getImageName(extInfoMap.showImage);
      }
    }

    if (extInfoMap.extInfo.actualSwapFaceImg) {
      if (typeof extInfoMap.extInfo.actualSwapFaceImg !== 'string') {
        if (extInfoMap.extInfo.actualSwapFaceImg.fileList && extInfoMap.extInfo.actualSwapFaceImg.fileList.length > 0) {

          if (!extInfoMap.extInfo.actualSwapFaceImg.fileList.some(e => e.response || e.url)) {
            console.log('extInfoMap.extInfo.actualSwapFaceImg.fileList:', extInfoMap.extInfo.actualSwapFaceImg);
            notification.error({ message: '请上传真实换脸图片' });
            return;
          }

          const fileList = extInfoMap.extInfo.actualSwapFaceImg.fileList.map(file => file.url || file.response.data);
          // 兼容逻辑, 1拆2，第一个放在actualSwapFaceImg/faceImage，其他2个放在faceImageMoreUrl/faceImageMore
          if (fileList && fileList.length > 1) {
            faceImageMorePath = fileList.slice(1);
            extInfoMap.extInfo['faceImageMoreUrl'] = faceImageMorePath; // 是oss上的地址
            faceImageMorePath = faceImageMorePath.map(item => getImageNameWithPath(item));

            extInfoMap.extInfo['actualSwapFaceImg'] = fileList[0];
          } else {
            extInfoMap.extInfo['faceImageMoreUrl'] = [];
            extInfoMap.extInfo['faceImageMore'] = [];
            extInfoMap.extInfo['actualSwapFaceImg'] = fileList[0];
          }

          faceImagePath = getImageNameWithPath(fileList[0]);
        } else {
          delete extInfoMap.extInfo.actualSwapFaceImg;
        }
      } else if (actualImgFileList) {
        // @ts-ignore 上传图片到机器上
        const success = await batchUploadOss2Input(actualImgFileList.map(item => item.url));
        if(!success) {
          notification.error({ message: '文件上传失败' });
          return;
        }
        extInfoMap.extInfo['actualSwapFaceImg'] = actualImgFileList[0].url;
        faceImagePath = getImageNameWithPath(actualImgFileList[0].url);

        if (actualImgFileList.length > 1) {
          // @ts-ignore
          faceImageMorePath = actualImgFileList.slice(1).map(item => item.url);
          extInfoMap.extInfo['faceImageMoreUrl'] = faceImageMorePath;
          faceImageMorePath = faceImageMorePath.map(item => getImageNameWithPath(item));
        }
      }
    }

    if (faceImagePath) {
      extInfoMap.extInfo['faceImage'] = faceImagePath;
    }
    if (faceImageMorePath && faceImageMorePath.length > 0) {
      extInfoMap.extInfo['faceImageMore'] = faceImageMorePath; // 是gpu机器上的地址
    }

    // 轮播展示图
    if (extInfoMap.extInfo.showImgs && Array.isArray(extInfoMap.extInfo.showImgs)) {
      extInfoMap.extInfo['showImgs'] = JSON.stringify(extInfoMap.extInfo.showImgs);
    }

    extInfoMap.saveShowImage = true;

    // 对type进行处理
    extInfoMap.type = [extInfoMap.type, ...extInfoMap.styleType, ...extInfoMap.specialType, ...extInfoMap.nationType];

    // 若type中包含AGE_RANGE_LIST
    if (extInfoMap.type.some(type => AGE_RANGE_LIST.includes(type))) {
      // 若包含则进行从type中删除
      extInfoMap.type = extInfoMap.type.filter(type => !AGE_RANGE_LIST.includes(type));
    }

    // 将年龄区段添加到type中
    const ageRange = extInfoMap.ageRange;
    if (ageRange) {
      extInfoMap.type.push(ageRange);
    }

    // 删除表单中的ageRange字段，它只是用于UI展示
    delete extInfoMap.ageRange;

    extInfoMap.extInfo['faceLora'] = extInfoMap.extInfo['faceLora'] ? extInfoMap.extInfo['faceLora'].trim() : extInfoMap.extInfo['faceLora'];

    processChildren(extInfoMap, AllFaceClothTypes, '服装角度');

    console.log('handleCommit:', extInfoMap);
    const method = add ? addElement : updateElement;
    method(extInfoMap).then((res) => {
      if (res) {
        notification.success({ message: add ? '添加成功' : '修改成功' });
        setShowDialog(false);
        setOpItem(undefined);
        fetchData();
      }
    });
  };

  function getTrainingStatusTitle(item: ElementConfigWithBlobs) {
    let labelStatus = item?.labelFinished ? '打标已完成' : '抠图打标中';
    let loraStatus = item?.loraTaskStatusDesc || '未开始';

    return item && item?.loraConfirmed ? (item?.loraTaskStatus === 'RUNNING' ? '模型训练中' : '模型' + loraStatus) : (labelStatus + '-未确认');
  }

  const selectOriginImgBtn = (element : ElementConfigWithBlobs | undefined) =>
    <Flex vertical style={{ height: 32 }}>
      <div>模特换脸图片</div>
      <Button size={'small'} onClick={() => setUploadImageModel(element?.loraModelId)}>从上传中选择</Button>
      <div className={'color-error text10 weight'}>3张图片自动</div>
      <div className={'color-error text10 weight'}>设为InstantId</div>
    </Flex>

  const ImageCard = (item: ElementConfigWithBlobs) => (
      <div className="models-image-card" style={{
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
        borderRadius: '12px',
        overflow: 'hidden',
        transition: 'all 0.3s ease',
        position: 'relative',
        width: '248px',
        background: '#FFFFFF',
        padding: 0,
      }}
           onMouseOver={(e) => {
             e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.12)';
             e.currentTarget.style.transform = 'translateY(-2px)';
           }}
           onMouseOut={(e) => {
             e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.08)';
             e.currentTarget.style.transform = 'translateY(0)';
           }}
      >
        <ExperimentalLabel {...item} />
        {/* New标签 */}
        {item.isNew &&
          <NewLabel />
        }

        <div className="models-img-cover"
             style={{
               cursor: item?.loraModelId ? 'pointer' : 'default',
               width: '100%',
               height: '248px',
               background: '#F5F6F9',
               borderRadius: '8px 8px 0 0',
               border: 'none',
             }}
             onClick={() => {
               if (!item.loraModelId) {
                 return;
               }
               setShowTrainDetail(true);
               setTrainDetailId(item.loraModelId);
             }}
        >
          <img src={item.showImage} alt={item.name} loading={'lazy'} style={{ objectFit: 'contain' }} />
          {item.status === 'TEST' &&
            <Flex align={'center'} justify={'center'} vertical={true}
                  style={{ width: '100%', height: '100%', position: 'absolute', background: 'rgba(41, 41, 41, 0.3)' }}>
              {item?.loraTraining === true &&
                <>
                  <l-helix size="45" speed="2.5" color={item?.loraTaskStatus === 'RUNNING' ? 'blue' : '#D88FFF'} />
                  <div className={'color-w text16'} style={{ marginTop: 8 }}>{getTrainingStatusTitle(item)}</div>
                </>
              }

              {!item?.loraTraining &&
                <div className={'color-w text16'}>测试中</div>
              }

            </Flex>
          }
        </div>

        <div style={{ padding: '16px', display: 'flex', flexDirection: 'column', gap: '8px' }}>
          {/*模型名称*/}
          <div className="models-image-card-name" style={{ fontSize: 14, fontWeight: '600' }}>
            <Tooltip title={item.name}>
							<span
                style={{
                  maxWidth: '180px',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  display: 'inline-block',
                  verticalAlign: 'middle',
                  marginRight: '4px',
                  cursor: 'pointer',
                  color: loraVipCfg?.includes(item?.loraModelId || 0) && item?.loraTraining ? 'red' : '',
                }}
                onClick={() => {
                  navigator.clipboard.writeText(item.name);
                  message.success('已复制模特名称');
                }}
              >
								{item.name.length > 18 ? item.name.slice(0, 18) + '...' : item.name}
							</span>
            </Tooltip>
            <Tooltip title={`复制ID: ${item?.id}`}>
							<span
                style={{ color: '#727375', fontSize: 12, cursor: 'pointer' }}
                onClick={() => {
                  navigator.clipboard.writeText(item?.id?.toString());
                  message.success('已复制ID');
                }}
              >
								{`(${item?.id})`}
							</span>
            </Tooltip>
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            <div className="models-image-card-name" style={{ fontSize: 12, fontWeight: 'normal' }}>
              创建时间：{item.createTime}
            </div>
            <div className="models-image-card-name" style={{ fontSize: 12, fontWeight: 'normal' }}>
              创建人：{item.userNick}
            </div>
            <div className="models-image-card-name" style={{ fontSize: 12, fontWeight: 'normal' }}>
              专属：{item.privatelyOpen2UserNick || '公开'}
            </div>
            <div className="models-image-card-name" style={{ fontSize: 12, fontWeight: 'normal' }}>
              最后修改：{item.operatorNick}
            </div>
            <div className="models-image-card-name" style={{ fontSize: 12, fontWeight: 'normal' }}>
              交付人：{item.deliveryName}
            </div>

          </div>
          <div className={'row-space-block'} style={{ paddingTop: '8px', borderTop: '1px solid #f0f0f0' }}>
            <Button className={'operate-btn'} size={'small'} onClick={() => handleModify(item)}>修改</Button>
            <Popconfirm title={'将克隆一个名为"' + item.name + '_copy"且状态为内部测试的新模特'}
                        onConfirm={() => handleClone(item)}>
              <Button size={'small'} className={'operate-btn'}>克隆</Button>
            </Popconfirm>

            <ElementImageRecord elementId={item.id} isIcon={true} />

            {item.loraModelId &&
              <>
                <RetrainBtn id={item.loraModelId} materialType={'face'} onlyLabel={false}
                            onChange={() => fetchData()} />

                <RetrainBtn id={item.loraModelId} materialType={'face'} onlyLabel={true}
                            onChange={() => fetchData()} />
              </>
            }

            <Dropdown
              menu={{
                items: [
                  {
                    key: 'loraVip',
                    label: '加急',
                    icon: <ThunderboltOutlined />,
                    disabled: !item?.loraModelId || loraVipCfg?.includes(item?.id) || !item?.loraTraining,
                  },
                  {
                    key: 'delLora',
                    label: '删除模特',
                    danger: true,
                    icon: <DeleteOutlined />,
                  }, {
                    key: 'transfer2Merchant',
                    label: '转交客户',
                    icon: <LogoutOutlined />,
                    disabled: !item?.loraModelId,
                  }, {
                    key: 'fullClone',
                    label: '完全克隆',
                    icon: <CopyOutlined />,
                    disabled: !item?.loraModelId,
                  }, {
                    key: 'handleExperimental',
                    label: getExperimentalLabel({ ...item }),
                    icon: <ExperimentOutlined />,
                    disabled: false,
                  },
                ],
                onClick: ({ key }) => {
                  if (key === 'loraVip') {
                    addLora2Vip(item?.loraModelId || 0).then(res => {
                      if (res) {
                        message.success('加急成功');
                        refreshVipCfg();
                      }
                    });
                  } else if (key === 'delLora') {
                    handleDelete(item);
                  } else if (key === 'transfer2Merchant') {
                    setAssignElementConfig(item);
                  } else if (key === 'fullClone') {
                    handleClone(item, true);
                  } else if (key === 'handleExperimental') {
                    setExperimental({ ...item, type: 'Element' }, fetchData);
                  }
                },
              }}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  更多
                  <DownOutlined />
                </Space>
              </a>
            </Dropdown>


          </div>
        </div>
      </div>
    )
  ;

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
  };

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    const file = newFileList[0];

    if (file && file.response && !file.response.success) {
      notification.error({ message: '上传图片异常，请重试' });
      setFileList([]);
      return;
    }
    setFileList(newFileList);
  };

  const handleActualImgChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    const file = newFileList[0];

    if (file && file.response && !file.response.success) {
      notification.error({ message: '上传图片异常，请重试' });
      setActualImgFileList([]);
      return;
    }
    setActualImgFileList(newFileList);
  };

  const handleChangeType = (e) => {
    setSelectType(e.target.value);
  };

  const filterBaseType = (type: Array<any>): Array<any> => {
    return type.filter(e => FaceBaseType.includes(e.code));
  };

  const gotoEditFaceTag = () => {
    const url = `/#/tags?defaultType=dict&defaultDictType=FACE_TYPES-system`;
    window.open(url, '_blank');
  };

  return (
    <PageContainer>

      <Flex vertical gap={8} style={{ padding: 16 }}>
        <Flex vertical justify={'flex-start'} gap={8}>
          <Flex align={'center'} justify={'flex-start'} gap={8}>
            <Flex align={'center'} justify={'flex-start'} gap={8}>

              {/* 刷新 */}
              <RefreshButton refresh={fetchData} />

              {/* 公开范围 */}
              <Radio.Group optionType="button" defaultValue={'all'} size={'small'}
                           options={[{ label: '全部', value: 'all' },
                             { label: '公共', value: 'public' },
                             { label: '专属', value: 'private' },
                           ]} onChange={e => setShowOpenScope(e.target.value)} />

              {/* 状态 */}
              <Radio.Group optionType="button" size={'small'}
                           options={[{ label: '全部', value: 'all' }, { label: '测试', value: 'TEST' },
                             { label: '正式', value: 'PROD' }]} defaultValue={'all'}
                           onChange={e => setShowTesting(e.target.value)} />

              {/* 专属范围 */}
              <Radio.Group optionType="button" size={'small'}
                           options={[{ label: '全部', value: 'all' },
                             { label: '商家专属', value: 'MERCHANT' },
                             { label: '运营专属', value: 'OPERATOR' },
                             { label: '管理员专属', value: 'ADMIN' }]}
                           defaultValue={'all'}
                           onChange={e => setPrivatelyOpen2UserRoleType(e.target.value)} />

              {/* 换脸类型 */}
              <Radio.Group optionType="button" size={'small'}
                           options={[{ label: '全部', value: 'all' }, { label: '换脸', value: 'IMAGE' }, {
                             label: 'LoRA', value: 'LORA',
                           }]} defaultValue={'all'} onChange={e => setShowSwapType(e.target.value)} />

              {/* 年龄区段 */}
              <Radio.Group options={AgeRanges}
                           defaultValue={'ALL'} optionType="button" onChange={e => setSearchAgeRange(e.target.value)} />


              {/* 基础类型 */}
              <Radio.Group options={[{ label: '全部', value: 'all' }, ...filterBaseType(faceConfig)]} size={'small'}
                           defaultValue={'all'} optionType="button" onChange={e => handleChangeType(e)} />

              <Radio.Group options={[{ label: '全部', value: 'all' }, ...getTypeItemsByTags(['special'])]} size={'small'}
                           defaultValue={'all'} optionType="button" onChange={e => setSelectSpecialType(e.target.value)} />
            </Flex>
            <Button className="models-image-card-button" icon={<PlusOutlined />} size={'small'} onClick={handleAdd}
                    type={'primary'}>新增模特</Button>

          </Flex>

          <Flex align={'center'} justify={'flex-start'} gap={8}>

            <Radio.Group options={[{ label: '全部', value: 'all' }, ...getStyleTypeItems()]} size={'small'}
                         defaultValue={'all'} optionType="button" onChange={e => setSelectStyleType(e.target.value)} />

            <Radio.Group options={[{ label: '全部', value: 'all' }, ...getTypeItemsByTags(['nation'])]} size={'small'}
                         defaultValue={'all'} optionType="button" onChange={e => setSelectNationType(e.target.value)} />

            <Button className="models-image-card-button" icon={<EditOutlined />} size={'small'}
                    onClick={gotoEditFaceTag}>编辑标签</Button>

          </Flex>

          <Flex align={'center'} justify={'flex-start'} gap={8}>

            <InputNumber placeholder={'ID检索'} onChange={(e) => debouncedSearchId(e ? Number(e) : null)}
                         style={{ width: 80 }} />

            <Select options={masterOptions} style={{ width: 160 }} showSearch allowClear
                    placeholder={'选择创建者'}
                    optionFilterProp="label" defaultActiveFirstOption={true}
                    value={searchUserId}
                    onChange={(e) => setSearchUserId(e)} />

            <Select options={masterOptions} style={{ width: 160 }} showSearch allowClear
                    placeholder={'选择专属者'}
                    optionFilterProp="label" defaultActiveFirstOption={true}
                    value={searchPrivateOpen2UserId}
                    onChange={(e) => setSearchPrivateOpen2UserId(e)} />

            <Input placeholder={'模特名称模糊'} allowClear onChange={(e) => debouncedSearchKey(e.target.value)} style={{ width: 150 }} />

            <Checkbox style={{ fontSize: 12 }} onChange={e => {
              setShowCustomerOnly(e.target.checked);
            }}>只看客户上传</Checkbox>

            <Checkbox style={{ fontSize: 12 }} checked={onlyNearingDelivery} onChange={e => {
              setOnlyNearingDelivery(e.target.checked);
            }}>20小时未交付</Checkbox>

            <Checkbox style={{ fontSize: 12 }} checked={onlyExperimental} onChange={e => {
              setOnlyExperimental(e.target.checked);
            }}>只看实验</Checkbox>

          </Flex>
        </Flex>

        <Flex gap={8} style={{ flexWrap: 'wrap' }}>
          {datas.map(item => (
            <ImageCard {...item} key={item.id} />
          ))}
        </Flex>

        <div className={'stick-bottom-pagination'}>
          <Pagination
            current={page}
            pageSize={pageSize}
            total={total}
            onChange={handlePageChange}
            showTotal={(total) => `共 ${total} 个模特`}
            showSizeChanger // 允许用户更改每页显示条数
            pageSizeOptions={[70, 140, 210]}
            showQuickJumper // 允许用户快速跳转到某一页
            style={{ marginTop: '16px', textAlign: 'center' }}
          />
        </div>
      </Flex>

      {showDialog &&
        <Modal open={showDialog} centered mask={true} closable={false} width={'auto'} keyboard={true}
               styles={{
                 footer: { display: 'flex', flexDirection: 'row', justifyContent: 'center' },
               }} maskClosable={false}
               onCancel={() => setShowDialog(false)} onOk={form.submit}>

          <Form className={'face-dialog-block'}
                labelCol={{ span: 2 }}
                wrapperCol={{ span: 22 }}
                initialValues={{ remember: true }}
                onFinish={handleCommit}
                autoComplete="off"
                form={form}
                onValuesChange={(changedValues) => {
                  if (changedValues['extInfo.swapType'] !== undefined) {
                    setFaceSwapType(changedValues['extInfo.swapType']);
                  }
                  if (changedValues['extInfo.loraSwapFace'] !== undefined) {
                    setLoraSwapFace(changedValues['extInfo.loraSwapFace'] !== 'N');
                  }
                  if (changedValues['extInfo.repairFaceType'] !== undefined) {
                    setFaceRepairFaceType(changedValues['extInfo.repairFaceType']);
                  }
                  if (changedValues['type'] !== undefined) {
                    setGenderType(changedValues['type']);
                    form.setFieldValue('styleType', []);
                  }
                }}
          >
            <Form.Item hidden label="id" name="id">
              <Input />
            </Form.Item>
            <Form.Item label="模特名称" name="name" style={{ width: '100%' }}
                       rules={[{ required: true, message: '请输入正确的模特名称' },
                         {
                           pattern: /^[\u4e00-\u9fa5a-zA-Z0-9-_]+$/,
                           message: '名称只能包含中文、英文、数字、下划线或中划线!',
                         }]}>
              <Input placeholder={'请输入模特名称'} style={{ width: 600 }} />
            </Form.Item>
            <Form.Item hidden label="模特图片地址" name="extInfo.faceImage">
              <Input />
            </Form.Item>
            <Flex style={{ width: '100%' }} align={'flex-start'} justify={'flex-start'}>
              <Form.Item label="换脸类型" name="extInfo.swapType" style={{ width: '25%' }} labelCol={{ span: 8 }}
                         rules={[{ required: true, message: '请选择换脸类型' }]} initialValue={'IMAGE'}>
                <Radio.Group options={[{ label: '图片换脸', value: 'IMAGE' }, { label: '模特lora', value: 'LORA' }]}
                             optionType="button" buttonStyle="solid" />
              </Form.Item>
              <Form.Item label="修脸类型" name="extInfo.repairFaceType" style={{ width: '30%' }} labelCol={{ span: 9 }}
                         rules={[{ required: true, message: '请选择换脸类型' }]} initialValue={'PW'}>
                <Radio.Group
                  options={[
                    // { label: '不修脸', value: 'N' },
                    { label: 'flux', value: 'v_2' },
                    { label: 'PW', value: 'PW', },
                    // { label: 'sd1.5', value: 'v_1' }
                  ]}
                  optionType="button" buttonStyle="solid" />
              </Form.Item>
              <Form.Item label="修脸强度" name="extInfo.faceAfterStrength" style={{ width: '15%' }}
                         labelCol={{ span: 10 }} initialValue={1}
                         rules={[{ required: false, message: '请选择修脸强度' }]} hidden={faceRepairFaceType === 'v_1'}>
                <InputNumber max={10} style={{ width: 160 }} placeholder={'不填,同模特lora强度'} />
              </Form.Item>
            </Flex>
            <Flex justify={'flex-start'} style={{ width: '100%' }}>
              <Form.Item
                label="模特展示图片"
                name="showImage"
                rules={[{ required: true, message: '请上传模特图片' }]}
                style={{ width: '20%' }}
                labelCol={{ span: 10 }}
              >
                <Upload
                  action={UPLOAD_PRO_URL}
                  listType="picture-card"
                  fileList={fileList}
                  onPreview={handlePreview}
                  onChange={handleChange}
                >
                  {fileList.length >= 1 ? null : uploadButton}
                </Upload>
              </Form.Item>
              <Flex justify={'flex-start'} style={{ width: '80%' }}>
                <Flex vertical style={{ width: '60%' }} align={'flex-start'}
                      hidden={faceSwapType !== 'LORA'}>
                  <Form.Item
                    label="模特lora地址"
                    name="extInfo.faceLora"
                    rules={[{ required: faceSwapType === 'LORA', message: '请输入模特lora地址' }]}
                    style={{ width: '100%' }}
                    labelCol={{ span: 4 }}
                    hidden={faceSwapType !== 'LORA'}
                  >
                    <Input maxLength={256}
                           placeholder={'请输入模特lora的地址，如：0529/luwei/retrain/jeans2/lv2jeans2simple.safetensors'} />
                  </Form.Item>
                  <Form.Item
                    label="模特lora强度"
                    name="extInfo.faceLoraStrength"
                    initialValue={0.5}
                    rules={[{ required: faceSwapType === 'LORA', message: '请输入模特lora强度' }]}
                    style={{ width: '100%', marginTop: -16 }}
                    labelCol={{ span: 4 }}
                    hidden={faceSwapType !== 'LORA'}
                  >
                    <InputNumber max={10} style={{ width: 100 }} />
                  </Form.Item>
                  <Flex className={'width-100'}>
                    <Form.Item label="是否需要换脸" name="extInfo.loraSwapFace"
                               style={{ width: '50%', marginTop: -16 }}
                               labelCol={{ span: 8 }}
                               rules={[{ required: faceSwapType === 'LORA', message: '请选择是否需要换脸' }]}
                               initialValue={'Y'} hidden={faceSwapType !== 'LORA'}>
                      <Radio.Group
                        options={[{ label: '否', value: 'N' }, { label: '是', value: 'Y' }]}
                        optionType="button" buttonStyle="solid" />
                    </Form.Item>
                    <Form.Item label="先换脸再修脸" name="extInfo.repairAfterSwap"
                               style={{ width: '50%', marginTop: -16 }}
                               labelCol={{ span: 8 }}
                               rules={[{ required: loraSwapFace, message: '先换脸再修脸' }]}
                               initialValue={'Y'} hidden={faceSwapType !== 'IMAGE' && !loraSwapFace}>
                      <Radio.Group
                        options={[{ label: '否', value: 'N' }, { label: '是', value: 'Y' }]}
                        optionType="button" buttonStyle="solid" />
                    </Form.Item>
                  </Flex>

                </Flex>

                <Form.Item
                  label={selectOriginImgBtn(opItem)}
                  name="extInfo.actualSwapFaceImg"
                  rules={[{ required: loraSwapFace, message: '请上传模特图片' }]}
                  style={{ width: '50%' }}
                  labelCol={{ span: 6 }}
                  hidden={faceSwapType !== 'IMAGE' && !loraSwapFace}
                >
                  <Upload
                    action={UPLOAD_PRO_URL}
                    listType="picture-card"
                    fileList={actualImgFileList}
                    onPreview={handlePreview}
                    onChange={handleActualImgChange}
                    multiple={true}
                  >
                    {actualImgFileList.length >= 3 ? null : uploadButton}
                  </Upload>
                </Form.Item>
              </Flex>
            </Flex>

            <Flex justify={'flex-start'} style={{ width: '100%' }}>
              <Form.Item
                label="加雀斑"
                name="extInfo.addFaceDetails"
                rules={[{ required: false, message: '请选择是否加雀斑' }]}
                style={{ width: '20%' }}
                labelCol={{ span: 6 }}
                valuePropName={'checked'}
              >
                <Checkbox><div className={'text12'}>只有当前流程是instantId时才生效</div></Checkbox>
              </Form.Item>

              <Form.Item
                label="新换脸V2"
                name="extInfo.instantIdV2"
                rules={[{ required: true, message: '请选择是否新换脸' }]}
                style={{ width: '20%' }}
                labelCol={{ span: 9 }}
                initialValue={'N'}
              >
                <Radio.Group options={[
                  { label: '否', value: 'N' },
                  { label: '是', value: 'Y' },
                ]} optionType="button" />
              </Form.Item>
            </Flex>

            <Form.Item
              label="轮播图"
              name="extInfo.showImgs"
              style={{ width: '100%' }}
              help={<span style={{ color: 'red', fontSize: 10 }}>说明：建议选择比例一致的长方形图片</span>}
            >
              <ElementImagesSelector
                elementId={form.getFieldValue('id')}
                maxChoose={10}
                styleImageList={[]} />
            </Form.Item>

            {/*临时将换脸功能下线*/}
            {/*<Form.Item label="换脸模型" name="extInfo.swapModelType"*/}
            {/*					 style={{width: '100%'}}*/}
            {/*					 rules={[{required: loraSwapFace, message: '换脸模型'}]}*/}
            {/*					 hidden={faceSwapType !== 'IMAGE' && !loraSwapFace}*/}
            {/*					 initialValue={'reactor'}>*/}
            {/*	<Radio.Group*/}
            {/*		options={[{label: 'Reactor(默认)', value: 'reactor'}, {label: 'InstantID', value: 'instantId'}]}*/}
            {/*		optionType="button" buttonStyle="solid"/>*/}
            {/*</Form.Item>*/}

            <Form.Item
              label="是否为上新状态"
              name="isNew"
              rules={[{ required: true, message: '请选择模特上新状态' }]}
              style={{ width: '100%' }}
            >
              <Radio.Group options={[
                { label: '是', value: true },
                { label: '否', value: false },
              ]} optionType="button" buttonStyle="solid" />
            </Form.Item>

            <Form.Item
              label="年龄区段"
              name="ageRange"
              rules={[{ required: true, message: '请选择年龄区段' }]}
              style={{ width: '100%' }}
            >
              <Select
                key={'checkedAgeRange'}
                placeholder="请选择（可多选）"
                style={{ width: 600 }}
                allowClear={true}
                onChange={value => {
                  setCheckedAgeRange(value);
                }}
              >
                {SELECT_AGE_RANGE_OPTIONS.map(group => (
                  <Select.OptGroup key={group.title} label={group.title}>
                    {group.options.map(option => (
                      <Select.Option key={option.value} value={option.value}>
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select.OptGroup>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              label="模特类型"
              name="type"
              rules={[{ required: true, message: '请选择正确的模特类型' }]}
              style={{ width: '100%' }}
            >
              <Radio.Group
                options={filterBaseType(faceConfig)}
                optionType="button" />
            </Form.Item>
            <Form.Item
              label="模特状态"
              name="status"
              rules={[{ required: true, message: '请选择正确的模特状态' }]}
              style={{ width: '100%' }}
            >
              <Radio.Group options={[
                { label: '仅供测试', value: 'TEST' },
                { label: '发布线上', value: 'PROD' },
              ]} optionType="button" buttonStyle="solid" />
            </Form.Item>

            <Form.Item label="风格标签"
                       name="styleType"
                       style={{ width: '100%' }}
                       rules={[{ required: false, message: '请选择风格标签' }]}>

              <Select
                key={'checkedStyleTypes'}
                mode="tags"
                placeholder="请选择（可多选）"
                value={checkedStyleTypes}
                style={{ width: 600 }}
                allowClear={true}
                onChange={value => setCheckedStyleTypes(value)}
              >
                {getTypeItemsByTags(['style', genderType==='male-model'?'male':'female']).map(s => (
                  <Select.Option key={s.value} value={s.value}>
                    {s.label}
                  </Select.Option>
                ))}
              </Select>

            </Form.Item>

            <Flex className={'width-100'} justify={'flex-start'}>
              <Form.Item label="国别标签"
                         name="nationType"
                         labelCol={{ span: 8 }}
                         style={{ width: '25%' }}
                         rules={[{ required: true, message: '请选择国别标签' }]}>

                <Select
                  key={'checkedNationTypes'}
                  mode="tags"
                  maxCount={1}
                  placeholder="请选择（单选）"
                  value={checkedNationTypes}
                  style={{ width: 200 }}
                  allowClear={true}
                  onChange={value => setCheckedNationTypes(value)}
                >
                  {getTypeItemsByTags(['nation']).map(s => (
                    <Select.Option key={s.value} value={s.value}>
                      {s.label}
                    </Select.Option>
                  ))}
                </Select>

              </Form.Item>

              <Form.Item label="特殊标签"
                                     name="specialType"
                                     labelCol={{ span: 2 }}
                                     style={{ width: '75%' }}
                                     rules={[{ required: false, message: '请选择特殊标签' }]}>

                <Select
                  key={'checkedSpecialTypes'}
                  mode="tags"
                  placeholder="请选择（可多选，待废弃）"
                  value={checkedSpecialTypes}
                  style={{ width: 400 }}
                  allowClear={true}
                  onChange={value => setCheckedSpecialTypes(value)}
                >
                  {getTypeItemsByTags(['special']).map(s => (
                    <Select.Option key={s.value} value={s.value}>
                      {s.label}
                    </Select.Option>
                  ))}
                </Select>

              </Form.Item>

            </Flex>


            <ChildrenTabCard children={form.getFieldValue('children')} opItemId={opItem?.id}
                             allTabList={AllFaceClothTypes} form={form}
                             onChangeChildren={children => form.setFieldValue('children', children)}
                             formItems={(name, restField) => (<>
                               <Form.Item
                                 {...restField}
                                 label="特征替换词"
                                 name={[name, 'extInfo.features']}
                                 rules={[{ required: checkedAgeRange!=='adult', message: '请输入正确的模特替换词' }]}
                                 style={{ width: '100%' }}
                               >
                                 <InputWithTags tagsType={TagType.FACIAL_FEATURES} rows={1}
                                                placeholder={'将被用于替换服装、场景中的模特特征，不要出现单/双引号，一般用于童模'} />
                               </Form.Item>
                               <div style={{ color: 'red', marginTop: -20, marginLeft: 100 }}>
                                 特征文案中不要带以下关键字：{modelReplaceKey.replaceAll(',', '、')}
                               </div>
                               {/*<Form.Item*/}
                               {/*  {...restField}*/}
                               {/*  label="印花上身描述"*/}
                               {/*  name={[name, 'extInfo.logoModelDesc']}*/}
                               {/*  rules={[{ required: false, message: '请输入描述' }]}*/}
                               {/*  style={{ width: '100%' }}*/}
                               {/*>*/}
                               {/*  <InputWithTags tagsType={TagType.LOGO_MODEL_DESC} rows={1}*/}
                               {/*                 placeholder={'用于印花上身时的模特特征描述，不要出现单/双引号'} />*/}
                               {/*</Form.Item>*/}
                               <Form.Item
                                 {...restField}
                                 label="模特形象"
                                 name={[name, 'tags']}
                                 rules={[{ required: true, message: '请输入正确的模特形象' }]}
                                 style={{ width: '100%' }}
                               >
                                 <InputWithTags tagsType={TagType.MODEL_DESC}
                                                placeholder={'什么人种的模特，如：白人，黑人，亚洲人'} />
                               </Form.Item>
                               <Form.Item
                                 {...restField}
                                 label="年龄描述"
                                 name={[name, 'extInfo.ageDesc']}
                                 rules={[{ required: true, message: '请输入正确的年龄描述' }]}
                                 style={{ width: '100%' }}
                               >
                                 <InputWithTags rows={1} placeholder={'如：20-year-old'} tagsType={TagType.FACE_AGE_DESC} />
                               </Form.Item>
                               <Form.Item
                                 {...restField}
                                 label="面部表情"
                                 name={[name, 'extInfo.expression']}
                                 rules={[{ required: false, message: '请输入正确的面部表情' }]}
                                 style={{ width: '100%' }}
                               >
                                 <InputWithTags tagsType={TagType.FACIAL_EXPRESSION}
                                                placeholder={'请输入模特动作，如：笑，大笑，平静等'} />
                               </Form.Item>
                               <Form.Item
                                 {...restField}
                                 label="模特发型"
                                 name={[name, 'extInfo.hairstyle']}
                                 rules={[{ required: false, message: '请输入正确的模特发型' }]}
                                 style={{ width: '100%' }}
                               >
                                 <InputWithTags tagsType={TagType.HAIR}
                                                placeholder={'请输入模特发型'} />

                               </Form.Item>
                               <Form.Item
                                 {...restField}
                                 label="脸部修复"
                                 name={[name, 'extTags']}
                                 rules={[{ required: false, message: '请输入正确的脸部修复' }]}
                                 style={{ width: '100%' }}
                               >
                                 <InputWithTags tagsType={TagType.FACE_FIX} rows={2}
                                                placeholder={'请输入脸部修复提示词，如：clear face, happy expression, looking at viewer, realistic, A young woman with soft, voluminous blonde hair and bright blue eyes. She has a radiant complexion, a subtle smile, and a friendly expression。'}
                                 />
                               </Form.Item>
                               <Form.Item
                                 {...restField}
                                 label="脸部修复cfg"
                                 name={[name, 'extInfo.faceCfg']}
                                 rules={[{ required: false, message: '请输入正确的负向提示词' }]}
                                 style={{ width: '100%' }}
                                 initialValue={1.5}
                               >
                                 <InputNumber placeholder={'请输入脸部修复cfg，默认1.5'} style={{ width: 600 }} />
                               </Form.Item>
                               {/*<Form.Item*/}
                               {/*  {...restField}*/}
                               {/*  label="负向提示词"*/}
                               {/*  name={[name, 'extInfo.negative']}*/}
                               {/*  rules={[{ required: false, message: '请输入正确的负向提示词' }]}*/}
                               {/*  style={{ width: '100%' }}*/}
                               {/*>*/}
                               {/*  <InputWithTags tagsType={TagType.NEGATIVE} rows={2}*/}
                               {/*                 placeholder={'请输入负向提示词'} />*/}
                               {/*</Form.Item>*/}

                             </>)}
            />

          </Form>
        </Modal>
      }

      {showTrainDetail && trainDetailId &&
        <LoraTrainDetail id={trainDetailId} onComplete={() => setShowTrainDetail(false)} />}

      {deleteId > 0 &&
        <Modal title="是否确认删除？" open={deleteId > 0} centered mask={true} closable={false} width={'300px'}
               styles={{
                 header: { display: 'flex', flexDirection: 'row', justifyContent: 'center' },
                 footer: { display: 'flex', flexDirection: 'row', justifyContent: 'center' },
               }} onCancel={() => setDeleteId(0)} onOk={() => commitDel()} maskClosable={false}>
          <div className={'row-center-block margin-top-12 margin-bottom-24'}
               style={{ color: 'red' }}>删除后将不能恢复，请谨慎操作！
          </div>
        </Modal>
      }
      {previewImage && (
        <Image
          wrapperStyle={{ display: 'none' }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            afterOpenChange: (visible) => !visible && setPreviewImage(''),
          }}
          src={previewImage}
        />
      )}

      {/*管理员：将模特和场景转移给客户*/}
      {assignElementConfig &&
        <AssignModel2User
          materialType={'face'}
          onCancel={() => {
            setAssignElementConfig(null);
          }} onOk={() => {
          setAssignElementConfig(null);
          fetchData();
        }}
          assignElementConfig={assignElementConfig}
        />
      }

      {uploadImageModel &&
        <UploadImageSelector modelId={uploadImageModel} onCancel={() => setUploadImageModel(null)}
                             onChange={(images) => handleChangeUploadImages(images)} maxChoose={3}
                             value={getUploadImageValue()} />
      }

    </PageContainer>
  );
};

export default Face;
