import React, { useEffect, useState } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Card,
  Table,
  Space,
  message,
  InputNumber,
  Modal,
  Typography,
  Row,
  Col,
  Pagination,
} from 'antd';
import type { SelectProps } from 'antd/es/select';
import type { ColumnsType } from 'antd/es/table';
import { queryMaterialModelList } from '@/services/MaterialModelController';
import { getElementConfig } from '@/services/ElementController';
import {
  TrainParam,
  TrainPlanForm,
  BatchParams,
  TrainPlanVO,
  createTrainPlan,
  queryAllTrainPlans,
} from '@/services/TrainPlanController';

export const creationSizeOptions: SelectProps['options'] = [
  { label: '768x1024', value: 'THREE_FOUR' },
  { label: '1024x1024', value: 'ONE_ONE' },
  { label: '1340x1785', value: 'THREE_FOUR_LG_N' },
  { label: '1536x1536', value: 'ONE_ONE_LG' },
  { label: '1080x1920', value: 'NINE_SIXTEEN_2K' },
  { label: '1152x1536', value: 'P_1152_1536' },
  { label: '1620x2100', value: 'P_1620_2100' },
  { label: '1200x600', value: 'P_1200_600' },
];

const TrainPlanPage: React.FC = () => {
  const [form] = Form.useForm();
  const [trainParams, setTrainParams] = useState<TrainParam[]>([]);
  const [isBatchModalVisible, setIsBatchModalVisible] = useState(false);
  const [batchForm] = Form.useForm();
  const [clothingOptions, setClothingOptions] = useState<SelectProps['options']>([]);
  const [faceModelOptions, setFaceModelOptions] = useState<SelectProps['options']>([]);
  const [sceneOptions, setSceneOptions] = useState<SelectProps['options']>([]);
  const [trainPlans, setTrainPlans] = useState<TrainPlanVO[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10; // 每页显示的记录数

  const trainResolutionOptions = [
    { label: '1024', value: '1024' },
    { label: '1024,1536', value: '1024,1536' },
    { label: '1536', value: '1536' },
    { label: '1024,1536,2048', value: '1024,1536,2048' },
  ];

  const contentStyleOptions = [
    { label: 'content', value: 'content' },
    { label: 'style', value: 'style' },
  ];

  useEffect(() => {
    queryMaterialModelList({ materialType: 'cloth' }).then(res => {
      if (res) {
        setClothingOptions(res.map(item => {
          return { label: item.name, value: item.id };
        }));
      }
    });

    getElementConfig('CREATE_IMAGE').then(res=>{
      if(!res) return;

      res.forEach(e=>{
        if (e.configKey === 'FACE'){
          setFaceModelOptions(e.children.map(item => {
            return { label: item.name, value: item.id };
          }))
        } else if (e.configKey === 'SCENE'){
          setSceneOptions(e.children.map(item => {
            return { label: item.name, value: item.id };
          }))
        }
      })
    })

    // 获取所有训练计划
    queryAllTrainPlans({}).then(res => {
      if (res) {
        setTrainPlans(res); // 设置训练计划列表
      }
    });
  }, []);

  const columns: ColumnsType<TrainParam> = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      render: (_, __, index) => index + 1,
    },
    {
      title: '训练分辨率',
      dataIndex: 'trainResolution',
      key: 'trainResolution',
      render: (_, record, index) => (
        <Select
          value={record.trainResolution}
          mode="multiple"
          options={trainResolutionOptions}
          onChange={(value) => handleParamChange(index, 'trainResolution', value)}
          style={{ width: '140px' }}
        />
      ),
    },
    {
      title: '学习内容',
      dataIndex: 'contentOrStyle',
      key: 'contentOrStyle',
      render: (_, record, index) => (
        <Select
          value={record.contentOrStyle}
          options={contentStyleOptions}
          onChange={(value) => handleParamChange(index, 'contentOrStyle', value)}
          style={{ width: '100px' }}
        />
      ),
    },
    {
      title: 'Rank',
      dataIndex: 'loraRank',
      key: 'loraRank',
      render: (_, record, index) => (
        <InputNumber
          value={record.loraRank}
          onChange={(value) => handleParamChange(index, 'loraRank', value)}
          min={1}
          style={{ width: '60px' }}
        />
      ),
    },
    {
      title: 'Alpha',
      dataIndex: 'alpha',
      key: 'alpha',
      render: (_, record, index) => (
        <InputNumber
          value={record.alpha}
          onChange={(value) => handleParamChange(index, 'alpha', value)}
          min={1}
          precision={0}
          style={{ width: '60px' }}
        />
      ),
    },
    {
      title: '训练步数',
      dataIndex: 'trainStep',
      key: 'trainStep',
      render: (_, record, index) => (
        <InputNumber
          value={record.trainStep}
          onChange={(value) => handleParamChange(index, 'trainStep', value)}
          min={1}
          step={100}
          style={{ width: '80px' }}
        />
      ),
    },
    {
      title: '学习率',
      dataIndex: 'lr',
      key: 'lr',
      render: (_, record, index) => (
        <InputNumber
          value={record.lr}
          onChange={(value) => handleParamChange(index, 'lr', value)}
          min={0}
          step={0.0001}
          style={{ width: '100px' }}
        />
      ),
    },
    {
      title: 'Dropout',
      dataIndex: 'dropout',
      key: 'dropout',
      render: (_, record, index) => (
        <InputNumber
          value={record.dropout}
          onChange={(value) => handleParamChange(index, 'dropout', value)}
          min={0}
          max={1}
          step={0.1}
          style={{ width: '60px' }}
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: any, index) => (
        <Space>
          <Button type="link" onClick={() => handleDeleteRow(index)}>
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleParamChange = (index: number, field: keyof TrainParam, value: any) => {
    const newParams = [...trainParams];
    newParams[index] = { ...newParams[index], [field]: value };
    setTrainParams(newParams);
  };

  const handleAddRow = () => {
    setTrainParams([...trainParams, {
      key: Date.now().toString(),
      trainResolution: '1024',
      contentOrStyle: 'content',
      loraRank: 32,
      alpha: 16,
      trainStep: 2500,
      lr: 0.00020,
      dropout: 0.20,
    }]);
  };

  const handleDeleteRow = (index: number) => {
    const newParams = trainParams.filter((_, i) => i !== index);
    setTrainParams(newParams);
  };

  const handleSubmit = async (values: Omit<TrainPlanForm, 'trainParams'>) => {
    const submitData = {
      ...values,
      trainParams,
    };

    console.log('提交的数据:', submitData);
    let res = await createTrainPlan(submitData);
    if (res) {
      message.success('训练计划创建成功');
      setTimeout(() => {
        window.location.reload(); // 刷新页面
      }, 500);
    } else {
      message.error('创建失败');
    }
  };

  // 生成笛卡尔积的辅助函数
  const cartesianProduct = (...arrays: any[]): any[] => {
    return arrays.reduce((acc, curr) =>
        acc.flatMap(x => curr.map(y => [...x, y])),
      [[]],
    );
  };

  // 处理批量生成
  const handleBatchGenerate = (values: BatchParams) => {
    const {
      trainResolutions,
      contentOrStyles,
      ranks,
      alphas,
      trainSteps,
      lrs,
      dropouts,
    } = values;

    const combinations = cartesianProduct(
      trainResolutions,
      contentOrStyles,
      ranks,
      alphas,
      trainSteps,
      lrs,
      dropouts,
    );

    const newParams = combinations.map((combo: any[], index: number) => ({
      key: Date.now().toString() + Math.random(),
      trainResolution: combo[0],
      contentOrStyle: combo[1],
      loraRank: combo[2],
      alpha: combo[3],
      trainStep: combo[4],
      lr: combo[5],
      dropout: combo[6],
    }));

    setTrainParams(newParams);
    setIsBatchModalVisible(false);
    batchForm.resetFields();
  };

  const handleBatchModalOpen = () => {
    batchForm.setFieldsValue({
      trainResolutions: ['1024'], // 默认值
      contentOrStyles: ['content'], // 默认值
      ranks: [32], // 默认值
      alphas: [16], // 默认值
      trainSteps: [2500], // 默认值
      lrs: [0.00020], // 默认值
      dropouts: [0.20], // 默认值
    });
    setIsBatchModalVisible(true);
  };

  // 在Card的extra部分添加批量生成按钮
  const cardExtra = (
    <Space>
      <Button onClick={handleBatchModalOpen}>批量生成</Button>
      <Button type="primary" onClick={handleAddRow}>添加参数组合</Button>
    </Space>
  );

  const copyParamsToClipboard = (trainParams) => {
    const markdown = trainParams.map((param, index) =>
      `| ${index + 1} | ${param.relatedLoraModelId} | ${param.relatedLoraModelName} | ${param.trainResolution} | ${param.contentOrStyle} | ${param.loraRank} | ${param.alpha} | ${param.trainStep} | ${param.lr} | ${param.dropout} |`,
    ).join('\n');

    const header = `| 序号 | 关联模型ID | 关联模型名称 | 训练分辨率 | 学习内容 | Rank | Alpha | 训练步数 | 学习率 | Dropout |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n`;
    const fullMarkdown = header + markdown;

    navigator.clipboard.writeText(fullMarkdown)
      .then(() => message.success('参数组合已复制到剪贴板'))
      .catch(err => message.error('复制失败'));
  };

  // 计算当前页的数据
  const paginatedData = trainPlans.slice((currentPage - 1) * pageSize, currentPage * pageSize);

  // 处理页码变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <div style={{ padding: '24px' }}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{}}
      >
        <Card title="基本信息" style={{ marginBottom: '24px' }}>
          <Form.Item
            label="训练计划名称"
            name="planName"
            help={form.getFieldError('planName')?.[0]}
            rules={[
              { required: true, message: '请输入计划名称' },
              {
                validator: (_, value) => {
                  if (!value) {
                    return Promise.reject();
                  }
                  if (trainPlans.some(plan => plan.planName === value)) {
                    return Promise.reject(new Error('训练计划名称已经存在，不可重复'));
                  }
                  return Promise.resolve(); // 确保合法且不重复时返回成功
                },
              },
            ]}
          >
            <Input
              placeholder="请输入计划名称"
              onChange={(e) => {
                const value = e.target.value;
                form.setFieldsValue({ planName: value });
              }}
            />
          </Form.Item>

          <Form.Item
            label="待克隆服装"
            name="clothingId"
            rules={[{ required: true, message: '请选择待克隆服装' }]}
          >
            <Select options={clothingOptions} placeholder="请选择服装" />
          </Form.Item>

          <Form.Item
            label="备注"
            name="remarks"
          >
            <Input.TextArea placeholder="请输入备注" rows={2} />
          </Form.Item>
        </Card>

        <Card
          title="训练参数组合"
          style={{ marginBottom: '24px' }}
          extra={cardExtra}
        >
          <Table
            columns={columns}
            dataSource={trainParams}
            pagination={false}
            scroll={{ x: 'max-content' }}
          />
        </Card>

        <Modal
          title="批量生成参数组合"
          open={isBatchModalVisible}
          onOk={() => batchForm.submit()}
          onCancel={() => setIsBatchModalVisible(false)}
          width={800}
          centered
        >
          <Form
            form={batchForm}
            onFinish={handleBatchGenerate}
            layout="vertical"
            style={{ margin: 0 }}
          >
            <Form.Item
              label="训练分辨率"
              name="trainResolutions"
              rules={[{ required: true, message: '请选择训练分辨率' }]}
              style={{ marginBottom: 8 }}
            >
              <Select
                mode="multiple"
                options={trainResolutionOptions}
                placeholder="请选择训练分辨率"
              />
            </Form.Item>

            <Form.Item
              label="学习内容"
              name="contentOrStyles"
              rules={[{ required: true, message: '请选择学习内容' }]}
              style={{ marginBottom: 8 }}
            >
              <Select
                mode="multiple"
                options={contentStyleOptions}
                placeholder="请选择学习内容"
              />
            </Form.Item>

            <Form.Item
              label="Rank值列表"
              name="ranks"
              rules={[{ required: true, message: '请输入Rank值' }]}
              tooltip="请输入正整数，用逗号分隔"
            >
              <Select
                mode="tags"
                placeholder="例如: 4,8,16"
                tokenSeparators={[',']}
                onChange={(values) => {
                  const numbers = values.map(v => Number(v))
                    .filter(n => !isNaN(n) && n > 0 && Number.isInteger(n));
                  batchForm.setFieldsValue({ ranks: numbers });
                }}
              />
            </Form.Item>

            <Form.Item
              label="Alpha值列表"
              name="alphas"
              rules={[{ required: true, message: '请输入Alpha值' }]}
              tooltip="请输入正数，用逗号分隔"
            >
              <Select
                mode="tags"
                placeholder="例如: 1,2,4"
                tokenSeparators={[',']}
                onChange={(values) => {
                  const numbers = values.map(v => Number(v))
                    .filter(n => !isNaN(n) && n > 0 && Number.isInteger(n));
                  batchForm.setFieldsValue({ alphas: numbers });
                }}
              />
            </Form.Item>

            <Form.Item
              label="训练步数列表"
              name="trainSteps"
              rules={[{ required: true, message: '请输入训练步数' }]}
              tooltip="输入正整数，用逗号分隔"
            >
              <Select
                mode="tags"
                placeholder="例如: 1000,2000,3000"
                tokenSeparators={[',']}
                onChange={(values) => {
                  const numbers = values.map(v => Number(v))
                    .filter(n => !isNaN(n) && n > 0 && Number.isInteger(n));
                  batchForm.setFieldsValue({ trainSteps: numbers });
                }}
              />
            </Form.Item>

            <Form.Item
              label="学习率列表"
              name="lrs"
              rules={[{ required: true, message: '请输入学习率' }]}
              tooltip="请输入大于0的数字，用逗号分隔"
            >
              <Select
                mode="tags"
                placeholder="例如: 0.001,0.0001,0.00001"
                tokenSeparators={[',']}
                onChange={(values) => {
                  const numbers = values.map(v => Number(v))
                    .filter(n => !isNaN(n) && n > 0);
                  batchForm.setFieldsValue({ lrs: numbers });
                }}
              />
            </Form.Item>

            <Form.Item
              label="Dropout列表"
              name="dropouts"
              rules={[{ required: true, message: '请输入Dropout值' }]}
              tooltip="请输入0-1之间的数字，用逗号分隔"
            >
              <Select
                mode="tags"
                placeholder="例如: 0.1,0.2,0.3"
                tokenSeparators={[',']}
                onChange={(values) => {
                  const numbers = values.map(v => Number(v))
                    .filter(n => !isNaN(n) && n >= 0 && n <= 1);
                  batchForm.setFieldsValue({ dropouts: numbers });
                }}
              />
            </Form.Item>

            <Form.Item>
              <Typography.Text type="secondary" style={{ margin: 0 }}>
                所有参数将进行笛卡尔积组合请注意组合数量。建议每个参数选择2-3个值，避免组合过多。
              </Typography.Text>
            </Form.Item>
          </Form>
        </Modal>

        <Card title="出图参数">
          <Form.Item
            label="模特选择"
            name="faceModels"
          >
            <Select
              mode="multiple"
              options={faceModelOptions}
              placeholder="请选择模特"
              showSearch
              filterOption={(input, option) =>
                typeof option?.label === 'string' && option.label.toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>

          <Form.Item
            label="场景选择"
            name="scenes"
          >
            <Select
              mode="multiple"
              options={sceneOptions}
              placeholder="请选择场景"
              showSearch
              filterOption={(input, option) =>
                typeof option?.label === 'string' && option.label.toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>

          <Form.Item
            label="出图尺寸"
            name="sizes"
          >
            <Select
              mode="multiple"
              options={creationSizeOptions}
              placeholder="选择出图尺寸"
            />
          </Form.Item>

          <Form.Item
            label="每组图片数量"
            name="imagesPerCombination"
            rules={[{ required: true, message: '请输入每组图片数量' }]}
          >
            <InputNumber min={1} style={{ width: '100%' }} />
          </Form.Item>
        </Card>

        <Form.Item>
          <Row justify="end">
            <Col>
              <Button type="primary" htmlType="submit" style={{ marginTop: '20px' }}>
                提交训练计划
              </Button>
            </Col>
          </Row>
        </Form.Item>

        <Card title="训练计划列表" style={{ marginTop: '24px' }}>
          <Table
            columns={[
              { title: 'ID', dataIndex: 'id', key: 'id' },
              { title: '计划名称', dataIndex: 'planName', key: 'planName' },
              { title: '服装', dataIndex: 'clothingName', key: 'clothingName' },
              { title: '服装ID', dataIndex: 'clothingId', key: 'clothingId' },
              {
                title: (
                  <div style={{ textAlign: 'center' }}>训练参数</div>
                ),
                dataIndex: 'trainParams',
                key: 'trainParams',
                render: (params) => (
                  <Table
                    dataSource={params}
                    pagination={false}
                    rowKey="id" // 假设每个训练参数都有唯一的 id
                    columns={[
                      { title: '序号', dataIndex: 'index', key: 'index', render: (_, __, index) => index + 1 },
                      { title: '关联模型', dataIndex: 'relatedLoraModelName', key: 'relatedLoraModelName' },
                      { title: '关联模型ID', dataIndex: 'relatedLoraModelId', key: 'relatedLoraModelId' },
                      { title: '训练分辨率', dataIndex: 'trainResolution', key: 'trainResolution' },
                      { title: '学习内容', dataIndex: 'contentOrStyle', key: 'contentOrStyle' },
                      { title: 'Rank', dataIndex: 'loraRank', key: 'rank' },
                      { title: 'Alpha', dataIndex: 'alpha', key: 'alpha' },
                      { title: '训练步数', dataIndex: 'trainStep', key: 'trainStep' },
                      { title: '学习率', dataIndex: 'lr', key: 'lr' },
                      { title: 'Dropout', dataIndex: 'dropout', key: 'dropout' },
                    ]}
                  />
                ),
              },
              { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
              {
                title: '出图模特',
                dataIndex: 'faceModels',
                key: 'faceModels',
                render: (faceModels) => {
                  if (!faceModels || faceModels.length === 0) return null; // 如果没有选择模特则不展示
                  return faceModels.map(id => {
                    const model = faceModelOptions?.find(option => option.value === id);
                    return model ? model.label : null; // 显示模特名称
                  }).filter(Boolean).join(', '); // 过滤掉 null 并用逗号连接
                },
              },
              {
                title: '出图场景',
                dataIndex: 'scenes',
                key: 'scenes',
                render: (scenes) => {
                  if (!scenes || scenes.length === 0) return null; // 如果没有选择场景则不展示
                  return scenes.map(id => {
                    const scene = sceneOptions?.find(option => option.value === id);
                    return scene ? scene.label : null; // 显示场景名称
                  }).filter(Boolean).join(', '); // 过滤掉 null 并用逗号连接
                },
              },
              {
                title: '出图尺寸',
                dataIndex: 'sizes',
                key: 'sizes',
                render: (sizes) => {
                  if (!sizes || sizes.length === 0) return null; // 如果没有选择出图尺寸则不展示
                  return sizes.map(size => {
                    const option = creationSizeOptions.find(option => option.value === size); // 查找对应的label
                    return option ? option.label : null; // 返回label或null
                  }).filter(Boolean).join(', '); // 过滤掉null并用逗号连接
                },
              },
              {
                title: '每组图片数量',
                dataIndex: 'imagesPerCombination', // 确保数据源中有这个字段
                key: 'imagesPerCombination',
                render: (value, record) => {
                  // 检查出图模特数量
                  const hasFaceModels = record.faceModels && record.faceModels.length > 0;
                  return hasFaceModels ? (value || 0) : null; // 如果有模特则显示数量，否则不展示
                },
              },
              {
                title: '操作',
                key: 'action',
                render: (_, record) => (
                  <Button type="link" onClick={() => copyParamsToClipboard(record.trainParams)}>
                    复制参数
                  </Button>
                ),
              },
            ]}
            dataSource={paginatedData} // 使用分页后的数据
            pagination={false} // 关闭 Table 自带的分页
            rowKey="id" // 假设每个训练计划都有唯一的 id
          />
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={trainPlans.length}
            onChange={handlePageChange}
            style={{ marginTop: '16px', textAlign: 'right' }} // 设置样式
          />
        </Card>
      </Form>
    </div>
  );
};

export default TrainPlanPage;