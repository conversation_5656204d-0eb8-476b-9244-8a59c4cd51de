import React, {useState, useEffect, useRef} from 'react';
import {Form, Button, Card, message, Table, Tag, Space, Flex, notification, Modal, Descriptions, Image, Upload, Radio, Popconfirm, Spin} from 'antd';
import { ReloadOutlined, EyeOutlined, EditOutlined, ToolOutlined, PictureOutlined } from '@ant-design/icons';
import moment from 'moment';
import {createTryonTask, queryTryonTask, queryTryonTasksByPage, createTryonRefinerTask, CreativeVO} from "@/services/CreativeController";
import { uploadFile } from "@/services/FileController";
import { ColumnsType } from 'antd/es/table';

// 接口定义
interface TryonTaskParams {
  topUrl: string;
  bottomUrl?: string;
  personImgUrl: string;
}

export interface CommonTaskVO {
  id: number;
  userId: number;
  operatorId: number;
  taskType: string;
  //INIT/PENDING/RUNNING/COMPLETED/FAILED/CANCELED
  taskStatus: string;
  outTaskPlatform: string;
  outTaskId: string;
  outTaskStatus: string;
  reqBizParams: string;
  relatedBizType: string;
  relatedBizId: string;
  extInfo: string;
  taskStartTime: string;
  taskEndTime: string;
  createTime: string;
  modifyTime: string;
  completeRequest: string;
  retDetail: string;
}

const TryonPage: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [taskList, setTaskList] = useState<CommonTaskVO[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const [resultImgUrl, setResultImgUrl] = useState('');
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // 添加预览相关的状态
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImages, setPreviewImages] = useState<{
    top?: string;
    bottom?: string;
    person?: string;
    tryonResult4Refiner?: string;
    result?: string;
  }>({});

  const [uploadPreviewVisible, setUploadPreviewVisible] = useState(false);
  const [uploadPreviewImages, setUploadPreviewImages] = useState<{
    top?: string;
    bottom?: string;
    person?: string;
    result?: string;
  }>({});

  const [isGenerating, setIsGenerating] = useState(false);

  const [refineVisible, setRefineVisible] = useState(false);
  const [gender, setGender] = useState<'man' | 'woman' | null>(null);

  // 新增 tryonType 状态
  const [tryonType, setTryonType] = useState<'aliyun' | 'comfyui'>('aliyun');

  // 提交表单
  const onFinish = async (values: TryonTaskParams) => {
    // 校验上装和下装的逻辑
    if (tryonType === 'comfyui') {
      const { topUrl, bottomUrl } = values;
      if (!topUrl && !bottomUrl) {
        message.error('请至少上传上装或下装图片');
        return;
      }
      if (topUrl && bottomUrl) {
        message.error('只能上传上装或下装图片，不能同时上传');
        return;
      }
    }

    const taskId = await createTryonTask({ ...values, tryonType });
    if (taskId) {
      setIsGenerating(true);
      message.success('提交成功');
      startPolling(taskId, tryonType === 'comfyui' ? 5000 : 3000);
      fetchTaskList();
    } else {
      message.error('提交失败');
    }
  };

  const startPolling = (taskId: number, interval=3000) => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    intervalRef.current = setInterval(async () => {
      let task = await queryTryonTask({id: taskId, tryonType});

      if (tryonType === 'aliyun') {

        if (task.taskStatus === 'COMPLETED') {
          if (task.taskType === 'TRY_ON' && task.retDetail) {
            setResultImgUrl(JSON.parse(task.retDetail)['image_url']);
          }
          clearInterval(intervalRef.current!);
          intervalRef.current = null;
          setIsGenerating(false);
          notification.success({ message: '生成成功', duration: 3 });

          fetchTaskList();

        } else if (task.taskStatus === 'INIT' || task.taskStatus === 'PENDING' || task.taskStatus === 'RUNNING') {
        } else {
          clearInterval(intervalRef.current!);
          intervalRef.current = null;
          setIsGenerating(false);
          message.error('生成失败');
        }

      } else if (tryonType === 'comfyui'){
        if (task.status === 'FINISHED') {
          if (task.resultImages && task.resultImages.length > 0) {
            setResultImgUrl(task.resultImages[0]);
          }
          clearInterval(intervalRef.current!);
          intervalRef.current = null;
          setIsGenerating(false);
          notification.success({ message: '生成成功', duration: 3 });

          fetchTaskList();

        } else if (task.processing) {
        } else {
          clearInterval(intervalRef.current!);
          intervalRef.current = null;
          setIsGenerating(false);
          message.error('生成失败');
        }
      }

    }, interval);
  };

  // 获取任务状态对应的标签颜色
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      INIT: 'default',
      PENDING: 'processing',
      RUNNING: 'processing',
      COMPLETED: 'success',
      FAILED: 'error',
      CANCELED: 'warning',
    };
    return colorMap[status] || 'default';
  };

  // 获取任务列表
  const fetchTaskList = async (page = pagination.current, pageSize = pagination.pageSize) => {
    setLoading(true);
    try {
      const pagedTasks = await queryTryonTasksByPage({
        pageNum: page,
        pageSize: pageSize,
        orderBy: 'id desc',
        tryonType,
      });

      // 根据 tryonType 选择不同的数据结构
      if (tryonType === 'comfyui') {
        // @ts-ignore
        setTaskList(pagedTasks?.list as CreativeVO[] || []);
      } else {
        setTaskList(pagedTasks?.list as CommonTaskVO[] || []);
      }

      setPagination({
        ...pagination,
        current: page,
        total: pagedTasks?.totalCount || 0,
      });
    } catch (error) {
      message.error('获取任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理表格分页变化
  const handleTableChange = (newPagination: any) => {
    fetchTaskList(newPagination.current, newPagination.pageSize);
  };

  // 初始加载获取数据
  useEffect(() => {

    fetchTaskList();

    handleClearImages();

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };

  }, [tryonType]);

  // 处理预览按钮点击
  const handlePreviewAll = (record: any) => {
    try {
      if (tryonType === 'aliyun') {
        const reqParams = JSON.parse(record.reqBizParams);
        const retDetail = JSON.parse(record.retDetail);

        console.log('handlePreviewAll,retDetail.image_url', retDetail.image_url);

        setPreviewImages({
          top: reqParams.params.topUrl,
          bottom: reqParams.params.bottomUrl,
          person: reqParams.params.personImgUrl,
          tryonResult4Refiner: reqParams.params?.tryonTaskResultImgUrl || '',
          result: retDetail.image_url || '',
        });

        //comfyui,CreativeVO
      } else {
        setPreviewImages({
          top: record.extInfo.topUrl,
          bottom: record.extInfo.bottomUrl,
          person: record.extInfo.personImgUrl,
          tryonResult4Refiner: '',
          result: record.resultImages?.[0] || '',
        });
      }
      setPreviewVisible(true);

    } catch (error) {
      message.error('解析图片数据失败');
    }
  };

  // 处理上传区域的预览
  const handleUploadPreview = () => {
    const formValues = form.getFieldsValue();
    setUploadPreviewImages({
      top: formValues.topUrl,
      bottom: formValues.bottomUrl,
      person: formValues.personImgUrl,
      result: resultImgUrl
    });
    setUploadPreviewVisible(true);
  };

  // 添加文件上传处理函数
  const handleFileUpload = async (file: File, fieldName: string) => {
    try {
      const url = await uploadFile(file, tryonType === 'comfyui');
      form.setFieldValue(fieldName, url);
    } catch (error) {
      message.error('上传失败');
    }
  };

  // 自定义上传组件
  const CustomUploader: React.FC<{
    value?: string;
    onChange?: (value: string) => void;
    title: string;
  }> = ({ value, onChange, title }) => (
    <div style={{
      width: '100%',
      height: 512,
      position: 'relative',
      border: '1px dashed #d9d9d9',
      borderRadius: 8,
      backgroundColor: '#fafafa',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      overflow: 'hidden'
    }}>
      {value ? (
        <Image
          src={value}
          alt={title}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain'
          }}
        />
      ) : (
        <>
          <div style={{ marginBottom: 8 }}>{title}</div>
          <Button>选择图片</Button>
        </>
      )}
      <input
        type="file"
        accept="image/*"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) {
            handleFileUpload(file, title === '参考图' ? 'personImgUrl' : title === '上装' ? 'topUrl' : 'bottomUrl');
          }
        }}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          opacity: 0,
          cursor: 'pointer'
        }}
      />
    </div>
  );

  // 处理精修请求
  const handleTaskRefine = async (task: CommonTaskVO) => {
    const refineTaskId = await createTryonRefinerTask({ tryonTaskId: task.id, gender });
    if (refineTaskId) {
      fetchTaskList();
      startPolling(refineTaskId, 5000);
    }
  };

  // 渲染任务列表的操作列
  const renderActionColumn = (record: any) => {

    //comfyui
    if (tryonType === 'comfyui') {
      const isCompleted = record.status === 'FINISHED';
      const isTryOnTask = record.type === 'TRYON';

      return (
        <div>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handlePreviewAll(record)}
            disabled={!isCompleted}
          >
            预览
          </Button>
        </div>
      );

      //aliyun
    } else {

      const isCompleted = record.taskStatus === 'COMPLETED';
      const isTryOnTask = record.taskType === 'TRY_ON';

      return (
        <div>
          {isTryOnTask && isCompleted && (
            <Popconfirm
              title="选择性别进行精修"
              description={
                <Radio.Group onChange={(e) => setGender(e.target.value)} value={gender}>
                  <Radio value="man">男性</Radio>
                  <Radio value="woman">女性</Radio>
                </Radio.Group>
              }
              onConfirm={() => handleTaskRefine(record)}
              okText="确认"
              cancelText="取消"
              okButtonProps={{
                disabled: !gender
              }}
            >
              <Button type="link" icon={<PictureOutlined />}>
                精修
              </Button>
            </Popconfirm>
          )}
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handlePreviewAll(record)}
            disabled={!isCompleted}
          >
            预览
          </Button>
        </div>
      );
    }

  };

  const aliyunTryonTaskColumns: ColumnsType<CommonTaskVO> = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
      key: 'taskType',
      width: 100,
      render: (type: string) => {
        const typeMap: Record<string, string> = {
          'TRY_ON': '试衣',
          'TRY_ON_REFINER': '精修'
        };
        return typeMap[type] || type;
      },
    },
    {
      title: '任务状态',
      dataIndex: 'taskStatus',
      key: 'taskStatus',
      width: 100,
      render: (status: string) => (
        status === 'RUNNING' ? (
          <Spin size="small" />
        ) : (
          <Tag color={getStatusColor(status)}>{status}</Tag>
        )
      ),
    },
    {
      title: '上装图片',
      dataIndex: 'reqBizParams',
      key: 'topUrl',
      width: 100,
      render: (text: string) => {
        try {
          const params = JSON.parse(text);
          const topUrl = params.params.topUrl;
          return topUrl ? (
            <Image
              src={topUrl}
              width={80}
              height={80}
              style={{ objectFit: 'cover' }}
            />
          ) : '-';
        } catch {
          return '-';
        }
      },
    },
    {
      title: '下装图片',
      dataIndex: 'reqBizParams',
      key: 'bottomUrl',
      width: 100,
      render: (text: string) => {
        try {
          const params = JSON.parse(text);
          const bottomUrl = params.params.bottomUrl;
          return bottomUrl ? (
            <Image
              src={bottomUrl}
              width={80}
              height={80}
              style={{ objectFit: 'cover' }}
            />
          ) : '-';
        } catch {
          return '-';
        }
      },
    },
    {
      title: '参考图',
      dataIndex: 'reqBizParams',
      key: 'personImgUrl',
      width: 100,
      render: (text: string) => {
        try {
          const params = JSON.parse(text);
          const personImgUrl = params.params.personImgUrl;
          return personImgUrl ? (
            <Image
              src={personImgUrl}
              width={80}
              height={80}
              style={{ objectFit: 'cover' }}
            />
          ) : '-';
        } catch {
          return '-';
        }
      },
    },
    {
      title: '原结果图',
      dataIndex: 'reqBizParams',
      key: 'originalResult',
      width: 100,
      render: (text: string, record: CommonTaskVO) => {
        if (record.taskType !== 'TRY_ON_REFINER') {
          return null;
        }
        try {
          const params = JSON.parse(text);
          const originalUrl = params.params.tryonTaskResultImgUrl;
          return originalUrl ? (
            <Image
              src={originalUrl}
              width={80}
              height={80}
              style={{ objectFit: 'cover' }}
            />
          ) : '-';
        } catch {
          return '-';
        }
      },
    },
    {
      title: '生成结果',
      dataIndex: 'retDetail',
      key: 'image_url',
      width: 100,
      render: (text: string) => {
        try {
          const detail = JSON.parse(text);
          return detail != null && detail.image_url ? (
            <Image
              src={detail.image_url}
              width={80}
              height={80}
              style={{ objectFit: 'cover' }}
            />
          ) : '-';
        } catch {
          return '-';
        }
      },
    },
    {
      title: '开始时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
      render: (time: string) => time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '结束时间',
      dataIndex: 'retDetail',
      key: 'end_time',
      width: 180,
      render: (text: string) => {
        try {
          const detail = JSON.parse(text);
          return detail.end_time ? detail.end_time.substring(0, 19) : '-';
        } catch {
          return '-';
        }
      },
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 100,
      render: (_, record: CommonTaskVO) => renderActionColumn(record), // 使用新的渲染函数
    },
  ];

  const comfyuiColumns: ColumnsType<CreativeVO> = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
      key: 'taskType',
      width: 100,
      render: (type: string) => {
        return '自研tryon';
      },
    },
    {
      title: '任务状态',
      key: 'taskStatus',
      width: 100,
      render: (_, record: any) => {
        let status = record.status;
        const statusMap: Record<string, string> = {
          'INIT': '生成中',
          'QUEUE': '生成中',
          'PROCESSING': '生成中',
          'FINISHED': '完成',
          'FAILED': '失败',
        };
        console.log('任务状态,status', status);
        return <Tag color={getStatusColor(status)}>{statusMap[status] || status}</Tag>;
      },
    },
    {
      title: '上装图片',
      dataIndex: 'extInfo',
      key: 'topUrl',
      width: 100,
      render: (extInfo: any) => {
        return extInfo?.topUrl ? (
          <Image
            src={extInfo.topUrl}
            width={80}
            height={80}
            style={{ objectFit: 'cover' }}
          />
        ) : '-';
      },
    },
    {
      title: '下装图片',
      dataIndex: 'extInfo',
      key: 'bottomUrl',
      width: 100,
      render: (extInfo: any) => {
        return extInfo?.bottomUrl ? (
          <Image
            src={extInfo.bottomUrl}
            width={80}
            height={80}
            style={{ objectFit: 'cover' }}
          />
        ) : '-';
      },
    },
    {
      title: '参考图',
      dataIndex: 'extInfo',
      key: 'personImgUrl',
      width: 100,
      render: (extInfo: any) => {
        return extInfo?.personImgUrl ? (
          <Image
            src={extInfo.personImgUrl}
            width={80}
            height={80}
            style={{ objectFit: 'cover' }}
          />
        ) : '-';
      },
    },
    {
      title: '生成结果',
      dataIndex: 'resultImages',
      key: 'image_url',
      width: 100,
      render: (resultImages: any[]) => {
        return resultImages && resultImages.length > 0 ? (
          <Image
            src={resultImages[0]}
            width={80}
            height={80}
            style={{ objectFit: 'cover' }}
          />
        ) : '-';
      },
    },
    {
      title: '开始时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
      render: (time: string) => time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '结束时间',
      key: 'end_time',
      width: 180,
      render: (_: string, record: any) => record.extInfo?.endTime || '',
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 100,
      render: (_, record: CreativeVO) => renderActionColumn(record), // 使用新的渲染函数
    },
  ];

  // 预览弹窗组件
  const PreviewModal: React.FC<{
    visible: boolean;
    onClose: () => void;
    images: {
      top?: string;
      bottom?: string;
      person?: string;
      tryonResult4Refiner?: string;
      result?: string;
    };
  }> = ({ visible, onClose, images }) => (
    <Modal
      title="图片预览"
      open={visible}
      onCancel={onClose}
      footer={null}
      width="95vw"
      style={{ 
        top: 10,
        paddingBottom: 0 
      }}
      bodyStyle={{ 
        height: 'calc(95vh - 55px)',
        overflow: 'auto',
        padding: '24px'
      }}
    >
      <Flex style={{ 
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        gap: '32px'
      }}>
        <div>
          <div style={{ 
            marginBottom: 12,
            fontSize: '16px',
            fontWeight: 500,
            textAlign: 'center'
          }}>
            参考图
          </div>
          <Image
            src={images.person}
            height="calc(95vh - 150px)"
            style={{ 
              objectFit: 'contain',
              backgroundColor: '#f5f5f5',
              borderRadius: '8px'
            }}
          />
        </div>
        
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '24px',
          height: '100%'
        }}>
          <div>
            <div style={{ 
              marginBottom: 12,
              fontSize: '16px',
              fontWeight: 500,
              textAlign: 'center'
            }}>
              上装图片
            </div>
            <Image
              src={images.top}
              height={images.bottom ? "calc((95vh - 200px)/2)" : "calc(95vh - 150px)"}
              style={{ 
                objectFit: 'contain',
                backgroundColor: '#f5f5f5',
                borderRadius: '8px'
              }}
            />
          </div>
          
          {images.bottom && (
            <div>
              <div style={{ 
                marginBottom: 12,
                fontSize: '16px',
                fontWeight: 500,
                textAlign: 'center'
              }}>
                下装图片
              </div>
              <Image
                src={images.bottom}
                height="calc((95vh - 200px)/2)"
                style={{ 
                  objectFit: 'contain',
                  backgroundColor: '#f5f5f5',
                  borderRadius: '8px'
                }}
              />
            </div>
          )}
        </div>

        {images.tryonResult4Refiner && (
          <div>
            <div style={{
              marginBottom: 12,
              fontSize: '16px',
              fontWeight: 500,
              textAlign: 'center'
            }}>
              try-on结果
            </div>
            <Image
              src={images.tryonResult4Refiner}
              height="calc(95vh - 150px)"
              style={{
                objectFit: 'contain',
                backgroundColor: '#f5f5f5',
                borderRadius: '8px'
              }}
            />
          </div>
        )}
        
        <div>
          <div style={{ 
            marginBottom: 12,
            fontSize: '16px',
            fontWeight: 500,
            textAlign: 'center'
          }}>
            {images.tryonResult4Refiner ? '精修图结果' : '生成结果'}
          </div>
          <Image
            src={images.result}
            height="calc(95vh - 150px)"
            style={{ 
              objectFit: 'contain',
              backgroundColor: '#f5f5f5',
              borderRadius: '8px'
            }}
          />
        </div>

      </Flex>
    </Modal>
  );

  // 添加清除图片的处理函数
  const handleClearImages = () => {
    form.setFieldsValue({
      personImgUrl: undefined,
      topUrl: undefined,
      bottomUrl: undefined
    });
    setResultImgUrl('');
  };

  // 添加清空图片的处理函数
  const handleClearImage = (fieldName: string) => {
    form.setFieldValue(fieldName, undefined);
  };

  return (
    <div style={{ width: '100%', padding: 24 }}>
      <Card title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span>虚拟试衣上传</span>
          <Radio.Group value={tryonType} onChange={(e) => setTryonType(e.target.value)} style={{ marginLeft: 16 }}>
            <Radio value="aliyun">阿里云tryon</Radio>
            <Radio value="comfyui">自研tryon</Radio>
          </Radio.Group>
          {tryonType === 'comfyui' && (
            <div style={{ color: 'red', marginLeft: 16 }}>！！请注意：目前自研tryon只能支持输入上装或下装图片，不能同时输入。</div>
          )}
        </div>
      }>
        <Form form={form} layout="vertical" onFinish={onFinish}>
          <Flex style={{ gap: 24 }} justify="space-between">
            {/* 参考图 */}
            <div style={{ width: '25%' }}>
              <Form.Item
                name="personImgUrl"
                label="参考图"
                rules={[{ required: true, message: '请上传参考图' }]}
              >
                <CustomUploader title="参考图" />
              </Form.Item>
              <Button onClick={() => handleClearImage('personImgUrl')}>清空参考图</Button>
            </div>

            {/* 上装 */}
            <div style={{ width: '25%' }}>
              <Form.Item
                name="topUrl"
                label="上装"
                rules={[{ required: tryonType === 'aliyun', message: '请上传上装图片' }]}
              >
                <CustomUploader title="上装" />
              </Form.Item>
              <Button onClick={() => handleClearImage('topUrl')}>清空上装</Button>
            </div>

            {/* 下装 */}
            <div style={{ width: '25%' }}>
              <Form.Item
                name="bottomUrl"
                label="下装"
                rules={tryonType === 'comfyui' ? [] : []}
              >
                <CustomUploader title="下装" />
              </Form.Item>
              <Button onClick={() => handleClearImage('bottomUrl')}>清空下装</Button>
            </div>

            {/* 生成结果展示 */}
            <div style={{ width: '25%' }}>
              <div style={{ marginBottom: 8 }}>生成结果</div>
              <div style={{
                width: '100%',
                height: 512,
                backgroundColor: '#fafafa',
                borderRadius: 8,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'hidden'
              }}>
                {isGenerating ? (
                  <Spin tip="生成中..." />
                ) : resultImgUrl ? (
                  <img
                    src={resultImgUrl}
                    alt="生成结果"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'contain'
                    }}
                  />
                ) : (
                  <span style={{ color: '#999' }}>等待生成</span>
                )}
              </div>
            </div>
          </Flex>

          {/* 底部按钮 */}
          <Flex justify="center" style={{ marginTop: 12 }}>
            <Space>
              <Button type="primary" htmlType="submit" loading={isGenerating}>
                {isGenerating ? '生成中...' : '提交生成'}
              </Button>
              <Button icon={<EyeOutlined />} onClick={handleUploadPreview}>
                预览
              </Button>
              <Button onClick={handleClearImages}>
                清除图片
              </Button>
            </Space>
          </Flex>
        </Form>
      </Card>

      {/* 上传区域的预览弹窗 */}
      <PreviewModal
        visible={uploadPreviewVisible}
        onClose={() => setUploadPreviewVisible(false)}
        images={uploadPreviewImages}
      />

      {/* 列表的预览弹窗 */}
      <PreviewModal
        visible={previewVisible}
        onClose={() => setPreviewVisible(false)}
        images={{
          top: previewImages.top,
          bottom: previewImages.bottom,
          person: previewImages.person,
          tryonResult4Refiner: previewImages.tryonResult4Refiner,
          result: previewImages.result,
        }}
      />

      {/* 任务列表区域 */}
      <Card 
        title="任务列表" 
        style={{ marginTop: 24 }}
        extra={
          <Button 
            icon={<ReloadOutlined />} 
            onClick={() => fetchTaskList()}
          >
            刷新
          </Button>
        }
      >
        <Table
          columns={tryonType === 'aliyun' ? aliyunTryonTaskColumns : comfyuiColumns}
          dataSource={taskList}
          rowKey="id"
          pagination={pagination}
          onChange={handleTableChange}
          loading={loading}
          scroll={{ x: 'max-content' }}
          size="small"
        />
      </Card>

    </div>
  );
};

export default TryonPage;
