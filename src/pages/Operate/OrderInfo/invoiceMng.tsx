import React, { useEffect, useState } from 'react';
import { Button, Form, Input, message, Modal, Popconfirm, Radio, Space, Table, Upload } from 'antd';
import {
    applyReverseInvoice,
    InvoiceInfoVO,
    queryInvoiceList, retryApplyInvoice,
    updateInvoiceInfo,
} from '@/services/InvoiceInfoController';
import { UploadOutlined } from '@ant-design/icons';
import { UPLOAD_URL } from '@/constants';
import { getUserInfo } from '@/utils/utils';

const InvoiceMng: React.FC = () => {
    const [invoices, setInvoices] = useState<InvoiceInfoVO[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
    const [currentInvoice, setCurrentInvoice] = useState<InvoiceInfoVO | null>(null);
    const [form] = Form.useForm();
    const [fileList, setFileList] = useState<any[]>([]);

    const userInfo = getUserInfo()

    const fetchInvoices = async () => {
        setLoading(true);
        try {
            const result = await queryInvoiceList({
                orderBy: 'id desc'
            });
            setInvoices(result || []);
        } catch (error) {
            console.error('Failed to fetch invoices:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchInvoices();
    }, []);

    const columns = [
        {
            title: '发票ID',
            dataIndex: 'id',
            key: 'id',
        },
        {
          title: '客户',
            dataIndex: 'masterUserNick',
            key: 'masterUserNick',
        },
        {
            title: '账号',
            dataIndex: 'masterUserLoginId',
            key: 'masterUserLoginId',
        },
        {
            title: '申请时间',
            dataIndex: 'applyTime',
            key: 'applyTime',
        },
        {
            title: '发票抬头',
            dataIndex: 'subjectName',
            key: 'subjectName',
        },
        {
            title: '统一社会信用代码',
            dataIndex: 'creditCode',
            key: 'creditCode',
        },
        {
            title: '发票金额（元）',
            dataIndex: 'amountWithTax',
            key: 'amountWithTax',
        },
        {
            title: '完结时间',
            dataIndex: 'finishTime',
            key: 'finishTime',
        },
        {
            title: '发票号码',
            dataIndex: 'invoiceNo',
            key: 'invoiceNo',
        },
        {
            title: '状态',
            dataIndex: 'statusName',
            key: 'statusName',
        },
        {
            title: '操作',
            key: 'action',
            render: (_, record) => (
                <Space>
                    <a onClick={() => handleEdit(record)}>编辑</a>
                    {record.invoiceDownloadUrl && (
                        <a onClick={() => window.open(record.invoiceDownloadUrl, '_blank')}>
                            下载发票
                        </a>
                    )}

                    {/*只给松然admin可见*/}
                    {record?.status === 'INVOICING' && !record?.invoiceNo && userInfo?.id === 100009 &&
                        <a style={{color: 'red'}} onClick={() => {
                            retryApplyInvoice({
                                id: record.id,
                            }).then(res => {
                                if (res){
                                    message.success('重试成功');
                                }
                            })
                        }}>失败重试</a>
                    }

                    {record.status === 'INVOICE_END' && record?.invoiceNo &&
                        <Popconfirm title={'在退款场景开红字发票，是否确认申请冲退'}
                                    okText={'确认冲退'}
                                    onConfirm={() => {
                                        applyReverseInvoice({id: record.id}).then(res => {
                                            if (res){
                                                message.success('提交成功');
                                                fetchInvoices();
                                            } else {
                                                message.error('操作失败');
                                            }
                                        })
                                    }}>
                            <a>发票冲退</a>
                        </Popconfirm>
                    }
                </Space>
            ),
        },
    ];

    const handleEdit = (record: InvoiceInfoVO) => {
        setCurrentInvoice(record);
        form.setFieldsValue({
            status: record.status, // 只允许编辑状态
        });
        setIsModalVisible(true); // 显示模态框
    };

    const handleOk = async () => {
        const values = await form.validateFields();
        updateInvoiceInfo({ ...currentInvoice, ...values }).then(res=>{
            if (res){
                message.success('操作成功');
                setIsModalVisible(false);
                fetchInvoices(); // 更新页面上的数据
                setFileList([]);
            } else {
                message.error('操作失败');
            }
        });
        
    };

    const handleCancel = () => {
        setIsModalVisible(false); // 关闭模态框
    };

    const handleUploadChange = (info: any) => {
        let newFileList = [...info.fileList];

        if (info.file.status === 'done') {
            if (info.file.response?.success && info.file.response?.data) {
                message.success(`${info.file.name} 文件上传成功`);
                form.setFieldsValue({
                    invoiceDownloadUrl: info.file.response?.data,
                });
            } else {
                message.error(`${info.file.name} 文件上传失败`);    
            }
        } else if (info.file.status === 'error') {
            message.error(`${info.file.name} 文件上传失败`);
        }

        setFileList(newFileList);
    };

    return (
        <div style={{padding: 12}}>
            <h1>发票管理</h1>
            <Table
                dataSource={invoices}
                columns={columns}
                loading={loading}
                rowKey="id"
            />
            <Modal
                title="编辑发票"
                open={isModalVisible}
                onOk={handleOk}
                onCancel={handleCancel}
            >
                <Form form={form} layout="vertical">
                    <Form.Item label="发票ID">
                        <Input value={currentInvoice?.id} disabled />
                    </Form.Item>
                    <Form.Item label="发票抬头">
                        <Input value={currentInvoice?.subjectName} disabled />
                    </Form.Item>
                    <Form.Item label="统一社会信用代码">
                        <Input value={currentInvoice?.creditCode} disabled />
                    </Form.Item>
                    <Form.Item label="发票金额（元）">
                        <Input value={currentInvoice?.amountWithTax} disabled />
                    </Form.Item>

                    <Form.Item name="status" label="发票状态" rules={[{ required: true, message: '请选择发票状态' }]}>
                        <Radio.Group>
                            <Radio value="NONE">未开票</Radio>
                            <Radio value="INVOICING">开票中</Radio>
                            <Radio value="INVOICE_END">已开票</Radio>
                            <Radio value="INVOICE_REVERSEING">冲退中</Radio>
                            <Radio value="INVOICE_REVERSED">已冲退</Radio>
                        </Radio.Group>
                    </Form.Item>

                    {form.getFieldValue('invoiceDownloadUrl') && (
                        <a onClick={() => window.open(form.getFieldValue('invoiceDownloadUrl'), '_blank')}>
                            下载发票 {currentInvoice?.invoiceNo}
                        </a>
                    )}

                    {!form.getFieldValue('invoiceDownloadUrl') && (
                        <Form.Item label="上传发票PDF" name={'invoiceDownloadUrl'}>
                            <Upload
                                name="file"
                                action={UPLOAD_URL}
                                onChange={handleUploadChange}
                                fileList={fileList}
                                showUploadList={{ showPreviewIcon: true }}
                                accept=".pdf"
                            >
                                <Button icon={<UploadOutlined />}>点击上传发票PDF</Button>
                            </Upload>
                        </Form.Item>
                    )}

                </Form>
            </Modal>
        </div>
    );
};

export default InvoiceMng;
