import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  Col,
  Collapse,
  DatePicker,
  Flex,
  message,
  Modal,
  Row,
  Select,
  Space,
  Statistic,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import { Column } from '@ant-design/charts';
import type { ColumnsType } from 'antd/es/table';
import './index.less';
import { getPricePlanDescription, OrderInfoVO, queryOrderInfoList } from '@/services/OrderInfoController';
import moment from 'moment';
import { queryAllMaster } from '@/services/UserController';
import { Dayjs } from 'dayjs';
import { useNavigate } from 'react-router-dom';
import { download } from '@/utils/utils';
import { OrganizationVO, OrgTree, queryDistributorOrganizationTrees, convertOrgsToTree } from '@/services/OrganizationController';
import { CopyOutlined } from '@ant-design/icons';
import { formatFullTime } from '@/utils/dateUtils';

interface UserSummary {
  totalAmount: number;
  userCount: number;
  userSummaries: Array<{
    userId: string | number;
    userNick: string;
    count: number;
    totalAmount: number;
  }>;
}

interface DailyAmount {
  date: string;
  amount: number;
}

// 添加新的接口定义
interface DistributorStats {
  totalAmount: number;
  userCount: number; // Change this from Set to number
}

interface MonthlyStatistics {
  totalAmount: number;
  userCount: number;
}

const { RangePicker } = DatePicker;

const OrderInfoMng: React.FC = () => {
  const [data, setData] = useState<OrderInfoVO[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [current, setCurrent] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);
  const [selectedOrder, setSelectedOrder] = useState<OrderInfoVO | null>(null);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [dailyAmounts, setDailyAmounts] = useState<DailyAmount[]>([]);
  const [summary, setSummary] = useState<UserSummary>({ totalAmount: 0, userCount: 0, userSummaries: [] });
  const [summaryPage, setSummaryPage] = useState<number>(1);
  const [summaryPageSize] = useState<number>(10);

  // 添加新的 state
  const [currentMonthStats, setCurrentMonthStats] = useState<MonthlyStatistics>({ totalAmount: 0, userCount: 0 });
  const [lastMonthStats, setLastMonthStats] = useState<MonthlyStatistics>({ totalAmount: 0, userCount: 0 });

  const [dateRange, setDateRange] = useState<[Dayjs | null, Dayjs | null] | null>(null);
  const [distributors, setDistributors] = useState<any[]>([]);
  const [customers, setCustomers] = useState<any[]>([]); // 添加客户列表state
  const [selectedCustomer, setSelectedCustomer] = useState<number | null>(null); // 添加选中客户state

  //  所有渠道商组织（包含子组织）
  const [selectedDistributor, setSelectedDistributor] = useState<any[]>([]);
  const [allDistributorOrgTrees, setAllDistributorOrgTrees] = useState<OrgTree[]>([]);

  const [allData, setAllData] = useState<OrderInfoVO[]>([]);
  const [filteredData, setFilteredData] = useState<OrderInfoVO[]>([]);
  const [quickMonth, setQuickMonth] = useState<string | null>(null);

  const navigate = useNavigate();

  const [allCustomers, setAllCustomers] = useState<any[]>([]);

  // Define calculateSummary here, before useMemo
  const calculateSummary = (orders: OrderInfoVO[]): UserSummary => {
    const summary: UserSummary = {
      totalAmount: 0,
      userCount: 0,
      userSummaries: [],
    };
    const uniqueUsers = new Set<string | number>();

    orders.forEach(order => {
      summary.totalAmount += Number(order.payAmount) || 0;
      uniqueUsers.add(order.masterUserId);

      const existingSummary = summary.userSummaries.find(
        s => s.userId === order.masterUserId,
      );
      if (existingSummary) {
        existingSummary.count++;
        existingSummary.totalAmount += Number(order.payAmount) || 0;
      } else {
        summary.userSummaries.push({
          userId: order.masterUserId,
          userNick: order.masterUserNick,
          count: 1,
          totalAmount: Number(order.payAmount) || 0,
        });
      }
    });

    summary.userCount = uniqueUsers.size;
    return summary;
  };

  const calculateDailyAmounts = (orders: OrderInfoVO[]) => {
    const dailyMap = new Map<string, number>();
    orders.forEach(order => {
      if (order.finishTime) {
        const date = new Date(order.finishTime).toISOString().split('T')[0];
        const amount = Number(order.payAmount) || 0;
        dailyMap.set(date, (dailyMap.get(date) || 0) + amount);
      }
    });
    const sortedDailyAmounts = Array.from(dailyMap.entries())
      .map(([date, amount]) => ({ date, amount }))
      .sort((a, b) => a.date.localeCompare(b.date));
    setDailyAmounts(sortedDailyAmounts);
  };

  // 添加新的函数来计算月度统计
  const calculateMonthlyStatistics = (orders: OrderInfoVO[]) => {
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();
    const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;

    const currentMonthStats: MonthlyStatistics = { totalAmount: 0, userCount: 0 };
    const lastMonthStats: MonthlyStatistics = { totalAmount: 0, userCount: 0 };
    const currentMonthUsers = new Set<string | number>();
    const lastMonthUsers = new Set<string | number>();

    const processOrder = (order: OrderInfoVO, stats: MonthlyStatistics, users: Set<string | number>) => {
      const amount = Number(order.payAmount) || 0;
      stats.totalAmount += amount;
      users.add(order.masterUserId);
    };

    orders.forEach(order => {
      if (order.finishTime) {
        const orderDate = new Date(order.finishTime);
        const orderMonth = orderDate.getMonth();
        const orderYear = orderDate.getFullYear();

        if (orderYear === currentYear && orderMonth === currentMonth) {
          processOrder(order, currentMonthStats, currentMonthUsers);
        } else if (orderYear === lastMonthYear && orderMonth === lastMonth) {
          processOrder(order, lastMonthStats, lastMonthUsers);
        }
      }
    });

    currentMonthStats.userCount = currentMonthUsers.size;
    lastMonthStats.userCount = lastMonthUsers.size;

    setCurrentMonthStats(currentMonthStats);
    setLastMonthStats(lastMonthStats);
  };

  useEffect(() => {
    setLoading(true);
    // 假设有一个 API 来获取所有渠道商
    queryAllMaster(['DISTRIBUTOR']).then(res => {
      if (res && Array.isArray(res)) {
        setDistributors(res.map(d => ({ value: String(d.id), label: d.corpName })));
      }
    });

    // 获取所有客户
    queryAllMaster(['MERCHANT']).then(res => {
      if (res && Array.isArray(res)) {
        const customerOptions = res.map(c => ({ value: c.id, label: c.corpName ? `${c.nickName}(${c.id})@${c.corpName}` : `${c.nickName}(${c.id})` }));
        setAllCustomers(customerOptions);
        setCustomers(customerOptions);
      }
    });

    queryDistributorOrganizationTrees().then(res => {
      if (res && Array.isArray(res)) {
        //@ts-ignore
        setAllDistributorOrgTrees(convertOrgsToTree(res));
      } else {
        setAllDistributorOrgTrees([]);
      }

    });

    queryOrderInfoList({ orderBy: 'finish_time desc' }).then(orders => {
      if (orders) {
        setAllData(orders);
      }
      setLoading(false);
    });
  }, []);

  const createCopyOrderNo = useCallback((orderNo: string) => {
    return () => {
      navigator.clipboard.writeText(orderNo);
      message.success('已复制');
    };
  }, []);

  // 当selectedDistributor发生变化时，更新 customers
  useEffect(() => {
    if (selectedDistributor && selectedDistributor.length > 0) {
      let distributorIds = selectedDistributor.map(distributor => distributor.value);
      let filteredCustomerIds = allData
        .filter(order => order?.distributorSalesOrgIdFullPath && isPrefix(order?.distributorSalesOrgIdFullPath, selectedDistributor))
        .map(order => order.masterUserId);
      if (filteredCustomerIds.length > 0) {
        let uniqueCustomerIds = [...new Set(filteredCustomerIds)];
        let filteredCustomers = allCustomers.filter(customer => uniqueCustomerIds.includes(customer.value));
        setCustomers(filteredCustomers);
      } else {
        setCustomers([]);
      }
    } else {
      setCustomers(allCustomers);
    }
  }, [selectedDistributor, allCustomers, allData]);

  function isPrefix<T>(a: T[], b: T[]): boolean {
    return b.length <= a.length &&
      b.every((item, index) => item === a[index]);
  }

  // Use useMemo to filter and recalculate data when filters change
  useMemo(() => {
    let filtered = allData;

    if (dateRange && dateRange[0] && dateRange[1]) {
      const startDate = moment(dateRange[0].toDate()).startOf('day');
      const endDate = moment(dateRange[1].toDate()).endOf('day');
      filtered = filtered.filter(order => {
        const orderDate = moment(order.finishTime);
        return orderDate.isSameOrAfter(startDate) && orderDate.isSameOrBefore(endDate);
      });
    }

    if (selectedDistributor && selectedDistributor.length > 0) {
      console.log('selectedDistributor\n', selectedDistributor);
      console.log('before filtered allData\n', filtered);
      filtered = filtered.filter(order => order?.distributorSalesOrgIdFullPath && isPrefix(order?.distributorSalesOrgIdFullPath, selectedDistributor));
      console.log('after filtered allData\n', filtered);
    }

    // 添加客户过滤逻辑
    if (selectedCustomer) {
      filtered = filtered.filter(order => order.masterUserId === selectedCustomer);
    }

    setFilteredData(filtered);
    setSummary(calculateSummary(filtered));
    calculateDailyAmounts(filtered);
    calculateMonthlyStatistics(filtered);
  }, [allData, dateRange, selectedDistributor, selectedCustomer]); // 添加selectedCustomer依赖

  function getInvoiceStatusDotColor(status: string | undefined) {
    if (status === 'NONE') {
      return '#D8D8D8';
    } else if (status === 'INVOICING') {
      return '#FF9900';
    } else if (status === 'INVOICE_END') {
      return '#1ECD04';
    }
    console.error('Invalid invoice status:', status);
    return '#D8D8D8';
  }

  const columns: ColumnsType<OrderInfoVO> = [
    {
      title: '订单号',
      key: 'orderNo',
      render: (_, record) => {
        // Ensure orderNo is a string
        const orderNo = typeof record?.orderNo === 'string' ? record.orderNo : '';
        const shortOrderNo = orderNo.substring(0, 8) + '...';
        const copyOrderNo = createCopyOrderNo(orderNo);

        return (
          <Tooltip title={'点击复制'}>
            <span onClick={copyOrderNo} style={{ cursor: 'pointer' }}>
              {shortOrderNo}
              <CopyOutlined style={{ marginLeft: 4 }} />
            </span>
          </Tooltip>
        );
      },
    },
    {
      title: '客户', key: 'masterUserNick',
      render: (_, record) => <span>{`${record.masterUserNick}(${record.masterUserId})`}</span>
    },
    { title: '客户公司', dataIndex: 'masterCorpName', key: 'masterCorpName', width: 200 },
    {
      title: '渠道',
      dataIndex: 'distributorCorpName',
      key: 'distributorCorpName',
    },
    {
      title: '销售',
      dataIndex: 'distributorSalesNickName',
      key: 'distributorSalesNickName',
    },
    {
      title: '销售所在部门',
      dataIndex: 'distributorSalesOrgName',
      key: 'distributorSalesOrgName',
    },
    { title: '订单金额（元）', dataIndex: 'payAmount', key: 'payAmount' },
    { title: '支付方式', key: 'payType',
      render: (_,record)=>{
        switch (record.payType) {
          case 'wx': return '微信';
          case 'alipay': return '支付宝';
          case 'offlinePayment': return '线下支付';
          default: return '-';
        }
    }},
    {
      title: '产品套餐',
      dataIndex: 'productName',
      key: 'productName'
    },
    { title: '充值时间', dataIndex: 'finishTime', key: 'finishTime' },
    {
      title: '开票状态',
      key: 'invoiceStatus',
      render: (_, record) => (
        <Flex gap={'8px'} align={'center'}>
          <div style={{
            width: 6,
            height: 6,
            backgroundColor: getInvoiceStatusDotColor(record?.invoiceStatus),
            borderRadius: '50%',
          }} />
          <span>{record.invoiceStatusName}</span>
        </Flex>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button type="link" onClick={() => handleViewDetails(record)}>查看详情</Button>

          {record.invoiceStatus === 'INVOICING' && (
            <a onClick={() => navigate('/invoice-mng')}>发票管理</a>
          )}

          {record.invoiceStatus === 'INVOICE_END' && record.invoiceFileUrl && (
            <>
              <a onClick={() => {
                if (record.invoiceFileUrl) {
                  download(record.invoiceFileUrl);
                }
              }}>下载发票</a>
            </>
          )}
        </Space>
      ),
    },
  ];

  const handleViewDetails = (record: OrderInfoVO) => {
    setSelectedOrder(record);
    setModalVisible(true);
  };

  const handleModalClose = () => {
    setModalVisible(false);
    setSelectedOrder(null);
  };

  const config = {
    data: dailyAmounts,
    xField: 'date',
    yField: 'amount',
    label: {
      position: 'middle',
      style: {
        fill: '#FFFFFF',
        opacity: 0.6,
      },
    },
    xAxis: {
      label: {
        autoHide: true,
        autoRotate: false,
      },
    },
    meta: {
      date: { alias: '日期' },
      amount: { alias: '充值金额' },
    },
  };

  const sortedUserSummaries = summary.userSummaries.sort((a, b) => b.totalAmount - a.totalAmount);
  const paginatedUserSummaries = sortedUserSummaries.slice(
    (summaryPage - 1) * summaryPageSize,
    summaryPage * summaryPageSize,
  );

  const changeMonth = (date: string) => {
    if (quickMonth === date) {
      setQuickMonth(null);
      setDateRange(null);
      return ;
    }

    setQuickMonth(date)

    // 使用 moment 解析字符串为日期对象
    const year = parseInt(date.slice(0, 4), 10); // 获取年份
    const month = parseInt(date.slice(4, 6), 10); // 获取月份

    // 创建当月第一天和最后一天的日期
    const startDate = moment(`${year}-${month}`, 'YYYY-MM').startOf('month'); // 当月第一天
    const endDate = moment(`${year}-${month}`, 'YYYY-MM').endOf('month'); // 当月最后一天

    //@ts-ignore
    setDateRange([startDate, endDate]);
  };

  const getRecentMonth = () => {
    //获取近6个月的字符串列表，格式为202506，但不包括本月
    const result: string[] = [];
    const now = moment();

    for (let i = 1; i <= 12; i++) {
      const targetMonth = now.clone().subtract(i, 'months');
      const monthStr = targetMonth.format('YYYYMM');
      result.push(monthStr); // 从远到近排列（如：202411 -> 202504）
    }

    return result;
  };

  const QuickTag = ({ month }) => (
    <Tag style={{ marginRight: 8 }} className={'pointer'} color={month === quickMonth ? 'blue': undefined}
         onClick={() => changeMonth(month)}>
      {month}
    </Tag>
  );

  return (
    <div className="order-info-page">
      <Card className="filter-card" style={{ marginBottom: 16 }}>
        <Flex vertical gap={12}>
          <Space>
            <RangePicker
              value={dateRange}
              style={{ width: 300 }}
              onChange={(dates: [Dayjs | null, Dayjs | null] | null) => {
                setDateRange(dates);
                setQuickMonth(null);
              }}
            />

            <Select
              style={{ width: 300 }}
              placeholder="选择销售/渠道商"
              allowClear
              showSearch
              optionFilterProp="label"
              onChange={(value) => {
                setSelectedDistributor(value ? [value] : []);
              }}
              options={allDistributorOrgTrees.map(item => ({
                label: item.label,
                value: item.value,
              }))}
            />

            {selectedDistributor && selectedDistributor.length > 0 && (() => {
                const foundDistributor = allDistributorOrgTrees.find(item => item && item.value === selectedDistributor[0]);
                return foundDistributor && foundDistributor.children && foundDistributor.children.length > 0;
              })() &&
              <Select
                style={{ width: 200 }}
                placeholder="团队/二级渠道（可选）"
                allowClear
                showSearch
                optionFilterProp="label"
                disabled={!selectedDistributor || selectedDistributor.length === 0}
                onChange={(value) => {
                  if (value && selectedDistributor && selectedDistributor.length > 0) {
                    console.log('selectedDistributor更新:\n', selectedDistributor, value, [...selectedDistributor, value]);
                    setSelectedDistributor([selectedDistributor[0], value]);
                  } else {
                    // 当清除二级团队选择时，恢复到只有一级渠道的状态
                    setSelectedDistributor(selectedDistributor.slice(0, 1));
                  }
                }}
                options={selectedDistributor && selectedDistributor.length > 0 ?
                  allDistributorOrgTrees
                    .find(item => item.value === selectedDistributor[0])?.children || [] :
                  []
                }
              />
            }

            {/* 添加客户过滤搜索下拉菜单 */}
            <Select
              style={{ width: 300 }}
              placeholder="选择客户（可搜索）"
              allowClear
              showSearch
              optionFilterProp="label"
              onChange={(value) => setSelectedCustomer(value)}
              options={customers}
            />

          </Space>

          <Space>
            快捷筛选：
            <Flex wrap={'wrap'} gap={8}>
              {getRecentMonth().map((month) => <QuickTag key={month} month={month} />)}
            </Flex>
          </Space>
        </Flex>
      </Card>

      {/* 添加新的统计卡片 */}
      <Card className="monthly-stats-card" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <h3>汇总统计</h3>
            <Statistic title="充值总金额" value={summary.totalAmount} precision={2} suffix="元" />
            <Statistic title="充值人数" value={summary.userCount} />
          </Col>
          {!(dateRange && dateRange[0] && dateRange[1]) &&
            <>
              <Col span={8}>
                <h3>本月统计</h3>
                <Statistic title="充值总金额" value={currentMonthStats.totalAmount} precision={2} suffix="元" />
                <Statistic title="充值人数" value={currentMonthStats.userCount} />
              </Col>
              <Col span={8}>
                <h3>上月统计</h3>
                <Statistic title="充值总金额" value={lastMonthStats.totalAmount} precision={2} suffix="元" />
                <Statistic title="充值人数" value={lastMonthStats.userCount} />
              </Col>
            </>
          }
        </Row>
      </Card>

      <Collapse
        className="summary-card"
        style={{ marginBottom: 16 }}
        items={[
          {
            key: '1',
            label: '统计概览',
            children: (
              <div>
                <Column {...config} />
              </div>
            ),
          },
        ]}
      />

      <Table
        columns={columns}
        dataSource={filteredData}
        loading={loading}
        pagination={{
          current,
          pageSize,
          total: filteredData.length,
          onChange: (page, size) => {
            setCurrent(page);
            setPageSize(size);
          },
        }}
        rowKey="id"
      />

      <Modal
        open={modalVisible}
        title="订单详情"
        onCancel={handleModalClose}
        footer={null}
      >
        {selectedOrder && (
          <div>
            <p><strong>订单号：</strong>{selectedOrder.orderNo}</p>
            <p><strong>主客户ID：</strong>{selectedOrder.masterUserId}</p>
            <p><strong>主客户昵称：</strong>{selectedOrder.masterUserNick}</p>
            <p><strong>操作员用户ID：</strong>{selectedOrder.operatorUserId}</p>
            <p><strong>订单状态：</strong>{selectedOrder.orderStatus}</p>
            <p><strong>产品码：</strong>{getPricePlanDescription(selectedOrder.productCode)}</p>
            <p><strong>创建时间：</strong>{selectedOrder.createTime}</p>
            <p><strong>充值时间：</strong>{selectedOrder.finishTime}</p>
            <p><strong>支付金额：</strong>{selectedOrder.payAmount}元</p>
            <p><strong>支付信息：</strong>{selectedOrder.payDetail}</p>
            <p><strong>产品详情：</strong>{selectedOrder.productDetail}</p>
            <p><strong>订单完结时间：</strong>{selectedOrder.finishTime}</p>
            <p><strong>扩展信息：</strong>{selectedOrder.extInfo}</p>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default OrderInfoMng;
