import {
  statsBackUserQueuedCreative,
  statsCustomerQueuedCreative,
  StatsUserQueuedCreativeDO,
} from '@/services/CreativeController';
import { Flex, Modal, Table } from 'antd';
import { useEffect, useState } from 'react';

interface UserQueueCreativeModalProps {
  isCustomer?: boolean;
  onCancel: () => void;
}

const UserQueueCreativeModal: React.FC<UserQueueCreativeModalProps> = ({
  isCustomer = true,
  onCancel,
}) => {
  const [data, setData] = useState<StatsUserQueuedCreativeDO[]>([]);
  useEffect(() => {
    const method = isCustomer
      ? statsCustomerQueuedCreative
      : statsBackUserQueuedCreative;
    method().then((res) => {
      if (!res) return;
      setData(res);
    });
  }, []);

  const columns = [
    {
      title: '用户ID',
      dataIndex: 'userId',
      key: 'userId',
    },
    {
      title: '用户昵称',
      dataIndex: 'nickName',
      key: 'nickName',
    },
    {
      title: '排队总数',
      dataIndex: 'total',
      key: 'total',
    },
    {
      title: '排队数',
      dataIndex: 'queueSize',
    },
    {
      title: '出图数',
      dataIndex: 'processingSize',
    },
    {
      title: '最早创建时间',
      dataIndex: 'minCreateTime',
    },
  ];

  return (
    <Modal
      open={true}
      onCancel={onCancel}
      width={800}
      centered
      title={'客户队列数据'}
      footer={null}
    >
      <Flex vertical align={'center'} className={'width-100'}>
        <Table
          columns={columns}
          dataSource={data}
          style={{ width: '100%' }}
          pagination={false}
        />
      </Flex>
    </Modal>
  );
};

export default UserQueueCreativeModal;
