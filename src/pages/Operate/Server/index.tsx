import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Card,
    Checkbox,
    Col,
    Descriptions,
    Dropdown,
    Flex,
    Form,
    Input,
    message,
    Modal,
    notification,
    Popconfirm,
    Row,
    Select,
    Switch,
    Tabs,
    Tooltip,
} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import {PageContainer} from '@ant-design/pro-components';
import {
    addPipeline,
    addServer,
    AllServerTypes,
    delPipeline,
    delServer,
    fetchServerRunLog,
    MachineInfo,
    MachineRoom,
    PipelineVO,
    queryAllPipeline,
    restartServer,
    restartServerPort,
    ServerVO,
    testServer, updateCreativeNode,
    updPipeline,
    updServer,
} from '@/services/ServerController';
import { deepCopy, sleep } from '@/utils/utils';
import { queryAllMaster, RoleTypesItems } from '@/services/UserController';
import {
  BugOutlined,
  CheckOutlined,
  CloseCircleOutlined,
  CloudServerOutlined,
  EditOutlined,
  FileTextOutlined,
  HomeOutlined,
  MergeOutlined,
  PlusCircleTwoTone,
  RedoOutlined,
  TransactionOutlined,
} from '@ant-design/icons';
import {queryDeviceInfo} from '@/services/SystemController';
import DeviceMng from '@/components/Operate/server/DeviceMng';
import './index.less';
import { QueuedModelData, statsQueuedModel } from '@/services/MaterialModelController';
import { QueuedCreativeData, statsQueuedCreative } from '@/services/CreativeController';
import { HighlightText } from '@/components/Common/CommonComponent';
import UserQueueCreativeModal from '@/pages/Operate/Server/UserQueueCreativeModal';

const Server: React.FC = () => {
    const [data, setData] = useState<Array<PipelineVO>>([]);
    const [masterOptions, setMasterOptions] = React.useState<any[]>([]);
    const [machineRooms, setMachineRooms] = useState<Array<MachineRoom>>([]);
    const [queuedModelData, setQueuedModelData] = useState<QueuedModelData | null>(null);
    const [queuedCreativeData, setQueuedCreativeData] = useState<QueuedCreativeData | null>(null);
    const [log, setLog] = useState<string>('');
    const logRef = useRef(null);

    const [items, setItems] = useState<Array<{ label: string; key: string, children: string }>>([]);
    const [update, setUpdate] = useState<null | PipelineVO>(null);
    const [add, setAdd] = useState(false);
    const [deleteItem, setDeleteItem] = useState<null | PipelineVO>(null);
    const [index, setIndex] = useState(-1);
    const [showUserQueued, setShowUserQueued] = useState(false);
    const [showBackUserQueued, setShowBackUserQueued] = useState(false);

    const [form] = Form.useForm();

    useEffect(() => {
        queryAllMaster(RoleTypesItems.map(e=>e.value) as any[]).then(res => {
            if (res && Array.isArray(res)) {
                const masterOptions = [];
                if (Array.isArray(res)) {
                    res.forEach(item => {
                        // @ts-ignore
                        masterOptions.push({
                            label: item.nickName + (item.corpName ? '@' + item.corpName : ''),
                            value: item.id + '',
                        });
                    });
                }
                setMasterOptions(masterOptions);
                fetchData(masterOptions);
            }
        });

        reloadDevice();
    }, []);

    useEffect(() => {
        if (log && logRef.current) {
            requestAnimationFrame(() => {
                // @ts-ignore
                logRef.current.scrollTop = logRef.current.scrollHeight;
            });
        }
    }, [log]);

    const reloadDevice = () => {
        queryDeviceInfo().then(res => {
            setMachineRooms(res || []);
        });
    };

    const fetchData = async (masterOptions: null | any[] = null) => {
        const statsModelData = await statsQueuedModel();
        if (statsModelData) {
            setQueuedModelData(statsModelData);
        }

        const statsCreativeData = await statsQueuedCreative();
        if (statsCreativeData) {
            setQueuedCreativeData(statsCreativeData);
        }

        queryAllPipeline().then(res => {
            if (!res) return;

            const items: Array<{ label: string; key: string, children: string }> = [];
            res.forEach(item => {
                // @ts-ignore
                items.push({label: item.name, key: item.id + '', children: buildItemChildren(item, masterOptions, statsModelData, statsCreativeData)});

                if (item.servers) {
                    item.servers.forEach(server => {
                        server.key = server.id + '';
                        server.isEdit = false;

                        if (server.children) {
                            server.children.forEach(child => {
                                child.key = child.id + '';
                                child.isEdit = false;
                            });
                            //对children按可用>不可用排序
                            server.children.sort((a, b) => {
                                if (a.status === b.status) {
                                    return 0;
                                }
                                return a.status === 'DISABLE' ? 1 : -1;
                            });
                        }
                    });

                    //对item.servers排序，如果servers的状态为不可用，则排到后面
                    item.servers.sort((a, b) => {
                        if (a.status === b.status) {
                            return 0;
                        }
                        return a.status === 'DISABLE' ? 1 : -1;
                    });
                }
            });
            setItems(items);
            setData(res);

            if (res.length > 0 && index < 0) {
                setIndex(0);
            }
        });
    };

    const buildItemChildren = (item: PipelineVO, masterOptions_, statusModelData, statsCreativeData) => {
        if (!item.userRelation) {
            return;
        }
        if (!masterOptions_) {
            // eslint-disable-next-line no-param-reassign
            masterOptions_ = masterOptions;
        }

        // @ts-ignore
        const {user, role, isDefault} = item.userRelation;

        const items: Array<{ key: string, label: string | React.ReactNode, children: string | React.ReactNode }> = [];

        items.push({
            key: 'user',
            label: '按用户配置',
            children: user && user.length > 0 ? user.map((u) => masterOptions_.find((e) => e.value === u)?.label + ',') : '未配置',
        });

        items.push({
            key: 'role',
            label: '按角色配置',
            children: role && role.length > 0 ? role.map((r) => RoleTypesItems.find((e) => e.value === r)?.label + ',') : '未配置',
        });

        items.push({
            key: 'isDefault',
            label: '是否默认服务器',
            children: isDefault ? <div style={{color: 'red'}}>是</div> : <>否</>,
        });

        items.push({
            key: 'memo',
            label: '备注',
            children: item.memo,
        });

        items.push({
            key: 'servers',
            label: '集群机器数',
            children: item.servers ? item.servers.length + '' : '0',
        });
        item.servers?.reduce((acc, cur) => {
            if (cur.children) {
                return acc + cur.children.length;
            }
            return acc;
        }, 0);

        const availablePorts = item.servers?.filter((e) => e.status !== 'DISABLE').reduce((acc, cur) => {
            if (cur.children) {
                return acc + cur.children.filter((e) => e.status !== 'DISABLE' && ['IMAGE_GENERATE', 'LORA_TRAIN', 'LORA_PRE_PROCESS'].includes(e.type)).length;
            }
            return acc;
        }, 0);

        const busyPorts = item.servers?.filter((e) => e.status !== 'DISABLE').reduce((acc, cur) => {
            if (cur.children) {
                return acc + cur.children.filter((e) => e.status !== 'DISABLE' && ['IMAGE_GENERATE', 'LORA_TRAIN', 'LORA_PRE_PROCESS'].includes(e.type)).filter(e => e.realtimeStatus === 'BUSY').length;
            }
            return acc;
        }, 0);

        items.push({
            key: 'ports',
            label: '繁忙/可用',
            children: busyPorts + ' / ' + availablePorts + '',
        });

        items.push({
            key: 'ports',
            label: '等待队列汇总',
            children: <Flex>
                <div>客户出图:&nbsp;</div>
                <div className={'color-error weight pointer'} onClick={()=>setShowUserQueued(true)}>
                    {statsCreativeData?.merchantCnt||0}
                </div>
                <div>&nbsp;&nbsp;/&nbsp;&nbsp;后台出图:&nbsp;</div>
                <div className={'color-error weight pointer'} onClick={()=>setShowBackUserQueued(true)}>
                    {statsCreativeData?.backUserCnt||0}
                </div>
                <div>&nbsp;&nbsp;/&nbsp;&nbsp;系统出图:&nbsp;</div>
                <div className={'color-error weight'}>{statsCreativeData?.systemCnt||0}</div>
                <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;预处理:&nbsp;</div>
                <div className={'color-error weight'}>{statusModelData?.queuedPreProcess||0}</div>
                <div>&nbsp;&nbsp;/&nbsp;&nbsp;训练:&nbsp;</div>
                <div className={'color-error weight'}>{statusModelData?.queuedTrain||0}</div>
            </Flex>,
        });

        return (
            <Descriptions bordered items={items} column={3} labelStyle={{width: 100}}
                          contentStyle={{width: 200}}/>
        );
    };

    const handleEdit = (targetKey, action) => {
        if (action === 'add') {
            setUpdate(null);
            setAdd(true);
            form.resetFields();
            return;
        } else { //delete
            const find = data.find(item => item.id === Number.parseInt(targetKey));
            if (find) {
                setDeleteItem(find);
            }
        }
    };

    const handleSwitch = (value) => {
        const index = data.findIndex(item => item.id === Number(value));
        setIndex(index);
    };

    const showUpdate = (item: PipelineVO) => {
        form.resetFields();
        console.log(item);
        setUpdate(item);
        setAdd(false);
        let payload = item;
        if (item.userRelation) {
            // @ts-ignore
            const {user, role, isDefault} = item.userRelation;
            // @ts-ignore
            payload = {...item, user, role, isDefault};
        }
        form.setFieldsValue(payload);
    };

    const handleCommit = (values: Record<string, any>) => {
        if (!add && !update) {
            message.warning('请设置要修改的配置项');
            return;
        }

        const {user, role, isDefault, ...other} = values;
        const userRelationStr = JSON.stringify({user, role, isDefault});

        console.log({...other, userRelationStr});

        const method = add ? addPipeline : updPipeline;
        method({...other, userRelationStr}).then(res => {
            if (res) {
                notification.success({message: '修改成功'});
                setAdd(false);
                setUpdate(null);
                fetchData();
            }
        });
    };

    const handleDel = () => {
        if (!deleteItem) {
            message.warning('请选择要删除的集群');
            return;
        }

        delPipeline(deleteItem.id).then(res => {
            if (res) {
                notification.success({message: '删除成功'});
                fetchData();
                setDeleteItem(null);
            }
        });
    };

    const handleAddServerItem = (index, machine: MachineInfo) => {
        const payload = {
            pipelineId: data[index].id,
            level: 1,
            name: machine.name,
            config: machine.publicAddress,
            internalAddress: machine.internalAddress,
            status: 'DISABLE',
            deviceId: machine.id,
        };
        addServer(payload).then(res => {
            if (!res) return;
            message.success('保存成功');
            fetchData();
        });
    };
    const test = (record: ServerVO) => {
        testServer(record.id).then(res => {
            if (!res) return;

            const copy = deepCopy(data);

            const current = copy[index];
            const parent = current.servers.find(item => item.id === record.parentId);
            const find = parent.children.find(item => item.id === record.id);
            find.realtimeStatus = res.realtimeStatus;
            find.realtimeStatusName = res.realtimeStatusName;
            find.realtimeTaskId = res.realtimeTaskId;

            message.success(`测试成功，${record.config}当前状态为：${res.realtimeStatusName}`);

            setData(copy);
        });
    };

    const fetchRunLog = (record: ServerVO) => {
        fetchServerRunLog(record.id).then(res => {
            if (!res) return;
            setLog(res);
        });
    };

    const restartPort  = (server: ServerVO) => {
        restartServerPort(server.id).then(res => {
            if (res) {
                message.success('重启成功');
                sleep(500).then(() => {
                    fetchData();
                });
            }
        });
    };

    const doRestartServer = (server: ServerVO, type) => {
        restartServer(server.id, type).then(res => {
            if (res) {
                message.success('重启成功');
                sleep(500).then(() => {
                    fetchData();
                });
            }
        });
    };

    const doUpdateCreativeNode = (server: ServerVO) => {
      updateCreativeNode(server.id).then(res => {
            if (res) {
                message.success('更新成功');
                sleep(500).then(() => {
                    fetchData();
                });
            }
        });
    };

    // const updateAllCreativeNode = () => {
    //     fetchAllFileServers().then(res => {
    //         if(!res) return;
    //
    //     });
    // }

    const testPorts = (server: ServerVO) => {
        server.children?.forEach(e => {
            test(e)
        })
    }

    const changePortType = (server, type) => {
        updServer({...server, type}).then(() => {
            message.success('保存成功');
            fetchData();
        });
    };

    const enable = (record: ServerVO) => {
        updServer({...record, status: record.status === 'DISABLE' ? 'ENABLE' : 'DISABLE'}).then(res => {
            if (res) {
                message.success('保存成功');
                fetchData();
            }
        });
    };

    const handleAddPort = (record: ServerVO, port, type, deviceId) => {
        const payload = {
            parentId: record.id,
            pipelineId: record.pipelineId,
            level: record.level + 1,
            config: port,
            type,
            status: 'ENABLE',
            deviceId,
        };
        addServer(payload).then(res => {
            if (res) {
                message.success('保存成功');
                fetchData();
            }
        });
    };

    const buildStatusIcon = (status: string) => {
        switch (status) {
            case 'ENABLE':
                return <Badge color="blue" text="已开启"/>;
            case 'IDLE':
                return <Badge status="success" text="空闲"/>;
            case 'BUSY':
                return <Badge status="warning" text="繁忙"/>;
            case 'UNUSABLE':
                return <Badge status="error" text="不可用"/>;
            case 'DISABLE':
                return <Badge status="default" text="未启用"/>;
            case 'TESTING':
                return <Badge status="processing" text="连接中"/>;
        }
    };
    const getAddServerMenus = (index: number) => {
        let items: any[] = [];
        if (!machineRooms) return {items};

        machineRooms.forEach(room => {
            room.machines.forEach(machine => {
                items.push({
                    key: machine.id,
                    label: (<a
                        onClick={() => handleAddServerItem(index, machine)}>{machine.name ? machine.name + ' @ ' : ''}{room.name}</a>),
                });
            });
        });
        items = items.filter(i => data && index >= 0 && !data[index].servers.some(s => s.deviceId === i.key));
        return {items};
    };

    const getPortTypeMenu = (server: ServerVO) => {
        const items = AllServerTypes.filter(e => e.key !== server.type).map(e => ({
            key: e.key,
            label: (
                <Popconfirm title={`是否要将当前端口${server.config}的类型改为${e.label}`}
                            onConfirm={() => changePortType(server, e.key)}>
                    {e.label}
                </Popconfirm>
            ),
        }));

        return {items};
    };
    const getPortMenu = (server: ServerVO) => {
        if (!server || !machineRooms) return {items: []};

        let hit: MachineInfo | null = null;
        machineRooms.forEach(room => {
            for (let m of room.machines) {
                if (m.id === server.deviceId) {
                    hit = m;
                    break;
                }
            }
        });
        if (!hit) return {items: []};

        //@ts-ignore
        const items = hit.ports.map(e => ({
            key: e.port,
            label: e.port,
            children: AllServerTypes.map(t => ({
                key: t.key,
                label: <a
                    onClick={() => handleAddPort(server, e.port, t.key, hit?.ports?.find(p => p.port === e.port)?.id)}>{t.label}</a>,
            })),
        }));

        return {items};
    };

    const buildServerTitle = (server) => {
        if (!server) return '';
        const room = machineRooms.find(room => room.machines.some(m => m.id === server.deviceId));
        const machine = room?.machines.find(m => m.id === server.deviceId);
        return <Flex align={'center'} gap={8}>
            <CloudServerOutlined style={{fontSize: 20}}/>
            <Tooltip
                title={machine ? `${machine.publicAddress}  (${machine.internalAddress ? machine?.internalAddress : '-'})` : ''}>
                {machine && (machine.name ? machine.name : machine.publicAddress)} @
            </Tooltip>
            <div style={{gap: 4}}>
                <Tooltip title={'机房'}>
                    <HomeOutlined style={{fontSize: 18}}/>
                    {room ? room.name : server.name}
                </Tooltip>
            </div>

            <Popconfirm title={`确认要将当前端口${server.status === 'ENABLE' ? '关闭' : '开启'}吗？`}
                        onConfirm={() => enable(server)}>
                <Switch checkedChildren="开启" unCheckedChildren="关闭" defaultChecked
                        checked={server.status === 'ENABLE'}/>
            </Popconfirm>

            <Tooltip title={'重启comfyui docker'}>
                <Popconfirm title={`确认要重启${server.config}的comfyui docker吗？慎重执行！！！`} onConfirm={() => doRestartServer(server, 'CREATIVE')}>
                    <BugOutlined className={'color-brand text16'} />
                </Popconfirm>
            </Tooltip>

            <Tooltip title={'重启ai-toolkit'}>
                <Popconfirm title={`确认要重启${server.config}的ai-toolkit docker吗？慎重执行！！！`} onConfirm={() => doRestartServer(server, 'TRAIN')}>
                    <TransactionOutlined className={'color-brand text16'} />
                </Popconfirm>
            </Tooltip>

            <Tooltip title={'更新comfyui-tools'}>
                <Popconfirm title={`确认要更新${server.config}的更新comfyui-tools吗？`} onConfirm={() => doUpdateCreativeNode(server)}>
                    <MergeOutlined className={'color-brand text16'} />
                </Popconfirm>
            </Tooltip>

            <Tooltip title={'查看ai-toolkit最近的100行日志'}>
                <FileTextOutlined onClick={() => fetchRunLog(server)}
                                  className={'color-brand text14'}/>
            </Tooltip>

            <Tooltip title={'测试所有端口是否可用'}>
                <RedoOutlined onClick={() => testPorts(server)}
                              className={'color-brand text16'}/>
            </Tooltip>
        </Flex>;
    };

    const DeleteServerIcon = ({server, isPort}) => {
        const title = isPort ? '端口' : '服务';

        return <Tooltip title={`删除${title}`}>
            <Popconfirm title={`确认要删除当前${title}吗？`} onConfirm={() => {
                delServer(server.id).then(res => {
                    if (res) {
                        fetchData();
                        message.success('删除成功');
                    }
                });
            }}>
                <CloseCircleOutlined style={{fontSize: 14}} className={'pointer'}/>
            </Popconfirm>
        </Tooltip>;
    };

    const AddPortTitle = ({server}) =>
        <Flex gap={16}>
            <Dropdown menu={getPortMenu(server)} trigger={['click']}>
                <PlusCircleTwoTone style={{fontSize: 14}}/>
            </Dropdown>

            <DeleteServerIcon server={server} isPort={false}/>
        </Flex>;

    return (
        <PageContainer style={{padding: 16}}>
            <Flex vertical gap={16}>
                <Tabs
                    type="editable-card"
                    onChange={(value) => handleSwitch(value)}
                    activeKey={data && index >= 0 ? data[index].id + '' : ''}
                    onEdit={handleEdit}
                    items={items}
                />

                <Flex justify="flex-start" gap={16}>
                    <DeviceMng onChange={fetchData}/>

                    <Button className="models-image-card-button"
                            onClick={() => showUpdate(data[index])}>修改集群配置</Button>

                    <Dropdown menu={getAddServerMenus(index)} trigger={['click']}>
                        <Button className="models-image-card-button" type={'primary'}>新增服务器</Button>
                    </Dropdown>

                    {/*<Tooltip title={'全局更新comfyui-tools'}>*/}
                    {/*    <Popconfirm title={`确认要全量更新comfyui-tools吗？`} onConfirm={() => updateAllCreativeNode()}>*/}
                    {/*        <MergeOutlined className={'color-brand text16'} />*/}
                    {/*    </Popconfirm>*/}
                    {/*</Tooltip>*/}

                    <Button icon={<RedoOutlined/>} onClick={() => {
                        fetchData();
                        reloadDevice();
                        message.success('刷新成功');
                    }}/>
                </Flex>

                <Flex gap={12} wrap={'wrap'}>
                    {data && index >= 0 && data[index].servers && data[index].servers.map(server => (
                        <Card key={server.id} title={buildServerTitle(server)} extra={<AddPortTitle server={server}/>}
                              style={{width: '49%', minWidth: 600}}>
                            <Flex gap={8} vertical>
                                {server.children && server.children.map(port => <Flex key={port.id} vertical>
                                    <Flex gap={8} justify={'space-between'} align={'center'}>
                                        <div style={{width: 40, fontSize: 12}}>{port.id}</div>

                                        <Flex align={'center'} gap={8} style={{width: 70}}>
                                            <div className={'text14 weight'}>{port.config}</div>
                                            {port.deviceId &&
                                                <Tooltip title={port.deviceId}>
                                                    <CheckOutlined style={{fontSize: 16, color: 'blue'}}/>
                                                </Tooltip>
                                            }
                                        </Flex>
                                        <Flex align={'center'} justify={'flex-start'} style={{width: 100}}
                                              className={'text12'} gap={8}>
                                            {port.typeName}
                                            <Dropdown menu={getPortTypeMenu(port)} trigger={['click']}>
                                                <EditOutlined style={{fontSize: 14}}/>
                                            </Dropdown>
                                        </Flex>

                                        <Switch checkedChildren="开启" unCheckedChildren="关闭" defaultChecked
                                                size={'small'} checked={port.status === 'ENABLE'}
                                                onChange={() => enable(port)}/>

                                        <Flex className={'text12 color-brand server-status'} gap={4}
                                              style={{width: 110}} justify={'space-between'}>
                                            <>{buildStatusIcon(port.realtimeStatus)}</>
                                            <Flex gap={4}>
                                                {!['REMOVE_BG','FILE_SERVICE'].includes(port.type) &&
                                                  <>
                                                      <Tooltip title={'重启端口'}>
                                                          <Popconfirm title={`确认要重启${port.config}端口吗？`} onConfirm={() => restartPort(port)}>
                                                            <BugOutlined />
                                                          </Popconfirm>
                                                      </Tooltip>

                                                      <Tooltip title={'查看最近的100行日志'}>
                                                          <FileTextOutlined onClick={() => fetchRunLog(port)}
                                                                            className={'color-brand text14'}/>
                                                      </Tooltip>
                                                  </>

                                                }
                                                <Tooltip title={'测试是否可用'}>
                                                    <RedoOutlined onClick={() => test(port)}
                                                                  className={'color-brand text14'}/>
                                                </Tooltip>
                                            </Flex>
                                        </Flex>

                                        <div className={'text12 color-brand'} style={{width: 60}}>
                                            {port.realtimeTaskId || '-'}
                                        </div>

                                        <div className={'text12'} style={{width: 106}}>
                                            {port.modifyTime?.substring(5)}
                                        </div>

                                        <DeleteServerIcon server={port} isPort={true}/>

                                    </Flex>

                                </Flex>)}
                            </Flex>
                        </Card>

                    ))}
                </Flex>
            </Flex>

            {(update || add) &&
                <Modal open={true} title={'变更集群'} closable={false} width={'800px'} onCancel={() => {
                    setUpdate(null);
                    setAdd(false);
                }} onOk={form.submit} centered maskClosable={false}>
                    <Form labelCol={{span: 4}}
                          wrapperCol={{span: 18}}
                          initialValues={{remember: true}}
                          onFinish={handleCommit}
                          autoComplete="off"
                          form={form}>
                        <Form.Item hidden label="id" name="id"> <Input/> </Form.Item>
                        <Form.Item hidden label="level" name="level"> <Input/> </Form.Item>
                        <Form.Item label="集群名称" name="name" style={{width: '100%'}}
                                   rules={[{required: true, message: '请输入集群名称'}]}>
                            <Input style={{width: 600}}/>
                        </Form.Item>
                        <Form.Item label="备注" name="memo" style={{width: '100%'}}
                                   rules={[{required: false, message: '请输入备注'}]}>
                            <Input style={{width: 600}}/>
                        </Form.Item>
                        <Flex style={{marginLeft: 20}} align={'center'}>
                            <span style={{color: 'red'}}>*</span> 用户关联配置:
                            <div className={'text12 color-9e'}
                                 style={{marginLeft: 20}}>(命中集群权重：用户 &gt; 角色 &gt; 默认)</div>
                        </Flex>
                        <Row gutter={16}>
                            <Col span={4}></Col>
                            <Col span={12}>
                                <Form.Item label={'按用户'} name="user" style={{width: '100%'}}>
                                    <Select options={masterOptions} mode={'multiple'} showSearch allowClear
                                            style={{width: 536}}
                                            optionFilterProp="label"/>
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row gutter={16}>
                            <Col span={4}></Col>
                            <Col span={12}>
                                <Form.Item label={'按角色'} name="role" style={{width: '100%'}}>
                                    <Select options={RoleTypesItems} mode={'multiple'} allowClear style={{width: 536}}/>
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row gutter={16}>
                            <Col span={4}></Col>
                            <Col span={12}>
                                <Form.Item name="isDefault" style={{width: '100%'}} valuePropName={'checked'}>
                                    <Checkbox>是否默认服务器</Checkbox>
                                </Form.Item>
                            </Col>
                        </Row>
                    </Form>
                </Modal>
            }

            {deleteItem &&
                <Modal open={true} title={'删除集群'} onCancel={() => setDeleteItem(null)} onOk={handleDel}>
                    <div style={{color: 'red'}}>确定要删除集群{deleteItem.name}吗？</div>
                </Modal>
            }

            {!!log &&
              <Modal open={true} onCancel={() => setLog('')} centered={true}
                footer={null} width={'90%'} closable={false}>
                  <div ref={logRef} style={{ whiteSpace: 'pre-line', width: 'auto',
                      height:'calc(100vh - 100px)', overflowY: 'auto' }}>
                      <HighlightText content={log} />
                  </div>
              </Modal>
            }

            {showUserQueued &&
                <UserQueueCreativeModal onCancel={()=>setShowUserQueued(false)}/>
            }
            {showBackUserQueued &&
              <UserQueueCreativeModal onCancel={()=>setShowBackUserQueued(false)} isCustomer={false}/>
            }
        </PageContainer>);
};

export default Server;