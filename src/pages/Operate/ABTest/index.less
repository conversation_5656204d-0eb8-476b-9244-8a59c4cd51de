.test-table {
  .ant-table-cell {
    padding: 8px 0 !important;
  }

  .ant-table-wrapper {
    margin-inline: 0 !important;
  }

  .ant-table-wrapper .ant-table-tbody > tr > td > .ant-table-wrapper:only-child .ant-table {
    margin-block: 0 !important;
    margin-inline: unset !important;
  }
}

.scroll-container {
  max-height: calc(90vh - 200px);
  overflow-y: auto;
  padding-right: 8px;
}

.items-container {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px 24px;
  margin-bottom: 16px;
  position: relative;

  .left-index-item {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 1px solid #618DFF;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
  }

  .item-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    border-radius: 8px;

    .mask-text {
      color: #fff;
      font-size: 16px;
      font-weight: 500;

      &.processing::after {
        content: '';
        animation: ellipsis 1.5s infinite steps(4, end);
      }
    }
  }
}

@keyframes ellipsis {
  0% {
    content: '';
  }
  25% {
    content: '.';
  }
  50% {
    content: '..';
  }
  75% {
    content: '...';
  }
  100% {
    content: '';
  }
}

.detail-modal-content {
  padding: 0 12px;
}

.detail-section-title {
  font-size: 16px;
  font-weight: 500;
  color: #1a1a1a;
  position: relative;
  padding-left: 12px;
  margin-bottom: 16px;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: #366EF4;
    border-radius: 2px;
  }
}

.detail-section {
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  font-size: 13px;
  color: #727272;
  margin-bottom: 8px;
}

.detail-value {
  font-size: 14px;
  color: #1a1a1a;

  .preview-image {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 4px;
    margin-right: 12px;
  }
}

.param-title {
  font-weight: 500;
  margin-right: 8px;
}

.param-value {
  color: #366EF4;
}

.result-label {
  margin-right: 8px;
}

.result-value {
  color: #366EF4;
  font-weight: 500;
}

.detail-item {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.full-screen-spin {
  :global {
    .ant-spin-nested-loading {
      height: 100%;

      .ant-spin {
        max-height: unset;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1001;

        .ant-spin-text {
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, 40px);
          margin: 0;
          padding: 0;
          width: 100%;
          text-align: center;
          color: #1677ff;
          font-size: 14px;
        }
      }

      .ant-spin-blur {
        opacity: 0.1;
      }

      .ant-spin-blur::after {
        opacity: 0.1;
      }
    }
  }
}

.detail-modal {
  @keyframes pulse {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.6;
    }
    100% {
      opacity: 1;
    }
  }
}

.experiment-detail-modal {
  @keyframes pulse {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.6;
    }
    100% {
      opacity: 1;
    }
  }

  .ant-modal-content {
    padding: 0;
    overflow: hidden;
    border-radius: 12px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
  }

  .ant-modal-body {
    padding: 0;
    background: #f8f9fa;
    overflow-y: auto;
    flex: 1;
    display: flex;
    justify-content: center;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #d9d9d9;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }

  .experiment-detail-content {
    width: 1100px;
    padding: 10px 14px;

    .experiment-section {
      background: #fff;
      padding: 10px 24px;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      margin-bottom: 10px;
      border: 1px solid rgba(0, 0, 0, 0.02);
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .experiment-section-title {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 8px;

        &::before {
          content: '';
          width: 4px;
          height: 20px;
          background: #1677ff;
          border-radius: 2px;
          display: inline-block;
        }
      }

      .experiment-item {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .experiment-label {
          color: #8c8c8c;
          margin-bottom: 8px;
          font-size: 14px;
          font-weight: 500;
        }

        .experiment-value {
          background: #fafafa;
          padding: 16px;
          border-radius: 8px;
          min-height: 56px;
          display: flex;
          align-items: center;
          transition: all 0.3s;
          border: 1px solid transparent;
          font-size: 14px;
          color: #262626;

          &:hover {
            background: #f5f5f5;
            border-color: #e6e6e6;
          }

          .experiment-image {
            width: 64px;
            height: 64px;
            object-fit: cover;
            border-radius: 8px;
            margin-right: 16px;
            border: 1px solid #f0f0f0;
            transition: all 0.3s;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

            &:hover {
              transform: scale(1.05);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
            }
          }

          .experiment-param-title {
            font-weight: 600;
            color: #262626;
            margin-right: 12px;
          }

          .experiment-param-value {
            color: #1677ff;
            font-weight: 500;
          }

          .experiment-result-label {
            color: #8c8c8c;
            margin-right: 12px;
          }

          .experiment-result-value {
            color: #1677ff;
            font-weight: 600;
            font-size: 16px;
          }
        }
      }
    }
  }

  // 状态标签样式
  .experiment-status-tag {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 6px;

    &.processing {
      background: #e6f4ff;
      color: #1677ff;
    }

    &.finished {
      background: #f6ffed;
      color: #52c41a;
    }

    &.disabled {
      background: #fff2f0;
      color: #ff4d4f;
    }

    &.default {
      background: #f5f5f5;
      color: #8c8c8c;
    }
  }

  // 进度条样式优化
  .experiment-progress {
    position: relative;
    width: 100%;
    padding: 0 16px;

    .ant-progress {
      margin: 0;

      .ant-progress-outer {
        padding-right: 0;
        margin-right: 0;
      }

      .ant-progress-bg {
        height: 12px !important;
        border-radius: 6px;
      }

      .ant-progress-success-bg {
        height: 12px !important;
        border-radius: 6px;
      }
    }
  }
}