import { PageContainer } from '@ant-design/pro-components';
import {
  <PERSON>ton,
  Col,
  Divider,
  Flex,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Progress,
  Radio,
  Row,
  Select,
  Spin,
  Table,
  Tabs,
  Tooltip,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useForm } from 'antd/lib/form/Form';
import IconFont from '@/components/IconFont';
import { ElementConfig, getElementConfig } from '@/services/ElementController';
import { getMaterialModelById, MaterialModel } from '@/services/MaterialModelController';
import {
  createTestPlan,
  deleteTestItemById,
  enableTestPlan,
  queryTestPlanByPage,
  queryTestResult,
  TestItem,
  TestItemGroup,
  TestPlan,
  updateTestPlan,
} from '@/services/TestController';
import {
  CloseCircleOutlined,
  CopyOutlined,
  DiffOutlined,
  EyeOutlined,
  InteractionOutlined,
  QuestionCircleOutlined,
  RedoOutlined,
} from '@ant-design/icons';
import { ImageCompareModal } from '@/components/Common/ImageComponent';
import MasterSelector from '@/components/MasterSelector';
import './index.less';
import {
  type ComponentConfig,
  getParamConfig,
  getConfigData,
  paramType,
  type Props,
  selectCommonParams,
} from '@/components/Operate/ABTest/ParamsSelectList';
import { formatText } from '@/utils/utils';
import { flattenObject } from '@/utils/objectUtils';
import TextArea from 'antd/lib/input/TextArea';
import AnalysisModal from '@/components/Operate/AnalysisModal';
import TrainParamList from '@/components/Operate/ABTest/TrainParamList';


// 扩展 ElementConfig 接口
interface ExtendedElementConfig extends ElementConfig {
  previewUrl?: string;
}

// Lora 详情
interface LoraDetail {
  id: number;
  name: string;
  showImage: string;
}

// 分析结果
const ABTest: React.FC = () => {


  // 实验计划
  const [data, setData] = useState<Array<TestPlan>>([]);

  // 脸和场景列表
  const [faceList, setFaceList] = useState<Array<ExtendedElementConfig>>([]);
  const [sceneList, setSceneList] = useState<Array<ExtendedElementConfig>>([]);

  // 实验结果
  const [compareGroups, setCompareGroups] = useState<Array<TestItemGroup>>([]);
  const [compareItem, setCompareItem] = useState<TestItem | null>(null);

  // 搜索条件
  const [searchUserId, setSearchUserId] = useState<number | undefined>(undefined);
  const [searchName, setSearchName] = useState<string | undefined>(undefined);
  const [searchId, setSearchId] = useState<number | undefined>(undefined);
  const [searchStatus, setSearchStatus] = useState<string | undefined>(undefined);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);

  // 新增和编辑
  const [add, setAdd] = useState(false);
  const [opItem, setOpItem] = useState<TestPlan | null>(null);
  const [finishId, setFinishId] = useState<number | null>(null);
  const [conclusion, setConclusion] = useState<string | null>(null);

  // 表单 
  const [form] = useForm();

  // 删除实验项
  const [deletingIndex, setDeletingIndex] = useState<number | null>(null);

  // 当前选中的实验计划
  const [currentSelectTestPlan, setCurrentSelectTestPlan] = useState<TestItem | null>(null);

  // 修改隐藏组件的状态管理，按实验项分开存储
  const [hiddenParams, setHiddenParams] = useState<{
    [itemName: number]: {
      name?: string,
      type?: string,
      label?: string,
      fullKey?: string,
    }
  }>({});

  // 加载中
  const [loading, setLoading] = useState(false);

  // 分析结果
  const [showAnalysisModal, setShowAnalysisModal] = useState(false);
  const [analysisItem, setAnalysisItem] = useState<TestPlan>();

  // 当前激活的标签页
  const [activeKey, setActiveKey] = useState<string>('0');

  // 获取数据
  useEffect(() => {
    fetchData();
    fetchElements();
  }, []);

  useEffect(() => {
    fetchData();
  }, [page, pageSize]);

  useEffect(() => {
    setPage(1);
    fetchData();
  }, [searchUserId, searchId, searchName, searchStatus]);

  // 获取实验计划
  const fetchData = () => {
    queryTestPlanByPage({
      pageNum: page,
      pageSize,
      userId: searchUserId,
      id: searchId,
      nameLike: searchName?.trim(),
      status: searchStatus,
    }).then(res => {
      if (!res) return;
      const data = res.list ? res.list : [];
      data.forEach(e => {
        let idx = 1;
        e.items.forEach(i => {
          i.idx = idx++;
        });
      });
      //@ts-ignore
      setData(res.list ? res.list : []);
      setTotal(res.totalCount ? res.totalCount : 0);
    });
  };

  //获取脸和场景
  const fetchElements = () => {
    getElementConfig('CREATE_IMAGE').then(cfg => {
      if (!cfg) return;

      for (let c of cfg) {
        if (c.configKey === 'FACE') {
          setFaceList(c.children);
        }
        if (c.configKey === 'SCENE') {
          setSceneList(c.children);
        }
      }
    });
  };

  // 新增实验
  const handleAdd = () => {
    form.resetFields();
    setOpItem(null);
    setAdd(true);

    // 获取所有公共参数配置
    const commonParams = selectCommonParams(0, true, {
      form,
      faceList,
      sceneList,
      onChange: (item, name) => {
        form.setFieldValue(name, item?.id);
      },
    });

    // 只设置有默认值的参数
    const defaultSharedParams = {};
    Object.entries(commonParams).forEach(([key, config]) => {
      if (config.defaultValue !== undefined) {
        defaultSharedParams[key] = config.defaultValue;
      }
    });

    // 设置默认值
    const defaultValues = {
      items: [{
        sharedParams: {
          ...defaultSharedParams,
          configs: {},
        },
        comparisonParams: [],
      }],
    };
    form.setFieldsValue(defaultValues);
  };

  // 新增实验项
  const handleAddItem = () => {
    let items = form.getFieldValue('items') || [];

    // 获取所有公共参数配置
    const commonParams = selectCommonParams(items.length, true, {
      form,
      faceList,
      sceneList,
      onChange: (item, name) => {
        form.setFieldValue(name, item?.id);
      },
    });

    // 只设置有默认值的参数
    const defaultSharedParams = {};
    Object.entries(commonParams).forEach(([key, config]) => {
      if (config.defaultValue !== undefined) {
        defaultSharedParams[key] = config.defaultValue;
      }
    });

    const newItem = {
      status: 'ENABLED',
      id: null,
      sharedParams: {
        ...defaultSharedParams,
        configs: {},
      },
      comparisonParams: [],
    };

    items = [...items, newItem];
    form.setFieldsValue({ items });
  };

  // 删除实验项
  const handleDelItem = (idx) => {
    setDeletingIndex(idx); // 设置选中的实验项
    const items = form.getFieldValue('items');
    const itemToDelete = items[idx];

    const deleteAndUpdateState = () => {
      // 删除实验项
      items.splice(idx, 1);

      // 重新设置整个表单的值，以确保所有字段都正确更新
      const currentFormValues = form.getFieldsValue();
      const updatedFormValues = {
        ...currentFormValues,
        items: items.map((item, newIndex) => ({
          ...item,
          sharedParams: {
            ...item.sharedParams,
            loraId: item.sharedParams.loraId, // 确保 loraId 正确保留
          },
          comparisonParams: item.comparisonParams || [],
          // 清空服饰相关字段
          clothesParams: null,
          clothesConfig: null,
        })),
      };
      form.setFieldsValue(updatedFormValues);

      // 更新 hiddenParams，重新映射索引
      const newHiddenParams = {};
      Object.keys(hiddenParams).forEach(key => {
        const keyIndex = parseInt(key);
        if (keyIndex < idx) {
          newHiddenParams[keyIndex] = hiddenParams[keyIndex];
        } else if (keyIndex > idx) {
          newHiddenParams[keyIndex - 1] = hiddenParams[keyIndex];
        }
      });
      setHiddenParams(newHiddenParams);
      setDeletingIndex(null);
    };

    // 删除实验项
    if (itemToDelete.id) {
      deleteTestItemById(itemToDelete.id).then(res => {
        if (!res) return;
        message.success('删除成功');
        deleteAndUpdateState();
      }).catch(err => {
        message.error('删除失败');
        setDeletingIndex(null);
      });
    } else {
      deleteAndUpdateState();
    }
  };

  const handleCopy = (record, index) => {
    if (!record) return;

    // 复制实验项数据
    const { id, ...others } = record;
    const items = form.getFieldValue('items') || [];
    items.push({ ...others, status: 'ENABLED' });
    const currentIdx = items.length - 1;

    // 复制实验参数状态
    if (hiddenParams[index]) {
      setHiddenParams(prev => ({
        ...prev,
        [currentIdx]: {
          ...hiddenParams[index],  // 完整复制所有状态，包括 name、type、label
        },
      }));
    }

    // 更新表单值
    const currentFormValues = form.getFieldsValue();
    const updatedFormValues = { ...currentFormValues, items };
    form.setFieldsValue(updatedFormValues);
  };

  const commitConclusion = () => {
    if (!finishId || !conclusion) {
      message.error('请填写结论');
      return;
    }
    // commitConclusion(finishId, conclusion).then(res => {
    //   if (!res) return;
    //   message.success('提交成功');
    //   setFinishId(null);
    //   setConclusion(null);
    // });
  };

  // 编辑实验
  const handleEdit = (record) => {
    form.resetFields();

    // 提取并格式化 comparisonParams
    const formattedComparisonParams = record.items.map(item => {
      return item.groups.map(group => ({
        key: group.comparisonParams.key,
        value: group.comparisonParams.value === 'true' ? true : (group.comparisonParams.value === 'false' ? false : group.comparisonParams.value),
      }));
    });

    // 处理每个实验项的数据
    const formattedItems = record.items.map((item, index) => {

      // 设置隐藏参数
      if (item.groups[0]?.comparisonParams) {
        const paramKey = item.groups[0].comparisonParams.key;
        
        // 查找对应的组件配置
        // 遍历所有可能的组件配置，找到匹配的
        const allConfigs = getConfigData(index, true, {
          form,
          faceList,
          sceneList,
        });
        
        // 在所有配置中查找匹配该key的配置
        let matchedConfigKey: string | null = null;
        for (const [configKey, config] of Object.entries(allConfigs)) {
          if (config.name && config.name.join('.') === paramKey) {
            matchedConfigKey = configKey;
            break;
          } else if (configKey === paramKey) {
            matchedConfigKey = configKey;
            break;
          }
        }
        
        // 如果找到匹配的配置，使用它的key
        const nameToStore = matchedConfigKey || (paramKey === 'trainParam' ? 'loraId' : paramKey);
        
        // 获取参数配置来获取标签
        const paramConfig = getParamConfig(nameToStore, index, true, {
          form,
          faceList,
          sceneList,
        });

        // 设置隐藏参数
        setHiddenParams(prev => ({
          ...prev,
          [index]: {
            name: nameToStore,
            label: paramConfig?.label || paramKey,
            type: paramKey === 'trainParam' ? paramType.trainPic : paramType.creativePic,
            // 存储完整key，用于后续比较
            fullKey: paramKey,
          },
        }));
      }

      return {
        ...item,
        comparisonParams: formattedComparisonParams[index],
        sharedParams: {
          ...item.sharedParams,
          // 处理 loraId
          loraId: typeof item.sharedParams.loraId === 'object' ? item.sharedParams.loraId.id : item.sharedParams.loraId,
        },
      };
    });

    // 设置表单值
    const formValues = {
      ...record,
      items: formattedItems,
    };

    // 设置表单值
    form.setFieldsValue(formValues);
    setOpItem(record);
  };

  // 停止实验
  const handleStop = (id) => {
    enableTestPlan(id, false).then(res => {
      if (!res) return;
      message.success('暂停成功');
      fetchData();
    });
  };

  // 打分
  const handleCompare = async (item: TestItem) => {
    if (item === null) return;

    try {
      // 获取实验结果
      const res = await queryTestResult(item.id);
      if (!res) return;

      // 设置当前选中的实验计划
      setCurrentSelectTestPlan(item);

      // 先进行排序
      const sortedResults = res.map(result => ({
        ...result,
        results: [...result.results].sort((a, b) => a.roundId - b.roundId),
      }));

      // 设置实验结果
      setCompareGroups(sortedResults);
      setCompareItem(item);

    } catch (error) {
      message.error('获取实验结果失败');
    }
  };

  // 关闭弹窗
  const handleClose = () => {
    setAdd(false);
    setOpItem(null);
    // 清空实验参数和隐藏参数
    setHiddenParams({});
  };

  // 分页
  const handlePageChange = (page: number, pageSize?: number) => {
    setPage(page);
    if (pageSize) {
      setPageSize(pageSize);
    }
  };

  // 提交实验
  const handleCommit = async (values) => {
    try {
      // 触发表单验证
      await form.validateFields();

      // 检查实验参数设置
      if (!validateExperimentParams(values.items)) {
        return;
      }

      // 如果是编辑状态，合并 opItem 的属性
      const mergedValues = opItem ? { ...opItem, ...values } : values;

      // 处理 items 数据
      const updatedItems = mergedValues.items.map((item, index) => {
        // 处理实验参数，只有训练参数时将key替换为paramList
        const processedComparisonParams = item.comparisonParams?.map(param => {
          // 检查是否为训练参数
          const isTrainParam = hiddenParams[index]?.type === paramType.trainPic;

          // 如果是训练参数，替换key为trainParam
          const paramKey = isTrainParam ? 'trainParam' : param.key;

          return {
            ...param,
            key: paramKey,
          };
        });

        // 确保实验参数中的键在sharedParams中不再存在
        const cleanedSharedParams = { ...item.sharedParams };
        if (item.comparisonParams && item.comparisonParams.length > 0) {
          const experimentParamKey = item.comparisonParams[0].key;
          
          // 对于普通参数，直接从sharedParams中删除
          if (!experimentParamKey.includes('.')) {
            delete cleanedSharedParams[experimentParamKey];
          } 
          // 对于嵌套参数，需要解析路径并删除
          else {
            const pathParts = experimentParamKey.split('.');
            let currentObj = cleanedSharedParams;
            
            // 遍历路径除了最后一个部分
            for (let i = 0; i < pathParts.length - 1; i++) {
              if (!currentObj[pathParts[i]]) break;
              currentObj = currentObj[pathParts[i]];
            }
            
            // 删除最后一个属性
            if (currentObj) {
              delete currentObj[pathParts[pathParts.length - 1]];
            }
          }
        }

        return {
          ...item,
          comparisonParams: processedComparisonParams,
          sharedParams: {
            ...cleanedSharedParams,
            // 处理 loraId
            loraId: typeof item.sharedParams.loraId === 'object' ? item.sharedParams.loraId.id : item.sharedParams.loraId,
          },
        };
      });

      // 更新实验项的 ID
      const method = add ? createTestPlan : updateTestPlan;

      // 提交实验
      const response = await method({ ...mergedValues, items: updatedItems });
      if (response) {
        message.success(`${add ? '新增' : '修改'}成功`);
        resetFormState();
        fetchData();
      }
    } catch (error) {
      console.error('提交失败:', error);
    }
  };

  // 验证实验参数设置
  const validateExperimentParams = (items) => {
    for (let i = 0; i < items.length; i++) {
      if (!hiddenParams[i] || !hiddenParams[i].name) {
        message.error(`实验项 ${i + 1} 必须设置一个实验参数`);
        return false;
      }
    }
    return true;
  };


  // 重置表单状态
  const resetFormState = () => {
    setHiddenParams({});
    handleClose();
  };


  // 实验项详情弹窗
  const showDetailModal = async (item: TestItem | null) => {
    if (!item) return;

    // 获取服饰信息
    const getElementDetail = async (id: number): Promise<LoraDetail | null> => {
      if (!id) return null;
      try {
        const res = await getMaterialModelById(id);
        return res ? {
          id: res.id,
          name: res.name,
          showImage: res.showImage,
        } : null;
      } catch (error) {
        console.error('获取服饰信息失败:', error);
        return null;
      }
    };

    // 获取共享参数中的服饰信息
    let sharedLoraDetail: LoraDetail | null = null;
    if (item.sharedParams.loraId) {
      sharedLoraDetail = await getElementDetail(item.sharedParams.loraId);
    }

    // 获取所有需要的服饰信息
    const loraPromises = item.groups.map(async (group) => {
      if ((group as any).comparisonParams?.key === 'loraId') {
        const loraDetail = await getElementDetail((group as any).comparisonParams.value);
        return {
          ...group,
          loraDetail,
        };
      }
      return group;
    });

    // 获取显示值
    const buildDisplayValue = (key, value, config) => {
      let displayValue: unknown = '';
      if (config.type === 'select' && config.dataList) {
        displayValue = config.dataList.find(opt => opt.value === value)?.label || '';
      } else if (config.type === 'checkbox') {
        displayValue = value ? '是' : '否';
      } else if (key === 'imageNum') {
        displayValue = `${item.roundsNum}张`;
      } else {
        displayValue = value;
      }

      return displayValue;
    };

    // 获取参数配置
    const getParamConfigByItem = (item, excludesKey) => {
      const configValueList: Array<{ config, value }> = [];
      
      // 先收集所有的实验参数键，包括嵌套键
      const experimentParamKeys = item.groups.map(group => (group as any).comparisonParams?.key || '');
      
      Object.entries(item.sharedParams).forEach(([key, value]) => {
        // 跳过特殊处理的参数
        if (excludesKey.includes(key)) {
          return;
        }

        // 如果该参数被设置为实验参数，则不显示
        if (experimentParamKeys.includes(key)) {
          return;
        }

        const config = getParamConfig(key, 0, true, { form, faceList, sceneList });

        if (config) {
          configValueList.push({ config, value: buildDisplayValue(key, value, config) });
          return;
        }

        if (value !== null && typeof value === 'object') {
          const result = flattenObject(value, key) || [];
          result.forEach(e => {
            // 检查嵌套参数是否是实验参数
            const fullKey = e.key;
            if (experimentParamKeys.includes(fullKey)) {
              return; // 如果是实验参数，跳过
            }
            
            const subConfig = getParamConfig(e.key, 0, true, { form, faceList, sceneList });
            if (subConfig) {
              configValueList.push({ config: subConfig, value: buildDisplayValue(e.key, e.value, subConfig) });
            }
          });
        }
      });

      // 去重，确保不会有重复的参数
      const uniqueList = configValueList.filter((item, index, self) => 
        index === self.findIndex(t => t.config.key === item.config.key)
      );

      return uniqueList;
    };

    // 获取服饰信息
    const groupsWithLora = await Promise.all(loraPromises);

    // 弹窗
    Modal.info({
      title: (
        <div style={{
          fontSize: '20px',
          fontWeight: 600,
          padding: '24px',
          borderBottom: '1px solid #f0f0f0',
          color: '#262626',
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
        }}>
          <div style={{ width: 4, height: 20, background: '#1677ff', borderRadius: 2 }} />
          实验项详情
        </div>
      ),
      width: 1200,
      centered: true,
      icon: null,
      closable: true,
      footer: null,
      className: 'experiment-detail-modal',
      content: (
        <div className="experiment-detail-content">
          {/* 基本信息 */}
          <div className="experiment-section">
            <div className="experiment-section-title">基本信息</div>
            <Row gutter={[24, 16]}>
              <Col span={12} className="experiment-item">
                <div className="experiment-label">实验项名称</div>
                <div className="experiment-value">{item.name}</div>
              </Col>
              <Col span={12} className="experiment-item">
                <div className="experiment-label">状态</div>
                <div className="experiment-value">
                  <span className={`experiment-status-tag ${item.status === 'PROCESSING' ? 'processing' :
                    item.status === 'FINISHED' ? 'finished' :
                      item.status === 'DISABLED' ? 'disabled' : 'default'
                  }`}>
                    {item.statusName}
                  </span>
                </div>
              </Col>
            </Row>
          </div>

          {/* 公共参数 */}
          {item.sharedParams && (
            <div className="experiment-section">
              <div className="experiment-section-title">公共参数</div>
              {/* 服饰、模特、场景占用第一行 */}
              <Row gutter={[24, 24]}>
                {['loraId', 'face', 'scene'].map(specialKey => {
                  // 如果该参数被设置为实验参数，则不显示
                  if (item.groups.some(group => (group as any).comparisonParams?.key === specialKey)) {
                    return null;
                  }

                  const paramConfig = getParamConfig(specialKey, 0, true, {
                    form,
                    faceList,
                    sceneList,
                  });

                  if (!paramConfig || !paramConfig.isShowParams) {
                    return null;
                  }

                  if (specialKey === 'face' || specialKey === 'scene') {
                    const elementList = specialKey === 'face' ? faceList : sceneList;
                    const configKey = specialKey === 'face' ? '1' : '2';
                    const elementItem = elementList.find(option => option.id === item.sharedParams.configs[configKey]?.[0]);

                    return (
                      <Col span={8} key={specialKey} className="experiment-item">
                        <div className="experiment-label">{paramConfig.label}</div>
                        <div className="experiment-value">
                          <Flex align="center">
                            {elementItem?.showImage && (
                              <img src={elementItem.showImage} alt={`${paramConfig.label}预览`}
                                   className="experiment-image" />
                            )}
                            <span>{elementItem?.name || ''}</span>
                          </Flex>
                        </div>
                      </Col>
                    );
                  }

                  if (specialKey === 'loraId') {
                    return (
                      <Col span={8} key={specialKey} className="experiment-item">
                        <div className="experiment-label">{paramConfig.label}</div>
                        <div className="experiment-value">
                          <Flex align="center">
                            {sharedLoraDetail?.showImage && (
                              <img src={sharedLoraDetail.showImage} alt="服饰预览" className="experiment-image" />
                            )}
                            <span>{sharedLoraDetail?.name || item.sharedParams[specialKey]}</span>
                          </Flex>
                        </div>
                      </Col>
                    );
                  }

                  return null;
                })}
              </Row>

              {/* 其他参数从第二行开始 */}
              <Row gutter={[24, 24]} style={{ marginTop: 8 }}>
                {getParamConfigByItem(item, ['loraId', 'face', 'scene']).map(({ config, value }) => (
                  <Col span={config.colSpan || 8} key={config.key} className="experiment-item">
                    <div className="experiment-label">{config.label}</div>
                    <div className="experiment-value">{value}</div>
                  </Col>
                ))}
              </Row>
            </div>
          )}

          {/* 实验参数 */}
          {groupsWithLora.length > 0 && (
            <div className="experiment-section">
              <div className="experiment-section-title">实验参数</div>
              <Row gutter={[24, 24]}>
                {groupsWithLora.map((group, index) => {
                  const paramKey = (group as any).comparisonParams?.key;
                  const paramValue = (group as any).comparisonParams?.value;
                  const paramConfig = getParamConfig(paramKey, 0, true, {
                    form,
                    faceList,
                    sceneList,
                  });

                  if (!paramConfig) return null;

                  // 获取显示值
                  let displayValue = paramValue;
                  if (paramKey === 'face' || paramKey === 'scene') {
                    const elementList = paramKey === 'face' ? faceList : sceneList;
                    const elementItem = elementList.find(option => option.id === paramValue[0]);
                    displayValue = <Flex align="center">
                      {elementItem?.showImage && (
                        <img src={elementItem.showImage} alt={`${paramConfig.label}预览`}
                             className="experiment-image" />
                      )}
                      <span>{elementItem?.name || ''}</span>
                    </Flex>;
                  } else if (paramConfig.type === 'select' && paramConfig.dataList) {
                    displayValue = paramConfig.dataList.find(opt => opt.value === paramValue)?.label || paramValue;
                  } else if (paramConfig.type === 'checkbox') {
                    displayValue = paramValue ? '是' : '否';
                  }

                  return (
                    <Col span={12} key={index} className="experiment-item">
                      <div className="experiment-label">{index === 0 ? '实验组' : '对照组'}</div>
                      {(group as any).comparisonParams && (
                        <div className="experiment-value">
                          <span className="experiment-param-title">
                            {paramConfig.label || paramKey}:
                          </span>
                          {paramKey === 'loraId' && (group as any).loraDetail ? (
                            <div className="experiment-param-value">
                              <Flex align="center">
                                <img
                                  src={(group as any).loraDetail.showImage}
                                  alt="服饰预览"
                                  className="experiment-image"
                                />
                                <span>{(group as any).loraDetail.name}</span>
                              </Flex>
                            </div>
                          ) : (
                            <span className="experiment-param-value">
                              {displayValue}
                            </span>
                          )}
                        </div>
                      )}
                    </Col>
                  );
                })}
              </Row>
            </div>
          )}

          {/* 实验结果 */}
          {item.groups && item.groups.length > 0 && (
            <div className="experiment-section">
              <div className="experiment-section-title">实验结果</div>
              <Row gutter={[24, 24]}>
                {item.groups.map((group, index) => (
                  <Col span={12} key={index} className="experiment-item">
                    <div className="experiment-label">{index === 0 ? '实验组' : '对照组'}</div>
                    <div className="experiment-value">
                      <span className="experiment-result-label">正向结果数:</span>
                      <span className="experiment-result-value">{group.positiveNum || 0}</span>
                    </div>
                  </Col>
                ))}
                <Col span={24} className="experiment-item">
                  <div className="experiment-label">项目进度</div>
                  <div className="experiment-value" style={{ padding: '24px 16px' }}>
                    <Flex gap={24} align="center" style={{ width: '100%' }}>
                      <div style={{ flex: 1 }}>
                        <div className="text12 color-6f mb-4">实验组</div>
                        <ProgressBlock
                          total={item.roundsNum}
                          positiveNum={item.groups.find(g => g.groupType === 'EXPERIMENTAL')?.positiveNum || 0}
                          negativeNum={item.groups.find(g => g.groupType === 'EXPERIMENTAL')?.negativeNum || 0}
                        />
                      </div>
                      <div>VS</div>
                      <div style={{ flex: 1 }}>
                        <div className="text12 color-6f mb-4">对照组</div>
                        <ProgressBlock
                          total={item.roundsNum}
                          positiveNum={item.groups.find(g => g.groupType === 'CONTROL')?.positiveNum || 0}
                          negativeNum={item.groups.find(g => g.groupType === 'CONTROL')?.negativeNum || 0}
                          positiveColor="#1677FF"
                        />
                      </div>
                    </Flex>
                  </div>
                </Col>
              </Row>
            </div>
          )}
        </div>
      ),
    });
  };

  // 进度条
  const ProgressBlock = ({ positiveNum, negativeNum, total, positiveColor = '#52c41a', negativeColor = '#ff4d4f' }) => (
    <Tooltip title={`正向${positiveNum},负向${negativeNum}`}>
      <div className="experiment-progress" style={{ position: 'relative' }}>
        <Progress
          percent={Math.round((negativeNum + positiveNum) * 100 / total)}
          success={{
            percent: Math.round(positiveNum * 100 / total),
            strokeColor: positiveColor,
          }}
          strokeColor={negativeColor}
          showInfo={false}
        />
        <div style={{ position: 'absolute', left: 0, top: 4, width: '100%', height: '100%' }}>
          <Flex align={'center'} justify={'center'} className={'text12'}>
            {`${positiveNum} / ${negativeNum + positiveNum}`}
          </Flex>
        </div>
      </div>
    </Tooltip>
  );

  // 判断实验项是否可编辑
  const isItemEditable = (status: string) => {
    return !['PROCESSING', 'COMPARING', 'DISABLED'].includes(status);
  };

  // 实验项列
  const itemColumns: any[] = [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 40, align: 'center' },
    {
      title: 'idx', dataIndex: 'idx', width: 220, key: 'idx', render: (value, record) => (
        <div>
          <span style={{ marginRight: 5 }}>
            {record.name &&
              <Tooltip title={record.name}>{formatText(record.name, 10)}</Tooltip>
            }
            {!record.name && '实验' + value}
          </span>
          <Tooltip title="查看详细信息">
            <EyeOutlined style={{ cursor: 'pointer', color: '#1677ff' }}
                         onClick={(e) => {
                           e.stopPropagation();
                           showDetailModal(record);
                         }} />
          </Tooltip>
        </div>
      ),
    },
    { title: '状态', dataIndex: 'statusName', width: 60, key: 'statusName', align: 'center' },
    { title: '轮数', dataIndex: 'roundsNum', width: 40, key: 'roundsNum', align: 'center' },
    {
      title: '实验结果',
      dataIndex: 'roundsNum',
      key: 'roundsNum',
      width: 300,
      render: (_, record: TestItem) => {
        const experimentalGroup = record.groups.find(g => g.groupType === 'EXPERIMENTAL');
        const controlGroup = record.groups.find(g => g.groupType === 'CONTROL');

        return (
          <Flex gap={8} align="center" style={{ width: '100%' }}>
            <div style={{ flex: 1 }}>
              <ProgressBlock
                total={experimentalGroup?.roundsNum || 0}
                positiveNum={experimentalGroup?.positiveNum || 0}
                negativeNum={experimentalGroup?.negativeNum || 0}
              />
            </div>
            <div>vs</div>
            <div style={{ flex: 1 }}>
              <ProgressBlock
                total={controlGroup?.roundsNum || 0}
                positiveNum={controlGroup?.positiveNum || 0}
                negativeNum={controlGroup?.negativeNum || 0}
                positiveColor="#1677FF"
              />
            </div>
          </Flex>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      minWidth: 30,
      // width: 190,
      render: (_, record: TestItem) => {
        return (
          <Flex gap={8}>
            {(record.status === 'COMPARING' || record.status === 'FINISHED') && (
              <Button type="link" size={'small'} onClick={() => handleCompare(record)}>打分</Button>
            )}
          </Flex>
        );
      },
    },
  ];

  // 服饰改变
  const handleLoraChange = (item: MaterialModel | null, name: any) => {
    const id = item ? item.id : null;
    const [index, , paramIndex] = name;  // 解构获取索引和参数索引

    // 获取当前表单值
    const currentValues = form.getFieldsValue();
    const currentItem = currentValues.items[index];

    // 确保 comparisonParams 存在
    if (!currentItem.comparisonParams) {
      currentItem.comparisonParams = [
        { key: 'loraId', value: null },
        { key: 'loraId', value: null },
      ];
    }

    // 设置实验参数值
    currentItem.comparisonParams[paramIndex] = {
      key: 'loraId',
      value: id,
    };

    // 删除共享参数中的 loraId
    if (currentItem.sharedParams) {
      delete currentItem.sharedParams.loraId;
    }

    // 更新表单值
    form.setFieldsValue(currentValues);
  };

  // 获取组件配置
  const getComponentConfig = (key: string, index: number, isExperimental: boolean): ComponentConfig | null => {
    const props: Props = {
      form,
      faceList,
      sceneList,
      onChange: handleLoraChange,
    };
    return getParamConfig(key, index, isExperimental, props);
  };

  const addToExperiment = (index: number, key: string, type: any) => {

    // 获取当前的表单值
    const currentValues = form.getFieldsValue();
    const currentItem = currentValues.items[index];

    // 获取参数配置
    const paramConfig = getParamConfig(key, index, true, {
      form,
      faceList,
      sceneList,
    });

    // 删除公共参数中对应的值
    if (paramConfig?.name) {
      // 处理嵌套路径的参数，如 ['testContext', 'loraStrength']
      let currentObj = currentItem.sharedParams;
      const path = paramConfig.name;
      
      if (path.length > 1) {
        // 嵌套路径情况：循环创建和访问嵌套对象
        let target = currentObj;
        for (let i = 0; i < path.length - 1; i++) {
          if (!target[path[i]]) {
            // 如果中间路径不存在，则创建
            break;
          }
          target = target[path[i]];
        }
        
        // 删除最终属性
        if (target) {
          delete target[path[path.length - 1]];
        }
      } else {
        // 简单情况：直接删除
        delete currentObj[path[0]];
      }
    } else {
      // 直接参数情况
      delete currentItem.sharedParams[key];
    }

    // 设置实验参数
    const paramKey = paramConfig?.name ? paramConfig.name.join('.') : key;
    currentItem.comparisonParams = [
      {
        key: paramKey,
        value: paramConfig?.type === 'checkbox' ? !paramConfig.defaultValue : undefined,
      },  // 实验组
      {
        key: paramKey,
        value: paramConfig?.type === 'checkbox' ? (paramConfig.defaultValue || false) : undefined,
      },  // 对照组
    ];

    // 更新表单值
    form.setFieldsValue(currentValues);

    // 为当前实验项的公共参数添加type属性
    const experimentType = type === paramType.trainPic ? 'TRAIN' : 'CREATIVE';
    form.setFieldValue(['items', index, 'type'], experimentType);

    // 设置当前实验项的隐藏参数
    setHiddenParams(prev => ({
      ...prev,
      [index]: {
        name: key,  // 存储原始key
        type: type,
        label: paramConfig?.label || key,
        fullKey: paramKey,  // 存储完整路径key
      },
    }));
  };

  // 修改删除实验参数的处理函数
  const handleRemoveExperimentParam = (index: number, key: string) => {
    // 清除当前实验项的隐藏参数
    setHiddenParams(prev => {
      const newParams = { ...prev };
      delete newParams[index];
      return newParams;
    });

    // 清空表单中对应的 comparisonParams 数据
    const items = form.getFieldValue('items');
    if (items && items[index]) {
      items[index].comparisonParams = [];
      form.setFieldValue('items', items);
    }
  };

  // 获取实验参数配置
  const getExperimentConfig = (index: number, hiddenParams: any) => {
    // 获取实验参数
    const experimentParam = hiddenParams[index];
    if (!experimentParam || !experimentParam.name) return null;

    // 获取配置
    const config = getComponentConfig(experimentParam.name, index, true);
    // 获取对照组配置
    const controlConfig = getComponentConfig(experimentParam.name, index, false);
    if (!config || !controlConfig) return null;

    // 获取值
    const isCheckbox = config.type === 'checkbox';

    // 获取值
    const group1Value = isCheckbox && config.defaultValue !== null && config.defaultValue !== undefined ? !config.defaultValue : undefined;
    // 获取值 
    const group2Value = isCheckbox && config.defaultValue !== null && config.defaultValue !== undefined ? config.defaultValue : undefined;


    return {
      experimentParam,
      config,
      controlConfig,
      isCheckbox,
      group1Value,
      group2Value,
    };
  };

  // 渲染实验参数
  const renderExperimentParams = (index: number) => {
    // 获取当前实验项的隐藏参数
    const configs = getExperimentConfig(index, hiddenParams);
    if (!configs) return null;

    // 渲染实验参数
    const ItemBlock = ({ idx, title, defaultValue, config }) => <Flex vertical style={{ flex: 1 }}>
      <div className="text12 color-72">{title}</div>
      <Form.Item
        label={configs.experimentParam.label}
        required={config.required}
        name={[index, 'comparisonParams', idx, 'value']}
        valuePropName={configs.isCheckbox ? 'checked' : 'value'}
        initialValue={defaultValue}
        rules={config.required ? [{ required: true, message: `请设置${configs.experimentParam.label}` }] : undefined}>
        {config.component}
      </Form.Item>

      <Form.Item hidden name={[index, 'comparisonParams', idx, 'key']}
                 initialValue={config.name ? config.name.join('.') : configs.experimentParam.name} />
    </Flex>;

    return (
      <Row key={`${index}-${configs.experimentParam.name}`}>
        <Col span={24}>
          <Flex align="center" gap={8}>
            <ItemBlock title={'实验组'} idx={0} defaultValue={configs.group1Value} config={configs.config} />

            <div className="experiment-group-divider" />

            <ItemBlock title={'对照组'} idx={1} defaultValue={configs.group2Value} config={configs.controlConfig} />

            <Popconfirm title="确认删除" description="您确定要删除这个实验参数吗？"
                        onConfirm={() => handleRemoveExperimentParam(index, configs.experimentParam.name)} okText="确定"
                        cancelText="取消">
              <CloseCircleOutlined />
            </Popconfirm>
          </Flex>
        </Col>
      </Row>
    );
  };


  // 渲染训练参数
  const renderTrainParams = (index: number) => {
    // 获取实验配置
    const configs = getExperimentConfig(index, hiddenParams);
    if (!configs) return null;

    // 渲染训练参数列表
    return (
      <TrainParamList
        index={index}
        onRemove={handleRemoveExperimentParam}
        form={form}
      />
    );
  };

  // 实验计划列
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 40,
      align: 'center' as 'center',
    },
    {
      title: '计划名称',
      dataIndex: 'name',
      key: 'name',
      width: 140,
      align: 'center' as 'center',
      render: (_, record: TestPlan) => (
        <Flex gap={4}>
          <div>{record.name}</div>
          {record.memo && (
            <Tooltip title={record.memo} styles={{ body: { width: 198 } }}>
              <QuestionCircleOutlined style={{ fontSize: 16 }} className={'margin-left-4 color-96'} />
            </Tooltip>
          )}
        </Flex>
      ),
    },
    {
      title: '状态',
      dataIndex: 'statusName',
      key: 'statusName',
      width: 60,
      align: 'center' as 'center',
    },
    {
      title: '实验项目',
      dataIndex: 'items',
      key: 'items',
      width: 500,
      align: 'center' as 'center',
      render: (items) => (
        <Table
          showHeader={false}
          dataSource={items}
          pagination={false}
          rowKey="id"
          columns={itemColumns}
        />
      ),
    },
    {
      title: '实验结果',
      dataIndex: 'statusName',
      key: 'statusName',
      width: 200,
      align: 'center' as 'center',
      render: (_, record: TestPlan) => {
        // 计算实验组数据
        const experimentalTotal = record.items.reduce((accumulator, current) => accumulator + current.roundsNum, 0);
        const experimentalPositive = record.items.reduce((accumulator, current) =>
          accumulator + current.groups.filter(g => g.groupType === 'EXPERIMENTAL').reduce((p, c) => p + c.positiveNum, 0), 0);
        const experimentalNegative = record.items.reduce((accumulator, current) =>
          accumulator + current.groups.filter(g => g.groupType === 'EXPERIMENTAL').reduce((p, c) => p + c.negativeNum, 0), 0);

        // 计算对照组数据
        const controlTotal = record.items.reduce((accumulator, current) => accumulator + current.roundsNum, 0);
        const controlPositive = record.items.reduce((accumulator, current) =>
          accumulator + current.groups.filter(g => g.groupType === 'CONTROL').reduce((p, c) => p + c.positiveNum, 0), 0);
        const controlNegative = record.items.reduce((accumulator, current) =>
          accumulator + current.groups.filter(g => g.groupType === 'CONTROL').reduce((p, c) => p + c.negativeNum, 0), 0);

        return (
          <Flex gap={8} align="center" style={{ width: '100%' }}>
            <div style={{ flex: 1 }}>
              <ProgressBlock
                total={experimentalTotal}
                positiveNum={experimentalPositive}
                negativeNum={experimentalNegative}
              />
            </div>
            <div>vs</div>
            <div style={{ flex: 1 }}>
              <ProgressBlock
                total={controlTotal}
                positiveNum={controlPositive}
                negativeNum={controlNegative}
                positiveColor="#1677FF"
              />
            </div>
          </Flex>
        );
      },
    },
    {
      title: '数据分析',
      dataIndex: 'statusName',
      key: 'statusName',
      width: 70,
      align: 'center' as 'center',
      render: (_, record: TestPlan) => {
        return <span>
          <Button size={'small'} disabled={
            record.status !== 'COMPARING' &&
            record.status !== 'FINISHED'
          } type="link" onClick={() => {
            // 设置当前选中的实验计划
            setAnalysisItem(record);
            // 显示分析弹窗
            setShowAnalysisModal(true);
          }}>分析</Button>
        </span>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 80,
      align: 'center' as 'center',
    },
    {
      title: '创建人',
      dataIndex: 'userNick',
      key: 'userNick',
      width: 80,
      align: 'center' as 'center',
    },
    {
      title: '操作',
      key: 'action2',
      width: 100,
      align: 'center' as 'center',
      render: (_, record) => (
        <span>
          {record.status !== 'FINISHED' &&
            <Button type="link" size={'small'} onClick={() => handleEdit(record)}>编辑</Button>}
          {record.status !== 'DISABLED' &&
            <Popconfirm title={'确定要作废吗？作废后将不可恢复状态'} onConfirm={() => handleStop(record.id)}>
              <Button type="link" danger size={'small'}>作废</Button>
            </Popconfirm>}
          {record.status === 'COMPARING' &&
            <Button type="link" danger size={'small'} onClick={() => setFinishId(record.id)}>完结</Button>}
        </span>
      ),
    },
  ];

  // 渲染公共参数
  const renderCommonParams = (index: number) => {
    // 获取公共参数配置
    const commonParams = selectCommonParams(index, true, {
      form,
      faceList,
      sceneList,
      onChange: (item, name) => {
        form.setFieldValue(name, item?.id);
      },
    });

    return Object.entries(commonParams).map(([key, config]: [string, ComponentConfig]) => {
      // 检查是否为隐藏参数 - 先检查简单key
      const isDirectlyHidden = hiddenParams[index]?.name === key;
      
      // 检查是否为嵌套参数 - 检查完整路径
      const fullKeyToCheck = config.name ? config.name.join('.') : key;
      const isNestedParamHidden = hiddenParams[index]?.fullKey === fullKeyToCheck;
      
      // 如果已经设置为实验参数，则不显示
      if (isDirectlyHidden || isNestedParamHidden) {
        return null;
      }

      // 如果没有设置 colSpan，默认为 6
      const getColSpan = () => {
        return config.colSpan || 6;
      };

      // 模特、场景单独设置
      const getFieldName = () => {
        if (key === 'face') return [index, 'sharedParams', 'configs', '1'];
        if (key === 'scene') return [index, 'sharedParams', 'configs', '2'];
        const name = config.name ? config.name : [key];
        return [index, 'sharedParams', ...name];
      };

      // loraId onChange 处理函数
      const getOnChange = () => {
        if (key === 'loraId') {
          return (item: any, name: any) => {
            form.setFieldValue(name, item?.id);
          };
        }
        return undefined;
      };

      // 获取 namePath
      const getNamePath = () => {
        if (key === 'loraId') return ['items', index, 'sharedParams', 'loraId'];
        return undefined;
      };

      // 获取参数配置
      const paramConfig = getParamConfig(key, index, true, {
        form,
        faceList,
        sceneList,
        onChange: getOnChange(),
        namePath: getNamePath(),
      });

      const ArgLabel = ({ config }) => {
        return <Flex gap={4} align={'center'} justify={'flex-start'}>
          {/* 标题部分 */}
          <div>{config?.label}</div>

          {/* 实验参数 */}
          {config?.isSetComparison &&
            <Tooltip title={'点击添加到实验参数'}>
              <DiffOutlined style={{ fontSize: 18 }} className={'pointer'}
                            onClick={() => addToExperiment(index, key, paramType.creativePic)} />
            </Tooltip>
          }

          {/* 训练参数 */}
          {config?.isTrainParam &&
            <Tooltip title={'点击添加作为训练参数'}>
              <InteractionOutlined style={{ fontSize: 18 }} className={'pointer'}
                                   onClick={() => addToExperiment(index, key, paramType.trainPic)} />
            </Tooltip>
          }
        </Flex>;
      };

      return (
        <Col span={getColSpan()} key={key}>
          <Form.Item
            label={<ArgLabel config={paramConfig} />}
            name={getFieldName()}
            rules={config.required ? [{
              required: true,
              message: `请选择${paramConfig?.label}`,
            }] : undefined}
            valuePropName={paramConfig?.type === 'checkbox' ? 'checked' : undefined}
            initialValue={config.defaultValue}
            style={paramConfig?.type === 'checkbox' ? { marginBottom: 0 } : undefined}>
            {paramConfig?.component}
          </Form.Item>
        </Col>
      );
    });
  };


  return (
    <PageContainer style={{ padding: 8 }}>
      <Flex vertical gap={12}>
        <Flex justify={'flex-start'} gap={8} className={'width-100'}>
          <Tooltip title={'刷新页面数据'}>
            <Button icon={<RedoOutlined />} onClick={() => {
              fetchData();
              message.success('刷新成功');
            }} />
          </Tooltip>

          <Input placeholder={'计划id'} onChange={e => setSearchId(Number(e.target.value))} style={{ width: 100 }}
                 allowClear />
          <Input placeholder={'请输入计划名称'} onChange={e => setSearchName(e.target.value)} style={{ width: 150 }}
                 allowClear />

          <MasterSelector onChange={userId => setSearchUserId(userId)} width={160} roleTypes={['ADMIN']}
                          placeholder={'创建人检索'} />

          <Select placeholder={'选择状态'} style={{ width: 120 }} onChange={value => setSearchStatus(value)} allowClear
                  defaultValue={''}>
            <Select.Option value={'ENABLED'}>待调度</Select.Option>
            <Select.Option value={'PROCESSING'}>调度中</Select.Option>
            <Select.Option value={'COMPARING'}>比较中</Select.Option>
            <Select.Option value={'FINISHED'}>已完结</Select.Option>
            <Select.Option value={'DISABLED'}>已作废</Select.Option>
          </Select>

          <Button className="models-image-card-button" onClick={handleAdd}>新增实验</Button>
        </Flex>

        <Flex vertical className={'test-table'}>
          <Table columns={columns}
                 dataSource={data}
                 pagination={{
                   current: page,
                   pageSize: pageSize,
                   total,
                   onChange: handlePageChange,
                 }}
                 rowKey="id"
          />
        </Flex>

        {/* 新增/修改实验计划 */}
        {(add || opItem) &&
          <Modal open={true} width={'95vw'} title={(add ? '新增' : '修改') + '实验计划'} keyboard={true}
                 onOk={form.submit} onCancel={handleClose} maskClosable={false} centered>
            <Form form={form} onFinish={handleCommit}>

              {/* 基本信息 */}
              <Flex vertical align={'center'} justify={'flex-start'} className={'width-100'}>
                <Flex vertical gap={8} className={'width-100'}>
                  <div className={'text14 weight color-1a'}>基础信息</div>

                  <Form.Item hidden={true} name={'id'}>
                    <Input />
                  </Form.Item>
                  <Row gutter={24}>
                    <Col span={8}>
                      <Form.Item label="名称" name="name" rules={[{ required: true, message: '请输入实验名称' }]}>
                        <Input placeholder="请输入实验名称" />
                      </Form.Item>
                    </Col>
                    <Col span={16}>
                      <Form.Item label="备注" name="memo" rules={[{ required: false, message: '请输入实验备注' }]}>
                        <Input placeholder="请输入实验备注" />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={24} style={{ marginTop: -12 }}>
                    <Col span={8}>
                      <Form.Item label="保持同seed" name={['extInfo', 'keepSeed']} initialValue={'N'}
                                 rules={[{ required: true, message: '请选择是否保持同seed' }]}>
                        <Radio.Group buttonStyle={'solid'}
                                     options={[{ label: '否', value: 'N' }, { label: '是', value: 'Y' }]}
                                     optionType="button" />
                      </Form.Item>
                    </Col>
                  </Row>
                </Flex>

                {/* 实验项目 */}
                <Flex vertical gap={8} className={'width-100'}>
                  <Flex className={'text14 weight color-1a'} style={{ gap: 8 }}>
                    <div>实验项目</div>
                    <Tooltip title={'新增实验项'}>
                      <IconFont type={'icon-lujing3'} style={{ fontSize: 16 }} className={'pointer'}
                                onClick={handleAddItem} />
                    </Tooltip>
                  </Flex>

                  {/* 添加滚动容器 */}
                  <Form.List name={'items'}>
                    {fields => (
                      <Tabs type="card">
                        {fields.map(({ key, name: index, ...restField }) => {
                          const currentItem = form.getFieldValue(['items', index]);
                          const isEditable = isItemEditable(currentItem?.status);

                          return (
                            <Tabs.TabPane tab={`实验项 ${index + 1}`} key={key}>
                              <div className={'scroll-container'}>
                                <Flex justify={'flex-start'} align={'flex-start'}
                                      gap={16} className={'items-container'} style={{ position: 'relative' }}>
                                  {!isEditable && (
                                    <div className="item-mask" style={{
                                      top: 'auto',
                                      height: 'calc(100% - 105px)',
                                      marginTop: '120px',
                                      width: '95%',
                                      marginLeft: 'auto',
                                      marginRight: 'auto',
                                    }}>
                                      <div
                                        className={`mask-text ${currentItem?.status === 'PROCESSING' || currentItem?.status === 'COMPARING' ? 'processing' : ''}`}>
                                        {currentItem?.status === 'PROCESSING' && '调度中'}
                                        {currentItem?.status === 'COMPARING' && '比较中'}
                                        {currentItem?.status === 'DISABLED' && '不可用'}
                                      </div>
                                    </div>
                                  )}

                                  {deletingIndex === index && (
                                    <div style={{
                                      position: 'absolute', background: 'rgba(0, 0, 0, 0.5)', display: 'flex',
                                      justifyContent: 'center', alignItems: 'center', zIndex: 1000,
                                    }}>
                                      <CloseCircleOutlined />
                                    </div>
                                  )}

                                  {/* 左侧序号 */}
                                  <Flex vertical align={'center'} gap={8}>
                                    <div className={'left-index-item'}>
                                      {index + 1}
                                    </div>
                                  </Flex>

                                  {/* 分割线 */}
                                  <div style={{ width: 1, backgroundColor: '#f0f0f0', alignSelf: 'stretch' }} />

                                  {/* 中间表单内容 */}
                                  <Flex vertical className={'width-100'}>
                                    {/* 基本信息部分 */}
                                    <div style={{ marginBottom: 0 }}>
                                      <div style={{
                                        fontSize: '14px',
                                        fontWeight: 500,
                                        color: '#1A1A1A',
                                        marginBottom: 12,
                                      }}>基本信息
                                      </div>
                                      <Divider style={{ margin: '12px 0 12px 0' }} />
                                      <Form.Item
                                        {...restField}
                                        label="实验项名称"
                                        name={[index, 'name']}
                                        rules={[{ required: true, message: '请输入实验项名称' }]}
                                      >
                                        <Input placeholder="请输入实验项名称" />
                                      </Form.Item>
                                    </div>

                                    {/* 公共参数部分 */}
                                    <div style={{ marginBottom: 0 }}>
                                      <div style={{
                                        fontSize: '14px',
                                        fontWeight: 500,
                                        color: '#1A1A1A',
                                        marginBottom: 12,
                                      }}>公共参数
                                      </div>
                                      <Divider style={{ margin: '12px 0 12px 0' }} />
                                      <Row gutter={[24, 12]}>
                                        {renderCommonParams(index)}
                                      </Row>
                                    </div>

                                    {/* 实验参数部分 */}
                                    {hiddenParams[index] && hiddenParams[index].type === paramType.creativePic &&
                                      <div>
                                        <div style={{
                                          fontSize: '14px',
                                          fontWeight: 500,
                                          color: '#1890ff',
                                          marginBottom: 12,
                                        }}>实验参数（只能设置一个实验变量）
                                        </div>
                                        <Divider style={{ margin: '12px 0 12px 0' }} />
                                        {renderExperimentParams(index)}
                                      </div>
                                    }

                                    {/* 训练参数部分 */}
                                    {hiddenParams[index] && hiddenParams[index].type === paramType.trainPic &&
                                      <div>
                                        <div style={{
                                          fontSize: '14px',
                                          fontWeight: 500,
                                          color: '#52c41a',
                                          marginBottom: 12,
                                        }}>训练参数（用于训练服饰）
                                        </div>
                                        <Divider style={{ margin: '12px 0 12px 0' }} />
                                        {renderTrainParams(index)}
                                      </div>
                                    }

                                  </Flex>

                                  {/* 分割线 */}
                                  <div style={{ width: 1, backgroundColor: '#f0f0f0', alignSelf: 'stretch' }} />

                                  {/* 右侧操作按钮 */}
                                  <Flex vertical gap={12} justify={'center'} align={'center'}
                                        style={{ padding: '8px 0', height: '100%' }}>
                                    {currentItem.status !== 'PROCESSING' ? (
                                      <Popconfirm title="确认删除" description="您确定要删除这个实验项吗？"
                                                  onConfirm={() => handleDelItem(index)}>
                                        <CloseCircleOutlined style={{ fontSize: 16 }}
                                                             className={'pointer hover-color-danger'} />
                                      </Popconfirm>
                                    ) : (
                                      <Tooltip title="任务调度中，无法删除...">
                                        <CloseCircleOutlined
                                          style={{ fontSize: 16, color: '#00000040', cursor: 'not-allowed' }} />
                                      </Tooltip>
                                    )}
                                    <Popconfirm title="确认复制" description="确定要按照当前实验项复制并新增吗？"
                                                onConfirm={() => {
                                                  handleCopy(currentItem, index);
                                                  // 同步添加标签页
                                                  const items = form.getFieldValue('items');

                                                  // 同步添加标签页
                                                  setActiveKey(items.length.toString());
                                                }}>
                                      <CopyOutlined style={{ fontSize: 16, zIndex: 1001 }}
                                                    className={'pointer hover-color-danger'} />
                                    </Popconfirm>
                                  </Flex>
                                </Flex>
                              </div>
                            </Tabs.TabPane>
                          );
                        })}
                      </Tabs>
                    )}
                  </Form.List>
                </Flex>

              </Flex>
            </Form>
          </Modal>
        }

        {/* 图片对比 */}
        {compareGroups && compareGroups.length > 0 &&
          <ImageCompareModal results={compareGroups.map(group => group.results)}
                             styleBatchIds={compareGroups.map(group => group.extInfo && group.extInfo['isStyleScene'] && group.extInfo['isStyleScene'] === true ? group.batchId : null)}
                             onCancel={() => {
                               setCompareGroups([]);
                               setCompareItem(null);
                               setCurrentSelectTestPlan(null);
                               fetchData();
                             }} onRefresh={() => currentSelectTestPlan && handleCompare(currentSelectTestPlan)}
                             showDetail={() => showDetailModal(compareItem)} />
        }

        {/* 分析结果 */}
        {showAnalysisModal && analysisItem && (
          <AnalysisModal
            item={analysisItem}
            onCancel={() => {
              setShowAnalysisModal(false);
              setAnalysisItem(undefined);
            }}
          />
        )}
      </Flex>


      <Spin spinning={loading} tip="正在启动实验..." fullscreen wrapperClassName="full-screen-spin" />

      {/* 实验结论 */}
      {finishId &&
        <Modal title="实验结论" open={true} width={400} onCancel={() => setFinishId(null)}
               onOk={() => commitConclusion()}>
          <TextArea rows={5} placeholder="请输入实验结论"
                    onChange={e => setConclusion(e.target.value)} />
        </Modal>
      }
    </PageContainer>
  );
};

export default ABTest;
