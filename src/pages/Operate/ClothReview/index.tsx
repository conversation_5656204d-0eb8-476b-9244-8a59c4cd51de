import { CopyIcon, MiniTag } from '@/components/Common/CommonComponent';
import SystemConfigSwitch from '@/components/Common/SystemConfigSwitch';
import IconFont from '@/components/IconFont';
import ImgPreview from '@/components/ImgPreview';
import LoraPromptsSwitch, {
  formatPrompt,
} from '@/components/Lora/LoraPromptsSwitch';
import {
  ElementConfig,
  getElementConfig,
  getMerchantRecentElement,
} from '@/services/ElementController';
import { batchDownload, uploadFile } from '@/services/FileController';
import {
  AllModelStatus,
  confirmTrainLora,
  cutoutAgain,
  FileVO,
  getSubTrainStatusDesc,
  getTrainDetail,
  MaterialModelWithBlogs,
  MODEL_LABEL_TYPES,
  ModelTrainDetailVO,
  queryMaterialModelListWithBlogs,
  syncToImageCase,
  updateExtInfo,
  deliverModel,
  confirmCanDeliver
} from '@/services/MaterialModelController';
import {
  MerchantPreference
} from '@/services/SystemController';
import { queryAllMaster, queryUserProfileList, UserVO } from '@/services/UserController';
import {
  deepCopy,
  download
} from '@/utils/utils';
import {
  RedoOutlined
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  Button,
  Checkbox,
  Col,
  Flex,
  Form,
  Image,
  Input,
  InputNumber,
  message,
  Modal,
  notification,
  Pagination,
  Radio,
  Row,
  Segmented,
  Select,
  Tabs,
  Tooltip
} from 'antd';
import type { SelectProps } from 'antd/es/select';
import TextArea from 'antd/lib/input/TextArea';
import { helix } from 'ldrs';
import React, { useEffect, useRef, useState } from 'react';
import './index.less';
import { createWorkflowTask, updateWorkflowTaskByBizId } from '@/services/WorkflowTaskController';
import { PromptUserNameText, ReviewStatusText, ReviewTaskStatus, TestImageStatus } from '@/components/Lora/WorkflowTask';
import LoraImageModal from '@/components/Operate/LoraImageModal';
import { queryWorkScheduleList } from '@/services/WorkScheduleController';
import dayjs from 'dayjs';

const { Option } = Select;

helix.register();

const AllReviewType = [
  { label: '初审', value: 'initial_review' },
  { label: '复审', value: 'again_review' },
];
const DEFAULT_TEST_NUM = 1;

export default function ClothReview() {
  // @ts-ignore
  const [models, setModels] = useState<Array<MaterialModelWithBlogs>>([]);
  const [recordModelId, setRecordModelId] = useState<number | null>(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [form] = Form.useForm();

  //训练详情页
  const [showTrainDetailModal, setShowTrainDetailModal] = useState(false);
  const [selectedTrainModelDetail, setSelectedTrainModelDetail] =
    useState<ModelTrainDetailVO>();

  const [groupedLabelFiles, setGroupedLabelFiles] = useState<
    Map<string, FileVO[]>
  >(new Map());
  const [showCutout, setShowCutout] = useState(true);
  const [showOriginal, setShowOriginal] = useState(true);

  //更新打标词
  const [updateTxtBtnEnabled, setUpdateTxtBtnEnabled] = useState(false);
  const [updateTxtFiles, setUpdateTxtFiles] = useState<Map<string, string>>(
    new Map(),
  );

  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewIdx, setPreviewIdx] = useState();
  const [previewImgs, setPreviewImgs] = useState();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(24);
  const [masterOptions, setMasterOptions] = useState<Array<any>>([]);
  const [reviewers, setReviewers] = useState<Array<UserVO>>([]);
  const [adminOptions, setAdminOptions] = useState<Array<UserVO>>([]);
  const [scheduledAdmins, setScheduledAdmins] = useState<Array<UserVO>>([]);
  const [selectedDistributorMasterId, setSelectedDistributorMasterId] =
  useState<number | null>(null);
  const [selectedGarment, setSelectedGarment] = useState<string[] | null>(null);
  const [allDistributorMasters, setAllDistributorMasters] = useState<
    Array<UserVO>
  >([]);

  const [reviewStatus, setReviewStatus] = useState<string>('unreviewed');
  const [promptUserId, setPromptUserId] = useState<number | null>(null);

  const [
    initialReviewRejectReasonVisible,
    setInitialReviewRejectReasonVisible,
  ] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [reviewType, setReviewType] = useState<string | null>(null);
  const [searchUserId, setSearchUserId] = useState<number | null>(null);
  const [searchClothType, setSearchClothType] = useState<string>('All');
  const [nameLike, setNameLike] = useState<string>();
  const [searchId, setSearchId] = useState<number>();
  const [onlyShowMine, setOnlyShowMine] = useState(true);
  const [searchReviewer, setSearchReviewer] = useState<string>(null);
  const [searchPromptUser, setPromptUser] = useState<string>(null);

  //指定平台运营
  const [relatedOperatorType, setRelatedOperatorType] = useState('all');
  const [relatedOperatorMobile, setRelatedOperatorMobile] =
    useState<string>('');

  //打标图片编辑
  const [labelImgFiles, setLabelImgFiles] = useState<
    Map<
      string,
      {
        filePath: string;
        url: string;
        deleted: boolean;
        isTrainingMaterial?: boolean;
      }
    >
  >(new Map());
  const labelImgInputRefs = useRef({});

  //替换打标词输入框
  const replaceLabelTagsInputRefs = useRef([]);
  //替换 detail_garment_type输入框
  const replaceLabelGarmentTypesInputRefs = useRef([]);

  //关联的测试脸和场景，用于训练完成后自动生成图片
  const [relatedTestFaces, setRelatedTestFaces] = useState<number[]>();
  const [relatedTestScenes, setRelatedTestScenes] = useState<number[]>();
  const [relatedTestNum, setRelatedTestNum] = useState(0);
  const [testClothCollocation, setTestClothCollocation] = useState({});
  const [copyByPreference, setCopyByPreference] = useState(false);

  const [faceList, setFaceList] = useState<ElementConfig[]>([]);
  const [sceneList, setSceneList] = useState<ElementConfig[]>([]);

  //是否需要重新抠图
  const [needCutoutAgain, setNeedCutoutAgain] = useState(false);
  //抠图关键词数组
  const [cutoutKeyword, setCutoutKeyword] = useState<string[]>([]);
  //重新抠图时，是否重新进行图片分析预处理
  const [prepareViewAgainWhenCutoutAgain, setPrepareViewAgainWhenCutoutAgain] =
    useState(false);
  const [cutoutOnlyUpscale, setCutoutOnlyUpscale] = useState('N');
  const [clothDetailsPrompt, setClothDetailsPrompt] = useState('');
  const [labelType, setLabelType] = useState<string | undefined>(undefined);

  //抠图时，是否让衣服在图片中占更高比例（实验参数）
  const [cut4ScaleUp, setCut4ScaleUp] = useState('N');

  const [imageSize, setImageSize] = useState('1024');
  const [isSquare, setIsSquare] = useState(false);

  const [loraTrainType, setLoraTrainType] = useState('flux');

  const [trainParams, setTrainParams] = useState({});

  //是否只显示未确认
  const [onlyUnconfirmedLora, setOnlyUnconfirmedLora] = useState(false);

  //是否只显示VIP和客户
  const [onlyVIPOrCustomerLora, setOnlyVIPOrCustomerLora] = useState(false);

  //是否显示确认训练的弹窗
  const [showConfirmLoraModal, setShowConfirmLoraModal] = useState(false);
  const [merchantPreferenceSetting, setMerchantPreferenceSetting] =
    useState<MaterialModelWithBlogs | null>(null);
  const [selectedPreferenceIndex, setSelectedPreferenceIndex] = useState(0);

  const [maxTrainStep, setMaxTrainStep] = useState<number>();

  // 是否需要手工替换文件（默认为 False）
  const [isManualReplacement, setIsManualReplacement] = useState<number>(0);

  const [confirmLoraForm] = Form.useForm();
  const [deliveryForm] = Form.useForm();

  const defaultFluxSteps = 2500;
  const defaultFluxSteps4MultiColor = 3000;

  const creationSizeOptions: SelectProps['options'] = [
    { label: '768x1024', value: 'THREE_FOUR' },
    { label: '1024x1024', value: 'ONE_ONE' },
    { label: '1340x1785', value: 'THREE_FOUR_LG_N' },
    { label: '1536x1536', value: 'ONE_ONE_LG' },
    { label: '1080x1920', value: 'NINE_SIXTEEN_2K' },
  ];

  //https://conrain.yuque.com/org-wiki-conrain-pcgdb4/cvib8a/dirymm4xnxl5ghht
  const cutoutKeywordOptions: SelectProps['options'] = [
    {
      label: 'clothing（通用，默认都带上，不包括帽子、鞋子）',
      value: 'clothing',
    },
    { label: 'T-shirt（T恤）', value: 'T-shirt' },
    { label: 'shirt（衬衫）', value: 'shirt' },
    { label: 'sweater（毛衣）', value: 'sweater' },
    { label: 'hoodie（连帽衫/卫衣）', value: 'hoodie' },
    { label: 'vest（背心）', value: 'vest' },
    { label: 'bra（内衣，包括运动内衣)', value: 'bra' },
    { label: 'jacket（夹克）', value: 'jacket' },
    { label: 'coat（外套，比jacket长、厚）', value: 'coat' },
    { label: 'trousers（长裤）', value: 'trousers' },
    { label: 'shorts（短裤）', value: 'shorts' },
    { label: 'leggings（瑜伽裤）', value: 'leggings' },
    { label: 'skirt（短裙/半身裙）', value: 'skirt' },
    { label: 'panties（内裤）', value: 'panties' },
    { label: 'dress（连衣裙）', value: 'dress' },
    { label: 'hat（帽子）', value: 'hat' },
    { label: 'scarf（围巾）', value: 'scarf' },
    { label: 'shoes（鞋子）', value: 'shoes' },
  ];

  const initialReviewRejectReasonOptions: string[] = [
    '衣服带吊牌',
    '90度侧面过多',
    '非标准人台',
    '服装过度倾斜',
    '拍照角度俯拍严重，下身腿短',
    '衣服有色差',
    '多色衣服当单色',
    '一款多色带拼色',
    '暗纹类服装',
    '图片张数不足',
    '头顶和脚底留的空间过大',
    '衣服被遮挡严重',
    '图片模糊',
    '真人模特身材比例过差',
    '模特服装配饰过多',
    '抠图衣服细节损失',
    '抠图结果带着人台',
    '抠图结果带着人台红线',
  ];

  const againReviewRejectReasonOptions: string[] = [
    '款式错误',
    '颜色色差显著错误',
    '花纹显著错误',
    '面料显著错误',
    '服装长度错误',
    'logo图案错误',
    '人物空心人',
    '人物多手多脚',
  ];

  const [reviewRejectReasons, setReviewRejectReasons] = useState<
    string[]
  >([]);
  const [downloading, setDownloading] = useState(false);

  //lora_vip配置，训练加急白名单
  const [loraVipCfg, setLoraVipCfg] = useState<number[]>([]);

  useEffect(() => {
    if (
      relatedTestFaces &&
      relatedTestFaces?.length > 0 &&
      relatedTestScenes
    ) {
      setRelatedTestNum(DEFAULT_TEST_NUM);
    } else {
      setRelatedTestNum(0);
    }
  }, [relatedTestFaces, relatedTestScenes]);

  useEffect(() => {
    fetchData(page, pageSize);
  }, [page]);

  useEffect(() => {
    initMasterOptions();
  }, [selectedDistributorMasterId]);

  useEffect(() => {
    setLoraTrainType('flux');
    setMaxTrainStep(getDefaultFluxSteps());

    setTrainParams({
      lr: 0.0002,
      contentOrStyle: 'content',
      rank: 32,
      alpha: 16,
      dropout: 0.2,
      resolution: '1024',
      trainExtInfo:
        selectedTrainModelDetail?.clothLoraTrainDetail?.trainExtInfo,
    });
  }, [showConfirmLoraModal, selectedTrainModelDetail]);

  useEffect(() => {
    setPage(1);
    fetchData(1, pageSize);
  }, [
    pageSize,
    reviewStatus,
    reviewType,
    selectedStatus,
    nameLike,
    searchUserId,
    relatedOperatorType,
    searchClothType,
    onlyUnconfirmedLora,
    onlyVIPOrCustomerLora,
    searchId,
    selectedDistributorMasterId,
    onlyShowMine,
    searchReviewer,
    searchPromptUser,
  ]);

  useEffect(() => {
    if (
      showTrainDetailModal &&
      selectedTrainModelDetail &&
      selectedTrainModelDetail.clothLoraTrainDetail
    ) {
      setUpdateTxtFiles(new Map());
      setLabelImgFiles(new Map());
      setUpdateTxtBtnEnabled(false);

      if (selectedTrainModelDetail.clothLoraTrainDetail.relatedOperatorMobile) {
        setRelatedOperatorMobile(
          selectedTrainModelDetail.clothLoraTrainDetail.relatedOperatorMobile,
        );
      } else {
        setRelatedOperatorMobile('');
      }
    }
  }, [showTrainDetailModal, selectedTrainModelDetail]);

  useEffect(() => {
    initMasterOptions();

    //获取脸和场景
    getElementConfig('CREATE_IMAGE').then((cfg) => {
      if (cfg) {
        for (let c of cfg) {
          if (c.configKey === 'FACE') {
            //所有level=2的脸
            setFaceList(c.children);
          }
          if (c.configKey === 'SCENE') {
            //所有level=2的场景
            setSceneList(c.children);
          }
        }
      }
    });

    queryAllMaster(['DISTRIBUTOR']).then((res) => {
      if (res) {
        setAllDistributorMasters(res);
      } else {
        setAllDistributorMasters([]);
      }
    });

  }, []);

  function getDefaultFluxSteps() {
    if (
      selectedTrainModelDetail != null &&
      selectedTrainModelDetail.clothLoraTrainDetail != null &&
      selectedTrainModelDetail.clothLoraTrainDetail.multiColors === 'Y'
    ) {
      return defaultFluxSteps4MultiColor;
    } else {
      return defaultFluxSteps;
    }
  }

  async function fetchData(
    pageNum: number | undefined,
    pageSize: number | undefined,
  ) {
    const statusList = selectedStatus ? [selectedStatus] : null;
    let garmentTypeList: string[] = [];
    if (selectedGarment) {
      selectedGarment.forEach((item) => {
        garmentTypeList = [...garmentTypeList, ...item.split(',')];
      });
    }

    let query = {
      pageNum,
      pageSize,
      type: 'CUSTOM',
      statusList,
      nameOrUserLike: nameLike ? nameLike : null,
      userId: searchUserId ? searchUserId : null,
      relatedOperatorType: relatedOperatorType ? relatedOperatorType : null,
      clothStyleType:
        searchClothType && searchClothType != 'All'
          ? searchClothType.toLowerCase()
          : null,
      onlyUnconfirmedLora: onlyUnconfirmedLora,
      onlyVIPOrCustomerLora: onlyVIPOrCustomerLora,
      id: searchId ? searchId : null,
      distributorMasterId: selectedDistributorMasterId || null,
      materialType: 'cloth',
      garmentTypeList,
      reviewStatus,
      reviewType,
      reviewerId: searchReviewer,
      promptUserId: searchPromptUser,
      onlyShowMine: true,
    };

    let res = await queryMaterialModelListWithBlogs(query);
    if (res) {
      setModels(res?.list || []);
      if (res?.totalCount != null) {
        setTotal(res?.totalCount);
      }
    }
    return res?.list || [];
  }

  const getServerUrl = (key) => {
    if (
      !selectedTrainModelDetail ||
      !selectedTrainModelDetail?.clothLoraTrainDetail
    )
      return '';

    const target = selectedTrainModelDetail?.clothLoraTrainDetail[key];
    return target && target.serverUrl ? target.serverUrl : '';
  };

  const handleChangeClothPrompt = (value: string, tag: string | undefined) => {
    setClothDetailsPrompt(value);
    setLabelType(tag);
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setPage(page);
    if (pageSize) {
      setPageSize(pageSize);
    }
  };

  // 处理时间，增加24小时并保留到小时返回
  const add24HoursCustomFormat = (timeStr: string): string => {
    // 解析传入的时间字符串为 Date 对象
    const date = new Date(timeStr);

    // 在当前时间的基础上加上24小时
    date.setTime(date.getTime() + 24 * 60 * 60 * 1000);

    // 获取日期和小时
    const day = date.getDate(); // 获取日期部分
    const hour = date.getHours(); // 获取小时部分

    // 返回自定义格式的字符串：13日16时
    return `${day}日${hour}时`;
  };

  const handleReviewReject = () => {
    setInitialReviewRejectReasonVisible(true);
  };

  const handleInitialReviewPass = () => {
    setShowConfirmLoraModal(true);
    setLoraTrainType('flux');
    setMaxTrainStep(2500);
    setCutoutKeyword(['']);
    initDefaultRelatedTestConfig(selectedTrainModelDetail);
    if (selectedTrainModelDetail?.merchantPreference && selectedTrainModelDetail.merchantPreference.preferences && selectedTrainModelDetail.merchantPreference.preferences.length > 0) {
      setTestClothCollocation(selectedTrainModelDetail.merchantPreference.preferences[0].clothCollocation);
    }
  };

  const handleReviewRejectConfirm = async () => {
    if (!['IN_TRAINING', 'TESTING'].includes(selectedTrainModelDetail?.status)) {
      return;
    }
    const reviewType = selectedTrainModelDetail?.status === 'IN_TRAINING' ? 'initial_review' : 'again_review';
    const promptUserType = selectedTrainModelDetail?.status === 'IN_TRAINING' ? 'prompt_user_review' : 'prompt_user_again_review';
    const res = await Promise.all([
      createWorkflowTask({
        bizId: selectedTrainModelDetail.id,
        type: promptUserType,
        operatorId: promptUserId,
        status: 'RUNNING',
      }),
      updateWorkflowTaskByBizId({
        bizId: selectedTrainModelDetail.id,
        type: reviewType,
        meta: { reasons: reviewRejectReasons },
        status: 'FAILED',
      })
    ]);
    if (res) {
      notification.success({
        message: '提交成功',
      });
      fetchData(page, pageSize);
      setInitialReviewRejectReasonVisible(false);
      setReviewRejectReasons([]);
      setShowTrainDetailModal(false);
    }
  };

  const handlePreviewUrl = (url, idx = -1, imgList: string[] | [] = []) => {
    setPreviewImage(url);

    if (idx >= 0) {
      //@ts-ignore
      setPreviewIdx(idx);
      //@ts-ignore
      setPreviewImgs(imgList);
    } else {
      setPreviewIdx(undefined);
      setPreviewImgs(undefined);
    }

    setPreviewVisible(true);
  };

  const handleCancelPreview = () => {
    setPreviewVisible(false);
  };

  function onShowTrainDetail(id: number, needInitDefaultTest = false) {
    getTrainDetail({ id: id }).then((res) => {
      if (res) {
        setSelectedTrainModelDetail(res);
        if (res.labelRetFiles) {
          setGroupedLabelFiles(groupFilesByFileDir(res.labelRetFiles));
        }
        setShowTrainDetailModal(true);
        setRelatedTestFaces(res.clothLoraTrainDetail.testFaces || undefined);
        setRelatedTestScenes(res.clothLoraTrainDetail.testScenes || undefined);
        setMaxTrainStep(
          res.clothLoraTrainDetail.maxTrainStep ||
            (loraTrainType === 'sdxl' ? 4000 : defaultFluxSteps),
        );
        replaceLabelTagsInputRefs.current = [];
        replaceLabelGarmentTypesInputRefs.current = [];

        if (needInitDefaultTest) {
          initDefaultRelatedTestConfig(res);
        }
        setCutoutOnlyUpscale(res.clothLoraTrainDetail.cutoutOnlyUpscale || 'N');

        setClothDetailsPrompt(
          formatPrompt(res.clothLoraTrainDetail.clothDetailsPrompt),
        );
        setLabelType(res.extInfo && res.extInfo['labelType']);
      } else {
        setSelectedTrainModelDetail(undefined);
        setShowTrainDetailModal(false);
      }

      queryUserProfileList({ profileKey: 'promptUserId', uid: res?.userId }).then((profileRes) => {
        if (profileRes && profileRes[0]?.profileVal &&
          scheduledAdmins.some(admin => admin.id === Number(profileRes[0]?.profileVal))) {
          setPromptUserId(Number(profileRes[0]?.profileVal));
        } else {
          setPromptUserId(undefined);
        }
      });
    });
  }

  function loraConfirmedByModel(model: any) {
    if (
      model &&
      model.clothLoraTrainDetail &&
      model.clothLoraTrainDetail['loraConfirmed'] === 'Y'
    ) {
      return true;
    }

    return !!(model && model.extInfo && model.extInfo['loraConfirmed'] === 'Y');
  }

  function initMasterOptions() {
    queryAllMaster(['MERCHANT', 'OPERATOR', 'ADMIN']).then((res) => {
      if (res && Array.isArray(res)) {
        const masterOptions = [];
        res.forEach((item) => {
          // @ts-ignore
          masterOptions.push({
            label: item.nickName + (item.corpName ? '@' + item.corpName : ''),
            value: item.id + '',
          });
        });

        setMasterOptions(masterOptions);
      }
    });
    queryAllMaster(['ADMIN']).then((res) => {
      if (res && Array.isArray(res)) {
        setAdminOptions(res);
        const startTime = dayjs().startOf('day').format('YYYY-MM-DD 00:00:00');
        const endTime = dayjs().endOf('day').format('YYYY-MM-DD 23:59:59');
        queryWorkScheduleList({ startTime, endTime }).then((schedules) => {
          if (schedules && Array.isArray(schedules)) {
            const scheduledUserIds = new Set(schedules.map(schedule => schedule.userId));
            const filteredAdmins = res.filter(admin => scheduledUserIds.has(admin.id));
            setScheduledAdmins(filteredAdmins);
          }
        });
      }
    });
    queryAllMaster(['REVIEWER']).then((res) => {
      if (res && Array.isArray(res)) {
        setReviewers(res);
      }
    });
  }

  function getTaskStatusTitle(task) {
    if (!task || !task?.status) {
      return '未开始';
    }

    return getSubTrainStatusDesc(task?.status);
  }

  function getTrainingStatusTitle(item) {
    //训练中
    if (item.status === 'IN_TRAINING') {
      //获取label和lora的运行状态
      let preprocessStatus = getTaskStatusTitle(
        item?.clothLoraTrainDetail?.prepareView,
      );
      let cutoutStatus = getTaskStatusTitle(item?.clothLoraTrainDetail?.cutout);
      let labelStatus = getTaskStatusTitle(item?.clothLoraTrainDetail?.label);
      let loraStatus = item?.clothLoraTrainDetail?.lora?.statusDesc || '未开始';

      //lora已确认
      if (loraConfirmedByModel(item)) {
        if (isLoraRunning(item)) {
          return '训练中';
        } else {
          return '训练' + loraStatus;
        }

        //lora未确认
      } else {
        //在打标环节
        if (item?.clothLoraTrainDetail?.label?.status) {
          if (item?.clothLoraTrainDetail?.label?.status === 'COMPLETED') {
            return `打标完成-未确认`;
          } else {
            return `打标${labelStatus}`;
          }
        }

        //在打标环节
        if (item?.clothLoraTrainDetail?.cutout?.status) {
          return `抠图${cutoutStatus}`;
        }

        //在预处理环节
        return `预处理${preprocessStatus}`;
      }
    }

    if (item.status === 'TESTING') {
      if (
        item?.clothLoraTrainDetail?.autoGenImg &&
        item?.clothLoraTrainDetail?.confirmCanDeliverInfo?.confirmCanDeliver ===
          'Y'
      ) {
        return '出图交付中';
      }

      return '审核中';
    }
    if (item.status === 'DISABLED') {
      return '审核不通过';
    }
  }

  function getMerchantPrefix(item: MaterialModelWithBlogs) {
    if (item.vipCustomer) {
      return 'VIP';
    }
    if (item.paidCustomer) {
      return '付费';
    }
    return '';
  }

  function isLoraRunning(item: MaterialModelWithBlogs) {
    return (
      item &&
      item.clothLoraTrainDetail &&
      item.clothLoraTrainDetail.lora &&
      item.clothLoraTrainDetail.lora.status === 'RUNNING'
    );
  }

  function colorNumberDesc(item: MaterialModelWithBlogs) {
    if (item.clothLoraTrainDetail && item.clothLoraTrainDetail?.colorNumber) {
      if (item.clothLoraTrainDetail.colorNumber === '1') {
        return '单色';
      }
      return Number(item.clothLoraTrainDetail.colorNumber).toString() + '色';
    } else {
      if (
        item.clothLoraTrainDetail &&
        item.clothLoraTrainDetail?.multiColors === 'Y'
      ) {
        return '多色';
      }
    }

    return '单色';
  }

  const ImageCard = (item: MaterialModelWithBlogs) => (
    <div className={'models-image-card'}>
      <div
        className={
          'models-img-cover' +
          (item.type === 'SYSTEM' ? ' lora-system-block' : '')
        }
      >
        <img
          src={item.showImage}
          alt={item.name}
          style={{ cursor: 'pointer' }}
          onClick={() => {
            onShowTrainDetail(item.id, false);
          }}
        />
        {(item.status === 'IN_TRAINING' ||
          item.status === 'TESTING' ||
          item.status === 'DISABLED') && (
          <div
            className="models-traing-mask"
            style={{ cursor: 'pointer' }}
            onClick={() => {
              onShowTrainDetail(item.id, false);
            }}
          >
            <div style={{ marginTop: 74 }}>
              <l-helix
                size="45"
                speed="2.5"
                color={isLoraRunning(item) ? 'blue' : '#D88FFF'}
              />
            </div>
            <ReviewStatusText item={item} />
            <div
              className={
                'models-training-title' +
                (isLoraRunning(item) ? ' lora-running-text' : '')
              }
            >
              {getTrainingStatusTitle(item)}
            </div>

            {item.status === 'DISABLED' &&
              item.extInfo != null &&
              item.extInfo['disableOperatorNick'] && (
                <div className="models-cancel-training-user">
                  (操作人：{item.extInfo['disableOperatorNick']})
                </div>
              )}

            {(item.status === 'IN_TRAINING' || item.status === 'TESTING') && (
              <div className="models-training-finish-time">
                预计完成时间{add24HoursCustomFormat(item.createTime)}
              </div>
            )}
          </div>
        )}
        {item.extInfo && item.extInfo['labelType'] && (
          <MiniTag
            defaultValue={'默认'}
            title={
              MODEL_LABEL_TYPES.find(
                (e) =>
                  item &&
                  item.extInfo &&
                  item.extInfo['labelType'] &&
                  e.value === item.extInfo['labelType'].toLowerCase(),
              )?.label
            }
          />
        )}
      </div>

      {/*模型名称*/}
      <div
        className="loras-image-card-name"
        style={{ color: item.type === 'SYSTEM' ? 'red' : '' }}
      >
        <span
          style={{
            fontSize: 12,
            color:
              loraVipCfg?.includes(item?.id) && item?.status === 'IN_TRAINING'
                ? 'red'
                : '',
          }}
        >
          {item.name}
        </span>
        <CopyIcon value={item.name} />

        {item.type === 'SYSTEM' ? '(官方试用)' : ''}
        <span
          style={{ color: '#727375', fontSize: 12 }}
        >{`(${item?.id})`}</span>
      </div>
      <div className="loras-image-card-info">上传时间：{item.createTime}</div>
      <PromptUserNameText item={item} users={adminOptions} />
      <div className="loras-image-card-info">
        {getMerchantPrefix(item) && (
          <span style={{ color: 'red' }}>{getMerchantPrefix(item)}</span>
        )}
        商户：
        <a
          onClick={() => {
            setNameLike(item.userNick);
          }}
        >
          {item.userNick}
        </a>
      </div>

      <div className="loras-image-card-info">
        <span>服装：</span>
        {item?.clothLoraTrainDetail?.multiColors === 'Y' ? (
          <span style={{ color: 'red' }}>{colorNumberDesc(item)}</span>
        ) : (
          '单色'
        )}
        {item.clothTypeDesc && <span>{item.clothTypeDesc}</span>}
      </div>
      <Flex justify={'space-between'} align={'center'} className="lora-card-actions">
        <Button className={'operate-btn'} size={'small'} onClick={() => setRecordModelId(item.id)}>查看历史创作记录</Button>
			</Flex>
    </div>
  );

  const changeTestClothCollocation = (key, value) => {
    // const copy = { ...testClothCollocation };
    testClothCollocation[key] = value;
    // setTestClothCollocation(copy);
  };


  async function onConfirmLora() {
    if (selectedTrainModelDetail) {
      let trainExtInfo = trainParams['trainExtInfo'].toString();
      trainExtInfo =
        trainExtInfo && trainExtInfo.length > 0 ? JSON.parse(trainExtInfo) : {};

      const res = await Promise.all([confirmTrainLora({
        id: selectedTrainModelDetail.id,
        maxTrainStep: maxTrainStep,
        testFaces: relatedTestFaces,
        testScenes: relatedTestScenes,
        testNum: relatedTestNum,
        loraType: loraTrainType,
        testClothCollocation,

        lr: trainParams['lr'].toString(),
        contentOrStyle: trainParams['contentOrStyle'],
        rank: trainParams['rank'].toString(),
        alpha: trainParams['alpha'].toString(),
        dropout: trainParams['dropout'].toString(),
        resolution: trainParams['resolution'].toString(),
        trainExtInfo,
      }), updateWorkflowTaskByBizId({
        bizId: selectedTrainModelDetail.id,
        type: 'initial_review',
        status: 'COMPLETED'
      })]);
      if (res) {
        setShowTrainDetailModal(false);
        setSelectedTrainModelDetail(undefined);
        setShowConfirmLoraModal(false);
        fetchData(page, pageSize);
        message.info('提交成功，将继续训练');
      }
    }
  }

  function onCutoutAgain() {
    if (selectedTrainModelDetail && needCutoutAgain) {
      cutoutAgain({
        id: selectedTrainModelDetail.id,
        cutoutKeyword: cutoutKeyword ? cutoutKeyword.join(' . ') : '',
        imageSize: imageSize || '',
        isSquare: isSquare,
        prepareViewAgainWhenCutoutAgain,
        cutoutOnlyUpscale,
        clothDetailsPrompt,
        labelType,
        cut4ScaleUp,
      }).then((res) => {
        if (res) {
          setShowTrainDetailModal(false);
          setSelectedTrainModelDetail(undefined);
          setShowConfirmLoraModal(false);
          setNeedCutoutAgain(false);
          setCutoutKeyword([]);

          message.info('提交成功，将重新抠图打标');

          // 更新扩展信息
          const modelId = selectedTrainModelDetail?.id;
          if (modelId) {
            updateExtInfo({
              id: modelId,
              isLoraSystemReload: true,
            });
          }
        }
      });
    }
  }

  const checkOtherField = (fieldName, value, errorMessage) => {
    // 获取另一个字段的值
    const otherValue = form.getFieldValue(fieldName);
    // 如果当前字段为空而另一个字段不为空，返回错误
    if ((!value || value.length === 0) && otherValue && otherValue.length > 0) {
      return Promise.reject(new Error(errorMessage));
    }
    return Promise.resolve();
  };

  function loraConfirmed() {
    return loraConfirmedByModel(selectedTrainModelDetail);
  }

  function loraConfirmedTime() {
    if (
      selectedTrainModelDetail &&
      selectedTrainModelDetail.clothLoraTrainDetail &&
      selectedTrainModelDetail.clothLoraTrainDetail['loraConfirmedTime']
    ) {
      return selectedTrainModelDetail.clothLoraTrainDetail['loraConfirmedTime'];
    }

    if (
      selectedTrainModelDetail &&
      selectedTrainModelDetail.extInfo &&
      selectedTrainModelDetail.extInfo['loraConfirmedTime']
    ) {
      return selectedTrainModelDetail.extInfo['loraConfirmedTime'];
    }

    return '';
  }

  function findLabelFailTxtFileName() {
    let ret: string[] = [];
    if (selectedTrainModelDetail && selectedTrainModelDetail?.labelRetFiles) {
      for (const f of selectedTrainModelDetail?.labelRetFiles) {
        let emptyTextContent =
          !f.fileDir.endsWith('/label') &&
          (!f.textContent ||
            f.textContent == '' ||
            f.textContent.toLowerCase().includes('error'));

        if (
          f.type === 'text' &&
          (emptyTextContent ||
            f.textContent.toLowerCase().includes('a garment'))
        ) {
          ret.push(f.fileName);
        }
      }
    }
    return ret;
  }

  function getAllUploadImgs() {
    let ret: string[] = [];

    if (
      selectedTrainModelDetail?.materialDetail &&
      selectedTrainModelDetail?.materialDetail?.imgUrls
    ) {
      ret = ret.concat(selectedTrainModelDetail.materialDetail?.imgUrls);
    } else {
      if (
        selectedTrainModelDetail?.materialDetail &&
        selectedTrainModelDetail?.materialDetail?.fullShotImgList
      ) {
        ret = ret.concat(
          selectedTrainModelDetail.materialDetail.fullShotImgList.map(
            (item) => item.imgUrl,
          ),
        );
      }
      if (
        selectedTrainModelDetail?.materialDetail &&
        selectedTrainModelDetail?.materialDetail?.detailShotImgList
      ) {
        ret = ret.concat(
          selectedTrainModelDetail.materialDetail.detailShotImgList.map(
            (item) => item.imgUrl,
          ),
        );
      }
      if (
        selectedTrainModelDetail?.materialDetail &&
        selectedTrainModelDetail?.materialDetail?.moreImgList
      ) {
        ret = ret.concat(
          selectedTrainModelDetail.materialDetail.moreImgList.map(
            (item) => item.imgUrl,
          ),
        );
      }
    }

    return ret;
  }

  function onLabelTextChange(
    e: React.ChangeEvent<HTMLTextAreaElement>,
    f: FileVO,
  ) {
    const newMap = new Map(updateTxtFiles);
    const key = f.fileDir + '/' + f.fileName;
    newMap.set(key, e.target.value);
    setUpdateTxtFiles(newMap);

    setUpdateTxtBtnEnabled(true);
  }

  function groupFilesByFileDir(labelRetFiles: FileVO[]): Map<string, FileVO[]> {
    const groupedFiles = new Map<string, FileVO[]>();

    for (const file of labelRetFiles) {
      if (!groupedFiles.has(file.fileDir)) {
        groupedFiles.set(file.fileDir, []);
      }
      groupedFiles.get(file.fileDir)!.push(file);
    }

    return groupedFiles;
  }

  function getLabelRetDirShowName(fileDir: string) {
    if (fileDir.includes('half')) {
      return '半身图';
    } else if (fileDir.includes('full')) {
      return '全身图';
    } else {
      return '特征';
    }
  }

  function getShowTestFaceListByModel(
    model: MaterialModelWithBlogs | undefined,
  ) {
    let clothStyleType = 'female';
    let version = loraTrainType === 'flux' ? 'v_2' : 'v_1';
    if (model && model?.extInfo && model?.extInfo['clothStyleType']) {
      clothStyleType = model.extInfo['clothStyleType'];
    }

    return faceList
      .filter((f) => f.type?.includes(clothStyleType + '-model'))
      .filter((f) => f.type?.includes(version));
  }

  function getShowTestSceneListByModel(
    model: MaterialModelWithBlogs | undefined,
  ) {
    let clothStyleType = 'female';
    let version = loraTrainType === 'flux' ? 'v_2' : 'v_1';
    if (model && model?.extInfo && model?.extInfo['clothStyleType']) {
      clothStyleType = model.extInfo['clothStyleType'];
    }

    return sceneList
      .filter((f) =>
        f.type?.some((i) => i.toLowerCase() === clothStyleType.toLowerCase()),
      )
      .filter((f) => f.type?.includes(version));
  }

  // 处理图片文件上传
  async function handleLabelImgChange(file: File, f: FileVO) {
    if (file) {
      // 上传图片文件
      uploadFile(file).then((res) => {
        if (res) {
          let filePath = f.fileDir + '/' + f.fileName;

          // 设置为训练素材
          const newLabelImgFiles = new Map(labelImgFiles);

          // 设置为训练素材
          newLabelImgFiles.set(filePath, {
            filePath: f.fileDir + '/' + f.fileName,
            url: res,
            deleted: false,
          });
          // 设置为训练素材
          setLabelImgFiles(newLabelImgFiles);

          // 获取模型id
          const modelId = selectedTrainModelDetail?.id;
          // 同步到图片案例
          if (modelId) {
            syncToImageCase({
              id: modelId,
              filePath: f.fileDir + '/' + f.fileName,
              url: res,
              fileDir: f.fileDir,
              fileName: f.fileName,
              imgUrl: f.imgUrl,
              textContent: f.textContent,
              type: f.type,
            });
          }

          // 设置为手动替换
          setIsManualReplacement(isManualReplacement + 1);
          // 设置更新打标按钮为启用状态
          setUpdateTxtBtnEnabled(true);
        }
      });
    }
  }

  function getImgFileColorByFile(f: FileVO) {
    let filePath = f.fileDir + '/' + f.fileName;
    if (labelImgFiles.has(filePath)) {
      if (labelImgFiles.get(filePath)?.deleted === true) {
        return 'red';
      }
      return 'blue';
    }
    return '';
  }

  function getTxtFileColorByFile(f: FileVO) {
    let filePath = f.fileDir + '/' + f.fileName;
    if (updateTxtFiles.has(filePath)) {
      return 'blue';
    }
    if (f.textContent && f.textContent.toLowerCase().includes('a garment')) {
      return 'red';
    }
    return '';
  }

  function safeParse(str: string | undefined) {
    if (str) {
      return JSON.parse(str);
    }
    return {};
  }

  function initDefaultRelatedTestConfig(
    model: MaterialModelWithBlogs | undefined,
    index = -1,
  ) {
    setCopyByPreference(false);

    if (index < 0) {
      index = selectedPreferenceIndex;
    }

    if (model?.merchantPreference && model?.merchantPreference.preferences) {
      const merchantSubPreference =
        model?.merchantPreference.preferences[index];
      let faceList = getShowTestFaceListByModel(model);

      if (merchantSubPreference?.faces) {
        faceList = faceList.filter((f) =>
          merchantSubPreference?.faces.some((i) => Number(i) === f.id),
        );
      }

      if (merchantSubPreference?.faces && faceList.length > 0) {
        setRelatedTestFaces(faceList.map((e) => e.id));
        setCopyByPreference(true);
      } else {
        getMerchantRecentElement({
          key: 'FACE',
          userId: model?.userId,
        }).then((res) => {
          if (res && res.length > 0) {
            faceList = faceList.filter((f) => res.some((i) => i.id === f.id));
            setRelatedTestFaces(faceList.slice(0, 5).map((e) => e.id));
          }
        });
      }

      let sceneList = getShowTestSceneListByModel(model);
      if (merchantSubPreference?.scenes) {
        sceneList = sceneList.filter((f) =>
          merchantSubPreference?.scenes.some((i) => Number(i) === f.id),
        );

        if (sceneList.length <= 0) {
          getMerchantRecentElement({
            key: 'SCENE',
            userId: model?.userId,
          }).then((res) => {
            if (res && res.length > 0) {
              sceneList = sceneList.filter((f) =>
                res.some((i) => i.id === f.id),
              );
              setRelatedTestScenes(sceneList.slice(0, 5).map((e) => e.id));
            }
          });
        }

        setRelatedTestScenes(sceneList.map((e) => e.id));
      }

      const copy = deepCopy(testClothCollocation);
      copy.tops =
        merchantSubPreference?.clothCollocation &&
        merchantSubPreference?.clothCollocation.tops
          ? merchantSubPreference?.clothCollocation.tops
          : null;
      copy.bottoms =
        merchantSubPreference?.clothCollocation &&
        merchantSubPreference?.clothCollocation.bottoms
          ? merchantSubPreference?.clothCollocation.bottoms
          : null;
      copy.shoe =
        merchantSubPreference?.clothCollocation &&
        merchantSubPreference?.clothCollocation.shoe
          ? merchantSubPreference?.clothCollocation.shoe
          : null;
      copy.others =
        merchantSubPreference?.clothCollocation &&
        merchantSubPreference?.clothCollocation.others
          ? merchantSubPreference?.clothCollocation.others
          : null;
      setTestClothCollocation(copy);

      confirmLoraForm.setFieldValue('testClothCollocation', copy);
      deliveryForm.setFieldValue('testClothCollocation', copy);
    }
  }

  const handleChangePreferenceIndex = (
    index: number,
    model: MaterialModelWithBlogs,
  ) => {
    setSelectedPreferenceIndex(index);

    initDefaultRelatedTestConfig(model, index);
  };

  const MerchantPreferenceOption: React.FC<{
    merchantPreference: MerchantPreference | null;
    model: MaterialModelWithBlogs;
  }> = ({ merchantPreference, model }) => {
    return (
      <>
        <div
          style={{ marginTop: -16 }}
          className={'text12 color-error font-pf weight'}
        >
          本服装商家备注：{model?.clothLoraTrainDetail.matchPrefer}
        </div>
        <div className={'text12 color-error font-pf weight'}>
          商家偏好
          <Tooltip title={'变更商家运营偏好'}>
            <IconFont
              type={'icon-bianji'}
              className={'color-error pointer'}
              style={{ fontSize: 14 }}
              onClick={() => setMerchantPreferenceSetting(model)}
            />
          </Tooltip>
          ：{merchantPreference ? merchantPreference.memo : ''}
        </div>

        {merchantPreference?.preferences && (
          <Tabs
            defaultActiveKey="0"
            type={'card'}
            items={merchantPreference?.preferences.map((item, index) => ({
              key: index + '',
              label: `偏好${index + 1}`,
            }))}
            activeKey={selectedPreferenceIndex + ''}
            onChange={(key) => handleChangePreferenceIndex(Number(key), model)}
            size={'large'}
            indicator={{ size: 102 }}
          />
        )}

        <Form.Item
          label={`测试模特：当前${copyByPreference ? '系统匹配' : '商家历史偏好'}`}
          rules={[
            {
              validator: (_, value) =>
                checkOtherField(
                  'relatedTestScenes',
                  value,
                  '测试模特和测试场景必须同时填写',
                ),
            },
          ]}
        >
          <Select
            style={{ width: '100%' }}
            mode="tags"
            value={relatedTestFaces}
            showSearch={true}
            filterOption={(input, option) => {
              if (option && option.children) {
                return option.children['props'].children[1]
                  .toLowerCase()
                  .includes(input.toLowerCase());
              }
              return false;
            }}
            optionLabelProp="label"
            onChange={(value) => {
              setRelatedTestFaces(value);
            }}
          >
            {getShowTestFaceListByModel(model).map((item) => (
              <Select.Option
                key={item.id}
                value={item.id}
                label={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <img
                      src={item.showImage}
                      alt={item.name}
                      style={{ width: 20, height: 20, marginRight: 8 }}
                    />
                    {item.name}
                  </div>
                }
              >
                <div
                  style={{ display: 'flex', alignItems: 'center', height: 40 }}
                >
                  <img
                    src={item.showImage}
                    alt={item.name}
                    style={{
                      width: 60,
                      height: 60,
                      marginRight: 8,
                      objectFit: 'contain',
                    }}
                  />
                  {item.name}
                </div>
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          label="测试场景："
          style={{ marginTop: -12 }}
          rules={[
            {
              validator: (_, value) =>
                checkOtherField(
                  'relatedTestFaces',
                  value,
                  '测试模特和测试场景必须同时填写',
                ),
            },
          ]}
        >
          <Select
            style={{ width: '100%' }}
            mode="tags"
            showSearch={true}
            filterOption={(input, option) => {
              if (option && option.children) {
                return option.children['props'].children[1]
                  .toLowerCase()
                  .includes(input.toLowerCase());
              }
              return false;
            }}
            value={relatedTestScenes}
            optionLabelProp="label"
            onChange={(value) => {
              setRelatedTestScenes(value);
            }}
          >
            {getShowTestSceneListByModel(model).map((item) => (
              <Select.Option
                key={item.id}
                value={item.id}
                label={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <img
                      src={item.showImage}
                      alt={item.name}
                      style={{ width: 20, height: 20, marginRight: 8 }}
                    />
                    {item.name}
                  </div>
                }
              >
                <div
                  style={{ display: 'flex', alignItems: 'center', height: 40 }}
                >
                  <img
                    src={item.showImage}
                    alt={item.name}
                    style={{
                      width: 60,
                      height: 60,
                      marginRight: 8,
                      objectFit: 'contain',
                    }}
                  />
                  {item.name}
                </div>
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item label="测试图尺寸：" name="testImgProportions">
          <Select options={creationSizeOptions} placeholder="选择出图尺寸" />
        </Form.Item>

        <Form.Item
          label="测试图组数：(最多5组，20张/组)"
          style={{ marginTop: -12 }}
        >
          <InputNumber
            value={relatedTestNum}
            style={{ width: '20%' }}
            min={0}
            max={5}
            addonAfter={'组'}
            onChange={(value) => setRelatedTestNum(value ? value : 1)}
          />
        </Form.Item>

        <Row style={{ marginTop: -12 }}>
          <Col span={12}>
            <Form.Item
              name={['testClothCollocation', 'tops']}
              label="上装"
              initialValue={
                merchantPreference &&
                merchantPreference.preferences &&
                merchantPreference.preferences.length > 0 &&
                merchantPreference.preferences[selectedPreferenceIndex] &&
                merchantPreference.preferences[selectedPreferenceIndex]
                  .clothCollocation
                  ? merchantPreference.preferences[selectedPreferenceIndex]
                      .clothCollocation?.tops
                  : null
              }
            >
              <Input
                placeholder="输入上装(英文)"
                onChange={(e) =>
                  changeTestClothCollocation('tops', e.target.value)
                }
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={['testClothCollocation', 'bottoms']}
              label="下装"
              initialValue={
                merchantPreference &&
                merchantPreference.preferences &&
                merchantPreference.preferences.length > 0 &&
                merchantPreference.preferences[selectedPreferenceIndex] &&
                merchantPreference.preferences[selectedPreferenceIndex]
                  .clothCollocation
                  ? merchantPreference.preferences[selectedPreferenceIndex]
                      .clothCollocation?.bottoms
                  : null
              }
            >
              <Input
                placeholder="输入下装(英文)"
                onChange={(e) =>
                  changeTestClothCollocation('bottoms', e.target.value)
                }
              />
            </Form.Item>
          </Col>
        </Row>
        <Row style={{ marginTop: -12 }}>
          <Col span={12}>
            <Form.Item
              name={['testClothCollocation', 'shoe']}
              label="鞋子"
              initialValue={
                merchantPreference &&
                merchantPreference.preferences &&
                merchantPreference.preferences.length > 0 &&
                merchantPreference.preferences[selectedPreferenceIndex] &&
                merchantPreference.preferences[selectedPreferenceIndex]
                  .clothCollocation
                  ? merchantPreference.preferences[selectedPreferenceIndex]
                      .clothCollocation?.shoe
                  : null
              }
            >
              <Input
                placeholder="输入鞋子(英文)"
                onChange={(e) =>
                  changeTestClothCollocation('shoe', e.target.value)
                }
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={['testClothCollocation', 'others']}
              label="其它"
              initialValue={
                merchantPreference &&
                merchantPreference.preferences &&
                merchantPreference.preferences.length > 0 &&
                merchantPreference.preferences[selectedPreferenceIndex] &&
                merchantPreference.preferences[selectedPreferenceIndex]
                  .clothCollocation
                  ? merchantPreference.preferences[selectedPreferenceIndex]
                      .clothCollocation?.others
                  : null
              }
            >
              <Input
                placeholder="输入其他(英文)"
                onChange={(e) =>
                  changeTestClothCollocation('others', e.target.value)
                }
              />
            </Form.Item>
          </Col>
        </Row>
      </>
    );
  };

  // @ts-ignore
  return (
    <PageContainer>
      <div className="models-form-container">
        <Flex vertical justify={'flex-start'} gap={8}>
          <Flex justify={'flex-start'} gap={8}>
            <Segmented
              defaultValue={'unreceived'}
              value={reviewStatus}
              style={{
                backgroundColor: '#E1E3EB',
                width: 'auto',
                fontSize: 12,
              }}
              onChange={(value: string) => setReviewStatus(value)}
              options={[
                { label: '待审核', value: 'unreviewed' },
                { label: '已审核', value: 'reviewed' },
              ]}
            />
          </Flex>
          <Flex gap={8} align={'center'} justify={'flex-start'}>
            <Tooltip title={'刷新页面数据'}>
              <Button
                icon={<RedoOutlined />}
                onClick={() => {
                  fetchData(page, pageSize);
                  message.success('刷新成功');
                }}
              />
            </Tooltip>

            <InputNumber
              controls={false}
              placeholder="服装id"
              onChange={(e) => setSearchId(e || undefined)}
              value={searchId}
              style={{ width: 80 }}
            />

            <Select
              options={masterOptions}
              style={{ width: 140 }}
              showSearch
              allowClear
              placeholder={'选择商家'}
              optionFilterProp="label"
              defaultActiveFirstOption={true}
              value={searchUserId}
              onChange={(e) => setSearchUserId(e)}
            />

            <Input
              allowClear
              placeholder="服装或商家名称"
              onChange={(e) => setNameLike(e.target.value)}
              value={nameLike}
              style={{ width: 140 }}
            />

            <Select
              value={selectedStatus}
              placeholder="状态"
              onChange={(e) => setSelectedStatus(e)}
              style={{ width: 110 }}
              allowClear
            >
              {AllModelStatus.map((item, index) => (
                <Option key={index} value={item.value}>
                  {item.label}
                </Option>
              ))}
            </Select>
            <Select
              value={reviewType}
              placeholder="审核阶段"
              onChange={(e) => setReviewType(e)}
              style={{ width: 110 }}
              allowClear
            >
              {AllReviewType.map((item, index) => (
                <Option key={index} value={item.value}>
                  {item.label}
                </Option>
              ))}
            </Select>
            {/* <Select
              value={searchReviewer}
              placeholder="审核员"
              onChange={(e) => setSearchReviewer(e)}
              style={{ width: 110 }}
              allowClear
            >
              {reviewers.map((item, index) => (
                <Option key={index} value={item.id}>
                  {item.nickName}
                </Option>
              ))}
            </Select> */}
            {/* <Select
              value={searchPromptUser}
              placeholder="工程师"
              onChange={(e) => setPromptUser(e)}
              style={{ width: 110 }}
              allowClear
            >
              {adminOptions.map((item, index) => (
                <Option key={index} value={item.value}>
                  {item.label}
                </Option>
              ))}
            </Select> */}
          </Flex>
        </Flex>
        <Row gutter={[16, 24]}>
          {models.map((model, index) => (
            <Col key={index}>
              <ImageCard {...model} />
            </Col>
          ))}
        </Row>
        <Pagination
          current={page}
          pageSize={pageSize}
          total={total}
          onChange={handlePageChange}
          showTotal={(total) => `共 ${total} 个模型`}
          showSizeChanger // 允许用户更改每页显示条数
          pageSizeOptions={[24, 48, 96]}
          showQuickJumper // 允许用户快速跳转到某一页
          style={{ marginTop: '16px', textAlign: 'center' }}
        />
      </div>

      <Modal
        title="审核不通过原因"
        open={initialReviewRejectReasonVisible}
        centered
        mask={true}
        width={'400px'}
        styles={{
          header: {
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'center',
          },
          footer: {
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'center',
          },
        }}
        onCancel={() => setInitialReviewRejectReasonVisible(false)}
        footer={[
          <Button
            key="reviewRejectCancel"
            onClick={() => setInitialReviewRejectReasonVisible(false)}
          >
            取消
          </Button>,
          <Button
            disabled={
              reviewRejectReasons.length === 0 || !promptUserId
            }
            key="reviewRejectConfirm"
            type="primary"
            onClick={() => handleReviewRejectConfirm()}
          >
            确认
          </Button>,
        ]}
      >
        <div className={'row-center-block margin-top-12 margin-bottom-24'}>
          <Checkbox.Group
            style={{ display: 'flex', flexDirection: 'column' }}
            options={selectedTrainModelDetail?.status === 'IN_TRAINING' ? initialReviewRejectReasonOptions : againReviewRejectReasonOptions}
            value={reviewRejectReasons}
            onChange={setReviewRejectReasons}
          />
        </div>
        <Flex key="operator" style={{ display: 'inline-flex', margin: '0 10px', alignItems: 'center' }}>
            <div>工程师：</div>
            <Select
              style={{ width: '200px' }}
              value={promptUserId}
              onChange={(value) => {
                setPromptUserId(value);
              }}
            >
              {scheduledAdmins.map((item) => (
                <Option key={item.id} value={item.id}>
                  {item.nickName}
                </Option>
              ))}
            </Select>
        </Flex>
      </Modal>

      {previewImage && (
        <Image
          wrapperStyle={{ display: 'none' }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            afterOpenChange: (visible) => !visible && setPreviewImage(''),
          }}
          src={previewImage}
        />
      )}

      {/*查看lora训练详情，看抠图、打标结果，确认推进lora训练，浮层弹窗页面*/}
      <Modal
        open={
          showTrainDetailModal && selectedTrainModelDetail?.clothLoraTrainDetail
        }
        centered={true}
        onCancel={() => {
          // 取消打标变更
          setShowTrainDetailModal(false);

          // 设置需要手工替换文件为 false
          setIsManualReplacement(0);

          // 设置更新打标按钮为禁用状态
          setUpdateTxtBtnEnabled(false);

          setReviewRejectReasons([]);
          setPromptUserId(undefined);
        }}
        width={'auto'}
        footer={[
          (selectedTrainModelDetail?.status === 'IN_TRAINING') && <Button
            key="reviewPass"
            type="primary"
            disabled={reviewStatus !== 'unreviewed' ||
              ['FAILED', 'COMPLETED'].includes(selectedTrainModelDetail?.tasks?.find(task => task.type === 'initial_review')?.status) ||
              selectedTrainModelDetail?.status !== 'IN_TRAINING' ||
              selectedTrainModelDetail?.clothLoraTrainDetail?.loraConfirmed === 'Y' ||
              selectedTrainModelDetail?.clothLoraTrainDetail?.label?.status !== 'COMPLETED'
            }
            onClick={handleInitialReviewPass}
          >
            初审通过
          </Button>,
          (selectedTrainModelDetail?.status === 'IN_TRAINING') && <Button
            key="reviewReject"
            type="primary"
            disabled={reviewStatus !== 'unreviewed' ||
              ['FAILED', 'COMPLETED'].includes(selectedTrainModelDetail?.tasks?.find(task => task.type === 'initial_review')?.status) ||
              selectedTrainModelDetail?.status !== 'IN_TRAINING' ||
              selectedTrainModelDetail?.clothLoraTrainDetail?.loraConfirmed === 'Y' ||
              selectedTrainModelDetail?.clothLoraTrainDetail?.label?.status !== 'COMPLETED'
            }
            onClick={handleReviewReject}
          >
            初审不通过
          </Button>,
          (selectedTrainModelDetail?.status === 'TESTING') && <Button
            key="againReviewPass"
            type="primary"
            hidden={selectedTrainModelDetail?.status === 'ENABLED' ||
              selectedTrainModelDetail?.clothLoraTrainDetail?.autoGenImg
            }
            disabled={reviewStatus !== 'unreviewed' ||
              ['FAILED', 'COMPLETED'].includes(selectedTrainModelDetail?.tasks?.find(task => task.type === 'again_review')?.status) ||
              selectedTrainModelDetail?.status !== 'TESTING' ||
              selectedTrainModelDetail?.clothLoraTrainDetail?.autoGenImg
            }
            onClick={() => {
              Promise.all([
                deliverModel({
                  id: selectedTrainModelDetail?.id
                }),
                updateWorkflowTaskByBizId({
                  bizId: selectedTrainModelDetail?.id,
                  type: 'again_review',
                  status: 'COMPLETED'
                })
              ]).then(() => {
                message.success('审核通过');
                setShowTrainDetailModal(false);
                fetchData(page, pageSize);
              });
            }}
          >
            审核通过
          </Button>,
          (selectedTrainModelDetail?.status === 'TESTING') && <Button
            key="againReviewPassAndDeliver"
            type="primary"
            hidden={selectedTrainModelDetail?.status !== 'TESTING' ||
              !selectedTrainModelDetail?.clothLoraTrainDetail?.autoGenImg
            }
            disabled={reviewStatus !== 'unreviewed' ||
              ['FAILED', 'COMPLETED'].includes(selectedTrainModelDetail?.tasks?.find(task => task.type === 'again_review')?.status) ||
              selectedTrainModelDetail?.status !== 'TESTING' ||
              !selectedTrainModelDetail?.clothLoraTrainDetail?.autoGenImg ||
              selectedTrainModelDetail?.clothLoraTrainDetail?.confirmCanDeliverInfo?.confirmCanDeliver === 'Y'
            }
            onClick={() => {
              confirmCanDeliver(selectedTrainModelDetail?.id)
                .then(res => {
                  if (res) {
                    message.success('开始启动自动出图并交付流程');
                    setShowTrainDetailModal(false);
                    fetchData(page, pageSize);

                    updateWorkflowTaskByBizId({
                      bizId: selectedTrainModelDetail?.id,
                      type: 'again_review',
                      status: 'COMPLETED'
                    })
                  } else {
                    message.error('操作失败');
                  }
                });
            }}
          >
            {selectedTrainModelDetail?.status === 'TESTING' &&
             selectedTrainModelDetail?.clothLoraTrainDetail?.autoGenImg &&
             selectedTrainModelDetail?.clothLoraTrainDetail?.confirmCanDeliverInfo?.confirmCanDeliver === 'Y' ? '自动出图中...' : '自动出图并交付'}
          </Button>,
          (selectedTrainModelDetail?.status === 'TESTING') && <Button
            key="againReviewReject"
            type="primary"
            disabled={reviewStatus !== 'unreviewed' ||
              ['FAILED', 'COMPLETED'].includes(selectedTrainModelDetail?.tasks?.find(task => task.type === 'again_review')?.status) ||
              selectedTrainModelDetail?.status !== 'TESTING'}
            onClick={handleReviewReject}
          >
            复审不通过
          </Button>,
        ]}
        styles={{
          mask: {
            backgroundColor: 'rgba(0,0,0,0.4)',
          },
        }}
      >
        <div className="lora-detail-content">
          {/* 左半部分 */}
          <div className={'lora-detail-left'}>
            <div className={'lora-detail-left-inner'}>
              <div className={'lora-left-inner-top'}>
                <div className="font-pf text14 weight">模型</div>
                <img
                  alt="cloth model"
                  style={{ cursor: 'pointer' }}
                  onClick={() =>
                    handlePreviewUrl(selectedTrainModelDetail?.showImage)
                  }
                  src={selectedTrainModelDetail?.showImage}
                />
                <div className="font-pf text14">
                  {selectedTrainModelDetail?.name}
                </div>
              </div>

              <div className={'lora-detail-pair'}>
                <div className="font-pf text14 weight">操作人</div>
                <div className="font-pf text12">
                  {selectedTrainModelDetail?.operatorNick}
                </div>
              </div>
              <div className={'lora-detail-pair'}>
                <div className="font-pf text14 weight">创建时间</div>
                <div className="font-pf text12">
                  {selectedTrainModelDetail?.createTime}
                </div>
              </div>

              {selectedTrainModelDetail?.clothLoraTrainDetail?.materialType !==
                'face' && (
                <div className={'lora-detail-pair'}>
                  <div className="font-pf text14 weight">
                    预处理
                    <span className={'text12 color-a0 normal margin-left-4'}>
                      {getServerUrl('prepareView')}
                    </span>
                  </div>
                  <div className="font-pf text12">
                    {selectedTrainModelDetail?.clothLoraTrainDetail?.prepareView
                      ?.status === 'COMPLETED'
                      ? selectedTrainModelDetail?.prepareViewFinishTime + '完成'
                      : '未完成'}
                  </div>
                </div>
              )}

              <div className={'lora-detail-pair'}>
                <div className="font-pf text14 weight">
                  抠图
                  <span className={'text12 color-a0 normal margin-left-4'}>
                    {getServerUrl('cutout')}
                  </span>
                </div>
                <div className="font-pf text12">
                  {selectedTrainModelDetail?.cutoutFiles &&
                  selectedTrainModelDetail?.cutoutFiles?.length > 0
                    ? selectedTrainModelDetail?.cutoutFinishTime + '完成'
                    : '未完成'}
                </div>
              </div>
              <div className={'lora-detail-pair'}>
                <div className="font-pf text14 weight">
                  打标
                  <span className={'text12 color-a0 normal margin-left-4'}>
                    {getServerUrl('label')}
                  </span>
                </div>
                <div className="font-pf text12">
                  {selectedTrainModelDetail?.labelRetFiles &&
                  selectedTrainModelDetail?.labelRetFiles?.length > 0
                    ? selectedTrainModelDetail?.labelFinishTime + '完成'
                    : '未完成'}
                </div>
              </div>

              <ReviewTaskStatus
                model={selectedTrainModelDetail}
                users={reviewers}
                type="initial_review"
              />

              <div className={'lora-detail-pair'}>
                <div className="font-pf text14 weight">
                  训练
                  <span className={'text12 color-a0 normal margin-left-4'}>
                    {getServerUrl('lora')}
                  </span>
                </div>
                {selectedTrainModelDetail &&
                  !selectedTrainModelDetail?.loraStatus && (
                    <div className="font-pf text12">状态：未开始</div>
                  )}
                {loraConfirmed() && (
                  <>
                    <div className="font-pf text12">
                      {loraConfirmedTime()}确认
                    </div>
                    <Flex
                      className="font-pf"
                      style={{ fontSize: 10 }}
                      vertical={true}
                    >
                      <div>
                        训练类型：
                        {selectedTrainModelDetail?.clothLoraTrainDetail
                          ?.loraType || 'sdxl'}
                      </div>
                      <Flex gap={8}>
                        <span>
                          训练次数：
                          {selectedTrainModelDetail?.clothLoraTrainDetail
                            ?.maxTrainStep || 4000}
                        </span>
                        <span>
                          {selectedTrainModelDetail?.clothLoraTrainDetail?.lr &&
                            `学习率：${selectedTrainModelDetail?.clothLoraTrainDetail?.lr}`}
                        </span>
                      </Flex>
                      <Flex gap={8}>
                        <span>
                          {selectedTrainModelDetail?.clothLoraTrainDetail
                            ?.contentOrStyle &&
                            `学习内容：${selectedTrainModelDetail?.clothLoraTrainDetail?.contentOrStyle}`}
                        </span>
                        <span>
                          {selectedTrainModelDetail?.clothLoraTrainDetail
                            ?.rank &&
                            `Rank: ${selectedTrainModelDetail?.clothLoraTrainDetail?.rank}`}
                        </span>
                      </Flex>
                      <Flex gap={8}>
                        <span>
                          {selectedTrainModelDetail?.clothLoraTrainDetail
                            ?.alpha &&
                            `Alpha: ${selectedTrainModelDetail?.clothLoraTrainDetail?.alpha}`}
                        </span>
                        <span>
                          {selectedTrainModelDetail?.clothLoraTrainDetail
                            ?.dropout &&
                            `Dropout: ${selectedTrainModelDetail?.clothLoraTrainDetail?.dropout}`}
                        </span>
                      </Flex>
                      <Flex gap={8}>
                        <span>
                          {selectedTrainModelDetail?.clothLoraTrainDetail
                            ?.resolution &&
                            `Resolution: ${selectedTrainModelDetail?.clothLoraTrainDetail?.resolution}`}
                        </span>
                      </Flex>
                    </Flex>
                  </>
                )}

                {selectedTrainModelDetail &&
                  selectedTrainModelDetail?.loraStatus === 'QUEUED' && (
                    <>
                      <div className="font-pf text12">状态：排队中</div>
                    </>
                  )}
                {selectedTrainModelDetail &&
                  selectedTrainModelDetail?.loraStatus === 'RUNNING' && (
                    <>
                      <div className="font-pf text12">
                        {selectedTrainModelDetail?.loraStartTime}开始
                      </div>
                      <div className="font-pf text12">任务状态：运行中</div>
                    </>
                  )}
                {selectedTrainModelDetail &&
                  selectedTrainModelDetail?.loraStatus === 'COMPLETED' && (
                    <>
                      <div className="font-pf text12">
                        {selectedTrainModelDetail?.loraStartTime}开始
                      </div>
                      <div className="font-pf text12">
                        {selectedTrainModelDetail?.loraFinishTime}完成
                      </div>
                      <div className="font-pf text12">状态：已完成</div>
                    </>
                  )}
              </div>
              <TestImageStatus item={selectedTrainModelDetail} />
              <ReviewTaskStatus
                model={selectedTrainModelDetail}
                users={reviewers}
                type="again_review"
              />

              <div className={'lora-detail-pair'}>
                <div className="font-pf text14 weight">交付时间</div>
                <div className="font-pf text12">
                  {selectedTrainModelDetail && selectedTrainModelDetail?.extInfo
                    ? selectedTrainModelDetail?.extInfo['deliveryTime']
                    : ''}
                </div>
              </div>
              <div className={'lora-detail-pair lora-detail-pair-row'}>
                <div className="font-pf text14 weight">模型ID</div>
                <div className="font-pf text12">
                  {selectedTrainModelDetail?.id}
                </div>
              </div>
            </div>
          </div>

          {/* 右半部分 */}
          <div className="lora-detail-right">
            {/* 原图 */}
            <Flex align={'baseline'}>
              <div
                className="font-pf text14 weight"
                style={{
                  marginTop: 10,
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                }}
                onClick={() => {
                  navigator.clipboard
                    .writeText(
                      selectedTrainModelDetail?.clothLoraTrainDetail?.clothDir,
                    )
                    .then(() => {
                      message.info('目录已复制到剪贴板');
                    });
                }}
              >
                原图：
                <span
                  style={{
                    fontSize: 12,
                    textDecoration: 'underline',
                  }}
                >
                  {selectedTrainModelDetail?.clothLoraTrainDetail?.clothDir}
                </span>
                <IconFont
                  type={'icon-icon_fuzhi'}
                  style={{ fontSize: 18, marginLeft: 5 }}
                />
              </div>

              <Button
                type={'default'}
                size={'small'}
                style={{ borderRadius: 8, fontSize: 12, marginLeft: 8 }}
                onClick={() => setShowOriginal(!showOriginal)}
              >
                {showOriginal ? '隐藏' : '展开'}
              </Button>

              <Button
                type={'default'}
                size={'small'}
                loading={downloading}
                style={{ borderRadius: 8, fontSize: 12, marginLeft: 8 }}
                onClick={() => {
                  setDownloading(true);

                  let urls = getAllUploadImgs();
                  batchDownload(
                    urls,
                    selectedTrainModelDetail?.name + '_' + new Date().getTime(),
                  )
                    .then((res) => {
                      if (res) {
                        download(res);
                        message.success('下载完成');
                      }
                    })
                    .finally(() => setDownloading(false));
                }}
              >
                批量下载原图
              </Button>
            </Flex>
            <div className="lora-img-list">
              {showOriginal &&
                getAllUploadImgs().map((f: string, index: number) => (
                  <div key={index} className="lora-detail-img-item">
                    <img
                      src={f}
                      alt={''}
                      onClick={() =>
                        handlePreviewUrl(f, index, getAllUploadImgs())
                      }
                    />
                  </div>
                ))}
            </div>

            {/*抠图结果*/}
            {selectedTrainModelDetail?.cutoutFiles &&
              selectedTrainModelDetail?.cutoutFiles?.length > 0 && (
                <>
                  <Flex align={'baseline'}>
                    <div
                      className="font-pf text14 weight"
                      style={{
                        marginTop: 10,
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                      }}
                      onClick={() => {
                        navigator.clipboard
                          .writeText(
                            selectedTrainModelDetail?.clothLoraTrainDetail
                              ?.clothDir,
                          )
                          .then(() => {
                            message.info('目录已复制到剪贴板');
                          });
                      }}
                    >
                      <span>抠图结果：</span>
                      <span
                        style={{
                          fontSize: 12,
                          textDecoration: 'underline',
                        }}
                      >
                        {
                          selectedTrainModelDetail?.clothLoraTrainDetail
                            ?.cutoutRetDir
                        }
                      </span>
                      <IconFont
                        type={'icon-icon_fuzhi'}
                        style={{ fontSize: 18, marginLeft: 5, marginRight: 5 }}
                      />
                    </div>
                    <Button
                      type={'default'}
                      size={'small'}
                      style={{ borderRadius: 8, fontSize: 12 }}
                      onClick={() => setShowCutout(!showCutout)}
                    >
                      {showCutout ? '隐藏' : '展开'}
                    </Button>
                  </Flex>

                  <div className="lora-img-list">
                    {showCutout &&
                      selectedTrainModelDetail &&
                      selectedTrainModelDetail?.cutoutFiles &&
                      selectedTrainModelDetail?.cutoutFiles.map(
                        (f: FileVO, index: number) => {
                          if (f.type === 'img') {
                            return (
                              <div key={index} className="lora-detail-img-item">
                                <img
                                  src={f.imgUrl}
                                  alt={f.fileName}
                                  onClick={() =>
                                    handlePreviewUrl(
                                      f.imgUrl,
                                      index,
                                      selectedTrainModelDetail?.cutoutFiles.map(
                                        (f: FileVO, index: number) => f.imgUrl,
                                      ),
                                    )
                                  }
                                />
                                <div style={{ maxWidth: 160, fontSize: 12 }}>
                                  {f.fileName}
                                </div>
                              </div>
                            );
                          }
                        },
                      )}
                  </div>
                </>
              )}

            {/*打标结果*/}
            {selectedTrainModelDetail &&
              selectedTrainModelDetail?.labelRetFiles &&
              groupedLabelFiles &&
              Array.from(groupedLabelFiles.entries()).map(
                ([fileDir, files]) => (
                  <>
                    <div
                      className="font-pf text14 weight"
                      style={{
                        marginTop: 10,
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                      }}
                      onClick={() => {
                        navigator.clipboard.writeText(fileDir).then(() => {
                          message.info('目录已复制到剪贴板');
                        });
                      }}
                    >
                      <span style={{ color: 'black' }}>
                        {getLabelRetDirShowName(fileDir)}打标结果：
                      </span>
                      <span
                        style={{ fontSize: 12, textDecoration: 'underline' }}
                      >
                        {fileDir}
                      </span>
                      <IconFont
                        type={'icon-icon_fuzhi'}
                        style={{ fontSize: 18, marginLeft: 5 }}
                      />
                    </div>

                    {findLabelFailTxtFileName().length > 0 && (
                      <div
                        style={{ color: 'red', fontSize: 16, fontWeight: 500 }}
                      >
                        请手工更新打标失败文件后，提交打标变更：
                        {findLabelFailTxtFileName().join(', ')}
                      </div>
                    )}

                    <div className="lora-img-list">
                      {files.map((f: FileVO, index: number) => (
                        <div
                          key={index}
                          className="label-ret-item"
                          onDrop={(event) => {
                            event.preventDefault();
                            //只响应图片控件的拖拽事件
                            if (f.type !== 'img') {
                              return;
                            }
                            let filePath = f.fileDir + '/' + f.fileName;
                            let deleted = labelImgFiles.get(filePath)?.deleted;
                            if (deleted) {
                              message.error('该图片已被删除，无法替换');
                            } else {
                              const file = event.dataTransfer.files[0];
                              handleLabelImgChange(file, f);
                            }
                          }}
                          onDragOver={(e) => {
                            e.preventDefault();
                            if (f.type !== 'img') {
                              return;
                            }
                          }}
                        >
                          {(() => {
                            // 如果是图片，则显示图片
                            if (f.type === 'img') {
                              let filePath = f.fileDir + '/' + f.fileName;

                              // 如果文件被删除，则不显示
                              let deleted =
                                labelImgFiles.get(filePath)?.deleted;

                              return (
                                <>
                                  <div style={{ position: 'relative' }}>
                                    <img
                                      src={
                                        labelImgFiles.get(filePath)?.url
                                          ? labelImgFiles.get(filePath)?.url
                                          : f.imgUrl
                                      }
                                      alt={''}
                                      onClick={() =>
                                        handlePreviewUrl(
                                          labelImgFiles.get(filePath)?.url
                                            ? labelImgFiles.get(filePath)?.url
                                            : f.imgUrl,
                                        )
                                      }
                                    />

                                    {/*上传新的打标图片*/}
                                    <Tooltip title={'点击或拖拽至此，替换图片'}>
                                      <IconFont
                                        type={'icon-icon_qiehuan'}
                                        style={{
                                          position: 'absolute',
                                          top: 0,
                                          right: 0,
                                          cursor: 'pointer',
                                          fontSize: 24,
                                        }}
                                        onClick={() => {
                                          if (deleted) {
                                            message.error(
                                              '该图片已被删除，无法替换',
                                            );
                                          } else {
                                            labelImgInputRefs.current[
                                              filePath
                                            ].click();
                                          }
                                        }}
                                      />
                                    </Tooltip>

                                    {/*删除打标图片*/}
                                    {!deleted && (
                                      <Tooltip
                                        title={'删除图片'}
                                        placement="bottom"
                                      >
                                        <IconFont
                                          style={{
                                            position: 'absolute',
                                            top: 36,
                                            right: 0,
                                            cursor: 'pointer',
                                            fontSize: 24,
                                          }}
                                          type={'icon-icon_shanchu'}
                                          onClick={() => {
                                            const newLabelImgFiles = new Map(
                                              labelImgFiles,
                                            );
                                            newLabelImgFiles.set(filePath, {
                                              filePath:
                                                f.fileDir + '/' + f.fileName,
                                              url: '',
                                              deleted: true,
                                            });
                                            setLabelImgFiles(newLabelImgFiles);
                                            setUpdateTxtBtnEnabled(true);
                                          }}
                                        />
                                      </Tooltip>
                                    )}

                                    {/*恢复删除的图片*/}
                                    {deleted && (
                                      <Tooltip
                                        title={'撤销删除'}
                                        placement="bottom"
                                      >
                                        <RollbackOutlined
                                          style={{
                                            position: 'absolute',
                                            top: 36,
                                            right: 4,
                                            cursor: 'pointer',
                                            fontSize: 18,
                                            color: 'red',
                                          }}
                                          onClick={() => {
                                            const newLabelImgFiles = new Map(
                                              labelImgFiles,
                                            );
                                            newLabelImgFiles.delete(filePath);
                                            setLabelImgFiles(newLabelImgFiles);
                                            setUpdateTxtBtnEnabled(true);
                                          }}
                                        />
                                      </Tooltip>
                                    )}

                                    <input
                                      type="file"
                                      ref={(el) =>
                                        (labelImgInputRefs.current[filePath] =
                                          el)
                                      }
                                      onChange={(e) => {
                                        // @ts-ignore
                                        const file = e.target.files[0];
                                        handleLabelImgChange(file, f);
                                      }}
                                      accept="image/png"
                                      style={{ display: 'none' }}
                                    />
                                  </div>

                                  {deleted && (
                                    <div
                                      style={{
                                        maxWidth: 160,
                                        fontSize: 12,
                                        color: getImgFileColorByFile(f),
                                      }}
                                    >
                                      <del>{f.fileName}</del>
                                    </div>
                                  )}
                                  {!deleted && (
                                    <div
                                      style={{
                                        maxWidth: 160,
                                        fontSize: 12,
                                        color: getImgFileColorByFile(f),
                                      }}
                                    >
                                      {f.fileName}
                                    </div>
                                  )}
                                </>
                              );

                              // txt文本
                            } else {
                              return (
                                <>
                                  <textarea
                                    disabled
                                    value={
                                      updateTxtFiles.get(
                                        f.fileDir + '/' + f.fileName,
                                      ) || f.textContent
                                    }
                                    style={{ fontSize: 12 }}
                                    onChange={(e) => onLabelTextChange(e, f)}
                                  />
                                  <div
                                    style={{
                                      maxWidth: 160,
                                      fontSize: 12,
                                      color: getTxtFileColorByFile(f),
                                    }}
                                  >
                                    {f.fileName}
                                  </div>
                                </>
                              );
                            }
                          })()}
                        </div>
                      ))}
                    </div>
                  </>
                ),
              )}
          </div>
        </div>
      </Modal>

      {showConfirmLoraModal && (
        <Modal
          open={showConfirmLoraModal}
          width={800}
          centered
          title={'设置lora训练参数'}
          onCancel={() => {
            setShowConfirmLoraModal(false);
          }}
          okText={needCutoutAgain ? '重新抠图打标' : '提交lora训练'}
          okButtonProps={{
            hidden: loraConfirmed() || !selectedTrainModelDetail?.labelRetFiles,
          }}
          onOk={() => {
            confirmLoraForm
              .validateFields()
              .then((values) => {
                //重新抠图
                if (needCutoutAgain) {
                  onCutoutAgain();
                } else {
                  onConfirmLora();
                }
              })
              .catch((info) => {
                console.log('Validate Failed:', info);
              });
          }}
        >
          <Form
            layout="vertical"
            style={{ paddingLeft: 12 }}
            form={confirmLoraForm}
          >
            <Form.Item
              label="是否需要重新抠图："
              name="needCutoutAgain"
              style={{ width: '100%' }}
              initialValue={false}
            >
              <Radio.Group
                value={needCutoutAgain}
                onChange={(e) => setNeedCutoutAgain(e.target.value)}
              >
                <Radio value={false}>不用，直接训练</Radio>
                <Radio value={true}>重新抠图</Radio>
              </Radio.Group>
            </Form.Item>

            {needCutoutAgain && (
              <>
                <Form.Item
                  label="抠图关键词（可空，可多选）："
                  name="cutoutKeyword"
                  style={{ width: '100%' }}
                >
                  <Select
                    key={'cutoutKeyword'}
                    mode="tags"
                    placeholder="请选择（可多选）"
                    options={cutoutKeywordOptions}
                    value={cutoutKeyword}
                    allowClear={true}
                    onChange={(value) => setCutoutKeyword(value)}
                  />
                </Form.Item>

                <Form.Item
                  label="是否需要重新分析图片："
                  name="prepareViewAgainWhenCutoutAgain"
                  style={{ width: '100%' }}
                >
                  <Radio.Group
                    value={prepareViewAgainWhenCutoutAgain}
                    defaultValue={false}
                    onChange={(e) =>
                      setPrepareViewAgainWhenCutoutAgain(e.target.value)
                    }
                  >
                    <Radio value={false}>不用，直接抠图（默认）</Radio>
                    <Radio value={true}>重新分析图片，再抠图</Radio>
                  </Radio.Group>
                </Form.Item>

                <Form.Item
                  label="是否需要抠图："
                  name="cutoutOnlyUpscale"
                  style={{ width: '100%' }}
                  valuePropName={'cutoutOnlyUpscale'}
                >
                  <Radio.Group
                    value={cutoutOnlyUpscale}
                    defaultValue={'N'}
                    onChange={(e) => setCutoutOnlyUpscale(e.target.value)}
                  >
                    <Radio value={'N'}>抠图</Radio>
                    <Radio value={'Y'}>不抠图</Radio>
                  </Radio.Group>
                </Form.Item>

                <Form.Item
                  label="是否大比例抠图剪裁（实验参数）："
                  name="cut4ScaleUp"
                  style={{ width: '100%' }}
                  valuePropName={'cut4ScaleUp'}
                >
                  <Radio.Group
                    value={cut4ScaleUp}
                    defaultValue={'N'}
                    onChange={(e) => setCut4ScaleUp(e.target.value)}
                  >
                    <Radio value={'N'}>不用，保持现状</Radio>
                    <Radio value={'Y'}>需要，抠图结果服装像素占比更大</Radio>
                  </Radio.Group>
                </Form.Item>

                <Form.Item
                  label={
                    <>
                      打标prompt：
                      <LoraPromptsSwitch
                        value={clothDetailsPrompt}
                        tag={labelType}
                        onChange={handleChangeClothPrompt}
                      />
                    </>
                  }
                  name="clothDetailsPrompt"
                  style={{ width: '100%' }}
                  valuePropName={'clothDetailsPrompt'}
                >
                  <TextArea rows={4} value={clothDetailsPrompt} />
                </Form.Item>

                <Form.Item
                  label="抠图样本保存尺寸："
                  name="imageSize"
                  style={{ width: '100%' }}
                >
                  <Input
                    style={{ width: '100%' }}
                    defaultValue={'1024'}
                    value={imageSize}
                    onChange={(e) => {
                      setImageSize(e.target.value);
                    }}
                  />
                </Form.Item>

                <Form.Item
                  label="抠图样本是否保存为方形（默认否）："
                  name="isSquare"
                  style={{ width: '100%' }}
                  hidden={true}
                >
                  <Radio.Group
                    defaultValue={false}
                    value={isSquare}
                    onChange={(e) => setIsSquare(e.target.value)}
                  >
                    <Radio value={false}>否，按原始比例</Radio>
                    <Radio value={true}>是，扩展为方形</Radio>
                  </Radio.Group>
                </Form.Item>
              </>
            )}

            {!needCutoutAgain && (
              <>
                <Row style={{ marginTop: -4 }}>
                  <Col span={4}>
                    <Form.Item
                      label="训练类型："
                      style={{ marginTop: -12 }}
                      rules={[{ required: true, message: '请上传展示图片' }]}
                    >
                      <Radio.Group
                        value={loraTrainType}
                        onChange={(e) => {
                          setLoraTrainType(e.target.value);
                          setMaxTrainStep(
                            e.target.value === 'flux'
                              ? getDefaultFluxSteps()
                              : 4000,
                          );
                        }}
                      >
                        <Radio.Button value={'flux'}>FLUX</Radio.Button>
                      </Radio.Group>
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item
                      label="训练步数："
                      rules={[{ required: true, message: '请指定步数' }]}
                      style={{ marginTop: -12 }}
                    >
                      <InputNumber
                        min={1}
                        precision={0}
                        style={{ width: '60%' }}
                        value={maxTrainStep}
                        onChange={(e) => {
                          setMaxTrainStep(
                            e ||
                              (loraTrainType === 'flux'
                                ? getDefaultFluxSteps()
                                : 4000),
                          );
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item
                      label="学习率："
                      rules={[{ required: true }]}
                      style={{ marginTop: -12 }}
                    >
                      <InputNumber
                        min={0.00005}
                        max={0.0005}
                        style={{ width: '80%' }}
                        value={trainParams['lr']}
                        onChange={(e) => {
                          setTrainParams({ ...trainParams, lr: e || 0.0002 });
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item
                      label="学习内容："
                      rules={[{ required: true }]}
                      style={{ marginTop: -12 }}
                    >
                      <Select
                        style={{ width: '60%' }}
                        options={[
                          { label: '内容', value: 'content' },
                          { label: '风格', value: 'style' },
                        ]}
                        value={trainParams['contentOrStyle']}
                        onChange={(e) => {
                          setTrainParams({
                            ...trainParams,
                            contentOrStyle: e || 'content',
                          });
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item
                      label="rank："
                      rules={[{ required: true }]}
                      style={{ marginTop: -12 }}
                    >
                      <InputNumber
                        min={8}
                        max={256}
                        precision={0}
                        style={{ width: '50%' }}
                        value={trainParams['rank']}
                        onChange={(e) => {
                          setTrainParams({ ...trainParams, rank: e || 32 });
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item
                      label="alpha："
                      rules={[{ required: true }]}
                      style={{ marginTop: -12 }}
                    >
                      <InputNumber
                        min={1}
                        max={256}
                        precision={0}
                        style={{ width: '50%' }}
                        value={trainParams['alpha']}
                        onChange={(e) => {
                          setTrainParams({ ...trainParams, alpha: e || 16 });
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item
                      label="dropout："
                      rules={[{ required: true }]}
                      style={{ marginTop: -12 }}
                    >
                      <InputNumber
                        min={0.01}
                        max={0.3}
                        style={{ width: '50%' }}
                        value={trainParams['dropout']}
                        onChange={(e) => {
                          setTrainParams({ ...trainParams, dropout: e || 0.2 });
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item
                      label="分辨率："
                      rules={[{ required: true }]}
                      style={{ marginTop: -12 }}
                    >
                      <Input
                        style={{ width: '80%' }}
                        value={trainParams['resolution']}
                        onChange={(e) => {
                          setTrainParams({
                            ...trainParams,
                            resolution: e.target.value,
                          });
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={16}>
                    <Form.Item
                      label={
                        <>
                          分层设置：
                          <SystemConfigSwitch
                            value={trainParams['trainExtInfo']}
                            keyLabels={[
                              { key: 'NONE', label: '不分层' },
                              {
                                key: 'LORA_TRAIN_EXT_INFO.cloth',
                                label: '默认分层',
                              },
                            ]}
                            onChange={(value) =>
                              setTrainParams({
                                ...trainParams,
                                trainExtInfo: value,
                              })
                            }
                          />
                        </>
                      }
                      rules={[{ required: false }]}
                      style={{ marginTop: -12 }}
                    >
                      <TextArea
                        rows={1}
                        style={{ width: '100%' }}
                        value={trainParams['trainExtInfo']}
                        onChange={(e) => {
                          setTrainParams({
                            ...trainParams,
                            trainExtInfo: e.target.value,
                          });
                        }}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                {selectedTrainModelDetail && (
                  <MerchantPreferenceOption
                    merchantPreference={
                      selectedTrainModelDetail?.merchantPreference
                    }
                    model={selectedTrainModelDetail}
                  />
                )}
              </>
            )}
          </Form>
        </Modal>
      )}


      {/* 图片预览 */}
      <ImgPreview
        previewVisible={previewVisible}
        handleCancelPreview={handleCancelPreview}
        previewImage={previewImage}
        needSwitch={!!previewImgs}
        previewIdx={previewIdx}
        previewImgs={previewImgs}
        needWatermark={false}
      />

      {recordModelId &&
        <LoraImageModal modelId={recordModelId} queryAll={true} onClose={() => setRecordModelId(null)} />
      }
    </PageContainer>
  );
}
