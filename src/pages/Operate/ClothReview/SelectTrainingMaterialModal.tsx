import { FileVO } from '@/services/MaterialModelController';
import { Button, Checkbox, Col, Modal, Row, Tooltip } from 'antd';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import React from 'react';

// 样式常量
const STYLES = {
  modalTitle: {
    borderBottom: '1px solid #f0f0f0',
    margin: '0 -24px',
    padding: '0 24px 16px',
  },
  titleText: {
    fontSize: 16,
    fontWeight: 500,
  },
  footer: {
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: 8,
  },
  selectAllButton: {
    display: 'flex',
    alignItems: 'center',
    gap: 4,
    backgroundColor: '#52c41a',
    borderColor: '#52c41a',
  },
  contentWrapper: {
    maxHeight: '70vh',
    overflow: 'auto',
    margin: '0 -8px',
    padding: '0 8px',
  },
} as const;

// 类型定义
interface ImageFile {
  filePath: string;
  url: string;
  deleted: boolean;
  isTrainingMaterial?: boolean;
}

const ImageItem = ({
  file,
  imgFile,
  onToggleSelect,
  handlePreviewUrl,
}: {
  file: FileVO;
  imgFile: ImageFile | undefined;
  onToggleSelect: (filePath: string, checked: boolean) => void;
  handlePreviewUrl: (url: string) => void;
}) => {
  const filePath = `${file.fileDir}/${file.fileName}`;

  const handleItemClick = (e: React.MouseEvent) => {
    if (e.target instanceof HTMLImageElement) {
      handlePreviewUrl(imgFile?.url || file.imgUrl);
      return;
    }
    onToggleSelect(filePath, !imgFile?.isTrainingMaterial);
  };

  const handleCheckboxChange = (e: CheckboxChangeEvent) => {
    onToggleSelect(filePath, e.target.checked);
    e.stopPropagation();
  };

  return (
    <Col span={4}>
      <div
        className="label-ret-item"
        style={{
          position: 'relative',
          border: `1px solid ${imgFile?.isTrainingMaterial ? '#b7eb8f' : '#f0f0f0'}`,
          borderRadius: '6px',
          padding: '4px',
          backgroundColor: imgFile?.isTrainingMaterial ? '#f6ffed' : 'white',
          transition: 'all 0.3s',
          cursor: 'pointer',
        }}
        onClick={handleItemClick}
      >
        <Checkbox
          checked={imgFile?.isTrainingMaterial}
          style={{
            position: 'absolute',
            top: 8,
            right: 8,
            zIndex: 1,
          }}
          onChange={handleCheckboxChange}
        />
        <div
          style={{
            width: '100%',
            height: 160,
            overflow: 'hidden',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#fafafa',
            borderRadius: '4px',
            marginBottom: 4,
          }}
        >
          <img
            src={imgFile?.url || file.imgUrl}
            alt={file.fileName}
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
              objectFit: 'contain',
            }}
          />
        </div>
        <Tooltip title={file.fileName}>
          <div
            style={{
              fontSize: 12,
              color: '#666',
              textOverflow: 'ellipsis',
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              padding: '0 4px',
            }}
          >
            {file.fileName}
          </div>
        </Tooltip>
      </div>
    </Col>
  );
};

// 主组件
export default function SelectTrainingMaterialModal({
  open,
  onCancel,
  onConfirm,
  labelImgFiles,
  setLabelImgFiles,
  selectedTrainModelDetail,
  groupedLabelFiles,
  handlePreviewUrl,
  getLabelRetDirShowName,
}: {
  // 是否显示
  open: boolean;
  // 取消
  onCancel: () => void;
  // 确认
  onConfirm: () => void;
  // 图片文件
  labelImgFiles: Map<string, ImageFile>;
  // 设置图片文件
  setLabelImgFiles: (files: Map<string, ImageFile>) => void;
  selectedTrainModelDetail: any;
  // 分组标签文件
  groupedLabelFiles: Map<string, FileVO[]>;
  // 预览图片
  handlePreviewUrl: (url: string) => void;
  // 获取标签文件夹显示名称
  getLabelRetDirShowName: (fileDir: string) => string;
}) {
  const handleToggleSelect = (filePath: string, checked: boolean) => {
    const newLabelImgFiles = new Map(labelImgFiles);
    const imgFile = newLabelImgFiles.get(filePath);
    if (imgFile) {
      newLabelImgFiles.set(filePath, {
        ...imgFile,
        isTrainingMaterial: checked,
      });
      setLabelImgFiles(newLabelImgFiles);
    }
  };

  const handleToggleAll = () => {
    const allSelected = Array.from(labelImgFiles.values())
      .filter((item) => !item.deleted)
      .every((item) => item.isTrainingMaterial === true);

    const newLabelImgFiles = new Map(labelImgFiles);
    newLabelImgFiles.forEach((value, key) => {
      if (!value.deleted) {
        newLabelImgFiles.set(key, {
          ...value,
          isTrainingMaterial: !allSelected,
        });
      }
    });

    setLabelImgFiles(newLabelImgFiles);
  };

  const isAllSelected = Array.from(labelImgFiles.values())
    .filter((item) => !item.deleted)
    .every((item) => item.isTrainingMaterial === true);

  const isIndeterminate =
    Array.from(labelImgFiles.values())
      .filter((item) => !item.deleted)
      .some((item) => item.isTrainingMaterial === true) && !isAllSelected;

  return (
    <Modal
      title={
        <div style={STYLES.modalTitle}>
          <div style={STYLES.titleText}>
            请选择需要保留mask信息并同步记录的图片
          </div>
        </div>
      }
      open={open}
      onCancel={onCancel}
      width={1200}
      centered
      footer={
        <div style={STYLES.footer}>
          <Button onClick={onCancel}>取消</Button>
          <Button
            type="primary"
            style={STYLES.selectAllButton}
            onClick={handleToggleAll}
          >
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: 4,
                color: '#fff',
                cursor: 'pointer',
              }}
            >
              <Checkbox
                checked={isAllSelected}
                indeterminate={isIndeterminate}
                style={{ color: '#fff' }}
              />
              <span>全选</span>
            </div>
          </Button>
          <Button type="primary" onClick={onConfirm}>
            确定
          </Button>
        </div>
      }
    >
      <div style={STYLES.contentWrapper}>
        {selectedTrainModelDetail &&
          labelImgFiles.size > 0 &&
          groupedLabelFiles &&
          Array.from(groupedLabelFiles.entries()).map(([fileDir, files]) => {
            const dirImages = files.filter((f) => {
              if (f.type === 'img') {
                const filePath = f.fileDir + '/' + f.fileName;
                return (
                  labelImgFiles.has(filePath) &&
                  !labelImgFiles.get(filePath)?.deleted
                );
              }
              return false;
            });

            if (dirImages.length === 0) {
              return null;
            }

            return (
              <div key={fileDir} style={{ marginBottom: 24 }}>
                <div
                  style={{
                    fontSize: 14,
                    fontWeight: 500,
                    color: '#1f1f1f',
                    marginBottom: 16,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 8,
                  }}
                >
                  <span>{getLabelRetDirShowName(fileDir)}</span>
                  <span
                    style={{
                      color: '#8c8c8c',
                      fontSize: 12,
                      fontWeight: 'normal',
                    }}
                  >
                    ({dirImages.length}张图片)
                  </span>
                </div>
                <Row gutter={[12, 12]}>
                  {dirImages.map((f: FileVO, index: number) => {
                    const filePath = f.fileDir + '/' + f.fileName;
                    const imgFile = labelImgFiles.get(filePath);

                    return (
                      <ImageItem
                        key={index}
                        file={f}
                        imgFile={imgFile}
                        onToggleSelect={handleToggleSelect}
                        handlePreviewUrl={handlePreviewUrl}
                      />
                    );
                  })}
                </Row>
              </div>
            );
          })}
      </div>
    </Modal>
  );
}
