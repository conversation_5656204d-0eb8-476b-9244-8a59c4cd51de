.lora-dialog-block {
  width: 1300px;
  height: auto;
  border-radius: 8px;
  opacity: 1;

  display: flex;
  gap: 0px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 10px 15px;
  box-shadow: 0 4px 28px 0 rgba(99, 102, 114, 0.3);
}

.lora-system-block {
  border-radius: 8px;
  border: 1px solid red;
}

.lora-detail-content {
  display: flex;
  flex-direction: row;
  padding: 0;
  border-radius: 24px;
}

.lora-detail-left {
  width: 260px;
  height: 706px;
  opacity: 1;
  overflow: auto;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0px;
  gap: 16px;
  align-self: stretch;

  z-index: 0;

  img {
    width: auto;
    height: 160px;
    object-fit: contain;
  }
}

.lora-detail-left-inner {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0px;
  gap: 12px;
  z-index: 0;
}

.lora-left-inner-top {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0px;
  gap: 8px;

  z-index: 0;

  img {
    width: 160px;
    height: 160px;
    object-fit: contain;
    border-radius: 5.16px;
    box-sizing: border-box;
    border: 0.65px solid #e0e0e0;
  }
}

.lora-detail-pair {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0;
  gap: 4px;
  z-index: 1;
  color: #1a1b1d;
}

.lora-detail-pair-row {
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.lora-detail-right {
  width: 1400px;
  height: 686px;
  border-radius: 0px 24px 24px 0px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  overflow-y: auto;
  will-change: transform;
}

.lora-img-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  margin-top: 10px;
}

.lora-detail-img-item {
  display: flex;
  flex-direction: column;

  img {
    width: 160px;
    height: 160px;
    object-fit: contain;
    border: 0.65px solid #e0e0e0;
    cursor: pointer;
  }

  textarea {
    width: 160px;
    height: 160px;
  }
}

.label-ret-item {
  display: flex;
  flex-direction: column;

  img {
    width: 160px;
    height: 160px;
    object-fit: contain;
    border: 0.65px solid #e0e0e0;
    cursor: pointer;
  }

  textarea {
    width: 160px;
    height: 160px;
  }

  &:hover {
    border-color: #40a9ff !important;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

.loras-image-card-name {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  letter-spacing: 0px;
  font-variation-settings: 'opsz' auto;
  color: #1a1b1d;
}

.loras-image-card-info {
  height: 14px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  padding: 0px;

  font-family: PingFang SC;
  font-size: 12px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0em;

  font-variation-settings: 'opsz' auto;
  color: #727375;
}

.operate-btn {
  width: auto !important;
  height: 28px !important;
  font-size: 12px;
}

.lora-running-text {
  font-weight: bold;
  color: blue;
}

.models-form-container {
  padding: 16px;
  gap: 8px;
}
