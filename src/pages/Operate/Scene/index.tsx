import { PageContainer } from '@ant-design/pro-components';
import React, { CSSProperties, useEffect, useState, useCallback } from 'react';
import {
  Button, Checkbox, Dropdown,
  Flex,
  Form,
  Image,
  Input,
  InputNumber,
  message,
  Modal,
  notification, Pagination,
  Popconfirm,
  Radio,
  Select, Space,
  Tooltip,
  Upload,
  UploadFile,
  UploadProps,
} from 'antd';
import {
  deepCopy,
  FileType,
  getBase64, getUserInfo, isProdEnv,
  objectToPathObject,
  pathObjectToObject,
} from '@/utils/utils';
import {
  addElement,
  AgeRanges,
  cloneElement,
  ClothCategoryGroup,
  ClothTypeScopeItem,
  deleteElement,
  ElementConfigWithBlobs,
  getClothCategoryCfg,
  getClothTypeScopeCfg4Scene,
  getElementTypes,
  queryElementById, queryElementByPage,
  SceneType,
  updateElement,
} from '@/services/ElementController';
import {
  AppstoreAddOutlined, CopyOutlined,
  DeleteOutlined, DownOutlined,
  ExperimentOutlined,
  HighlightOutlined,
  LoadingOutlined,
  LogoutOutlined,
  PlusOutlined, ThunderboltOutlined,
} from '@ant-design/icons';
import { UPLOAD_URL } from '@/constants';
import InputWithTags from '@/components/InputWithTags';
import { TagType } from '@/services/TagsController';
import './index.less';
import ElementEditModal from '@/components/Operate/ElementEditModal';
import ChildrenTabCard, { processChildren } from '@/components/Operate/ChildrenTabCard';
import ManualVerificationModal from '@/components/Operate/ManualVerificationModal';
import { sortElements } from '@/pages/Operate/Face';
import ElementImageRecord from '@/components/Operate/ElementImageRecord';
import NewLabel from '@/components/new/NewLabel';
import LoraTrainDetail from '@/components/Lora/LoraTrainDetail';
import { MiniTag, RefreshButton } from '@/components/Common/CommonComponent';
import { addLora2Vip, relabelModel, retrainModel } from '@/services/MaterialModelController';
import ClothTypeScopeSelector from '@/components/Operate/ClothTypeScopeSelector';
import ElementImagesSelector from '@/components/Operate/ElementImagesSelector';
import AssignModel2User from '@/components/AssignModel2User';
import { queryAllMaster } from '@/services/UserController';
import { queryLoraVipCfg } from '@/services/SystemController';
import CreatePoseModal from './CreatePoseModal';
import { poseSampleDiagram } from '@/services/CreativeController';
import { useExperimental } from '@/components/Operate/Experimental';
import { AGE_RANGE_LIST, SELECT_AGE_RANGE_OPTIONS } from '@/components/AgeRangeSelector';
import ClothCategorySelector from '@/components/Operate/ClothCategory';
import debounce from 'lodash/debounce';

interface ElementConfigWithClothStyle extends ElementConfigWithBlobs {
  clothTypes: Array<{ key: string, label: string }>;
  newClothTypes: Array<{ key: string, label: string }>;
}

const clothStyle: CSSProperties = { width: 90, padding: 0, textAlign: 'center' };
const clothStyleV2: CSSProperties = { ...clothStyle, color: 'red' };

const AllSceneClothTypesKL: Array<{ key: string; label: string }> = [
  { key: 'v_1,Female,front view', label: 'sdxl女装正面' },
  { key: 'v_1,Female,back view', label: 'sdxl女装背面' },
  { key: 'v_1,Male,front view', label: 'sdxl男装正面' },
  { key: 'v_1,Male,back view', label: 'sdxl男装背面' },
  { key: 'v_1,Child,front view', label: 'sdxl童装正面' },
  { key: 'v_1,Child,back view', label: 'sdxl童装背面' },
  { key: 'v_2,Female,front view,whole body', label: '女装正面全身' },
  { key: 'v_2,Female,back view,whole body', label: '女装背面全身' },
  { key: 'v_2,Female,front view,lower body', label: '女装正面下装' },
  { key: 'v_2,Female,front view,upper body', label: '女装正面上装' },
  { key: 'v_2,Female,back view,lower body', label: '女装背面下装' },
  { key: 'v_2,Female,back view,upper body', label: '女装背面上装' },
  { key: 'v_2,Female,front view,double people', label: '女装正面双人' },
  { key: 'v_2,Female,back view,double people', label: '女装背面双人' },
  { key: 'v_2,Male,front view,whole body', label: '男装正面全身' },
  { key: 'v_2,Male,back view,whole body', label: '男装背面全身' },
  { key: 'v_2,Male,front view,lower body', label: '男装正面下装' },
  { key: 'v_2,Male,front view,upper body', label: '男装正面上装' },
  { key: 'v_2,Male,back view,lower body', label: '男装背面下装' },
  { key: 'v_2,Male,back view,upper body', label: '男装背面上装' },
  { key: 'v_2,Male,front view,double people', label: '男装正面双人' },
  { key: 'v_2,Male,back view,double people', label: '男装背面双人' },
  { key: 'v_2,Child,front view,whole body', label: '童装正面全身' },
  { key: 'v_2,Child,back view,whole body', label: '童装背面' },
];

const AllSceneClothTypes: Array<{ key: string; value: string; label: string, style: CSSProperties }> =
  AllSceneClothTypesKL.map((item) => {
    return { ...item, value: item.key, style: item.key.includes('v_2') ? clothStyleV2 : clothStyle };
  });

const Face: React.FC<unknown> = () => {
  const userInfo = getUserInfo();
  // @ts-ignore
  const [datas, setDatas] = useState<Array<ElementConfigWithClothStyle>>([]);
  const [showDialog, setShowDialog] = useState(false);
  const [add, setAdd] = useState(false);
  const [deleteId, setDeleteId] = useState<number>(0);
  const [form] = Form.useForm();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(70);

  //图片上传相关
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');

  //封面图
  const [coverShowImgFileList, setCoverShowImgFileList] = useState<UploadFile[]>([]);

  //场景列表，scene type code array
  const [checkedSceneTypes, setCheckedSceneTypes] = useState<string[]>();
  const [checkedAgeRange, setCheckedAgeRange] = useState<string | null>(null);
  const [sceneTypeList, setSceneTypeList] = useState<SceneType[]>([]);
  const [selectType, setSelectType] = useState<string>('all');
  const [showEditType, setShowEditType] = useState<boolean>(false);
  const [showOpenScope, setShowOpenScope] = useState('all');
  const [showTesting, setShowTesting] = useState('all');
  const [swapType, setSwapType] = useState('all');
  const [trainType, setTrainType] = useState('all');

  const [genderType, setGenderType] = useState('all');
  const [searchAgeRange, setSearchAgeRange] = useState<string>('ALL');

  const [privatelyOpen2UserRoleType, setPrivatelyOpen2UserRoleType] = useState('all');

  const [isLoraScene, setIsLoraScene] = useState(false);

  const [opItem, setOpItem] = useState<ElementConfigWithBlobs | undefined>();
  const [searchKey, setSearchKey] = useState<string | undefined>();
  const [searchId, setSearchId] = useState<number | undefined | null>();

  const [trainDetailId, setTrainDetailId] = useState<number>();
  const [showTrainDetail, setShowTrainDetail] = useState(false);
  const [cloningId, setCloningId] = useState<number | null>(null);
  const [reTrainId, setRetrainId] = useState<number | null>(null);

  const [clothTypeScopeCfg, setClothTypeScopeCfg] = useState([]);
  const [clothCategoryCfg, setClothCategoryCfg] = useState<ClothCategoryGroup[]>([]);

  //需要转交客户的模型
  const [assignElementConfig, setAssignElementConfig] = useState<ElementConfigWithBlobs | null>();

  //是否只看客户上传模型
  const [showCustomerOnly, setShowCustomerOnly] = useState(false);

  const [masterOptions, setMasterOptions] = useState<Array<any>>([]);
  const [searchUserId, setSearchUserId] = useState<number | null>(null);
  const [searchPrivateOpen2UserId, setSearchPrivateOpen2UserId] = useState<number | null>(null);

  //lora_vip配置，训练加急白名单
  const [loraVipCfg, setLoraVipCfg] = useState<number[]>([]);

  const [showCreatePoseModal, setShowCreatePoseModal] = useState(false);
  const [currentSceneId, setCurrentSceneId] = useState<number | null>(null);

  // 人工核验弹窗状态
  const [showManualVerificationModal, setShowManualVerificationModal] = useState(false);

  const [modelOptions, setModelOptions] = useState<Array<{ label: string; value: number; showImage?: string }>>([]);

  const [onlyNearingDelivery, setOnlyNearingDelivery] = useState(false);

  const [onlyExperimental, setOnlyExperimental] = useState(false);
  const [onlyNoshowFace, setOnlyNoshowFace] = useState(false);

  // 使用封装的hooks处理实验状态
  const { getExperimentalLabel, setExperimental, ExperimentalLabel } = useExperimental();

  // 创建防抖函数
  const debouncedSearchId = useCallback(
    debounce((value: number | null) => {
      setSearchId(value);
    }, 100),
    []
  );

  const debouncedSearchKey = useCallback(
    debounce((value: string) => {
      setSearchKey(value);
    }, 300),
    []
  );

  async function fetchData() {
    const query = {
      configKey: 'SCENE', openScope: showOpenScope === 'all' ? null : showOpenScope,
      status: showTesting === 'all' ? null : showTesting, isLora: swapType === 'all' ? null : swapType === 'style',
      type: selectType === 'all' ? null : selectType, userId: searchUserId || null,
      id: searchId, name: searchKey?.trim(), trainType: trainType === 'all' ? null : trainType, pageNum: page, pageSize,
      belong: showCustomerOnly ? 'CUSTOM' : null,
      onlyNearingDelivery,
      genderType: genderType === 'all' ? null : genderType,
      privatelyOpen2UserRoleType: privatelyOpen2UserRoleType === 'all' ? null : privatelyOpen2UserRoleType,
      privatelyOpen2UserId: searchPrivateOpen2UserId || null,
      onlyExperimental,
      onlyNoshowFace,
      filterAgeRange: searchAgeRange === 'ALL' ? null : searchAgeRange,
    };

    queryElementByPage(query).then(res => {
      if (!res) return;
      setTotal(res.totalCount || 0);

      res.list?.forEach(item => {
        item.type = [...new Set(item.type)];
      });

      setDatas((res.list || []) as ElementConfigWithClothStyle[]);
    });
  }

  const handlePageChange = (page: number, pageSize?: number) => {
    setPage(page);
    if (pageSize) {
      setPageSize(pageSize);
    }
  };

  const refreshVipCfg = () => {
    queryLoraVipCfg().then(res => {
      if (res && Array.isArray(res)) {
        setLoraVipCfg(res);
      }
    });
  };

  useEffect(() => {
    fetchData();

    //场景分类列表-下拉菜单
    getElementTypes('SCENE').then(types => {
      if (types) {
        setSceneTypeList(types);
      }
    });
    //场景适合的服装类型-下拉菜单
    getClothTypeScopeCfg4Scene().then(cfgs => {
      if (cfgs) {
        //@ts-ignore
        setClothTypeScopeCfg(cfgs);
      }
    });

    // 获取模特列表
    queryElementByPage({
      configKey: 'FACE',
      pageNum: 1,
      pageSize: 9999,
      status: 'PROD',
      onlyExperimental: false,
      onlyNearingDelivery: false,
    }).then(res => {
      if (res?.list) {
        const options = res.list.map((item: any) => ({
          label: item.name,
          value: item.id,
          showImage: item.showImage,
        }));

        setModelOptions(options);
      }
    });

    queryAllMaster(['MERCHANT', 'OPERATOR', 'ADMIN', 'DISTRIBUTOR']).then(res => {
      if (res && Array.isArray(res)) {
        const masterOptions = [];
        res.forEach(item => {
          // @ts-ignore
          masterOptions.push({
            label: item.nickName + (item.corpName ? '@' + item.corpName : ''),
            value: item.id + '',
          });
        });

        setMasterOptions(masterOptions);
      }
    });

    // 只在生产环境下启用
    if (isProdEnv() && ![100155, 100158].includes(userInfo?.id || 0)) {
      queryElementByPage({
        pageNum: 1,
        pageSize: 10,
        onlyNearingDelivery: true,
        onlyExperimental: false,
        configKey: 'SCENE',

      }).then(res => {
        if (res && res?.totalCount && res?.totalCount > 0) {
          Modal.confirm({
            title: '交付超时提醒',
            content: `有${res?.totalCount}个客户场景交付即将/已经超时，是否查看？`,
            okText: '查看超时场景',
            cancelText: '先忽略',
            centered: true,
            zIndex: 1030,
            onOk: () => {
              setOnlyNearingDelivery(true);
            },
          });
        }
      });
    }

  }, []);

  useEffect(() => {
    fetchData();
  }, [page, pageSize]);

  useEffect(() => {
    setPage(1);
    fetchData();
  }, [showOpenScope, searchKey, swapType, showTesting, selectType, searchId, trainType, showCustomerOnly, searchUserId, onlyNearingDelivery, genderType, privatelyOpen2UserRoleType, searchPrivateOpen2UserId, onlyExperimental, searchAgeRange, onlyNoshowFace]);

  const convertWithClothStyle = (item: ElementConfigWithBlobs): ElementConfigWithClothStyle => {
    item.type = [...new Set(item.type)];

    if (item.type.includes('v_2') && !(item.type.includes('lower body') || item.type.includes('upper body') || item.type.includes('whole body') || item.type.includes('double people'))) {
      item.type.push('whole body');
    }

    if (item.children) {
      item.children.forEach(child => {
        if (child.type.includes('v_2') && !(child.type.includes('lower body') || child.type.includes('upper body') || item.type.includes('whole body') || child.type.includes('double people'))) {
          child.type.push('whole body');
        }
      });

      //对children进行排序，只要type中带"v_2"的都排在前面
      item.children = item.children.sort(sortElements());
    }

    return item as ElementConfigWithClothStyle;
  };

  //刷新表单数据
  function refreshFormData(item: ElementConfigWithClothStyle) {
    const copy = deepCopy(item);
    const newChildren = [];
    copy.children.forEach(child => {
      const extInfoExtend = objectToPathObject(child.extInfo, 'extInfo');
      // @ts-ignore
      newChildren.push({ ...child, ...extInfoExtend });
    });
    copy.children = newChildren;

    //展开扩展信息
    const extInfoExtend = objectToPathObject(copy.extInfo, 'extInfo');
    form.setFieldsValue({ ...copy, ...extInfoExtend });
    setOpItem(item);
  }

  const handleAdd = () => {
    //先清空一遍表单数据
    form.resetFields();
    setCoverShowImgFileList([]);
    setAdd(true);
    setShowDialog(true);
    setOpItem(undefined);
    setIsLoraScene(false);
    setCheckedAgeRange(null);
  };

  const handleModify = (item: ElementConfigWithClothStyle) => {
    //先清空一遍表单数据
    form.resetFields();
    setAdd(false);

    // 设置当前场景ID
    setCurrentSceneId(item.id);

    queryElementById(item.id).then(res => {
      if (!res) return;
      const isShowScene = item.type?.includes('show');
      getClothCategoryCfg().then(cfg => {
        if (cfg) {
          // 秀场T台场景不需要
          setClothCategoryCfg(isShowScene ? [] : cfg);
        }
      });
      const target = convertWithClothStyle(res);
      refreshFormData(target);
      form.setFieldValue('type', target.type.filter(e => !AllSceneClothTypes.some(item => item.key === e)));

      // 从 target.type 中匹配 SELECT_AGE_RANGE_OPTIONS 的数据
      const ageRangeValues = target.type.filter(type =>
        SELECT_AGE_RANGE_OPTIONS.some(group =>
          group.options.some(option => option.value === type),
        ),
      );
      setCheckedAgeRange(ageRangeValues[0] || null);
      form.setFieldValue('ageRange', ageRangeValues[0] || null);


      setCoverShowImgFileList([{
        uid: '-1',
        name: 'image.png',
        status: 'done',
        url: target.showImage,
      }]);

      const showImgs = JSON.parse(res.extInfo['showImgs'] || '[]') as string[];
      form.setFieldValue(['extInfo', 'showImgs'], showImgs);

      const clothTypeScope = JSON.parse(res.extInfo['clothTypeScope'] || '[]') as ClothTypeScopeItem[];
      form.setFieldValue(['extInfo', 'clothTypeScope'], clothTypeScope);

      form.setFieldValue(['extInfo', 'clothCategory'], isShowScene ? [] : (res.extInfo['clothCategory'] || []));

      setIsLoraScene(target.extInfo['isLora'] === 'Y');
      setShowDialog(true);
    });
  };

  const handleDelete = (item: ElementConfigWithBlobs) => {
    setDeleteId(item.id);
  };

  const commitDel = () => {
    deleteElement(deleteId).then((res) => {
      if (res) {
        setDeleteId(0);
        fetchData();
      }
    });
  };

  const handleClone = (item: ElementConfigWithBlobs, fullCopy = false) => {
    setCloningId(item.id);
    cloneElement(item.id, fullCopy).then(res => {
      setCloningId(null);
      if (res) {
        message.success('克隆成功');
        fetchData();
      }
    });
  };

  const handleRetain = (item: ElementConfigWithBlobs) => {
    const modelId = item.loraModelId;
    if (!modelId) return;

    setRetrainId(modelId);
    retrainModel(modelId).then(res => {
      setRetrainId(null);
      if (res) {
        message.success('重新训练成功');
        fetchData();
      }
    });
  };

  const handleRelabel = (item: ElementConfigWithBlobs) => {
    const modelId = item.loraModelId;
    if (!modelId) return;

    setRetrainId(modelId);
    relabelModel(modelId).then(res => {
      setRetrainId(null);
      if (res) {
        message.success('重新开始抠图打标成功');
        fetchData();
      }
    });
  };

  const handleCensoredFaceRelabel = (item: ElementConfigWithBlobs) => {
    const modelId = item.loraModelId;
    if (!modelId) return;

    setRetrainId(modelId);
    relabelModel(modelId, null, null, 'Y').then(res => {
      setRetrainId(null);
      if (res) {
        message.success('重新开始白头处理抠图打标成功');
        fetchData();
      }
    });
  };

  const handleCommit = (values) => {
    const valuesObj = pathObjectToObject(values);

    valuesObj.configKey = 'SCENE';
    valuesObj.level = 2;
    valuesObj.opChildren = true;

    //封面展示图
    if (valuesObj.showImage) {
      if (typeof valuesObj.showImage !== 'string') {
        if (!valuesObj.showImage.file.response) {
          notification.error({ message: '请上传图片' });
          return;
        }
        valuesObj.showImage = valuesObj.showImage.file.response.data;
      }
    }

    // 轮播展示图
    if (valuesObj.extInfo.showImgs && Array.isArray(valuesObj.extInfo.showImgs)) {
      valuesObj.extInfo['showImgs'] = JSON.stringify(valuesObj.extInfo.showImgs);
    }

    // 对type进行处理
    // 若type中包含AGE_RANGE_LIST
    if (valuesObj.type.some(type => AGE_RANGE_LIST.includes(type))) {
      // 若包含则进行从type中删除
      valuesObj.type = valuesObj.type.filter(type => !AGE_RANGE_LIST.includes(type));
    }

    // 将ageRange添加到type中 为空则不添加
    const ageRange = valuesObj.ageRange;
    if (ageRange) {
      valuesObj.type.push(ageRange);
    }

    // 删除表单中的ageRange字段，它只是用于UI展示
    delete valuesObj.ageRange;

    //处理场景适合的服装类型范围，转换类型
    if (valuesObj.extInfo.clothTypeScope) {
      valuesObj.extInfo['clothTypeScope'] = JSON.stringify(valuesObj.extInfo.clothTypeScope);
    }

    processChildren(valuesObj, AllSceneClothTypes, '服装款式（男/女款）');

    const method = add ? addElement : updateElement;
    method(valuesObj).then((res) => {
      if (res) {
        notification.success({ message: add ? '添加成功' : '修改成功' });
        setShowDialog(false);
        fetchData();
      }
    });
  };

  function getTrainingStatusTitle(item: ElementConfigWithBlobs) {
    let labelStatus = item?.labelFinished ? '打标已完成' : '抠图打标中';
    let loraStatus = item?.loraTaskStatusDesc || '未开始';

    return item && item?.loraConfirmed ? (item?.loraTaskStatus === 'RUNNING' ? '模型训练中' : '模型' + loraStatus) : (labelStatus + '-未确认');
  }

  const ImageCard = (item: ElementConfigWithClothStyle) => (
    <div className="models-image-card" style={{
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
      borderRadius: '12px',
      overflow: 'hidden',
      transition: 'all 0.3s ease',
      position: 'relative',
      width: '248px',
      background: '#FFFFFF',
      padding: 0,
    }}
      onMouseOver={(e) => {
        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.12)';
        e.currentTarget.style.transform = 'translateY(-2px)';
      }}
      onMouseOut={(e) => {
        e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.08)';
        e.currentTarget.style.transform = 'translateY(0)';
      }}
    >
      <ExperimentalLabel {...item} />
      {/* 上新标签 */}
      {item.isNew &&
        <NewLabel />
      }

      <div className="models-img-cover"
        style={{
          cursor: item?.loraModelId ? 'pointer' : 'default',
          width: '100%',
          height: '248px',
          background: '#F5F6F9',
          borderRadius: '8px 8px 0 0',
          border: 'none',
        }}
        onClick={() => {
          if (!item.loraModelId) {
            return;
          }
          setShowTrainDetail(true);
          setTrainDetailId(item.loraModelId);
        }}
      >
        <img src={item.showImage} alt={item.name} loading={'lazy'} style={{ objectFit: 'contain' }} />
        {item.status === 'TEST' &&
          <Flex align={'center'} justify={'center'} vertical={true}
            style={{ width: '100%', height: '100%', position: 'absolute', background: 'rgba(41, 41, 41, 0.3)' }}>
            {item?.loraTraining === true &&
              <>
                <l-helix size="45" speed="2.5" color={item?.loraTaskStatus === 'RUNNING' ? 'blue' : '#D88FFF'} />
                <div className={'color-w text16'} style={{ marginTop: 8 }}>{getTrainingStatusTitle(item)}</div>
              </>
            }

            {!item?.loraTraining &&
              <div className={'color-w text16'}>测试中</div>
            }

          </Flex>
        }

        {item.loraModelId &&
          <MiniTag title={item.contentOrStyle} defaultValue={'style'} />
        }

        {item.maskLoss &&
          <MiniTag title={item.maskLoss ? 'mask+loss' : ''} position={'rightBottom'} />
        }
      </div>

      <div style={{ padding: '16px', display: 'flex', flexDirection: 'column', gap: '8px' }}>
        {/*模型名称*/}
        <div className="models-image-card-name" style={{ fontSize: 14, fontWeight: '600' }}>
          <Tooltip title={item.name}>
            <span
              style={{
                maxWidth: '180px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: 'inline-block',
                verticalAlign: 'middle',
                marginRight: '4px',
                cursor: 'pointer',
                color: loraVipCfg?.includes(item?.loraModelId || 0) && item?.loraTraining ? 'red' : '',
              }}
              onClick={() => {
                navigator.clipboard.writeText(item.name);
                message.success('已复制场景名称');
              }}
            >
              {item.name.length > 10 ? item.name.slice(0, 10) + '...' : item.name}
            </span>
          </Tooltip>
          <Tooltip title={`复制ID: ${item?.id}`}>
            <span
              style={{ color: '#727375', fontSize: 12, cursor: 'pointer' }}
              onClick={() => {
                navigator.clipboard.writeText(item?.id?.toString());
                message.success('已复制ID');
              }}
            >
              {`(${item?.id})`}
            </span>
          </Tooltip>
        </div>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
          <div className="models-image-card-name" style={{ fontSize: 12, fontWeight: 'normal' }}>
            创建时间：{item.createTime}
          </div>
          <div className="models-image-card-name" style={{ fontSize: 12, fontWeight: 'normal' }}>
            创建人：{item.userNick}
          </div>
          <div className="models-image-card-name" style={{ fontSize: 12, fontWeight: 'normal' }}>
            专属：{item.privatelyOpen2UserNick || '公开'}
          </div>
          <div className="models-image-card-name" style={{ fontSize: 12, fontWeight: 'normal' }}>
            最后修改：{item.operatorNick}
          </div>
          <div className="models-image-card-name" style={{ fontSize: 12, fontWeight: 'normal' }}>
            交付人：
            {item.extInfo && item.extInfo['deliveryTime'] ?
              <Tooltip title={`交付时间 ${item.extInfo['deliveryTime']}`}>{item.deliveryName}</Tooltip>
              : item.deliveryName}

          </div>

        </div>
        <div className={'row-space-block'} style={{ paddingTop: '8px', borderTop: '1px solid #f0f0f0' }}>
          <Button className={'operate-btn'} size={'small'} onClick={() => handleModify(item)}>修改</Button>

          <Popconfirm title={'将克隆一个名为"' + item.name + '_copy"且状态为内部测试的新模特'}
            disabled={cloningId !== null && cloningId !== item.id}
            onConfirm={() => handleClone(item)}>
            <Button className={'operate-btn'}
              size={'small'}
              disabled={cloningId !== null && cloningId !== item.id}
              style={{ minWidth: 50 }}>{cloningId === item.id ? <LoadingOutlined /> : '克隆'}
            </Button>
          </Popconfirm>

          {item.loraModelId &&
            <>
              <Tooltip title={'重新以最新参数进行抠图打标，不经过人工确认直接开始训练'}>
                <Popconfirm title={'重新以最新参数进行抠图打标，不经过人工确认直接开始训练，线上将不可用'}
                  onConfirm={() => handleRetain(item)}>
                  <Button className={'operate-btn'} size={'small'} style={{ minWidth: 50 }}>{reTrainId === item.id ?
                    <LoadingOutlined /> : '重训'}</Button>
                </Popconfirm>
              </Tooltip>
            </>
          }

          <ElementImageRecord elementId={item.id} isIcon={true} />

          <Dropdown
            menu={{
              items: [
                {
                  key: 'loraVip',
                  label: '加急',
                  icon: <ThunderboltOutlined />,
                  disabled: !item?.loraModelId || loraVipCfg?.includes(item?.id) || !item?.loraTraining,
                },
                {
                  key: 'retrainNeedConfirm',
                  label: (
                    <Tooltip title={'重新以最新参数进行抠图打标，训练前需要人工确认，线上将不可用！'}>
                      <span>重新抠图打标</span>
                    </Tooltip>
                  ),
                  danger: true,
                  icon: <HighlightOutlined />,
                },
                {
                  key: 'retrainCensoredFaceNeedConfirm',
                  label: (
                    <Tooltip title={'重新设置为白头化抠图打标，训练前需要人工确认，线上将不可用！'}>
                      <span>重新白头打标</span>
                    </Tooltip>
                  ),
                  danger: true,
                  icon: <HighlightOutlined />,
                },
                {
                  key: 'fullClone',
                  label: <Tooltip title={'完整克隆当前场景'}>完全克隆</Tooltip>,
                  icon: <CopyOutlined />,
                },
                {
                  key: 'delLora',
                  label: '删除场景',
                  danger: true,
                  icon: <DeleteOutlined />,
                }, {
                  key: 'transfer2Merchant',
                  label: '转交客户',
                  icon: <LogoutOutlined />,
                  disabled: !item?.loraModelId,
                }, {
                  key: 'handleExperimental',
                  label: getExperimentalLabel(item),
                  icon: <ExperimentOutlined />,
                  disabled: false,
                },
              ],
              onClick: ({ key }) => {
                if (key === 'loraVip') {
                  addLora2Vip(item?.loraModelId || 0).then(res => {
                    if (res) {
                      message.success('加急成功');
                      refreshVipCfg();
                    }
                  });
                } else if (key === 'delLora') {
                  handleDelete(item);
                } else if (key === 'transfer2Merchant') {
                  setAssignElementConfig(item);
                } else if (key === 'fullClone') {
                  handleClone(item, true);
                } else if (key === 'retrainNeedConfirm') {
                  handleRelabel(item);
                } else if (key === 'retrainCensoredFaceNeedConfirm') {
                  handleCensoredFaceRelabel(item);
                } else if (key === 'handleExperimental') {
                  setExperimental({ ...item, type: 'Element' }, fetchData);
                }
              },
            }}
          >
            <a onClick={(e) => e.preventDefault()}>
              <Space>
                更多
                <DownOutlined />
              </Space>
            </a>
          </Dropdown>


        </div>
      </div>
    </div>
  );


  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
  };

  const handleChangeCoverShowImg: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    const file = newFileList[0];

    if (file && file.response && !file.response.success) {
      notification.error({ message: '上传图片异常，请重试' });
      setCoverShowImgFileList([]);
      return;
    }
    setCoverShowImgFileList(newFileList);
  };


  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  const getSearchSceneList = (sceneTypeList: any[]) => {
    const res = [];
    sceneTypeList.forEach(item => {
      // @ts-ignore
      res.push({ label: item.name, value: item.code });
    });

    return [{ label: '全部', value: 'all' }, ...res];
  };

  function isMerchantPrivateScene() {
    return form.getFieldValue('userRoleType') === 'MERCHANT' && form.getFieldValue('exclusive') === true && form.getFieldValue('styleScene');
  }

  function getSceneShowImgBlockHelpMsg() {
    return isMerchantPrivateScene() ? '' : '提醒：建议设置长宽比例一致的长方形图片';
  }

  return (
    <PageContainer>
      <Flex vertical style={{ padding: 16 }} gap={4}>
        <Flex gap={8} align={'center'} justify={'flex-start'}>
          {/* 刷新按钮 */}
          <RefreshButton refresh={fetchData} />
          {/* 展示范围 */}
          <Flex align={'center'}>
            <Radio.Group optionType="button" defaultValue={'all'} size={'small'}
              options={[{ label: '全部', value: 'all' },
              { label: '公共', value: 'public' },
              { label: '专属', value: 'private' },
              ]} onChange={e => setShowOpenScope(e.target.value)} />
          </Flex>

          {/* 状态 */}
          <Radio.Group optionType="button" size={'small'}
            options={[{ label: '全部', value: 'all' }, { label: '测试中', value: 'TEST' },
            { label: '正式', value: 'PROD' }]} defaultValue={'all'}
            onChange={e => setShowTesting(e.target.value)} />

          {/* 类型 */}
          <Radio.Group optionType="button" size={'small'}
            options={[{ label: '全部', value: 'all' }, { label: '普通场景', value: 'normal' },
            { label: '风格场景', value: 'style' }]} defaultValue={'all'}
            onChange={e => setSwapType(e.target.value)} />

          {/* 年龄区段 */}
          <Radio.Group options={AgeRanges}
            defaultValue={'ALL'} optionType="button" onChange={e => setSearchAgeRange(e.target.value)} />

          {/* 性别 */}
          <Radio.Group optionType="button" size={'small'}
            options={[{ label: '全部', value: 'all' }, { label: '男', value: 'Male' },
            { label: '女', value: 'Female' }]} defaultValue={'all'}
            onChange={e => setGenderType(e.target.value)} />

          {/* 专属 */}
          <Radio.Group optionType="button" size={'small'}
            options={[{ label: '全部', value: 'all' },
            { label: '商家专属', value: 'MERCHANT' },
            { label: '运营专属', value: 'OPERATOR' },
            { label: '管理员专属', value: 'ADMIN' }]}
            defaultValue={'all'}
            onChange={e => setPrivatelyOpen2UserRoleType(e.target.value)} />

          {/* 训练类型 */}
          <Radio.Group optionType="button" size={'small'}
            options={[{ label: '全部', value: 'all' }, { label: 'content', value: 'content' },
            { label: 'style', value: 'style' }]} defaultValue={'all'}
            onChange={e => setTrainType(e.target.value)} />

          <Button className="models-image-card-button" type={'primary'} icon={<PlusOutlined />}
            size={'small'} onClick={handleAdd}>新增场景</Button>

        </Flex>

        <Flex>
          <div className="models-filter-row" style={{ gap: 8, marginTop: 0 }}>
            <Radio.Group options={getSearchSceneList(sceneTypeList)} size={'small'}
              defaultValue={'all'} optionType="button" onChange={e => setSelectType(e.target.value)} />
            <Button className="models-image-card-button" size={'small'}
              onClick={() => setShowEditType(true)}>编辑标签</Button>
          </div>
        </Flex>

        <Flex gap={8} align={'center'}>
          <InputNumber placeholder={'ID检索'} onChange={(e) => debouncedSearchId(e ? Number(e) : null)}
            style={{ width: 80 }} />

          <Select options={masterOptions} style={{ width: 160 }} showSearch allowClear
            placeholder={'选择创建者'}
            optionFilterProp="label" defaultActiveFirstOption={true}
            value={searchUserId}
            onChange={(e) => setSearchUserId(e)} />

          <Select options={masterOptions} style={{ width: 160 }} showSearch allowClear
            placeholder={'选择专属者'}
            optionFilterProp="label" defaultActiveFirstOption={true}
            value={searchPrivateOpen2UserId}
            onChange={(e) => setSearchPrivateOpen2UserId(e)} />

          <Input allowClear placeholder={'场景名称模糊'} onChange={(e) => debouncedSearchKey(e.target.value)} style={{ width: 150 }} />

          {/* 只看客户上传 */}
          <Checkbox style={{ fontSize: 12 }} onChange={e => {
            setShowCustomerOnly(e.target.checked);
          }}>只看客户上传</Checkbox>

          {/* 20小时未交付 */}
          <Checkbox style={{ fontSize: 12 }} checked={onlyNearingDelivery} onChange={e => {
            setOnlyNearingDelivery(e.target.checked);
          }}>20小时未交付</Checkbox>

          {/* 只看实验 */}
          <Checkbox style={{ fontSize: 12 }} checked={onlyExperimental} onChange={e => {
            setOnlyExperimental(e.target.checked);
          }}>只看实验</Checkbox>

          <Checkbox style={{ fontSize: 12 }} checked={onlyNoshowFace} onChange={e => {
            setOnlyNoshowFace(e.target.checked);
          }}>只看不出头</Checkbox>
        </Flex>

        <Flex gap={8} style={{ flexWrap: 'wrap', marginTop: 8 }}>
          {datas.map(item => (
            <ImageCard {...item} key={item.id} />
          ))}
        </Flex>
        <div className={'stick-bottom-pagination'}>
          <Pagination
            current={page}
            pageSize={pageSize}
            total={total}
            onChange={handlePageChange}
            showTotal={(total) => `共 ${total} 个场景`}
            showSizeChanger // 允许用户更改每页显示条数
            pageSizeOptions={[70, 140, 210]}
            showQuickJumper // 允许用户快速跳转到某一页
            style={{ marginTop: '16px', textAlign: 'center' }}
          />
        </div>
      </Flex>

      {showDialog &&
        <Modal title={''} open={showDialog} centered mask={true} keyboard={true}
          styles={{
            footer: { display: 'flex', flexDirection: 'row', justifyContent: 'center' },
          }} maskClosable={false}
          onCancel={() => {
            setShowDialog(false);
          }} onOk={form.submit}
          closable={false} width={'auto'} zIndex={1000}>

          <Form className={'scene-dialog-block'}
            labelCol={{ span: 2 }}
            wrapperCol={{ span: 22 }}
            initialValues={{ remember: true }}
            onFinish={handleCommit}
            autoComplete="off"
            form={form}
            onValuesChange={(changedValues) => {
              if (changedValues['extInfo.isLora'] !== undefined) {
                setIsLoraScene(changedValues['extInfo.isLora'] === 'Y');
              }
            }}
          >
            <Form.Item hidden label="id" name="id">
              <Input />
            </Form.Item>
            <Form.Item label="场景名称" name="name" style={{ width: '100%' }}
              rules={[{ required: true, message: '请输入正确的场景名称' },
              {
                pattern: /^[\u4e00-\u9fa5a-zA-Z0-9-_]+$/,
                message: '名称只能包含中文、英文、数字、下划线或中划线!',
              },
              ]}>
              <Input placeholder={'请输入场景名称'} style={{ width: 600 }} />
            </Form.Item>

            <Form.Item label="场景标签"
              name="type"
              style={{ width: '100%' }}
              rules={[{ required: true, message: '请选择场景标签' }]}>

              {sceneTypeList && sceneTypeList.length > 0 && (
                <Select
                  key={'checkedSceneTypes'}
                  mode="tags"
                  placeholder="请选择（可多选）"
                  value={checkedSceneTypes}
                  style={{ width: 600 }}
                  allowClear={true}
                  onChange={value => setCheckedSceneTypes(value)}
                >
                  {sceneTypeList.map(s => (
                    <Select.Option key={s.code} value={s.code}>
                      {s.name}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>

            <Form.Item
              label="年龄区段"
              name="ageRange"
              rules={[{ required: true, message: '请选择年龄区段' }]}
              style={{ width: '100%' }}
            >
              <Select
                key={'checkedAgeRange'}
                placeholder="请选择年龄区段"
                style={{ width: 600 }}
                allowClear={true}
                onChange={value => {
                  setCheckedAgeRange(value);
                }}
              >
                {SELECT_AGE_RANGE_OPTIONS.map(group => (
                  <Select.OptGroup key={group.title} label={group.title}>
                    {group.options.map(option => (
                      <Select.Option key={option.value} value={option.value}>
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select.OptGroup>
                ))}
              </Select>
            </Form.Item>

            {/* 新增服装类型范围选择 */}
            <Form.Item
              label="服装类型范围"
              name={['extInfo', 'clothTypeScope']}
              style={{ width: '100%' }}
            >
              <ClothTypeScopeSelector scopeConfig={clothTypeScopeCfg} />
            </Form.Item>
            <Form.Item
              label="服装款式范围"
              hidden={clothCategoryCfg.length === 0}
              name={['extInfo', 'clothCategory']}
              style={{ width: '100%' }}
            >
              <ClothCategorySelector width={600} clothCategoryCfg={clothCategoryCfg} />
            </Form.Item>

            <Form.Item hidden label="展示图片地址" name="extInfo.faceImage">
              <Input />
            </Form.Item>

            <Form.Item
              label="展示图片"
              name="showImage"
              rules={[{ required: true, message: '请上传场景图片' }]}
              style={{ width: '100%' }}
            >
              <Upload
                action={UPLOAD_URL}
                listType="picture-card"
                fileList={coverShowImgFileList}
                onPreview={handlePreview}
                onChange={handleChangeCoverShowImg}
              >
                {coverShowImgFileList.length >= 1 ? null : uploadButton}
              </Upload>
            </Form.Item>

            <Form.Item
              label="轮播图"
              name="extInfo.showImgs"
              style={{ width: '100%' }}
              help={<span style={{ color: 'red', fontSize: 10 }}>{getSceneShowImgBlockHelpMsg()}</span>}
            >
              {isMerchantPrivateScene()
                ? <span style={{ color: 'red' }}>当前为商家专属风格，系统自动取前10张样本图作为轮播图</span>
                : <ElementImagesSelector
                  elementId={form.getFieldValue('id')}
                  maxChoose={10}
                  isLoraScene={isLoraScene}
                  styleImageList={[]} />
              }
            </Form.Item>

            <Flex justify={'flex-start'} style={{ width: '100%', marginLeft: 80 }}>
              <Form.Item
                label="lora场景"
                name="extInfo.isLora"
                rules={[{ required: true, message: '请选择正确的场景状态' }]}
                style={{ width: '20%' }}
                labelCol={{ span: 7 }}
                initialValue={'N'}
              >
                <Radio.Group options={[{ label: '否', value: 'N' },
                { label: '是', value: 'Y' },
                ]} optionType="button" buttonStyle="solid" />
              </Form.Item>
              <Form.Item
                label="lora地址"
                name="extInfo.loraPath"
                rules={[{ required: isLoraScene, message: '请输入lora地址' }]}
                style={{ width: '80%' }}
                labelCol={{ span: 2 }}
                hidden={!isLoraScene}
              >
                <Input placeholder="请输入lora地址" style={{ width: 800 }} />
              </Form.Item>
            </Flex>

            <Flex justify={'flex-start'}
              style={{ width: '100%', marginLeft: 80, display: isLoraScene ? 'flex' : 'none' }}>
              {/*<Form.Item*/}
              {/*	label="lora强度"*/}
              {/*	name="extInfo.loraStrength"*/}
              {/*	rules={[{ required: false, message: '请选择lora强度' }]}*/}
              {/*	style={{ width: '20%' }}*/}
              {/*	labelCol={{ span: 7 }}*/}
              {/*>*/}
              {/*	<InputNumber placeholder="不填,使用默认值" style={{ width: 140 }} />*/}
              {/*</Form.Item>*/}

              {isLoraScene &&
                <Form.Item
                  label="复用发型"
                  name="extInfo.useHairstyle"
                  rules={[{ required: true, message: '请选择模特上新状态' }]}
                  style={{ width: '20%' }}
                  labelCol={{ span: 8 }}
                  initialValue={'N'}
                >
                  <Radio.Group options={[
                    { label: '是', value: 'Y' },
                    { label: '否', value: 'N' },
                  ]} optionType="button" buttonStyle="solid" />
                </Form.Item>
              }

              <Form.Item
                label="是否展示人脸"
                name="extInfo.noshowFace"
                rules={[{ required: true, message: '请选择是否展示人脸' }]}
                style={{ width: '60%' }}
                labelCol={{ span: 3 }}
                initialValue={'N'}
              >
                <Radio.Group options={[
                  { label: '是', value: 'N' },
                  { label: '否', value: 'Y' },
                ]} optionType="button" buttonStyle="solid" />
              </Form.Item>

            </Flex>

            <Flex justify={'flex-start'} style={{ width: '100%', marginLeft: 80 }}>
              <Form.Item
                label="场景状态"
                name="status"
                rules={[{ required: true, message: '请选择正确的场景状态' }]}
                style={{ width: '20%' }}
                labelCol={{ span: 7 }}
              >
                <Radio.Group options={[{ label: '仅供测试', value: 'TEST' },
                { label: '发布线上', value: 'PROD' },
                ]} optionType="button" buttonStyle="solid" />
              </Form.Item>

              <Form.Item
                label="是否为上新状态"
                name="isNew"
                rules={[{ required: true, message: '请选择模特上新状态' }]}
                style={{ width: '80%' }}
                labelCol={{ span: 3 }}
              >
                <Radio.Group options={[
                  { label: '是', value: true },
                  { label: '否', value: false },
                ]} optionType="button" buttonStyle="solid" />
              </Form.Item>
            </Flex>

            {form.getFieldValue('extInfo.isLora') === 'Y' && (
              <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end', marginBottom: 10 }}>
                <div style={{ display: 'flex', gap: 16 }}>
                  <Button
                    type="default"
                    icon={<AppstoreAddOutlined />}
                    style={{ width: '200px' }}
                    onClick={() => setShowManualVerificationModal(true)}
                  >
                    人工核验
                  </Button>
                  <Button
                    type="primary"
                    icon={<AppstoreAddOutlined />}
                    style={{ width: '200px' }}
                    onClick={() => setShowCreatePoseModal(true)}
                  >
                    创建姿势示例图
                  </Button>
                </div>
              </div>
            )}

            <ChildrenTabCard
              children={form.getFieldValue('children')}
              opItemId={opItem?.id}
              allTabList={AllSceneClothTypes}
              form={form}
              withNumber={true}
              onChangeChildren={children => form.setFieldValue('children', children)}
              formItems={(name, restField) => (
                <>
                  {/* lora场景下 并且 全平台开放的场景，展示展示图 */}
                  {form.getFieldValue('extInfo.isLora') === 'Y' && (
                    <Form.Item
                      {...restField}
                      label={form.getFieldValue('extInfo.openScope') === 'ALL' ? '展示图' : null}
                      name={[name, 'extInfo.showImage']}
                      style={{ width: '100%', marginBottom: 24 }}
                    >
                      <Flex gap={24} align="flex-start">
                        {form.getFieldValue('extInfo.openScope') === 'ALL' ? (
                          <div style={{ display: 'flex', flexDirection: 'column' }}>
                            <div style={{ position: 'relative' }}>
                              <ElementImageRecord
                                value={form.getFieldValue(['children', name, 'extInfo.showImage'])}
                                elementId={opItem?.id || 0}
                                currentId={form.getFieldValue(['children', name, 'id'])}
                                mode="select"
                                onChange={(value) => {
                                  form.setFieldValue(['children', name, 'extInfo.showImage'], value);
                                  // 选择展示图后自动保存，但不关闭弹窗
                                  if (value) {
                                    form.validateFields().then((values) => {
                                      const valuesObj = pathObjectToObject(values);

                                      valuesObj.configKey = 'SCENE';
                                      valuesObj.level = 2;
                                      valuesObj.opChildren = true;

                                      // 处理封面展示图
                                      if (valuesObj.showImage) {
                                        if (typeof valuesObj.showImage !== 'string') {
                                          if (!valuesObj.showImage.file.response) {
                                            console.log('封面图片未上传完成，跳过自动保存');
                                            return; // 如果图片未上传完成，不进行保存
                                          }
                                          valuesObj.showImage = valuesObj.showImage.file.response.data;
                                        }
                                      }

                                      // 处理轮播展示图
                                      if (valuesObj.extInfo.showImgs && Array.isArray(valuesObj.extInfo.showImgs)) {
                                        valuesObj.extInfo['showImgs'] = JSON.stringify(valuesObj.extInfo.showImgs);
                                      }

                                      // 处理type和年龄区段
                                      if (valuesObj.type.some(type => AGE_RANGE_LIST.includes(type))) {
                                        valuesObj.type = valuesObj.type.filter(type => !AGE_RANGE_LIST.includes(type));
                                      }
                                      const ageRange = valuesObj.ageRange;
                                      if (ageRange) {
                                        valuesObj.type.push(ageRange);
                                      }
                                      delete valuesObj.ageRange;

                                      // 处理服装类型范围
                                      if (valuesObj.extInfo.clothTypeScope) {
                                        valuesObj.extInfo['clothTypeScope'] = JSON.stringify(valuesObj.extInfo.clothTypeScope);
                                      }

                                      processChildren(valuesObj, AllSceneClothTypes, '服装款式（男/女款）');

                                      // 标记更新展示图状态
                                      valuesObj.showImageStatus = true;

                                      const method = add ? addElement : updateElement;
                                      method(valuesObj).then((res) => {
                                        if (res) {
                                          message.success('展示图保存成功');
                                          // 不关闭弹窗，继续编辑
                                        }
                                      });
                                    }).catch((errorInfo) => {
                                      console.log('表单验证失败，跳过自动保存:', errorInfo);
                                    });
                                  }
                                }}
                              />
                            </div>
                          </div>
                        ) : null}
                        <div style={{ display: 'flex', flexDirection: 'column' }}>
                          <div style={{ position: 'relative' }}>
                            <Flex align="center" gap={8}>
                              <span className="reference-image-title">原始参考图:</span>
                              <div className="reference-image-container">
                                <img
                                  src={form.getFieldValue('children')[name]['extInfo']['styleImage']}
                                  onClick={() => {
                                    setPreviewOpen(true);
                                    setPreviewImage(form.getFieldValue('children')[name]['extInfo']['styleImage']);
                                  }}
                                  alt={'style'}
                                />
                                <div className="preview-text">
                                  点击预览
                                </div>
                              </div>
                            </Flex>
                          </div>
                        </div>
                      </Flex>
                    </Form.Item>
                  )}
                  <Form.Item
                    {...restField}
                    label="构图背景"
                    name={[name, 'tags']}
                    rules={[{ required: true, message: '请输入正确的构图背景' }]}
                    style={{ width: '100%' }}
                  >
                    <InputWithTags tagsType={TagType.IMG_BACKGROUND}
                      placeholder={'请填写构图场景，如：公园,花海,卧室等'} />
                  </Form.Item>
                  <Form.Item
                    {...restField}
                    label="人物姿势"
                    name={[name, 'extInfo.posture']}
                    rules={[{ required: false, message: '请输入正确的人物姿势' }]}
                    style={{ width: '100%' }}
                  >
                    <InputWithTags rows={4}
                      tagsType={TagType.POSE}
                      placeholder={'请输入人物姿势，如：举手，坐姿，撩头发等'} />
                  </Form.Item>
                  <Form.Item
                    {...restField}
                    label="构图镜头"
                    name={[name, 'extInfo.lens']}
                    rules={[{ required: false, message: '请输入正确的构图镜头' }]}
                    style={{ width: '100%' }}
                  >
                    <InputWithTags tagsType={TagType.CAMERA_VIEW}
                      placeholder={'请输入构图镜头，如：远景，中景，特写等'} />
                  </Form.Item>
                  <Form.Item
                    {...restField}
                    label="摄影风格"
                    name={[name, 'extInfo.style']}
                    rules={[{ required: false, message: '请输入正确的摄影风格' }]}
                    style={{ width: '100%' }}
                  >
                    <InputWithTags tagsType={TagType.PHOTO_STYLE}
                      placeholder={'请输入摄影风格，如：cinematic, photopraphic等'} />
                  </Form.Item>
                  {/*<Form.Item*/}
                  {/*	{...restField}*/}
                  {/*	label="负向提示词"*/}
                  {/*	name={[name, 'extInfo.negative']}*/}
                  {/*	rules={[{ required: false, message: '请输入正确的负向提示词' }]}*/}
                  {/*	style={{ width: '100%' }}*/}
                  {/*>*/}
                  {/*	<InputWithTags rows={1} tagsType={TagType.NEGATIVE}*/}
                  {/*		placeholder={'请输入负向提示词'} />*/}
                  {/*</Form.Item>*/}
                  <Form.Item
                    {...restField}
                    label="cfg"
                    name={[name, 'extInfo.cfg']}
                    rules={[{ required: false, message: '请输入正确的cfg' }]}
                    style={{ width: '100%' }}
                  >
                    <InputNumber placeholder={'为空时，默认以服装的cfg为准'} style={{ width: 600 }} />
                  </Form.Item>
                  <div style={{ color: 'red', marginTop: -20, marginLeft: 100 }}>
                    场景的cfg优先级 &gt; 服装的cfg。特殊场景如纯色背景等需要基于场景独立设置cfg时使用
                  </div>
                </>
              )}
            />

          </Form>
        </Modal>
      }

      {
        showTrainDetail && trainDetailId &&
        <LoraTrainDetail id={trainDetailId} onComplete={() => setShowTrainDetail(false)} />
      }

      {
        deleteId > 0 &&
        <Modal title="是否确认删除？" open={deleteId > 0} centered mask={true} closable={false} width={'300px'}
          styles={{
            header: { display: 'flex', flexDirection: 'row', justifyContent: 'center' },
            footer: { display: 'flex', flexDirection: 'row', justifyContent: 'center' },
          }} maskClosable={false}
          onCancel={() => setDeleteId(0)} onOk={() => commitDel()} zIndex={1020}
        >
          <div className={'row-center-block'}
            style={{ color: 'red' }}>删除后将不能恢复，请谨慎操作！
          </div>
        </Modal>
      }

      {
        previewImage && (
          <Image
            wrapperStyle={{ display: 'none' }}
            preview={{
              visible: previewOpen,
              onVisibleChange: (visible) => setPreviewOpen(visible),
              afterOpenChange: (visible) => !visible && setPreviewImage(''),
            }}
            src={previewImage}
          />
        )
      }
      {
        showEditType &&
        <ElementEditModal elementKey={'SCENE'} title={'编辑场景类型'} visible={showEditType}
          onCancel={() => {
            setShowEditType(false);
            setCurrentSceneId(null);
          }}
          onSave={() => {
            setShowEditType(false);
            fetchData();
          }}
          typeList={sceneTypeList}
        />
      }

      {/*管理员：将模特和场景转移给客户*/}
      {assignElementConfig &&
        <AssignModel2User
          materialType={'scene'}
          onCancel={() => {
            setAssignElementConfig(null);
          }} onOk={() => {
            setAssignElementConfig(null);
            fetchData();
          }}
          assignElementConfig={assignElementConfig}
        />
      }

      {/* 创建姿势示例图模态框 */}
      <CreatePoseModal
        visible={showCreatePoseModal}
        sceneId={currentSceneId || 0}
        onCancel={() => {
          setShowCreatePoseModal(false);
        }}
        onOk={(values) => {
          // 立即关闭弹窗
          setShowCreatePoseModal(false);

          // 调用创建姿势示例图的接口
          poseSampleDiagram({
            sceneId: currentSceneId,
            imgNum: values.imgNum,
            proportion: values.proportion,
            faceIdList: values.faceIdList,
          }).then(() => {
            message.success('创建成功');
          }).catch((error) => {
            message.error('创建姿势示例图失败,场景id:' + currentSceneId);
            console.log('创建姿势示例图失败,场景id:' + currentSceneId, error);
          });
        }}
        modelOptions={modelOptions}
      />

      {/* 人工核验弹窗 */}
      <ManualVerificationModal
        visible={showManualVerificationModal}
        sceneId={opItem?.id || 0}
        children={form.getFieldValue('children') || []}
        allTabList={AllSceneClothTypes}
        onClose={() => setShowManualVerificationModal(false)}
        onImageSelect={(poseItemId, imageUrl) => {
          // 在父组件中进行完整的更新操作
          const currentValues = form.getFieldsValue();
          const valuesObj = pathObjectToObject(currentValues);

          valuesObj.configKey = 'SCENE';
          valuesObj.level = 2;
          valuesObj.opChildren = true;

          // 处理封面展示图
          if (valuesObj.showImage) {
            if (typeof valuesObj.showImage !== 'string') {
              if (!valuesObj.showImage.file.response) {
                console.log('封面图片未上传完成，跳过自动保存');
                return;
              }
              valuesObj.showImage = valuesObj.showImage.file.response.data;
            }
          }

          // 处理轮播展示图
          if (valuesObj.extInfo.showImgs && Array.isArray(valuesObj.extInfo.showImgs)) {
            valuesObj.extInfo['showImgs'] = JSON.stringify(valuesObj.extInfo.showImgs);
          }

          // 处理type和年龄区段
          if (valuesObj.type.some(type => AGE_RANGE_LIST.includes(type))) {
            valuesObj.type = valuesObj.type.filter(type => !AGE_RANGE_LIST.includes(type));
          }
          const ageRange = valuesObj.ageRange;
          if (ageRange) {
            valuesObj.type.push(ageRange);
          }
          delete valuesObj.ageRange;

          // 处理服装类型范围
          if (valuesObj.extInfo.clothTypeScope) {
            valuesObj.extInfo['clothTypeScope'] = JSON.stringify(valuesObj.extInfo.clothTypeScope);
          }

          // 更新对应姿势项的展示图
          if (valuesObj.children && Array.isArray(valuesObj.children)) {
            valuesObj.children = valuesObj.children.map(child => {
              if (child.id === poseItemId) {
                return {
                  ...child,
                  extInfo: {
                    ...child.extInfo,
                    showImage: imageUrl
                  }
                };
              }
              return child;
            });
          }

          // 标记更新展示图状态
          valuesObj.showImageStatus = true;

          const method = add ? addElement : updateElement;
          method(valuesObj).then((res) => {
            if (res) {
              message.success('展示图保存成功');
              // 重新获取数据并刷新表单
              queryElementById(opItem?.id || 0).then(refreshRes => {
                if (refreshRes) {
                  const isShowScene = refreshRes.type?.includes('show');
                  // 重新获取服装类别配置
                  getClothCategoryCfg().then(cfg => {
                    if (cfg) {
                      setClothCategoryCfg(isShowScene ? [] : cfg);
                    }
                  });

                  const target = convertWithClothStyle(refreshRes);
                  refreshFormData(target);

                  // 重新设置表单字段
                  form.setFieldValue('type', target.type.filter(e => !AllSceneClothTypes.some(item => item.key === e)));

                  // 重新设置年龄区段
                  const ageRangeValues = target.type.filter(type =>
                    SELECT_AGE_RANGE_OPTIONS.some(group =>
                      group.options.some(option => option.value === type),
                    ),
                  );
                  setCheckedAgeRange(ageRangeValues[0] || null);
                  form.setFieldValue('ageRange', ageRangeValues[0] || null);

                  // 重新设置封面图
                  setCoverShowImgFileList([{
                    uid: '-1',
                    name: 'image.png',
                    status: 'done',
                    url: target.showImage,
                  }]);

                  // 重新设置轮播图和其他扩展信息
                  const showImgs = JSON.parse(refreshRes.extInfo['showImgs'] || '[]') as string[];
                  form.setFieldValue(['extInfo', 'showImgs'], showImgs);

                  const clothTypeScope = JSON.parse(refreshRes.extInfo['clothTypeScope'] || '[]') as ClothTypeScopeItem[];
                  form.setFieldValue(['extInfo', 'clothTypeScope'], clothTypeScope);

                  form.setFieldValue(['extInfo', 'clothCategory'], isShowScene ? [] : (refreshRes.extInfo['clothCategory'] || []));

                  setIsLoraScene(target.extInfo['isLora'] === 'Y');
                }
              });
            }
          }).catch((error) => {
            console.error('展示图保存失败:', error);
            message.error('展示图保存失败');
          });
        }}
      />

    </PageContainer>
  );
};

export default Face;