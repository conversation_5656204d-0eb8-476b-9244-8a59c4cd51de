import React, { useState } from 'react';
import { Modal, Form, Select, InputNumber, Button, Alert } from 'antd';

interface CreatePoseModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  modelOptions: Array<{ label: string; value: number; showImage?: string }>;
  sceneId: number;
}

const CreatePoseModal: React.FC<CreatePoseModalProps> = ({ visible, onCancel, onOk, modelOptions, sceneId }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      // 处理模特参数
      const processedValues = {
        ...values,
        sceneId: sceneId,
        faceIdList: values.faceIdList || []
      };

      Modal.confirm({
        title: '确认',
        centered: true,
        content: '确定开始创作？',
        okText: '确定',
        cancelText: '取消',
        zIndex: 1060,
        onOk: () => {
          onOk(processedValues);
          form.resetFields(); // 清空表单数据
        },
      });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="创建姿势示例图"
      open={visible}
      centered={true}
      onCancel={handleCancel}
      zIndex={1050}
      width={800}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={handleOk}>
          确认
        </Button>,
      ]}
    >
      <Alert
        message="注意：该功能会为该场景所有姿势进行图片创作，注意出图数量，以免占用过多资源～"
        type="warning"
        showIcon
        style={{ marginBottom: 16 }}
      />
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          faceIdList: [],
          proportion: 'P_1152_1536',
          imgNum: 3
        }}
      >
        <Form.Item
          label="选择模特"
          name="faceIdList"
          rules={[
            { required: true, message: '请选择模特' },
            {
              validator: (_, value) => {
                if (!value || value.length === 0) {
                  return Promise.reject(new Error('请至少选择一个模特'));
                }
                if (value.length > 3) {
                  return Promise.reject(new Error('最多只能选择3个模特'));
                }
                return Promise.resolve();
              }
            }
          ]}
        >
          <Select
            mode="multiple"
            showSearch
            placeholder="请选择模特（最多3个）"
            optionFilterProp="children"
            maxTagCount={3}
            dropdownRender={(menu) => (
              <div style={{ maxHeight: 300, overflow: 'auto' }}>
                {menu}
              </div>
            )}
            tagRender={(props) => {
              const { label, value, closable, onClose } = props;
              const selectedOption = modelOptions.find(opt => opt.value === value);
              const labelText = typeof label === 'string' ? label : selectedOption?.label || '';
              
              return (
                <div style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '6px',
                  padding: '2px 8px',
                  backgroundColor: '#f0f0f0',
                  borderRadius: '6px',
                  margin: '2px',
                  fontSize: '12px',
                  border: '1px solid #d9d9d9'
                }}>
                  {selectedOption?.showImage && (
                    <img 
                      src={selectedOption.showImage}
                      alt={labelText}
                      style={{
                        width: '20px',
                        height: '20px',
                        borderRadius: '4px',
                        objectFit: 'cover'
                      }}
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  )}
                  <span style={{ color: '#333' }}>{labelText}</span>
                  {closable && (
                    <span 
                      onClick={(e) => {
                        e.preventDefault();
                        onClose?.();
                      }}
                      style={{
                        cursor: 'pointer',
                        color: '#999',
                        marginLeft: '4px',
                        fontSize: '12px'
                      }}
                    >
                      ×
                    </span>
                  )}
                </div>
              );
            }}
            filterOption={(input, option) => {
              const searchValue = input.toLowerCase();
              const label = option?.label?.toString().toLowerCase() || '';
              const value = option?.value?.toString().toLowerCase() || '';
              // 支持按模特名称或ID进行模糊搜索
              return label.includes(searchValue) || value.includes(searchValue);
            }}
            onChange={(values) => {
              // 限制最多选择3个
              if (values && values.length > 3) {
                form.setFieldValue('faceIdList', values.slice(0, 3));
              }
            }}
          >
            {modelOptions.map(option => (
              <Select.Option key={option.value} value={option.value} label={option.label}>
                <div 
                  style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    padding: '8px 4px',
                    gap: '12px',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f0f8ff';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  {/* 模特首图 */}
                  <div style={{
                    width: '48px',
                    height: '48px',
                    borderRadius: '8px',
                    overflow: 'hidden',
                    backgroundColor: '#f5f5f5',
                    border: '1px solid #e8e8e8',
                    flexShrink: 0,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                  }}>
                    {option.showImage ? (
                      <img 
                        src={option.showImage} 
                        alt={option.label}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover',
                          transition: 'transform 0.2s ease'
                        }}
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                          e.currentTarget.parentElement!.innerHTML = '<div style="color: #999; font-size: 12px; text-align: center;">无图</div>';
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'scale(1.05)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'scale(1)';
                        }}
                      />
                    ) : (
                      <div style={{ 
                        color: '#999', 
                        fontSize: '12px',
                        textAlign: 'center',
                        lineHeight: 1.2
                      }}>
                        无图
                      </div>
                    )}
                  </div>
                  
                  {/* 模特信息 */}
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <div style={{ 
                      fontWeight: '500',
                      fontSize: '14px',
                      color: '#333',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      marginBottom: '2px'
                    }}>
                      {option.label}
                    </div>
                    <div style={{ 
                      color: '#999', 
                      fontSize: '12px',
                      fontFamily: 'monospace'
                    }}>
                      ID: {option.value}
                    </div>
                  </div>
                </div>
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          label="出图数量"
          name="imgNum"
          rules={[{ required: true, message: '请输入出图数量' }]}
        >
          <InputNumber min={1} max={10} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          label="分辨率"
          name="proportion"
          rules={[{ required: true, message: '请选择分辨率' }]}
        >
          <Select
            options={[
              { label: '768x1024', value: 'THREE_FOUR' },
              { label: '1024x1024', value: 'ONE_ONE' },
              { label: '1340x1785', value: 'THREE_FOUR_LG_N' },
              { label: '1536x1536', value: 'ONE_ONE_LG' },
              { label: '1080x1920', value: 'NINE_SIXTEEN_2K' },
              { label: '1152x1536（优先使用）', value: 'P_1152_1536' },
              { label: '1620x2100', value: 'P_1620_2100' },
              { label: '1200x600', value: 'P_1200_600' },
            ]}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreatePoseModal; 