.scene-dialog-block {
  width: 1500px;
  height: auto;
  border-radius: 8px;
  opacity: 1;

  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding: 12px;
  box-shadow: 0 4px 28px 0 rgba(99, 102, 114, 0.3);

  //.ant-tabs-tab {
  //  width: 100px !important;
  //  display: flex;
  //  flex-direction: row;
  //  justify-content: center;
  //  align-items: center;
  //}
  //
  //.ant-tabs-nav-wrap {
  //  flex: none !important;
  //}
  //
  //.ant-tabs-extra-content {
  //  margin-left: 24px;
  //}
}

.scene-cloth-type {
  //margin-top: -8px;
  width: 100%;
}

.reference-image {
  &-title {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.88);
    position: relative;
    top: -25px;
  }

  &-container {
    width: 80px;
    height: 80px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    background-color: #fafafa;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      cursor: pointer;
    }

    .preview-text {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 20px;
      background: rgba(0, 0, 0, 0.45);
      color: #fff;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.carousel-upload-custom .ant-upload-list-picture-card {
  width: 600px;
  height: auto;
}