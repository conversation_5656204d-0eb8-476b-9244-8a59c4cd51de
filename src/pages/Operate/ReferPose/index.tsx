import { <PERSON><PERSON>ontainer } from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';
import {
  Button,
  Checkbox,
  Flex,
  Form,
  Input,
  Modal,
  notification,
  Radio,
  Select,
  Upload,
  UploadFile,
  UploadProps,
  Tooltip,
  message,
} from 'antd';
import { FileType, getBase64, getImageName, objectToPathObject, parseArray, pathObjectToObject } from '@/utils/utils';
import {
  addElement,
  deleteElement,
  ElementConfigWithBlobs, getClothColors,
  getElementConfigByKey,
  getElementTypes,
  resetOrder,
  SceneType,
  updateElement,
} from '@/services/ElementController';
import './index.less';
import DraggableCardList, { DraggableData } from '@/components/DraggableCard';
import ElementEditModal from '@/components/Operate/ElementEditModal';
import OpenScopeSelector from '@/components/Operate/OpenScopeSelector';
import { UPLOAD_PRO_URL, UPLOAD_URL } from '@/constants';
import { PlusOutlined } from '@ant-design/icons';

const ReferPose: React.FC = () => {
  const [datas, setDatas] = useState<Array<ElementConfigWithBlobs>>([]);
  const [showDialog, setShowDialog] = useState(false);
  const [add, setAdd] = useState(false);
  const [deleteId, setDeleteId] = useState<number>(0);
  const [form] = Form.useForm();

  //场景列表，scene type code array
  const [checkedTypes, setCheckedTypes] = useState<string[]>();
  const [categoryTypeList, setCategoryTypeList] = useState<SceneType[]>([]);
  const [selectType, setSelectType] = useState<string>('all');
  const [showEditType, setShowEditType] = useState<boolean>(false);
  const [showOpenScope, setShowOpenScope] = useState(false);

  //图片上传相关
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  const [colorTypes, setColorTypes] = useState<string[]>([]);
  const [logoLocationTypes, setLogoLocationTypes] = useState<SceneType[]>([]);
  const [clothStyleTypes, setClothStyleTypes] = useState<SceneType[]>([]);

  const [faceList, setFaceList] = useState<ElementConfigWithBlobs[]>([]);
  const [checkedFaceList, setCheckedFaceList] = useState<ElementConfigWithBlobs[]>([]);

  const [clothStyleList, setClothStyleList] = useState<ElementConfigWithBlobs[]>([]);
  const [checkedClothStyleList, setCheckedClothStyleList] = useState<ElementConfigWithBlobs[]>([]);

  async function fetchData() {
    const res = await getElementConfigByKey('REFER');
    if (res) {
      setDatas(res);
    }
  }

  useEffect(() => {
    fetchData();

    getElementConfigByKey('FACE').then(faces => {
      if (faces) {
        setFaceList(faces);
      }
    });

    getElementConfigByKey('CLOTH_STYLE').then(res => {
      if (res) {
        setClothStyleList(res);
      }
    });

    getClothColors().then(res => {
      if (res) {
        setColorTypes(res);
      }
    });

    getElementTypes('LOGO_POSITION').then(res => {
      if (res) {
        setLogoLocationTypes(res);

        //将logo位置类型作为参考图的标签
        setCategoryTypeList(res);
      }
    });

    getElementTypes('CLOTH_STYLE').then(types => {
      if (types) {
        setClothStyleTypes(types);
      }
    });

  }, []);

  const handleAdd = () => {
    //先清空一遍表单数据
    form.resetFields();
    setAdd(true);
    setShowDialog(true);
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
  };

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    const file = newFileList[0];

    if (file && file.response && !file.response.success) {
      notification.error({ message: '上传图片异常，请重试' });
      setFileList([]);
      return;
    }
    setFileList(newFileList);
  };

  const handleModify = (item: ElementConfigWithBlobs) => {
    //先清空一遍表单数据
    form.resetFields();
    setAdd(false);

    //展开扩展信息
    const extInfoExtend = objectToPathObject(item.extInfo, 'extInfo');

    console.log('extInfoExtend:', extInfoExtend);

    //string => array
    if (extInfoExtend['extInfo.faces']){
      extInfoExtend['extInfo.faces'] = parseArray(extInfoExtend['extInfo.faces']);
    }
    if (extInfoExtend['extInfo.clothStyles']){
      extInfoExtend['extInfo.clothStyles'] = parseArray(extInfoExtend['extInfo.clothStyles']);
    }

    let values = { ...item, ...extInfoExtend };

    form.setFieldsValue(values);

    setFileList([{
      uid: '-1', // 文件唯一标识，负数表示不是用户新上传的
      name: 'image.png', // 文件名
      status: 'done', // 状态有：uploading, done, error, removed
      url: item.showImage, // 图片路径
    }]);

    console.log('handleModify, init values:', values);

    setShowDialog(true);
  };

  const handleDelete = (item: ElementConfigWithBlobs) => {
    setDeleteId(item.id);
  };

  const commitDel = () => {
    deleteElement(deleteId).then((res) => {
      if (res) {
        setDeleteId(0);
        fetchData();
      }
    });
  };

  const handleCommit = (values) => {

    const extInfoMap = pathObjectToObject(values);
    extInfoMap.configKey = 'REFER';
    extInfoMap.level = 2;

    if (extInfoMap.showImage) {
      if (typeof extInfoMap.showImage !== 'string') {
        if (!extInfoMap.showImage.file.response) {
          notification.error({ message: '请上传图片' });
          return;
        }
        extInfoMap.showImage = extInfoMap.showImage.file.response.data;
        extInfoMap.extInfo['referPoseImage'] = getImageName(extInfoMap.showImage);
      }
    }

    extInfoMap.type = [extInfoMap.extInfo.logoLocation, extInfoMap.extInfo.clothStyleType];

    console.log('handleCommit:', extInfoMap);
    const method = add ? addElement : updateElement;
    method(extInfoMap).then((res) => {
      if (res) {
        notification.success({ message: add ? '添加成功' : '修改成功' });
        setShowDialog(false);
        fetchData();
      }
    });
  };

  const ImageCard = (item: ElementConfigWithBlobs) => (
    <div className="models-image-card" style={{
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
      borderRadius: '12px',
      overflow: 'hidden',
      transition: 'all 0.3s ease',
      position: 'relative',
      background: '#FFFFFF',
      padding: 0,
    }} 
    onMouseOver={(e) => {
      e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.12)';
      e.currentTarget.style.transform = 'translateY(-2px)';
    }}
    onMouseOut={(e) => {
      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.08)';
      e.currentTarget.style.transform = 'translateY(0)';
    }}
    >
      <div className="models-img-cover" style={{
        width: '100%',
        height: '200px',
        background: '#F5F6F9',
        borderRadius: '8px 8px 0 0',
        border: 'none'
      }}>
        <img src={item.showImage} alt={item.name} style={{objectFit: 'contain'}} />
      </div>

      <div style={{padding: '16px', display: 'flex', flexDirection: 'column', gap: '8px'}}>
        <div className="models-image-card-name" style={{fontSize: 14, fontWeight: '600'}}>
          <Tooltip title={item.name}>
            <span
              style={{
                maxWidth: '180px', 
                whiteSpace: 'nowrap', 
                overflow: 'hidden', 
                textOverflow: 'ellipsis',
                display: 'inline-block',
                verticalAlign: 'middle',
                marginRight: '4px',
                cursor: 'pointer'
              }}
              onClick={() => {
                navigator.clipboard.writeText(item.name);
                message.success('已复制参考图名称');
              }}
            >
              {item.name.length > 18 ? item.name.slice(0, 18) + '...' : item.name}
            </span>
          </Tooltip>
          {item.id && 
            <Tooltip title={`复制ID: ${item?.id}`}>
              <span 
                style={{ color: '#727375', fontSize: 12, cursor: 'pointer' }}
                onClick={() => {
                  navigator.clipboard.writeText(item?.id?.toString());
                  message.success('已复制ID');
                }}
              >
                {`(${item?.id})`}
              </span>
            </Tooltip>
          }
        </div>
        <div className={'row-space-block'} style={{paddingTop: '8px', borderTop: '1px solid #f0f0f0'}}>
          <Button className={'operate-btn'} size={'small'} onClick={() => handleModify(item)}>修改配置</Button>
          <Button className={'operate-btn'} size={'small'} onClick={() => handleDelete(item)}>删除</Button>
        </div>
      </div>
    </div>
  );

  const changeOrder = (data: Array<DraggableData>) => {
    console.log('changeOrder', data);
    resetOrder({ items: data }).then((res) => {
      if (res) {
        fetchData();
      }
    });
  };

  const getSearchList = (typeList: SceneType[]) => {
    const res = [];
    if (Array.isArray(typeList)) {
      typeList.forEach(item => {
        // @ts-ignore
        res.push({ label: item.name, value: item.code });
      });
    }
    return [{ label: '全部', value: 'all' }, ...res];
  };

  const handleChangeType = (e) => {
    setSelectType(e.target.value);
  };

  const filterType = (datas) => {
    if (datas.length === 0) {
      return [];
    }
    return datas.filter((item: ElementConfigWithBlobs) => {
      if (selectType === 'all') {
        return true;
      }
      return item.extInfo && item.extInfo.logoLocation && item.extInfo.logoLocation.includes(selectType);
    }).filter(item => {
      if (showOpenScope) {
        console.log('item.extInfo.openScope', item.extInfo.openScope);
        return item.extInfo.openScope && item.extInfo.openScope !== '' && item.extInfo.openScope !== 'ALL';
      }
      return true;
    });
  };

  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  return (
    <PageContainer>

      <Flex vertical={true} gap={8} style={{padding: 16}}>
        <div className="models-filter-row margin-bottom-12">
          <Flex align={'center'}>
            <Checkbox onChange={e => setShowOpenScope(e.target.checked)}>仅展示限制开放</Checkbox>
          </Flex>
          <Radio.Group options={getSearchList(categoryTypeList)}
                       defaultValue={'all'} optionType="button" onChange={e => handleChangeType(e)} />

          <Button className="models-image-card-button"
                  onClick={handleAdd}>新增参考图</Button>
        </div>
        <DraggableCardList data={filterType(datas)} ShowCard={ImageCard}
                           onChange={(data: DraggableData[]) => changeOrder(data)} />
      </Flex>

      <Modal title={''} open={showDialog} centered mask={true} keyboard={false}
             styles={{
               footer: { display: 'flex', flexDirection: 'row', justifyContent: 'center' },
             }} maskClosable={false}
             onCancel={() => setShowDialog(false)} onOk={form.submit}
             closable={false} width={'auto'}>

        <Form className={'scene-dialog-block'}
              labelCol={{ span: 2 }}
              wrapperCol={{ span: 22 }}
              initialValues={{ remember: true }}
              onFinish={handleCommit}
              autoComplete="off"
              form={form}
        >
          <Form.Item hidden label="id" name="id">
            <Input />
          </Form.Item>
          <Form.Item label="名称" name="name" style={{ width: '100%' }}
                     rules={[{ required: true, message: '请输入正确的名称' }]}>
            <Input placeholder={'请输入名称'} style={{ width: 600 }} />
          </Form.Item>

          <Form.Item
            label="展示图片"
            name="showImage"
            rules={[{ required: true, message: '请上传场景图片' }]}
            style={{ width: '100%' }}
          >
            <Upload
              action={UPLOAD_PRO_URL}
              listType="picture-card"
              fileList={fileList}
              onPreview={handlePreview}
              onChange={handleChange}
            >
              {fileList.length >= 1 ? null : uploadButton}
            </Upload>
          </Form.Item>

          <Form.Item hidden label="姿势参考图地址" name="extInfo.referPoseImage">
            <Input />
          </Form.Item>

          <Form.Item label="印花位置"
                     name="extInfo.logoLocation"
                     style={{ width: '100%' }}
                     rules={[{ required: true, message: '请选择印花位置' }]}>

            {logoLocationTypes && logoLocationTypes.length > 0 && (
              <Select
                placeholder="选择印花位置"
                style={{ width: 600 }}
              >
                {logoLocationTypes.map(s => (
                  <Select.Option key={s.code} value={s.code}>
                    {s.name}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>

          <Form.Item label="款式类型"
                     name="extInfo.clothStyleType"
                     style={{ width: '100%' }}
                     rules={[{ required: true, message: '请选择款式类型' }]}>

            {clothStyleTypes && clothStyleTypes.length > 0 && (
              <Select
                placeholder="选择款式"
                style={{ width: 600 }}
              >
                {clothStyleTypes.map(s => (
                  <Select.Option key={s.code} value={s.code}>
                    {s.name}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>

          <Form.Item label="服装款式"
                     name="extInfo.clothStyles"
                     style={{ width: '100%' }}
                     rules={[{ required: true, message: '选择服装款式' }]}>

            {clothStyleList && clothStyleList.length > 0 && (
              <Select
                mode="tags"
                placeholder="选择服装款式（可多选）"
                style={{ width: 600 }}
                value={checkedClothStyleList}
                onChange={value => {
                  console.log('服装款式 onchange', value);
                  setCheckedClothStyleList(value);
                }}
                optionLabelProp="label"
              >
                {clothStyleList.map(s => (
                  <Select.Option key={s.id} value={s.id} label={(
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <img
                        src={s.showImage}
                        alt={s.name}
                        style={{ width: 20, height: 20, marginRight: 8 }}
                      />
                      {s.name}
                    </div>
                  )}>
                    <div style={{ display: 'flex', alignItems: 'center', height: 40 }}>
                      <img
                        src={s.showImage}
                        alt={s.name}
                        style={{ width: 60, height: 60, marginRight: 8, objectFit: 'contain' }}
                      />
                      {s.name}
                    </div>
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>

          <Form.Item label="模特"
                     name="extInfo.faces"
                     style={{ width: '100%' }}
                     rules={[{ required: true, message: '选择模特' }]}>

            {faceList && faceList.length > 0 && (
              <Select
                mode="tags"
                placeholder="选择模特（可多选）"
                style={{ width: 600 }}
                optionLabelProp="label"
                value={checkedFaceList}
                onChange={value => setCheckedFaceList(value)}
              >
                {faceList.map(s => (
                  <Select.Option key={s.id} value={s.id} label={(
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <img
                        src={s.showImage}
                        alt={s.name}
                        style={{ width: 20, height: 20, marginRight: 8 }}
                      />
                      {s.name}
                    </div>
                  )}>
                    <div style={{ display: 'flex', alignItems: 'center', height: 40 }}>
                      <img
                        src={s.showImage}
                        alt={s.name}
                        style={{ width: 60, height: 60, marginRight: 8, objectFit: 'contain' }}
                      />
                      {s.name}
                    </div>
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>

          <Form.Item
            label="状态"
            name="status"
            rules={[{ required: true, message: '请选择正确的状态' }]}
            style={{ width: '100%' }}
          >
            <Radio.Group options={[{ label: '仅供测试', value: 'TEST' },
              { label: '发布线上', value: 'PROD' },
            ]} optionType="button" buttonStyle="solid" />
          </Form.Item>
          <OpenScopeSelector />
        </Form>
      </Modal>
      <Modal title="是否确认删除？" open={deleteId > 0} centered mask={true} closable={false} width={'300px'}
             styles={{
               header: { display: 'flex', flexDirection: 'row', justifyContent: 'center' },
               footer: { display: 'flex', flexDirection: 'row', justifyContent: 'center' },
             }} maskClosable={false}
             onCancel={() => setDeleteId(0)} onOk={() => commitDel()}
      >
        <div className={'row-center-block'}
             style={{ color: 'red' }}>删除后将不能恢复，请谨慎操作！
        </div>
      </Modal>

      {showEditType &&
        <ElementEditModal elementKey={'REFER'} title={'编辑场景类型'} visible={showEditType} onCancel={() => setShowEditType(false)}
                          onSave={() => {
                            setShowEditType(false);
                            fetchData();
                          }}
                          typeList={categoryTypeList}
        />
      }
    </PageContainer>
  );
};

export default ReferPose;