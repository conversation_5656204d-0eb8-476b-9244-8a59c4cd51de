import React, { useEffect, useState } from 'react';
import { Button, Form, Input, message, Modal, Switch, Table, Tabs, TabsProps } from 'antd';
import { createTag, deleteTagById, queryTagsList, TagVO, updateTag } from '@/services/TagsController';
import { PageContainer } from '@ant-design/pro-layout';
import ShowCaseBlock from '@/components/Operate/ShowCaseBlock';
import PromptDictBlock from '@/components/Operate/PromptDictBlock';
import ImageCaseBlock from '@/components/Operate/ImageCaseBlock';
import ShootingStyleBlock from '@/components/Operate/ShootingStyleBlock';
import { useLocation } from 'react-router-dom';

const TagManagement: React.FC = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const defaultType = queryParams.get('defaultType');
  const defaultDictType = queryParams.get('defaultDictType');

  const [type, setType] = useState(defaultType || 'tags');
  const [tags, setTags] = useState<TagVO[]>();
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentTag, setCurrentTag] = useState(null);

  const [form] = Form.useForm();

  useEffect(() => {
    fetchTags();
  }, []);

  const fetchTags = async () => {
    setLoading(true);
    queryTagsList({}).then(res => {
      if (res) {
        // @ts-ignore
        setTags(res);
      }
      setLoading(false);
    });
  };
  const handleEdit = (tag) => {
    setCurrentTag(tag);
    form.setFieldsValue(tag);
    setModalVisible(true);
  };

  const handleDelete = async (id) => {
    setLoading(true);
    try {
      await deleteTagById(id);
      message.success('删除成功');
      fetchTags();
    } catch (error) {
      message.error('删除失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values) => {
    setLoading(true);
    try {
      if (currentTag) {
        // @ts-ignore
        await updateTag({ ...currentTag, ...values });
        message.success('更新成功');
      } else {
        await createTag(values);
        message.success('添加成功');
      }
      fetchTags();
      setModalVisible(false);
    } catch (error) {
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  const changeType = (key: string) => {
    if (key !== 'dict') {
      setType(key);
      return;
    }
    setType(key);
  };


  const items: TabsProps['items'] = [
    { key: 'tags', label: '标签管理' },
    { key: 'dict', label: 'prompt字典' },
    { key: 'show-case', label: '优秀案例' },
    { key: 'image-case', label: '图库管理' },
    { key: 'shooting-style', label: '风格分类管理' },
  ];

  const columns = [
    { title: '标签ID', dataIndex: 'id', key: 'id' },
    { title: '标签类型', dataIndex: 'type', key: 'type' },
    { title: '标签名称', dataIndex: 'name', key: 'name' },
    { title: '标签详情', dataIndex: 'detail', key: 'detail' },
    {
      title: '默认选中',
      dataIndex: 'defChecked',
      key: 'defChecked',
      render: (checked) => (checked ? '是' : '否'),
    },
    { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
    { title: '修改时间', dataIndex: 'modifyTime', key: 'modifyTime' },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <span>
          <Button type="link" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Button type="link" danger onClick={() => handleDelete(record.id)}>
            删除
          </Button>
        </span>
      ),
    },
  ];


  return (
    <PageContainer style={{ padding: 12 }}>
      <Tabs defaultActiveKey={type} items={items} onChange={(key) => changeType(key)} size={'large'}
        indicator={{ size: 102 }} style={{ justifyContent: 'center' }} />

      {type === 'tags' &&
        <div className="tag-management">
          <Table
            columns={columns}
            dataSource={tags}
            loading={loading}
            rowKey="id"
            className="tm-table"
          />
          <Modal
            title={currentTag ? '编辑标签' : '新增标签'}
            open={modalVisible}
            onCancel={() => setModalVisible(false)}
            onOk={() => form.submit()}
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
            >
              <Form.Item name="type" label="标签类型" rules={[{ required: true }]}>
                <Input disabled={true} />
              </Form.Item>
              <Form.Item name="name" label="标签名称" rules={[{ required: true }]}>
                <Input />
              </Form.Item>
              <Form.Item name="detail" label="标签详情">
                <Input.TextArea />
              </Form.Item>
              <Form.Item name="defChecked" label="默认选中" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Form>
          </Modal>
        </div>
      }

      {type === 'dict' &&
        <PromptDictBlock defaultDictType={defaultDictType} />
      }

      {type === 'show-case' &&
        <ShowCaseBlock />
      }

      {type === 'image-case' &&
        <ImageCaseBlock />
      }

      {type === 'shooting-style' &&
        <ShootingStyleBlock />
      }
    </PageContainer>
  );
};

export default TagManagement;