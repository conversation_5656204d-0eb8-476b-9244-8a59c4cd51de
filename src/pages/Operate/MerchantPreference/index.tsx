import { <PERSON><PERSON>ontainer } from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';
import { Button, Card, Flex, Input, message, Radio, Tabs, TabsProps, Tag, Tooltip } from 'antd';
import {
  MerchantPreference,
  queryAllMerchantPreference,
  queryMerchantConfigs,
  updateSys,
} from '@/services/SystemController';
import IconFont from '@/components/IconFont';
import MerchantPreferenceSetting from '@/components/Operate/MerchantPreferenceSetting';
import { ElementConfig, getElementConfig } from '@/services/ElementController';
import { formatText } from '@/utils/utils';
import Popconfirm from 'antd/lib/popconfirm';
import { PlusOutlined } from '@ant-design/icons';
import MasterSelector from '@/components/MasterSelector';
import { ROLE_TYPE } from '@/services/UserController';

const MerchantPreferenceMng: React.FC = () => {
  const [data, setData] = useState<Array<MerchantPreference>>([]);
  const [opItem, setOpItem] = useState<MerchantPreference | null>(null);
  const [faceList, setFaceList] = useState<Array<ElementConfig>>([]);
  const [sceneList, setSceneList] = useState<Array<ElementConfig>>([]);
  const [searchKey, setSearchKey] = useState<string | null>(null);
  const [add, setAdd] = useState(false);
  const [type, setType] = useState('preference');
  const [delConfirm, setDelConfirm] = useState<string | null>(null);
  const [addConfig, setAddConfig] = useState<string | null>(null);
  const [otherSetting, setOtherSetting] = useState({});
  const [searchCreativeType, setSearchCreativeType] = useState('all');
  const [searchEnableAutoCreative, setSearchEnableAutoCreative] = useState('all');

  useEffect(() => {
    fetchData();
    //获取脸和场景
  }, []);

  const fetchElements = async () => {
    if (faceList.length > 0 && sceneList.length > 0) {
      return;
    }
    getElementConfig('CREATE_IMAGE').then(cfg => {
      if (cfg) {
        for (let c of cfg) {
          if (c.configKey === 'FACE') {
            setFaceList(c.children);
          }
          if (c.configKey === 'SCENE') {
            setSceneList(c.children);
          }
        }
      }
    });
  };

  const fetchData = () => {
    queryAllMerchantPreference().then(res => {
      if (res) {
        setData(res);
      }
    });
  };

  const handleAdd = () => {
    setOpItem(null);
    fetchElements().then(() => {
      //先清空一遍表单数据
      setAdd(true);
    });
  };

  const handleModify = (item: MerchantPreference) => {
    setAdd(false);
    fetchElements().then(() => {
      setOpItem(item);
    });
  };

  const changeType = (key: string) => {
    if (key !== 'others') {
      setType(key);
      return;
    }
    queryMerchantConfigs().then(res => {
      if (res) {
        setOtherSetting(res);
        console.log('asdf', res);
        setType(key);
      }
    });
  };

  const NAME_MAP = {
    autoTrainMerchants: 'AUTO_TRAIN_MERCHANT',
    autoDeliveryMerchants: 'AUTO_DELIVERY_MERCHANT',
    excludeSystemCollocation: 'EXCLUDE_SYSTEM_COLLOCATION',
  };
  const commitDel = (name, id) => {
    let array = otherSetting[name] ? otherSetting[name].filter(item => item.id !== id).map(item => item.id) : [];

    const confKey = NAME_MAP[name] ? NAME_MAP[name] : name;
    updateSys({ confKey: confKey, confValue: JSON.stringify([...new Set(array)]) }).then(res => {
      if (res) {
        message.success('删除成功');
        setDelConfirm(null);
        changeType('others');
      }
    });
  };

  const commitAdd = (name, id) => {
    let array = otherSetting[name] ? otherSetting[name].map(item => item.id) : [];
    array.push(Number(id));

    const confKey = NAME_MAP[name] ? NAME_MAP[name] : name;
    updateSys({ confKey, confValue: JSON.stringify([...new Set(array)]) }).then(res => {
      if (res) {
        message.success('添加成功');
        setAddConfig(null);
        changeType('others');
      }
    });
  };

  const commitUpd = (name, value) => {
    const confKey = NAME_MAP[name] ? NAME_MAP[name] : name;
    updateSys({ confKey, confValue: value }).then(res => {
      if (res) {
        message.success('修改成功');
        changeType('others');
      }
    });
  };

  const items: TabsProps['items'] = [{ key: 'preference', label: '偏好设置' }, { key: 'others', label: '其他设置' }];
  const searchItems = [{ label: '全部', value: 'all' }, { label: '未设置', value: 'unset' },
    { label: '已设置', value: 'setOn' }];
  const searchEnableItems = [{ label: '全部', value: 'all' }, { label: '自动出图', value: 'true' },
    { label: '非自动出图', value: 'false' }];

  const tagStyle = { height: 30, display: 'flex', alignItems: 'center' };

  const getTitle = (item) => {
    return <Flex justify={'flex-start'} align={'center'} gap={8}>
      {item.nickName}
      {item.creativePreferences.length > 0 &&
        <Tooltip title={'已配置商家创作默认搭配'}>
          <div className={'detail-round text12 weight color-brand'}
               style={{ width: 16, height: 16, background: 'rgba(0, 0, 0, 0.15)', zIndex: 2 }}>搭
          </div>
        </Tooltip>
      }
      {item.creativePreferences.some(item => item.enableAutoCreative) &&
        <Tooltip title={'已配置商家创作自动出图'}>
          <div className={'detail-round text12 weight color-brand'}
               style={{ width: 16, height: 16, background: 'rgba(0, 0, 0, 0.15)', zIndex: 2 }}>自
          </div>
        </Tooltip>
      }
    </Flex>;
  };

  const MerchantConfig: React.FC<{ title: string, name: string, roleTypes?: Array<ROLE_TYPE> }> = ({
                                                                                                     title,
                                                                                                     name,
                                                                                                     roleTypes = null,
                                                                                                   }) => {
    return <Flex vertical gap={12}>
      <div className={'text16 weight'}>{title}</div>
      <Flex justify={'flex-start'} gap={8} wrap={'wrap'}>
        {otherSetting && otherSetting[name] && otherSetting[name].map(item =>
          <Popconfirm
            key={item.id}
            title={`确定要从${title}中删除${item.nickName}吗?`}
            open={delConfirm === name + '_' + item.id}
            onConfirm={() => commitDel(name, item.id)}
            onCancel={() => setDelConfirm(null)}
          >
            <Tag closable={true} style={tagStyle} onClose={(e) => {
              setDelConfirm(name + '_' + item.id);
              e.preventDefault();
            }} color={'processing'}>{item.nickName}</Tag>
          </Popconfirm>)
        }

        {(!addConfig || addConfig !== name) &&
          <Tag className={'pointer'} style={tagStyle} icon={<PlusOutlined />}
               onClick={() => setAddConfig(name)}>新增</Tag>
        }

        {(addConfig && addConfig === name) &&
          <MasterSelector onChange={userId => commitAdd(name, userId)} width={160} roleTypes={roleTypes}
                          excludes={otherSetting[name] ? otherSetting[name].map(item => item.id) : []} />
        }
      </Flex>
    </Flex>;
  };

  const OptionsConfig = ({ title, name, options }) => {
    return <Flex vertical gap={12}>
      <div className={'text16 weight'}>{title}</div>
      <Flex justify={'flex-start'} gap={8} wrap={'wrap'}>
        <Radio.Group options={[{ label: '关闭', value: 'none' }, { label: '全部', value: 'all' }, ...options]}
                     defaultValue={'none'} optionType="button" onChange={e => commitUpd(name, e.target.value)}
                     value={otherSetting && otherSetting[name]} buttonStyle={'solid'} />
      </Flex>
    </Flex>;
  };

  return (
    <PageContainer style={{ padding: 12 }}>
      <Tabs defaultActiveKey={type} items={items} onChange={(key) => changeType(key)} size={'large'}
            indicator={{ size: 102 }} style={{ justifyContent: 'center' }} />

      {type === 'preference' &&
        <Flex vertical gap={12}>
          <Flex justify={'flex-start'} gap={8} className={'width-100'}>
            <Input placeholder="根据商家名称筛选" onChange={e => setSearchKey(e.target.value)} style={{ width: 180 }} />
            <Radio.Group options={searchEnableItems} defaultValue={'all'} optionType="button"
                         onChange={e => setSearchEnableAutoCreative(e.target.value)} />
            <Radio.Group options={searchItems} defaultValue={'all'} optionType="button"
                         onChange={e => setSearchCreativeType(e.target.value)} />
            <Button className="models-image-card-button"
                    onClick={handleAdd}>新增商家</Button>
          </Flex>

          <Flex gap={8} wrap={'wrap'} justify={'flex-start'} align={'flex-start'}>
            {data.filter(item => !searchKey || item.nickName.includes(searchKey)).filter(item => searchCreativeType === 'all' || (searchCreativeType === 'unset' ? item.creativePreferences.length <= 0 : item.creativePreferences.length > 0))
              .filter(item => searchEnableAutoCreative === 'all' || (searchEnableAutoCreative === 'true' ? item.creativePreferences.some(e => e.enableAutoCreative) : !item.creativePreferences.some(e => e.enableAutoCreative))).map(item =>
                <Card key={item.userId} title={getTitle(item)} style={{ width: 300, height: 240 }}
                      extra={<IconFont type={'icon-bianji'} className={'color-error pointer'} style={{ fontSize: 18 }}
                                       onClick={() => handleModify(item)} />}>
                  <Flex vertical gap={8}>
                    {item.preferences && item.preferences.map((p, index) =>
                      <div key={index}>
                        偏好{index + 1}：{formatText(p.memo, 13)}
                      </div>)}
                  </Flex>
                </Card>,
              )}
          </Flex>
        </Flex>
      }

      {type === 'others' &&
        <Flex vertical gap={24}>
          <MerchantConfig title={'自动训练白名单'} name={'autoTrainMerchants'}
                          roleTypes={['MERCHANT', 'OPERATOR', 'ADMIN', 'DISTRIBUTOR', 'SYSTEM']} />
          <MerchantConfig title={'自动交付白名单'} name={'autoDeliveryMerchants'}
                          roleTypes={['MERCHANT', 'OPERATOR', 'ADMIN', 'DISTRIBUTOR', 'SYSTEM']} />
          <MerchantConfig title={'不使用系统搭配白名单(目前仅支持上半身)'} name={'excludeSystemCollocation'} />
          <MerchantConfig title={'可查看所有服装和创作记录白名单'} name={'SEE_ALL_MODELS_AND_HISTORY'}
                          roleTypes={['MERCHANT', 'DISTRIBUTOR']} />
          <MerchantConfig title={'服装训练：不抠图商家白名单'} name={'NO_CUTOUT_MERCHANT_LIST'}
                          roleTypes={['MERCHANT', 'OPERATOR', 'ADMIN', 'DISTRIBUTOR', 'SYSTEM']} />
          <MerchantConfig title={'服装训练：精准打标商家白名单'} name={'LORA_NEW_LABEL_MERCHANT_LIST'}
                          roleTypes={['MERCHANT', 'OPERATOR', 'ADMIN', 'DISTRIBUTOR', 'SYSTEM']} />
          <MerchantConfig title={'服装训练：极简打标商家白名单'} name={'LORA_MINI_LABEL_MERCHANT_LIST'}
                          roleTypes={['MERCHANT', 'OPERATOR', 'ADMIN', 'DISTRIBUTOR', 'SYSTEM']} />
          <OptionsConfig title={'夜间自动训练开关（20:30-09:00）'} name={'NIGHTTIME_AUTO_TRAIN_SWITCH'}
                         options={[{ label: 'VIP客户', value: 'vip' }]} />
        </Flex>
      }

      {(opItem || add) &&
        <MerchantPreferenceSetting userId={opItem ? opItem.userId : null} userNick={opItem ? opItem.nickName : null}
                                   faceList={faceList} sceneList={sceneList} merchantPreference={opItem ? opItem : null}
                                   onCancel={() => {
                                     setOpItem(null);
                                     setAdd(false);
                                   }} changeMerchantPreference={() => fetchData()} newMerchant={add}
                                   hasMerchantIds={data.map(item => item.userId)}
        />
      }

    </PageContainer>
  );
};

export default MerchantPreferenceMng;