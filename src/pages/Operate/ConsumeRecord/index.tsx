import React, { useEffect, useState } from 'react';
import { DatePicker, Flex, Pagination, Select, Table } from 'antd';
import { PageContainer } from '@ant-design/pro-layout';
import type { ColumnsType } from 'antd/es/table';
import { queryPointUsageInfoByPage, UserPointUsageInfoVO } from '@/services/UserPointLogController';
import IconFont from '@/components/IconFont';
import { queryAllMaster, RoleTypesItems } from '@/services/UserController';

interface user {
  id: number;
  nickName: string;
}

const ConsumeRecord: React.FC = () => {

  const [usageRecords, setUsageRecords] = useState<UserPointUsageInfoVO[]>([]);
  const [totalCount, setTotalCount] = useState<number>(0);

  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [hasNextPage, setHasNextPage] = useState<boolean>(true);

  const [dates, setDates] = useState([]);
  const [orderByCreateTime, setOrderByCreateTime] = useState('');
  const [masterUserId, setMasterUserId] = useState<number | null>();
  const [userList, setUserList] = useState<Array<user>>();

  useEffect(() => {
    let orderBy = 'create_time desc';

    if (orderByCreateTime === 'ascend') {
      orderBy = 'create_time asc';
    }

    let query = {
      pageNum: currentPage,
      pageSize: pageSize,
      orderBy: 'id desc',
      userId: masterUserId,
    };

    if (dates && dates.length > 1) {
      // @ts-ignore
      query['dateFrom'] = dates[0].format('YYYYMMDD');
      // @ts-ignore
      query['dateTo'] = dates[1].format('YYYYMMDD');
    }


    queryPointUsageInfoByPage(query).then(page => {
      if (page) {
        setHasNextPage(page.hasNextPage != null && page.hasNextPage);
        setUsageRecords(page?.list || []);
        setTotalCount(page?.totalCount || 0);
      }
      });
  }, [currentPage, pageSize, orderByCreateTime, dates, masterUserId]);

  useEffect(() => {
    queryAllMaster().then(res => {
      if (res && Array.isArray(res)) {
        setUserList(res);
      }
    });
  }, []);

  const handleDateChange = (newDates) => {
    setDates(newDates);
    setCurrentPage(1);
  };

  const handleMasterUserId = (value) => {
    setMasterUserId(value);
    setCurrentPage(1);
  };
  function getRoleTypeName(record: UserPointUsageInfoVO) {
    switch (record.operatorRole) {
      case 'MERCHANT':
        return '商家' + (record.masterUser ? '(主)' : '(子)');
      case 'DISTRIBUTOR':
        return '销售/渠道商';
      default:
        return RoleTypesItems.find(item=> item.key === record.operatorRole)?.label;
    }
  };

  // 使用记录
  const usageColumns: ColumnsType<UserPointUsageInfoVO> = [
    { title: '客户id', dataIndex: 'userId', key: 'userId' },
    { title: '昵称', dataIndex: 'masterUserNickName', key: 'masterNickName' },
    { title: '使用场景', dataIndex: 'usageScene', key: 'usageScene' },
    { title: '使用方式', dataIndex: 'usageWay', key: 'usageWay' },
    {
      title: '消耗数量', key: 'usedCount',
      render: (text, record) => {
        return (
          <Flex gap={'10px'} align={'center'}>

            {record.usedModelPoint != null && record.usedModelPoint > 0 && (
              <Flex gap={'4px'} align={'center'} style={{ fontSize: 14 }}>
                <IconFont type={'icon-icon_tupian'} style={{ fontSize: 24 }} />
                {record.usedModelPoint}张创作图
              </Flex>
            )}
            {record.usedGivePoint != null && record.usedGivePoint > 0 && (
              <Flex gap={'4px'} align={'center'} style={{ fontSize: 14 }}>
                <IconFont type={'icon-icon_tupian'} style={{ fontSize: 24 }} />
                {record.usedGivePoint}张赠送创作图
              </Flex>
            )}
            {record.usedPoint != null && (
              <Flex gap={'4px'} align={'center'} style={{ fontSize: 14 }}>
                <IconFont type={'icon-icon_mousidian'} style={{ fontSize: 24 }} />
                {record.usedPoint > 0 ? '+' : ''}{record.usedPoint}缪斯点
              </Flex>
            )}
          </Flex>
        );
      },
    },
    { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
    {
      title: '使用账号', key: 'operatorLoginId',
      render: (text, record) => {
        return (
          <Flex gap={'8px'}>
            <span>{`${record.operatorNickName}(${record.operatorLoginId})`}</span>
            <div style={{ borderRadius: '4px', background: '#F5F6F9', color: '#727375' }}>
              {getRoleTypeName(record)}
            </div>
          </Flex>
        );
      },
    },
  ];

  return (
    <PageContainer style={{ padding: 24, gap: '24px' }}>
      <div
        style={{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center', gap: '24px', margin: '0 0 24px 0' }}>

        <DatePicker.RangePicker onChange={handleDateChange} placeholder={['开始日期', '结束日期']}
                     style={{ width: 320, height: 32 }} />
        <Select value={masterUserId} placeholder="搜索主账号昵称" onChange={handleMasterUserId}
                style={{ width: 200 }} allowClear showSearch optionFilterProp="label"
                options={userList ? userList.map(user => ({ label: user.nickName, value: user.id })) : []} />

      </div>
      <div className="consume-info-page">
        <Table
          columns={usageColumns}
          dataSource={usageRecords}
          rowKey="pointLogId"
          className={'members-user-table'}
          pagination={false}
        />
        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={totalCount}
          onChange={(page, pageSize) => {
            setCurrentPage(page);
            setPageSize(pageSize);
          }}
          showTotal={(total, range) => `共 ${totalCount} 条`}
          showSizeChanger // 允许用户更改每页显示条数
          showQuickJumper // 允许用户快速跳转到某一页
          onShowSizeChange={(current, size) => {
            console.log('Pagination onShowSizeChange:', current, size);
            setPageSize(size);
            setCurrentPage(1);
          }}
          pageSizeOptions={['10', '20', '50', '100']}
          style={{ marginTop: '16px', textAlign: 'center' }}
        />
      </div>
    </PageContainer>
  );
};

export default ConsumeRecord;