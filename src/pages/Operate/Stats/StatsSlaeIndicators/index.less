.sale-stats-container {
  margin-top: 24px;

  .sale-stats-selection-card {
    margin-bottom: 16px;
  }

  .sale-stats-filter-card {
    margin-bottom: 16px;
  }

  .sale-stats-detail-section {
    margin-top: 16px;
  }

  .sale-stats-upper-tabs {
    margin-bottom: 16px;
  }

  .sale-stats-metric-usage {
    font-weight: 500;
    color: #1890ff;
  }

  .sale-stats-metric-amount {
    font-weight: 500;
    color: #52c41a;
  }

  .sale-stats-metric-customers {
    font-weight: 500;
    color: #fa8c16;
  }

  .sale-stats-metric-retention {
    font-weight: 500;
    color: #722ed1;
  }

  .sale-stats-company-label {
    display: inline-block;
    background-color: #e6f7ff;
    color: #1890ff;
    padding: 2px 6px;
    border-radius: 4px;
    margin-right: 4px;
    font-size: 12px;
    border: 1px solid #91d5ff;
  }

  .sale-stats-salesperson-label {
    display: inline-block;
    background-color: #f6ffed;
    color: #52c41a;
    padding: 2px 6px;
    border-radius: 4px;
    margin-right: 4px;
    font-size: 12px;
    border: 1px solid #b7eb8f;
  }

  /* 主表格和展开行样式 */
  .stats-main-table {
    .ant-table-row {
      background: #fff;
      
      &.company-row {
        background-color: #fafafa;
      }
      
      &.salesperson-row {
        background-color: #fff;
      }
    }
    
    .ant-table-expanded-row {
      .expanded-row-class {
        margin-bottom: 16px;
        position: relative;
        z-index: 2;
        
        &:after {
          content: '';
          height: 16px;
          display: block;
          position: absolute;
          bottom: -16px;
          left: 0;
          right: 0;
          background: #f9f9f9;
          z-index: 1;
        }
      }
    }
    
    /* 确保展开行不会影响后续行 */
    .ant-table-row {
      position: relative;
      z-index: 1;
    }
    
    .ant-table-expanded-row {
      position: relative;
      z-index: 0; 
      
      .sale-stats-detail-expanded {
        position: relative;
        z-index: 0;
        margin-bottom: 16px;
        
        /* 为子表格设置样式 */
        .sublist-table-container, 
        .orders-table-container {
          position: relative;
          z-index: 0;
          margin-bottom: 16px;
          
          .ant-table-wrapper {
            position: relative;
            z-index: 0;
            margin-bottom: 0;
            
            .ant-table {
              margin-bottom: 0;
            }
          }
        }
      }
    }
    
    /* 确保行的间隔和分隔 */
    .ant-table-tbody {
      > tr:not(.ant-table-expanded-row) {
        & + tr:not(.ant-table-expanded-row) {
          td {
            border-top: 1px solid #f0f0f0;
          }
        }
        
        &.ant-table-row:hover > td {
          background-color: rgba(24, 144, 255, 0.05);
        }
      }
      
      > tr.ant-table-expanded-row > td {
        padding: 0;
        border-bottom: none;
      }
      
      tr.ant-table-expanded-row + tr.ant-table-row td {
        border-top: 1px solid #e8e8e8;
      }
    }

    .ant-table-row-expand-icon {
      color: #1890ff;
    }
  }

  .sale-stats-detail-expanded {
    padding: 0;
    background-color: #f9f9f9;
    border-radius: 4px;
    margin-bottom: 16px;
    position: relative;
    z-index: 0;
    
    /* 子表格容器 */
    .sublist-table-container,
    .orders-table-container {
      border-radius: 4px;
      overflow: hidden;
    }
    
    /* 嵌套表格样式 */
    .nested-table-component,
    .orders-table-component {
      .ant-table-thead > tr > th {
        background-color: #f0f0f0;
        font-weight: 600;
        color: #333;
      }
      
      .ant-table-tbody > tr > td {
        border-bottom: 1px solid #f5f5f5;
      }
      
      .ant-table-tbody > tr:last-child > td {
        border-bottom: none;
      }
    }
  }

  .sale-indicator-card {
    transition: all 0.3s;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
  }

  .company-card {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .ant-card-head {
      background-color: #f5f7fa;
      border-bottom: 1px solid #e8eaed;
    }
  }

  .salesperson-card {
    border-left: 4px solid #52c41a;
    margin-left: 24px;
    margin-bottom: 12px;
  }

  /* 嵌套表格样式 */
  .nested-table {
    background-color: #f9f9f9;
    padding: 16px;
    margin-top: 0;
    border-radius: 4px;

    .nested-table-title {
      font-weight: 500;
      margin-bottom: 12px;
      font-size: 14px;
      color: #333;
    }

    .nested-table-content {
      background-color: white;
      border-radius: 4px;
      padding: 8px;
    }
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
    margin: 16px 0;
  }

  .metric-item {
    background: #fafafa;
    padding: 12px;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    
    .metric-label {
      color: #666;
      font-size: 12px;
      margin-bottom: 4px;
    }
    
    .metric-value {
      font-size: 18px;
      font-weight: 600;
    }
  }

  /* 指标样式 */
  .exp-count {
    color: #1890ff;
    font-weight: 500;
  }

  .conversion-count {
    color: #52c41a;
    font-weight: 500;
  }

  .points-count {
    color: #fa8c16;
    font-weight: 500;
  }

  .activity-rate {
    color: #722ed1;
    font-weight: 500;
  }

  .repurchase-rate {
    color: #eb2f96;
    font-weight: 500;
  }
  
  .custom-model {
    color: #f5222d;
    font-weight: 500;
  }
  
  .custom-scene {
    color: #13c2c2;
    font-weight: 500;
  }
  
  .protection-metrics {
    color: #faad14;
    font-weight: 500;
  }
  
  .create-count {
    color: #2f54eb;
    font-weight: 500;
  }
}
