import React, { useEffect, useState, useCallback, memo, useMemo, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
    Card, DatePicker, Table, Pagination,
    Button, Flex, InputNumber, message, Input, Tooltip, Tag, Empty, Spin, Radio
} from 'antd';
import { PageInfo, statsSaleIndicators, StatsSaleIndicatorsVO } from '@/services/DataController';
import { getUserInfo } from '@/utils/utils';
import './index.less';
import dayjs from 'dayjs';
import debounce from 'lodash/debounce';
import { QuestionCircleOutlined } from '@ant-design/icons';

// 格式化百分比值的公共函数
const formatPercentage = (value: string | undefined) => {
    if (!value) return '0%';
    return value.includes('%') ? value : `${value}%`;
};

// Type guard to check if response is a PageInfo
function isPageInfo(response: any): response is PageInfo<StatsSaleIndicatorsVO> {
    return response &&
        typeof response === 'object' &&
        Array.isArray(response.list) &&
        typeof response.totalCount === 'number';
}

// Convert API response to appropriate format
function formatApiResponse(response: any): PageInfo<StatsSaleIndicatorsVO> {
    if (isPageInfo(response)) {
        return response;
    } else if (Array.isArray(response)) {
        return {
            list: response,
            totalCount: response.length
        };
    } else {
        return {
            list: [],
            totalCount: 0
        };
    }
}

// 定义筛选条件状态接口
interface FilterState {
    name: string;  // 企业名称
    parentId: number | null;
    selectedDate: dayjs.Dayjs | null;
    dateRange: [string, string] | null;
}

// FilterCard组件接口
interface FilterCardProps {
    filterState: FilterState;
    updateFilter: (key: keyof FilterState, value: any) => void;
    onFilter: () => void;
    loading: boolean;
    statsType: string;
    statsTypeLabel: string;
}

// FilterCard组件
const FilterCard = memo(({
    filterState,
    updateFilter,
    onFilter,
    loading,
    statsType,
    statsTypeLabel
}: FilterCardProps) => {
    // 使用本地状态来存储输入框的值，避免直接触发父组件状态更新导致的卡顿
    const [localName, setLocalName] = useState<string>(filterState.name);
    const [localParentId, setLocalParentId] = useState<string>(filterState.parentId !== null ? String(filterState.parentId) : '');

    // 创建名称输入防抖函数
    const debouncedNameChange = useCallback(
        debounce((value: string) => {
            updateFilter('name', value);
        }, 500),
        [updateFilter]
    );

    // 创建企业ID输入防抖函数
    const debouncedParentIdChange = useCallback(
        debounce((value: string) => {
            // 将字符串转换为数字或null
            const numValue = value === '' ? null : Number(value);
            updateFilter('parentId', numValue);
        }, 500),
        [updateFilter]
    );

    // 当filterState变化时，同步更新本地状态
    useEffect(() => {
        setLocalName(filterState.name);
        setLocalParentId(filterState.parentId !== null ? String(filterState.parentId) : '');
    }, [filterState]);

    // 创建日期选择防抖函数
    const debouncedDateChange = useCallback(
        debounce((date: dayjs.Dayjs | null, formatFunc: (date: dayjs.Dayjs) => [string, string] | null) => {
            updateFilter('selectedDate', date);
            if (date) {
                const dateRange = formatFunc(date);
                updateFilter('dateRange', dateRange);
            } else {
                updateFilter('selectedDate', null);
                updateFilter('dateRange', null);
            }
        }, 500),
        [updateFilter]
    );

    // 处理名称输入
    const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        // 立即更新本地状态，使输入框响应迅速
        setLocalName(value);
        // 防抖更新父组件状态
        debouncedNameChange(value);
    };

    // 处理企业ID输入
    const handleParentIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        // 验证输入是否为数字或空
        if (value === '' || /^\d+$/.test(value)) {
            // 立即更新本地状态
            setLocalParentId(value);
            // 防抖更新父组件状态
            debouncedParentIdChange(value);
        }
    };

    return (
        <Card className="sale-stats-filter-card">
            <Flex gap={16} wrap="wrap" align="flex-start">
                <Flex vertical gap={0} style={{ width: '100%' }}>
                    <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                        当前统计类型: <span style={{ color: '#1890ff' }}>{statsTypeLabel}</span>
                    </div>
                </Flex>

                <Flex vertical gap={8}>
                    <span>企业ID:</span>
                    <Input
                        placeholder="输入企业ID"
                        style={{ width: 150 }}
                        value={localParentId}
                        onChange={handleParentIdChange}
                        allowClear
                    />
                </Flex>

                <Flex vertical gap={8}>
                    <span>企业名称:</span>
                    <Input
                        placeholder="输入企业名称"
                        style={{ width: 150 }}
                        value={localName}
                        onChange={handleNameChange}
                        allowClear
                    />
                </Flex>

                <Flex vertical gap={8}>
                    {statsType !== 'TOTAL' && (
                        <>
                            <span>查询日期:</span>
                            {statsType === 'WEEKLY' && (
                                <DatePicker
                                    picker="week"
                                    placeholder="选择周"
                                    style={{ width: 150 }}
                                    value={filterState.selectedDate}
                                    onChange={(date) => {
                                        if (date) {
                                            debouncedDateChange(date, (selectedDate) => {
                                                const weekStart = selectedDate.startOf('week').format('YYYY-MM-DD');
                                                const weekEnd = selectedDate.endOf('week').format('YYYY-MM-DD');
                                                return [weekStart, weekEnd];
                                            });
                                        } else {
                                            // 当用户清空日期时
                                            debouncedDateChange(null, () => null as any);
                                        }
                                    }}
                                    allowClear
                                />
                            )}
                            {statsType === 'MONTHLY' && (
                                <DatePicker
                                    picker="month"
                                    placeholder="选择月"
                                    style={{ width: 150 }}
                                    value={filterState.selectedDate}
                                    onChange={(date) => {
                                        if (date) {
                                            debouncedDateChange(date, (selectedDate) => {
                                                const monthStart = selectedDate.startOf('month').format('YYYY-MM-DD');
                                                const monthEnd = selectedDate.endOf('month').format('YYYY-MM-DD');
                                                return [monthStart, monthEnd];
                                            });
                                        } else {
                                            // 当用户清空日期时
                                            debouncedDateChange(null, () => null as any);
                                        }
                                    }}
                                    allowClear
                                />
                            )}
                            {statsType === 'QUARTERLY' && (
                                <DatePicker
                                    picker="quarter"
                                    placeholder="选择季度"
                                    style={{ width: 150 }}
                                    value={filterState.selectedDate}
                                    onChange={(date) => {
                                        if (date) {
                                            debouncedDateChange(date, (selectedDate) => {
                                                const quarter = Math.floor(selectedDate.month() / 3);
                                                const quarterStart = dayjs(selectedDate).month(quarter * 3).startOf('month').format('YYYY-MM-DD');
                                                const quarterEnd = dayjs(selectedDate).month(quarter * 3 + 2).endOf('month').format('YYYY-MM-DD');
                                                return [quarterStart, quarterEnd];
                                            });
                                        } else {
                                            // 当用户清空日期时
                                            debouncedDateChange(null, () => null as any);
                                        }
                                    }}
                                    allowClear
                                />
                            )}
                        </>
                    )}
                </Flex>

                <Flex style={{ alignSelf: 'flex-end' }}>
                    <Button
                        type="primary"
                        onClick={onFilter}
                        loading={loading}
                    >
                        筛选
                    </Button>
                </Flex>
            </Flex>
        </Card>
    );
});

// 扩展StatsSaleIndicatorsVO类型
interface ExtendedStatsSaleIndicatorsVO extends StatsSaleIndicatorsVO {
    // 兼容字段
    key?: string | number;
}

interface PageResponse<T> {
    list: T[];
    hasNextPage: boolean;
    size: number;
    totalCount: number;
}

// 统一数据视图组件
/**
 * 统一数据视图组件
 * 筛选逻辑：
 * 1. 企业ID(parentId): 只筛选一级菜单(企业列表)
 * 2. 人员ID(userId): 只筛选二级菜单(销售人员列表)
 * 3. 名称(name): 同时筛选一级和二级菜单
 * 4. 日期(statsDate): 同时筛选一级和二级菜单
 */
const StatsDataView: React.FC<{
    data: PageResponse<ExtendedStatsSaleIndicatorsVO>;
    loading: boolean;
    fetchData: (page?: number, size?: number, parentId?: number) => void;
    activeTab: string;
    filterState: FilterState;
}> = ({ data, loading, fetchData, activeTab, filterState }) => {
    const [pageSize, setPageSize] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);
    const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);
    const [subDataLoading, setSubDataLoading] = useState<Record<string, boolean>>({});
    const [salesPersonData, setSalesPersonData] = useState<Record<string, ExtendedStatsSaleIndicatorsVO[]>>({});
    const [subTablePagination, setSubTablePagination] = useState<Record<string, {
        current: number;
        pageSize: number;
        total: number;
    }>>({});

    // 表格列配置
    const getStatsColumns = (isSubTable: boolean = false, currentActiveTab: string = activeTab) => {
        const baseColumns: any[] = [
            {
                title: 'ID',
                dataIndex: 'userId',
                key: 'userId',
                width: 60,
                align: 'center' as const
            },
            {
                title: '昵称',
                dataIndex: 'name',
                key: 'name',
                width: 120,
                render: (text: string, record: StatsSaleIndicatorsVO) => (
                    <span>{text || '未知'}</span>
                )
            },
            {
                title: '服装体验量',
                dataIndex: 'clothesExpCount',
                key: 'clothesExpCount',
                width: 100,
                align: 'center' as const,
                render: (text: number) => <span className="exp-count">{text || 0}</span>
            },
            {
                title: (
                    <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                        客户转换量
                        <Tooltip title="新签付费3999以上的客户">
                            <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
                        </Tooltip>
                    </div>
                ),
                dataIndex: 'customerConversionCount',
                key: 'customerConversionCount',
                width: 120,
                align: 'center' as const,
                render: (text: number) => <span className="conversion-count">{text || 0}</span>
            },
            {
                title: '消耗点数',
                dataIndex: 'customerConsumptionPoints',
                key: 'customerConsumptionPoints',
                width: 90,
                align: 'center' as const,
                render: (text: number) => <span className="points-count">{text ? (text / 1000).toFixed(2) : 0}</span>
            },
            {
                title: (
                    <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                        活跃客户率
                        <Tooltip title="周期内有过出图/上传服装/消耗点数的客户即为活跃用户">
                            <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
                        </Tooltip>
                    </div>
                ),
                dataIndex: 'customerActivityRate',
                key: 'customerActivityRate',
                width: 110,
                align: 'center' as const,
                render: (text: string) => <span className="activity-rate">{formatPercentage(text)}</span>
            },
            {
                title: '客户复购率',
                dataIndex: 'customerRepurchaseRate',
                key: 'customerRepurchaseRate',
                width: 110,
                align: 'center' as const,
                render: (text: string) => <span className="repurchase-rate">{formatPercentage(text)}</span>
            },
            {
                title: (
                    <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                        模特定制率
                        <Tooltip title="定制模特的客户占所有名下客户的比例">
                            <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
                        </Tooltip>
                    </div>
                ),
                dataIndex: 'customModelCustomers',
                key: 'customModelCustomers',
                width: 120,
                align: 'center' as const,
                render: (text: string) => {
                    return (<span className="custom-model">{formatPercentage(text)}</span>);
                }
            },
            {
                title: (
                    <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                        场景定制率
                        <Tooltip title="定制场景的客户占所有名下客户的比例">
                            <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
                        </Tooltip>
                    </div>
                ),
                dataIndex: 'customSceneCustomers',
                key: 'customSceneCustomers',
                width: 120,
                align: 'center' as const,
                render: (text: string) => {
                    return (<span className="custom-scene">{formatPercentage(text)}</span>);
                }
            },
            {
                title: (
                    <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                        待保护客户
                        <Tooltip title="库内大于60天的的客户数量">
                            <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
                        </Tooltip>
                    </div>
                ),
                dataIndex: 'customerProtectionMetrics',
                key: 'customerProtectionMetrics',
                width: 120,
                align: 'center' as const,
                render: (text: number) => <span className="protection-metrics">{text || 0}</span>
            },
            {
                title: '出图数量',
                dataIndex: 'createCount',
                key: 'createCount',
                width: 100,
                align: 'center' as const,
                render: (text: number) => <span className="create-count">{text || 0}</span>
            },
            {
                title: '统计日期',
                dataIndex: 'statsDate',
                key: 'statsDate',
                width: 110,
                render: (text: string, record: StatsSaleIndicatorsVO) => {
                    if (!text) return '-';

                    try {
                        const startDate = dayjs(text);

                        if (currentActiveTab === 'WEEKLY') {
                            // 对于周统计，显示周一到周日的日期范围
                            const endDate = startDate.add(6, 'day');
                            return (
                                <div style={{ whiteSpace: 'nowrap' }}>
                                    <div>{startDate.format('YYYY-MM-DD')} / </div>
                                    <div>{endDate.format('YYYY-MM-DD')}</div>
                                </div>
                            );
                        } else if (currentActiveTab === 'MONTHLY') {
                            // 对于月统计，显示月初到月末的日期范围
                            const endDate = startDate.endOf('month');
                            return (
                                <div style={{ whiteSpace: 'nowrap' }}>
                                    <div>{startDate.format('YYYY-MM-DD')} / {endDate.format('DD')}</div>
                                </div>
                            );
                        } else if (currentActiveTab === 'QUARTERLY') {
                            // 对于季度统计，显示季度开始到季度结束的日期范围
                            const quarter = Math.floor((startDate.month()) / 3);
                            const quarterStart = dayjs(startDate).month(quarter * 3).startOf('month');
                            const quarterEnd = dayjs(startDate).month(quarter * 3 + 2).endOf('month');
                            return (
                                <div style={{ whiteSpace: 'nowrap' }}>
                                    <div>{quarterStart.format('YYYY-MM-DD')} / </div>
                                    <div>{quarterEnd.format('YYYY-MM-DD')}</div>
                                </div>
                            );
                        } else if (currentActiveTab === 'TOTAL') {
                            // 对于总统计，显示"全部"
                            return <Tag color="blue">全部数据</Tag>;
                        } else {
                            // 日统计和其他类型直接显示日期
                            return startDate.format('YYYY-MM-DD');
                        }
                    } catch (e) {
                        console.error('日期格式化错误:', e);
                        return text || '-';
                    }
                }
            }
        ];

        // 只有主表格才添加操作列
        if (!isSubTable) {
            baseColumns.push({
                title: '操作',
                dataIndex: 'operation',
                key: 'operation',
                width: 80,
                align: 'center' as const,
                render: (_: any, record: ExtendedStatsSaleIndicatorsVO) => {
                    // 只有企业记录才显示展开按钮
                    if (!record.parentId || record.parentId === 0) {
                        const expanded = expandedRowKeys.includes(record.id as number);
                        const loading = subDataLoading[`${record.userId}`];
                        return (
                            <Button
                                type="link"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleExpand(!expanded, record);
                                }}
                                loading={loading}
                            >
                                {expanded ? '收起' : '展开'}
                            </Button>
                        );
                    }
                    return <></>;
                }
            });
        }

        return baseColumns;
    };

    // 处理分页变化
    const handlePageChange = (page: number, size?: number) => {
        setCurrentPage(page);
        if (size) {
            setPageSize(size);
        }
        fetchData(page, size || pageSize);
    };

    // 处理展开/收起
    const handleExpand = (expanded: boolean, record: ExtendedStatsSaleIndicatorsVO) => {
        if (!record.parentId || record.parentId === 0) {
            if (expanded) {
                setExpandedRowKeys([record.id as number]);
                // 展开时，始终传递当前行的statsDate，确保二级表格加载正确的数据
                loadSalesPersonData(record.userId as number, record.statsDate, 1, 10);
                console.log('展开企业行:', record.name, '企业ID:', record.userId);
            } else {
                setExpandedRowKeys([]);
            }
        }
    };

    // 加载人员数据
    /**
     * 加载下属销售人员数据
     * @param companyId 企业ID，作为parentId查询其下属人员
     * @param statsDate 统计日期
     * @param page 页码
     * @param size 每页数量
     */
    const loadSalesPersonData = async (companyId: number, statsDate?: string, page: number = 1, size: number = 10) => {
        const key = `${companyId}`;

        // 只在首次加载或强制刷新时重置数据
        if (page === 1) {
            setSalesPersonData(prev => ({
                ...prev,
                [key]: []
            }));
        }

        setSubDataLoading(prev => ({ ...prev, [key]: true }));

        try {
            // 基础查询参数
            const params: any = {
                pageNum: page,
                pageSize: size,
                parentId: companyId,  // 这里指定了parentId为当前展开的企业ID
                statsType: activeTab
            };

            // 企业名称筛选 - 适用于二级菜单
            if (filterState.name) {
                params.name = filterState.name;
            }

            // 添加统计日期作为查询条件
            if (statsDate) {
                params.statsDate = statsDate;
            } else if (filterState.selectedDate) {
                let dateToUse;
                if (activeTab === 'WEEKLY') {
                    dateToUse = filterState.selectedDate.startOf('week');
                } else if (activeTab === 'MONTHLY') {
                    dateToUse = filterState.selectedDate.startOf('month');
                } else if (activeTab === 'QUARTERLY') {
                    const quarter = Math.floor(filterState.selectedDate.month() / 3);
                    dateToUse = filterState.selectedDate.month(quarter * 3).startOf('month');
                } else {
                    // TOTAL和其他类型直接使用选择的日期
                    dateToUse = filterState.selectedDate;
                }
                params.statsDate = dateToUse.format('YYYY-MM-DD');
            }

            // 添加查询日志
            console.log('二级菜单查询参数:', params);

            const response = await statsSaleIndicators(params);
            const result = formatApiResponse(response);

            if (result && result.list && result.list.length > 0) {
                setSalesPersonData(prev => ({
                    ...prev,
                    [key]: result.list.map(item => ({
                        ...item,
                        key: `${item.id}`
                    }))
                }));

                // 更新子表格分页信息
                setSubTablePagination(prev => ({
                    ...prev,
                    [key]: {
                        current: page,
                        pageSize: size,
                        total: result.totalCount || 0
                    }
                }));
            } else {
                setSalesPersonData(prev => ({
                    ...prev,
                    [key]: []
                }));

                // 更新子表格分页信息（空数据情况）
                setSubTablePagination(prev => ({
                    ...prev,
                    [key]: {
                        current: 1,
                        pageSize: size,
                        total: 0
                    }
                }));
            }
        } catch (error) {
            console.error('获取人员数据失败:', error);
            message.error('获取人员数据失败');
        } finally {
            setSubDataLoading(prev => ({ ...prev, [key]: false }));
        }
    };

    // 处理子表格分页变化
    const handleSubTablePageChange = (companyId: number, page: number, pageSize: number, statsDate?: string) => {
        loadSalesPersonData(companyId, statsDate, page, pageSize);
    };

    // 渲染展开行
    const expandedRowRender = (record: ExtendedStatsSaleIndicatorsVO) => {
        if (record.parentId !== 0) {
            return null;
        }

        const key = `${record.userId}`;
        const paginationInfo = subTablePagination[key] || { current: 1, pageSize: 10, total: 0 };

        return (
            <div className="nested-table" style={{ paddingTop: 0 }}>
                <div className="nested-table-title">
                    下属人员数据 ({paginationInfo.total || 0}人)
                    {subDataLoading[key] && ' (加载中...)'}
                </div>

                <div className="nested-table-content">
                    {subDataLoading[key] && salesPersonData[key]?.length === 0 ? (
                        <div style={{ padding: '20px 0', textAlign: 'center' }}>
                            <Spin tip="正在加载人员数据..." />
                        </div>
                    ) : salesPersonData[key]?.length > 0 ? (
                        <>
                            <Table
                                dataSource={salesPersonData[key]}
                                rowKey="id"
                                pagination={false}
                                size="small"
                                scroll={{ x: 1200 }}
                                columns={getStatsColumns(true, activeTab)}
                            />
                            {paginationInfo.total > 0 && (
                                <div style={{ textAlign: 'right', marginTop: 16 }}>
                                    <Pagination
                                        current={paginationInfo.current}
                                        pageSize={paginationInfo.pageSize}
                                        total={paginationInfo.total}
                                        onChange={(page, size) =>
                                            handleSubTablePageChange(record.userId as number, page, size || paginationInfo.pageSize, record.statsDate)
                                        }
                                        showSizeChanger
                                        showQuickJumper
                                        showTotal={(total) => `共 ${total} 条`}
                                        size="small"
                                    />
                                </div>
                            )}
                        </>
                    ) : (
                        <Empty description="暂无人员数据" />
                    )}
                </div>
            </div>
        );
    };

    return (
        <div style={{ padding: '16px' }}>
            {loading ? (
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    minHeight: '200px'
                }}>
                    <Spin size="large" />
                </div>
            ) : data?.list?.length === 0 ? (
                <Empty description="暂无数据" />
            ) : (
                <>
                    <Table
                        dataSource={data.list.map(item => ({
                            ...item,
                            key: `${item.id}`
                        }))}
                        rowKey="id"
                        pagination={false}
                        expandable={{
                            expandedRowKeys,
                            onExpand: (expanded, record) => handleExpand(expanded, record),
                            expandedRowRender,
                            expandRowByClick: false,
                            showExpandColumn: false // 隐藏默认的展开列
                        }}
                        columns={getStatsColumns(false, activeTab)}
                        scroll={{ x: 1500 }}
                    />

                    {data.totalCount > 0 && (
                        <div style={{ textAlign: 'right', marginTop: 16 }}>
                            <Pagination
                                current={currentPage}
                                pageSize={pageSize}
                                total={data.totalCount}
                                onChange={handlePageChange}
                                showSizeChanger
                                showQuickJumper
                                showTotal={(total) => `共 ${total} 条`}
                            />
                        </div>
                    )}
                </>
            )}
        </div>
    );
};

// 主内容区组件
const SaleStatsSection: React.FC = () => {
    const [activeTab, setActiveTab] = useState<string>('WEEKLY');
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [data, setData] = useState<PageResponse<ExtendedStatsSaleIndicatorsVO>>({
        list: [],
        hasNextPage: false,
        size: 10,
        totalCount: 0
    });
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [isInitialized, setIsInitialized] = useState<boolean>(false);

    // 创建初始筛选状态，根据统计类型设置不同的默认日期
    const createInitialFilterState = useCallback((statsType?: string): FilterState => {
        const type = statsType || activeTab;
        let defaultDate: dayjs.Dayjs;
        let dateRange: [string, string] | null;

        if (type === 'WEEKLY') {
            // 上一周
            defaultDate = dayjs().subtract(1, 'week');
            const weekStart = defaultDate.startOf('week');
            const weekEnd = defaultDate.endOf('week');
            dateRange = [weekStart.format('YYYY-MM-DD'), weekEnd.format('YYYY-MM-DD')];
        } else if (type === 'MONTHLY') {
            // 上个月
            defaultDate = dayjs().subtract(1, 'month');
            const monthStart = defaultDate.startOf('month');
            const monthEnd = defaultDate.endOf('month');
            dateRange = [monthStart.format('YYYY-MM-DD'), monthEnd.format('YYYY-MM-DD')];
        } else if (type === 'QUARTERLY') {
            // 上个季度
            const currentMonth = dayjs().month();
            const currentQuarter = Math.floor(currentMonth / 3);
            const previousQuarter = currentQuarter - 1 < 0 ? 3 : currentQuarter - 1;
            const previousQuarterYear = currentQuarter - 1 < 0 ? dayjs().year() - 1 : dayjs().year();

            // 创建上一季度的日期
            defaultDate = dayjs().year(previousQuarterYear).month(previousQuarter * 3 + 1);

            const quarterStart = defaultDate.month(previousQuarter * 3).startOf('month');
            const quarterEnd = defaultDate.month(previousQuarter * 3 + 2).endOf('month');
            dateRange = [quarterStart.format('YYYY-MM-DD'), quarterEnd.format('YYYY-MM-DD')];
        } else if (type === 'TOTAL') {
            // 总统计查询昨天的数据
            defaultDate = dayjs().subtract(1, 'day');
            dateRange = [defaultDate.format('YYYY-MM-DD'), defaultDate.format('YYYY-MM-DD')];
        } else {
            // 昨天（日统计）
            defaultDate = dayjs().subtract(1, 'day');
            dateRange = [defaultDate.format('YYYY-MM-DD'), defaultDate.format('YYYY-MM-DD')];
        }

        return {
            name: '',
            parentId: null,
            selectedDate: defaultDate,
            dateRange: dateRange
        };
    }, [activeTab]);

    // 筛选条件状态
    const [filterState, setFilterState] = useState<FilterState>(() => createInitialFilterState());

    // 部分更新筛选条件的辅助函数
    const updateFilter = useCallback((key: keyof FilterState, value: any) => {
        setFilterState(prev => ({ ...prev, [key]: value }));
    }, []);

    // 数据加载函数
    /**
     * 数据加载函数
     * 筛选逻辑处理：
     * 1. 当parentId传入时，表示查询企业下属人员数据
     * 2. 当设置了filterState.parentId时，表示查询特定企业ID的数据
     * 3. 默认查询所有顶级企业数据(parentId=0)
     */
    const fetchData = useCallback(async (page?: number, size?: number, parentId?: number) => {
        const pageToUse = page || currentPage;
        const sizeToUse = size || pageSize;

        setIsLoading(true);
        try {
            // 基础查询参数
            const params: any = {
                pageNum: pageToUse,
                pageSize: sizeToUse,
                statsType: activeTab
            };

            // 处理企业ID筛选逻辑
            if (parentId !== undefined) {
                // 场景1: 指定parentId - 查询某企业下的销售人员
                params.parentId = parentId;
            } else if (filterState.parentId !== null) {
                // 场景2: 通过输入框指定企业ID - 查询该企业的数据
                // 这里设置userId表示我们要查询的是该ID对应的企业
                params.userId = filterState.parentId;
                // parentId=0确保只查询顶级企业数据
                params.parentId = 0;
            } else {
                // 场景3: 默认情况 - 查询所有顶级企业
                params.parentId = 0;
            }

            // 企业名称筛选 - 适用于所有层级
            if (filterState.name) {
                params.name = filterState.name;
            }

            // 日期参数处理 - 适用于所有层级
            if (filterState.selectedDate) {
                let dateToUse;
                if (activeTab === 'WEEKLY') {
                    dateToUse = filterState.selectedDate.startOf('week');
                } else if (activeTab === 'MONTHLY') {
                    dateToUse = filterState.selectedDate.startOf('month');
                } else if (activeTab === 'QUARTERLY') {
                    const quarter = Math.floor(filterState.selectedDate.month() / 3);
                    dateToUse = filterState.selectedDate.month(quarter * 3).startOf('month');
                } else {
                    // TOTAL和其他类型直接使用选择的日期
                    dateToUse = filterState.selectedDate;
                }
                params.statsDate = dateToUse.format('YYYY-MM-DD');
            }

            const response = await statsSaleIndicators(params);
            const result = formatApiResponse(response);

            if (result) {
                setData({
                    list: result.list || [],
                    hasNextPage: true,
                    size: sizeToUse,
                    totalCount: result.totalCount || 0
                });

                if (pageToUse !== currentPage) {
                    setCurrentPage(pageToUse);
                }
                if (sizeToUse !== pageSize) {
                    setPageSize(sizeToUse);
                }
            }
        } catch (error) {
            console.error('获取数据失败:', error);
            message.error('获取数据失败，请重试');
        } finally {
            setIsLoading(false);
        }
    }, [activeTab, currentPage, pageSize, filterState]);

    // 首次加载和统计类型变化时加载数据
    useEffect(() => {
        // 只在组件第一次挂载时设置初始日期，避免在清空后重新设置
        if (!isInitialized) {
            setIsInitialized(true);
            // 根据当前activeTab设置默认日期
            setFilterState(createInitialFilterState(activeTab));
        }

        fetchData();
    }, [fetchData, activeTab, isInitialized, createInitialFilterState]);

    // 获取对应的统计类型标签
    const getStatsTypeLabel = (type: string) => {
        switch (type) {
            case 'WEEKLY':
                return '周统计';
            case 'MONTHLY':
                return '月统计';
            case 'QUARTERLY':
                return '季度统计';
            default:
                return '未知';
        }
    };

    return (
        <div className="sale-stats-container">
            {/* 统计类型选择卡片 */}
            <Card className="sale-stats-selection-card" style={{ marginBottom: '16px' }}>
                <Flex align="center" gap={24}>
                    <Flex vertical gap={8}>
                        <span style={{ fontWeight: 'bold' }}>统计周期:</span>
                        <Radio.Group 
                            value={activeTab} 
                            onChange={(e) => {
                                setActiveTab(e.target.value);
                                // 切换统计类型时，设置对应的默认日期（上一周/上个月/上个季度）
                                setFilterState(createInitialFilterState(e.target.value));
                                setCurrentPage(1);
                            }}
                            buttonStyle="solid"
                        >
                            <Radio.Button value="WEEKLY">周统计</Radio.Button>
                            <Radio.Button value="MONTHLY">月统计</Radio.Button>
                            <Radio.Button value="QUARTERLY">季度统计</Radio.Button>
                        </Radio.Group>
                    </Flex>
                </Flex>
            </Card>

            <FilterCard
                filterState={filterState}
                updateFilter={updateFilter}
                onFilter={() => fetchData()}
                loading={isLoading}
                statsType={activeTab}
                statsTypeLabel={getStatsTypeLabel(activeTab)}
            />

            <StatsDataView
                data={data}
                loading={isLoading}
                fetchData={fetchData}
                activeTab={activeTab}
                filterState={filterState}
            />
        </div>
    );
};

// 主组件
const StatsSaleIndicators: React.FC = () => {
    // 检查用户是否为管理员
    const userInfo = getUserInfo();
    const isAdmin = userInfo && userInfo.roleType === 'ADMIN';

    // 如果不是管理员，显示无权限信息
    if (!isAdmin) {
        return (
            <PageContainer>
                <Card>
                    <div style={{ textAlign: 'center', padding: '50px 0' }}>
                        您没有访问此页面的权限
                    </div>
                </Card>
            </PageContainer>
        );
    }

    return (
        <PageContainer title="">
            <Card>
                <SaleStatsSection />
            </Card>
        </PageContainer>
    );
};

export default StatsSaleIndicators;
