.clothes-stats-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 8px 8px 0 8px;
}

.clothes-stats-summary-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  height: auto;
}

.clothes-stats-summary-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: white;
  border-radius: 8px;
  padding: 0 16px;
  height: 508.85px;
}

.clothes-stats-detail-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  min-height: 60vh;
}

.clothes-stats-filter-card {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 4px 16px;
  margin-bottom: 0;
  .ant-card-body {
    padding: 16px 24px !important;
  }
}

.clothes-stats-detail-expanded {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 0 16px;
}

// 指标颜色样式
.clothes-stats-metric-usage {
  color: #00000073;
  text-shadow: 
    0 0 2px #fff,
    0 0 4px #fff,
    0 0 6px #F6C3CD,
    0 0 10px #F6C3CD,
    0 0 15px #F6C3CD;
  font-weight: 600;
  padding: 2px 6px;
}

.clothes-stats-metric-revenue {
  color: #00000073;
  text-shadow:
    0 0 2px #fff,
    0 0 4px #fff,
    0 0 6px #DBE8B3,
    0 0 10px #DBE8B3,
    0 0 15px #DBE8B3;
  font-weight: 600;
  padding: 2px 6px;
}

.clothes-stats-metric-users {
  color: #00000073;
  text-shadow: 
    0 0 2px #fff,
    0 0 4px #fff,
    0 0 6px #B4E1C2,
    0 0 10px #B4E1C2,
    0 0 15px #B4E1C2;
  font-weight: 600;
  padding: 2px 6px;
}

.clothes-stats-metric-rating {
  color: #00000073;
  text-shadow:
    0 0 2px #fff,
    0 0 4px #fff,
    0 0 6px #F4C3B8,
    0 0 10px #F4C3B8,
    0 0 15px #F4C3B8;
  font-weight: 600;
  padding: 2px 6px;
}

.clothes-stats-upper-tabs .ant-tabs-tab {
  min-width: 80px;
  justify-content: center;
  font-size: 16px;
}

.clothes-stats-detail-tabs .ant-tabs-tab {
  font-size: 14px;
}

.warning-stats-container {
  .warning-stats-selection-card {
    margin-bottom: 16px;
  }

  .warning-stats-filter-card {
    margin-bottom: 16px;
  }

  .warning-stats-metric {
    font-weight: 500;
    &.positive {
      color: #52c41a;
    }
    &.negative {
      color: #f5222d;
    }
  }

  .warning-stats-detail-section {
    margin-top: 16px;
  }

  .ant-table-tbody > tr.ant-table-row:hover > td {
    background-color: rgba(24, 144, 255, 0.05);
  }

  .ant-table-row-expand-icon {
    color: #1890ff;
  }

  .stats-main-table {
    .ant-table-tbody > tr.company-row {
      background-color: #fafafa;
    }
  }
}
