import React, { useEffect, useState, useCallback, memo, useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
    Card, DatePicker, Table, Pagination,
    Button, Flex, message, Tooltip, Empty, Spin, Radio, Modal, Tabs, Tag
} from 'antd';
import { PageInfo, statsWarningInfo, StatsWarningInfoVO, getWarningInfo } from '@/services/DataController';
import { getUserInfo } from '@/utils/utils';
import '@/pages/Operate/Stats/StatsWarningInfo/index.less';
import dayjs from 'dayjs';
import debounce from 'lodash/debounce';
import { QuestionCircleOutlined } from '@ant-design/icons';

// Type guard to check if response is a PageInfo
function isPageInfo(response: any): response is PageInfo<StatsWarningInfoVO> {
    return response &&
        typeof response === 'object' &&
        Array.isArray(response.list) &&
        typeof response.totalCount === 'number';
}

// Convert API response to appropriate format
function formatApiResponse(response: any): PageInfo<StatsWarningInfoVO> {
    let result: PageInfo<StatsWarningInfoVO>;

    if (isPageInfo(response)) {
        result = response;
    } else if (Array.isArray(response)) {
        result = {
            list: response,
            totalCount: response.length
        };
    } else {
        result = {
            list: [],
            totalCount: 0
        };
    }

    return result;
}

// 定义筛选条件状态接口
interface FilterState {
    selectedDate: dayjs.Dayjs | null;
    dateRange: [string, string] | null;
}

// FilterCard组件接口
interface FilterCardProps {
    filterState: FilterState;
    updateFilter: (key: keyof FilterState, value: any) => void;
    onReset: () => void;
    onFilter: () => void;
    loading: boolean;
    statsType: string;
    statsTypeLabel: string;
}

// FilterCard组件
const FilterCard = memo(({
    filterState,
    updateFilter,
    onReset,
    onFilter,
    loading,
    statsType,
    statsTypeLabel,
}: FilterCardProps) => {
    // 创建日期选择防抖函数
    const debouncedDateChange = useCallback(
        debounce((date: dayjs.Dayjs | null, formatFunc: (date: dayjs.Dayjs) => [string, string] | null) => {
            updateFilter('selectedDate', date);
            if (date) {
                const dateRange = formatFunc(date);
                updateFilter('dateRange', dateRange);
            } else {
                updateFilter('selectedDate', null);
                updateFilter('dateRange', null);
            }
        }, 500),
        [updateFilter]
    );

    return (
        <Card className="warning-stats-filter-card">
            <Flex gap={16} wrap="wrap" align="flex-start">
                <Flex vertical gap={0} style={{ width: '100%' }}>
                    <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                        当前统计类型: <span style={{ color: '#1890ff' }}>{statsTypeLabel}</span>
                    </div>
                </Flex>

                <Flex vertical gap={8}>
                    <span>查询日期:</span>
                    {statsType === 'WEEKLY' && (
                        <DatePicker
                            picker="week"
                            placeholder="选择周"
                            style={{ width: 150 }}
                            value={filterState.selectedDate}
                            onChange={(date) => {
                                if (date) {
                                    debouncedDateChange(date, (selectedDate) => {
                                        const weekStart = selectedDate.startOf('week').format('YYYY-MM-DD');
                                        const weekEnd = selectedDate.endOf('week').format('YYYY-MM-DD');
                                        return [weekStart, weekEnd];
                                    });
                                } else {
                                    debouncedDateChange(null, () => null as any);
                                }
                            }}
                            allowClear
                        />
                    )}
                    {statsType === 'MONTHLY' && (
                        <DatePicker
                            picker="month"
                            placeholder="选择月"
                            style={{ width: 150 }}
                            value={filterState.selectedDate}
                            onChange={(date) => {
                                if (date) {
                                    debouncedDateChange(date, (selectedDate) => {
                                        const monthStart = selectedDate.startOf('month').format('YYYY-MM-DD');
                                        const monthEnd = selectedDate.endOf('month').format('YYYY-MM-DD');
                                        return [monthStart, monthEnd];
                                    });
                                } else {
                                    debouncedDateChange(null, () => null as any);
                                }
                            }}
                            allowClear
                        />
                    )}
                </Flex>

                <Flex style={{ alignSelf: 'flex-end' }}>
                    <Button
                        type="primary"
                        onClick={onFilter}
                        loading={loading}
                    >
                        筛选
                    </Button>
                </Flex>
            </Flex>
        </Card>
    );
});

interface PageResponse<T> {
    list: T[];
    hasNextPage: boolean;
    size: number;
    totalCount: number;
}

// 工具提示渲染函数
const renderTooltip = (title: string, content: React.ReactNode) => (
    <Tooltip title={title}>
        <div style={{ display: 'inline-flex', alignItems: 'center' }}>
            {content}
            <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
        </div>
    </Tooltip>
);

// 百分比格式化函数
const formatPercentage = (value: string | undefined) => {
    if (!value) return '0.0%';
    const numValue = parseFloat(value);
    return `${numValue.toFixed(1)}%`;
};

// DataTable组件接口
interface DataTableProps {
    data: PageResponse<StatsWarningInfoVO>;
    loading: boolean;
    totalRecords: number;
    currentPage: number;
    pageSize: number;
    sortField: string;
    sortOrder?: 'ascend' | 'descend';
    onPageChange: (page: number, size?: number) => void;
    onTableChange: (pagination: any, filters: any, sorter: any) => void;
    statsType: string;
    fetchDetailData: (page?: number, size?: number) => void;
}

// DataTable组件
const DataTable = memo(({
    data,
    loading,
    totalRecords,
    currentPage,
    pageSize,
    sortField,
    sortOrder,
    onPageChange,
    onTableChange,
    statsType,
    fetchDetailData
}: DataTableProps) => {
    // 添加展开功能状态
    const [expandedRecord, setExpandedRecord] = useState<StatsWarningInfoVO | null>(null);
    const [detailVisible, setDetailVisible] = useState(false);
    const [detailLoading, setDetailLoading] = useState(false);

    // 处理展开逻辑
    const handleExpand = async (record: StatsWarningInfoVO) => {
        try {
            setDetailLoading(true);
            setDetailVisible(true);
            
            // 调用API获取详细信息
            const detailInfo = await getWarningInfo(record.id as number);
            if (detailInfo) {
                setExpandedRecord(detailInfo);
            }
            
        } catch (error) {
            console.error('获取预警详情失败:', error);
            message.error('获取预警详情失败，请重试');
        } finally {
            setDetailLoading(false);
        }
    };

    // 关闭弹窗
    const handleClose = () => {
        setDetailVisible(false);
        setExpandedRecord(null);
    };
    
    // 格式化日期显示
    const formatStatsDate = (statsDate: string | undefined, type: string) => {
        if (!statsDate) return '-';

        try {
            const startDate = dayjs(statsDate);

            if (type === 'WEEKLY') {
                // 对于周统计，显示周一到周日的日期范围
                const endDate = startDate.add(6, 'day');
                return (
                    <div style={{ whiteSpace: 'nowrap' }}>
                        <div>{startDate.format('YYYY-MM-DD')} / </div>
                        <div>{endDate.format('YYYY-MM-DD')}</div>
                    </div>
                );
            } else if (type === 'MONTHLY') {
                // 对于月统计，显示月初到月末的日期范围
                const endDate = startDate.endOf('month');
                return (
                    <div style={{ whiteSpace: 'nowrap' }}>
                        <div>{startDate.format('YYYY-MM-DD')} / {endDate.format('DD')}</div>
                    </div>
                );
            } else {
                // 其他情况直接显示日期
                return startDate.format('YYYY-MM-DD');
            }
        } catch (e) {
            console.error('日期格式化错误:', e);
            return statsDate || '-';
        }
    };

    // 为弹窗专门创建的日期格式化函数
    const formatStatsDateForModal = (statsDate: string | undefined, type: string) => {
        if (!statsDate) return '-';

        try {
            const startDate = dayjs(statsDate);

            if (type === 'WEEKLY') {
                // 对于周统计，显示周一到周日的日期范围
                const endDate = startDate.add(6, 'day');
                return `${startDate.format('YYYY-MM-DD')} ～ ${endDate.format('YYYY-MM-DD')}`;
            } else if (type === 'MONTHLY') {
                // 对于月统计，显示月初到月末的日期范围
                const endDate = startDate.endOf('month');
                return `${startDate.format('YYYY-MM-DD')} ～ ${endDate.format('YYYY-MM-DD')}`;
            } else {
                // 其他情况直接显示日期
                return startDate.format('YYYY-MM-DD');
            }
        } catch (e) {
            console.error('日期格式化错误:', e);
            return statsDate || '-';
        }
    };

    // 检查数据是否为空
    const noData = !loading && (!data?.list || data.list.length === 0);

    // 使用useMemo根据statsType动态生成表格列
    const columns = useMemo(() => {
        // 基础列配置
        const baseColumns = [
            {
                title: 'ID',
                dataIndex: 'id',
                key: 'id',
                width: 60,
                align: 'center' as const
            },
            {
                title: (
                    <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                        退款率预警客户
                        <Tooltip title="退款率大于5%的客户数量">
                            <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
                        </Tooltip>
                    </div>
                ),
                dataIndex: 'customerRefundRateCount',
                key: 'customerRefundRateCount',
                width: 150,
                align: 'center' as const,
                render: (text) => <span className="warning-stats-metric" style={{ color: '#722ed1', fontWeight: 'bold' }}>{text || 0}</span>,
            },
            {
                title: (
                    <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                        交付超时
                        <Tooltip title="交付超过24小时的服装数量">
                            <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
                        </Tooltip>
                    </div>
                ),
                dataIndex: 'deliveryTimeoutCount',
                key: 'deliveryTimeoutCount',
                width: 130,
                align: 'center' as const,
                render: (text) => <span className="warning-stats-metric" style={{ color: '#eb2f96', fontWeight: 'bold' }}>{text || 0}</span>,
            },
            {
                title: (
                    <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                        余额预警
                        <Tooltip title="余额低于2000点或小于充值金额30%的客户数量">
                            <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
                        </Tooltip>
                    </div>
                ),
                dataIndex: 'customerBalanceAlertCount',
                key: 'customerBalanceAlertCount',
                width: 130,
                align: 'center' as const,
                render: (text) => <span className="warning-stats-metric" style={{ color: '#1890ff', fontWeight: 'bold' }}>{text || 0}</span>,
            },
            {
                title: (
                    <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                        长期未转化
                        <Tooltip title="入库超过60天未转化的客户数量">
                            <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
                        </Tooltip>
                    </div>
                ),
                dataIndex: 'customerNotConvertCount',
                key: 'customerNotConvertCount',
                width: 130,
                align: 'center' as const,
                render: (text) => <span className="warning-stats-metric" style={{ color: '#52c41a', fontWeight: 'bold' }}>{text || 0}</span>,
            },
            {
                title: '统计日期',
                dataIndex: 'statsDate',
                key: 'statsDate',
                width: 150,
                align: 'center' as const,
                render: (text) => formatStatsDate(text, statsType),
            },
            {
                title: '操作',
                key: 'action',
                width: 80,
                align: 'center' as const,
                render: (_, record) => (
                    <Button 
                        type="link" 
                        onClick={() => handleExpand(record)}
                    >
                        详情
                    </Button>
                ),
            }
        ];

        // 周统计特有的列
        const weeklyColumn = {
            title: (
                <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                    周内不消耗客户
                    <Tooltip title="周内不消耗客户比率">
                        <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
                    </Tooltip>
                </div>
            ),
            dataIndex: 'weeklyNoConsumptionRate',
            key: 'weeklyNoConsumptionRate',
            width: 160,
            align: 'center' as const,
            render: (text) => <span className="warning-stats-metric" style={{ color: '#f5222d', fontWeight: 'bold' }}>{formatPercentage(text)}</span>,
        };

        // 月统计特有的列
        const monthlyColumn = {
            title: (
                <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                    月内不消耗客户
                    <Tooltip title="月内不消耗客户比率">
                        <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
                    </Tooltip>
                </div>
            ),
            dataIndex: 'monthlyNoConsumptionRate',
            key: 'monthlyNoConsumptionRate',
            width: 160,
            align: 'center' as const,
            render: (text) => <span className="warning-stats-metric" style={{ color: '#fa8c16', fontWeight: 'bold' }}>{formatPercentage(text)}</span>,
        };

        // 根据统计类型，生成最终的列配置
        const finalColumns = [...baseColumns];

        // 在ID列后插入特定的列
        if (statsType === 'WEEKLY') {
            finalColumns.splice(1, 0, weeklyColumn);
        } else if (statsType === 'MONTHLY') {
            finalColumns.splice(1, 0, monthlyColumn);
        }

        return finalColumns;
    }, [statsType, formatPercentage]);

    // 渲染详情弹窗
    const renderDetailModal = () => {
        if (!expandedRecord) return null;
        
        return (
            <Modal
                title={
                    <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
                        预警详细信息 
                        <span style={{ fontSize: '14px', fontWeight: 'normal', marginLeft: '10px', color: '#666' }}>
                            ID: {expandedRecord.id}
                        </span>
                    </div>
                }
                open={detailVisible}
                onCancel={handleClose}
                width="80%"
                footer={null}
                centered
                destroyOnClose
                bodyStyle={{ maxHeight: 'calc(100vh - 200px)', overflow: 'auto', padding: '16px', backgroundColor: '#f5f5f5' }}
            >
                {detailLoading ? (
                    <div style={{ textAlign: 'center', padding: '50px 0' }}>
                        <Spin size="large" />
                    </div>
                ) : (
                    <div>
                        <Card 
                            title={
                                <div style={{ fontWeight: 'bold', fontSize: '16px', color: '#1890ff' }}>
                                    <span style={{ marginRight: '8px' }}>📊</span>
                                    基本信息
                                </div>
                            }
                            bordered={false}
                            style={{ marginBottom: '16px', boxShadow: '0 2px 8px rgba(0,0,0,0.09)' }}
                        >
                            <Flex wrap="wrap" gap="middle">
                                <div style={{ minWidth: '200px' }}>
                                    <div style={{ color: '#666', marginBottom: '4px' }}>统计类型</div>
                                    <div style={{ fontWeight: 'bold' }}>
                                        {expandedRecord.statsType === 'WEEKLY' ? '周统计' : 
                                         expandedRecord.statsType === 'MONTHLY' ? '月统计' : 
                                         expandedRecord.statsType === 'DAILY' ? '日统计' : expandedRecord.statsType}
                                    </div>
                                </div>
                                <div style={{ minWidth: '200px' }}>
                                    <div style={{ color: '#666', marginBottom: '4px' }}>统计日期</div>
                                    <div style={{ fontWeight: 'bold' }}>{formatStatsDateForModal(expandedRecord.statsDate, expandedRecord.statsType || 'WEEKLY')}</div>
                                </div>
                                <div style={{ minWidth: '200px' }}>
                                    <div style={{ color: '#666', marginBottom: '4px' }}>创建时间</div>
                                    <div>{expandedRecord.createTime ? dayjs(expandedRecord.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</div>
                                </div>
                            </Flex>
                        </Card>
                        
                        <Card 
                            title={
                                <div style={{ fontWeight: 'bold', fontSize: '16px', color: '#ff4d4f' }}>
                                    <span style={{ marginRight: '8px' }}>⚠️</span>
                                    预警指标
                                </div>
                            }
                            bordered={false}
                            style={{ marginBottom: '16px', boxShadow: '0 2px 8px rgba(0,0,0,0.09)' }}
                        >
                            <Flex wrap="wrap" gap="middle">
                                {expandedRecord.statsType === 'WEEKLY' && (
                                    <div style={{ 
                                        minWidth: '150px', 
                                        padding: '12px',
                                        backgroundColor: '#fff2f0',
                                        borderRadius: '8px',
                                        borderLeft: '4px solid #f5222d'
                                    }}>
                                        <div style={{ color: '#666', marginBottom: '4px', fontSize: '13px' }}>周内不消耗客户</div>
                                        <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#f5222d' }}>
                                            {formatPercentage(expandedRecord.weeklyNoConsumptionRate)}
                                        </div>
                                    </div>
                                )}
                                
                                {expandedRecord.statsType === 'MONTHLY' && (
                                    <div style={{ 
                                        minWidth: '150px', 
                                        padding: '12px',
                                        backgroundColor: '#fff7e6',
                                        borderRadius: '8px',
                                        borderLeft: '4px solid #fa8c16'
                                    }}>
                                        <div style={{ color: '#666', marginBottom: '4px', fontSize: '13px' }}>月内不消耗客户</div>
                                        <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#fa8c16' }}>
                                            {formatPercentage(expandedRecord.monthlyNoConsumptionRate)}
                                        </div>
                                    </div>
                                )}
                                
                                <div style={{ 
                                    minWidth: '150px', 
                                    padding: '12px',
                                    backgroundColor: '#f9f0ff',
                                    borderRadius: '8px',
                                    borderLeft: '4px solid #722ed1'
                                }}>
                                    <div style={{ color: '#666', marginBottom: '4px', fontSize: '13px' }}>退款率预警客户</div>
                                    <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#722ed1' }}>
                                        {expandedRecord.customerRefundRateCount || 0}
                                    </div>
                                </div>
                                
                                <div style={{ 
                                    minWidth: '150px', 
                                    padding: '12px',
                                    backgroundColor: '#fff0f6',
                                    borderRadius: '8px',
                                    borderLeft: '4px solid #eb2f96'
                                }}>
                                    <div style={{ color: '#666', marginBottom: '4px', fontSize: '13px' }}>交付超时</div>
                                    <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#eb2f96' }}>
                                        {expandedRecord.deliveryTimeoutCount || 0}
                                    </div>
                                </div>
                                
                                <div style={{ 
                                    minWidth: '150px', 
                                    padding: '12px',
                                    backgroundColor: '#e6f7ff',
                                    borderRadius: '8px',
                                    borderLeft: '4px solid #1890ff'
                                }}>
                                    <div style={{ color: '#666', marginBottom: '4px', fontSize: '13px' }}>余额预警</div>
                                    <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#1890ff' }}>
                                        {expandedRecord.customerBalanceAlertCount || 0}
                                    </div>
                                </div>
                                
                                <div style={{ 
                                    minWidth: '150px', 
                                    padding: '12px',
                                    backgroundColor: '#f6ffed',
                                    borderRadius: '8px',
                                    borderLeft: '4px solid #52c41a'
                                }}>
                                    <div style={{ color: '#666', marginBottom: '4px', fontSize: '13px' }}>长期未转化</div>
                                    <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#52c41a' }}>
                                        {expandedRecord.customerNotConvertCount || 0}
                                    </div>
                                </div>
                            </Flex>
                        </Card>
                        
                        <Card 
                            title={
                                <div style={{ fontWeight: 'bold', fontSize: '16px', color: '#1890ff' }}>
                                    <span style={{ marginRight: '8px' }}>👥</span>
                                    相关信息列表
                                </div>
                            }
                            bordered={false}
                            style={{ boxShadow: '0 2px 8px rgba(0,0,0,0.09)' }}
                        >
                            <Tabs 
                                defaultActiveKey="weekly" 
                                type="card"
                                tabBarStyle={{ marginBottom: '16px' }}
                            >
                                {expandedRecord.statsType === 'WEEKLY' && (
                                    <Tabs.TabPane 
                                        tab={
                                            <span>
                                                <span style={{ color: '#f5222d', marginRight: '4px' }}>●</span>
                                                周内未消费用户
                                                <span style={{ marginLeft: '4px', color: '#999', fontSize: '12px' }}>
                                                    ({expandedRecord.weeklyUserList?.length || 0})
                                                </span>
                                            </span>
                                        } 
                                        key="weekly"
                                    >
                                        <div style={{ height: '350px', overflow: 'auto' }}>
                                        {expandedRecord.weeklyUserList && expandedRecord.weeklyUserList.length > 0 ? (
                                            <Table
                                                dataSource={expandedRecord.weeklyUserList}
                                                rowKey="id"
                                                size="small"
                                                pagination={{ pageSize: 5, size: 'small', showSizeChanger: false }}
                                                columns={[
                                                    { title: 'ID', dataIndex: 'id', width: 60, align: 'center' as const },
                                                    { title: '用户名', dataIndex: 'nickName', width: 120, align: 'center' as const },
                                                    { title: '手机号', dataIndex: 'mobile', width: 120, align: 'center' as const }
                                                ]}
                                                style={{ backgroundColor: 'white', borderRadius: '4px' }}
                                            />
                                        ) : <Empty description="无周内未消费用户" style={{ height: '250px', display: 'flex', flexDirection: 'column', justifyContent: 'center' }} />}
                                        </div>
                                    </Tabs.TabPane>
                                )}
                                
                                {expandedRecord.statsType === 'MONTHLY' && (
                                    <Tabs.TabPane 
                                        tab={
                                            <span>
                                                <span style={{ color: '#fa8c16', marginRight: '4px' }}>●</span>
                                                月内未消费用户
                                                <span style={{ marginLeft: '4px', color: '#999', fontSize: '12px' }}>
                                                    ({expandedRecord.monthlyUserList?.length || 0})
                                                </span>
                                            </span>
                                        } 
                                        key="monthly"
                                    >
                                        <div style={{ height: '350px', overflow: 'auto' }}>
                                        {expandedRecord.monthlyUserList && expandedRecord.monthlyUserList.length > 0 ? (
                                            <Table
                                                dataSource={expandedRecord.monthlyUserList}
                                                rowKey="id"
                                                size="small"
                                                pagination={{ pageSize: 5, size: 'small', showSizeChanger: false }}
                                                columns={[
                                                    { title: 'ID', dataIndex: 'id', width: 60, align: 'center' as const },
                                                    { title: '用户名', dataIndex: 'nickName', width: 120, align: 'center' as const },
                                                    { title: '手机号', dataIndex: 'mobile', width: 120, align: 'center' as const }
                                                ]}
                                                style={{ backgroundColor: 'white', borderRadius: '4px' }}
                                            />
                                        ) : <Empty description="无月内未消费用户" style={{ height: '250px', display: 'flex', flexDirection: 'column', justifyContent: 'center' }} />}
                                        </div>
                                    </Tabs.TabPane>
                                )}
                                
                                <Tabs.TabPane 
                                    tab={
                                        <span>
                                            <span style={{ color: '#722ed1', marginRight: '4px' }}>●</span>
                                            退点率{'>'} 5%用户
                                            <span style={{ marginLeft: '4px', color: '#999', fontSize: '12px' }}>
                                                ({expandedRecord.customerRefundUser?.length || 0})
                                            </span>
                                        </span>
                                    }
                                    key="refund"
                                >
                                    <div style={{ height: '350px', overflow: 'auto' }}>
                                    {expandedRecord.customerRefundUser && expandedRecord.customerRefundUser.length > 0 ? (
                                        <Table
                                            dataSource={expandedRecord.customerRefundUser}
                                            rowKey="id"
                                            size="small"
                                            pagination={{ pageSize: 5, size: 'small', showSizeChanger: false }}
                                            columns={[
                                                { title: 'ID', dataIndex: 'id', width: 60, align: 'center' },
                                                { title: '用户名', dataIndex: 'nickName', width: 120, align: 'center' },
                                                { title: '手机号', dataIndex: 'mobile', width: 120, align: 'center' },
                                                { 
                                                    title: '退点数据（退/总）', 
                                                    width: 120,
                                                    align: 'center',
                                                    render: (_, record) => {
                                                        const refundRate = record.refundRate || '0/0';
                                                        return <span>{refundRate}</span>;
                                                    }
                                                },
                                                { 
                                                    title: '退点率', 
                                                    width: 80,
                                                    align: 'center',
                                                    render: (_, record) => {
                                                        const refundRate = record.refundRate || '0/0';
                                                        const parts = refundRate.split('/');
                                                        if (parts.length !== 2) return <Tag color="red" style={{ fontWeight: 'bold' }}>0.0%</Tag>;
                                                        
                                                        const numerator = parseFloat(parts[0]);
                                                        const denominator = parseFloat(parts[1]);
                                                        
                                                        if (denominator === 0) return <Tag color="red" style={{ fontWeight: 'bold' }}>0.0%</Tag>;
                                                        
                                                        // 计算百分比
                                                        const percentage = (numerator / denominator * 100).toFixed(1) + '%';
                                                        
                                                        return <Tag color="red" style={{ fontWeight: 'bold' }}>{percentage}</Tag>;
                                                    }
                                                }
                                            ]}
                                            style={{ backgroundColor: 'white', borderRadius: '4px' }}
                                        />
                                    ) : <Empty description="无退款率预警用户" style={{ height: '250px', display: 'flex', flexDirection: 'column', justifyContent: 'center' }} />}
                                    </div>
                                </Tabs.TabPane>
                                
                                <Tabs.TabPane 
                                    tab={
                                        <span>
                                            <span style={{ color: '#1890ff', marginRight: '4px' }}>●</span>
                                            余额预警用户
                                            <span style={{ marginLeft: '4px', color: '#999', fontSize: '12px' }}>
                                                ({expandedRecord.balanceWarningUser?.length || 0})
                                            </span>
                                        </span>
                                    }
                                    key="balance"
                                >
                                    <div style={{ height: '350px', overflow: 'auto' }}>
                                    {expandedRecord.balanceWarningUser && expandedRecord.balanceWarningUser.length > 0 ? (
                                        <Table
                                            dataSource={expandedRecord.balanceWarningUser}
                                            rowKey="id"
                                            size="small"
                                            pagination={{ pageSize: 5, size: 'small', showSizeChanger: false }}
                                            columns={[
                                                { title: 'ID', dataIndex: 'id', width: 60, align: 'center' },
                                                { title: '用户名', dataIndex: 'nickName', width: 120, align: 'center' },
                                                { 
                                                    title: '缪斯点余额', 
                                                    dataIndex: 'point', 
                                                    width: 120, 
                                                    align: 'center',
                                                    render: (text) => {
                                                        if (!text && text !== 0) return '-';
                                                        const musePoint = (text / 1000).toFixed(2);
                                                        return <span style={{ color: text < 2000 ? '#ff4d4f' : '#333' }}>
                                                            {musePoint}
                                                        </span>;
                                                    }
                                                }
                                            ]}
                                            style={{ backgroundColor: 'white', borderRadius: '4px' }}
                                        />
                                    ) : <Empty description="无余额预警用户" style={{ height: '250px', display: 'flex', flexDirection: 'column', justifyContent: 'center' }} />}
                                    </div>
                                </Tabs.TabPane>
                                
                                <Tabs.TabPane 
                                    tab={
                                        <span>
                                            <span style={{ color: '#52c41a', marginRight: '4px' }}>●</span>
                                            60天未转化用户
                                            <span style={{ marginLeft: '4px', color: '#999', fontSize: '12px' }}>
                                                ({expandedRecord.sixtyDaysNoConvertUserList?.length || 0})
                                            </span>
                                        </span>
                                    }
                                    key="noConvert"
                                >
                                    <div style={{ height: '350px', overflow: 'auto' }}>
                                    {expandedRecord.sixtyDaysNoConvertUserList && expandedRecord.sixtyDaysNoConvertUserList.length > 0 ? (
                                        <Table
                                            dataSource={expandedRecord.sixtyDaysNoConvertUserList}
                                            rowKey="id"
                                            size="small"
                                            pagination={{ pageSize: 5, size: 'small', showSizeChanger: false }}
                                            columns={[
                                                { title: 'ID', dataIndex: 'id', width: 60, align: 'center' },
                                                { title: '用户名', dataIndex: 'nickName', width: 120, align: 'center' },
                                                { title: '手机号', dataIndex: 'mobile', width: 120, align: 'center' },
                                                { 
                                                    title: '创建时间', 
                                                    dataIndex: 'createTime', 
                                                    width: 150, 
                                                    align: 'center',
                                                    render: (text) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-' 
                                                }
                                            ]}
                                            style={{ backgroundColor: 'white', borderRadius: '4px' }}
                                        />
                                    ) : <Empty description="无长期未转化用户" style={{ height: '250px', display: 'flex', flexDirection: 'column', justifyContent: 'center' }} />}
                                    </div>
                                </Tabs.TabPane>
                                
                                <Tabs.TabPane 
                                    tab={
                                        <span>
                                            <span style={{ color: '#eb2f96', marginRight: '4px' }}>●</span>
                                            交付超过24小时服装
                                            <span style={{ marginLeft: '4px', color: '#999', fontSize: '12px' }}>
                                                ({expandedRecord.deliveryTimeoutMaterial?.length || 0})
                                            </span>
                                        </span>
                                    }
                                    key="delivery"
                                >
                                    <div style={{ height: '350px', overflow: 'auto' }}>
                                    {expandedRecord.deliveryTimeoutMaterial && expandedRecord.deliveryTimeoutMaterial.length > 0 ? (
                                        <Table
                                            dataSource={expandedRecord.deliveryTimeoutMaterial}
                                            rowKey="id"
                                            size="small"
                                            pagination={{ pageSize: 5, size: 'small', showSizeChanger: false }}
                                            columns={[
                                                { title: 'ID', dataIndex: 'id', width: 60, align: 'center' },
                                                { title: '服装名称', dataIndex: 'name', width: 150, align: 'center' },
                                                { 
                                                    title: '交付总耗时(小时)', 
                                                    dataIndex: 'timeoutHours', 
                                                    width: 120,
                                                    align: 'center',
                                                    render: (text) => {
                                                        if (!text) return '-';
                                                        const hours = parseFloat(text);
                                                        const formattedHours = hours.toFixed(2); // 保留两位小数
                                                        
                                                        let color;
                                                        if (hours > 48) color = 'red';
                                                        else if (hours > 24) color = 'orange';
                                                        else color = 'green';
                                                        
                                                        return <Tag color={color} style={{ fontWeight: 'bold' }}>{formattedHours}</Tag>;
                                                    }
                                                },
                                                { 
                                                    title: '展示图', 
                                                    dataIndex: 'showImage', 
                                                    width: 100,
                                                    align: 'center',
                                                    render: (text) => text ? (
                                                        <img
                                                            src={text}
                                                            alt="服装图片"
                                                            style={{
                                                                width: 50,
                                                                height: 50,
                                                                objectFit: 'cover',
                                                                borderRadius: '4px',
                                                                border: '1px solid #eee'
                                                            }}
                                                        />
                                                    ) : '无图片'
                                                },
                                                { 
                                                    title: '创建时间', 
                                                    dataIndex: 'createTime', 
                                                    width: 150,
                                                    align: 'center',
                                                    render: (text) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-' 
                                                },
                                            ]}
                                            style={{ backgroundColor: 'white', borderRadius: '4px' }}
                                        />
                                    ) : <Empty description="无交付超时服装" style={{ height: '250px', display: 'flex', flexDirection: 'column', justifyContent: 'center' }} />}
                                    </div>
                                </Tabs.TabPane>
                            </Tabs>
                        </Card>
                    </div>
                )}
            </Modal>
        );
    };

    return (
        <div style={{ padding: '16px' }}>
            {loading ? (
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    minHeight: '200px'
                }}>
                    <Spin size="large" />
                </div>
            ) : noData ? (
                <Empty description="暂无数据" />
            ) : (
                <>
                    <Table
                        loading={loading}
                        dataSource={data?.list || []}
                        rowKey="id"
                        pagination={false}
                        onChange={onTableChange}
                        scroll={{ x: 1500 }}
                        columns={columns}
                        locale={{
                            emptyText: (
                                <div style={{ padding: '20px 0' }}>
                                    <div style={{ fontSize: 16, marginBottom: 8 }}>没有对应的记录</div>
                                    <div>请尝试更改查询条件或时间范围</div>
                                </div>
                            )
                        }}
                    />

                    {/* 只有在有记录时才显示分页 */}
                    {!noData && totalRecords > 0 && (
                        <div style={{ textAlign: 'right', marginTop: 16 }}>
                            <Pagination
                                current={currentPage}
                                pageSize={pageSize}
                                total={totalRecords}
                                onChange={(page, size) => {
                                    onPageChange(page, size);
                                    // 直接使用新的页码和每页条数调用请求函数
                                    fetchDetailData(page, size || pageSize);
                                }}
                                showSizeChanger
                                pageSizeOptions={['10', '20', '30', '40']}
                                showQuickJumper
                                showTotal={(total) => `共 ${total} 条`}
                            />
                        </div>
                    )}
                    
                    {/* 详情弹窗 */}
                    {renderDetailModal()}
                </>
            )}
        </div>
    );
});

// 统计信息部分组件
const StatsWarningSection: React.FC = () => {
    const [activeTab, setActiveTab] = useState<string>('WEEKLY');
    const [detailData, setDetailData] = useState<PageResponse<StatsWarningInfoVO>>({
        list: [],
        hasNextPage: false,
        size: 10,
        totalCount: 0
    });
    const [detailLoading, setDetailLoading] = useState<boolean>(false);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [totalRecords, setTotalRecords] = useState<number>(0);
    const [sortField, setSortField] = useState<string>('');
    const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | undefined>(undefined);
    const [isInitialized, setIsInitialized] = useState<boolean>(false);

    // 创建初始筛选状态函数
    const createInitialFilterState = useCallback((statsType: string): FilterState => {
        // 移除默认日期设置，返回空值
        return {
            selectedDate: null,
            dateRange: null
        };
    }, []);

    // 使用单一状态管理所有筛选条件
    const [filterState, setFilterState] = useState<FilterState>(() => createInitialFilterState(activeTab));

    // 部分更新筛选条件的辅助函数
    const updateFilter = useCallback((key: keyof FilterState, value: any) => {
        setFilterState(prev => ({ ...prev, [key]: value }));
    }, []);

    // 数据表格数据加载函数
    const fetchDetailData = useCallback(async (page?: number, size?: number) => {
        const pageToUse = page || currentPage;
        const sizeToUse = size || pageSize;

        setDetailLoading(true);
        try {
            const params: any = {
                pageNum: pageToUse,
                pageSize: sizeToUse,
                statsType: activeTab
            };

            // 添加排序参数
            if (sortField && sortOrder) {
                const direction = sortOrder === 'ascend' ? 'asc' : 'desc';
                // 将驼峰式字段名转换为下划线式
                const dbField = sortField.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
                params.orderBy = `${dbField} ${direction}`;
            } else {
                params.orderBy = 'stats_date desc';
            }

            // 添加选中日期参数
            if (filterState.selectedDate) {
                // 根据统计类型设置不同的日期参数
                let dateToUse;
                if (activeTab === 'WEEKLY') {
                    dateToUse = filterState.selectedDate.startOf('week');
                } else if (activeTab === 'MONTHLY') {
                    dateToUse = filterState.selectedDate.startOf('month');
                } else {
                    dateToUse = filterState.selectedDate;
                }
                params.statsDate = dateToUse.format('YYYY-MM-DD');
            }

            // 添加dateRange参数
            if (filterState.dateRange) {
                params.statsDateLower = filterState.dateRange[0];
                params.statsDateUpper = filterState.dateRange[1];
            }

            // 调用API
            const response = await statsWarningInfo(params);
            const result = formatApiResponse(response);

            if (result) {
                // 更新状态
                setDetailData({
                    list: result.list || [],
                    hasNextPage: true,  // 这里可以根据实际情况设置
                    size: sizeToUse,
                    totalCount: result.totalCount || 0
                });
                setTotalRecords(result.totalCount || 0);

                // 如果页码或每页条数发生变化，同步更新状态
                if (pageToUse !== currentPage) {
                    setCurrentPage(pageToUse);
                }
                if (sizeToUse !== pageSize) {
                    setPageSize(sizeToUse);
                }
            }
        } catch (error) {
            console.error('获取统计数据失败:', error);
            message.error('获取统计数据失败，请重试');
        } finally {
            setDetailLoading(false);
        }
    }, [currentPage, pageSize, sortField, sortOrder, filterState, activeTab]);

    // 表格筛选处理函数
    const handleTableChange = useCallback((pagination: any, filters: any, sorter: any) => {
        // 这里不再处理排序，只处理其他筛选逻辑
        // 重置到第一页
        setCurrentPage(1);

        // 直接触发加载
        fetchDetailData(1, pagination.pageSize);
    }, [fetchDetailData]);

    // 处理筛选按钮点击
    const handleFilter = useCallback(() => {
        setCurrentPage(1);
        fetchDetailData(1, pageSize);
    }, [fetchDetailData, pageSize]);

    // 首次加载和统计类型变化时加载数据
    useEffect(() => {
        // 不再设置初始日期，只记录组件已初始化
        if (!isInitialized) {
            setIsInitialized(true);
        }
        
        fetchDetailData();
    }, [fetchDetailData, activeTab, isInitialized]);

    // 获取对应的统计类型标签
    const getStatsTypeLabel = (type: string) => {
        switch (type) {
            case 'WEEKLY':
                return '周统计';
            case 'MONTHLY':
                return '月统计';
            default:
                return '未知';
        }
    };

    return (
        <div className="warning-stats-container">
            {/* 统计类型选择卡片 */}
            <Card className="warning-stats-selection-card" style={{ marginBottom: '16px' }}>
                <Flex align="center" gap={24}>
                    <Flex vertical gap={8}>
                        <span style={{ fontWeight: 'bold' }}>统计周期:</span>
                        <Radio.Group 
                            value={activeTab} 
                            onChange={(e) => {
                                setActiveTab(e.target.value);
                                // 切换统计类型时不再设置默认日期，保持为空
                                setFilterState({
                                    selectedDate: null,
                                    dateRange: null
                                });
                                setCurrentPage(1);
                            }}
                            buttonStyle="solid"
                        >
                            <Radio.Button value="WEEKLY">周统计</Radio.Button>
                            <Radio.Button value="MONTHLY">月统计</Radio.Button>
                        </Radio.Group>
                    </Flex>
                </Flex>
            </Card>

            <FilterCard
                filterState={filterState}
                updateFilter={updateFilter}
                onReset={handleFilter}
                onFilter={handleFilter}
                loading={detailLoading}
                statsType={activeTab}
                statsTypeLabel={getStatsTypeLabel(activeTab)}
            />

            <DataTable
                data={detailData}
                loading={detailLoading}
                totalRecords={totalRecords}
                currentPage={currentPage}
                pageSize={pageSize}
                sortField={sortField}
                sortOrder={sortOrder}
                onPageChange={(page, size) => {
                    setCurrentPage(page);
                    if (size) {
                        setPageSize(size);
                    }
                }}
                onTableChange={handleTableChange}
                statsType={activeTab}
                fetchDetailData={fetchDetailData}
            />
        </div>
    );
};

// 主组件
const StatsWarningInfo: React.FC = () => {
    // 检查用户是否为管理员
    const userInfo = getUserInfo();
    const isAdmin = userInfo && userInfo.roleType === 'ADMIN';

    // 如果不是管理员，显示无权限信息
    if (!isAdmin) {
        return (
            <PageContainer>
                <Card>
                    <div style={{ textAlign: 'center', padding: '50px 0' }}>
                        您没有访问此页面的权限
                    </div>
                </Card>
            </PageContainer>
        );
    }

    return (
        <PageContainer title="">
            <Card>
                <StatsWarningSection />
            </Card>
        </PageContainer>
    );
};

export default StatsWarningInfo;
