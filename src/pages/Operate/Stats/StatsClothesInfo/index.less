// 服装统计页面样式
.clothes-stats-container {
  .clothes-stats-card {
    margin-bottom: 24px;
    min-height: 700px;
  }
  
  .clothes-stats-total-section {
    margin: 0 0 24px 0;
    padding: 16px;
    background: #f9fcff;
    border-radius: 8px;
    border: 1px solid #d6e4ff;
  }
  
  .clothes-stats-total-title {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px dashed #d6e4ff;
    text-align: center;
  }
  
  .clothes-stats-total-text {
    font-size: 18px;
    font-weight: bold;
    color: #1890ff;
    position: relative;
    padding: 0 16px;
    
    &::before, &::after {
      content: "";
      position: absolute;
      top: 50%;
      width: 20px;
      height: 2px;
      background-color: #1890ff;
    }
    
    &::before {
      left: -10px;
      transform: translateY(-50%);
    }
    
    &::after {
      right: -10px;
      transform: translateY(-50%);
    }
  }
  
  .clothes-stats-chart-section {
    margin: 0 0 24px 0;
    padding: 16px;
    background: #f9fcff;
    border-radius: 8px;
    border: 1px solid #d6e4ff;
  }
  
  .clothes-stats-chart-controls {
    margin-bottom: 16px;
    
    .ant-picker {
      min-width: 240px;
    }
    
    .ant-select {
      margin-right: 8px;
    }
  }
  
  .clothes-stats-chart-title {
    font-size: 16px;
    font-weight: bold;
    color: #1890ff;
  }
  
  .clothes-stats-total-card {
    margin-bottom: 24px;
    
    .ant-card-head {
      background: #f0f7ff;
      border-bottom: 1px solid #d6e4ff;
    }
    
    .ant-card-head-title {
      font-weight: bold;
      color: #1890ff;
    }
  }

  .clothes-stats-filter-card {
    margin: 16px 0;
    padding: 16px;
    background-color: #f5f5f5;
  }

  .clothes-stats-metrics-row {
    margin-top: 16px;
    margin-bottom: 8px;
  }

  // 统计指标卡片样式
  .clothes-stats-metric-vip,
  .clothes-stats-metric-auto-train,
  .clothes-stats-metric-manual,
  .clothes-stats-metric-auto-train-delivery,
  .clothes-stats-metric-retry-matting,
  .clothes-stats-metric-update-prompt,
  .clothes-stats-metric-copy,
  .clothes-stats-metric-card {
    min-width: 180px;
    flex: 1;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
    
    .ant-statistic-title {
      font-size: 14px;
      margin-bottom: 8px;
      font-weight: 500;
      color: #333;
    }

    .ant-statistic-content {
      font-size: 24px;
      font-weight: 500;
    }
    
    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
  
  // 马卡龙配色方案
  .clothes-stats-metric-vip {
    background-color: rgba(251, 184, 197, 0.1);
    border: 1px solid #fbb8c5;
    
    .ant-statistic-content {
      color: #fbb8c5;
    }
  }
  
  .clothes-stats-metric-auto-train {
    background-color: rgba(158, 221, 178, 0.1);
    border: 1px solid #9eddb2;
    
    .ant-statistic-content {
      color: #9eddb2;
    }
  }
  
  .clothes-stats-metric-manual {
    background-color: rgba(175, 180, 218, 0.1);
    border: 1px solid #afb4da;
    
    .ant-statistic-content {
      color: #afb4da;
    }
  }
  
  .clothes-stats-metric-auto-train-delivery {
    background-color: rgba(248, 204, 161, 0.1);
    border: 1px solid #f8cca1;
    
    .ant-statistic-content {
      color: #f8cca1;
    }
  }
  
  .clothes-stats-metric-retry-matting {
    background-color: rgba(218, 236, 162, 0.1);
    border: 1px solid #daeca2;
    
    .ant-statistic-content {
      color: #daeca2;
    }
  }
  
  .clothes-stats-metric-update-prompt {
    background-color: rgba(244, 175, 159, 0.1);
    border: 1px solid #f4af9f;
    
    .ant-statistic-content {
      color: #f4af9f;
    }
  }
  
  .clothes-stats-metric-copy {
    background-color: rgba(224, 214, 245, 0.1);
    border: 1px solid #e0d6f5;
    
    .ant-statistic-content {
      color: #e0d6f5;
    }
  }

  .clothes-stats-table-container {
    margin-top: 24px;
  }

  // 响应式调整
  @media (max-width: 768px) {
    .clothes-stats-metric-card {
      min-width: 120px;
    }
  }
}

// 服装统计数值样式
.clothes-stats-vip {
  color: #00000073;  // 粉红色马卡龙
  text-shadow: 
    0 0 2px #fff,
    0 0 4px #fff,
    0 0 6px #F6C3CD,
    0 0 10px #F6C3CD,
    0 0 15px #F6C3CD;
  font-weight: 600;
  padding: 2px 6px;
}

.clothes-stats-auto-train {
  color: #00000073;  // 薄荷绿马卡龙
  text-shadow: 
    0 0 2px #fff,
    0 0 4px #fff,
    0 0 6px #B4E1C2,
    0 0 10px #B4E1C2,
    0 0 15px #B4E1C2;
  font-weight: 600;
  padding: 2px 6px;
}

.clothes-stats-manual {
  color: #00000073;  // 蓝莓色马卡龙
  text-shadow: 
    0 0 2px #fff,
    0 0 4px #fff,
    0 0 6px #C3C7E5,
    0 0 10px #C3C7E5,
    0 0 15px #C3C7E5;
  font-weight: 600;
  padding: 2px 6px;
}

.clothes-stats-auto-train-delivery {
  color: #00000073;  // 杏仁色马卡龙
  text-shadow: 
    0 0 2px #fff,
    0 0 4px #fff,
    0 0 6px #F5D2AF,
    0 0 10px #F5D2AF,
    0 0 15px #F5D2AF;
  font-weight: 600;
  padding: 2px 6px;
}

.clothes-stats-retry-matting {
  color: #00000073;  // 开心果色马卡龙
  text-shadow:
    0 0 2px #fff,
    0 0 4px #fff,
    0 0 6px #DBE8B3,
    0 0 10px #DBE8B3,
    0 0 15px #DBE8B3;
  font-weight: 600;
  padding: 2px 6px;
}

.clothes-stats-update-prompt {
  color: #00000073;  // 覆盆子色马卡龙
  text-shadow:
    0 0 2px #fff,
    0 0 4px #fff,
    0 0 6px #F4C3B8,
    0 0 10px #F4C3B8,
    0 0 15px #F4C3B8;
  font-weight: 600;
  padding: 2px 6px;
}

.clothes-stats-copy {
  color: #00000073;  // 淡紫色马卡龙
  text-shadow:
    0 0 2px #fff,
    0 0 4px #fff,
    0 0 6px #E0D6F5,
    0 0 10px #E0D6F5,
    0 0 15px #E0D6F5;
  font-weight: 600;
  padding: 2px 6px;
}
