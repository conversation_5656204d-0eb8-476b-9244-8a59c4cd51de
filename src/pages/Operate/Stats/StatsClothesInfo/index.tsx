import '@/pages/Operate/Stats/StatsClothesInfo/index.less';
import React, { useEffect, useState, useCallback, memo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Card, Tabs, Table, Pagination, Select,
  Flex, Spin, Statistic, message, DatePicker
} from 'antd';
import { PageInfo, statsClothesInfo, StatsClothesInfoVO } from '@/services/DataController';
import { getUserInfo } from '@/utils/utils';
import dayjs from 'dayjs';
import ChartsClothesInfo from '@/components/Operate/Stats/ChartsClothesInfo';

const { Option } = Select;
const { RangePicker } = DatePicker;

// Type guard to check if response is a PageInfo
function isPageInfo(response: any): response is PageInfo<StatsClothesInfoVO> {
  return response &&
    typeof response === 'object' &&
    Array.isArray(response.list) &&
    typeof response.totalCount === 'number';
}

// Convert API response to appropriate format
function formatApiResponse(response: any): PageInfo<StatsClothesInfoVO> {
  let result: PageInfo<StatsClothesInfoVO>;

  if (isPageInfo(response)) {
    result = response;
  } else if (Array.isArray(response)) {
    result = {
      list: response,
      totalCount: response.length
    };
  } else {
    result = {
      list: [],
      totalCount: 0
    };
  }

  return result;
}

// 抽离统计卡片组件
interface MetricCardProps {
  title: string;
  value: number;
  loading?: boolean;
  precision?: number;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  className?: string;
}

const MetricCard = memo(({
  title,
  value,
  loading = false,
  precision = 0,
  prefix,
  suffix,
  className
}: MetricCardProps) => {
  return (
    <Card className={className || 'clothes-stats-metric-card'}>
      <Statistic
        title={title}
        value={value}
        precision={precision}
        loading={loading}
        prefix={prefix}
        suffix={suffix}
      />
    </Card>
  );
});

// DataTable组件接口
interface DataTableProps {
  data: StatsClothesInfoVO[];
  loading: boolean;
  totalRecords: number;
  currentPage: number;
  pageSize: number;
  onPageChange: (page: number, size?: number) => void;
  onTableChange: (pagination: any, filters: any, sorter: any) => void;
  statsType: string;
}

// DataTable组件
const DataTable = memo(({
  data,
  loading,
  totalRecords,
  currentPage,
  pageSize,
  onPageChange,
  onTableChange,
  statsType
}: DataTableProps) => {
  // 格式化日期显示
  const formatStatsDate = (statsDate: string | undefined, type: string) => {
    if (!statsDate) return '-';

    try {
      const startDate = dayjs(statsDate);

      if (type === 'WEEKLY') {
        // 对于周统计，显示周一到周日的日期范围
        const endDate = startDate.add(6, 'day');
        return (
          <div style={{ whiteSpace: 'nowrap' }}>
            <div>{startDate.format('YYYY-MM-DD')} / </div>
            <div>{endDate.format('YYYY-MM-DD')}</div>
          </div>
        );
      } else if (type === 'MONTHLY') {
        // 对于月统计，显示月初到月末的日期范围
        const endDate = startDate.endOf('month');
        return (
          <div style={{ whiteSpace: 'nowrap' }}>
            <div>{startDate.format('YYYY-MM-DD')} / {endDate.format('DD')}</div>
          </div>
        );
      } else {
        // 日统计和总统计直接显示日期
        return startDate.format('YYYY-MM-DD');
      }
    } catch (e) {
      console.error('日期格式化错误:', e);
      return statsDate || '-';
    }
  };

  // 检查数据是否为空
  const noData = !loading && (!data || data.length === 0);

  return (
    <>
      <Table
        loading={loading}
        dataSource={data}
        rowKey="id"
        pagination={false}
        onChange={onTableChange}
        columns={[
          {
            title: 'VIP用户上传',
            dataIndex: 'vipClothesCount',
            key: 'vipClothesCount',
            width: 120,
            sorter: true,
            render: (text) => (
              <span className="clothes-stats-vip">{text || 0} 套</span>
            ),
          },
          {
            title: '自动训练',
            dataIndex: 'autoTrainCount',
            key: 'autoTrainCount',
            width: 120,
            sorter: true,
            render: (text) => (
              <span className="clothes-stats-auto-train">{text || 0} 套</span>
            ),
          },
          {
            title: '人工交付',
            dataIndex: 'manualDeliveryCount',
            key: 'manualDeliveryCount',
            width: 120,
            sorter: true,
            render: (text) => (
              <span className="clothes-stats-manual">{text || 0} 套</span>
            ),
          },
          {
            title: '自动训练+交付',
            dataIndex: 'autoTrainAndDeliveryCount',
            key: 'autoTrainAndDeliveryCount',
            width: 150,
            sorter: true,
            render: (text) => (
              <span className="clothes-stats-auto-train-delivery">{text || 0} 套</span>
            ),
          },
          {
            title: '二次抠图',
            dataIndex: 'retryMattingCount',
            key: 'retryMattingCount',
            width: 120,
            sorter: true,
            render: (text) => (
              <span className="clothes-stats-retry-matting">{text || 0} 次</span>
            ),
          },
          {
            title: '更新提示词',
            dataIndex: 'updatePromptCount',
            key: 'updatePromptCount',
            width: 120,
            sorter: true,
            render: (text) => (
              <span className="clothes-stats-update-prompt">{text || 0} 次</span>
            ),
          },
          {
            title: '克隆服装',
            dataIndex: 'copyCount',
            key: 'copyCount',
            width: 120,
            sorter: true,
            render: (text) => (
              <span className="clothes-stats-copy">{text || 0} 套</span>
            ),
          },
          {
            title: '统计日期',
            dataIndex: 'statsDate',
            key: 'statsDate',
            width: 150,
            render: (text) => formatStatsDate(text, statsType),
          },
        ]}
        locale={{
          emptyText: (
            <div style={{ padding: '20px 0' }}>
              <div style={{ fontSize: 16, marginBottom: 8 }}>没有对应的记录</div>
              <div>请尝试调整页码或排序方式</div>
            </div>
          )
        }}
      />

      {/* 只有在有记录时才显示分页 */}
      {!noData && totalRecords > 0 && (
        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={totalRecords}
          onChange={onPageChange}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `共 ${total} 条`}
          style={{ marginTop: 16, textAlign: 'right' }}
        />
      )}
    </>
  );
});

// 定义统计类型的Tab项
const statsTabItems = [
  { key: 'DAILY', label: '日统计' },
  { key: 'WEEKLY', label: '周统计' },
  { key: 'MONTHLY', label: '月统计' },
];

// 定义统计卡片的配置项数组
const metricDefinitions = [
  { key: 'vipClothesCount', title: 'VIP用户上传', suffix: '套' },
  { key: 'autoTrainCount', title: '自动训练', suffix: '套' },
  { key: 'manualDeliveryCount', title: '人工交付', suffix: '套' },
  { key: 'autoTrainAndDeliveryCount', title: '自动训练+交付', suffix: '套' },
  { key: 'retryMattingCount', title: '二次抠图', suffix: '套' },
  { key: 'updatePromptCount', title: '更新提示词', suffix: '次' },
  { key: 'copyCount', title: '克隆服装', suffix: '套' },
];

// 服装信息统计主组件
const StatsClothesInfo: React.FC = () => {
  // Check if user is admin
  const userInfo = getUserInfo();
  const isAdmin = userInfo && userInfo.roleType === 'ADMIN';

  // 定义统计类型状态
  const [activeTab, setActiveTab] = useState<string>('DAILY');
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<StatsClothesInfoVO[]>([]);
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [sortField, setSortField] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | undefined>(undefined);
  
  // 图表相关状态
  const [chartType, setChartType] = useState<'bar' | 'line' | 'pie'>('bar');
  const [chartData, setChartData] = useState<StatsClothesInfoVO[]>([]);
  const [chartLoading, setChartLoading] = useState<boolean>(false);
  const [chartDateRange, setChartDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  
  // 总统计数据状态
  const [totalStatsLoading, setTotalStatsLoading] = useState<boolean>(false);
  const [totalStatsData, setTotalStatsData] = useState<StatsClothesInfoVO | null>(null);
  
  // 计算指标总数
  const [metrics, setMetrics] = useState({
    vipClothesCount: 0,
    autoTrainCount: 0,
    manualDeliveryCount: 0,
    autoTrainAndDeliveryCount: 0,
    retryMattingCount: 0,
    updatePromptCount: 0,
    copyCount: 0
  });
  
  // 加载图表数据
  const fetchChartData = useCallback(async (statsType: string) => {
    console.log('[图表] 开始加载数据:', { statsType, dateRange: chartDateRange });
    
    setChartLoading(true);
    try {
      // 使用较大的pageSize加载更多数据用于图表展示
      const params: any = {
        pageNum: 1,
        pageSize: 30, // 限制最多30条记录
        statsType,
        orderBy: 'stats_date desc' // 修改为按日期降序排列，获取最新数据
      };
      
      // 如果设置了日期范围，添加到查询参数
      if (chartDateRange && chartDateRange[0] && chartDateRange[1]) {
        params.startDate = chartDateRange[0].format('YYYY-MM-DD');
        params.endDate = chartDateRange[1].format('YYYY-MM-DD');
      }

      console.log('[图表] 请求参数:', params);
      const response = await statsClothesInfo(params);
      const result = formatApiResponse(response);

      if (result && result.list) {
        console.log(`[图表] 数据加载完成，共${result.list.length}条数据`);
        // 将数据反转，以便图表按时间先后顺序展示
        const sortedData = [...result.list].reverse();
        setChartData(sortedData);
      }
    } catch (error) {
      console.error('[图表] 获取数据失败:', error);
      message.error('获取图表数据失败，请重试');
    } finally {
      setChartLoading(false);
    }
  }, [chartDateRange]);
  
  // 加载总统计数据
  const fetchTotalStats = useCallback(async () => {
    console.log('[总统计] 开始加载数据');
    
    setTotalStatsLoading(true);
    try {
      const params = {
        pageNum: 1,
        pageSize: 1,
        statsType: 'TOTAL',
        orderBy: 'stats_date desc' // 获取最后一条记录
      };

      console.log('[总统计] 请求参数:', params);
      const response = await statsClothesInfo(params);
      const result = formatApiResponse(response);

      if (result && result.list && result.list.length > 0) {
        console.log(`[总统计] 数据加载完成:`, result.list[0]);
        setTotalStatsData(result.list[0]);
      } else {
        setTotalStatsData(null);
      }
    } catch (error) {
      console.error('[总统计] 获取数据失败:', error);
      message.error('获取总统计数据失败，请重试');
    } finally {
      setTotalStatsLoading(false);
    }
  }, []);

  // 数据加载函数
  const fetchData = useCallback(async () => {
    console.log('[服装统计] 开始加载数据:', { page: currentPage, pageSize, statsType: activeTab });

    setLoading(true);
    try {
      const params: any = {
        pageNum: currentPage,
        pageSize: pageSize,
        statsType: activeTab
      };

      // 添加排序参数
      if (sortField && sortOrder) {
        const direction = sortOrder === 'ascend' ? 'asc' : 'desc';
        // 将驼峰式字段名转换为下划线式
        const dbField = sortField.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        params.orderBy = `${dbField} ${direction}`;
      } else {
        params.orderBy = 'stats_date desc';
      }

      console.log('[服装统计] 请求参数:', params);
      const response = await statsClothesInfo(params);
      const result = formatApiResponse(response);

      if (result) {
        console.log(`[服装统计] 数据加载完成，共${result.list?.length || 0}条数据`);
        setData(result.list || []);
        setTotalRecords(result.totalCount || 0);

        // 计算指标总数
        if (result.list && result.list.length > 0) {
          const vipClothesCount = result.list.reduce((sum, item) => sum + (item.vipClothesCount || 0), 0);
          const autoTrainCount = result.list.reduce((sum, item) => sum + (item.autoTrainCount || 0), 0);
          const manualDeliveryCount = result.list.reduce((sum, item) => sum + (item.manualDeliveryCount || 0), 0);
          const autoTrainAndDeliveryCount = result.list.reduce((sum, item) => sum + (item.autoTrainAndDeliveryCount || 0), 0);
          const retryMattingCount = result.list.reduce((sum, item) => sum + (item.retryMattingCount || 0), 0);
          const updatePromptCount = result.list.reduce((sum, item) => sum + (item.updatePromptCount || 0), 0);
          const copyCount = result.list.reduce((sum, item) => sum + (item.copyCount || 0), 0);

          setMetrics({
            vipClothesCount,
            autoTrainCount,
            manualDeliveryCount,
            autoTrainAndDeliveryCount,
            retryMattingCount,
            updatePromptCount,
            copyCount
          });
        } else {
          // 重置指标为0
          setMetrics({
            vipClothesCount: 0,
            autoTrainCount: 0,
            manualDeliveryCount: 0,
            autoTrainAndDeliveryCount: 0,
            retryMattingCount: 0,
            updatePromptCount: 0,
            copyCount: 0
          });
        }
      }
    } catch (error) {
      console.error('[服装统计] 获取数据失败:', error);
      message.error('获取服装统计数据失败，请重试');
    } finally {
      setLoading(false);
    }
  }, [activeTab, currentPage, pageSize, sortField, sortOrder]);

  // 表格筛选处理函数
  const handleTableChange = useCallback((pagination: any, filters: any, sorter: any) => {
    console.log('[服装统计] 表格变化:', { sorter });

    // 处理排序
    if (sorter && sorter.columnKey) {
      setSortField(sorter.columnKey);
      setSortOrder(sorter.order);
    } else {
      setSortField('');
      setSortOrder(undefined);
    }

    // 重置到第一页
    setCurrentPage(1);

    // 直接触发加载
    fetchData();
  }, [fetchData]);

  // 处理Tab切换
  const handleTabChange = useCallback((key: string) => {
    setActiveTab(key);
    setCurrentPage(1);
    // 切换Tab后会自动触发请求
  }, []);

  // 处理日期范围变化
  const handleDateRangeChange = useCallback((
    dates: any,
    dateStrings: [string, string]
  ) => {
    console.log('[图表] 日期范围变化:', dateStrings);
    setChartDateRange(dates);
    
    // 明确调用图表数据加载函数
    if (activeTab) {
      setTimeout(() => {
        fetchChartData(activeTab);
      }, 0);
    }
  }, [activeTab, fetchChartData]);

  // 首次加载和Tab变化时加载数据
  useEffect(() => {
    fetchData();
    fetchChartData(activeTab); // 加载图表数据
  }, [fetchData, fetchChartData, activeTab]);

  // 首次加载时获取总统计数据
  useEffect(() => {
    fetchTotalStats();
  }, [fetchTotalStats]);

  // If not admin, show no permission message
  if (!isAdmin) {
    return (
      <PageContainer>
        <Card>
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            您没有访问此页面的权限
          </div>
        </Card>
      </PageContainer>
    );
  }

  // 自定义Tab内容，添加图表
  const getTabContent = (tabKey: string) => {
    // 根据不同的统计类型渲染不同的日期选择器
    const renderDatePicker = () => {
      if (tabKey === 'DAILY') {
        return (
          <RangePicker 
            value={chartDateRange}
            onChange={handleDateRangeChange}
            placeholder={['开始日期', '结束日期']}
            allowClear
            style={{ width: 300 }}
          />
        );
      } else if (tabKey === 'WEEKLY') {
        return (
          <RangePicker
            picker="week"
            placeholder={["开始周", "结束周"]}
            style={{ width: 300 }}
            value={chartDateRange ? [chartDateRange[0], chartDateRange[1]] : null}
            onChange={(dates) => {
              console.log('周范围选择器选中:', dates);
              if (dates && dates[0] && dates[1]) {
                const startWeek = dates[0].startOf('week');
                const endWeek = dates[1].endOf('week');
                handleDateRangeChange([startWeek, endWeek], [
                  startWeek.format('YYYY-MM-DD'),
                  endWeek.format('YYYY-MM-DD')
                ]);
              } else {
                handleDateRangeChange(null, ['', '']);
              }
            }}
          />
        );
      } else if (tabKey === 'MONTHLY') {
        return (
          <RangePicker
            picker="month"
            placeholder={["开始月", "结束月"]}
            style={{ width: 300 }}
            value={chartDateRange ? [chartDateRange[0], chartDateRange[1]] : null}
            onChange={(dates) => {
              console.log('月范围选择器选中:', dates);
              if (dates && dates[0] && dates[1]) {
                const startMonth = dates[0].startOf('month');
                const endMonth = dates[1].endOf('month');
                handleDateRangeChange([startMonth, endMonth], [
                  startMonth.format('YYYY-MM-DD'),
                  endMonth.format('YYYY-MM-DD')
                ]);
              } else {
                handleDateRangeChange(null, ['', '']);
              }
            }}
          />
        );
      }
      return null;
    };

    return (
      <>
        {/* 图表区域 */}
        <div className="clothes-stats-chart-section">
          <Flex justify="space-between" align="center" className="clothes-stats-chart-controls">
            <div className="clothes-stats-chart-title">数据趋势图</div>
            <Flex gap={8}>
              <Select
                value={chartType}
                onChange={(value) => setChartType(value)}
                style={{ width: 120 }}
              >
                <Option value="bar">柱状图</Option>
                <Option value="line">折线图</Option>
                <Option value="pie">饼图</Option>
              </Select>
              {renderDatePicker()}
            </Flex>
          </Flex>
          
          {chartLoading ? (
            <div className="charts-clothes-loading">
              <Spin />
            </div>
          ) : chartData.length === 0 ? (
            <div className="charts-clothes-empty">
              暂无数据
            </div>
          ) : (
            <ChartsClothesInfo
              data={chartData}
              chartType={chartType}
              statsType={tabKey as 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'TOTAL'}
              height={300}
              dateRange={chartDateRange ? 
                [chartDateRange[0].format('YYYY-MM-DD'), chartDateRange[1].format('YYYY-MM-DD')] : 
                undefined}
            />
          )}
        </div>

        {/* 数据表格 */}
        <div className="clothes-stats-table-container">
          <DataTable
            data={data}
            loading={loading}
            totalRecords={totalRecords}
            currentPage={currentPage}
            pageSize={pageSize}
            onPageChange={(page, size) => {
              setCurrentPage(page);
              setPageSize(size || pageSize);
            }}
            onTableChange={handleTableChange}
            statsType={activeTab}
          />
        </div>
      </>
    );
  };

  return (
    <PageContainer title="">
      <div className="clothes-stats-container">


        {/* 详细统计数据卡片 */}
        <Card className="clothes-stats-card">
          {/* 总统计卡片 - 放在最上面，单独展示 */}
          <div className="clothes-stats-total-section">
            <div className="clothes-stats-total-title">
              <span className="clothes-stats-total-text">服装信息变更累计统计</span>
            </div>
            {totalStatsLoading ? (
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                <Spin />
              </div>
            ) : !totalStatsData ? (
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                <div>暂无总统计数据</div>
              </div>
            ) : (
              <Flex wrap="wrap" gap={16} className="clothes-stats-metrics-row">
                {metricDefinitions.map((metric) => {
                  // 根据指标类型选择对应的样式类
                  let className = 'clothes-stats-metric-card';
                  if (metric.key === 'vipClothesCount') {
                    className = 'clothes-stats-metric-vip';
                  } else if (metric.key === 'autoTrainCount') {
                    className = 'clothes-stats-metric-auto-train';
                  } else if (metric.key === 'manualDeliveryCount') {
                    className = 'clothes-stats-metric-manual';
                  } else if (metric.key === 'autoTrainAndDeliveryCount') {
                    className = 'clothes-stats-metric-auto-train-delivery';
                  } else if (metric.key === 'retryMattingCount') {
                    className = 'clothes-stats-metric-retry-matting';
                  } else if (metric.key === 'updatePromptCount') {
                    className = 'clothes-stats-metric-update-prompt';
                  } else if (metric.key === 'copyCount') {
                    className = 'clothes-stats-metric-copy';
                  }
                  
                  return (
                    <MetricCard
                      key={metric.key}
                      title={metric.title}
                      value={totalStatsData[metric.key as keyof StatsClothesInfoVO] as number || 0}
                      suffix={metric.suffix}
                      className={className}
                    />
                  );
                })}
              </Flex>
            )}
          </div>

          <Tabs
            activeKey={activeTab}
            onChange={handleTabChange}
            items={statsTabItems.map(item => ({
              key: item.key,
              label: item.label,
              children: getTabContent(item.key)
            }))}
          />
        </Card>
      </div>
    </PageContainer>
  );
};

export default StatsClothesInfo;