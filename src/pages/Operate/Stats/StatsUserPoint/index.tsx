import React, { useEffect, useState, useRef, useCallback, memo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Card, Tabs, DatePicker, Table, Pagination, Select,
  Button, Flex, InputNumber, Spin, Statistic, message, Input, Alert,
} from 'antd';
import { PageInfo, statsUserPoint, StatsUserPointVO } from '@/services/DataController';
import { getUserInfo } from '@/utils/utils';
import '@/pages/Operate/Stats/StatsUserPoint/index.less';
import dayjs from 'dayjs';
import debounce from 'lodash/debounce';
import ChartsUserPoint from '@/components/Operate/Stats/ChartsUserPoint';

const { RangePicker } = DatePicker;
const { Option } = Select;

// 定义筛选条件状态接口
interface FilterState {
  userId: number | null;
  nickName: string;
  distributorCorpName: string;
  pointRange: [number | null, number | null];
  rechargeRange: [number | null, number | null];
  selectedDate: dayjs.Dayjs;
  dateRange: [string, string] | null; // 添加dateRange字段，记录开始日期和结束日期
}

// FilterCard组件接口
interface FilterCardProps {
  filterState: FilterState;
  updateFilter: (key: keyof FilterState, value: any) => void;
  onReset: () => void;
  onFilter: () => void;
  loading: boolean;
  statsType: string;
  statsTypeLabel: string;
  setInitialTabChange: (value: boolean) => void;
  handleInputChange: (field: string, value: string) => void;
}

// FilterCard组件
const FilterCard = memo(({
  filterState,
  updateFilter,
  onReset,
  onFilter,
  loading,
  statsType,
  statsTypeLabel,
  setInitialTabChange,
  handleInputChange
}: FilterCardProps) => {
  return (
    <Card className="point-stats-filter-card">
      <Flex gap={16} wrap="wrap" align="flex-start">
        <Flex vertical gap={0} style={{ width: '100%' }}>
          <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
            当前统计类型: <span style={{ color: '#1890ff' }}>{statsTypeLabel}</span>
          </div>
        </Flex>
        
        <Flex vertical gap={8}>
          <span>用户ID:</span>
          <InputNumber
            placeholder="输入用户ID"
            style={{ width: 150 }}
            value={filterState.userId}
            onChange={value => updateFilter('userId', value)}
          />
        </Flex>

        <Flex vertical gap={8}>
          <span>昵称:</span>
          <Input
            placeholder="输入昵称"
            style={{ width: 150 }}
            value={filterState.nickName}
            onChange={(e) => {
              updateFilter('nickName', e.target.value);
              // handleInputChange('nickName', e.target.value);
            }}
            allowClear
          />
        </Flex>

        <Flex vertical gap={8}>
          <span>所属销售/渠道商:</span>
          <Input
            placeholder="输入销售/渠道商名称"
            style={{ width: 150 }}
            value={filterState.distributorCorpName}
            onChange={(e) => {
              updateFilter('distributorCorpName', e.target.value);
              // handleInputChange('distributorCorpName', e.target.value);
            }}
            allowClear
          />
        </Flex>

        <Flex vertical gap={8}>
          <span>缪斯点范围:</span>
          <Flex gap={8}>
            <InputNumber
              placeholder="最小值"
              style={{ width: 100 }}
              value={filterState.pointRange[0]}
              onChange={value => {
                updateFilter('pointRange', [value, filterState.pointRange[1]]);
                // 不需要即时搜索，等用户点击筛选按钮
              }}
            />
            <span>-</span>
            <InputNumber
              placeholder="最大值"
              style={{ width: 100 }}
              value={filterState.pointRange[1]}
              onChange={value => {
                updateFilter('pointRange', [filterState.pointRange[0], value]);
                // 不需要即时搜索，等用户点击筛选按钮
              }}
            />
          </Flex>
        </Flex>

        <Flex vertical gap={8}>
          <span>充值金额范围:</span>
          <Flex gap={8}>
            <InputNumber
              placeholder="最小值"
              style={{ width: 100 }}
              value={filterState.rechargeRange[0]}
              onChange={value => {
                updateFilter('rechargeRange', [value, filterState.rechargeRange[1]]);
                // 不需要即时搜索，等用户点击筛选按钮
              }}
              precision={2}
              prefix="¥"
            />
            <span>-</span>
            <InputNumber
              placeholder="最大值"
              style={{ width: 100 }}
              value={filterState.rechargeRange[1]}
              onChange={value => {
                updateFilter('rechargeRange', [filterState.rechargeRange[0], value]);
                // 不需要即时搜索，等用户点击筛选按钮
              }}
              precision={2}
              prefix="¥"
            />
          </Flex>
        </Flex>

        <Flex vertical gap={8}>
          <span>查询日期:</span>
          {(statsType === 'DAILY' || statsType === 'TOTAL') && (
            <DatePicker
              placeholder="选择日期"
              style={{ width: 150 }}
              value={filterState.selectedDate}
              onChange={(date) => {
                console.log('日期选择器选中:', date);
                if (date) {
                  // 对于日选择器，dateRange的开始和结束日期相同
                  const formattedDate = date.format('YYYY-MM-DD');
                  updateFilter('selectedDate', date);
                  updateFilter('dateRange', [formattedDate, formattedDate]);
                  // 手动选择日期后，取消初始Tab变化标志
                  setInitialTabChange(false);
                }
              }}
            />
          )}
          {statsType === 'WEEKLY' && (
            <DatePicker
              picker="week"
              placeholder="选择周"
              style={{ width: 150 }}
              value={filterState.selectedDate}
              onChange={(date) => {
                console.log('周选择器选中:', date);
                if (date) {
                  // 对于周选择器，获取该周的开始和结束日期
                  const weekStart = date.startOf('week').format('YYYY-MM-DD');
                  const weekEnd = date.endOf('week').format('YYYY-MM-DD');
                  updateFilter('selectedDate', date);
                  updateFilter('dateRange', [weekStart, weekEnd]);
                  // 手动选择日期后，取消初始Tab变化标志
                  setInitialTabChange(false);
                }
              }}
            />
          )}
          {statsType === 'MONTHLY' && (
            <DatePicker
              picker="month"
              placeholder="选择月"
              style={{ width: 150 }}
              value={filterState.selectedDate}
              onChange={(date) => {
                console.log('月选择器选中:', date);
                if (date) {
                  // 对于月选择器，获取该月的开始和结束日期
                  const monthStart = date.startOf('month').format('YYYY-MM-DD');
                  const monthEnd = date.endOf('month').format('YYYY-MM-DD');
                  updateFilter('selectedDate', date);
                  updateFilter('dateRange', [monthStart, monthEnd]);
                  // 手动选择日期后，取消初始Tab变化标志
                  setInitialTabChange(false);
                }
              }}
            />
          )}
        </Flex>

        <Flex style={{ alignSelf: 'flex-end' }}>
          <Button
            style={{ marginRight: 8 }}
            onClick={onReset}
          >
            重置
          </Button>
          <Button
            type="primary"
            onClick={onFilter}
            loading={loading}
          >
            筛选
          </Button>
        </Flex>
      </Flex>
    </Card>
  );
});

// Type guard to check if response is a PageInfo
function isPageInfo(response: any): response is PageInfo<StatsUserPointVO> {
  return response &&
    typeof response === 'object' &&
    Array.isArray(response.list) &&
    typeof response.totalCount === 'number';
}

// Convert API response to appropriate format
function formatApiResponse(response: any): PageInfo<StatsUserPointVO> {
  let result: PageInfo<StatsUserPointVO>;

  if (isPageInfo(response)) {
    result = response;
  } else if (Array.isArray(response)) {
    result = {
      list: response,
      totalCount: response.length
    };
  } else {
    result = {
      list: [],
      totalCount: 0
    };
  }

  // 统一处理 pointConsumed，将其缩小 1000 倍
  if (result.list && result.list.length > 0) {
    result.list = result.list.map(item => ({
      ...item,
      pointConsumed: item.pointConsumed ? item.pointConsumed / 1000 : 0
    }));
  }

  return result;
}

// 抽离统计卡片组件
interface MetricCardProps {
  title: string;
  value: number;
  metricType: 'point' | 'givePoint' | 'expPoint' | 'recharge' | 'ratio';
  loading?: boolean;
  precision?: number;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  className?: string;
}

const MetricCard = memo(({
  title,
  value,
  metricType,
  loading = false,
  precision = 2,
  prefix,
  suffix,
  className
}: MetricCardProps) => {
  // 不同类型的样式名和前后缀
  const metricClassMap = {
    point: 'point-stats-metric-point',
    givePoint: 'point-stats-metric-give-point',
    expPoint: 'point-stats-metric-exp-point',
    recharge: 'point-stats-metric-recharge',
    ratio: 'point-stats-metric-ratio'
  };

  // 根据类型添加默认前后缀
  const getPrefix = () => {
    if (prefix !== undefined) return prefix;
    if (metricType === 'recharge') return '¥';
    return undefined;
  };

  const getSuffix = () => {
    if (suffix !== undefined) return suffix;
    if (metricType === 'ratio') return '%';
    return undefined;
  };

  return (
    <Card className={className || 'point-stats-metric-card'}>
      <Statistic
        title={title}
        value={value}
        precision={precision}
        className={metricClassMap[metricType]}
        loading={loading}
        prefix={getPrefix()}
        suffix={getSuffix()}
      />
    </Card>
  );
});

// 用户详情页签项
const userDetailTabItems = [
  { key: 'DAILY', label: '日统计' },
  { key: 'WEEKLY', label: '周统计' },
  { key: 'MONTHLY', label: '月统计' },
  { key: 'TOTAL', label: '总统计' }
];


// 定义统计卡片的配置项数组
const metricDefinitions = [
  { key: 'totalPointConsumed', title: '缪斯点消耗', metricType: 'point' as const },
  { key: 'totalGivePointConsumed', title: '赠送点消耗', metricType: 'givePoint' as const },
  { key: 'totalModelPointConsumed', title: '套内点消耗', metricType: 'expPoint' as const },
  { key: 'totalRechargeAmount', title: '充值金额', metricType: 'recharge' as const },
  { key: 'consumptionRate', title: '消耗率', metricType: 'ratio' as const }
];

// 提取ExpandedContent组件到组件外部，并使用React.memo包装
interface ExpandedContentProps {
  record: StatsUserPointVO;
  fetchUserStatsDetail: (userId: number, statsType: string, dateRange?: [dayjs.Dayjs, dayjs.Dayjs]) => Promise<StatsUserPointVO[]>;
  expandedRowKeys: number[];
}

const ExpandedContent = React.memo(({ 
  record, 
  fetchUserStatsDetail, 
  expandedRowKeys 
}: ExpandedContentProps) => {
  // 将状态本地化到组件内部 - 完全隔离的状态
  const [expandedUserTab, setExpandedUserTab] = useState<string>('DAILY');
  const [expandedUserChartType, setExpandedUserChartType] = useState<string>('bar');
  const [userDetailDateRange, setUserDetailDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [localUserData, setLocalUserData] = useState<{
    userId: number,
    data: {
      DAILY: StatsUserPointVO[],
      WEEKLY: StatsUserPointVO[],
      MONTHLY: StatsUserPointVO[],
      TOTAL: StatsUserPointVO[]
    }
  } | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true); // 默认为加载状态
  const userIdRef = useRef<number | null>(null); // 使用ref追踪userID变化
  const isUnmountedRef = useRef<boolean>(false); // 组件卸载标志
  const isLoadingRef = useRef<{ [key: string]: boolean }>({ // 使用对象追踪每种统计类型的加载状态
    DAILY: false,
    WEEKLY: false, 
    MONTHLY: false,
    TOTAL: false
  });
  
  // 确保组件卸载时不会继续设置状态或请求数据
  useEffect(() => {
    console.log('ExpandedContent 组件挂载，id:', record.id);
    isUnmountedRef.current = false;
    
    return () => {
      console.log('ExpandedContent 组件卸载，id:', record.id);
      isUnmountedRef.current = true;
    };
  }, [record.id]);

  // 组件内部私有函数 - 加载单一类型的用户数据
  const loadUserData = useCallback(async (userId: number, statsType: string, dateRange?: [dayjs.Dayjs, dayjs.Dayjs]) => {
    if (!userId || isUnmountedRef.current) {
      console.log(`[用户详情] 跳过加载 ${statsType}: 无效ID或已卸载`);
      return;
    }
    
    // 确认行是否处于展开状态
    if (!expandedRowKeys.includes(record.id as number)) {
      console.log(`[用户详情] 跳过加载 ${statsType}: 行未展开`, record.id);
      return;
    }
    
    // 检查指定类型是否正在加载
    if (isLoadingRef.current[statsType]) {
      console.log(`[用户详情] 跳过加载 ${statsType}: 已在加载中`);
      return;
    }
    
    console.log(`[用户详情] 开始加载 ${statsType} 数据:`, { userId, dateRange });
    
    // 标记指定类型为加载中
    isLoadingRef.current[statsType] = true;
    setIsLoading(true);
    
    try {
      // 只加载指定类型的数据
      const data = await fetchUserStatsDetail(userId, statsType, dateRange);
      
      // 防止组件已卸载时设置状态
      if (isUnmountedRef.current) {
        console.log(`[用户详情] 跳过 ${statsType} 状态更新: 组件已卸载`);
        return;
      }
      
      console.log(`[用户详情] ${statsType} 数据加载完成，条数:`, data.length);
      
      // 更新指定类型的数据，保留其他类型的现有数据
      setLocalUserData((prevData) => {
        // 如果是首次加载或用户ID变化，初始化数据对象
        if (!prevData || prevData.userId !== userId) {
          const initialData = {
            DAILY: [],
            WEEKLY: [],
            MONTHLY: [],
            TOTAL: []
          };
          initialData[statsType] = data;
          return {
            userId,
            data: initialData
          };
        } else {
          // 保留现有数据，只更新请求的统计类型
          return {
            ...prevData,
            data: {
              ...prevData.data,
              [statsType]: data
            }
          };
        }
      });
    } catch (error) {
      console.error(`[用户详情] 获取 ${statsType} 数据失败`, error);
      if (!isUnmountedRef.current) {
        message.error(`获取用户 ${statsType} 统计数据失败`);
      }
    } finally {
      if (!isUnmountedRef.current) {
        setIsLoading(false);
      }
      // 取消指定类型的加载状态
      isLoadingRef.current[statsType] = false;
    }
  }, [fetchUserStatsDetail, record.id, expandedRowKeys]);

  // 私有函数 - 处理Tab切换
  const handleUserTabChange = useCallback((activeKey: string) => {
    console.log('[用户详情] Tab切换:', activeKey);
    setExpandedUserTab(activeKey);
    
    // 清空日期选择器
    setUserDetailDateRange(null);
    
    // 检查该类型的数据是否已加载
    const hasData = localUserData && 
                    localUserData.userId === record.userId && 
                    localUserData.data[activeKey] && 
                    localUserData.data[activeKey].length > 0;
    
    // 如果数据未加载，则加载该类型的数据
    if (!hasData && record.userId) {
      console.log(`[用户详情] ${activeKey} 数据未加载，开始请求`);
      loadUserData(record.userId, activeKey, undefined); // 使用undefined而不是null
    }
  }, [record, localUserData, loadUserData]);

  // 私有函数 - 处理图表类型切换
  const handleUserChartTypeChange = useCallback((type: string) => {
    console.log('[用户详情] 图表类型切换:', type);
    setExpandedUserChartType(type);
  }, []);

  // 私有函数 - 处理日期范围变化
  const handleUserDateRangeChange = useCallback((dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
    console.log('[用户详情] 日期范围变更:', dates);
    setUserDetailDateRange(dates);
    
    // 只有当有用户ID时才重新加载数据，且只加载当前选中Tab的数据
    if (record?.userId) {
      loadUserData(record.userId, expandedUserTab, dates || undefined);
    }
  }, [record, loadUserData, expandedUserTab]);

  // 初始数据加载 - 仅当行展开且用户ID变化时，只加载DAILY类型数据
  useEffect(() => {
    // 判断当前记录是否被展开
    const isCurrentRowExpanded = expandedRowKeys.includes(record.id as number);
    console.log('[用户详情] 展开状态/ID检查', {
      recordId: record.id,
      userId: record.userId,
      previousUserId: userIdRef.current,
      isExpanded: isCurrentRowExpanded
    });
    
    // 只有当用户ID变化且当前记录被展开时才加载DAILY数据
    if (record?.userId && 
        (record.userId !== userIdRef.current || userIdRef.current === null) && 
        isCurrentRowExpanded) {
      console.log('[用户详情] 用户ID变化或初始加载, 仅请求DAILY数据');
      userIdRef.current = record.userId;
      
      // 只加载DAILY类型数据
      loadUserData(record.userId, 'DAILY', userDetailDateRange || undefined);
    } else if (!isCurrentRowExpanded && userIdRef.current === record.userId) {
      // 行被收起但userIdRef仍是当前记录，重置引用
      console.log('[用户详情] 行已收起, 重置userIdRef');
      userIdRef.current = null;
    }
  }, [record.userId, record.id, expandedRowKeys, loadUserData, userDetailDateRange]);

  // 获取当前Tab的数据
  const getCurrentTabData = useCallback(() => {
    if (!localUserData || !localUserData.data) {
      console.log('[用户详情] 尚无数据可渲染');
      return [];
    }
    
    const data = localUserData.data[expandedUserTab] || [];
    console.log(`[用户详情] 渲染${expandedUserTab}类型图表, 数据条数:`, data.length);
    return data;
  }, [localUserData, expandedUserTab]);

  // 获取用户指标数据
  const getUserMetrics = useCallback(() => {
    if (!localUserData || !localUserData.data) {
      return {
        totalPointConsumed: 0,
        totalGivePointConsumed: 0,
        totalModelPointConsumed: 0,
        totalRechargeAmount: 0,
        consumptionRate: 0
      };
    }
    
    const data = localUserData.data[expandedUserTab] || [];
    const totalPointConsumed = data.reduce((sum, item) => sum + (item.pointConsumed || 0), 0);
    const totalGivePointConsumed = data.reduce((sum, item) => sum + (item.givePointConsumed || 0), 0);
    const totalModelPointConsumed = data.reduce((sum, item) => sum + (item.modelPointConsumed || 0), 0);
    const totalRechargeAmount = data.reduce((sum, item) => sum + (item.rechargeAmount || 0), 0);
    const consumptionRate = totalRechargeAmount > 0
      ? (totalPointConsumed / totalRechargeAmount) * 100
      : 0;

    return {
      totalPointConsumed,
      totalGivePointConsumed,
      totalModelPointConsumed,
      totalRechargeAmount,
      consumptionRate
    };
  }, [localUserData, expandedUserTab]);

  // 判断是否为TOTAL类型
  const isCurrentTabTotal = expandedUserTab === 'TOTAL';

  // 检查当前选中类型的数据是否已加载
  const isCurrentTabDataLoaded = localUserData && 
                               localUserData.userId === record.userId && 
                               Array.isArray(localUserData.data[expandedUserTab]) &&
                               localUserData.data[expandedUserTab].length > 0;

  // 新增检查：数据已加载但为空
  const isCurrentTabDataEmpty = localUserData && 
                              localUserData.userId === record.userId && 
                              Array.isArray(localUserData.data[expandedUserTab]) &&
                              localUserData.data[expandedUserTab].length === 0 &&
                              !isLoadingRef.current[expandedUserTab];

  // 使用本地状态渲染
  if (isLoading || !isCurrentTabDataLoaded) {
    // 如果已加载完成但数据为空，显示无数据提示
    if (isCurrentTabDataEmpty) {
      return (
        <div className="point-stats-user-detail-expanded">
          <Flex justify="space-between" align="center" className="point-stats-chart-controls">
            <Tabs
              className="point-stats-user-detail-tabs"
              activeKey={expandedUserTab}
              onChange={handleUserTabChange}
              style={{ marginRight: 'auto' }}
              items={userDetailTabItems}
            />

            <Flex gap={16}>
              <Select
                value={expandedUserChartType === 'pie' && isCurrentTabTotal ? 'bar' : expandedUserChartType}
                onChange={handleUserChartTypeChange}
                style={{ width: 120 }}
              >
                <Option value="bar">柱状图</Option>
                <Option value="line">折线图</Option>
                <Option value="pie" disabled={isCurrentTabTotal}>饼图</Option>
              </Select>

              {/* 保留日期选择器 */}
              {(expandedUserTab === 'DAILY' || expandedUserTab === 'TOTAL') && (
                <RangePicker
                  style={{ width: 300 }}
                  value={userDetailDateRange}
                  onChange={(dates) => {
                    handleUserDateRangeChange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null);
                  }}
                />
              )}
              {expandedUserTab === 'WEEKLY' && (
                <RangePicker
                  picker="week"
                  placeholder={["开始周", "结束周"]}
                  style={{ width: 300 }}
                  value={userDetailDateRange ? [dayjs(userDetailDateRange[0]), dayjs(userDetailDateRange[1])] : null}
                  onChange={(dates) => {
                    if (dates && dates[0] && dates[1]) {
                      const startWeek = dates[0].startOf('week');
                      const endWeek = dates[1].endOf('week');
                      handleUserDateRangeChange([startWeek, endWeek]);
                    } else {
                      handleUserDateRangeChange(null);
                    }
                  }}
                />
              )}
              {expandedUserTab === 'MONTHLY' && (
                <RangePicker
                  picker="month"
                  placeholder={["开始月", "结束月"]}
                  style={{ width: 300 }}
                  value={userDetailDateRange ? [dayjs(userDetailDateRange[0]), dayjs(userDetailDateRange[1])] : null}
                  onChange={(dates) => {
                    if (dates && dates[0] && dates[1]) {
                      const startMonth = dates[0].startOf('month');
                      const endMonth = dates[1].endOf('month');
                      handleUserDateRangeChange([startMonth, endMonth]);
                    } else {
                      handleUserDateRangeChange(null);
                    }
                  }}
                />
              )}
            </Flex>
          </Flex>

          {/* 显示无数据提示 */}
          <div style={{ height: 300, display: 'flex', justifyContent: 'center', alignItems: 'center', background: '#f0f2f5', borderRadius: 4, marginTop: 16 }}>
            <div style={{ textAlign: 'center', color: '#999' }}>
              <div style={{ fontSize: 16, marginBottom: 8 }}>没有对应的记录</div>
              <div>请尝试更改查询条件或时间范围</div>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="point-stats-user-detail-expanded">
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin>
            <div style={{ height: 300, padding: 30, background: '#f0f2f5' }}>
              <div>加载中...</div>
            </div>
          </Spin>
        </div>
      </div>
    );
  }

  const userMetrics = getUserMetrics();

  return (
    <div className="point-stats-user-detail-expanded">
      {/* Controls */}
      <Flex justify="space-between" align="center" className="point-stats-chart-controls">
        <Tabs
          className="point-stats-user-detail-tabs"
          activeKey={expandedUserTab}
          onChange={handleUserTabChange}
          style={{ marginRight: 'auto' }}
          items={userDetailTabItems}
        />

        <Flex gap={16}>
          <Select
            value={expandedUserChartType === 'pie' && isCurrentTabTotal ? 'bar' : expandedUserChartType}
            onChange={handleUserChartTypeChange}
            style={{ width: 120 }}
          >
            <Option value="bar">柱状图</Option>
            <Option value="line">折线图</Option>
            <Option value="pie" disabled={isCurrentTabTotal}>饼图</Option>
          </Select>

          {(expandedUserTab === 'DAILY' || expandedUserTab === 'TOTAL') && (
            <RangePicker
              style={{ width: 300 }}
              value={userDetailDateRange}
              onChange={(dates) => {
                console.log('[用户详情] 日期范围选择器选中(日/总):', dates);
                handleUserDateRangeChange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null);
              }}
            />
          )}
          {expandedUserTab === 'WEEKLY' && (
            <RangePicker
              picker="week"
              placeholder={["开始周", "结束周"]}
              style={{ width: 300 }}
              value={userDetailDateRange ? [dayjs(userDetailDateRange[0]), dayjs(userDetailDateRange[1])] : null}
              onChange={(dates) => {
                console.log('[用户详情] 周范围选择器选中:', dates);
                if (dates && dates[0] && dates[1]) {
                  const startWeek = dates[0].startOf('week');
                  const endWeek = dates[1].endOf('week');
                  handleUserDateRangeChange([startWeek, endWeek]);
                } else {
                  handleUserDateRangeChange(null);
                }
              }}
            />
          )}
          {expandedUserTab === 'MONTHLY' && (
            <RangePicker
              picker="month"
              placeholder={["开始月", "结束月"]}
              style={{ width: 300 }}
              value={userDetailDateRange ? [dayjs(userDetailDateRange[0]), dayjs(userDetailDateRange[1])] : null}
              onChange={(dates) => {
                console.log('[用户详情] 月范围选择器选中:', dates);
                if (dates && dates[0] && dates[1]) {
                  const startMonth = dates[0].startOf('month');
                  const endMonth = dates[1].endOf('month');
                  handleUserDateRangeChange([startMonth, endMonth]);
                } else {
                  handleUserDateRangeChange(null);
                }
              }}
            />
          )}
        </Flex>
      </Flex>

      {/* User metrics cards - 使用map渲染 - 在TOTAL类型时不显示 */}
      {!isCurrentTabTotal && (
        <>
          <Alert style={{marginBottom: 8}} message={'说明: 卡片上的数值为图表中显示的时间都数值加和(默认显示最近30个日/周/月)'}  />
          <div className="point-stats-metrics-row-expanded">
            {metricDefinitions.map((metric, index) => (
              <MetricCard
                key={metric.key}
                title={metric.title}
                value={userMetrics[metric.key as keyof typeof userMetrics]}
                metricType={metric.metricType}
                className={index === 0 ? 'point-stats-metric-card-expanded-first' : 'point-stats-metric-card-expanded'}
              />
            ))}
          </div>
        </>
      )}

      {/* User chart using ChartsUserPoint component */}
      <div style={{ marginTop: isCurrentTabTotal ? 16 : 0 }}>
        <ChartsUserPoint
          data={getCurrentTabData()}
          chartType={(expandedUserChartType === 'pie' && isCurrentTabTotal ? 'bar' : expandedUserChartType) as 'bar' | 'line' | 'pie'}
          statsType={expandedUserTab as 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'TOTAL'}
          height={isCurrentTabTotal ? 380 : 300}
        />
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // 自定义比较函数，只有在关键属性变化时才重新渲染
  // 当用户记录ID变化或展开状态变化才重新渲染
  const recordIdChanged = prevProps.record.id !== nextProps.record.id;
  const userIdChanged = prevProps.record.userId !== nextProps.record.userId;
  
  // 检查记录是否被展开或收起
  const prevExpanded = prevProps.expandedRowKeys.includes(prevProps.record.id as number);
  const nextExpanded = nextProps.expandedRowKeys.includes(nextProps.record.id as number);
  const expandStateChanged = prevExpanded !== nextExpanded;
  
  return !(recordIdChanged || userIdChanged || expandStateChanged);
});

// DataTable组件接口
interface DataTableProps {
  data: StatsUserPointVO[];
  loading: boolean;
  totalRecords: number;
  currentPage: number;
  pageSize: number;
  expandedRowKeys: number[];
  sortField: string;
  sortOrder?: 'ascend' | 'descend';
  onPageChange: (page: number, size?: number) => void;
  onTableChange: (pagination: any, filters: any, sorter: any) => void;
  onExpand: (expanded: boolean, record: StatsUserPointVO) => void;
  expandedRowRender: (record: StatsUserPointVO) => React.ReactNode;
  statsType: string;
}

// DataTable组件
const DataTable = memo(({
  data,
  loading,
  totalRecords,
  currentPage,
  pageSize,
  expandedRowKeys,
  onPageChange,
  onTableChange,
  onExpand,
  expandedRowRender,
  statsType
}: DataTableProps) => {
  // 格式化日期显示
  const formatStatsDate = (statsDate: string | undefined, type: string) => {
    if (!statsDate) return '-';
    
    try {
      const startDate = dayjs(statsDate);
      
      if (type === 'WEEKLY') {
        // 对于周统计，显示周一到周日的日期范围
        const endDate = startDate.add(6, 'day');
        return (
          <div style={{ whiteSpace: 'nowrap' }}>
            <div>{startDate.format('YYYY-MM-DD')} / </div>
            <div>{endDate.format('YYYY-MM-DD')}</div>
          </div>
        );
      } else if (type === 'MONTHLY') {
        // 对于月统计，显示月初到月末的日期范围
        const endDate = startDate.endOf('month');
        return (
          <div style={{ whiteSpace: 'nowrap' }}>
            <div>{startDate.format('YYYY-MM-DD')} / {endDate.format('DD')}</div>
            {/* <div style={{ paddingLeft: 12 }}></div> */}
          </div>
        );
      } else {
        // 日统计和总统计直接显示日期
        return startDate.format('YYYY-MM-DD');
      }
    } catch (e) {
      console.error('日期格式化错误:', e);
      return statsDate || '-';
    }
  };

  // 检查数据是否为空
  const noData = !loading && (!data || data.length === 0);

  return (
    <>
      <Table
        loading={loading}
        dataSource={data}
        rowKey="id"
        pagination={false}
        onChange={onTableChange}
        expandable={{
          expandedRowKeys,
          onExpand,
          expandedRowRender
        }}
        columns={[
          {
            title: '用户ID',
            dataIndex: 'userId',
            key: 'userId',
            width: 100,
          },
          {
            title: '昵称',
            dataIndex: 'nickName',
            key: 'nickName',
            width: 120,
          },
          {
            title: '销售/渠道商',
            dataIndex: 'distributorCorpName',
            key: 'distributorCorpName',
            width: 150,
            ellipsis: true,
          },
          {
            title: '缪斯点消耗',
            dataIndex: 'pointConsumed',
            key: 'pointConsumed',
            width: 120,
            sorter: true,
            render: (text) => (
              <span className="point-stats-metric-point">{text || 0}</span>
            ),
          },
          {
            title: '赠送点消耗',
            dataIndex: 'givePointConsumed',
            key: 'givePointConsumed',
            width: 120,
            sorter: true,
            render: (text) => (
              <span className="point-stats-metric-give-point">{text || 0}</span>
            ),
          },
          {
            title: '套内点消耗',
            dataIndex: 'modelPointConsumed',
            key: 'modelPointConsumed',
            width: 120,
            sorter: true,
            render: (text) => (
              <span className="point-stats-metric-model-point">{text || 0}</span>
            ),
          },
          {
            title: '充值金额',
            dataIndex: 'rechargeAmount',
            key: 'rechargeAmount',
            width: 120,
            sorter: true,
            render: (text) => (
              <span className="point-stats-metric-recharge">¥{text?.toFixed(2) || '0.00'}</span>
            ),
          },
          {
            title: '消耗率',
            key: 'consumptionRate',
            width: 100,
            sorter: false,
            render: (_, record) => {
              const rate = record.rechargeAmount && record.rechargeAmount > 0
                ? ((record.pointConsumed || 0) / record.rechargeAmount * 100).toFixed(2)
                : '0.00';
              return <span className="point-stats-metric-ratio">{rate}%</span>;
            },
          },
          {
            title: '统计日期',
            dataIndex: 'statsDate',
            key: 'statsDate',
            width: 150,
            render: (text) => formatStatsDate(text, statsType),
          },
          {
            title: '操作',
            key: 'action',
            width: 80,
            render: (_, record) => (
              <a onClick={() => {
                if (expandedRowKeys.includes(record.id as number)) {
                  onExpand(false, record);
                } else {
                  onExpand(true, record);
                }
              }}>
                {expandedRowKeys.includes(record.id as number) ? '收起' : '展开'}
              </a>
            )
          },
        ]}
        locale={{
          emptyText: (
            <div style={{ padding: '20px 0' }}>
              <div style={{ fontSize: 16, marginBottom: 8 }}>没有对应的记录</div>
              <div>请尝试更改查询条件或时间范围</div>
            </div>
          )
        }}
      />

      {/* 只有在有记录时才显示分页 */}
      {!noData && totalRecords > 0 && (
        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={totalRecords}
          onChange={onPageChange}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `共 ${total} 条`}
          style={{ marginTop: 16, textAlign: 'right' }}
        />
      )}
    </>
  );
});

// ChartSection组件接口
interface ChartSectionProps {
  activeTab: string;
  onTabChange: (activeKey: string) => void;
  chartType: string;
  onChartTypeChange: (type: string) => void;
  dateRange: [dayjs.Dayjs, dayjs.Dayjs] | null;
  onDateRangeChange: (dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => void;
  data: StatsUserPointVO[];
  loading: boolean;
  metrics: {
    totalPointConsumed: number;
    totalGivePointConsumed: number;
    totalModelPointConsumed: number;
    totalRechargeAmount: number;
    consumptionRate: number;
  };
  tabItems: { key: string; label: string }[];
}

// ChartSection组件
const ChartSection = memo(({
  activeTab,
  onTabChange,
  chartType,
  onChartTypeChange,
  dateRange,
  onDateRangeChange,
  data,
  loading,
  metrics,
  tabItems
}: ChartSectionProps) => {
  // 检查是否有数据
  const hasData = Array.isArray(data) && data.length > 0;
  // 是否为TOTAL类型
  const isTotalTab = activeTab === 'TOTAL';

  return (
    <div className="point-stats-stats-summary-content">
      <Flex justify="space-between" align="center" className="point-stats-chart-controls">
        <Tabs
          className="point-stats-upper-tabs"
          activeKey={activeTab}
          onChange={onTabChange}
          style={{ marginRight: 'auto' }}
          items={tabItems}
        />

        <Select
          value={chartType === 'pie' && isTotalTab ? 'bar' : chartType}
          onChange={onChartTypeChange}
          style={{ width: 120, marginRight: 16 }}
        >
          <Option value="bar">柱状图</Option>
          <Option value="line">折线图</Option>
          <Option value="pie" disabled={isTotalTab}>饼图</Option>
        </Select>

        {(activeTab === 'DAILY' || activeTab === 'TOTAL') && (
          <RangePicker
            style={{ width: 300 }}
            value={dateRange}
            onChange={(dates) => {
              console.log('主图表日期范围选择器选中(日/总):', dates);
              // 日期类型处理：确保类型正确
              onDateRangeChange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null);
            }}
          />
        )}
        {activeTab === 'WEEKLY' && (
          <RangePicker
            picker="week"
            placeholder={["开始周", "结束周"]}
            style={{ width: 300 }}
            value={dateRange ? [dayjs(dateRange[0]), dayjs(dateRange[1])] : null}
            onChange={(dates) => {
              console.log('主图表周范围选择器选中:', dates);
              if (dates && dates[0] && dates[1]) {
                const startWeek = dates[0].startOf('week');
                const endWeek = dates[1].endOf('week');
                onDateRangeChange([startWeek, endWeek]);
              } else {
                onDateRangeChange(null);
              }
            }}
          />
        )}
        {activeTab === 'MONTHLY' && (
          <RangePicker
            picker="month"
            placeholder={["开始月", "结束月"]}
            style={{ width: 300 }}
            value={dateRange ? [dayjs(dateRange[0]), dayjs(dateRange[1])] : null}
            onChange={(dates) => {
              console.log('主图表月范围选择器选中:', dates);
              if (dates && dates[0] && dates[1]) {
                const startMonth = dates[0].startOf('month');
                const endMonth = dates[1].endOf('month');
                onDateRangeChange([startMonth, endMonth]);
              } else {
                onDateRangeChange(null);
              }
            }}
          />
        )}
      </Flex>

      {/* Metrics Cards - 在TOTAL类型时不显示 */}
      {!isTotalTab && (
        <>
          <Alert message={'说明: 卡片上的数值为图表中显示的时间都数值加和(默认显示最近30个日/周/月)'}  />
          <div className="point-stats-metrics-row">
            {metricDefinitions.map((metric, index) => (
              <MetricCard
                key={metric.key}
                title={metric.title}
                value={metrics[metric.key as keyof typeof metrics]}
                metricType={metric.metricType}
                loading={loading}
                className={index === 0 ? 'point-stats-metric-card-first' : 'point-stats-metric-card'}
              />
            ))}
          </div>
        </>
      )}

      {/* Chart */}
      <div className="point-stats-chart-container" style={{ 
        // height: isTotalTab ? 'calc(100% - 10px)' : 'calc(100% - 184px)',
        // height: 300,
        // alignItems: isTotalTab ? 'flex-end': 'center',
        flexGrow: 1,
        position: 'relative' 
      }}>
        {loading ? (
          <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Spin>
              <div style={{ padding: 50, textAlign: 'center' }}>
                <div>加载中...</div>
              </div>
            </Spin>
          </div>
        ) : !hasData ? (
          <div style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, display: 'flex', justifyContent: 'center', background: '#f0f2f5', alignItems: 'center', borderRadius: 4 }}>
            <div style={{ textAlign: 'center', color: '#999' }}>
              <div style={{ fontSize: 16, marginBottom: 8 }}>没有对应的记录</div>
              <div>请尝试更改查询条件或时间范围</div>
            </div>
          </div>
        ) : (
          <ChartsUserPoint
            data={data}
            chartType={(chartType === 'pie' && isTotalTab ? 'bar' : chartType) as 'bar' | 'line' | 'pie'}
            statsType={activeTab as 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'TOTAL'}
            height="100%"
          />
        )}
      </div>
    </div>
  );
});

// 将原组件拆分为两个子组件和主组件
// 1. 主图表组件
const SummaryChartSection: React.FC<{
  summaryActiveTab: string,
  setSummaryActiveTab: (tab: string) => void,
  summaryDateRange: [dayjs.Dayjs, dayjs.Dayjs] | null,
  setSummaryDateRange: (range: [dayjs.Dayjs, dayjs.Dayjs] | null) => void,
  setInitialTabChange: (value: boolean) => void
}> = ({ 
  summaryActiveTab, 
  setSummaryActiveTab, 
  summaryDateRange, 
  setSummaryDateRange,
  setInitialTabChange 
}) => {
  const [chartType, setChartType] = useState<string>('bar');
  const [summaryLoading, setSummaryLoading] = useState<boolean>(false);
  const [summaryData, setSummaryData] = useState<StatsUserPointVO[]>([]);
  const [metrics, setMetrics] = useState({
    totalPointConsumed: 0,
    totalGivePointConsumed: 0,
    totalModelPointConsumed: 0,
    totalRechargeAmount: 0,
    consumptionRate: 0
  });
  
  const DEFAULT_CHARTS_SIZE = 30;
  
  // 定义上方统计页签项
  const summaryTabItems = [
    { key: 'DAILY', label: '日统计' },
    { key: 'WEEKLY', label: '周统计' },
    { key: 'MONTHLY', label: '月统计' },
    { key: 'TOTAL', label: '总统计' }
  ];
  
  // 根据统计类型和日期范围计算pageSize
  const calculatePageSize = (statsType: string, dateRange: [dayjs.Dayjs, dayjs.Dayjs] | null): number => {
    console.log('[表格] 计算pageSize:', { statsType, dateRange });
    if (!dateRange) return DEFAULT_CHARTS_SIZE;
    switch(statsType) {
      case 'DAILY':
      case 'TOTAL':
        return dateRange ? dateRange[1].diff(dateRange[0], 'days') + 1 : DEFAULT_CHARTS_SIZE;
      case 'WEEKLY':
        const weekDaysBetween = dateRange ? dateRange[1].diff(dateRange[0], 'days') : 7;
        return Math.ceil(weekDaysBetween / 7);
      case 'MONTHLY':
        const monthDaysBetween = dateRange ? dateRange[1].diff(dateRange[0], 'days') : 30;
        return Math.ceil(monthDaysBetween / 30);
      default:
        return DEFAULT_CHARTS_SIZE;
    }
  };
  
  // 主图表数据加载函数
  const fetchSummaryData = useCallback(async (dateRange?: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
    console.log('[主图表] 开始加载数据:', { statsType: summaryActiveTab, dateRange });
    setSummaryLoading(true);
    try {
      const params: any = {
        statsType: summaryActiveTab,
        userId: 0, // 0 means all users aggregated
        pageNum: 1,
        pageSize: DEFAULT_CHARTS_SIZE,
        orderBy: 'stats_date desc'
      };
      
      // 使用传入的日期范围参数，如果没有则使用状态中的日期范围
      const actualDateRange = dateRange !== undefined ? dateRange : summaryDateRange;
      
      // Add date range if selected
      if (actualDateRange && actualDateRange.length === 2) {
        params.statsDateLower = actualDateRange[0].format('YYYY-MM-DD');
        params.statsDateUpper = actualDateRange[1].format('YYYY-MM-DD');
      }
      
      // 使用calculatePageSize函数计算pageSize
      params.pageSize = calculatePageSize(summaryActiveTab, actualDateRange);

      console.log('[主图表] 请求参数:', params);
      const response = await statsUserPoint(params);
      const result = formatApiResponse(response);
      
      if (result && result.list) {
        console.log(`[主图表] 数据加载完成，共${result.list.length}条数据`);
        setSummaryData(result.list);
        
        // Calculate metrics
        const totalPointConsumed = result.list.reduce((sum, item) => sum + (item.pointConsumed || 0), 0);
        const totalGivePointConsumed = result.list.reduce((sum, item) => sum + (item.givePointConsumed || 0), 0);
        const totalModelPointConsumed = result.list.reduce((sum, item) => sum + (item.modelPointConsumed || 0), 0);
        const totalRechargeAmount = result.list.reduce((sum, item) => sum + (item.rechargeAmount || 0), 0);
        const consumptionRate = totalRechargeAmount > 0
          ? (totalPointConsumed / totalRechargeAmount) * 100
          : 0;
          
        setMetrics({
          totalPointConsumed,
          totalGivePointConsumed,
          totalModelPointConsumed,
          totalRechargeAmount,
          consumptionRate
        });
      }
    } catch (error) {
      console.error('[主图表] 获取数据失败:', error);
      message.error('获取数据失败，请重试');
    } finally {
      setSummaryLoading(false);
    }
  }, [summaryActiveTab, summaryDateRange, DEFAULT_CHARTS_SIZE]);
  
  // 处理主图表tab变化
  const handleSummaryTabChange = useCallback((activeKey: string) => {
    console.log('[主图表] Tab切换:', activeKey);
    setSummaryActiveTab(activeKey);
    // 清空日期选择器
    setSummaryDateRange(null);
    // 标记这是一次手动切换，应该重置日期
    setInitialTabChange(true);
  }, [setSummaryActiveTab, setSummaryDateRange, setInitialTabChange]);

  // 处理主图表图表类型变化
  const handleSummaryChartTypeChange = useCallback((type: string) => {
    console.log('[主图表] 图表类型切换:', type);
    setChartType(type);
  }, []);
  
  // 处理主图表日期范围变化
  const handleSummaryDateChange = useCallback((dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
    console.log('[主图表] 日期范围变更:', dates);
    setSummaryDateRange(dates);
    // 直接触发请求
    fetchSummaryData(dates);
  }, [fetchSummaryData, setSummaryDateRange]);
  
  // 首次加载数据
  useEffect(() => {
    console.log('[主图表] 首次加载或Tab变化');
    fetchSummaryData();
  }, [fetchSummaryData, summaryActiveTab]);
  
  return (
    <div className="point-stats-stats-summary-section">
      <ChartSection
        activeTab={summaryActiveTab}
        onTabChange={handleSummaryTabChange}
        chartType={chartType}
        onChartTypeChange={handleSummaryChartTypeChange}
        dateRange={summaryDateRange}
        onDateRangeChange={handleSummaryDateChange}
        data={summaryData}
        loading={summaryLoading}
        metrics={metrics}
        tabItems={summaryTabItems}
      />
    </div>
  );
};

// 修改主组件
const StatsUserPoint: React.FC = () => {
  // Check if user is admin
  const userInfo = getUserInfo();
  const isAdmin = userInfo && userInfo.roleType === 'ADMIN';

  // State for the upper section (summary)
  const [summaryActiveTab, setSummaryActiveTab] = useState<string>('DAILY');
  const [summaryDateRange, setSummaryDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  
  // 添加状态跟踪是否是初始/手动Tab切换
  const [initialTabChange, setInitialTabChange] = useState(true);

  // If not admin, show no permission message
  if (!isAdmin) {
    return (
      <PageContainer>
        <Card>
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            您没有访问此页面的权限
          </div>
        </Card>
      </PageContainer>
    );
  }

  return (
    <PageContainer title="">
      <div className="point-stats-user-point-stats-container">
        {/* Upper section - Summary Charts */}
        <SummaryChartSection 
          summaryActiveTab={summaryActiveTab}
          setSummaryActiveTab={setSummaryActiveTab}
          summaryDateRange={summaryDateRange}
          setSummaryDateRange={setSummaryDateRange}
          setInitialTabChange={setInitialTabChange}
        />

        {/* Lower section - User Details */}
        <UserDetailSection
          summaryActiveTab={summaryActiveTab}
          initialTabChange={initialTabChange}
          setInitialTabChange={setInitialTabChange}
        />
      </div>
    </PageContainer>
  );
};

// 数据表格组件
const UserDetailSection: React.FC<{
  summaryActiveTab: string,
  initialTabChange: boolean,
  setInitialTabChange: (value: boolean) => void
}> = ({ 
  summaryActiveTab, 
  initialTabChange, 
  setInitialTabChange 
}) => {
  const DEFAULT_CHARTS_SIZE = 30;
  const [userDetailData, setUserDetailData] = useState<StatsUserPointVO[]>([]);
  const [userDetailLoading, setUserDetailLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const [expandedRowKeys, setExpandedRowKeys] = useState<number[]>([]);
  const [sortField, setSortField] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | undefined>(undefined);
  
  // 创建一个标志，用于跟踪是否应该触发主图表请求
  const preventSummaryRequest = useRef(false);
  
  // 根据统计类型设置默认日期
  const getDefaultDateByStatsType = useCallback((statsType: string): dayjs.Dayjs => {
    switch (statsType) {
      case 'WEEKLY':
        return dayjs().startOf('week');
      case 'MONTHLY':
        return dayjs().startOf('month');
      case 'DAILY':
      case 'TOTAL':
      default:
        return dayjs().subtract(1, 'day');
    }
  }, []);
  
  // 创建初始筛选状态函数
  const createInitialFilterState = useCallback((statsType: string): FilterState => ({
    userId: null,
    nickName: '',
    distributorCorpName: '',
    pointRange: [null, null],
    rechargeRange: [null, null],
    selectedDate: getDefaultDateByStatsType(statsType),
    dateRange: null
  }), [getDefaultDateByStatsType]);
  
  // 使用单一状态管理所有筛选条件
  const [filterState, setFilterState] = useState<FilterState>(() => createInitialFilterState(summaryActiveTab));
  
  // 部分更新筛选条件的辅助函数
  const updateFilter = useCallback((key: keyof FilterState, value: any) => {
    setFilterState(prev => ({ ...prev, [key]: value }));
  }, []);
  
  // 用户详情表格组件的处理函数
  const fetchUserDetailData = useCallback(async () => {
    console.log('[表格] 开始加载数据:', { page: currentPage, pageSize, statsType: summaryActiveTab });
    
    setUserDetailLoading(true);
    try {
      const params: any = {
        pageNum: currentPage,
        pageSize: pageSize,
        statsType: summaryActiveTab // 使用与主图表相同的统计类型
      };

      // 添加排序参数
      if (sortField && sortOrder) {
        const direction = sortOrder === 'ascend' ? 'asc' : 'desc';
        // 将驼峰式字段名转换为下划线式
        const dbField = sortField.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        params.orderBy = `${dbField} ${direction}`;
      } else {
        params.orderBy = 'stats_date desc';
      }

      // Add filters
      if (filterState.userId) {
        params.userId = filterState.userId;
      }

      // 添加昵称和渠道商过滤
      if (filterState.nickName) {
        params.nickNameLike = filterState.nickName;
      }

      if (filterState.distributorCorpName) {
        params.distributorCorpNameLike = filterState.distributorCorpName;
      }

      if (filterState.pointRange[0] !== null) {
        params.pointConsumedLower = filterState.pointRange[0];
      }

      if (filterState.pointRange[1] !== null) {
        params.pointConsumedUpper = filterState.pointRange[1];
      }

      if (filterState.rechargeRange[0] !== null) {
        params.rechargeAmountLower = filterState.rechargeRange[0];
      }

      if (filterState.rechargeRange[1] !== null) {
        params.rechargeAmountUpper = filterState.rechargeRange[1];
      }

      // 添加选中日期参数
      if (filterState.selectedDate) {
        // 根据统计类型设置不同的日期参数
        // 对于周选择器和月选择器，使用其第一天的日期
        let dateToUse;
        if (summaryActiveTab === 'WEEKLY') {
          dateToUse = filterState.selectedDate.startOf('week');
        } else if (summaryActiveTab === 'MONTHLY') {
          dateToUse = filterState.selectedDate.startOf('month');
        } else {
          dateToUse = filterState.selectedDate;
        }
        params.statsDate = dateToUse.format('YYYY-MM-DD');
      }
      // 添加dateRange参数
      if (filterState.dateRange) {
        params.statsDateLower = filterState.dateRange[0];
        params.statsDateUpper = filterState.dateRange[1];
      }

      console.log('[表格] 请求参数:', params);
      const response = await statsUserPoint(params);
      const result = formatApiResponse(response);

      if (result) {
        console.log(`[表格] 数据加载完成，共${result.list?.length || 0}条数据`);
        setUserDetailData(result.list || []);
        setTotalRecords(result.totalCount || 0);
      }
    } catch (error) {
      console.error('[表格] 获取数据失败:', error);
      message.error('获取用户数据失败，请重试');
    } finally {
      setUserDetailLoading(false);
    }
  }, [currentPage, pageSize, sortField, sortOrder, filterState, summaryActiveTab]);
  
  // 表格筛选处理函数
  const handleTableChange = useCallback((pagination: any, filters: any, sorter: any) => {
    console.log('[表格] 表格变化:', { sorter });
    
    // 处理排序
    if (sorter && sorter.columnKey) {
      setSortField(sorter.columnKey);
      setSortOrder(sorter.order);
    } else {
      setSortField('');
      setSortOrder(undefined);
    }
    
    // 重置到第一页
    setCurrentPage(1);
    
    // 直接触发加载
    fetchUserDetailData();
  }, [fetchUserDetailData]);

  // 重置按钮点击处理
  const handleReset = useCallback(() => {
    console.log('[表格] 重置筛选条件');
    
    // 重置所有筛选条件
    setFilterState(createInitialFilterState(summaryActiveTab));
    setInitialTabChange(true);
    setCurrentPage(1);
    
    // 直接触发数据请求
    fetchUserDetailData();
  }, [summaryActiveTab, createInitialFilterState, fetchUserDetailData, setInitialTabChange]);
  
  // 创建防抖处理函数 - 直接触发请求
  const debouncedSearch = useCallback(
    debounce((field: string, value: string) => {
      console.log('[筛选] 搜索字段变化:', field, value);
      
      // 根据字段类型设置对应的状态
      if (field === 'nickName') {
        updateFilter('nickName', value);
      } else if (field === 'distributorCorpName') {
        updateFilter('distributorCorpName', value);
      }
      
      // 重置回第一页
      setCurrentPage(1);
      
      // 直接触发表格数据加载
      fetchUserDetailData();
    }, 500),
    [updateFilter, setCurrentPage, fetchUserDetailData]
  );

  // 处理输入框变化
  const handleInputChange = useCallback((field: string, value: string) => {
    debouncedSearch(field, value);
  }, [debouncedSearch]);
  
  // 根据统计类型和日期范围计算pageSize
  const calculatePageSize = (statsType: string, dateRange: [dayjs.Dayjs, dayjs.Dayjs] | null): number => {
    console.log('[表格] 计算pageSize:', { statsType, dateRange });
    if (!dateRange) return DEFAULT_CHARTS_SIZE;
    switch(statsType) {
      case 'DAILY':
      case 'TOTAL':
        return dateRange ? dateRange[1].diff(dateRange[0], 'days') + 1 : DEFAULT_CHARTS_SIZE;
      case 'WEEKLY':
        const weekDaysBetween = dateRange ? dateRange[1].diff(dateRange[0], 'days') : 7;
        return Math.ceil(weekDaysBetween / 7);
      case 'MONTHLY':
        const monthDaysBetween = dateRange ? dateRange[1].diff(dateRange[0], 'days') : 30;
        return Math.ceil(monthDaysBetween / 30);
      default:
        return DEFAULT_CHARTS_SIZE;
    }
  };
  
  // 用户详情数据加载函数 - 独立于主图表
  const memoizedFetchUserStatsDetail = useCallback(async (userId: number, statsType: string = 'DAILY', dateRange?: [dayjs.Dayjs, dayjs.Dayjs]) => {
    console.log('[用户详情] fetchUserStatsDetail 被调用:', { userId, statsType, dateRange });
    try {
      const params: any = {
        userId,
        statsType,
        pageNum: 1,
        pageSize: DEFAULT_CHARTS_SIZE,
        orderBy: 'stats_date desc'
      };

      // 添加日期范围过滤条件
      if (dateRange && dateRange.length === 2) {
        params.statsDateLower = dateRange[0].format('YYYY-MM-DD');
        params.statsDateUpper = dateRange[1].format('YYYY-MM-DD');
      }
      // 使用calculatePageSize函数计算pageSize
      params.pageSize = calculatePageSize(statsType, dateRange || null);

      const response = await statsUserPoint(params);
      const result = formatApiResponse(response);
      return result?.list || [];
    } catch (error) {
      console.error('[用户详情] 获取用户详细统计数据失败:', error);
      message.error('获取用户详细统计数据失败');
      return [];
    }
  }, [DEFAULT_CHARTS_SIZE]);

  // 使用记忆化的方式创建expandedRowRender函数，避免每次父组件渲染都重新创建
  const expandedRowRender = useCallback((record: StatsUserPointVO) => {
    return (
      <ExpandedContent
        record={record}
        fetchUserStatsDetail={memoizedFetchUserStatsDetail} 
        expandedRowKeys={expandedRowKeys}
      />
    );
  }, [memoizedFetchUserStatsDetail, expandedRowKeys]);

  // 自定义展开/收起事件
  const handleExpand = useCallback((expanded: boolean, record: StatsUserPointVO) => {
    if (expanded) {
      setExpandedRowKeys([record.id as number]);
    } else {
      setExpandedRowKeys([]);
    }
  }, []);
  
  // 首次加载和统计类型变化时加载数据
  useEffect(() => {
    console.log('[表格] 首次加载或统计类型变化');
    fetchUserDetailData();
  }, [fetchUserDetailData, summaryActiveTab]);
  
  // 定义上方统计页签项
  const summaryTabItems = [
    { key: 'DAILY', label: '日统计' },
    { key: 'WEEKLY', label: '周统计' },
    { key: 'MONTHLY', label: '月统计' },
    { key: 'TOTAL', label: '总统计' }
  ];
  
  return (
    <div className="point-stats-stats-detail-section">
      {/* Filter Card */}
      <FilterCard
        filterState={filterState}
        updateFilter={(key, value) => {
          console.log('[筛选] 更新字段:', key, value);
          updateFilter(key, value);
        }}
        onReset={handleReset}
        onFilter={() => {
          console.log('[筛选] 点击筛选按钮');
          fetchUserDetailData();
        }}
        loading={userDetailLoading}
        statsType={summaryActiveTab}
        statsTypeLabel={summaryTabItems.find(item => item.key === summaryActiveTab)?.label || ''}
        setInitialTabChange={setInitialTabChange}
        handleInputChange={handleInputChange}
      />

      {/* User Data Table */}
      <DataTable
        data={userDetailData}
        loading={userDetailLoading}
        totalRecords={totalRecords}
        currentPage={currentPage}
        pageSize={pageSize}
        expandedRowKeys={expandedRowKeys}
        sortField={sortField}
        sortOrder={sortOrder}
        onPageChange={(page, size) => {
          setCurrentPage(page);
          setPageSize(size || pageSize);
        }}
        onTableChange={handleTableChange}
        onExpand={handleExpand}
        expandedRowRender={expandedRowRender}
        statsType={summaryActiveTab}
      />
    </div>
  );
};

export default StatsUserPoint;
