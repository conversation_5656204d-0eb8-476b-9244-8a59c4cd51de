.point-stats-user-point-stats-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 8px 8px 0 8px;
}

.point-stats-stats-summary-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  height: auto;
}

.point-stats-stats-summary-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: white;
  border-radius: 8px;
  padding: 0 16px;
  height: 508.85px;
}

.point-stats-stats-detail-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  min-height: 60vh;
}

.point-stats-chart-container {
  width: 100%;
  height: 25vh;
  min-height: 250px;
}

.point-stats-stats-filter-row {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.point-stats-stats-card {
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.point-stats-stats-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.point-stats-filter-card {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 4px 16px;
  margin-bottom: 0;
  .ant-card-body {
    padding: 16px 24px !important;
  }
}

.point-stats-metrics-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.point-stats-metrics-row-expanded {
  display: flex;
  width: 100%;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.point-stats-metric-card {
  flex: 1;
  min-width: 150px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.point-stats-metric-card-first {
  flex: 1;
  min-width: 150px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.point-stats-metric-card-expanded {
  flex: 1;
  min-width: 150px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  .ant-card-body {
    padding: 0 24px !important;
  }
}

.point-stats-metric-card-expanded-first {
  flex: 1;
  min-width: 150px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  .ant-card-body {
    padding: 0 24px !important;
  }
}

.point-stats-metric-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.point-stats-metric-card-expanded:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.point-stats-metric-card-expanded-first:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.point-stats-chart-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.point-stats-user-detail-expanded {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 0 16px;
  //margin: 8px 0;
}

// Different colors for metrics
.point-stats-metric-point {
  color: #00000073;  // 粉红色马卡龙
  text-shadow: 
    0 0 2px #fff,
    0 0 4px #fff,
    0 0 6px #F6C3CD,
    0 0 10px #F6C3CD,
    0 0 15px #F6C3CD;
  font-weight: 600;
  padding: 2px 6px;
}

.point-stats-metric-give-point {
  color: #00000073;  // 薄荷绿马卡龙
  text-shadow: 
    0 0 2px #fff,
    0 0 4px #fff,
    0 0 6px #B4E1C2,
    0 0 10px #B4E1C2,
    0 0 15px #B4E1C2;
  font-weight: 600;
  padding: 2px 6px;
}

.point-stats-metric-exp-point {
  color: #00000073;  // 蓝莓色马卡龙
  text-shadow: 
    0 0 2px #fff,
    0 0 4px #fff,
    0 0 6px #C3C7E5,
    0 0 10px #C3C7E5,
    0 0 15px #C3C7E5;
  font-weight: 600;
  padding: 2px 6px;
}

.point-stats-metric-model-point {
  color: #00000073;  // 杏仁色马卡龙
  text-shadow: 
    0 0 2px #fff,
    0 0 4px #fff,
    0 0 6px #F5D2AF,
    0 0 10px #F5D2AF,
    0 0 15px #F5D2AF;
  font-weight: 600;
  padding: 2px 6px;
}

.point-stats-metric-recharge {
  color: #00000073;  // 开心果色马卡龙
  text-shadow:
          0 0 2px #fff,
          0 0 4px #fff,
          0 0 6px #DBE8B3,
          0 0 10px #DBE8B3,
          0 0 15px #DBE8B3;
  font-weight: 600;
  padding: 2px 6px;
}

.point-stats-metric-ratio {
  color: #00000073;  // 覆盆子色马卡龙
  text-shadow:
          0 0 2px #fff,
          0 0 4px #fff,
          0 0 6px #F4C3B8,
          0 0 10px #F4C3B8,
          0 0 15px #F4C3B8;
  font-weight: 600;
  padding: 2px 6px;
}

.point-stats-upper-tabs .ant-tabs-tab {
  min-width: 80px;
  justify-content: center;
}

.point-stats-user-detail-expanded tr.expanded-row td {
  background-color: #f9f9f9;
}

// Make sure the tabs in each section are visually distinct
.point-stats-upper-tabs .ant-tabs-tab {
  font-size: 16px;
}

.point-stats-user-detail-tabs .ant-tabs-tab {
  font-size: 14px;
}

