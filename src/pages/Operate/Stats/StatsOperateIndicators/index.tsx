import React, { useEffect, useState, useCallback, memo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Card, DatePicker, Table, Pagination,
  Button, Flex, message, Input, Tooltip, Empty, Spin, Radio, Select,
  Modal,
} from 'antd';
import {
  statsOperateIndicators,
  StatsOperateIndicatorsVO as ApiStatsOperateIndicatorsVO,
  download,
} from '@/services/DataController';
import './index.less';
import dayjs from 'dayjs';
import debounce from 'lodash/debounce';
import { DownloadOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { downloadExcel } from '@/utils/downloadUtils';

// 格式化百分比值的公共函数
const formatPercentage = (value: string | undefined) => {
  if (!value) return '0%';
  return value.includes('%') ? value : `${value}%`;
};

// 类型检查函数
function isPageInfo(response: any): response is PageInfo<ApiStatsOperateIndicatorsVO> {
  return response &&
    typeof response === 'object' &&
    Array.isArray(response.list) &&
    typeof response.totalCount === 'number';
}

// 转换API响应为统一格式
function formatApiResponse(response: any): PageInfo<ApiStatsOperateIndicatorsVO> {
  if (isPageInfo(response)) {
    // 添加前端需要的parentId字段
    const listWithParentId = response.list.map(item => ({
      ...item,
      // 根据extInfo或其他属性判断是否为企业记录，为企业记录添加parentId=0，否则为null
      parentId: item.extInfo && (item.extInfo as any).isCompany ? 0 : null,
    }));
    return {
      list: listWithParentId,
      totalCount: response.totalCount,
    };
  } else if (Array.isArray(response)) {
    // 添加前端需要的parentId字段
    const listWithParentId = response.map(item => ({
      ...item,
      // 根据extInfo或其他属性判断是否为企业记录，为企业记录添加parentId=0，否则为null
      parentId: item.extInfo && (item.extInfo as any).isCompany ? 0 : null,
    }));
    return {
      list: listWithParentId,
      totalCount: response.length,
    };
  } else {
    return {
      list: [],
      totalCount: 0,
    };
  }
}

// 定义页面信息接口
export interface PageInfo<T> {
  list: T[];
  totalCount: number;
}

// 扩展运营指标VO类型
interface ExtendedStatsOperateIndicatorsVO extends ApiStatsOperateIndicatorsVO {
  // 兼容字段
  key?: string | number;
  // 确保包含前端需要的parentId字段
  parentId?: number | null;
}

// 定义分页响应接口
interface PageResponse<T> {
  list: T[];
  hasNextPage: boolean;
  size: number;
  totalCount: number;
}

// 定义筛选条件状态接口
interface FilterState {
  name: string;  // 企业名称
  userId: number | null;  // 修改parentId为userId
  selectedDate: dayjs.Dayjs | null;
  dateRange: [string, string] | null;
}

// 定义排序配置接口
interface SortConfig {
  field: string;
  order: 'ASC' | 'DESC';
}

// FilterCard组件接口
interface FilterCardProps {
  filterState: FilterState;
  updateFilter: (key: keyof FilterState, value: any) => void;
  onFilter: () => void;
  loading: boolean;
  statsType: string;
  statsTypeLabel: string;
  sortConfig: SortConfig;
  updateSortConfig: (config: SortConfig) => void;
  activeRoleType: string; // 添加activeRoleType属性
}

// FilterCard组件
const FilterCard = memo(({
                           filterState,
                           updateFilter,
                           onFilter,
                           loading,
                           statsType,
                           statsTypeLabel,
                           sortConfig,
                           updateSortConfig,
                           activeRoleType,
                         }: FilterCardProps) => {
  // 使用本地状态来存储输入框的值，避免直接触发父组件状态更新导致的卡顿
  const [localName, setLocalName] = useState<string>(filterState.name);
  const [localUserId, setLocalUserId] = useState<string>(filterState.userId !== null ? String(filterState.userId) : '');

  // 创建名称输入防抖函数
  const debouncedNameChange = useCallback(
    debounce((value: string) => {
      updateFilter('name', value);
    }, 500),
    [updateFilter],
  );

  // 创建企业ID输入防抖函数
  const debouncedUserIdChange = useCallback(
    debounce((value: string) => {
      // 将字符串转换为数字或null
      const numValue = value === '' ? null : Number(value);
      updateFilter('userId', numValue);
    }, 500),
    [updateFilter],
  );

  // 当filterState变化时，同步更新本地状态
  useEffect(() => {
    setLocalName(filterState.name);
    setLocalUserId(filterState.userId !== null ? String(filterState.userId) : '');
  }, [filterState]);

  // 创建日期选择防抖函数
  const debouncedDateChange = useCallback(
    debounce((date: dayjs.Dayjs | null, formatFunc: (date: dayjs.Dayjs) => [string, string] | null) => {
      updateFilter('selectedDate', date);
      if (date) {
        const dateRange = formatFunc(date);
        updateFilter('dateRange', dateRange);
      } else {
        updateFilter('selectedDate', null);
        updateFilter('dateRange', null);
      }
    }, 500),
    [updateFilter],
  );

  // 处理名称输入
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // 立即更新本地状态，使输入框响应迅速
    setLocalName(value);
    // 防抖更新父组件状态
    debouncedNameChange(value);
  };

  // 处理企业ID输入
  const handleUserIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // 验证输入是否为数字或空
    if (value === '' || /^\d+$/.test(value)) {
      // 立即更新本地状态
      setLocalUserId(value);
      // 防抖更新父组件状态
      debouncedUserIdChange(value);
    }
  };

  // 获取适用于当前角色类型的排序字段选项
  const getSortFieldOptions = () => {

    // 工程师角色特有字段
    const engineerFields = [
      { label: '客户总数', value: 'customer_total_count' },
      { label: '转换量', value: 'customer_conversion_count' },
      { label: '消耗总数', value: 'customer_consumption_points' },
      { label: '交付服装量', value: 'delivery_clothing_count' },
      { label: '待保护客户', value: 'customer_protection_metrics' },
    ];

    // 审核员角色特有字段
    const reviewerFields = [
      { label: '审核服装量', value: 'approve_clothing_count' },
      { label: '服装返点率', value: 'garment_rebate_rate' },
    ];

    return [
      ...(statsTypeLabel.includes('工程师') ? engineerFields : []),
      ...(statsTypeLabel.includes('审核员') ? reviewerFields : []),
    ];
  };

  const alterDownload = async () => {
    // 弹窗提示用户是否下载，弹出中展示统计日期以及统计周期类型，美化样式
    const content = (
      <div style={{ padding: '16px 0' }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          marginBottom: '12px',
          fontSize: '14px',
        }}>
                    <span style={{
                      fontWeight: 'bold',
                      color: '#666',
                      minWidth: '100px',
                      marginRight: '8px',
                    }}>
                        统计日期:
                    </span>
          <span style={{
            color: '#1890ff',
            fontWeight: '500',
            backgroundColor: '#f0f8ff',
            padding: '4px 8px',
            borderRadius: '4px',
            border: '1px solid #d9ecff',
          }}>
                        {filterState.dateRange ? `${filterState.dateRange[0]} ~ ${filterState.dateRange[1]}` : '未选择'}
                    </span>
        </div>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          fontSize: '14px',
        }}>
                    <span style={{
                      fontWeight: 'bold',
                      color: '#666',
                      minWidth: '100px',
                      marginRight: '8px',
                    }}>
                        统计周期类型:
                    </span>
          <span style={{
            color: '#52c41a',
            fontWeight: '500',
            backgroundColor: '#f6ffed',
            padding: '4px 8px',
            borderRadius: '4px',
            border: '1px solid #b7eb8f',
          }}>
                        {statsType === 'WEEKLY' ? '周统计' :
                          statsType === 'MONTHLY' ? '月统计' :
                            statsType === 'QUARTERLY' ? '季度统计' : '总统计'}
                    </span>
        </div>
      </div>
    );

    Modal.confirm({
      title: '导出信息确认',
      content,
      centered: true,
      onOk: () => {
        return new Promise(async (resolve, reject) => {
          try {
            // 构建下载参数，与查询参数保持一致
            const params: any = {
              statsType: statsType,
              roleType: activeRoleType, // 添加角色类型参数
            };

            // 添加排序参数
            params.orderBy = `${sortConfig.field} ${sortConfig.order}`;

            // 添加企业名称筛选
            if (filterState.name) {
              params.name = filterState.name;
            }

            // 添加运营ID筛选
            if (filterState.userId !== null) {
              params.userId = filterState.userId;
            }

            // 添加日期筛选
            if (filterState.dateRange) {
              params.statsDate = filterState.dateRange[0];
            }

            // 调用下载接口
            const blob = await download(params);

            // 生成文件名
            const statsTypeText = statsType === 'WEEKLY' ? '周统计' :
              statsType === 'MONTHLY' ? '月统计' :
                statsType === 'QUARTERLY' ? '季度统计' : '总统计';
            const filename = `运营指标_${statsTypeText}_.xlsx`;

            // 下载文件
            downloadExcel(blob, filename);
            message.success('导出成功');
            resolve(undefined);
          } catch (error) {
            console.error('导出失败:', error);
            message.error('导出失败，请重试');
            reject(error);
          }
        });
      },
    });
  };

  return (
    <Card className="operate-stats-filter-card">
      <Flex gap={16} wrap="wrap" align="flex-start">
        <Flex vertical gap={0} style={{ width: '100%' }}>
          <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
            当前统计类型: <span style={{ color: '#1890ff' }}>{statsTypeLabel}</span>
          </div>
        </Flex>

        <Flex vertical gap={8}>
          <span>运营ID:</span>
          <Input
            placeholder="输入运营ID"
            style={{ width: 150 }}
            value={localUserId}
            onChange={handleUserIdChange}
            allowClear
          />
        </Flex>

        <Flex vertical gap={8}>
          <span>运营名称:</span>
          <Input
            placeholder="输入运营名称"
            style={{ width: 150 }}
            value={localName}
            onChange={handleNameChange}
            allowClear
          />
        </Flex>

        <Flex vertical gap={8}>
          {statsType !== 'TOTAL' && (
            <>
              <span>查询日期:</span>
              {statsType === 'WEEKLY' && (
                <DatePicker
                  picker="week"
                  placeholder="选择周"
                  style={{ width: 150 }}
                  value={filterState.selectedDate}
                  onChange={(date) => {
                    if (date) {
                      debouncedDateChange(date, (selectedDate) => {
                        const weekStart = selectedDate.startOf('week').format('YYYY-MM-DD');
                        const weekEnd = selectedDate.endOf('week').format('YYYY-MM-DD');
                        return [weekStart, weekEnd];
                      });
                    } else {
                      // 当用户清空日期时
                      debouncedDateChange(null, () => null as any);
                    }
                  }}
                  allowClear
                />
              )}
              {statsType === 'MONTHLY' && (
                <DatePicker
                  picker="month"
                  placeholder="选择月"
                  style={{ width: 150 }}
                  value={filterState.selectedDate}
                  onChange={(date) => {
                    if (date) {
                      debouncedDateChange(date, (selectedDate) => {
                        const monthStart = selectedDate.startOf('month').format('YYYY-MM-DD');
                        const monthEnd = selectedDate.endOf('month').format('YYYY-MM-DD');
                        return [monthStart, monthEnd];
                      });
                    } else {
                      debouncedDateChange(null, () => null as any);
                    }
                  }}
                  allowClear
                />
              )}
              {statsType === 'QUARTERLY' && (
                <DatePicker
                  picker="quarter"
                  placeholder="选择季度"
                  style={{ width: 150 }}
                  value={filterState.selectedDate}
                  onChange={(date) => {
                    if (date) {
                      debouncedDateChange(date, (selectedDate) => {
                        const quarter = Math.floor(selectedDate.month() / 3);
                        const quarterStart = selectedDate.month(quarter * 3).startOf('month').format('YYYY-MM-DD');
                        const quarterEnd = selectedDate.month(quarter * 3 + 2).endOf('month').format('YYYY-MM-DD');
                        return [quarterStart, quarterEnd];
                      });
                    } else {
                      debouncedDateChange(null, () => null as any);
                    }
                  }}
                  allowClear
                />
              )}
            </>
          )}
        </Flex>

        <Flex vertical gap={8}>
          <span>排序字段:</span>
          <Select
            value={sortConfig.field}
            onChange={(value) => updateSortConfig({ ...sortConfig, field: value })}
            style={{ width: 150 }}
            options={getSortFieldOptions()}
          />
        </Flex>

        <Flex vertical gap={8}>
          <span>排序方式:</span>
          <Radio.Group
            value={sortConfig.order}
            onChange={(e) => updateSortConfig({ ...sortConfig, order: e.target.value })}
            optionType="button"
            buttonStyle="solid"
          >
            <Radio.Button value="DESC">降序</Radio.Button>
            <Radio.Button value="ASC">升序</Radio.Button>
          </Radio.Group>
        </Flex>

        <Flex style={{ alignSelf: 'flex-end' }}>
          <Button
            type="primary"
            onClick={onFilter}
            loading={loading}
          >
            筛选
          </Button>
          {/* 添加下载图标到下载按钮中 */}

          <Button
            style={{ marginLeft: 8, display: 'flex', alignItems: 'center' }}
            loading={loading}
            onClick={alterDownload}
          >
            <DownloadOutlined />导出
          </Button>

        </Flex>
      </Flex>
    </Card>
  );
});

// 汇总数据视图组件
const StatsSummaryView: React.FC<{
  data: ExtendedStatsOperateIndicatorsVO | null;
  loading: boolean;
  activeTab: string;
  activeRoleType: string;
}> = ({ data, loading, activeTab, activeRoleType }) => {

  // 表格列配置（与StatsDataView保持一致）
  const getSummaryColumns = () => {
    // 日期渲染函数
    const renderDate = (text: string) => {
      if (!text) return '-';

      try {
        const startDate = dayjs(text);

        if (activeTab === 'WEEKLY') {
          // 对于周统计，显示周一到周日的日期范围
          const endDate = startDate.add(6, 'day');
          return (
            <div style={{ whiteSpace: 'nowrap' }}>
              <div>{startDate.format('YYYY-MM-DD')} /</div>
              <div>{endDate.format('YYYY-MM-DD')}</div>
            </div>
          );
        } else if (activeTab === 'MONTHLY') {
          // 对于月统计，显示月初到月末的日期范围
          const endDate = startDate.endOf('month');
          return (
            <div style={{ whiteSpace: 'nowrap' }}>
              <div>{startDate.format('YYYY-MM-DD')} / {endDate.format('DD')}</div>
            </div>
          );
        } else if (activeTab === 'QUARTERLY') {
          // 对于季度统计，显示季度开始到季度结束的日期范围
          const quarter = Math.floor((startDate.month()) / 3);
          const quarterStart = dayjs(startDate).month(quarter * 3).startOf('month');
          const quarterEnd = dayjs(startDate).month(quarter * 3 + 2).endOf('month');
          return (
            <div style={{ whiteSpace: 'nowrap' }}>
              <div>{quarterStart.format('YYYY-MM-DD')} /</div>
              <div>{quarterEnd.format('YYYY-MM-DD')}</div>
            </div>
          );
        } else {
          // 日统计和总统计直接显示日期
          return startDate.format('YYYY-MM-DD');
        }
      } catch (e) {
        console.error('日期格式化错误:', e);
        return text || '-';
      }
    };

    // 基础列（两种角色都显示）
    const baseColumns: any[] = [
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        width: 150,
        render: () => <span style={{ fontWeight: 'bold', color: '#1890ff' }}>VIP客户 数据汇总</span>,
      },
    ];

    // 工程师角色特有的列
    if (activeRoleType === 'ENGINEER') {
      baseColumns.push(
        {
          title: (
            <div style={{ display: 'inline-flex', alignItems: 'center' }}>
              客户|服装总数
              <Tooltip title="客户总数|时间范围内服装总数">
                <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
              </Tooltip>
            </div>
          ),
          width: 140,
          align: 'center' as const,
          render: (record: ExtendedStatsOperateIndicatorsVO) => <span
            style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                        <span className="customerTotalCount" style={{ fontWeight: 'bold' }}>
                            {record.customerTotalCount || 0} 人
                        </span>
                        <span className="customerUploadMaterialCount" style={{ fontWeight: 'bold' }}>
                            {record.customerUploadMaterialCount || 0} 件
                        </span>
                    </span>,
        },
        {
          title: (
            <div style={{ display: 'inline-flex', alignItems: 'center' }}>
              活跃客户率
              <Tooltip title="时间段内活跃的客户数量/客户总数">
                <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
              </Tooltip>
            </div>
          ),
          width: 120,
          align: 'center' as const,
          render: (record: ExtendedStatsOperateIndicatorsVO) => <span
            style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                        <span className="activity-rate" style={{ fontWeight: 'bold' }}>
                            {formatPercentage(record.customerActivityRate)}
                        </span>
                    </span>,
        },
        {
          title: (
            <div style={{ display: 'inline-flex', alignItems: 'center' }}>
              消耗总数|客均
              <Tooltip title="消耗总数 | 客均消耗">
                <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
              </Tooltip>
            </div>
          ),
          width: 140,
          align: 'center' as const,
          render: (record: ExtendedStatsOperateIndicatorsVO) => <span
            style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                        <span className="activity-rate" style={{ fontWeight: 'bold' }}>
                            {record.customerConsumptionPoints ? (record.customerConsumptionPoints / 1000).toFixed(2) : 0}
                        </span>
                        <span className="activity-rate-denominator"
                              style={{ fontSize: '12px', color: '#999', fontWeight: 'bold' }}>
                            ({record.customerConsumptionPointsAvg ? (Number(record.customerConsumptionPointsAvg) / 1000).toFixed(2) : '0'}/人)
                        </span>
                    </span>,
        },
        {
          title: (
            <div style={{ display: 'inline-flex', alignItems: 'center' }}>
              客户复购率
              <Tooltip title="客户复购率 | 复购人数">
                <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
              </Tooltip>
            </div>
          ),
          width: 120,
          align: 'center' as const,
          render: (record: ExtendedStatsOperateIndicatorsVO) => <span
            style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                        <span className="customerRepurchaseRate" style={{ fontWeight: 'bold' }}>
                            {formatPercentage(record.customerRepurchaseRate)}
                        </span>
                        <span className="customerRepurchaseRate-denominator"
                              style={{ fontSize: '12px', color: '#999', fontWeight: 'bold' }}>
                            ({record.extInfo?.customerRepurchaseRateMolecular}人)
                        </span>
                    </span>,
        },
        {
          title: (
            <div style={{ display: 'inline-flex', alignItems: 'center' }}>
              定制模特率
              <Tooltip title="定制模特率 | 定制模特比例">
                <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
              </Tooltip>
            </div>
          ),
          width: 120,
          align: 'center' as const,
          render: (record: ExtendedStatsOperateIndicatorsVO) => <span
            style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                        <span className="customModelCustomers" style={{ fontWeight: 'bold' }}>
                            {formatPercentage(record.customModelCustomers)}
                        </span>
                        <span className="customModelCustomers-denominator"
                              style={{ fontSize: '12px', color: '#999', fontWeight: 'bold' }}>
                            ({record.extInfo?.customModelCustomersMolecular} / {record.extInfo?.customModelCustomersDenominator})
                        </span>
                    </span>,
        },
        {
          title: (
            <div style={{ display: 'inline-flex', alignItems: 'center' }}>
              定制场景率
              <Tooltip title="定制场景率 | 定制场景比例">
                <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
              </Tooltip>
            </div>
          ),
          width: 120,
          align: 'center' as const,
          render: (record: ExtendedStatsOperateIndicatorsVO) => <span
            style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                        <span className="customSceneCustomers" style={{ fontWeight: 'bold' }}>
                            {formatPercentage(record.customSceneCustomers)}
                        </span>
                        <span className="customSceneCustomers-denominator"
                              style={{ fontSize: '12px', color: '#999', fontWeight: 'bold' }}>
                            ({record.extInfo?.customSceneCustomersMolecular} / {record.extInfo?.customSceneCustomersDenominator})
                        </span>
                    </span>,
        },
        {
          title: (
            <div style={{ display: 'inline-flex', alignItems: 'center' }}>
              视频数量|客均
              <Tooltip title="视频数量 | 客均视频数量">
                <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
              </Tooltip>
            </div>
          ),
          width: 140,
          align: 'center' as const,
          render: (record: ExtendedStatsOperateIndicatorsVO) => <span
            style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                        <span className="videoCount" style={{ fontWeight: 'bold' }}>
                            {record.videoCount || 0}
                        </span>
                        <span className="videoCountAvg"
                              style={{ fontSize: '12px', color: '#999', fontWeight: 'bold' }}>
                            ({record.videoCountAvg || 0} 个/人)
                        </span>
                    </span>,
        },
        {
          title: '交付服装量',
          dataIndex: 'deliveryClothingCount',
          key: 'deliveryClothingCount',
          width: 110,
          align: 'center' as const,
          render: (text: number) => <span className="deliveryClothingCount" style={{ fontWeight: 'bold' }}>{text || 0} 件</span>,
        },
        {
          title: '统计日期',
          dataIndex: 'statsDate',
          key: 'statsDate',
          width: 110,
          align: 'center' as const,
          render: renderDate,
        },
      );
    }

    // 审核员角色特有的列
    if (activeRoleType === 'REVIEWER') {
      baseColumns.push(
        {
          title: '审核服装量',
          dataIndex: 'approveClothingCount',
          key: 'approveClothingCount',
          width: 110,
          align: 'center' as const,
          render: (text: number) => <span className="approveClothingCount" style={{ fontWeight: 'bold' }}>{text || 0}</span>,
        },
        {
          title: '服装返点率',
          dataIndex: 'garmentRebateRate',
          key: 'garmentRebateRate',
          width: 110,
          align: 'center' as const,
          render: (text: string) => <span className="garmentRebateRate" style={{ fontWeight: 'bold' }}>{formatPercentage(text)}</span>,
        },
        {
          title: '审核错误率',
          dataIndex: 'approveErrorRate',
          key: 'approveErrorRate',
          width: 110,
          align: 'center' as const,
          render: (text: string) => <span className="approveErrorRate" style={{ fontWeight: 'bold' }}>暂未统计</span>,
        },
        {
          title: '客户投诉率',
          dataIndex: 'customerComplaintRate',
          key: 'customerComplaintRate',
          width: 110,
          align: 'center' as const,
          render: (text: string) => <span className="customerComplaintRate" style={{ fontWeight: 'bold' }}>暂未统计</span>,
        },
        {
          title: '统计日期',
          dataIndex: 'statsDate',
          key: 'statsDate',
          width: 110,
          align: 'center' as const,
          render: renderDate,
        },
      );
    }

    return baseColumns;
  };

  return (
    <div className="operate-stats-summary-section" style={{ marginBottom: '16px' }}>
      <div style={{ 
        backgroundColor: '#f6f8fa', 
        padding: '8px 16px', 
        borderRadius: '6px 6px 0 0',
        borderBottom: '1px solid #e8e8e8',
        fontWeight: 'bold',
        color: '#1890ff'
      }}>
        数据汇总
      </div>
      <Table
        className="stats-summary-table"
        columns={getSummaryColumns()}
        dataSource={data ? [{ ...data, key: 'summary' }] : []}
        pagination={false}
        loading={loading}
        rowClassName="summary-row"
        locale={{
          emptyText: loading ? <Spin /> : <Empty description="暂无数据汇总" />,
        }}
        style={{
          border: '1px solid #e8e8e8',
          borderTop: 'none',
          borderRadius: '0 0 6px 6px'
        }}
      />
    </div>
  );
};

// 统一数据视图组件
const StatsDataView: React.FC<{
  data: PageResponse<ExtendedStatsOperateIndicatorsVO>;
  loading: boolean;
  fetchDetailData: (page?: number, size?: number) => void;
  activeTab: string;
  activeRoleType: string;
  filterState: FilterState;
}> = ({ data, loading, fetchDetailData, activeTab, activeRoleType, filterState }) => {
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // 表格列配置
  const getStatsColumns = (isSubTable: boolean = false) => {
    // 日期渲染函数
    const renderDate = (text: string) => {
      if (!text) return '-';

      try {
        const startDate = dayjs(text);

        if (activeTab === 'WEEKLY') {
          // 对于周统计，显示周一到周日的日期范围
          const endDate = startDate.add(6, 'day');
          return (
            <div style={{ whiteSpace: 'nowrap' }}>
              <div>{startDate.format('YYYY-MM-DD')} /</div>
              <div>{endDate.format('YYYY-MM-DD')}</div>
            </div>
          );
        } else if (activeTab === 'MONTHLY') {
          // 对于月统计，显示月初到月末的日期范围
          const endDate = startDate.endOf('month');
          return (
            <div style={{ whiteSpace: 'nowrap' }}>
              <div>{startDate.format('YYYY-MM-DD')} / {endDate.format('DD')}</div>
            </div>
          );
        } else if (activeTab === 'QUARTERLY') {
          // 对于季度统计，显示季度开始到季度结束的日期范围
          const quarter = Math.floor((startDate.month()) / 3);
          const quarterStart = dayjs(startDate).month(quarter * 3).startOf('month');
          const quarterEnd = dayjs(startDate).month(quarter * 3 + 2).endOf('month');
          return (
            <div style={{ whiteSpace: 'nowrap' }}>
              <div>{quarterStart.format('YYYY-MM-DD')} /</div>
              <div>{quarterEnd.format('YYYY-MM-DD')}</div>
            </div>
          );
        } else {
          // 日统计和总统计直接显示日期
          return startDate.format('YYYY-MM-DD');
        }
      } catch (e) {
        console.error('日期格式化错误:', e);
        return text || '-';
      }
    };

    // 基础列（两种角色都显示）
    const baseColumns: any[] = [
      {
        title: 'ID',
        dataIndex: 'userId',
        key: 'userId',
        width: 60,
        align: 'center' as const,
      },
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        width: 100,
        render: (text: string) => <span>{text || '未知'}</span>,
      },
    ];

    // 工程师角色特有的列
    if (activeRoleType === 'ENGINEER') {
      baseColumns.push(
        {
          title: (
            <div style={{ display: 'inline-flex', alignItems: 'center' }}>
              客户|服装总数
              <Tooltip title="客户总数|时间范围内服装总数">
                <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
              </Tooltip>
            </div>
          ),
          width: 140,
          align: 'center' as const,
          render: (record: ExtendedStatsOperateIndicatorsVO) => <span
            style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                        <span className="customerTotalCount">
                            {record.customerTotalCount || 0} 人
                        </span>
                        <span className="customerUploadMaterialCount">
                            {record.customerUploadMaterialCount || 0} 件
                        </span>
                    </span>,
        },
        {
          title: (
            <div style={{ display: 'inline-flex', alignItems: 'center' }}>
              转换量
              <Tooltip title="新签3999以上的客户数量">
                <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
              </Tooltip>
            </div>
          ),
          dataIndex: 'customerConversionCount',
          key: 'customerConversionCount',
          width: 100,
          align: 'center' as const,
          render: (text: number) => <span className="conversion-count">{text || 0} 人</span>,
        },
        {
          title: (
            <div style={{ display: 'inline-flex', alignItems: 'center' }}>
              活跃客户率
              <Tooltip title="时间段内活跃的客户数量/客户总数">
                <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
              </Tooltip>
            </div>
          ),
          width: 120,
          align: 'center' as const,
          render: (record: ExtendedStatsOperateIndicatorsVO) => <span
            style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                        <span className="activity-rate">
                            {formatPercentage(record.customerActivityRate)}
                        </span>
                    </span>,
        },
        {
          title: (
            <div style={{ display: 'inline-flex', alignItems: 'center' }}>
              消耗总数|客均
              <Tooltip title="消耗总数 | 客均消耗">
                <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
              </Tooltip>
            </div>
          ),
          width: 140,
          align: 'center' as const,
          render: (record: ExtendedStatsOperateIndicatorsVO) => <span
            style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                        <span className="activity-rate">
                            {record.customerConsumptionPoints ? (record.customerConsumptionPoints / 1000).toFixed(2) : 0}
                        </span>
                        <span className="activity-rate-denominator"
                              style={{ fontSize: '12px', color: '#999' }}>
                            ({record.customerConsumptionPointsAvg ? (Number(record.customerConsumptionPointsAvg) / 1000).toFixed(2) : '0'}/人)
                        </span>
                    </span>,
        },
        {
          title: (
            <div style={{ display: 'inline-flex', alignItems: 'center' }}>
              客户复购率
              <Tooltip title="客户复购率 | 复购人数">
                <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
              </Tooltip>
            </div>
          ),
          width: 120,
          align: 'center' as const,
          render: (record: ExtendedStatsOperateIndicatorsVO) => <span
            style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                        <span className="customerRepurchaseRate">
                            {formatPercentage(record.customerRepurchaseRate)}
                        </span>
                        <span className="customerRepurchaseRate-denominator"
                              style={{ fontSize: '12px', color: '#999' }}>
                            ({record.extInfo?.customerRepurchaseRateMolecular}人)
                        </span>
                    </span>,
        },
        {
          title: (
            <div style={{ display: 'inline-flex', alignItems: 'center' }}>
              定制模特率
              <Tooltip title="定制模特率 | 定制模特比例">
                <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
              </Tooltip>
            </div>
          ),
          width: 120,
          align: 'center' as const,
          render: (record: ExtendedStatsOperateIndicatorsVO) => <span
            style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                        <span className="customModelCustomers">
                            {formatPercentage(record.customModelCustomers)}
                        </span>
                        <span className="customModelCustomers-denominator"
                              style={{ fontSize: '12px', color: '#999' }}>
                            ({record.extInfo?.customModelCustomersMolecular} / {record.extInfo?.customModelCustomersDenominator})
                        </span>
                    </span>,
        },
        {
          title: (
            <div style={{ display: 'inline-flex', alignItems: 'center' }}>
              定制场景率
              <Tooltip title="定制场景率 | 定制场景比例">
                <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
              </Tooltip>
            </div>
          ),
          width: 120,
          align: 'center' as const,
          render: (record: ExtendedStatsOperateIndicatorsVO) => <span
            style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                        <span className="customSceneCustomers">
                            {formatPercentage(record.customSceneCustomers)}
                        </span>
                        <span className="customSceneCustomers-denominator"
                              style={{ fontSize: '12px', color: '#999' }}>
                            ({record.extInfo?.customSceneCustomersMolecular} / {record.extInfo?.customSceneCustomersDenominator})
                        </span>
                    </span>,
        },
        {
          title: (
            <div style={{ display: 'inline-flex', alignItems: 'center' }}>
              视频数量|客均
              <Tooltip title="视频数量 | 客均视频数量">
                <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
              </Tooltip>
            </div>
          ),
          width: 140,
          align: 'center' as const,
          render: (record: ExtendedStatsOperateIndicatorsVO) => <span

            style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                        <span className="videoCount">
                            {record.videoCount || 0}
                        </span>
                        <span className="videoCountAvg"
                              style={{ fontSize: '12px', color: '#999' }}>
                            ({record.videoCountAvg || 0} 个/人)
                        </span>
                    </span>,
        },
        {
          title: (
            <div style={{ display: 'inline-flex', alignItems: 'center' }}>
              待保护客户
              <Tooltip title="大于60天未充值的客户">
                <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
              </Tooltip>
            </div>
          ),
          dataIndex: 'customerProtectionMetrics',
          key: 'customerProtectionMetrics',
          width: 120,
          align: 'center' as const,
          render: (text: number) => <span className="customerProtectionMetrics">{text || 0} 人</span>,
        },
        {
          title: '交付服装量',
          dataIndex: 'deliveryClothingCount',
          key: 'deliveryClothingCount',
          width: 110,
          align: 'center' as const,
          render: (text: number) => <span className="deliveryClothingCount">{text || 0} 件</span>,
        },
        {
          title: '统计日期',
          dataIndex: 'statsDate',
          key: 'statsDate',
          width: 110,
          align: 'center' as const,
          render: renderDate,
        },
      );
    }
    ;


    // 审核员角色特有的列
    if (activeRoleType === 'REVIEWER') {
      baseColumns.push(
        {
          title: '审核服装量',
          dataIndex: 'approveClothingCount',
          key: 'approveClothingCount',
          width: 110,
          align: 'center' as const,
          render: (text: number) => <span className="approveClothingCount">{text || 0}</span>,
        },
        {
          title: '服装返点率',
          dataIndex: 'garmentRebateRate',
          key: 'garmentRebateRate',
          width: 110,
          align: 'center' as const,
          render: (text: string) => <span className="garmentRebateRate">{formatPercentage(text)}</span>,
        },
        {
          title: '审核错误率',
          dataIndex: 'approveErrorRate',
          key: 'approveErrorRate',
          width: 110,
          align: 'center' as const,
          render: (text: string) => <span className="approveErrorRate">暂未统计</span>,
        },
        {
          title: '客户投诉率',
          dataIndex: 'customerComplaintRate',
          key: 'customerComplaintRate',
          width: 110,
          align: 'center' as const,
          render: (text: string) => <span className="customerComplaintRate">暂未统计</span>,
        },
        {
          title: '统计日期',
          dataIndex: 'statsDate',
          key: 'statsDate',
          width: 110,
          align: 'center' as const,
          render: renderDate,
        },
      );
    }

    return baseColumns;
  };

  // 处理分页变化
  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) {
      setPageSize(size);
    }
    fetchDetailData(page, size || pageSize);
  };

  return (
    <div className="operate-stats-detail-section">
      <Table
        className="stats-main-table"
        columns={getStatsColumns()}
        dataSource={data.list}
        pagination={false}
        loading={loading}
        rowClassName={(record) => !record.parentId || record.parentId === 0 ? 'company-row' : 'operator-row'}
        locale={{
          emptyText: loading ? <Spin /> : <Empty description="暂无数据" />,
        }}
      />
      {data.list.length > 0 && (
        <div style={{ textAlign: 'right', marginTop: '16px' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={data.totalCount}
            onChange={handlePageChange}
            showSizeChanger
            showQuickJumper
            showTotal={(total) => `共 ${total} 条`}
          />
        </div>
      )}
    </div>
  );
};

// 运营统计主区域组件
const StatsOperateSection: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('WEEKLY');
  const [activeRoleType, setActiveRoleType] = useState<string>('ENGINEER');
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<PageResponse<ExtendedStatsOperateIndicatorsVO>>({
    list: [],
    hasNextPage: false,
    size: 10,
    totalCount: 0,
  });
  const [summaryData, setSummaryData] = useState<ExtendedStatsOperateIndicatorsVO | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  // 添加排序配置状态
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: activeRoleType === 'ENGINEER' ? 'customer_consumption_points' : 'approve_clothing_count',
    order: 'DESC',
  });

  // 添加一个标志来跟踪是否是初始化设置的 filterState
  const [isSettingInitialFilter, setIsSettingInitialFilter] = useState<boolean>(false);

  // 创建初始筛选状态，根据统计类型设置不同的默认日期
  const createInitialFilterState = useCallback((statsType?: string): FilterState => {
    const type = statsType || activeTab;
    let defaultDate: dayjs.Dayjs;
    let dateRange: [string, string] | null;

    if (type === 'WEEKLY') {
      // 上一周
      defaultDate = dayjs().subtract(1, 'week');
      const weekStart = defaultDate.startOf('week');
      const weekEnd = defaultDate.endOf('week');
      dateRange = [weekStart.format('YYYY-MM-DD'), weekEnd.format('YYYY-MM-DD')];
    } else if (type === 'MONTHLY') {
      // 上个月
      defaultDate = dayjs().subtract(1, 'month');
      const monthStart = defaultDate.startOf('month');
      const monthEnd = defaultDate.endOf('month');
      dateRange = [monthStart.format('YYYY-MM-DD'), monthEnd.format('YYYY-MM-DD')];
    } else if (type === 'QUARTERLY') {
      // 上个季度
      const currentMonth = dayjs().month();
      const currentQuarter = Math.floor(currentMonth / 3);
      const previousQuarter = currentQuarter - 1 < 0 ? 3 : currentQuarter - 1;
      const previousQuarterYear = currentQuarter - 1 < 0 ? dayjs().year() - 1 : dayjs().year();

      // 创建上一季度的日期
      defaultDate = dayjs().year(previousQuarterYear).month(previousQuarter * 3 + 1);

      const quarterStart = defaultDate.month(previousQuarter * 3).startOf('month');
      const quarterEnd = defaultDate.month(previousQuarter * 3 + 2).endOf('month');
      dateRange = [quarterStart.format('YYYY-MM-DD'), quarterEnd.format('YYYY-MM-DD')];
    } else if (type === 'TOTAL') {
      // TOTAL类型设置前一天作为默认日期
      defaultDate = dayjs().subtract(1, 'day');
      const yesterday = defaultDate.format('YYYY-MM-DD');
      dateRange = [yesterday, yesterday];

      return {
        name: '',
        userId: null,
        selectedDate: defaultDate,
        dateRange: dateRange,
      };
    } else {
      // 其他类型不设置默认日期
      return {
        name: '',
        userId: null,
        selectedDate: null,
        dateRange: null,
      };
    }

    return {
      name: '',
      userId: null,
      selectedDate: defaultDate,
      dateRange: dateRange,
    };
  }, [activeTab]);

  // 筛选条件状态
  const [filterState, setFilterState] = useState<FilterState>(() => ({
    name: '',
    userId: null,
    selectedDate: null,
    dateRange: null,
  }));

  // 更新筛选状态
  const updateFilter = useCallback((key: keyof FilterState, value: any) => {
    setFilterState(prev => ({
      ...prev,
      [key]: value,
    }));
  }, []);

  // 更新排序配置
  const updateSortConfig = useCallback((config: SortConfig) => {
    setSortConfig(config);
  }, []);

  // 获取数据汇总
  const fetchSummaryData = useCallback(async () => {
    try {
      // 构建数据汇总查询参数
      const params: any = {
        pageNum: 1,
        pageSize: 1,
        statsType: activeTab,
        roleType: activeRoleType,
        systemUserId: 1, // 固定查询 userId = 1 的汇总记录
      };

      // 添加日期筛选
      if (filterState.dateRange) {
        params.statsDate = filterState.dateRange[0];
      }

      const response = await statsOperateIndicators(params);
      const result = formatApiResponse(response);

      // 设置数据汇总
      if (result.list.length > 0) {
        const summaryRecord = {
          ...result.list[0],
          key: 'summary',
        };
        setSummaryData(summaryRecord);
      } else {
        setSummaryData(null);
      }
    } catch (error) {
      console.error('获取数据汇总失败:', error);
      setSummaryData(null);
    }
  }, [activeTab, filterState, activeRoleType]);

  // 获取详细数据
  const fetchDetailData = useCallback(async (page: number = 1, size: number = 10) => {
    setLoading(true);

    try {
      // 构建详细数据查询参数
      const params: any = {
        pageNum: page,
        pageSize: size, // 增大页面大小以补偿过滤掉的汇总数据
        statsType: activeTab,
        roleType: activeRoleType,
        excludeUserId: 1, // 排除 userId = 1 的汇总记录
      };

      // 添加排序参数
      params.orderBy = `${sortConfig.field} ${sortConfig.order}`;

      // 添加企业名称筛选
      if (filterState.name) {
        params.name = filterState.name;
      }

      // 添加运营ID筛选
      if (filterState.userId !== null) {
        params.userId = filterState.userId;
      }

      // 添加日期筛选
      if (filterState.dateRange) {
        params.statsDate = filterState.dateRange[0];
      }

      console.log('详细数据查询参数:', params);

      const response = await statsOperateIndicators(params);
      const result = formatApiResponse(response);

      // 为每个记录添加key字段，并过滤掉汇总数据（userId为1的记录）
      const dataWithKeys = result.list
        .filter(item => item.userId !== 1) // 过滤掉汇总数据
        .map((item, index) => ({
          ...item,
          key: item.id || `${item.userId}-${item.statsDate}` || `fallback-${index}`,
        }))
        .slice(0, size); // 限制返回的数据量

      // 设置详细数据
      setData({
        list: dataWithKeys,
        hasNextPage: dataWithKeys.length >= size,
        size,
        totalCount: Math.max(0, result.totalCount - 1), // 总数减去汇总记录
      });

      setCurrentPage(page);
      setPageSize(size);
    } catch (error) {
      console.error('获取详细数据失败:', error);
      message.error('获取详细数据失败，请重试');
    } finally {
      setLoading(false);
    }
  }, [activeTab, filterState, sortConfig, activeRoleType]);

  // 获取所有数据（汇总 + 详细）
  const fetchAllData = useCallback(async (page: number = 1, size: number = 10) => {
    // 并行获取汇总数据和详细数据
    await Promise.all([
      fetchSummaryData(),
      fetchDetailData(page, size),
    ]);
  }, [fetchSummaryData, fetchDetailData]);

  // 处理Tab切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    // 切换Tab时设置对应的默认日期（上一周/上个月/上个季度）
    setFilterState(createInitialFilterState(key));
    setCurrentPage(1);
  };

  // 处理筛选按钮点击
  const handleFilter = () => {
    setCurrentPage(1);
    fetchAllData(1, pageSize);
  };

  // 处理角色类型切换
  const handleRoleTypeChange = (key: string) => {
    setActiveRoleType(key);
    // 切换角色类型时重新设置默认排序字段
    setSortConfig({
      field: key === 'ENGINEER' ? 'customer_consumption_points' : 'approve_clothing_count',
      order: 'DESC',
    });
    // 切换角色类型时重新加载数据
    setCurrentPage(1);
  };

  // 初始加载和Tab切换时获取数据
  useEffect(() => {
    // 只在组件第一次挂载时设置初始日期，避免在清空后重新设置
    if (!isInitialized) {
      setIsInitialized(true);
      // 设置默认日期，包括TOTAL类型
      setFilterState(createInitialFilterState(activeTab));
    }

    fetchAllData(1, pageSize);
  }, [activeTab, fetchAllData, pageSize, activeRoleType, isInitialized, createInitialFilterState]);

  // 获取对应的统计类型标签
  const getStatsTypeLabel = (type: string) => {
    switch (type) {
      case 'ENGINEER':
        return '工程师';
      case 'REVIEWER':
        return '审核员';
      case 'WEEKLY':
        return '周统计';
      case 'MONTHLY':
        return '月统计';
      case 'QUARTERLY':
        return '季度统计';
      case 'TOTAL':
        return '总统计';
      default:
        return '未知';
    }
  };

  return (
    <div className="operate-stats-container">
      {/* 角色与统计类型选择卡片 */}
      <Card className="operate-stats-selection-card" style={{ marginBottom: '16px' }}>
        <Flex align="center" gap={24}>
          <Flex vertical gap={8}>
            <span style={{ fontWeight: 'bold' }}>角色类型:</span>
            <Radio.Group
              value={activeRoleType}
              onChange={(e) => handleRoleTypeChange(e.target.value)}
              buttonStyle="solid"
            >
              <Radio.Button value="ENGINEER">工程师</Radio.Button>
              <Radio.Button value="REVIEWER">审核员</Radio.Button>
            </Radio.Group>
          </Flex>

          <Flex vertical gap={8}>
            <span style={{ fontWeight: 'bold' }}>统计周期:</span>
            <Radio.Group
              value={activeTab}
              onChange={(e) => handleTabChange(e.target.value)}
              buttonStyle="solid"
            >
              <Radio.Button value="WEEKLY">周统计</Radio.Button>
              <Radio.Button value="MONTHLY">月统计</Radio.Button>
              <Radio.Button value="QUARTERLY">季度统计</Radio.Button>
              <Radio.Button value="TOTAL">总统计</Radio.Button>
            </Radio.Group>
          </Flex>
        </Flex>
      </Card>

      <FilterCard
        filterState={filterState}
        updateFilter={updateFilter}
        onFilter={handleFilter}
        loading={loading}
        statsType={activeTab}
        statsTypeLabel={`${activeRoleType === 'ENGINEER' ? '工程师' : '审核员'} - ${getStatsTypeLabel(activeTab)}`}
        sortConfig={sortConfig}
        updateSortConfig={updateSortConfig}
        activeRoleType={activeRoleType}
      />

      <StatsSummaryView
        data={summaryData}
        loading={loading}
        activeTab={activeTab}
        activeRoleType={activeRoleType}
      />

      <StatsDataView
        data={data}
        loading={loading}
        fetchDetailData={fetchDetailData}
        activeTab={activeTab}
        activeRoleType={activeRoleType}
        filterState={filterState}
      />
    </div>
  );
};

// 主组件
const StatsOperateIndicators: React.FC = () => {
  return (
    <PageContainer>
      <Card>
        <StatsOperateSection />
      </Card>
    </PageContainer>
  );
};

export default StatsOperateIndicators;
