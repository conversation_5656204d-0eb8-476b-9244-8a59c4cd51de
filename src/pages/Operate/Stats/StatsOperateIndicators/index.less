.operate-stats-container {
  margin-top: 24px;
}

.operate-stats-filter-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.operate-stats-detail-section {
  margin-bottom: 16px;
}

.operate-stats-upper-tabs {
  margin-bottom: 16px;
}

.operate-stats-metric-usage {
  font-weight: 500;
  color: #1890ff;
}

.operate-stats-metric-customer {
  font-weight: 500;
  color: #52c41a;
}

.operate-stats-metric-conversion {
  font-weight: 500;
  color: #fa8c16;
}

.operate-stats-metric-growth {
  font-weight: 500;
  color: #722ed1;
}

.operate-stats-company-label {
  display: inline-block;
  background-color: #e6f7ff;
  color: #1890ff;
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 4px;
  font-size: 12px;
  border: 1px solid #91d5ff;
}

.operate-stats-operator-label {
  display: inline-block;
  background-color: #f6ffed;
  color: #52c41a;
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 4px;
  font-size: 12px;
  border: 1px solid #b7eb8f;
}

/* 主表格和展开行样式 */
.stats-main-table {
  .ant-table-row {
    background: #fff;
    
    &.company-row {
      background-color: #fafafa;
    }
    
    &.operator-row {
      background-color: #fff;
    }
  }
  
  .ant-table-expanded-row {
    .expanded-row-class {
      margin-bottom: 16px;
      position: relative;
      z-index: 2;
      
      &:after {
        content: '';
        height: 16px;
        display: block;
        position: absolute;
        bottom: -16px;
        left: 0;
        right: 0;
        background: #f9f9f9;
        z-index: 1;
      }
    }
  }
  
  /* 确保展开行不会影响后续行 */
  .ant-table-row {
    position: relative;
    z-index: 1;
  }
  
  .ant-table-expanded-row {
    position: relative;
    z-index: 0; 
    
    .operate-stats-detail-expanded {
      position: relative;
      z-index: 0;
      margin-bottom: 16px;
      
      /* 为子表格设置样式 */
      .sublist-table-container {
        position: relative;
        z-index: 0;
        margin-bottom: 16px;
        
        .ant-table-wrapper {
          position: relative;
          z-index: 0;
          margin-bottom: 0;
          
          .ant-table {
            margin-bottom: 0;
          }
        }
      }
    }
  }
  
  /* 确保行的间隔和分隔 */
  .ant-table-tbody {
    > tr:not(.ant-table-expanded-row) {
      & + tr:not(.ant-table-expanded-row) {
        td {
          border-top: 1px solid #f0f0f0;
        }
      }
      
      &.ant-table-row:hover > td {
        background-color: #f5f5f5;
      }
    }
    
    > tr.ant-table-expanded-row > td {
      padding: 0;
      border-bottom: none;
    }
    
    tr.ant-table-expanded-row + tr.ant-table-row td {
      border-top: 1px solid #e8e8e8;
    }
  }
}

.operate-stats-detail-expanded {
  padding: 0;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 16px;
  position: relative;
  z-index: 0;
  
  /* 子表格容器 */
  .sublist-table-container {
    border-radius: 4px;
    overflow: hidden;
  }
  
  /* 嵌套表格样式 */
  .nested-table-component {
    .ant-table-thead > tr > th {
      background-color: #f0f0f0;
      font-weight: 600;
      color: #333;
    }
    
    .ant-table-tbody > tr > td {
      border-bottom: 1px solid #f5f5f5;
    }
    
    .ant-table-tbody > tr:last-child > td {
      border-bottom: none;
    }
  }
}

.operate-indicator-card {
  transition: all 0.3s;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
}

.company-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .ant-card-head {
    background-color: #f5f7fa;
    border-bottom: 1px solid #e8eaed;
  }
}

.operator-card {
  border-left: 4px solid #52c41a;
  margin-left: 24px;
  margin-bottom: 12px;
}

/* 嵌套表格样式 */
.nested-table {
  margin-top: 16px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  
  /* 嵌套表格标题样式 */
  .nested-table-title {
    font-size: 14px; 
    font-weight: bold;
    color: #333;
    margin-bottom: 12px;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fafafa;
  }
  
  /* 嵌套表格内容区域 */
  .nested-table-content {
    padding: 0 16px 16px;
  }
  
  .ant-table {
    border-radius: 4px;
    overflow: hidden;
    
    .ant-table-thead > tr > th {
      background-color: #f5f5f5;
      font-weight: 500;
    }
  }
}

.operate-stats-selection-card {
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
  
  .ant-radio-group {
    font-size: 14px;
  }
  
  .ant-radio-button-wrapper {
    min-width: 80px;
    text-align: center;
  }
}

/* 指标样式 - 参考自StatsSlaeIndicators */
.conversion-count {
  color: #52c41a;
  font-weight: 500;
}

.activity-rate {
  color: #fa8c16;
  font-weight: 500;
}

.customerTotalCount {
  color: #52c41a;
  font-weight: 500;
}

.videoCount {
  color: #1890ff;
  font-weight: 500;
}

.videoCountAvg {
  color: #1890ff;
  font-weight: 500;
}

.customerUploadMaterialCount {
  color: #fa8c16;
  font-weight: 500;
}



.customerRepurchaseRate {
  color: #eb2f96;
  font-weight: 500;
}

.customModelCustomers {
  color: #13c2c2;
  font-weight: 500;
}

.customSceneCustomers {
  color: #faad14;
  font-weight: 500;
}

.customerProtectionMetrics {
  color: #f5222d;
  font-weight: 500;
}

.deliveryClothingCount {
  color: #2f54eb;
  font-weight: 500;
}

.approveClothingCount {
  color: #1890ff;
  font-weight: 500;
}

.approveErrorRate {
  color: #f5222d;
  font-weight: 500;
}

.garmentRebateRate {
  color: #722ed1;
  font-weight: 500;
}

.customerComplaintRate {
  color: #fa8c16;
  font-weight: 500;
}

.customerConsumptionPoints {
  color: #722ed1;
  font-weight: 500;
}

.customerConsumptionPointsAvg {
  color: #13c2c2;
  font-weight: 500;
}
