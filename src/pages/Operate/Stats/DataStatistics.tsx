import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Tabs, TabsProps } from 'antd';
import { useLocation } from 'react-router-dom';
import StatsUserPoint from './StatsUserPoint';
import StatsClothesInfo from './StatsClothesInfo';
import StatsUserOperate from './StatsUserOperate';
import StatsOperateIndicators from './StatsOperateIndicators';
import StatsSaleIndicators from './StatsSlaeIndicators';
import StatsWarningInfo from './StatsWarningInfo';

const DataStatistics: React.FC = () => {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('point-stats');
  
  // 根据当前路由设置默认Tab
  useEffect(() => {
    const path = location.pathname;
    if (path.includes('/point-stats')) {
      setActiveTab('point-stats');
    } else if (path.includes('/stats-clothes-info')) {
      setActiveTab('clothes-info');
    } else if (path.includes('/stats-user-operate') || path.includes('/admin/stats-user-operate')) {
      setActiveTab('user-operate');
    } else if (path.includes('/stats-operate-indicators')) {
      setActiveTab('operate-indicators');
    } else if (path.includes('/stats-sale-indicators')) {
      setActiveTab('sale-indicators');
    } else if (path.includes('/stats-warning-info')) {
      setActiveTab('warning-info');
    } else {
      setActiveTab('point-stats'); // 默认Tab
    }
  }, [location.pathname]);


  const changeTab = (key: string) => {
    setActiveTab(key);
  };

  const tabItems: TabsProps['items'] = [
    {
      key: 'point-stats',
      label: '消耗统计',
      children: <StatsUserPoint />,
    },
    {
      key: 'clothes-info',
      label: '服装统计',
      children: <StatsClothesInfo />,
    },
    {
      key: 'user-operate',
      label: '出图统计',
      children: <StatsUserOperate />,
    },
    {
      key: 'operate-indicators',
      label: '运营数据',
      children: <StatsOperateIndicators />,
    },
    {
      key: 'sale-indicators',
      label: '销售数据',
      children: <StatsSaleIndicators />,
    },
    {
      key: 'warning-info',
      label: '预警数据',
      children: <StatsWarningInfo />,
    },
  ];

  return (
    <PageContainer style={{ padding: 12 }}>
      <Tabs
        activeKey={activeTab}
        items={tabItems}
        onChange={changeTab}
        size="large"
        style={{ justifyContent: 'center' }}
        type="card"
      />
    </PageContainer>
  );
};

export default DataStatistics; 