import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Tag,
  Spin,
  Flex,
  Empty,
  message,
  Space,
  Table,
  Image,
  Tooltip,
  theme,
  Modal,
  Select
} from 'antd';
import {
  ReloadOutlined,
  UpOutlined,
  DownOutlined,
  CalendarOutlined,
  FileImageOutlined,
  PictureOutlined,
  CloudDownloadOutlined,

} from '@ant-design/icons';
import { PageInfo, statsUserOperate, StatsUserOperateVO } from '@/services/DataController';
import { batchQueryCreative, queryCreativeById, ALLCreativeType, CreativeType, CreativeVO } from '@/services/CreativeController';
import { getPageUser, UserVO, queryAllSubByUserId } from '@/services/UserController';
import dayjs from 'dayjs';
import ImgPreview from '@/components/ImgPreview';

// 添加全局样式
const GlobalStyle = () => (
  <style dangerouslySetInnerHTML={{
    __html: `
    .material-card {
      position: relative;
      overflow: hidden;
      border-radius: 8px;
      transition: all 0.2s ease;
      cursor: pointer;
    }
    
    .material-card:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    .hover-mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      opacity: 0;
      transform: translateX(100%);
      transition: opacity 0.3s ease, transform 0.3s ease;
      color: white;
      text-align: center;
      padding: 20px;
      pointer-events: none;
    }
    
    .material-card:hover .hover-mask {
      opacity: 1;
      transform: translateX(0);
    }
    
    /* 添加详情弹窗样式 */
    .history-detail-content {
      display: flex;
      flex-direction: row;
      padding: 0;
      border-radius: 24px;
    }
    
    .history-detail-left {
      width: 264px;
      margin-left: -24px;
      opacity: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0 24px;
      gap: 16px;
      align-self: stretch;
      z-index: 0;
    }
    
    .history-detail-left-inner {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 0;
      gap: 12px;
      z-index: 0;
      width: 100%;
    }
    
    .history-left-inner-top {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 0;
      gap: 8px;
      width: 100%;
      z-index: 0;
    }
    
    .history-left-inner-top img {
      width: 160px;
      height: 160px;
      object-fit: contain;
      border-radius: 5.16px;
      box-sizing: border-box;
      border: 0.65px solid #E0E0E0;
      margin: 0 auto;
    }
    
    .history-detail-section {
      margin-bottom: 8px;
      padding-bottom: 3px;
    }
    
    .history-detail-info-row {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      padding: 3px 0;
      border-bottom: 1px dashed #E0E0E0;
    }
    
    .history-detail-info-row:last-child {
      border-bottom: none;
    }
    
    .history-detail-info-label {
      font-size: 13px;
      font-weight: 500;
      color: #333;
      flex: 0 0 auto;
    }
    
    .history-detail-info-value {
      font-size: 12px;
      color: #666;
      text-align: right;
      flex: 1;
      word-break: break-word;
      padding-left: 8px;
    }
    
    .history-detail-right {
      width: 920px;
      border-radius: 0 24px 24px 0;
      opacity: 1;
      background: #F5F6F9;
      z-index: 1;
      padding-left: 30px;
      overflow-y: auto;
      will-change: transform;
    }
    
    .history-detail-image-item {
      opacity: 1;
      display: flex;
      flex-direction: column;
      padding: 0px;
      gap: 8px;
      margin-right: 12px;
      margin-top: 16px;
      z-index: 0;
    }
    
    .history-detail-img-container {
      position: relative;
      opacity: 1;
      display: flex;
      z-index: 0;
      cursor: pointer;
    }
    
    .font-pf {
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }
    
    .text14 {
      font-size: 14px;
    }
    
    .text12 {
      font-size: 12px;
    }
    
    .weight {
      font-weight: 500;
    }
    
    .color-96 {
      color: #969696;
    }
  ` }} />
);

interface ExtInfo {
  batchIdList?: string[];
  showImage?: string;
}

// 用户明细组件Props定义
interface UserDetailSectionProps {
  userId: number;
  parentId?: number;
  isSubAccountsView?: boolean;
}

// 用户明细组件
const UserDetailSection: React.FC<UserDetailSectionProps> = ({
  userId,
  parentId,
  isSubAccountsView
}) => {
  const { token } = theme.useToken();
  const DEFAULT_CHARTS_SIZE = 30;
  const [userDetailData, setUserDetailData] = useState<StatsUserOperateVO[]>([]);
  const [userDetailLoading, setUserDetailLoading] = useState<boolean>(false);
  const [sortField, setSortField] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | undefined>(undefined);

  // 日期相关状态
  const [availableDates, setAvailableDates] = useState<string[]>([]);
  const [dateDataMap, setDateDataMap] = useState<Record<string, number>>({});
  const [activeDate, setActiveDate] = useState<string>('');
  const [loadingDates, setLoadingDates] = useState(false);

  // 时间线视图状态
  const [yearMonthGroups, setYearMonthGroups] = useState<Record<string, Record<string, number[]>>>({});

  // 展开状态
  const [expandedYears, setExpandedYears] = useState<Record<string, boolean>>({});
  const [expandedMonths, setExpandedMonths] = useState<Record<string, boolean>>({});

  // 添加展开/折叠状态
  const [expandedMaterials, setExpandedMaterials] = useState<Record<number, boolean>>({});

  // 批次详情数据
  const [batchDetails, setBatchDetails] = useState<Record<string, any[]>>({});
  const [batchLoading, setBatchLoading] = useState<Record<string, boolean>>({});

  // 批次详情弹窗状态
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedCreation, setSelectedCreation] = useState<CreativeVO | null>(null);
  const [detailLoading, setDetailLoading] = useState(false);

  // 图片预览状态
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewImgs, setPreviewImgs] = useState<string[]>([]);
  const [previewIdx, setPreviewIdx] = useState(0);

  // 子用户选择状态
  const [subUsers, setSubUsers] = useState<UserVO[]>([]);
  const [subUsersLoading, setSubUsersLoading] = useState(false);
  const [selectedSubUserId, setSelectedSubUserId] = useState<number | null>(null);
  const [filteredDetailData, setFilteredDetailData] = useState<StatsUserOperateVO[]>([]);

  // Type guard to check if response is a PageInfo - 从index.tsx移入
  function isPageInfo(response: any): response is PageInfo<StatsUserOperateVO> {
    return response &&
      typeof response === 'object' &&
      Array.isArray(response.list) &&
      typeof response.totalCount === 'number';
  }

  // Convert API response to appropriate format - 从index.tsx移入
  function formatApiResponse(response: any): PageInfo<StatsUserOperateVO> {
    let result: PageInfo<StatsUserOperateVO>;

    if (isPageInfo(response)) {
      result = response;
    } else if (Array.isArray(response)) {
      result = {
        list: response,
        totalCount: response.length
      };
    } else {
      result = {
        list: [],
        totalCount: 0
      };
    }

    return result;
  }

  // 获取子用户列表
  const fetchSubUsers = useCallback(async () => {
    if (!isSubAccountsView || !parentId) return;
    
    try {
      setSubUsersLoading(true);
      
      // 替换为queryAllSubByUserId接口
      const response = await queryAllSubByUserId(parentId);
      
      if (response && Array.isArray(response)) {
        setSubUsers(response);
      }
    } catch (error) {
      console.error('获取子用户列表失败:', error);
      message.error('获取子用户列表失败');
    } finally {
      setSubUsersLoading(false);
    }
  }, [isSubAccountsView, parentId]);

  // 组件挂载时获取子用户列表
  useEffect(() => {
    fetchSubUsers();
  }, [fetchSubUsers]);

  // 处理子用户选择变化
  const handleSubUserChange = (value: number | null) => {
    setSelectedSubUserId(value);
    
    // 清除已展开状态
    setExpandedYears({});
    setExpandedMonths({});
    setExpandedMaterials({});
    
    // 将activeDate设置为空，这样时间线加载后会自动选择最新日期
    if (value === null) {
      setActiveDate('');
    }
    
    // 重新获取日期列表，这会级联触发数据重新加载
    fetchAvailableDates();
  };

  // 监听主用户变化事件
  useEffect(() => {
    const handleResetSubUserSelection = () => {
      // 重置子用户选择为null
      setSelectedSubUserId(null);
      
      // 清除已展开状态
      setExpandedYears({});
      setExpandedMonths({});
      setExpandedMaterials({});
      
      // 将activeDate设置为空，这样时间线加载后会自动选择最新日期
      setActiveDate('');
      
      // 使用setTimeout确保状态更新后再获取数据
      setTimeout(() => {
        fetchAvailableDates();
      }, 0);
    };
    
    // 添加事件监听
    window.addEventListener('resetSubUserSelection', handleResetSubUserSelection);
    
    // 组件卸载时移除事件监听
    return () => {
      window.removeEventListener('resetSubUserSelection', handleResetSubUserSelection);
    };
  }, []);

  // 设置默认展开当前选中日期所在的年份和月份
  useEffect(() => {
    if (activeDate && Object.keys(expandedYears).length === 0) {
      const date = dayjs(activeDate);
      const year = date.format('YYYY');
      const month = date.format('MM');

      const newExpandedYears = {};
      newExpandedYears[year] = true;
      setExpandedYears(newExpandedYears);

      const newExpandedMonths = {};
      newExpandedMonths[`${year}-${month}`] = true;
      setExpandedMonths(newExpandedMonths);
    }
  }, [activeDate, expandedYears]);

  // 获取可用日期列表和数据分布
  const fetchAvailableDates = useCallback(async () => {
    setLoadingDates(true);
    try {
      // 构建请求参数
      const params: any = {
        pageNum: 1,
        pageSize: 1000, // 获取更多日期数据
        orderBy: 'stats_date desc',
        statsType: 'DAILY'
      };

      // 处理用户和账号类型参数
      if (isSubAccountsView) {
        params.userType = 'SUB';
        if (parentId) {
          params.parentId = parentId;
        } else {
          params.userId = 0;
        }
        
        // 如果选择了特定子用户，则添加子用户ID筛选
        if (selectedSubUserId !== null) {
          params.userId = selectedSubUserId;
        }
      } else {
        params.userId = userId;
      }

      const response = await statsUserOperate(params);
      const result = formatApiResponse(response);

      if (result && result.list) {
        // 创建日期到数据量的映射 - 这里不累加createCount，而是统计记录数量
        const dataMap: Record<string, number> = {};

        // 创建年月分组 - 也是统计记录数量
        const yearMonthMap: Record<string, Record<string, number[]>> = {};

        result.list.forEach(item => {
          if (item.statsDate) {
            // 累加记录数量而不是createCount
            if (!dataMap[item.statsDate]) {
              dataMap[item.statsDate] = 0;
            }
            dataMap[item.statsDate] += 1; // 每条记录算一次出图

            // 分组到年月结构
            const date = dayjs(item.statsDate);
            const year = date.format('YYYY');
            const month = date.format('MM');
            const day = date.date();

            if (!yearMonthMap[year]) {
              yearMonthMap[year] = {};
            }

            if (!yearMonthMap[year][month]) {
              yearMonthMap[year][month] = Array(31).fill(0); // 初始化31天
            }

            yearMonthMap[year][month][day - 1] += 1; // 每条记录算一次出图
          }
        });

        setDateDataMap(dataMap);
        setYearMonthGroups(yearMonthMap);

        // 从数据中提取不重复的日期
        const dates = Object.keys(dataMap)
          .sort((a, b) => dayjs(b).unix() - dayjs(a).unix()); // 按日期降序排序

        setAvailableDates(dates);

        // 日期选择策略改进
        if (dates.length > 0) {
          // 当前活动日期
          const currentActive = activeDate;
          
          // 如果当前日期为空或者不在新的日期列表中，选择新列表中的第一个日期（最新日期）
          const isCurrentDateValid = currentActive && dates.includes(currentActive);
          
          if (!currentActive || !isCurrentDateValid) {
            // 如果当前日期无效或为空，选择新列表中的第一个日期（最新日期）
            setActiveDate(dates[0]);
          }
          // 如果当前日期有效，则保持不变
        } else {
          // 如果没有日期数据，清空activeDate
          setActiveDate('');
          // 清空用户详情数据
          setUserDetailData([]);
          setFilteredDetailData([]);
        }
      }
    } catch (error) {
      console.error('获取可用日期失败:', error);
      message.error('获取日期数据失败，请重试');
    } finally {
      setLoadingDates(false);
    }
  }, [userId, parentId, isSubAccountsView, selectedSubUserId, activeDate]);

  // 组件挂载时获取可用日期
  useEffect(() => {
    fetchAvailableDates();
  }, [fetchAvailableDates]);

  // 用户详情表格组件的处理函数
  const fetchUserDetailData = useCallback(async (dateToFetch?: string) => {
    setUserDetailLoading(true);
    try {
      // 构建基础查询参数
      const params: any = {
        pageNum: 1,
        pageSize: 1000, // 设置大值以获取所有数据
        orderBy: sortField && sortOrder
          ? `${sortField.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)} ${sortOrder === 'ascend' ? 'asc' : 'desc'}`
          : 'stats_date desc',
        statsType: 'DAILY'
      };

      // 处理用户和账号类型参数
      if (isSubAccountsView) {
        params.userType = 'SUB';
        if (parentId) {
          params.parentId = parentId;
        } else {
          params.userId = 0;
        }
        
        // 如果选择了特定子用户，则添加子用户ID筛选
        if (selectedSubUserId !== null) {
          params.userId = selectedSubUserId;
        }
      } else {
        params.userId = userId;
      }

      // 设置查询日期
      const dateToUse = dateToFetch || activeDate;
      if (dateToUse) {
        params.statsDate = dateToUse;
      }

      // 发送请求
      const response = await statsUserOperate(params);
      const result = formatApiResponse(response);

      if (result) {
        const newData = result.list || [];
        setUserDetailData(newData);
        // 直接设置过滤后的数据，因为API请求已经包含了筛选条件
        setFilteredDetailData(newData);
      }
    } catch (error) {
      console.error('获取用户数据失败:', error);
      message.error('获取用户数据失败，请重试');
    } finally {
      setUserDetailLoading(false);
    }
  }, [sortField, sortOrder, userId, parentId, isSubAccountsView, activeDate, selectedSubUserId]);

  // 当活动日期变化时，重新加载数据
  useEffect(() => {
    if (activeDate) {
      fetchUserDetailData();
    }
  }, [activeDate, fetchUserDetailData]);

  // 初始化材料展开状态，所有记录默认都是折叠状态
  useEffect(() => {
    if (userDetailData && userDetailData.length > 0) {
      // 设置所有记录默认折叠
      setExpandedMaterials({});
    }
  }, [userDetailData]);

  // 切换材料展开/折叠状态，并按需加载批次详情
  const toggleExpand = (index: number, material: any) => {
    // 获取当前展开状态
    const isCurrentlyExpanded = expandedMaterials[index];
    
    // 更新展开状态
    setExpandedMaterials(prev => {
      const newState = { ...prev };
      newState[index] = !prev[index];
      return newState;
    });
    
    // 如果是展开操作且有批次ID，则加载批次详情
    if (!isCurrentlyExpanded && material.batchIds && material.batchIds.length > 0) {
      // 将字符串ID转为数字ID
      const batchIdsNumber = material.batchIds
        .map((id: string) => parseInt(id, 10))
        .filter((id: number) => !isNaN(id));
      
      if (batchIdsNumber.length > 0) {
        // 标记为加载中
        setBatchLoading(prev => {
          const newState = { ...prev };
          material.batchIds.forEach((id: string) => {
            newState[id] = true;
          });
          return newState;
        });
        
        // 调用API获取批次详情
        batchQueryCreative(batchIdsNumber, true)
          .then(response => {
            if (response && Array.isArray(response)) {
              // 创建新的批次详情对象
              const newBatchDetails = { ...batchDetails };
              
              // 处理返回数据
              response.forEach((item: any) => {
                if (item && item.id) {
                  const batchIdStr = String(item.id);
                  
                  // 使用物料ID作为查找键，确保正确匹配批次
                  material.batchIds.forEach((originalId: string) => {
                    if (batchIdStr === originalId || parseInt(originalId, 10) === item.id) {
                      if (!newBatchDetails[originalId]) {
                        newBatchDetails[originalId] = [];
                      }
                      // 将返回项添加到批次详情中
                      newBatchDetails[originalId].push(item);
                    }
                  });
                }
              });
              
              // 更新批次详情状态
              setBatchDetails(newBatchDetails);
            }
          })
          .catch(error => {
            console.error('获取批次创作详情失败:', error);
            message.error('获取批次创作详情失败');
          })
          .finally(() => {
            // 取消加载状态
            setBatchLoading(prev => {
              const newState = { ...prev };
              material.batchIds.forEach((id: string) => {
                newState[id] = false;
              });
              return newState;
            });
          });
      }
    }
  };

  // 表格筛选处理函数
  const handleTableChange = useCallback((pagination: any, filters: any, sorter: any) => {
    // 处理排序
    if (sorter && sorter.columnKey) {
      setSortField(sorter.columnKey);
      setSortOrder(sorter.order);
    } else {
      setSortField('');
      setSortOrder(undefined);
    }

    // 直接触发加载
    fetchUserDetailData();
  }, [fetchUserDetailData]);

  // 调整卡片标题
  const cardTitle = isSubAccountsView
    ? '子账号历史明细（按照服装统计）'
    : '历史明细（按照服装统计）';

  // 切换年份展开/折叠状态
  const toggleYear = (year: string) => {
    // 创建新的状态对象，只保留当前切换的年份状态
    const newExpandedYears = {};

    // 如果当前年份已经展开，那么折叠它
    if (expandedYears[year]) {
      // 不设置任何年份为展开状态
    } else {
      // 只设置当前年份为展开状态
      newExpandedYears[year] = true;
    }

    setExpandedYears(newExpandedYears);

    // 如果当前年份被折叠，同时折叠该年份下的所有月份
    if (expandedYears[year]) {
      const updatedMonths = {};
      setExpandedMonths(updatedMonths);
    }
  };

  // 切换月份展开/折叠状态
  const toggleMonth = (yearMonth: string) => {
    // 创建新的状态对象，只保留当前切换的月份状态
    const newExpandedMonths = {};

    // 如果当前月份已经展开，那么折叠它
    if (!expandedMonths[yearMonth]) {
      // 设置当前月份为展开状态
      newExpandedMonths[yearMonth] = true;
    }

    setExpandedMonths(newExpandedMonths);
  };

  // 渲染时间线视图
  const renderTimelineView = () => {
    if (loadingDates) {
      return <Spin />;
    }

    // 获取所有年份并排序
    const years = Object.keys(yearMonthGroups).sort((a, b) => b.localeCompare(a));

    // 如果没有数据
    if (years.length === 0) {
      return <Empty description="暂无数据" />;
    }

    // 月份中文名称
    const monthNames = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];

    // 计算当前年份的数据总量 - 统计有出图的天数
    const getYearDataCount = (year: string) => {
      let count = 0;

      // 遍历该年份下所有月份
      for (const month in yearMonthGroups[year]) {
        // 遍历该月所有天
        yearMonthGroups[year][month].forEach(dayCount => {
          if (dayCount > 0) {
            count += 1; // 只统计有数据的天数（出图天数）
          }
        });
      }

      return count;
    };

    // 计算当前月份的数据总量 - 统计有出图的天数
    const getMonthDataCount = (year: string, month: string) => {
      let count = 0;

      if (yearMonthGroups[year] && yearMonthGroups[year][month]) {
        // 遍历该月所有天
        yearMonthGroups[year][month].forEach(dayCount => {
          if (dayCount > 0) {
            count += 1; // 只统计有数据的天数（出图天数）
          }
        });
      }

      return count;
    };

    return (
      <div className="time-series-wrapper" style={{ padding: '0 8px' }}>
        <div style={{ padding: '8px 0', marginBottom: 8, fontWeight: 'bold' }}>
          时间线
        </div>

        <div style={{ padding: '0 0 8px' }}>
          {years.map(year => {
            const yearData = yearMonthGroups[year];
            const yearDataCount = getYearDataCount(year);
            const isYearExpanded = expandedYears[year] || false;

            return (
              <div key={year} className="year-section" style={{ marginBottom: 16, borderBottom: '1px dashed #f0f0f0', paddingBottom: 8 }}>
                <Flex justify="space-between" align="center" style={{ cursor: 'pointer' }} onClick={() => toggleYear(year)}>
                  <Flex align="center">
                    <span style={{ fontSize: '14px', fontWeight: 'bold', marginRight: 8 }}>{year}年</span>
                    <Tag color="blue">出图 {yearDataCount} 天</Tag>
                  </Flex>
                  {isYearExpanded ? <UpOutlined /> : <DownOutlined />}
                </Flex>

                {isYearExpanded && (
                  <div style={{ marginLeft: 8, marginTop: 8 }}>
                    {Object.keys(yearData)
                      .sort((a, b) => b.localeCompare(a)) // 降序排列月份
                      .map(month => {
                        const monthData = yearData[month];
                        const monthHasData = monthData.some(count => count > 0);
                        const monthDataCount = getMonthDataCount(year, month);
                        const monthKey = `${year}-${month}`;
                        const isMonthExpanded = expandedMonths[monthKey] || false;

                        // 如果这个月没有数据，不显示
                        if (!monthHasData) return null;

                        const monthIndex = parseInt(month) - 1;
                        const daysInMonth = dayjs(`${year}-${month}-01`).daysInMonth();

                        return (
                          <div key={monthKey} className="month-section" style={{ marginBottom: 12 }}>
                            <Flex justify="space-between" align="center"
                              style={{ cursor: 'pointer', padding: '4px 8px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}
                              onClick={() => toggleMonth(monthKey)}
                            >
                              <Flex align="center">
                                <div style={{ fontWeight: 'bold', marginRight: 8, color: '#666' }}>
                                  {monthNames[monthIndex]}
                                </div>
                                <Tag color="cyan" style={{ margin: 0 }}>出图 {monthDataCount} 天</Tag>
                              </Flex>
                              {isMonthExpanded ? <UpOutlined style={{ fontSize: 12 }} /> : <DownOutlined style={{ fontSize: 12 }} />}
                            </Flex>

                            {isMonthExpanded && (
                              <Flex wrap="wrap" gap={4} style={{ marginTop: 8, marginLeft: 8 }}>
                                {Array.from({ length: daysInMonth }).map((_, i) => {
                                  const day = i + 1;
                                  const count = monthData[i] || 0;
                                  const dateStr = `${year}-${month.padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                                  const isActive = dateStr === activeDate;

                                  // 如果这天没有数据，不显示
                                  if (count === 0) return null;

                                  return (
                                    <Button
                                      key={dateStr}
                                      size="small"
                                      type={isActive ? "primary" : "default"}
                                      style={{
                                        margin: 0,
                                        padding: '0 8px',
                                        fontWeight: isActive ? 'bold' : undefined
                                      }}
                                      onClick={() => setActiveDate(dateStr)}
                                    >
                                      {day}日
                                    </Button>
                                  );
                                })}
                              </Flex>
                            )}
                          </div>
                        );
                      })}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // 左侧时间导航部分
  const renderTimeNavigation = () => {
    return (
      <div className="operate-stats-time-navigation" style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* 日期选择区域 */}
        <div className="operate-stats-date-picker-area" style={{ marginBottom: 16 }}>
          <Flex justify="space-between" align="center">
            <div style={{
              fontSize: '13px',
              color: '#666',
              display: 'flex',
              alignItems: 'center',
              padding: '6px 12px',
              background: '#f5f7fa',
              borderRadius: '4px',
              width: '100%',
              justifyContent: 'space-between',
              boxShadow: '0 1px 2px rgba(0,0,0,0.03)'
            }}>
              <Flex align="center">
                <CalendarOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                <span style={{ fontWeight: '500' }}>选中日期：</span>
                <span style={{ color: '#333' }}>
                  {activeDate ? dayjs(activeDate).format('YYYY年MM月DD日') : '未选择'}
                </span>
              </Flex>
              <ReloadOutlined
                onClick={() => {
                  fetchAvailableDates();
                  if (activeDate) {
                    fetchUserDetailData();
                  }
                }}
                style={{
                  cursor: 'pointer',
                  color: '#1890ff',
                  fontSize: '14px',
                  padding: '4px',
                  borderRadius: '50%',
                  transition: 'all 0.3s',
                  backgroundColor: 'rgba(24, 144, 255, 0.05)'
                }}
              />
            </div>
          </Flex>
        </div>

        {/* 时间线浏览区域 */}
        <div className="operate-stats-timeline-area" style={{
          border: '1px solid #f0f0f0',
          borderRadius: '4px',
          backgroundColor: '#fff',
          height: 'calc(100% - 40px)',
          maxHeight: '490px',
          overflowY: 'auto',
          padding: '0 12px',
          flex: 1
        }}>
          {renderTimelineView()}
        </div>
      </div>
    );
  };

  // 组片组件，用于显示服装图片、名称和创作数量
  interface MaterialCardProps {
    materialId?: number;
    createCount?: number;
    downloadCount?: number;
    batchIdCount?: number;
    showImage?: string;
  }

  // 单个创作卡片组件
  const MaterialCard: React.FC<MaterialCardProps> = ({
    materialId,
    createCount = 0,
    downloadCount = 0,
    batchIdCount = 0,
    showImage
  }) => {
    // 随机颜色作为备用背景
    const fallbackColor = Math.floor(Math.random() * 16777215).toString(16);
    const materialName = materialId === -1 ? "其他创作（未使用服装）" : `服装${materialId || '未知'}`;
    const isOtherCreation = materialId === -1;
    const hasImage = !!showImage && showImage.trim() !== '';

    return (
      <div
        className="material-card"
        style={{
          width: '200px',
          height: '240px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
          margin: '0 16px 16px 0',
          background: '#fff',
          border: '1px solid #f0f0f0'
        }}
      >
        {/* 服装图片 */}
        <div style={{
          position: 'relative',
          height: '180px',
          overflow: 'hidden',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: isOtherCreation ? '#f9f9f9' : (hasImage ? 'initial' : '#f9f9f9')
        }}>
          {isOtherCreation ? (
            <div style={{
              textAlign: 'center',
              padding: '20px',
              color: '#666'
            }}>
              <FileImageOutlined style={{ fontSize: '36px', marginBottom: '14px', display: 'block', color: '#bbb' }} />
              <div>其他创作，未使用服装</div>
            </div>
          ) : hasImage ? (
            <Image
              src={showImage}
              alt={materialName}
              preview={false}
              style={{
                width: '100%',
                height: '180px',
                objectFit: 'contain'
              }}
              placeholder={
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '100%',
                  height: '180px',
                  background: '#f9f9f9'
                }}>
                  <PictureOutlined style={{ fontSize: '28px', color: '#d9d9d9' }} />
                </div>
              }
            />
          ) : (
            <div style={{
              textAlign: 'center',
              padding: '20px',
              color: '#666'
            }}>
              <PictureOutlined style={{ fontSize: '36px', marginBottom: '14px', display: 'block', color: '#bbb' }} />
              <div>图片加载失败...</div>
            </div>
          )}

          {/* 悬停时显示的遮罩层 - 显示出图次数和下载次数 */}
          <div className="hover-mask">
            <div style={{ marginBottom: '12px', fontSize: '15px', fontWeight: 'bold' }}>
              出图 {createCount} 张
            </div>
            <div style={{ fontSize: '15px', fontWeight: 'bold' }}>
              下载 {downloadCount} 张
            </div>
          </div>

          {/* 右上角创作数量指示 - 使用batchIdCount */}
          <div style={{
            position: 'absolute',
            top: '10px',
            right: '10px',
            background: 'rgba(0,0,0,0.65)',
            color: '#fff',
            borderRadius: '14px',
            padding: '3px 10px',
            fontSize: '12px',
            fontWeight: 'bold',
            backdropFilter: 'blur(2px)'
          }}>
            共{batchIdCount}组创作
          </div>
        </div>

        {/* 服装名称和ID信息 - 移除出图/下载信息 */}
        <div style={{ padding: '10px 12px', height: '60px', background: '#fafafa', borderTop: '1px solid #f0f0f0' }}>
          <div style={{
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '5px',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            color: '#333'
          }}>
            {materialName}
          </div>
          <div style={{
            fontSize: '12px',
            color: '#666'
          }}>
            ID: {materialId || '--'}
          </div>
        </div>
      </div>
    );
  };

  // 处理图片预览
  const handlePreviewImage = (image: string, images: string[] = [], index: number = 0) => {
    setPreviewImage(image);
    setPreviewImgs(images.length > 0 ? images : [image]);
    setPreviewIdx(index);
    setPreviewVisible(true);
  };

  // 处理取消预览
  const handleCancelPreview = () => {
    setPreviewVisible(false);
  };

  // 创建一个批次卡片组件
  const BatchCard = ({ batchId }: { batchId: string }) => {
    const loading = batchLoading[batchId];
    const details = batchDetails[batchId] || [];
    const hasDetails = details.length > 0;
    
    // 获取详细信息
    const batchDetail = hasDetails ? details[0] : null;
    const batchIdStr = String(batchId || '');
    const displayId = batchIdStr.length > 16 ? `${batchIdStr.substring(0, 16)}...` : batchIdStr;
    
    // 获取图片预览
    const showImage = batchDetail?.showImage || 
      (batchDetail?.imageThumbnailUrl || batchDetail?.imageUrl);
    
    // 获取类型名称
    let creativeTypeName = "创作批次";
    let typeColor = "#666"; // 默认颜色
    
    if (batchDetail?.type) {
      const typeInfo = ALLCreativeType.find(t => t.value === batchDetail.type);
      if (typeInfo) {
        creativeTypeName = typeInfo.label;
        
        // 根据不同类型设置不同颜色
        switch(typeInfo.type) {
          case 'IMAGE':
            typeColor = "#1890ff"; // 蓝色
            break;
          case 'DESIGN':
            typeColor = "#722ed1"; // 紫色
            break;
          case 'VIDEO':
            typeColor = "#52c41a"; // 绿色
            break;
          default:
            typeColor = "#fa8c16"; // 橙色
        }
      }
    }
    
    // 获取批次数量
    const batchCnt = batchDetail?.resultImages?.length || 0;
    
    // 获取操作人
    const operatorNick = batchDetail?.userNick || '--';
    
    // 处理卡片点击事件，打开详情弹窗
    const handleClick = () => {
      handleBatchCardClick(batchId);
    };
    
    return (
      <div 
        style={{
          width: '130px',
          height: '180px', // 增加整体高度以适应新增的操作人信息
          border: '1px solid #f0f0f0',
          borderRadius: '6px',
          margin: '0 8px 12px 0',
          overflow: 'hidden',
          background: '#fff',
          boxShadow: '0 1px 3px rgba(0,0,0,0.03)',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'flex-start',
          alignItems: 'center',
          transition: 'all 0.2s ease',
          cursor: 'pointer',
          position: 'relative'
        }}
        onClick={handleClick}
      >
        <div style={{
          width: '100%',
          height: '130px',
          background: '#f7f9fc',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          borderBottom: '1px solid #f0f0f0',
          overflow: 'hidden',
          position: 'relative'
        }}>
          {loading ? (
            <Spin size="small" />
          ) : showImage ? (
            <img 
              src={showImage}
              alt="创作预览"
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                cursor: 'zoom-in'
              }}
            />
          ) : (
            <div style={{
              fontSize: '24px',
              color: '#bbb'
            }}>
              <FileImageOutlined />
            </div>
          )}
          
          {/* 批次数量标识 */}
          {!loading && batchCnt > 0 && (
            <div style={{
              position: 'absolute',
              top: '5px',
              right: '5px',
              background: 'rgba(0,0,0,0.65)',
              color: '#fff',
              fontSize: '10px',
              padding: '2px 5px',
              borderRadius: '10px',
              backdropFilter: 'blur(2px)'
            }}>
              {batchCnt}张
            </div>
          )}
          
          {/* 类型标签 */}
          {!loading && creativeTypeName !== "创作批次" && (
            <div style={{
              position: 'absolute',
              bottom: '5px',
              left: '5px',
              background: `${typeColor}`,
              color: '#fff',
              fontSize: '10px',
              padding: '1px 6px',
              borderRadius: '10px',
              fontWeight: 'bold',
              boxShadow: '0 1px 2px rgba(0,0,0,0.15)'
            }}>
              {creativeTypeName}
            </div>
          )}
        </div>
        <div style={{
          padding: '6px 8px',
          width: '100%'
        }}>
          <div style={{
            fontSize: '11px',
            color: '#999',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            marginBottom: '4px'
          }}>
            批次ID: {displayId}
          </div>
          <div style={{
            fontSize: '11px',
            color: '#999',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            borderTop: '1px dashed #f0f0f0',
            paddingTop: '4px'
          }}>
            操作人: {operatorNick}
          </div>
        </div>
      </div>
    );
  };

  // 修改批次点击处理函数
  const handleBatchCardClick = async (batchId: string) => {
    try {
      // 将批次ID转为数字
      const batchIdNumber = parseInt(batchId, 10);
      if (isNaN(batchIdNumber)) {
        message.error('无效的批次ID');
        return;
      }
      
      setDetailLoading(true);
      
      // 调用接口获取批次详情
      const response = await queryCreativeById(batchIdNumber);
      console.log('批次详情数据:', response);
      
      if (response) {
        // 设置选中的创作对象并显示弹窗
        setSelectedCreation(response);
        // 总是显示详情弹窗，不再自动打开预览
        setShowDetailModal(true);
      } else {
        message.error('获取批次详情失败');
      }
    } catch (error) {
      console.error('获取批次详情失败:', error);
      message.error('获取批次详情失败');
    } finally {
      setDetailLoading(false);
    }
  };

  // 渲染用户详情数据组件
  const renderUserDetailContent = () => {
    if (userDetailLoading) {
      return (
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          width: '100%'
        }}>
          <Spin size="large" />
        </div>
      );
    }

    if (!filteredDetailData || filteredDetailData.length === 0) {
      return (
        <Empty
          description="暂无数据"
          style={{ marginTop: '100px' }}
        />
      );
    }

    // 直接处理每条记录
    const materialStats = filteredDetailData.map(item => {
      let batchIdCount = 0;
      let showImage = '';
      let batchIds: string[] = [];

      try {
        if (item.extInfo && typeof item.extInfo === 'object') {
          const extInfo = item.extInfo as ExtInfo;

          // 获取batchIdList
          if (extInfo.batchIdList && Array.isArray(extInfo.batchIdList)) {
            batchIds = extInfo.batchIdList.filter(batchId => !!batchId);
            batchIdCount = batchIds.length;
          }

          // 获取showImage
          if (extInfo.showImage && typeof extInfo.showImage === 'string') {
            showImage = extInfo.showImage;
          }
        }
      } catch (e) {
        console.error('解析extInfo失败:', e);
      }

      return {
        materialId: item.materialId,
        createCount: item.createCount || 0,
        downloadCount: item.downloadCount || 0,
        batchIdCount: batchIdCount,
        batchIds: batchIds,
        showImage,
        name: item.materialId === -1 ? "其他创作（未使用服装）" : `${item.extInfo?.modelName || '未查询到服装名称'}`,
        userId: item.userId
      };
    });

    // 按创作组数（batchIdCount）排序（降序）
    materialStats.sort((a, b) => (b.batchIdCount || 0) - (a.batchIdCount || 0));

    return (
      <div style={{
        position: 'relative',
        height: '100%',
        overflow: 'auto',
        background: token.colorFillAlter,
        borderRadius: token.borderRadiusLG,
        padding: '0 16px 20px'
      }}>
        <h3 style={{
          marginBottom: '20px',
          fontSize: '16px',
          fontWeight: 'bold',
          position: 'relative',
          paddingLeft: '20px',
          paddingTop: '16px',
          color: '#1f1f1f'
        }}>
          <span style={{
            position: 'absolute',
            left: '10px',
            top: '29px',
            transform: 'translateY(-50%)',
            width: '4px',
            height: '16px',
            background: '#1890ff',
            borderRadius: '2px',
            marginRight: '8px'
          }}></span>
          {activeDate ? dayjs(activeDate).format('YYYY年MM月DD日') : ''} 创作记录 （共{filteredDetailData.length}条）
        </h3>

        <div style={{
          display: 'flex',
          flexWrap: 'wrap',
        }}>
          {materialStats.map((material, index) => (
            <div key={index} style={{ 
              width: 'calc(100% - 20px)',
              marginBottom: '24px', 
              background: '#fff',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
              overflow: 'hidden'
            }}>
              <div 
                style={{
                  padding: '16px',
                  borderBottom: material.batchIdCount > 0 ? '1px solid #f0f0f0' : 'none',
                  display: 'flex',
                  alignItems: 'center',
                  cursor: material.batchIdCount > 0 ? 'pointer' : 'default'
                }}
                onClick={() => material.batchIdCount > 0 && toggleExpand(index, material)}
              >
                {/* 服装图片/占位图 */}
                <div style={{
                  width: '60px',
                  height: '60px',
                  borderRadius: '4px',
                  overflow: 'hidden',
                  marginRight: '16px',
                  background: '#f5f5f5',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  {material.showImage ? (
                    <img 
                      src={material.showImage} 
                      alt={material.name}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover'
                      }}
                    />
                  ) : (
                    <PictureOutlined style={{ fontSize: '24px', color: '#d9d9d9' }} />
                  )}
                </div>
                
                {/* 服装信息 */}
                <div style={{ flex: 1 }}>
                  <div style={{
                    fontSize: '16px',
                    fontWeight: 'bold',
                    marginBottom: '6px',
                    color: '#333'
                  }}>
                    {material.name}
                  </div>
                  <Flex align="center" gap={16}>
                    <div style={{ fontSize: '13px', color: '#666' }}>
                    服装ID: {material.materialId || '--'}
                    </div>
                    <div style={{ fontSize: '13px', color: '#666' }}>
                      <Tooltip title="总出图数量">
                        <span style={{ marginRight: '4px' }}>出图:</span>
                        <span style={{ color: '#1890ff', fontWeight: 'bold' }}>{material.createCount}</span>
                      </Tooltip>
                    </div>
                    <div style={{ fontSize: '13px', color: '#666' }}>
                      <Tooltip title="总下载数量">
                        <span style={{ marginRight: '4px' }}>下载:</span>
                        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>{material.downloadCount}</span>
                      </Tooltip>
                    </div>
                  </Flex>
                </div>
                
                {/* 创作组数量和展开/折叠图标 */}
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Tag color="blue" style={{ marginRight: '8px' }}>
                    共 {material.batchIdCount} 组创作
                  </Tag>
                  
                  {material.batchIdCount > 0 && (
                    expandedMaterials[index] ? 
                      <UpOutlined style={{ color: '#999' }} /> : 
                      <DownOutlined style={{ color: '#999' }} />
                  )}
                </div>
              </div>
              
              {/* 创作卡片组 - 仅当展开且有批次时显示 */}
              {material.batchIdCount > 0 && expandedMaterials[index] && (
                <div style={{ padding: '16px' }}>
                  <div style={{ 
                    marginBottom: '12px', 
                    fontSize: '14px', 
                    color: '#666',
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    <div style={{
                      width: '3px',
                      height: '14px',
                      background: '#1890ff',
                      borderRadius: '2px',
                      marginRight: '8px'
                    }}></div>
                    批次明细
                  </div>
                  <Flex wrap="wrap">
                    {material.batchIds.map((batchId: string, batchIndex: number) => (
                      <BatchCard key={batchIndex} batchId={batchId} />
                    ))}
                  </Flex>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 格式化文本函数
  const formatText = (text: string | undefined, maxLength: number) => {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  };

  // 渲染组件
  return (
    <div className="operate-stats-stats-detail-section">
      {/* 添加全局样式 */}
      <GlobalStyle />

      <Card
        title={
          <Flex align="center" justify="space-between">
            <span>{cardTitle}</span>
            {isSubAccountsView && (
              <Select
                placeholder="选择子用户"
                style={{ width: 220 }}
                allowClear
                loading={subUsersLoading}
                onChange={handleSubUserChange}
                optionLabelProp="label" 
                value={selectedSubUserId}
                showSearch
                filterOption={(input, option) => {
                  const nickName = option?.nickName?.toLowerCase() || '';
                  const userId = option?.userId?.toString() || '';
                  const searchText = input.toLowerCase();
                  return nickName.includes(searchText) || userId.includes(searchText);
                }}
              >
                {subUsers.map(user => (
                  <Select.Option 
                    key={user.id} 
                    value={user.id}
                    label={`${user.nickName}(${user.id})`}
                    nickName={user.nickName}
                    userId={user.id}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span style={{ 
                        maxWidth: '140px', 
                        overflow: 'hidden', 
                        textOverflow: 'ellipsis', 
                        whiteSpace: 'nowrap' 
                      }}>
                        {user.nickName || '用户'}
                      </span>
                      <span style={{ color: '#999', flexShrink: 0 }}>
                        {user.id}
                      </span>
                    </div>
                  </Select.Option>
                ))}
              </Select>
            )}
          </Flex>
        }
        className="operate-stats-detail-card"
        style={{
          height: '670px',
          minHeight: '600px',
          overflow: 'hidden'
        }}
      >
        <Row gutter={16} style={{ height: '670px' }}>
          {/* 左侧时间导航部分 - 占3列 */}
          <Col span={6} style={{ height: '100%' }}>
            {renderTimeNavigation()}
          </Col>

          {/* 右侧数据展示部分 - 占7列，现在改为卡片布局 */}
          <Col span={18} style={{ height: '620px' }}>
            <div style={{ height: '100%', overflow: 'hidden', position: 'relative' }}>
              {renderUserDetailContent()}
            </div>
          </Col>
        </Row>
      </Card>
      
      {/* 批次详情弹窗 */}
      <Modal
        open={showDetailModal}
        onCancel={() => setShowDetailModal(false)}
        footer={null}
        width={selectedCreation?.type === 'CREATE_VIDEO' ? 'auto' : 1160}
        centered={true}
        closable={true}
        styles={{
          mask: {
            backgroundColor: 'rgba(0,0,0,0.4)',
          },
          body: {
            padding: '24px 24px 16px 24px',
          },
        }}
      >
        {detailLoading ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
            <Spin size="large" />
          </div>
        ) : selectedCreation ? (
          <div className="history-detail-content"
               style={{
                 height: selectedCreation?.type === 'CREATE_VIDEO' ? 'auto' : 686,
                 minHeight: 600,
               }}>
            <div className={'history-detail-left'}>
              <div className={'history-detail-left-inner'}>
                {selectedCreation?.type === 'CREATE_IMAGE' && (
                  <div className={'history-left-inner-top'}>
                    <div className="font-pf text14 weight" style={{ textAlign: 'left', width: '100%' }}>服装</div>
                    <div style={{ textAlign: 'left', width: '100%' }}>
                      <Tooltip title={selectedCreation?.modelName}>
                        <span className="font-pf text14">{formatText(selectedCreation?.modelName, 12)}</span>
                      </Tooltip>
                    </div>
                    <img alt="cloth model"
                        style={{ cursor: 'pointer' }}
                        src={selectedCreation?.modelShowImg} />
                  </div>
                )}

                <div className="history-detail-section">
                  {selectedCreation?.faceName && (
                    <div className="history-detail-info-row">
                      <div className="history-detail-info-label">模特</div>
                      <div className="history-detail-info-value">{selectedCreation?.faceName}</div>
                    </div>
                  )}

                  {selectedCreation?.type === 'CREATE_IMAGE' && (
                    <div className="history-detail-info-row">
                      <div className="history-detail-info-label">场景</div>
                      <div className="history-detail-info-value">{selectedCreation?.sceneName || '自定义场景'}</div>
                    </div>
                  )}

                  {selectedCreation?.imageProportionName && selectedCreation?.type !== 'CREATE_VIDEO' && (
                    <div className="history-detail-info-row">
                      <div className="history-detail-info-label">图片比例</div>
                      <div className="history-detail-info-value">{selectedCreation?.imageProportionName}</div>
                    </div>
                  )}

                  <div className="history-detail-info-row">
                    <div className="history-detail-info-label">操作人</div>
                    <div className="history-detail-info-value">{selectedCreation?.operatorNick}</div>
                  </div>

                  <div className="history-detail-info-row">
                    <div className="history-detail-info-label">任务ID</div>
                    <div className="history-detail-info-value">{selectedCreation?.id}</div>
                  </div>

                  <div className="history-detail-info-row">
                    <div className="history-detail-info-label">创建时间</div>
                    <div className="history-detail-info-value">{selectedCreation?.createTime}</div>
                  </div>
                </div>
              </div>
            </div>
            <div className="history-detail-right"
                 style={{
                   paddingTop: 50,
                   padding: (selectedCreation?.type === 'CREATE_VIDEO') ? '6px 16px' : '',
                   height: selectedCreation?.type === 'CREATE_VIDEO' ? 'auto' : 686,
                   width: selectedCreation?.type === 'CREATE_VIDEO' ? 'auto' : 920,
                 }}>
              {selectedCreation &&
                selectedCreation.type !== 'CREATE_VIDEO' &&
                selectedCreation.resultImages && (
                  <Flex wrap="wrap" gap={8}
                        style={{ width: '100%', justifyContent: 'flex-start', alignItems: 'flex-start' }}>
                    {selectedCreation.resultImages.map((imgUrl: string, index: number) => (
                      <div key={index}
                           className="history-detail-image-item"
                           style={{
                             width: 180,
                             height: 'auto',
                             margin: '0 8px 8px 0',
                           }}>
                        <div className={'history-detail-img-container'} style={{ position: 'relative' }}>
                          <img 
                            src={imgUrl} 
                            className={'image-gallery width-100'} 
                            style={{ width: '100%', height: '100%', objectFit: 'contain' }}
                            onClick={() => handlePreviewImage(imgUrl, selectedCreation.resultImages, index)}
                          />
                          <div className={'history-detail-img-icon'} onClick={(e) => {
                            e.stopPropagation();
                            window.open(imgUrl, '_blank');
                          }}
                               style={{
                                 position: 'absolute',
                                 right: '8px',
                                 bottom: '8px',
                                 width: '20px',
                                 height: '20px',
                                 borderRadius: '5.68px',
                                 display: 'flex',
                                 justifyContent: 'center',
                                 alignItems: 'center',
                                 padding: '4px',
                                 background: 'rgba(0, 0, 0, 0.6)',
                                 cursor: 'pointer',
                               }}>
                            <CloudDownloadOutlined  style={{ fontSize: '12px', color: '#fff' }} />
                          </div>
                        </div>
                      </div>
                    ))}
                  </Flex>
                )}
            </div>
          </div>
        ) : (
          <Empty description="无法获取详情数据" />
        )}
      </Modal>
      
      {/* 图片预览组件 */}
      <ImgPreview
        previewVisible={previewVisible}
        handleCancelPreview={handleCancelPreview}
        previewImage={previewImage}
        needSwitch={previewImgs.length > 1}
        previewIdx={previewIdx}
        previewImgs={previewImgs}
        creativeBatch={selectedCreation}
        needWatermark={true}
        showTools={false}
      />
    </div>
  );
};

export default UserDetailSection; 