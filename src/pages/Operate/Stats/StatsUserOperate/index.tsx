import '@/pages/Operate/Stats/StatsUserOperate/index.less';
import React, { useEffect, useState, useCallback, memo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Card,
  Tabs,
  Select,
  Empty,
  DatePicker,
  Spin,
  Flex,
  message,
  Statistic,
} from 'antd';
import {
  InfoCircleOutlined
} from '@ant-design/icons';
import { PageInfo, statsUserOperate, StatsUserOperateVO } from '@/services/DataController';
import { getUserInfo } from '@/utils/utils';
import { allByRoleTypes, queryAllMaster, RoleTypesItems } from '@/services/UserController';
import dayjs from 'dayjs';
import ChartsUserOperate from '@/components/Operate/Stats/ChartsUserOperate';
import UserDetailSection from './UserDetailSection';
import OverviewCard from './OverviewCard';

const { RangePicker } = DatePicker;

// ChartSection组件接口
interface ChartSectionProps {
  activeTab: string;
  onTabChange: (activeKey: string) => void;
  chartType: string;
  onChartTypeChange: (type: string) => void;
  dateRange: [dayjs.Dayjs, dayjs.Dayjs] | null;
  onDateRangeChange: (dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => void;
  data: StatsUserOperateVO[];
  loading: boolean;
  metrics: {
    totalCreateCount: number;
    totalDownloadCount: number;
  };
  tabItems: { key: string; label: string }[];
}

// ChartSection组件
const ChartSection = memo(({
  activeTab,
  onTabChange,
  chartType,
  onChartTypeChange,
  dateRange,
  onDateRangeChange,
  data,
  loading,
  metrics,
  tabItems
}: ChartSectionProps) => {
  // 检查是否有数据
  const hasData = Array.isArray(data) && data.length > 0;
  // 是否为TOTAL类型
  const isTotalTab = activeTab === 'TOTAL';

  // 定义快捷时间选择选项
  const quickTimeOptions = [
    { label: '最近7天', value: '7d' },
    { label: '最近30天', value: '30d' },
    { label: '本月', value: 'thisMonth' },
    { label: '上月', value: 'lastMonth' }
  ];

  // 扩展图表类型选项
  const chartTypeOptions = [
    { value: 'bar', label: '柱状图' },
    { value: 'line', label: '折线图' },
    { value: 'area', label: '面积图' },
    { value: 'pie', label: '饼图', disabled: isTotalTab },
    { value: 'stack', label: '堆叠图', disabled: isTotalTab }
  ];

  // 处理快捷时间选择
  const handleQuickTimeSelect = (value: string) => {
    let start: dayjs.Dayjs;
    let end: dayjs.Dayjs = dayjs();

    switch (value) {
      case '7d':
        start = dayjs().subtract(6, 'day');
        break;
      case '30d':
        start = dayjs().subtract(29, 'day');
        break;
      case 'thisMonth':
        start = dayjs().startOf('month');
        break;
      case 'lastMonth':
        start = dayjs().subtract(1, 'month').startOf('month');
        end = dayjs().subtract(1, 'month').endOf('month');
        break;
      default:
        start = dayjs().subtract(6, 'day');
    }

    onDateRangeChange([start, end]);
  };

  const renderDatePicker = () => {
    if (activeTab === 'DAILY' || activeTab === 'TOTAL') {
      return (
        <RangePicker
          style={{ width: 250 }}
          value={dateRange}
          onChange={(dates) => {
            onDateRangeChange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null);
          }}
          allowClear
        />
      );
    }

    if (activeTab === 'WEEKLY') {
      return (
        <RangePicker
          picker="week"
          placeholder={["开始周", "结束周"]}
          style={{ width: 250 }}
          value={dateRange ? [dayjs(dateRange[0]), dayjs(dateRange[1])] : null}
          onChange={(dates) => {
            if (dates && dates[0] && dates[1]) {
              const startWeek = dates[0].startOf('week');
              const endWeek = dates[1].endOf('week');
              onDateRangeChange([startWeek, endWeek]);
            } else {
              onDateRangeChange(null);
            }
          }}
          allowClear
        />
      );
    }

    if (activeTab === 'MONTHLY') {
      return (
        <RangePicker
          picker="month"
          placeholder={["开始月", "结束月"]}
          style={{ width: 250 }}
          value={dateRange ? [dayjs(dateRange[0]), dayjs(dateRange[1])] : null}
          onChange={(dates) => {
            if (dates && dates[0] && dates[1]) {
              const startMonth = dates[0].startOf('month');
              const endMonth = dates[1].endOf('month');
              onDateRangeChange([startMonth, endMonth]);
            } else {
              onDateRangeChange(null);
            }
          }}
          allowClear
        />
      );
    }

    return null;
  };

  return (
    <div className="operate-stats-stats-summary-content">
      <Flex justify="space-between" align="center" className="operate-stats-chart-controls" wrap="wrap">
        <Tabs
          className="operate-stats-upper-tabs"
          activeKey={activeTab}
          onChange={onTabChange}
          items={tabItems}
          style={{ marginBottom: 16 }}
        />

        <Flex gap={8} align="center" wrap="wrap" style={{ marginBottom: 16 }}>
          <Select
            value={chartType}
            onChange={onChartTypeChange}
            style={{ width: 100 }}
            options={chartTypeOptions}
          />

          {renderDatePicker()}

          <Select
            placeholder="快捷选择"
            style={{ width: 100 }}
            options={quickTimeOptions}
            onChange={handleQuickTimeSelect}
            allowClear
          />
        </Flex>
      </Flex>

      {/* Metrics Cards - 在TOTAL类型时不显示 */}
      {!isTotalTab && (
        <div className="operate-stats-metrics-row">
          <Flex style={{ width: '100%' }} gap={16}>
            <MetricCard
              key="totalCreateCount"
              title="用户出图量"
              value={metrics.totalCreateCount}
              metricType="create"
              loading={loading}
              className="operate-stats-metric-card-first"
              style={{ flex: 1 }}
            />
            <MetricCard
              key="totalDownloadCount"
              title="图片下载量"
              value={metrics.totalDownloadCount}
              metricType="download"
              loading={loading}
              className="operate-stats-metric-card"
              style={{ flex: 1 }}
            />
          </Flex>
        </div>
      )}

      {/* Chart */}
      <div className="operate-stats-chart-container">
        {loading ? (
          <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Spin>
              <div style={{ padding: '20px', background: 'transparent' }}>加载中...</div>
            </Spin>
          </div>
        ) : !hasData ? (
          <div style={{ height: 300, display: 'flex', justifyContent: 'center', background: '#f0f2f5', alignItems: 'center', borderRadius: 4 }}>
            <Empty description="暂无数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
          </div>
        ) : (
          <ChartsUserOperate
            data={data}
            chartType={(chartType === 'pie' || chartType === 'area' || chartType === 'stack') && isTotalTab ? 'bar' : (chartType as 'bar' | 'line' | 'pie')}
            statsType={activeTab as 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'TOTAL'}
            height="100%"
          />
        )}
      </div>
    </div>
  );
});

// Type guard to check if response is a PageInfo
function isPageInfo(response: any): response is PageInfo<StatsUserOperateVO> {
  return response &&
    typeof response === 'object' &&
    Array.isArray(response.list) &&
    typeof response.totalCount === 'number';
}

// Convert API response to appropriate format
function formatApiResponse(response: any): PageInfo<StatsUserOperateVO> {
  let result: PageInfo<StatsUserOperateVO>;

  if (isPageInfo(response)) {
    result = response;
  } else if (Array.isArray(response)) {
    result = {
      list: response,
      totalCount: response.length
    };
  } else {
    result = {
      list: [],
      totalCount: 0
    };
  }

  return result;
}

// 抽离统计卡片组件
interface MetricCardProps {
  title: string;
  value: number;
  metricType: 'create' | 'download';
  loading?: boolean;
  precision?: number;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

const MetricCard = memo(({
  title,
  value,
  metricType,
  loading = false,
  precision = 0,
  prefix,
  suffix,
  className,
  style
}: MetricCardProps) => {
  // 不同类型的样式名
  const metricClassMap = {
    create: 'operate-stats-metric-create',
    download: 'operate-stats-metric-download'
  };

  return (
    <Card className={className || 'operate-stats-metric-card'} style={style}>
      <Statistic
        title={title}
        value={value}
        precision={precision}
        className={metricClassMap[metricType]}
        loading={loading}
        prefix={prefix}
        suffix={suffix}
      />
    </Card>
  );
});

// 扩展全局数据接口
interface GlobalMetricsData {
  // 累计出图量
  totalCreateCount: number;
  // 累计下载量
  totalDownloadCount: number;
  // 累计使用服装数
  materialCount: number;
  // 累计出图任务数
  createTaskCount: number;
}

// 我的数据标签内容
const SummaryChartSection: React.FC<{
  summaryActiveTab: string,
  setSummaryActiveTab: (tab: string) => void,
  summaryDateRange: [dayjs.Dayjs, dayjs.Dayjs] | null,
  setSummaryDateRange: (range: [dayjs.Dayjs, dayjs.Dayjs] | null) => void,
  setInitialTabChange: (value: boolean) => void,
  userId: number,
}> = ({
  summaryActiveTab,
  summaryDateRange,
  setSummaryDateRange,
  userId,
}) => {
    const [summaryLoading, setSummaryLoading] = useState<boolean>(false);
    const [summaryData, setSummaryData] = useState<StatsUserOperateVO[]>([]);
    const [metrics, setMetrics] = useState({
      totalCreateCount: 0,
      totalDownloadCount: 0
    });
    // 全局数据状态
    const [globalMetrics, setGlobalMetrics] = useState<GlobalMetricsData>({
      totalCreateCount: 0,
      totalDownloadCount: 0,
      materialCount: 0,
      createTaskCount: 0
    });
    const [globalLoading, setGlobalLoading] = useState<boolean>(false);

    const DEFAULT_CHARTS_SIZE = 30;

    // 设置默认日期范围
    useEffect(() => {
      if (!summaryDateRange && !summaryLoading) {
        // 如果没有日期范围且不在加载中，设置默认日期范围（最近7天）
        const endDate = dayjs();
        const startDate = dayjs().subtract(6, 'day');
        setSummaryDateRange([startDate, endDate]);
      }
    }, [summaryDateRange, summaryLoading, setSummaryDateRange]);

    // 添加获取全局数据的函数
    const fetchGlobalData = useCallback(async () => {
      setGlobalLoading(true);
      try {
        // 构建查询参数 - 不设置日期范围，获取全部数据
        const params: any = {
          statsType: 'TOTAL', // 使用总统计模式
          userId: userId,
          pageNum: 1,
          pageSize: 1000, // 使用较大的页面大小以获取更多数据
          orderBy: 'stats_date desc'
        };

        const response = await statsUserOperate(params);
        const result = formatApiResponse(response);

        if (result && result.list) {
          // 计算总体指标
          const totalCreateCount = result.list.reduce((sum, item) => sum + (item.createCount || 0), 0);
          const totalDownloadCount = result.list.reduce((sum, item) => sum + (item.downloadCount || 0), 0);

          // 计算不同素材的数量
          const distinctMaterials = new Set();
          result.list.forEach(item => {
            if (item.materialId) {
              distinctMaterials.add(item.materialId);
            }
          });

          setGlobalMetrics({
            totalCreateCount,
            totalDownloadCount,
            materialCount: distinctMaterials.size,
            createTaskCount: totalCreateCount
          });
        }
      } catch (error) {
        console.error('获取全局数据失败:', error);
      } finally {
        setGlobalLoading(false);
      }
    }, [userId]);

    // 提供更新全局指标的回调函数
    const updateGlobalMetrics = useCallback((metrics: Partial<GlobalMetricsData>) => {
      setGlobalMetrics(prev => ({
        ...prev,
        ...metrics
      }));
    }, []);

    // 在首次加载时获取全局数据
    useEffect(() => {
      fetchGlobalData();
    }, [fetchGlobalData]);

    // 根据统计类型和日期范围计算pageSize
    const calculatePageSize = (statsType: string, dateRange: [dayjs.Dayjs, dayjs.Dayjs] | null): number => {
      if (!dateRange) return DEFAULT_CHARTS_SIZE;
      switch (statsType) {
        case 'DAILY':
        case 'TOTAL':
          return dateRange ? Math.min(dateRange[1].diff(dateRange[0], 'days') + 1, DEFAULT_CHARTS_SIZE) : DEFAULT_CHARTS_SIZE;
        case 'WEEKLY':
          const weekDaysBetween = dateRange ? dateRange[1].diff(dateRange[0], 'days') : 7;
          return Math.min(Math.ceil(weekDaysBetween / 7), DEFAULT_CHARTS_SIZE);
        case 'MONTHLY':
          const monthDaysBetween = dateRange ? dateRange[1].diff(dateRange[0], 'days') : 30;
          return Math.min(Math.ceil(monthDaysBetween / 30), DEFAULT_CHARTS_SIZE);
        default:
          return DEFAULT_CHARTS_SIZE;
      }
    };

    // 主图表数据加载函数
    const fetchSummaryData = useCallback(async (dateRange?: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
      setSummaryLoading(true);
      try {
        const params: any = {
          statsType: summaryActiveTab,
          userId: userId,
          pageNum: 1,
          pageSize: DEFAULT_CHARTS_SIZE,
          orderBy: 'stats_date desc'
        };

        // 使用传入的日期范围参数，如果没有则使用状态中的日期范围
        const actualDateRange = dateRange !== undefined ? dateRange : summaryDateRange;

        if (actualDateRange && actualDateRange.length === 2) {
          params.statsDateLower = actualDateRange[0].format('YYYY-MM-DD');
          params.statsDateUpper = actualDateRange[1].format('YYYY-MM-DD');
        }

        // 使用calculatePageSize函数计算pageSize
        params.pageSize = calculatePageSize(summaryActiveTab, actualDateRange);

        const response = await statsUserOperate(params);
        const result = formatApiResponse(response);

        if (result && result.list) {
          setSummaryData(result.list);

          // 计算指标
          const totalCreateCount = result.list.reduce((sum, item) => sum + (item.createCount || 0), 0);
          const totalDownloadCount = result.list.reduce((sum, item) => sum + (item.downloadCount || 0), 0);

          setMetrics({
            totalCreateCount,
            totalDownloadCount
          });
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        message.error('获取数据失败，请重试');
      } finally {
        setSummaryLoading(false);
      }
    }, [summaryActiveTab, summaryDateRange, DEFAULT_CHARTS_SIZE, userId]);

    // 监听依赖变化时加载数据
    useEffect(() => {
      fetchSummaryData();
    }, [fetchSummaryData]);

    return (
      <div className="operate-stats-stats-summary-section">
        {/* 数据概览组件 */}
        <OverviewCard
          globalMetrics={globalMetrics}
          globalLoading={globalLoading}
          userId={userId}
          isSubAccountsView={false}
          updateGlobalMetrics={updateGlobalMetrics}
        />

        {/* 历史明细卡片 */}
        <UserDetailSection
          userId={userId}
          isSubAccountsView={false}
        />
      </div>
    );
  };

// 子账号数据标签内容
const SubAccountSummaryChartSection: React.FC<{
  summaryActiveTab: string,
  setSummaryActiveTab: (tab: string) => void,
  summaryDateRange: [dayjs.Dayjs, dayjs.Dayjs] | null,
  setSummaryDateRange: (range: [dayjs.Dayjs, dayjs.Dayjs] | null) => void,
  setInitialTabChange: (value: boolean) => void,
  userId: number,
  parentId?: number,
}> = ({
  summaryActiveTab,
  summaryDateRange,
  setSummaryDateRange,
  userId,
  parentId,
}) => {
    const [summaryLoading, setSummaryLoading] = useState<boolean>(false);
    const [summaryData, setSummaryData] = useState<StatsUserOperateVO[]>([]);
    const [metrics, setMetrics] = useState({
      totalCreateCount: 0,
      totalDownloadCount: 0
    });
    // 全局数据状态
    const [globalMetrics, setGlobalMetrics] = useState<GlobalMetricsData>({
      totalCreateCount: 0,
      totalDownloadCount: 0,
      materialCount: 0,
      createTaskCount: 0
    });
    const [globalLoading, setGlobalLoading] = useState<boolean>(false);

    const DEFAULT_CHARTS_SIZE = 30;

    // 设置默认日期范围
    useEffect(() => {
      if (!summaryDateRange && !summaryLoading) {
        // 如果没有日期范围且不在加载中，设置默认日期范围（最近7天）
        const endDate = dayjs();
        const startDate = dayjs().subtract(6, 'day');
        setSummaryDateRange([startDate, endDate]);
      }
    }, [summaryDateRange, summaryLoading, setSummaryDateRange]);

    // 添加获取全局数据的函数
    const fetchGlobalData = useCallback(async () => {
      setGlobalLoading(true);
      try {
        // 构建查询参数 - 不设置日期范围，获取全部数据
        const params: any = {
          statsType: 'TOTAL', // 使用总统计模式
          userId: userId,
          pageNum: 1,
          pageSize: 1000, // 使用较大的页面大小以获取更多数据
          orderBy: 'stats_date desc',
          userType: 'SUB' // 指定为子账号查询
        };

        // 如果有parentId，添加到查询参数中
        if (parentId) {
          params.parentId = parentId;
        }

        const response = await statsUserOperate(params);
        const result = formatApiResponse(response);

        if (result && result.list) {
          // 计算总体指标
          const totalCreateCount = result.list.reduce((sum, item) => sum + (item.createCount || 0), 0);
          const totalDownloadCount = result.list.reduce((sum, item) => sum + (item.downloadCount || 0), 0);

          // 计算不同素材的数量
          const distinctMaterials = new Set();
          result.list.forEach(item => {
            if (item.materialId) {
              distinctMaterials.add(item.materialId);
            }
          });

          setGlobalMetrics({
            totalCreateCount,
            totalDownloadCount,
            materialCount: distinctMaterials.size,
            createTaskCount: totalCreateCount
          });
        }
      } catch (error) {
        console.error('获取全局数据失败:', error);
      } finally {
        setGlobalLoading(false);
      }
    }, [userId, parentId]);

    // 提供更新全局指标的回调函数
    const updateGlobalMetrics = useCallback((metrics: Partial<GlobalMetricsData>) => {
      setGlobalMetrics(prev => ({
        ...prev,
        ...metrics
      }));
    }, []);

    // 在首次加载时获取全局数据
    useEffect(() => {
      fetchGlobalData();
    }, [fetchGlobalData]);

    // 根据统计类型和日期范围计算pageSize
    const calculatePageSize = (statsType: string, dateRange: [dayjs.Dayjs, dayjs.Dayjs] | null): number => {
      if (!dateRange) return DEFAULT_CHARTS_SIZE;
      switch (statsType) {
        case 'DAILY':
        case 'TOTAL':
          return dateRange ? Math.min(dateRange[1].diff(dateRange[0], 'days') + 1, DEFAULT_CHARTS_SIZE) : DEFAULT_CHARTS_SIZE;
        case 'WEEKLY':
          const weekDaysBetween = dateRange ? dateRange[1].diff(dateRange[0], 'days') : 7;
          return Math.min(Math.ceil(weekDaysBetween / 7), DEFAULT_CHARTS_SIZE);
        case 'MONTHLY':
          const monthDaysBetween = dateRange ? dateRange[1].diff(dateRange[0], 'days') : 30;
          return Math.min(Math.ceil(monthDaysBetween / 30), DEFAULT_CHARTS_SIZE);
        default:
          return DEFAULT_CHARTS_SIZE;
      }
    };

    // 主图表数据加载函数
    const fetchSummaryData = useCallback(async (dateRange?: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
      setSummaryLoading(true);
      try {
        const params: any = {
          statsType: summaryActiveTab,
          userId: userId,
          pageNum: 1,
          pageSize: DEFAULT_CHARTS_SIZE,
          orderBy: 'stats_date desc',
          userType: 'SUB' // 指定为子账号查询
        };

        // 如果有parentId，添加到查询参数中
        if (parentId) {
          params.parentId = parentId;
        }

        // 使用传入的日期范围参数，如果没有则使用状态中的日期范围
        const actualDateRange = dateRange !== undefined ? dateRange : summaryDateRange;

        if (actualDateRange && actualDateRange.length === 2) {
          params.statsDateLower = actualDateRange[0].format('YYYY-MM-DD');
          params.statsDateUpper = actualDateRange[1].format('YYYY-MM-DD');
        }

        // 使用calculatePageSize函数计算pageSize
        params.pageSize = calculatePageSize(summaryActiveTab, actualDateRange);

        const response = await statsUserOperate(params);
        const result = formatApiResponse(response);

        if (result && result.list) {
          setSummaryData(result.list);

          // 计算指标
          const totalCreateCount = result.list.reduce((sum, item) => sum + (item.createCount || 0), 0);
          const totalDownloadCount = result.list.reduce((sum, item) => sum + (item.downloadCount || 0), 0);

          setMetrics({
            totalCreateCount,
            totalDownloadCount
          });
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        message.error('获取数据失败，请重试');
      } finally {
        setSummaryLoading(false);
      }
    }, [summaryActiveTab, summaryDateRange, DEFAULT_CHARTS_SIZE, userId, parentId]);

    // 监听依赖变化时加载数据
    useEffect(() => {
      fetchSummaryData();
    }, [fetchSummaryData]);

    return (
      <div className="operate-stats-stats-summary-section">
        {/* 数据概览组件 */}
        <OverviewCard
          globalMetrics={globalMetrics}
          globalLoading={globalLoading}
          userId={userId}
          isSubAccountsView={true}
          updateGlobalMetrics={updateGlobalMetrics}
        />

        {/* 历史明细卡片 */}
        <UserDetailSection
          userId={userId}
          isSubAccountsView={true}
          parentId={parentId}
        />
      </div>
    );
  };

const StatsUserOperate: React.FC = () => {
  const [summaryActiveTab, setSummaryActiveTab] = useState<string>('DAILY');
  const [summaryDateRange, setSummaryDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);

  // 添加状态跟踪是否是初始/手动Tab切换
  const [initialTabChange, setInitialTabChange] = useState(true);

  // 添加标签页状态
  const [activeViewTab, setActiveViewTab] = useState<string>('myData');

  // 添加当前选择的用户ID
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);

  // 添加主账户列表状态，扩展类型定义
  const [masterOptions, setMasterOptions] = useState<{ 
    label: string | React.ReactNode; 
    value: string;
    nickName?: string;
  }[]>([]);

  // 获取用户信息
  const userInfo = getUserInfo();
  const userId = userInfo?.id || 0; // 使用id字段，如果不存在则使用0
  const userType = userInfo?.userType || 'SUB'; // 获取用户类型，默认为SUB

  // 判断是否为主账户
  const isMasterUser = userType === 'MASTER';

  // 获取所有主账户
  useEffect(() => {
    if (userInfo?.roleType === 'ADMIN') {


      allByRoleTypes(['MERCHANT', 'ADMIN', 'OPERATOR', 'DISTRIBUTOR', 'SYSTEM', 'DEMO_ACCOUNT', 'REVIEWER']).then(res => {
        if (res && Array.isArray(res)) {
          const options: { 
            label: string | React.ReactNode; 
            value: string;
            nickName?: string;
          }[] = [];

          // 处理返回的主账户数据
          res.forEach(item => {
            if (item && item.id && item.nickName) {
              options.push({
                label: (
                  <span>
                    {item.nickName} <span style={{ color: '#999' }}>({item.id})</span>
                  </span>
                ),
                value: String(item.id), // 将id转为字符串
                nickName: item.nickName // 添加nickName属性用于搜索
              });
            }
          });

          setMasterOptions(options);
        }
      });
    }
  }, []);

  // 处理用户切换
  const handleUserChange = (value: string | null) => {
    setCurrentUserId(value);
    // 重置日期范围，让组件使用默认值
    setSummaryDateRange(null);
    
    // 子账号视图重置：切换到子账号数据视图，确保子用户选择被清空
    if (activeViewTab === 'subData') {
      // 发送一个自定义事件，通知子组件清空子用户选择
      const resetEvent = new CustomEvent('resetSubUserSelection', { detail: { parentId: value ? Number(value) : userId } });
      window.dispatchEvent(resetEvent);
    }
  };

  // 计算实际使用的用户ID
  const effectiveUserId = currentUserId ? Number(currentUserId) : userId;

  // 处理标签页切换
  const handleViewTabChange = (key: string) => {
    setActiveViewTab(key);

    // 重置日期范围，让组件使用默认值
    setSummaryDateRange(null);
  };

  // 主账户标签页配置
  const masterTabs = [
    {
      key: 'myData',
      label: '我的数据',
      children: (
        <div className="operate-stats-user-operate-stats-container">
          <SummaryChartSection
            summaryActiveTab={summaryActiveTab}
            setSummaryActiveTab={setSummaryActiveTab}
            summaryDateRange={summaryDateRange}
            setSummaryDateRange={setSummaryDateRange}
            setInitialTabChange={setInitialTabChange}
            userId={effectiveUserId}
          />
        </div>
      ),
    },
    {
      key: 'subData',
      label: '子账号数据',
      children: (
        <div className="operate-stats-user-operate-stats-container">
          <Card className="operate-stats-notice-card">
            <Flex align="center" gap={8} style={{ fontSize: '14px' }}>
              <InfoCircleOutlined style={{ color: '#1890ff' }} />
              <div>此页面显示您名下所有子账号的操作数据统计，您可以查看所有子账号的出图量和下载量。</div>
            </Flex>
          </Card>

          <SubAccountSummaryChartSection
            summaryActiveTab={summaryActiveTab}
            setSummaryActiveTab={setSummaryActiveTab}
            summaryDateRange={summaryDateRange}
            setSummaryDateRange={setSummaryDateRange}
            setInitialTabChange={setInitialTabChange}
            userId={effectiveUserId}
            parentId={effectiveUserId}
          />
        </div>
      ),
    }
  ];

  return (
    <PageContainer className="operate-stats-page-container">
      {/* 统计说明卡片 */}
      <Card className="operate-stats-intro-card" style={{ marginBottom: 16 }}>
        <Flex align="center" gap={16}>
          <InfoCircleOutlined style={{ fontSize: 24, color: '#1890ff' }} />
          <div style={{ flex: 1 }}>
            <Flex align="center" justify="space-between">
              <div>
                <div style={{ fontWeight: 'bold', marginBottom: 4 }}>数据统计说明</div>
                <div>本页面展示您的出图量和下载量统计数据，数据并不是实时数据，每日数据更新会在当日 24 点之后进行</div>
              </div>
              {
                userInfo?.roleType === 'ADMIN' &&
                <Select
                  onChange={handleUserChange}
                  style={{ width: 220 }}
                  value={currentUserId}
                  allowClear
                  placeholder="选择用户查看数据"
                  showSearch
                  optionLabelProp="label"
                  filterOption={(input, option) => {
                    const nickName = option?.nickName?.toLowerCase() || '';
                    const userId = option?.userId?.toString() || '';
                    const searchText = input.toLowerCase();
                    return nickName.includes(searchText) || userId.includes(searchText);
                  }}
                >
                  {masterOptions.map(option => (
                    <Select.Option 
                      key={option.value} 
                      value={option.value}
                      label={typeof option.label === 'string' ? option.label : `${option.nickName}(${option.value})`}
                      nickName={option.nickName || ''}
                      userId={option.value}
                    >
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span style={{ 
                          maxWidth: '140px', 
                          overflow: 'hidden', 
                          textOverflow: 'ellipsis', 
                          whiteSpace: 'nowrap' 
                        }}>
                          {option.nickName || (typeof option.label === 'string' ? option.label.split(' ')[0] : '用户')}
                        </span>
                        <span style={{ color: '#999', flexShrink: 0 }}>
                          {option.value}
                        </span>
                      </div>
                    </Select.Option>
                  ))}
                </Select>
              }
            </Flex>
          </div>
        </Flex>
      </Card>

      {/* 如果是主账户，显示标签页切换；如果是子账号，直接显示内容 */}
      {isMasterUser ? (
        <Tabs
          activeKey={activeViewTab}
          onChange={handleViewTabChange}
          className="operate-stats-main-tabs"
          items={masterTabs}
        />
      ) : (
        <div className="operate-stats-user-operate-stats-container">
          <SummaryChartSection
            summaryActiveTab={summaryActiveTab}
            setSummaryActiveTab={setSummaryActiveTab}
            summaryDateRange={summaryDateRange}
            setSummaryDateRange={setSummaryDateRange}
            setInitialTabChange={setInitialTabChange}
            userId={effectiveUserId}
          />
        </div>
      )}
    </PageContainer>
  );
};

export default StatsUserOperate;