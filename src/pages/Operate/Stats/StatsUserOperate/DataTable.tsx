import React from 'react';
import { Table, Empty } from 'antd';
import { StatsUserOperateVO } from '@/services/DataController';
import dayjs from 'dayjs';

// DataTable组件接口
interface DataTableProps {
  data: StatsUserOperateVO[];
  loading: boolean;
  totalRecords?: number;
  sortField: string;
  sortOrder?: 'ascend' | 'descend';
  onTableChange: (pagination: any, filters: any, sorter: any) => void;
  statsType: string;
}

// DataTable组件
const DataTable: React.FC<DataTableProps> = ({
  data,
  loading,
  sortField,
  sortOrder,
  onTableChange,
  statsType
}) => {
  // 格式化日期显示
  const formatStatsDate = (statsDate: string | undefined, type: string) => {
    if (!statsDate) return '-';
    
    try {
      const startDate = dayjs(statsDate);
      
      if (type === 'WEEKLY') {
        // 对于周统计，显示周一到周日的日期范围
        const endDate = startDate.add(6, 'day');
        return `${startDate.format('YYYY-MM-DD')} 至 ${endDate.format('YYYY-MM-DD')}`;
      } else if (type === 'MONTHLY') {
        // 对于月统计，显示月初到月末的日期范围
        const endDate = startDate.endOf('month');
        return `${startDate.format('YYYY-MM-DD')} 至 ${endDate.format('YYYY-MM-DD')}`;
      } else {
        // 日统计和总统计直接显示日期
        return startDate.format('YYYY-MM-DD');
      }
    } catch (e) {
      console.error('日期格式化错误:', e);
      return statsDate || '-';
    }
  };

  // 格式化用户类型显示
  const formatUserType = (userType: string | undefined) => {
    if (!userType) return '-';
    return userType === 'MASTER' ? '主账户' : '子账号';
  };

  return (
    <div className="operate-stats-table-container">
      <Table
        loading={loading}
        dataSource={data}
        rowKey="id"
        pagination={false}
        onChange={onTableChange}
        scroll={{ x: 'max-content', y: 450 }}
        columns={[
          {
            title: '用户ID',
            dataIndex: 'userId',
            key: 'userId',
            width: 100,
          },
          {
            title: '用户类型',
            dataIndex: 'userType',
            key: 'userType',
            width: 100,
            render: (text) => formatUserType(text),
          },
          {
            title: '服装ID',
            dataIndex: 'materialId',
            key: 'materialId',
            width: 100,
          },
          {
            title: '出图量',
            dataIndex: 'createCount',
            key: 'createCount',
            width: 120,
            sorter: true,
            render: (text) => (
              <span className="operate-stats-metric-create">{text || 0}</span>
            ),
          },
          {
            title: '下载量',
            dataIndex: 'downloadCount',
            key: 'downloadCount',
            width: 120,
            sorter: true,
            render: (text) => (
              <span className="operate-stats-metric-download">{text || 0}</span>
            ),
          },
          {
            title: '统计日期',
            dataIndex: 'statsDate',
            key: 'statsDate',
            width: 180,
            render: (text) => formatStatsDate(text, statsType),
          }
        ]}
        locale={{
          emptyText: (
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无数据"
            />
          )
        }}
      />
    </div>
  );
};

export default DataTable; 