import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Tabs, Table, Empty, Tooltip, Spin, Flex, message } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { getUserCreativeData, UserCreativeData } from '@/services/DataController';
import * as echarts from 'echarts';

// 定义全局数据接口
interface GlobalMetricsData {
  // 累计出图量
  totalCreateCount: number;
  // 累计下载量
  totalDownloadCount: number;
  // 累计使用服装数
  materialCount: number;
  // 累计出图任务数
  createTaskCount: number; 
}

// 定义排行榜素材数据接口
interface MaterialRankingItem {
  materialId: number;
  totalCreateCount: number;
  totalDownloadCount: number;
  materialName?: string;
}

// 添加用户排行数据接口
interface UserRankingItem {
  userId: number;
  nickname: string;
  totalCreateCount: number;
  totalDownloadCount: number;
}

// OverviewCard组件接口
interface OverviewCardProps {
  globalMetrics: GlobalMetricsData;
  globalLoading: boolean;
  userId: number;
  isSubAccountsView?: boolean;
  updateGlobalMetrics?: (metrics: Partial<GlobalMetricsData>) => void;
}

// 数据概览卡片组件
const OverviewCard: React.FC<OverviewCardProps> = ({ 
  globalMetrics, 
  globalLoading, 
  userId,
  isSubAccountsView = false,
  updateGlobalMetrics
}) => {
  // 添加内部状态来管理数据
  const [internalMetrics, setInternalMetrics] = useState<GlobalMetricsData>(globalMetrics);
  const [loading, setLoading] = useState<boolean>(false);
  const [materialRanking, setMaterialRanking] = useState<MaterialRankingItem[]>([]);
  const [rankingLoading, setRankingLoading] = useState<boolean>(false);
  const [userRanking, setUserRanking] = useState<UserRankingItem[]>([]);
  const [userRankingLoading, setUserRankingLoading] = useState<boolean>(false);

  // 饼图引用
  const pieChartRef = useRef<HTMLDivElement>(null);
  
  // 图表实例
  const [activeKey, setActiveKey] = useState<string>('table');
  
  // 子账户专用状态
  const [dimensionKey, setDimensionKey] = useState<string>('material');
  const [viewMode, setViewMode] = useState<string>('table');
  const materialPieChartRef = useRef<HTMLDivElement>(null);
  const userPieChartRef = useRef<HTMLDivElement>(null);

  // 添加获取创作数据的函数
  const fetchUserCreativeData = useCallback(async () => {
    setLoading(true);
    setRankingLoading(true);
    setUserRankingLoading(true);
    
    try {
      // 在子账号视图中设置isParent=true
      const response = await getUserCreativeData(userId, isSubAccountsView || false);
      if (response) {
        // 使用类型断言处理返回值
        const data = response as unknown as UserCreativeData;

        const newMetrics = {
          totalCreateCount: data.totalCreateCount || 0,
          totalDownloadCount: data.totalDownloadCount || 0,
          materialCount: data.modelCount || 0,
          createTaskCount: data.createCount || 0
        };

        // 更新内部状态
        setInternalMetrics(prev => ({
          ...prev,
          ...newMetrics
        }));

        // 如果提供了外部更新函数，也更新外部状态
        if (updateGlobalMetrics) {
          updateGlobalMetrics(newMetrics);
        }

        // 更新排行榜数据
        if (data.topMaterials && Array.isArray(data.topMaterials)) {
          setMaterialRanking(data.topMaterials);
        } else {
          console.log('API没有返回排行榜数据，使用默认空数组');
          setMaterialRanking([]);
        }
        
        // 无论是主账号还是子账号，都需要获取用户数据
        // 对于主账号，这些数据用于用户维度的展示
        // 对于子账号，这些数据用于子账号的用户排行
        if (data.userOperateData && Array.isArray(data.userOperateData)) {
          // 从userOperateData中提取数据并格式化为UserRankingItem格式
          const userRankingData = data.userOperateData.map(item => ({
            userId: item.userId || 0,
            nickname: item.nickname || '',
            totalCreateCount: item.totalCreateCount || 0,
            totalDownloadCount: item.totalDownloadCount || 0
          }));
          
          // 按出图量排序
          userRankingData.sort((a, b) => b.totalCreateCount - a.totalCreateCount);
          
          setUserRanking(userRankingData);
        } else {
          console.log('API没有返回用户操作数据，使用默认空数组');
          setUserRanking([]);
        }
      }
    } catch (error) {
      console.error('获取用户创作数据失败:', error);
      setMaterialRanking([]);
      setUserRanking([]);
    } finally {
      setLoading(false);
      setRankingLoading(false);
      setUserRankingLoading(false);
    }
  }, [userId, isSubAccountsView, updateGlobalMetrics]);
  
  // 在首次加载和userId/isSubAccountsView变化时获取创作数据
  useEffect(() => {
    fetchUserCreativeData();
  }, [fetchUserCreativeData]);

  // 当外部globalMetrics变化时，更新内部状态
  useEffect(() => {
    setInternalMetrics(globalMetrics);
  }, [globalMetrics]);

  // 处理Tab切换
  const handleTabChange = (key: string) => {
    setActiveKey(key);
    
    // 如果切换到图表Tab，等DOM渲染完成后初始化图表
    if (key === 'chart') {
      setTimeout(initPieChart, 50);
    }
  };

  // 初始化饼图
  const initPieChart = () => {
    // 确保DOM元素已存在
    if (!pieChartRef.current) return;
    
    try {
      // 创建图表实例
      const chart = echarts.init(pieChartRef.current);
      
      // 准备数据
      const pieData = materialRanking.slice(0, 5).map(item => {
        return {
          name: item.materialName || `素材${item.materialId}`,
          value: item.totalCreateCount
        };
      });
      
      // 配置饼图选项
      const option = {
        backgroundColor: '#fff',
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}张 ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          bottom: 10,
          left: 'center',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '服装使用统计',
            type: 'pie',
            radius: ['35%', '60%'],
            center: ['50%', '45%'],
            avoidLabelOverlap: true,
            itemStyle: {
              borderRadius: 6,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              formatter: '{b}\n{c}张 ({d}%)'
            },
            labelLine: {
              show: true
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.2)'
              },
              label: {
                show: true,
                fontSize: 14,
                fontWeight: 'bold'
              }
            },
            data: pieData.length ? pieData : [{ name: '暂无数据', value: 1 }]
          }
        ],
        color: ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#E8684A', '#6DC8EC']
      };
      
      // 设置图表选项
      chart.setOption(option);
      
      // 监听窗口大小变化
      const resizeHandler = () => chart.resize();
      window.addEventListener('resize', resizeHandler);
      
      // 返回清理函数
      return () => {
        window.removeEventListener('resize', resizeHandler);
        chart.dispose();
      };
    } catch (error) {
      console.error('初始化饼图失败:', error);
    }
  };
  
  // 在数据加载完成后，如果当前是图表Tab，则初始化图表
  useEffect(() => {
    if (activeKey === 'chart' && !rankingLoading && materialRanking.length > 0) {
      setTimeout(initPieChart, 50);
    }
  }, [materialRanking, rankingLoading, activeKey]);

  // 当isSubAccountsView变化时，如果切换回主账号视图且当前是图表页，重新初始化图表
  useEffect(() => {
    if (!isSubAccountsView && activeKey === 'chart') {
      setTimeout(initPieChart, 100);
    }
  }, [isSubAccountsView]);

  // 自定义饼图组件
  const PieChart = () => {
    // 在组件挂载时初始化图表
    useEffect(() => {
      if (!rankingLoading && materialRanking.length > 0) {
        setTimeout(initPieChart, 50);
      }
    }, []);

    // 如果正在加载，显示加载中
    if (rankingLoading) {
      return (
        <div style={{ height: 240, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Spin tip="加载中..."/>
        </div>
      );
    }
    
    // 如果没有数据，显示空状态
    if (materialRanking.length === 0) {
      return (
        <div style={{ height: 240, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Empty description="暂无数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      );
    }
    
    // 渲染图表容器
    return (
      <div 
        ref={pieChartRef} 
        style={{ height: 240, width: '100%' }} 
        id="material-pie-chart"
      />
    );
  };

  // 自定义排行榜组件，确保高度与图表一致
  const RankingTable = () => {
    return (
      <div style={{ height: 240, overflowY: 'auto' }}>
        <Table
          dataSource={materialRanking}
          rowKey="materialId"
          size="small"
          pagination={false}
          loading={rankingLoading}
          scroll={{ y: 198 }} // 设置表格内容区域的滚动高度，留出表头空间
          columns={[
            {
              title: '排名',
              key: 'rank',
              width: 60,
              render: (_, __, index) => {
                return (
                  <span style={{ 
                    fontWeight: 'bold',
                    color: index < 3 ? ['#f5222d', '#fa8c16', '#faad14'][index] : 'inherit'
                  }}>
                    {index + 1}
                  </span>
                );
              }
            },
            {
              title: '服装信息',
              dataIndex: 'materialId',
              key: 'materialId',
              width: 150,
              render: (text, record) => {
                return (
                  <span>
                    {record.materialName ? 
                      <>
                        <span 
                          style={{ 
                            cursor: 'pointer', 
                            color: '#1890ff',
                            textDecoration: 'underline'
                          }}
                          onClick={() => {
                            // 复制服装名称到剪贴板
                            navigator.clipboard.writeText(record.materialName || '')
                              .then(() => {
                                message.success('服装名称已复制到剪贴板');
                              })
                              .catch(err => {
                                console.error('复制失败:', err);
                                message.error('复制失败，请手动复制');
                              });
                          }}
                        >
                          {record.materialName}
                        </span>
                        <span style={{ color: '#8c8c8c' }}>({text})</span>
                      </> : 
                      `（服装名称缺失）${text}`
                    }
                  </span>
                );
              }
            },
            {
              title: '出图量',
              dataIndex: 'totalCreateCount',
              key: 'totalCreateCount',
              width: 80,
              render: (text) => <span style={{ color: '#1890ff', fontWeight: 'bold' }}>{text}张</span>
            },
            {
              title: '下载量',
              dataIndex: 'totalDownloadCount',
              key: 'totalDownloadCount',
              width: 80,
              render: (text) => <span style={{ color: '#52c41a', fontWeight: 'bold' }}>{text}张</span>
            }
          ]}
          locale={{
            emptyText: <Empty description="暂无数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
          }}
        />
      </div>
    );
  };

  // 添加用户排行表格组件
  const UserRankingTable = () => {
    return (
      <div style={{ height: 240, overflowY: 'auto' }}>
        <Table
          dataSource={userRanking}
          rowKey="userId"
          size="small"
          pagination={false}
          loading={userRankingLoading}
          scroll={{ y: 198 }} // 设置表格内容区域的滚动高度，留出表头空间
          columns={[
            {
              title: '排名',
              key: 'rank',
              width: 60,
              render: (_, __, index) => {
                return (
                  <span style={{ 
                    fontWeight: 'bold',
                    color: index < 3 ? ['#f5222d', '#fa8c16', '#faad14'][index] : 'inherit'
                  }}>
                    {index + 1}
                  </span>
                );
              }
            },
            {
              title: '用户昵称',
              dataIndex: 'nickname',
              key: 'nickname',
              width: 150,
              render: (text, record) => {
                return (
                  <span>
                    {text ? (
                      <span>
                        {text} <span style={{ color: '#8c8c8c' }}>({record.userId})</span>
                      </span>
                    ) : (
                      <span>
                        <span style={{ color: '#8c8c8c' }}>用户({record.userId})</span>
                      </span>
                    )}
                  </span>
                );
              }
            },
            {
              title: '出图量',
              dataIndex: 'totalCreateCount',
              key: 'totalCreateCount',
              width: 80,
              render: (text) => <span style={{ color: '#1890ff', fontWeight: 'bold' }}>{text}张</span>
            },
            {
              title: '下载量',
              dataIndex: 'totalDownloadCount',
              key: 'totalDownloadCount',
              width: 80,
              render: (text) => <span style={{ color: '#52c41a', fontWeight: 'bold' }}>{text}张</span>
            }
          ]}
          locale={{
            emptyText: <Empty description="暂无数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
          }}
        />
      </div>
    );
  };

  // 添加子账户所需的处理函数
  const handleDimensionChange = (key: string) => {
    setDimensionKey(key);
  };

  const handleViewModeChange = (key: string) => {
    setViewMode(key);
  };
  
  // 服装饼图组件（子账号专用）
  const MaterialPieChart = () => {
    // 如果正在加载，显示加载中
    if (rankingLoading) {
      return (
        <div style={{ height: 240, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Spin tip="加载中..."/>
        </div>
      );
    }
    
    // 如果没有数据，显示空状态
    if (materialRanking.length === 0) {
      return (
        <div style={{ height: 240, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Empty description="暂无数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      );
    }
    
    // 当组件渲染后初始化图表
    useEffect(() => {
      // 确保DOM元素已存在
      if (!materialPieChartRef.current) return;
      
      try {
        // 创建图表实例
        const chart = echarts.init(materialPieChartRef.current);
        
        // 准备数据
        const pieData = materialRanking.slice(0, 5).map(item => {
          return {
            name: item.materialName || `素材${item.materialId}`,
            value: item.totalCreateCount
          };
        });
        
        // 配置饼图选项
        const option = {
          backgroundColor: '#fff',
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c}张 ({d}%)'
          },
          legend: {
            orient: 'horizontal',
            bottom: 10,
            left: 'center',
            itemWidth: 10,
            itemHeight: 10,
            textStyle: {
              fontSize: 12
            }
          },
          series: [
            {
              name: '服装使用统计',
              type: 'pie',
              radius: ['35%', '60%'],
              center: ['50%', '45%'],
              avoidLabelOverlap: true,
              itemStyle: {
                borderRadius: 6,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: true,
                formatter: '{b}\n{c}张 ({d}%)'
              },
              labelLine: {
                show: true
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.2)'
                },
                label: {
                  show: true,
                  fontSize: 14,
                  fontWeight: 'bold'
                }
              },
              data: pieData.length ? pieData : [{ name: '暂无数据', value: 1 }]
            }
          ],
          color: ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#E8684A', '#6DC8EC']
        };
        
        // 设置图表选项
        chart.setOption(option);
        
        // 监听窗口大小变化
        const resizeHandler = () => chart.resize();
        window.addEventListener('resize', resizeHandler);
        
        // 组件卸载时清理事件监听
        return () => {
          window.removeEventListener('resize', resizeHandler);
          chart.dispose();
        };
      } catch (error) {
        console.error('初始化服装饼图失败:', error);
      }
    }, [materialRanking]);
    
    // 渲染图表容器
    return (
      <div 
        ref={materialPieChartRef} 
        style={{ height: 240, width: '100%' }} 
        id="material-pie-chart-sub"
      />
    );
  };

  // 用户饼图组件（子账号专用）
  const UserPieChart = () => {
    // 如果正在加载，显示加载中
    if (userRankingLoading) {
      return (
        <div style={{ height: 240, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Spin tip="加载中..."/>
        </div>
      );
    }
    
    // 如果没有数据，显示空状态
    if (userRanking.length === 0) {
      return (
        <div style={{ height: 240, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Empty description="暂无数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      );
    }
    
    // 当组件渲染后初始化图表
    useEffect(() => {
      // 确保DOM元素已存在
      if (!userPieChartRef.current) return;
      
      try {
        // 创建图表实例
        const chart = echarts.init(userPieChartRef.current);
        
        // 准备数据
        const pieData = userRanking.slice(0, 5).map(item => {
          return {
            name: item.nickname || `用户${item.userId}`,
            value: item.totalCreateCount
          };
        });
        
        // 配置饼图选项
        const option = {
          backgroundColor: '#fff',
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c}张 ({d}%)'
          },
          legend: {
            orient: 'horizontal',
            bottom: 10,
            left: 'center',
            itemWidth: 10,
            itemHeight: 10,
            textStyle: {
              fontSize: 12
            }
          },
          series: [
            {
              name: '用户出图统计',
              type: 'pie',
              radius: ['35%', '60%'],
              center: ['50%', '45%'],
              avoidLabelOverlap: true,
              itemStyle: {
                borderRadius: 6,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: true,
                formatter: '{b}\n{c}张 ({d}%)'
              },
              labelLine: {
                show: true
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.2)'
                },
                label: {
                  show: true,
                  fontSize: 14,
                  fontWeight: 'bold'
                }
              },
              data: pieData.length ? pieData : [{ name: '暂无数据', value: 1 }]
            }
          ],
          color: ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#E8684A', '#6DC8EC']
        };
        
        // 设置图表选项
        chart.setOption(option);
        
        // 监听窗口大小变化
        const resizeHandler = () => chart.resize();
        window.addEventListener('resize', resizeHandler);
        
        // 组件卸载时清理事件监听
        return () => {
          window.removeEventListener('resize', resizeHandler);
          chart.dispose();
        };
      } catch (error) {
        console.error('初始化用户饼图失败:', error);
      }
    }, [userRanking]);
    
    // 渲染图表容器
    return (
      <div 
        ref={userPieChartRef} 
        style={{ height: 240, width: '100%' }} 
        id="user-pie-chart-sub"
      />
    );
  };

  // 使用内部状态或外部状态，优先使用内部状态
  const displayMetrics = internalMetrics;
  const isLoading = loading || globalLoading;
  
  return (
    <Card title="数据概览" className="operate-stats-overview-card" style={{ marginBottom: 16 }}>
      <Flex gap={16} wrap="wrap">
        <Flex style={{ flex: 1, minWidth: 250 }} vertical>
          <Flex align="center" style={{ marginBottom: 8 }}>
            <span style={{ fontWeight: 'bold' }}>核心指标</span>
            <span style={{ fontSize: '12px', color: '#8c8c8c', marginLeft: 8 }}>(全部数据)</span>
          </Flex>
          
          {/* 更加紧凑的指标布局 */}
          <Flex style={{ background: '#f9f9f9', padding: '12px', borderRadius: '4px', marginBottom: 8 }}>
            <Flex vertical style={{ flex: 1 }}>
              <Flex align="center" style={{ marginBottom: 4 }}>
                <div style={{ width: 8, height: 8, borderRadius: '50%', background: '#1890ff', marginRight: 8 }}></div>
                <div style={{ fontSize: '13px', color: '#8c8c8c' }}>总出图量</div>
              </Flex>
              <Flex align="baseline">
                <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#1890ff', marginRight: 8 }}>
                  {isLoading ? <Spin size="small" /> : displayMetrics.totalCreateCount}
                </div>
              </Flex>
            </Flex>
            
            <div style={{ width: 1, background: '#e8e8e8', margin: '0 12px' }}></div>
            
            <Flex vertical style={{ flex: 1 }}>
              <Flex align="center" style={{ marginBottom: 4 }}>
                <div style={{ width: 8, height: 8, borderRadius: '50%', background: '#52c41a', marginRight: 8 }}></div>
                <div style={{ fontSize: '13px', color: '#8c8c8c' }}>总下载量</div>
              </Flex>
              <Flex align="baseline">
                <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#52c41a', marginRight: 8 }}>
                  {isLoading ? <Spin size="small" /> : displayMetrics.totalDownloadCount}
                </div>
              </Flex>
            </Flex>
          </Flex>
         
          
          {/* 新增：使用活跃数据 */}
          <Flex vertical style={{ background: '#f9f9f9', padding: '12px', borderRadius: '4px' }}>
            <Flex align="center" style={{ marginBottom: 8 }}>
              <div style={{ width: 8, height: 8, borderRadius: '50%', background: '#fa8c16', marginRight: 8 }}></div>
              <div style={{ fontSize: '13px', color: '#8c8c8c', marginRight: 'auto' }}>数据概览</div>
              <Tooltip title="用户数据概览情况统计">
                <QuestionCircleOutlined style={{ color: '#bfbfbf', fontSize: '12px' }} />
              </Tooltip>
            </Flex>
            
            <Flex justify="space-between" style={{ marginBottom: 6 }}>
              <div style={{ fontSize: '13px', color: '#8c8c8c' }}>累计使用服装数</div>
              <div style={{ fontWeight: 'bold' }}>
                {isLoading ? <Spin size="small" /> : displayMetrics.materialCount} 套
              </div>
            </Flex>
            
            <Flex justify="space-between">
              <div style={{ fontSize: '13px', color: '#8c8c8c' }}>累计出图次数</div>
              <div style={{ fontWeight: 'bold' }}>
                {isLoading ? <Spin size="small" /> : displayMetrics.createTaskCount || 0} 次
              </div>
            </Flex>
          </Flex>

        </Flex>
        
        {/* 数据排行卡片 */}
        <Card 
          title={isSubAccountsView ? "数据排行" : "服装使用排行"} 
          style={{ flex: 3, minWidth: 300 }} 
          size="small" 
          bordered={true}
        >
          {isSubAccountsView ? (
            // 子账号视图：优化后的UI，带有服装/用户两个维度
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              {/* 主维度选项卡 */}
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                paddingBottom: '16px',
                borderBottom: '1px solid #f0f0f0',
                marginBottom: '16px'
              }}>
                <div style={{ display: 'flex' }}>
                  <div 
                    style={{ 
                      padding: '6px 16px', 
                      cursor: 'pointer',
                      fontSize: '14px',
                      color: dimensionKey === 'material' ? '#1890ff' : '#595959',
                      fontWeight: dimensionKey === 'material' ? 500 : 'normal',
                      position: 'relative',
                      transition: 'all 0.3s'
                    }}
                    onClick={() => handleDimensionChange('material')}
                  >
                    服装维度
                    {dimensionKey === 'material' && (
                      <div style={{
                        position: 'absolute',
                        left: '16px',
                        right: '16px',
                        bottom: '-2px',
                        height: '2px',
                        background: '#1890ff',
                        borderRadius: '2px 2px 0 0'
                      }} />
                    )}
                  </div>
                  <div 
                    style={{ 
                      padding: '6px 16px', 
                      cursor: 'pointer',
                      fontSize: '14px',
                      color: dimensionKey === 'user' ? '#1890ff' : '#595959',
                      fontWeight: dimensionKey === 'user' ? 500 : 'normal',
                      position: 'relative',
                      transition: 'all 0.3s'
                    }}
                    onClick={() => handleDimensionChange('user')}
                  >
                    用户维度
                    {dimensionKey === 'user' && (
                      <div style={{
                        position: 'absolute',
                        left: '16px',
                        right: '16px',
                        bottom: '-2px',
                        height: '2px',
                        background: '#1890ff',
                        borderRadius: '2px 2px 0 0'
                      }} />
                    )}
                  </div>
                </div>
                
                {/* 视图模式切换 */}
                <div style={{ 
                  display: 'flex', 
                  background: '#f5f5f5', 
                  borderRadius: '4px',
                  padding: '2px'
                }}>
                  <div 
                    style={{ 
                      padding: '4px 12px',
                      fontSize: '12px',
                      cursor: 'pointer',
                      borderRadius: '3px',
                      transition: 'all 0.3s',
                      background: viewMode === 'table' ? '#fff' : 'transparent',
                      color: viewMode === 'table' ? '#1890ff' : 'inherit',
                      boxShadow: viewMode === 'table' ? '0 1px 2px rgba(0, 0, 0, 0.1)' : 'none'
                    }}
                    onClick={() => handleViewModeChange('table')}
                  >
                    表格
                  </div>
                  <div 
                    style={{ 
                      padding: '4px 12px',
                      fontSize: '12px',
                      cursor: 'pointer',
                      borderRadius: '3px',
                      transition: 'all 0.3s',
                      background: viewMode === 'chart' ? '#fff' : 'transparent',
                      color: viewMode === 'chart' ? '#1890ff' : 'inherit',
                      boxShadow: viewMode === 'chart' ? '0 1px 2px rgba(0, 0, 0, 0.1)' : 'none'
                    }}
                    onClick={() => handleViewModeChange('chart')}
                  >
                    图表
                  </div>
                </div>
              </div>
              
              {/* 内容区域 */}
              <div style={{ flex: 1, minHeight: '240px' }}>
                {dimensionKey === 'material' ? 
                  (viewMode === 'table' ? <RankingTable /> : <MaterialPieChart />) : 
                  (viewMode === 'table' ? <UserRankingTable /> : <UserPieChart />)
                }
              </div>
            </div>
          ) : (
            // 主账号视图：恢复为最初的样式，但删除用户出图信息选项卡
            <Tabs 
              activeKey={activeKey}
              onChange={handleTabChange}
              items={[
                {
                  key: 'table',
                  label: '排行榜',
                  children: <RankingTable />
                },
                {
                  key: 'chart',
                  label: '图表展示',
                  children: <PieChart />
                }
              ]}
            />
          )}
        </Card>
      </Flex>
    </Card>
  );
};

export default OverviewCard; 