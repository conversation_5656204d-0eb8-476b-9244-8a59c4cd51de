.operate-stats-page-container {
  background-color: #f0f2f5;
}

.operate-stats-page-container .ant-pro-page-container-children-content {
  margin: 0;
}

.operate-stats-user-operate-stats-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin: 0;
}

.operate-stats-stats-summary-section {
  margin-bottom: 16px;
}

.operate-stats-stats-detail-section {
  margin-bottom: 16px;
}

.operate-stats-filter-card {
  margin-bottom: 16px;
}

.operate-stats-stats-summary-content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  gap: 16px;
  position: relative;
  height: 100%;
}

.operate-stats-metrics-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.operate-stats-metric-card {
  flex: 1 1 calc(20% - 16px);
  min-width: 200px;
}

.operate-stats-metric-card-first {
  flex: 1 1 calc(20% - 16px);
  min-width: 200px;
}

.operate-stats-chart-controls {
  margin-bottom: 16px;
}

.operate-stats-upper-tabs .ant-tabs-nav-wrap {
  flex: 0 0 auto;
}

.operate-stats-chart-container {
  flex-grow: 1;
  min-height: 300px;
  height: 400px;
  margin-bottom: 16px;
}

/* Metric card colors */
.operate-stats-metric-create {
  color: #1890ff;
}

.operate-stats-metric-download {
  color: #52c41a;
}

.operate-stats-user-detail-tabs .ant-tabs-nav {
  margin-bottom: 16px;
}

.operate-stats-notice-card {
  margin-bottom: 16px;
  background-color: #f6f6f6;
  border-left: 4px solid #1890ff;
}

.operate-stats-main-tabs {
  margin-bottom: 16px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.operate-stats-main-tabs .ant-tabs-nav {
  padding-left: 16px;
  margin-left: 4px;
  margin-bottom: 0;
  padding-top: 8px;
}

.operate-stats-main-tabs .ant-tabs-tab {
  padding-left: 20px;
  padding-right: 20px;
  margin-right: 12px;
  font-size: 15px;
  height: 48px;
}

.operate-stats-main-tabs .ant-tabs-tab:hover {
  color: #1890ff;
}

.operate-stats-main-tabs .ant-tabs-tab-active {
  font-weight: 500;
}

.operate-stats-main-tabs .ant-tabs-ink-bar {
  height: 3px;
  border-radius: 2px 2px 0 0;
}

.operate-stats-main-tabs .ant-tabs-content {
  height: 100%;
  padding: 16px;
}

.operate-stats-chart-card, 
.operate-stats-detail-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.ant-pagination {
  margin: 16px 0;
  text-align: right;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .operate-stats-metrics-row {
    flex-direction: column;
  }
  
  .operate-stats-metric-card,
  .operate-stats-metric-card-first {
    width: 100%;
  }
  
  .operate-stats-chart-container {
    height: 300px;
  }
}
