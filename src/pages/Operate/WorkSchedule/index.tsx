import { <PERSON><PERSON>ontainer } from '@ant-design/pro-layout';
import {
  <PERSON>ge,
  Button,
  Calendar,
  Card,
  Checkbox,
  Col,
  DatePicker,
  Flex,
  Form,
  message,
  Modal,
  Popconfirm,
  Row,
  Select,
  Space,
  TimePicker,
  Tooltip,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { DeleteOutlined, EditOutlined, PlusOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import localeData from 'dayjs/plugin/localeData';
import 'dayjs/locale/zh-cn';
import {
  batchCreateWorkSchedule,
  batchDeleteWorkSchedule,
  deleteWorkSchedule,
  queryWorkScheduleList,
  updateWorkSchedule, WorkScheduleQuery,
  WorkScheduleVO,
} from '@/services/WorkScheduleController';
import { queryAllMaster, UserVO } from '@/services/UserController';
import { USER_INFO } from '@/constants';

// Extend dayjs with plugins
dayjs.extend(isSameOrBefore);
dayjs.extend(localeData);
// Set locale to Chinese
dayjs.locale('zh-cn');

export default function WorkSchedule() {
  const userInfo = localStorage.getItem(USER_INFO);
  const isAdmin = userInfo ? JSON.parse(userInfo).roleType === 'ADMIN' : false;
  // States
  const [loading, setLoading] = useState<boolean>(false);
  const [scheduleList, setScheduleList] = useState<WorkScheduleVO[]>([]);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalTitle, setModalTitle] = useState<string>('');
  const [editingRecord, setEditingRecord] = useState<WorkScheduleVO | null>(null);
  const [users, setUsers] = useState<UserVO[]>([]);
  const [searchUserId, setSearchUserId] = useState<number | undefined>(undefined);
  const [searchDateRange, setSearchDateRange] = useState<[any, any] | null>(null);
  const [calendarViewDate, setCalendarViewDate] = useState(dayjs());
  const [selectedSchedules, setSelectedSchedules] = useState<number[]>([]);
  const [deleteModalVisible, setDeleteModalVisible] = useState<boolean>(false);
  const [cellModalVisible, setCellModalVisible] = useState<boolean>(false);
  const [currentCellDate, setCurrentCellDate] = useState<string>('');
  const [currentCellSchedules, setCurrentCellSchedules] = useState<WorkScheduleVO[]>([]);
  const [editMode, setEditMode] = useState<boolean>(false);

  // Form reference
  const [form] = Form.useForm();

  // Helper function to render user name with role label
  const renderUserWithRole = (user?: UserVO) => {
    if (!user) return '';

    return (
      <>
        {user.nickName}
      </>
    );
  };

  // Helper function to create user options with role labels
  const getUserOptions = (userList: UserVO[]) => {
    return userList.map(user => ({
      label: (
        <>
          {user.nickName}
        </>
      ),
      value: user.id,
    }));
  };

  // Initial data loading
  useEffect(() => {
    fetchUsers();

    // Use the first and last visible dates on the calendar panel as query range
    const [firstVisibleDate, lastVisibleDate] = getVisibleDateRange(calendarViewDate);
    setSearchDateRange([firstVisibleDate, lastVisibleDate]);
    setSelectedDate(dayjs());
  }, []);

  // Effect to fetch data when search filters change
  useEffect(() => {
    fetchScheduleList();
  }, [searchUserId, searchDateRange]);

  // Fetch user options for select dropdown
  const fetchUsers = async () => {
    try {
      const users = await queryAllMaster(['ADMIN']);
      if (users && Array.isArray(users)) {
        setUsers(users);
      }
    } catch (error) {
      console.error('Failed to fetch users:', error);
      message.error('获取用户列表失败');
    }
  };

  // Fetch schedule list with filters (no pagination)
  const fetchScheduleList = async () => {
    setLoading(true);
    try {
      const params: Partial<WorkScheduleQuery> = {};

      // Add filters if they exist
      if (searchUserId) {
        params.userId = searchUserId;
      }

      if (searchDateRange && searchDateRange[0] && searchDateRange[1]) {
        params.startTime = searchDateRange[0].format('YYYY-MM-DD 00:00:00');
        params.endTime = searchDateRange[1].format('YYYY-MM-DD 23:59:59');
      }

      const response = await queryWorkScheduleList(params);

      if (response && Array.isArray(response)) {
        setScheduleList(response);
      }
    } catch (error) {
      console.error('Failed to fetch schedule list:', error);
      message.error('获取排班列表失败');
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = () => {
    // Directly fetch data with current filters
    fetchScheduleList();
  };

  // Handle reset search
  const handleResetSearch = () => {
    // Clear search filters
    setSearchUserId(undefined);
  };

  // Open modal for adding new schedule
  const handleAdd = () => {
    setModalTitle('添加排班');
    setEditingRecord(null);
    setTimeRange(null);
    
    // Set today as the default date
    const today = dayjs();
    setSelectedDates([today]);
    
    setModalVisible(true);
    // Reset form fields after modal is visible
    setTimeout(() => {
      form.resetFields();
      // Set default time slot to 9-20
      setFullDayShift();
      // Set default date range to today
      form.setFieldsValue({
        dates: [today, today]
      });
    }, 0);
  };

  // Open modal for editing schedule
  const handleEdit = (record: WorkScheduleVO) => {
    setModalTitle('编辑排班');
    setEditingRecord(record);

    // Extract date and time parts
    const startMoment = dayjs(record.startTime);
    const endMoment = dayjs(record.endTime);

    // Get the date (just need one since they're the same day in edit mode)
    const date = startMoment.startOf('day');

    // Get time parts only
    const startTimeOnly = dayjs(startMoment.format('HH:mm:ss'), 'HH:mm:ss');
    const endTimeOnly = dayjs(endMoment.format('HH:mm:ss'), 'HH:mm:ss');

    // Set state variables
    setSelectedDates([date]);
    setTimeSlot([startTimeOnly, endTimeOnly]);

    // First make the modal visible
    setModalVisible(true);

    // Then set form values after the form is mounted
    setTimeout(() => {
      form.setFieldsValue({
        userIds: [record.userId],
        dates: [date, date],
        timeSlot: [startTimeOnly, endTimeOnly],
      });
    }, 0);
  };

  // Handle delete schedule
  const handleDelete = async (id: number) => {
    setLoading(true);
    try {
      const result = await deleteWorkSchedule(id);
      if (result) {
        message.success('删除成功');
        fetchScheduleList();
      } else {
        message.error('删除失败，请重试');
      }
    } catch (error) {
      console.error('Failed to delete schedule:', error);
      message.error('删除失败');
    } finally {
      setLoading(false);
    }
  };

  // Handle batch delete schedules
  const handleBatchDelete = async () => {
    if (selectedSchedules.length === 0) {
      message.warning('请先选择要删除的排班');
      return;
    }

    if (!editMode) {
      message.warning('请先进入编辑模式');
      return;
    }

    setDeleteModalVisible(true);
  };

  // Confirm batch delete
  const confirmBatchDelete = async () => {
    try {
      setLoading(true);
      const result = await batchDeleteWorkSchedule(selectedSchedules);
      if (result) {
        message.success(`成功删除 ${selectedSchedules.length} 条排班记录`);
        setSelectedSchedules([]);
        fetchScheduleList();
      } else {
        message.error('批量删除失败，请重试');
      }
    } catch (error) {
      console.error('Failed to batch delete schedules:', error);
      message.error('批量删除失败');
    } finally {
      setLoading(false);
      setDeleteModalVisible(false);
    }
  };

  // Toggle schedule selection
  const toggleScheduleSelection = (id: number) => {
    if (!editMode) {
      message.warning('请先进入编辑模式');
      return;
    }

    setSelectedSchedules(prev => {
      if (prev.includes(id)) {
        return prev.filter(scheduleId => scheduleId !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  // Select all schedules for a date
  const selectAllSchedulesForDate = (schedules: WorkScheduleVO[], selected: boolean) => {
    const scheduleIds = schedules.map(schedule => schedule.id);

    if (selected) {
      // Add all schedules that aren't already selected
      setSelectedSchedules(prev => {
        const newSelection = [...prev];
        scheduleIds.forEach(id => {
          if (!newSelection.includes(id)) {
            newSelection.push(id);
          }
        });
        return newSelection;
      });
    } else {
      // Remove all schedules for this date
      setSelectedSchedules(prev => prev.filter(id => !scheduleIds.includes(id)));
    }
  };

  // State for time range to ensure the UI updates
  const [timeRange, setTimeRange] = useState<[any, any] | null>(null);
  const [selectedDates, setSelectedDates] = useState<any[]>([]);
  const [timeSlot, setTimeSlot] = useState<[any, any] | null>(null);

  // Set full day shift (9 AM to 8 PM)
  const setFullDayShift = () => {
    try {
      // Create time objects without dates
      const startTime = dayjs('09:00:00', 'HH:mm:ss');
      const endTime = dayjs('20:00:00', 'HH:mm:ss');

      // Update both the form and our state variable
      const newTimeSlot: [any, any] = [startTime, endTime];
      setTimeSlot(newTimeSlot);

      form.setFieldsValue({
        timeSlot: newTimeSlot,
      });
    } catch (error) {
      console.error('Error setting full day shift:', error);
    }
  };

  // Handle modal submit
  const handleModalSubmit = async () => {
    try {
      // Validate form fields
      const values = await form.validateFields();
      const selectedDates = values.dates;
      const [startTimeObj, endTimeObj] = values.timeSlot;

      // Get the date range from form
      const dateRange = values.dates;
      if (!dateRange || !dateRange[0] || !dateRange[1]) {
        message.error('请选择日期范围');
        return;
      }

      // Always generate array of dates between start and end
      const startDate = dayjs(dateRange[0]);
      const endDate = dayjs(dateRange[1]);
      const dateArray: dayjs.Dayjs[] = [];

      let currentDate = dayjs(startDate);
      while (currentDate.isSameOrBefore(endDate, 'day')) {
        dateArray.push(dayjs(currentDate));
        currentDate = currentDate.add(1, 'day');
      }

      // Use the generated date array instead of relying on selectedDates state
      const datesForSchedule = dateArray;

      if (!startTimeObj || !endTimeObj) {
        message.error('请选择有效的时间范围');
        return;
      }

      // Validate time range
      if (startTimeObj.isAfter(endTimeObj)) {
        message.error('开始时间不能晚于结束时间');
        return;
      }

      // Get time parts only
      const startTimeStr = startTimeObj.format('HH:mm:ss');
      const endTimeStr = endTimeObj.format('HH:mm:ss');

      setLoading(true);

      try {
        if (editingRecord) {
          // Update existing record - only supports single date in edit mode
          const dateStr = selectedDates[0].format('YYYY-MM-DD');
          const formattedStartTime = `${dateStr} ${startTimeStr}`;
          const formattedEndTime = `${dateStr} ${endTimeStr}`;

          const result = await updateWorkSchedule({
            id: editingRecord.id,
            userId: values.userIds[0],
            startTime: formattedStartTime,
            endTime: formattedEndTime,
          });

          if (result) {
            message.success('更新成功');
            setModalVisible(false);
            fetchScheduleList();
          } else {
            message.error('更新失败，请重试');
          }
        } else {
          // Create new records - supports multiple dates
          if (values.userIds && values.userIds.length > 0 && datesForSchedule.length > 0) {
            // Create an array of schedule objects for batch creation
            const schedules: Partial<WorkScheduleVO>[] = [];

            // For each date in the range and user, create a schedule
            for (const date of datesForSchedule) {
              const dateStr = date.format('YYYY-MM-DD');

              for (const userId of values.userIds) {
                schedules.push({
                  userId: userId,
                  startTime: `${dateStr} ${startTimeStr}`,
                  endTime: `${dateStr} ${endTimeStr}`,
                });
              }
            }

            // Always use batch creation API for consistency
            if (schedules.length > 0) {
              const result = await batchCreateWorkSchedule(schedules);

              if (result) {
                message.success(`成功创建 ${schedules.length} 条排班记录`);
                setModalVisible(false);
                fetchScheduleList();
              } else {
                message.error('创建失败，请重试');
              }
            }
          } else {
            message.error('请选择至少一个人员和一个日期');
          }
        }
      } catch (apiError) {
        console.error('API request failed:', apiError);
        message.error(editingRecord ? '更新失败' : '创建失败');
      } finally {
        setLoading(false);
      }
    } catch (formError) {
      console.error('Form validation failed:', formError);
    }
  };

  // State for selected date in calendar
  const [selectedDate, setSelectedDate] = useState(dayjs());
  // Function to group schedules by date for calendar view
  const getSchedulesByDate = () => {
    const schedulesByDate: { [key: string]: WorkScheduleVO[] } = {};

    scheduleList.forEach(schedule => {
      const dateStr = dayjs(schedule.startTime).format('YYYY-MM-DD');
      if (!schedulesByDate[dateStr]) {
        schedulesByDate[dateStr] = [];
      }
      schedulesByDate[dateStr].push(schedule);
    });

    return schedulesByDate;
  };

  // Handle calendar date selection without changing month/year view
  const onCalendarSelect = (date) => {
    // Only update the selected date without changing the view
    setSelectedDate(date);
    // Return false to prevent default behavior
    return false;
  };

  // Get the first and last visible dates on the calendar panel
  const getVisibleDateRange = (date) => {
    const firstDayOfMonth = date.startOf('month');
    const lastDayOfMonth = date.endOf('month');

    // Get the day of week for the first day (0 is Sunday, 1 is Monday, etc.)
    const firstDayWeekday = firstDayOfMonth.day();

    // Calculate the first visible date on the calendar
    // If month starts on Monday (1), that's the first visible date
    // Otherwise, go back to the previous Monday
    const firstVisibleDate = firstDayWeekday === 1
      ? firstDayOfMonth
      : firstDayOfMonth.subtract(firstDayWeekday - 1, 'day');

    // Get the day of week for the last day
    const lastDayWeekday = lastDayOfMonth.day();

    // Calculate the last visible date on the calendar
    // If month ends on Sunday (0), that's the last visible date
    // Otherwise, go forward to the next Sunday
    const lastVisibleDate = lastDayWeekday === 0
      ? lastDayOfMonth
      : lastDayOfMonth.add(7 - lastDayWeekday, 'day');

    return [firstVisibleDate, lastVisibleDate];
  };

  // Handle panel change (month/year selection)
  const onPanelChange = (date: dayjs.Dayjs, mode: string) => {
    // Update calendar view date
    setCalendarViewDate(date);

    // Set search date range to match the calendar's visible dates
    const [firstVisibleDate, lastVisibleDate] = getVisibleDateRange(date);
    setSearchDateRange([firstVisibleDate, lastVisibleDate]);
  };


  // Handle adding schedule for selected date
  const handleAddForDate = (date: dayjs.Dayjs) => {
    const selectedDate = date.clone();
    // Set default time range for the selected date (9:00 - 15:00)
    const startTime = selectedDate.hour(9).minute(0).second(0);
    const endTime = selectedDate.hour(15).minute(0).second(0);

    // Open modal with pre-filled date
    setModalVisible(true);
    form.setFieldsValue({
      userId: undefined,
      dateRange: [selectedDate, selectedDate],
      timeRange: [dayjs(startTime.format()), dayjs(endTime.format())],
    });
  };

  // Calendar cell renderer
  const dateCellRender = (value: dayjs.Dayjs) => {
    const dateStr = value.format('YYYY-MM-DD');
    const schedulesByDate = getSchedulesByDate();
    const daySchedules = schedulesByDate[dateStr] || [];

    // Check if date is before today for strikethrough styling
    const isPastDate = value.isBefore(dayjs(), 'day');

    // Group schedules by time slot
    const schedulesByTimeSlot = {};

    daySchedules.forEach(schedule => {
      const startTime = dayjs(schedule.startTime).format('HH:mm');
      const endTime = dayjs(schedule.endTime).format('HH:mm');
      const timeSlotKey = `${startTime}-${endTime}`;

      if (!schedulesByTimeSlot[timeSlotKey]) {
        schedulesByTimeSlot[timeSlotKey] = [];
      }

      schedulesByTimeSlot[timeSlotKey].push(schedule);
    });

    // Get sorted time slots
    const timeSlots = Object.keys(schedulesByTimeSlot).sort();

    return (
      <div
        style={{
          height: '100%',
          padding: '2px',
          cursor: editMode ? 'default' : 'pointer',
        }}
        onClick={(e) => {
          e.stopPropagation();
          // Only open modal if not in edit mode
          if (!editMode) {
            setCurrentCellDate(dateStr);
            setCurrentCellSchedules(daySchedules);
            setCellModalVisible(true);
          }
        }}
      >
        <ul className="events" style={{ listStyle: 'none', padding: 0, margin: 0 }}>
          {timeSlots.map((timeSlot, timeSlotIndex) => {
            const schedules = schedulesByTimeSlot[timeSlot];
            const [startTime, endTime] = timeSlot.split('-');

            return (
              <React.Fragment key={timeSlot}>
                {/* Add divider between time slots */}
                {timeSlotIndex > 0 && (
                  <div style={{
                    borderTop: '1px solid #f0f0f0',
                    margin: '4px 0',
                    width: '100%',
                  }} />
                )}

                {/* Select all checkbox for this time slot */}
                {isAdmin && editMode && !isPastDate && (
                  <div style={{
                    display: 'flex',
                    justifyContent: 'start',
                    alignItems: 'center',
                    marginBottom: '2px',
                  }}>
                    <Checkbox
                      checked={schedules.every(s => selectedSchedules.includes(s.id))}
                      onChange={(e) => selectAllSchedulesForDate(schedules, e.target.checked)}
                      style={{ marginRight: '4px' }}
                    />
                    <span style={{ fontSize: '12px', fontWeight: 'bold' }}>全选</span>
                  </div>
                )}

                {/* Users in this time slot */}
                {schedules.map(schedule => (
                  <li key={schedule.id} style={{ marginBottom: '2px', display: 'flex', alignItems: 'center' }}>
                    {isAdmin && editMode && !isPastDate && (
                      <Checkbox
                        checked={selectedSchedules.includes(schedule.id)}
                        onChange={() => toggleScheduleSelection(schedule.id)}
                        style={{ marginRight: '4px' }}
                      />
                    )}
                    <Tooltip
                      title={
                        <div>
                          {users.find(user => user.id === schedule.userId)?.nickName}: {startTime}-{endTime}
                        </div>
                      }
                    >
                      <Badge
                        status={isPastDate ? 'default' : 'success'}
                        text={
                          <span
                            style={{
                              fontSize: '12px',
                              fontWeight: isPastDate ? 'inherit' : 'bold',
                              textDecoration: isPastDate ? 'line-through' : 'none',
                              color: isPastDate ? '#aaaaaa' : 'inherit',
                            }}
                          >
                            {renderUserWithRole(users.find(user => user.id === schedule.userId))}
                          </span>
                        }
                      />
                    </Tooltip>
                  </li>
                ))}
              </React.Fragment>
            );
          })}
        </ul>
      </div>
    );
  };

  return (
    <PageContainer>
      <Card>
        {/* Search Form */}
        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 12 }}>
            <Space wrap>
              <Select
                placeholder="选择审核员"
                style={{ width: 200 }}
                allowClear
                options={getUserOptions(users)}
                value={searchUserId}
                onChange={value => setSearchUserId(value)}
              />
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleSearch}
              >
                搜索
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleResetSearch}
              >
                重置
              </Button>
              {isAdmin && (
                <>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleAdd}
                  >
                    添加排班
                  </Button>
                  <Button
                    type={editMode ? 'primary' : 'default'}
                    icon={<EditOutlined />}
                    onClick={() => {
                      setEditMode(!editMode);
                      if (!editMode) {
                        // Clear selections when entering edit mode
                        setSelectedSchedules([]);
                      }
                    }}
                    style={{ marginLeft: '8px' }}
                  >
                    {editMode ? '完成编辑' : '编辑模式'}
                  </Button>
                  {editMode && selectedSchedules.length > 0 && (
                    <Button
                      danger
                      icon={<DeleteOutlined />}
                      onClick={handleBatchDelete}
                      style={{ marginLeft: '8px' }}
                    >
                      批量删除 ({selectedSchedules.length})
                    </Button>
                  )}
                </>
              )}
            </Space>
          </div>
        </div>

        {/* Calendar View */}
        <div style={{ position: 'relative' }}>
          {loading && (
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'rgba(255, 255, 255, 0.6)',
              zIndex: 1,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              fontSize: '16px',
            }}>
              加载中...
            </div>
          )}
          <Calendar
            cellRender={(current, info) => {
              if (info.type === 'date') {
                return dateCellRender(current);
              }
              return info.originNode;
            }}
            value={selectedDate}
            onSelect={onCalendarSelect}
            style={{ background: '#fff' }}
            defaultValue={dayjs()} // Default to today
            onPanelChange={onPanelChange}
            validRange={[dayjs('2000-01-01'), dayjs('2100-12-31')]}
            headerRender={({ value, type, onChange, onTypeChange }) => {
              const start = 0;
              const end = 12;
              const monthOptions: React.ReactNode[] = [];

              const localeData = value.locale('zh-cn').localeData();
              const months: dayjs.MonthNames[] = [];

              // Use separate date objects for each month to avoid mutation issues
              for (let i = 0; i < 12; i++) {
                const monthDate = value.clone().month(i);
                months.push(localeData.monthsShort(monthDate));
              }

              for (let i = start; i < end; i++) {
                monthOptions.push(
                  <Select.Option key={i} value={i} className="month-item">
                    {months[i]}
                  </Select.Option>,
                );
              }

              const year = value.year();
              const month = value.month();
              const options: React.ReactNode[] = [];
              for (let i = year - 10; i < year + 10; i += 1) {
                options.push(
                  <Select.Option key={i} value={i} className="year-item">
                    {i}
                  </Select.Option>,
                );
              }

              return (
                <div style={{ padding: '8px 0' }}>
                  <Row gutter={8} justify="start" align="middle">
                    <Col>
                      <Select
                        size="small"
                        popupMatchSelectWidth={false}
                        className="my-year-select"
                        value={year}
                        onChange={newYear => {
                          const now = value.clone().year(newYear);
                          onChange(now);
                          setCalendarViewDate(now.clone());
                        }}
                      >
                        {options}
                      </Select>
                    </Col>
                    <Col>
                      <Select
                        size="small"
                        popupMatchSelectWidth={false}
                        value={month}
                        onChange={newMonth => {
                          const now = value.clone().month(newMonth);
                          onChange(now);
                          setCalendarViewDate(now.clone());
                        }}
                      >
                        {monthOptions}
                      </Select>
                    </Col>
                    <Col>
                      <Space>
                        <Button
                          size="small"
                          onClick={() => {
                            // Only update to today's date without changing year/month view if already in current year
                            const today = dayjs();
                            setSelectedDate(today);
                            // No need to set calendar view separately
                            onChange(today);
                          }}
                        >
                          今天
                        </Button>
                      </Space>
                    </Col>
                  </Row>
                </div>
              );
            }}
          />
        </div>
      </Card>

      {/* Add/Edit Modal */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleModalSubmit}
        onCancel={() => {
          setModalVisible(false);
          // Reset form when closing modal
          setTimeout(() => {
            form.resetFields();
          }, 100);
        }}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="userIds"
            label="人员"
            rules={[{ required: true, message: '请选择审核员' }]}
          >
            <Select
              placeholder="选择审核员"
              mode="multiple"
              allowClear
              optionFilterProp="label"
              options={getUserOptions(users)}
              showSearch
              style={{ width: '100%' }}
            />
          </Form.Item>
          <Form.Item
            name="dates"
            label="日期"
            rules={[{ required: true, message: '请选择日期' }]}
          >
            <DatePicker.RangePicker
              format="YYYY-MM-DD"
              placeholder={['开始日期', '结束日期']}
              style={{ width: '100%', marginBottom: '8px' }}
              onChange={(dates) => {
                if (!dates) {
                  setSelectedDates([]);
                  return;
                }

                // Generate array of dates between start and end
                const startDate = dayjs(dates[0]);
                const endDate = dayjs(dates[1]);
                const dateArray: dayjs.Dayjs[] = [];

                let currentDate = dayjs(startDate);
                while (currentDate.isSameOrBefore(endDate, 'day')) {
                  dateArray.push(dayjs(currentDate));
                  currentDate = currentDate.add(1, 'day');
                }

                setSelectedDates(dateArray);
                // Don't update the form field - keep the original range
              }}
            />
          </Form.Item>

          <Form.Item
            name="timeSlot"
            label="时间段"
            rules={[{ required: true, message: '请选择时间段' }]}
          >
            <div>
              <TimePicker.RangePicker
                format="HH:mm"
                placeholder={['开始时间', '结束时间']}
                style={{ width: '100%', marginBottom: '8px' }}
                value={timeSlot}
                onChange={(times) => {
                  if (times) {
                    setTimeSlot(times as [any, any]);
                  } else {
                    setTimeSlot(null);
                  }
                }}
              />
            </div>
          </Form.Item>

          <div style={{ marginBottom: '16px' }}>
            <Space>
              <Button
                type="primary"
                onClick={setFullDayShift}
              >
                9:00 - 20:00
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* Batch Delete Confirmation Modal */}
      <Modal
        title="确认批量删除"
        open={deleteModalVisible}
        onCancel={() => setDeleteModalVisible(false)}
        onOk={confirmBatchDelete}
        okText="确认删除"
        cancelText="取消"
        okButtonProps={{ danger: true }}
        confirmLoading={loading}
      >
        <p>确定要删除选中的 {selectedSchedules.length} 条排班记录吗？此操作不可恢复。</p>
      </Modal>

      {/* Calendar Cell Detail Modal */}
      <Modal
        title={`${currentCellDate} 排班详情`}
        open={cellModalVisible}
        onCancel={() => setCellModalVisible(false)}
        footer={null}
        width={600}
      >
        {currentCellSchedules.length > 0 ? (
          <div>
            {/* Group schedules by time slot */}
            {Object.entries(currentCellSchedules.reduce((acc: { [key: string]: WorkScheduleVO[] }, schedule) => {
              const startTime = dayjs(schedule.startTime).format('HH:mm');
              const endTime = dayjs(schedule.endTime).format('HH:mm');
              const timeSlotKey = `${startTime}-${endTime}`;

              if (!acc[timeSlotKey]) {
                acc[timeSlotKey] = [];
              }

              acc[timeSlotKey].push(schedule);
              return acc;
            }, {})).sort().map(([timeSlot, schedules]) => (
              <div key={timeSlot} style={{ marginBottom: '16px' }}>
                <ul style={{ listStyle: 'none', padding: 0 }}>
                  {schedules.map((schedule: WorkScheduleVO) => {
                    const isPastDate = dayjs(schedule.startTime).isBefore(dayjs(), 'day');

                    return (
                      <li key={schedule.id} style={{
                        padding: '8px',
                        marginBottom: '8px',
                        border: '1px solid #f0f0f0',
                        borderRadius: '4px',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        opacity: isPastDate ? 0.6 : 1,
                        textDecoration: isPastDate ? 'line-through' : 'none',
                      }}>
                        <Flex style={{ width: '100%' }} justify="space-between" align="center">
                          <div style={{
                            fontSize: '14px',
                            fontWeight: 'bold',
                          }}>{renderUserWithRole(users.find(user => user.id === schedule.userId))}</div>
                          <div style={{ fontSize: '12px', color: '#888' }}>
                            {dayjs(schedule.startTime).format('HH:mm')} - {dayjs(schedule.endTime).format('HH:mm')}
                          </div>
                        </Flex>
                        {isAdmin && !isPastDate && (
                          <Space>
                            <Popconfirm
                              title="确定要删除该排班吗？"
                              onConfirm={(e) => {
                                e?.stopPropagation();
                                handleDelete(schedule.id);
                                setCellModalVisible(false);
                              }}
                              okText="确定"
                              cancelText="取消"
                            >
                              <Button
                                size="small"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={(e) => e.stopPropagation()}
                              />
                            </Popconfirm>
                          </Space>
                        )}
                      </li>
                    );
                  })}
                </ul>
              </div>
            ))}
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <p>该日期没有排班记录</p>
            {isAdmin && (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setCellModalVisible(false);
                  // Pre-select the date
                  const date = dayjs(currentCellDate);

                  // First prepare the modal
                  setModalTitle('添加排班');
                  setEditingRecord(null);
                  setTimeRange(null);
                  setSelectedDates([date]);
                  
                  // Then open the modal
                  setModalVisible(true);
                  
                  // Set the form values after modal is open
                  setTimeout(() => {
                    form.resetFields();
                    form.setFieldsValue({
                      dates: [date, date],
                    });
                    setFullDayShift();
                  }, 0);
                }}
              >
                添加排班
              </Button>
            )}
          </div>
        )}
      </Modal>
    </PageContainer>
  );
}
