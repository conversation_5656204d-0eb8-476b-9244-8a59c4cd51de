.title {
  font-size: 20px;
  height: 28px;
  line-height: 28px;
}

.tableCard {
  flex: 1;

  .tableHeader {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
  }

  :global {
    .ant-table {
      font-family: "PingFang SC", serif;
      font-size: 13px;
    }

    .ant-table-thead > tr > th {
      font-family: "PingFang SC", serif;
      font-weight: lighter;
      color: gray;
      font-size: 13px;
      line-height: 48px;
    }

    .ant-table-tbody > tr > td {
      font-family: "PingFang SC", serif;
      font-weight: lighter;
      font-size: 13px;
      line-height: 46px;
    }

    // 统一按钮样式
    .ant-btn {
      font-size: 13px;
      font-family: "PingFang SC", serif;
      font-weight: lighter;
    }

    // 特别针对表格中的按钮
    .ant-table-tbody .ant-btn {
      padding: 0 8px;
      height: 24px;
      line-height: 24px;
    }
  }
}

// ProTable 样式
.protableCard {
  flex: 1;

  .tableHeader {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
  }

  :global {
    // 表格整体样式
    .ant-pro-table {
      font-family: "PingFang SC", serif;
      font-size: 13px;
    }
    
    // 表格主体样式
    .ant-table {
      font-family: "PingFang SC", serif;
      font-size: 13px;
    }

    // 表头样式
    .ant-table-thead > tr > th {
      font-family: "PingFang SC", serif;
      font-weight: lighter;
      color: gray;
      font-size: 13px;
      line-height: 48px;
      padding: 0 16px;
      background-color: #fafafa;
    }

    // 表格内容行样式
    .ant-table-tbody > tr > td {
      font-family: "PingFang SC", serif;
      font-weight: lighter;
      font-size: 13px;
      line-height: 46px;
      padding: 0 16px;
    }
    
    // 工具栏样式
    .ant-pro-table-toolbar {
      padding: 12px 0;
      
      .ant-pro-table-toolbar-title {
        font-family: "PingFang SC", serif;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
      }
      
      .ant-pro-table-toolbar-option {
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
    
    // 搜索表单样式
    .ant-pro-table-search {
      padding: 16px;
      margin-bottom: 16px;
      background-color: #fff;
      
      .ant-form-item-label > label {
        font-family: "PingFang SC", serif;
        font-size: 13px;
      }
    }
    
    // 分页样式
    .ant-pagination-item {
      font-family: "PingFang SC", serif;
      font-size: 13px;
    }
    
    // 按钮样式
    .ant-btn {
      font-size: 13px;
      font-family: "PingFang SC", serif;
      font-weight: lighter;
    }
    
    // 操作列按钮样式
    .ant-table-tbody .ant-btn {
      padding: 0 8px;
      height: 24px;
      line-height: 24px;
    }
  }
}