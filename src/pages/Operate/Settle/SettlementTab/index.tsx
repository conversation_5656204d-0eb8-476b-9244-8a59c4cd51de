import { ProTable } from '@ant-design/pro-components';
import {
  DistributorSettlementVO,
  queryAllDistributorPrincipalBasicInfo,
  queryDistributorSettlementListByPage,
  settleStatusMap,
} from '@/services/FinanceController';

import styles from '@/pages/Operate/Settle/index.less';
import type { ActionType, FormInstance } from '@ant-design/pro-components';
import { Divider, Space, Button, Flex, Tooltip } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import ModalForManualSettlement from '../components/ModalForManualSettlement';
import { getUserRoleType } from '@/utils/utils';
import OrderDetailDrawer from '../components/OrderDetailDrawer';
import { DistributorBasicVO } from '@/services/SettlementController';
import { DistributorEnhancedVO } from '@/services/AssessmentController';
import { CustomRoleMap } from '@/services/UserController';
import { ColorPoint } from '@/pages/Operate/Settle/components/SettlementConfigDrawer';
import { salesTypeMap } from '@/services/OrganizationController';

// 全局缓存，确保 distributorBasicInfo 只加载一次
let distributorBasicInfoCache: DistributorBasicVO[] | null = null;
let distributorBasicInfoPromise: Promise<DistributorBasicVO[]> | null = null;

// 获取 distributorBasicInfo 的函数，带缓存机制
const getDistributorBasicInfo = async (): Promise<DistributorBasicVO[]> => {
  // 如果已经有缓存，直接返回
  if (distributorBasicInfoCache) {
    return distributorBasicInfoCache;
  }
  
  // 如果正在加载中，返回同一个 Promise
  if (distributorBasicInfoPromise) {
    return distributorBasicInfoPromise;
  }
  
  // 开始加载数据
  distributorBasicInfoPromise = queryAllDistributorPrincipalBasicInfo().then((res) => {
    distributorBasicInfoCache = res || [];
    distributorBasicInfoPromise = null; // 清除 Promise 引用
    return distributorBasicInfoCache;
  }).catch((error) => {
    distributorBasicInfoPromise = null; // 出错时也要清除 Promise 引用
    console.error('Failed to fetch distributor basic info:', error);
    return [];
  });
  
  return distributorBasicInfoPromise;
};

export default () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  // 定义初始值状态
  const [initialValues] = useState({
    status: 0, // 默认查询"已结算"
  });

  // 抽屉相关状态
  const [drawerVisible, setDrawerVisible] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<{
    distributorCorpName: string;
    settleId: string | number;
  } | null>(null);

  // 结算主体基础信息加载状态
  const [distributorBasicInfo, setDistributorBasicInfo] = useState<DistributorBasicVO[]>([]);
  const [distributorBasicInfoLoaded, setDistributorBasicInfoLoaded] = useState<boolean>(false);
  const [distributorBasicInfoLoading, setDistributorBasicInfoLoading] = useState<boolean>(true);
  
  // 使用 ref 来保存最新的 distributorBasicInfo 数据
  const distributorBasicInfoRef = useRef<DistributorBasicVO[]>([]);

  useEffect(() => {
    // 预加载 distributorBasicInfo
    const loadBasicInfo = async () => {
      try {
        setDistributorBasicInfoLoading(true);
        const data = await getDistributorBasicInfo();
        setDistributorBasicInfo(data);
        distributorBasicInfoRef.current = data; // 同时更新 ref
        setDistributorBasicInfoLoaded(true);
      } catch (error) {
        console.error('Failed to load distributor basic info:', error);
        setDistributorBasicInfo([]);
        distributorBasicInfoRef.current = []; // 同时更新 ref
        setDistributorBasicInfoLoaded(true); // 即使失败也设置为已加载，避免无限等待
      } finally {
        setDistributorBasicInfoLoading(false);
      }
    };

    loadBasicInfo();
  }, []);

  // 自动提交表单的函数
  const autoSubmitForm = () => {
    setTimeout(() => {
      formRef.current?.submit();
    }, 0);
  };

  const handleViewOrderDetail = (record) => {
    setCurrentRecord({
      distributorCorpName: record.distributorCorpName,
      settleId: record.settleId,
    });
    setDrawerVisible(true);
  };

  const columns = [
    {
      title: '结算主体',
      dataIndex: 'principalName',
      key: 'principalName',
      width: 280,
      search: false,
      render: (_, record: DistributorSettlementVO) => {
        if (!record.basicInfo) {
          return <>-</>;
        }
        const orgName = record.basicInfo.subCorpName || record.basicInfo.masterCorpName;
        const orgId = record.basicInfo.subCorpName ? record.basicInfo.subCorpId : record.basicInfo.masterCorpId;

        return (
          <Flex align="center" style={{ gap: 4 }}>
            <Tooltip title={orgName}>
                <span style={{
                  display: 'inline-block',
                  width: '120px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}>
                  {orgName}
                </span>
            </Tooltip>
            <span>(<span style={{ color: '#1890ff' }}>{orgId}</span>)</span>

            { record.basicInfo.principalType === 'USER' &&
              <>
                <span style={{color: '#BFBFBF', margin: '0 4px'}}>|</span>
                <Tooltip title={record.basicInfo.principalName}>
                  <span style={{
                    display: 'inline-block',
                    width: '120px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}>
                    {record.basicInfo.principalName}
                  </span>
                </Tooltip>
                <span>(<span style={{color: '#1890ff'}}>{record.basicInfo.principalId}</span>)</span>
              </>
            }
          </Flex>
        )
      },
    },
    {
      title: '销售/渠道商',
      dataIndex: 'distributorCorpName',
      key: 'distributorCorpName',
    },
    {
      title: '销售/渠道商类型 | 角色',
      dataIndex: 'salesType',
      key: 'salesType',
      width: 290,
      search: false,
      render: (_, record) => {
        if (!record.basicInfo) {
          return <>-</>;
        }
        const salesType = record.basicInfo.subSalesType || record.basicInfo.salesType;
        const roleType = record.basicInfo.principalType === 'CORP' ? '' :
          record.basicInfo.principalType === 'SUB_CORP' ? '二级渠道' :
            CustomRoleMap[record.basicInfo.customRole];
        const color :string = roleType === '渠道管理员' ? '#F5222D' :
          roleType === '二级渠道管理员' ? '#FAAD14' :
            roleType === '二级渠道' ? '#16f2fa' : 'gray';
        return (
          <Flex style={{gap: 16}}>
            <span style={{ display: 'inline-flex', alignItems: 'center', width: 66 }}>
              <ColorPoint color={salesTypeMap[salesType].color} />
              {salesTypeMap[salesType].desc}
            </span>
            <span style={{color: '#BFBFBF'}}>|</span>
            <span>
              <ColorPoint color={color} />
              {roleType || '-'}
            </span>
          </Flex>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: settleStatusMap,
      fieldProps: {
        placeholder: '请选择状态',
        options: Object.entries(settleStatusMap).map(([_, val]) => ({label: val.text, value: val.code})),
        onChange: autoSubmitForm,
      },
      render: (_, record) => {
        return settleStatusMap[record.status].text;
      },
    },
    {
      title: '结算时间',
      dataIndex: 'settleTime',
    },
    {
      title: '订单数',
      dataIndex: 'orderNum',
      search: false,
    },
    {
      title: '总金额(元)',
      dataIndex: 'totalAmount',
      search: false,
    },
    {
      title: '结算金额(元)',
      dataIndex: 'settleAmount',
      search: false,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      search: false,
      render: (_, record: DistributorSettlementVO) => {
        return (
          <Space split={<Divider type="vertical"/>}>
            {record.status === 'PENDING_SETTLE' && getUserRoleType() === 'ADMIN' && (
              <ModalForManualSettlement
                settleData={record}
                isButton={false}
                onSuccess={() => {
                  actionRef?.current?.reload();
                }}
              />
            )}
            <Button 
              type="link" 
              onClick={() => handleViewOrderDetail(record)}
              style={{ padding: 0 }}
            >
              查看对账单
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <div className={styles.container}>
      <ProTable
        rowKey="id"
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        className={styles.protableCard}
        style={{margin: 16}}
        // 设置表单默认值
        form={{
          initialValues,
        }}
        search={{
          labelWidth: 'auto',
          searchText: '查询',
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        dateFormatter="string"
        request={async (params) => {
          console.log('ProTable request triggered, distributorBasicInfoLoaded:', distributorBasicInfoLoaded);
          console.log('distributorBasicInfoRef.current length:', distributorBasicInfoRef.current.length);
          
          // 确保 distributorBasicInfo 已加载完成再执行请求
          let currentDistributorBasicInfo = distributorBasicInfoRef.current;
          
          if (!distributorBasicInfoLoaded || currentDistributorBasicInfo.length === 0) {
            console.log('Loading distributorBasicInfo...');
            // 如果还没加载完成，等待加载完成并获取最新数据
            currentDistributorBasicInfo = await getDistributorBasicInfo();
            distributorBasicInfoRef.current = currentDistributorBasicInfo; // 更新 ref
            console.log('distributorBasicInfo loaded, length:', currentDistributorBasicInfo.length);
          }
          
          //参数转换
          const page = await queryDistributorSettlementListByPage(params);
          if (page && page.list) {
            console.log('Processing page.list, items count:', page.list.length);
            page.list = page.list.map(item => {
              const basicInfo = currentDistributorBasicInfo.find(info => info.principalId === item.principalId);
              if (basicInfo) {
                item.basicInfo = basicInfo;
              } else {
                console.warn('No basicInfo found for principalId:', item.principalId);
              }
              return item;
            });
          }
          //转换为ProTable需要的数据字段
          return {
            data: page && page.list ? page.list : [],
            total: page && page.list ? page.totalCount : 0,
            success: true,
          }
        }}
        // 在 distributorBasicInfo 加载完成前显示加载状态
        loading={distributorBasicInfoLoading}
      />

      {/* 对账单抽屉 */}
      <OrderDetailDrawer
        visible={drawerVisible}
        onClose={() => setDrawerVisible(false)}
        corporationName={currentRecord?.distributorCorpName || ''}
        settleId={currentRecord?.settleId || null}
      />
    </div>
  );
};
