.rateStyle{
    margin-left: 20px;
    margin-bottom: 20px;

.serviceStyle {
    display: flex;
   
    .cardStyle{
        width: 131px;
        height: 70px;
        border-radius: 3px;
        opacity: 0.8;
        /* 中性色/页面背景 color_bg_page */
        background: #F4F5F7;
    }

    .nextValueStyle {
        margin-left: 10px;
        margin-top: 20px;
        > div{
            margin-left: 10px;
            // width: 264px;
            height: 16px;
            opacity: 1;
    
            font-family: Alibaba PuHuiTi 2.0;
            font-size: 16px;
            font-weight: normal;
            line-height: 16px;
            letter-spacing: 0px;
    
            /* 填充色/#999999 */
            color: #999999;
         }
    }
    .title{
        text-align: center;
        padding-top: 10px;
    }
    .text {
        padding-top: 10px;
        height: 24px;
        opacity: 1;
        text-align: center;
        font-family: Alibaba PuHuiTi 2.0;
        font-size: 24px;
        font-weight: normal;
        line-height: 24px;
        letter-spacing: 0px;

        /* 中性色/标题 color_text_title */
        color: #131212;
    }
}

.modifyBtn {
    margin-top: 10px;
    display: flex;
    align-items: center;
     > div{
        margin-left: 10px;
        // width: 264px;
        height: 16px;
        opacity: 1;

        font-family: Alibaba PuHuiTi 2.0;
        font-size: 16px;
        font-weight: normal;
        line-height: 16px;
        letter-spacing: 0px;

        /* 填充色/#999999 */
        color: #999999;
     }
 }
}