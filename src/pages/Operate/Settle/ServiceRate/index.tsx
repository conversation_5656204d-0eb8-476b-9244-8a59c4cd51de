import { useRef } from 'react';
import ModalForRateAdjustment from '../components/ModalForRateAdjustment';
import {
  DistributorSettleRateItem,
  queryAllDistributorSettleRateConfigs,
} from '@/services/FinanceController';
import CommonTable from '@/components/commonTable';
import { Divider, Flex, Space } from 'antd';
import type { ActionType } from '@ant-design/pro-components';
import type { ColumnsType } from 'antd/es/table';
export default () => {

  //当前表格的引用，用于刷新数据
  const actionRef = useRef<ActionType>();

  const columns: ColumnsType<DistributorSettleRateItem> = [
    {
      title: '销售/渠道商',
      dataIndex: 'corpName',
    },
    {
      title: '销售/渠道商管理员',
      dataIndex: 'masterUserName',
    },
    {
      title: '当前费率',
      key: 'currentRate',
      render: (_, record) => (record.settleRate ? `${Number(record.settleRate.confValue) * 100}%` : '未设置')
    },
    {
      title: '变更后费率',
      key: 'nextRate',
      hidden: true,
      render: (_, record) => (record.settleRate && record.settleRate.confValueNext ? `${Number(record.settleRate.confValueNext) * 100}%` : '')
    },
    {
      title: '变更生效时间',
      key: 'effectTime',
      hidden: true,
      render: (_, record) => (record.settleRate && record.settleRate.confValueNext && record.settleRate.effectTime ? record.settleRate.effectTime : '')
    },
    {
      title: '操作',
      key: 'option',
      render: (_, record) => {
        return (
          <Space split={<Divider type="vertical" />}>
            <ModalForRateAdjustment
              rateValue={record?.settleRate?.confValue ? Number(record?.settleRate?.confValue) * 100 : null}
              distributorCorpId={record.corpId}
              onSuccess={() => {
                actionRef?.current?.reload();
              }}
            />
          </Space>
        );
      },
    }
  ];

  return (

    <CommonTable
      columns={columns}
      actionRef={actionRef}
      pagination={false}
      request={async () => {
        const rateCfg = await queryAllDistributorSettleRateConfigs();
        //响应转换为ProTable需要的数据字段
        return {
          data: rateCfg?.distributorSettleRateList || [],
          success: true
        };
      }}
    />

  );
};
