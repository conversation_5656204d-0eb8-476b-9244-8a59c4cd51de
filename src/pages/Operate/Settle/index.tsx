import { PageContainer } from '@ant-design/pro-components';
import { Flex, Tabs, TabsProps } from 'antd';
import React, { useEffect, useState } from 'react';
import SettlementTab from './SettlementTab/index';
import AssessmentPlan from './AssessmentPlan';

export default () => {
  const [tabKey, setTabKey] = useState('ASSESSMENT_PLAN');

  useEffect(() => {
  }, []);
  // tabItems
  const tableItems :TabsProps['items'] = [
    {key: 'ASSESSMENT_PLAN', label: '考核方案'},
    {key: 'SETTLEMENT_TAB', label: '结算详情'},
  ];
  // 组件映射
  const componentMap = {
    SETTLEMENT_TAB: SettlementTab,
    ASSESSMENT_PLAN: AssessmentPlan
  };
  const CurrentComponent = tabKey ? componentMap[tabKey] : null;
  const handleTabChange = (key: string) => {
    setTabKey(key);
  }

  return (
    <PageContainer>
      <div className="toolkit-form-container">
        <Flex vertical className={'toolkit-form-body'}>
          <Tabs activeKey={tabKey} items={tableItems} onChange={(key) => {handleTabChange(key)}} size={'large'}
                indicator={{ size: 102 }} />
        </Flex>
      </div>
      {CurrentComponent && <CurrentComponent className={"toolkit-content"} />}
    </PageContainer>
  );
};
