.modal {
  :global {
    .ant-form-item {
      margin-bottom: 0;
    }
  }
}
// 表单只读
.readonly-form {
  .ant-input:disabled,
  .ant-select-disabled .ant-select-selector,
  .ant-picker-disabled,
  .ant-input-number-disabled {
    color: rgba(0, 0, 0, 0.88) !important;
    background-color: #ffffff !important;
    border-color: #d9d9d9 !important;
    cursor: default !important;
    opacity: 1 !important;
  }
  
  .ant-select-disabled .ant-select-selection-item {
    color: rgba(0, 0, 0, 0.88) !important;
  }
  
  .ant-picker-disabled .ant-picker-input > input {
    color: rgba(0, 0, 0, 0.88) !important;
  }
}