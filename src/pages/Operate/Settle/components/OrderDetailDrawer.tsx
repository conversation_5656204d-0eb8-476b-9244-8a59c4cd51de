import React, { useRef, useState } from 'react';
import { Drawer, Space, Spin } from 'antd';
import SearchComponent from '@/components/SearchComponent';
import CommonTable from '@/components/commonTable/index';
import type { ActionType } from '@ant-design/pro-components';
import { queryOrderSettlementsByPage } from '@/services/FinanceController';
import _ from 'lodash';
import mainStyles from '@/pages/Operate/Settle/index.less';
import { KEY_IS_NEW_ORDER, orderSettlementStatusMap, orderSettleTypeMap } from '@/services/SettlementController';

interface OrderDetailDrawerProps {
  visible: boolean;
  onClose: () => void;
  corporationName: string;
  settleId: string | number | null;
}

const OrderDetailDrawer: React.FC<OrderDetailDrawerProps> = ({
  visible,
  onClose,
  corporationName,
  settleId,
}) => {
  const actionRef = useRef<ActionType>();
  const [topupMerchantNick, setTopupMerchantNick] = useState('');

  const footerCellRender = (field, record, type) => {
    // 合计行始终显示加粗样式的数值
    if (record.orderNo === '合计') {
      return (
        <span
          style={{
            display: 'inline-block',
            fontSize: '17px',
            fontWeight: '800',
            textAlign: 'right',
          }}
        >
          {field}
        </span>
      );
    }
    
    // 普通行根据类型匹配来决定显示内容
    if (type === 'TOTAL') {
      // 充值金额对所有类型都显示
      return field;
    } else if (type === 'COMMISSION') {
      // 分佣相关字段只对 COMMISSION 类型显示
      return record.type === 'COMMISSION' ? field : <span>-</span>;
    } else if (type === 'COMMISSION_RELATED') {
      // 抽成相关字段只对 COMMISSION_RELATED 类型显示
      return record.type === 'COMMISSION_RELATED' ? field : <span>-</span>;
    }
    
    return field;
  };

  const columns = [
    {
      title: '订单号',
      key: 'orderNo',
      dataIndex: 'orderNo',
      width: '20%'
    },
    {
      title: '结算类型',
      key: 'type',
      dataIndex: 'type',
      render: (_, record) => {
        return <span>{orderSettleTypeMap[record.type]?.desc || '-'}</span>
      }
    },
    {
      title: '充值商家',
      key: 'merchantCorpName',
      dataIndex: 'merchantCorpName',
    },
    {
      title: '充值商家管理员',
      key: 'merchantName',
      dataIndex: 'merchantName',
    },
    {
      title: '充值时间',
      key: 'orderFinishTime',
      valueType: 'dateTime',
      dataIndex: 'orderFinishTime',
    },
    {
      title: '结算时间',
      key: 'settleTime',
      valueType: 'dateTime',
      dataIndex: 'settleTime',
    },
    {
      title: '状态',
      key: 'status',
      valueType: 'status',
      dataIndex: 'status',
      render : (_, record) => {
        return <span>{orderSettlementStatusMap[record.status]}</span>
      }
    },
    {
      title: '充值金额(元)',
      key: 'totalAmount',
      dataIndex: 'totalAmount',
      render: (field, record) => {
        return footerCellRender(field, record, 'TOTAL');
      },
    },
    {
      title: '新续(180天)',
      key: 'renewedOrder',
      render: (_, record) => {
        if (record.orderNo === '合计'){
          return '-';
        }

        if (record && record?.extInfo) {
          const extInfo = typeof record.extInfo === 'string' ? JSON.parse(record.extInfo) : record.extInfo;
          if (extInfo[KEY_IS_NEW_ORDER] === true) {
            return <span style={{color: 'red'}}>续签</span>;
          }
        }

        return '新签';
      },
    },
         {
       title: '分佣费率',
       key: 'commissionRate',
       dataIndex: 'channelRate',
       render: (field, record) => {
         if (record.orderNo === '合计') {
           return <span>-</span>;
         }
         if (record.type === 'COMMISSION') {
           const value: string = `${Number(field || 0) * 100}%`;
           return <span>{value}</span>;
         }
         return <span>-</span>;
       }
     },
    {
      title: '分佣金额(元)',
      key: 'commissionAmount',
      dataIndex: 'settleAmount',
      render: (field, record) => {
        return footerCellRender(field, record, 'COMMISSION');
      }
    },
         {
       title: '抽成费率',
       key: 'commissionRelatedRate',
       dataIndex: 'channelRate',
       render: (field, record) => {
         if (record.orderNo === '合计') {
           return <span>-</span>;
         }
         if (record.type === 'COMMISSION_RELATED') {
           const value: string = `${Number(field || 0) * 100}%`;
           return <span>{value}</span>;
         }
         return <span>-</span>;
       }
     },
    {
      title: '抽成金额(元)',
      key: 'commissionRelatedAmount',
      dataIndex: 'commissionRelatedAmount',
      render: (field, record) => {
        return footerCellRender(field, record, 'COMMISSION_RELATED');
      },
    },
  ];

  return (
    <Drawer
      title={`${corporationName || ''}对账单`}
      placement="right"
      onClose={onClose}
      open={visible}
      width="80%"
      destroyOnClose={true}
      extra={
        <SearchComponent
          type="search"
          key="search"
          placeholder="搜索充值商家"
          onSearch={(value: string) => {
            setTopupMerchantNick(value);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }}
          style={{ width: 200 }}
        />
      }
    >
      <div style={{ padding: '0 20px' }}>
        <CommonTable
          className={mainStyles.protableCard}
          columns={columns}
          actionRef={actionRef}
          request={async (params) => {
            // 参数转换
            const page = await queryOrderSettlementsByPage({
              ...params,
              settleId: settleId,
              merchantCorpName: topupMerchantNick || undefined
            });
            if (page) {
              const footerObj = {
                orderNo: '合计',
                totalAmount: page?.extInfo?.totalOrderAmount,
                serviceFee: page?.extInfo?.totalServiceFee,
                settleAmount: page?.extInfo?.totalSettleAmount, // 用于分佣金额和抽成金额的数据源
                commissionAmount: page?.extInfo?.totalCommissionAmount,
                commissionRelatedAmount: page?.extInfo?.totalCommissionRelatedAmount,
              };
              const tempData = _.concat(page.list, footerObj);
              return {
                data: tempData || [],
                total: page.totalCount || 0,
                success: true,
              };
            } else {
              return {
                data: [],
                total: 0,
                success: true,
              };
            }
          }}
        />
      </div>
    </Drawer>
  );
};

export default OrderDetailDrawer;
