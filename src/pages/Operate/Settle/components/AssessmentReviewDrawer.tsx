import {
  assessStatusMap,
  DistributorEnhancedVO,
  principalTypeMap,
  reviewAssessmentPlan,
  salesAssessTypeMap,
} from '@/services/AssessmentController';
import {
  convertToWan,
  decimalToPercent,
  percentToDecimal,
  SettleConfigModel,
  SettleTypeMap,
} from '@/services/SettlementController';
import { OrganizationVO, salesTypeMap } from '@/services/OrganizationController';
import { Button, Drawer, Flex, Spin, Tooltip, Typography, Alert, Switch, message, Modal } from 'antd';
import {
  ProCard,
  ProDescriptions,
  ProForm,
  ProFormDigit,
  ProFormSelect,
  ProFormSwitch,
} from '@ant-design/pro-components';
import { CustomRoleMap } from '@/services/UserController';
import React, { useState, useEffect } from 'react';
import { ColorPoint } from '@/pages/Operate/Settle/components/SettlementConfigDrawer';

interface AssessmentReviewDrawerProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  distributorEnhancedVO: DistributorEnhancedVO | null;
  defaultSettleConfigs: SettleConfigModel[];
  allOrganizations: OrganizationVO[];
}
const AssessmentReviewDrawer: React.FC<AssessmentReviewDrawerProps> = ({
   visible,
   onClose,
   onSuccess,
   distributorEnhancedVO,
   defaultSettleConfigs,
   allOrganizations,}) => {

  const [formRef] = ProForm.useForm();
  const [newConfigFormRef] = ProForm.useForm();
  const [formLoading, setFormLoading] = useState(false);
  const [showCalculationDateHelp, setShowCalculationDateHelp] = useState(false);
  const [needEditConfig, setNeedEditConfig] = useState(false);

  useEffect(() => {
    if (visible && distributorEnhancedVO?.settleConfig) {
      const value = distributorEnhancedVO.settleConfig;

      formRef.setFieldsValue({
        ...value,
        // 小数转百分比
        newOrderCommRate: decimalToPercent(value.newOrderCommRate),
        renewOrderCommRate: decimalToPercent(value.renewOrderCommRate),
        initYrRate: decimalToPercent(value.initYrRate),
        subseqYrsRate: decimalToPercent(value.subseqYrsRate),
      });
      
      newConfigFormRef.setFieldsValue({
        hasBeneficiary: value.hasBeneficiary,
        beneficiaryTargetId: value.beneficiaryTargetId,
      });
    }
  }, [visible, distributorEnhancedVO, formRef, newConfigFormRef]);

  useEffect(() => {

    if (visible && distributorEnhancedVO &&
      (distributorEnhancedVO.salesType === salesTypeMap.DIRECT.code) &&
      (distributorEnhancedVO.customRole !== 'CHANNEL_ADMIN')) {
      formRef.setFieldsValue({
        hasBeneficiary: true,
        beneficiaryTargetId: distributorEnhancedVO.channelAdminId,
      })
    }
    if (!visible) {
      newConfigFormRef.resetFields();
      setNeedEditConfig(false);
    }

    return () => {
      newConfigFormRef.resetFields();
    }
  }, [visible, newConfigFormRef]);

  const applyPresetConfig = (preset: SettleConfigModel) => {
    newConfigFormRef.setFieldsValue(preset);
  };

  const handleSubmit = async (values: any) => {
    Modal.confirm({
      title: '确认提交',
      centered: true,
      content: '确认提交新的结算配置并完成考核结果确认吗？',
      onOk: async () => {
        try {
          setFormLoading(true);
          let planId :number = 0;
          if (distributorEnhancedVO?.currentPlan.id) {
            planId = distributorEnhancedVO.currentPlan.id;
          } else {
            message.error('数据异常');
            return;
          }
          let newSettleConfig = {
            ...values,
            // 百分比转小数
            newOrderCommRate: percentToDecimal(values.newOrderCommRate),
            renewOrderCommRate: percentToDecimal(values.renewOrderCommRate),
            initYrRate: percentToDecimal(values.initYrRate),
            subseqYrsRate: percentToDecimal(values.subseqYrsRate),
          } as SettleConfigModel;

          const result = await reviewAssessmentPlan(planId, newSettleConfig);
          if (result) {
            message.success('考核处理结果提交成功, 并创建新的考核计划')
            onSuccess();
            onClose();
          } else {
            message.error('提交失败');
          }
        } catch (error) {
          message.error('提交失败');
        } finally {
          setFormLoading(false);
        }
      },
      onCancel: () => {
        // 用户取消，不执行任何操作
      }
    });
  };

  const handleDirectConfirm = async () => {
    Modal.confirm({
      title: '确认提交',
      centered: true,
      content: '确认完成考核结果确认吗？',
      onOk: async () => {
        try {
          setFormLoading(true);
          let planId :number = 0;
          if (distributorEnhancedVO?.currentPlan.id) {
            planId = distributorEnhancedVO.currentPlan.id;
          } else {
            message.error('数据异常');
            return;
          }
          const result = await reviewAssessmentPlan(planId);
          if (result) {
            message.success('考核处理结果提交成功, 并创建新的考核计划')
            onSuccess();
            onClose();
          } else {
            message.error('提交失败');
          }
        } catch (error) {
          console.error('确认失败:', error);
        } finally {
          setFormLoading(false);
        }
      },
      onCancel: () => {
        // 用户取消，不执行任何操作
      }
    });
  };

  return (
    <Drawer
      title={`考核结果确认 - ${distributorEnhancedVO?.principalName || '未知用户'}`}
      placement="right"
      onClose={onClose}
      destroyOnClose
      open={visible}
      width={'50%'}
      footer={
        <div style={{ textAlign: 'right' }}>
          <Button onClick={onClose} style={{ marginRight: 8 }}>
            取消
          </Button>
          {needEditConfig ? (
            <Button
              type="primary"
              onClick={() => newConfigFormRef.submit()}
              loading={formLoading}
            >
              确认考核结果并更新配置
            </Button>
          ) : (
            <Button
              type="primary"
              onClick={handleDirectConfirm}
              loading={formLoading}
            >
              确认考核结果
            </Button>
          )}
        </div>
      }
    >
      {formLoading ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin />
        </div>
      ) : distributorEnhancedVO ? (
        <Flex vertical gap={16}>
          {/* 数据展示层（顶部区块） */}
          <div>
            <Typography.Title level={5}>基本信息</Typography.Title>
            <ProDescriptions 
              column={2} 
              bordered 
              dataSource={distributorEnhancedVO}
              labelStyle={{ width: '100px' }}
              contentStyle={{ width: '150px' }}
            >
              <ProDescriptions.Item label="主组织ID" dataIndex="masterCorpId" />
              <ProDescriptions.Item label="主组织名称" dataIndex="masterCorpName" />
              <ProDescriptions.Item
                label="类型"
                dataIndex="salesType"
                render={(_, record) => (
                  <span
                    style={{ display: 'inline-flex', alignItems: 'center' }}
                  >
                    <ColorPoint color={salesTypeMap[record.salesType].color} />
                    {salesTypeMap[record.salesType].desc}
                  </span>
                )}
              />
              <ProDescriptions.Item label="主账号ID" dataIndex="channelAdminId" />
              <ProDescriptions.Item label="主账号昵称" dataIndex="channelAdminNickName" />
              <ProDescriptions.Item
                label="用户ID"
                render={(_, record) => {
                  let userId;
                  switch (record.principalType) {
                    case 'CORP':
                      userId = record.channelAdminId;
                      break;
                    case 'SUB_CORP':
                      userId = record.subChannelAdminId;
                      break;
                    default :
                      userId = record.principalId;
                      break;
                  }
                  return (
                    <span>{userId}</span>
                  )
                }}
              />
              <ProDescriptions.Item
                label="用户昵称"
                render={(_, record) => {
                  let userName;
                  switch (record.principalType) {
                    case 'CORP':
                      userName = record.channelAdminNickName;
                      break;
                    case 'SUB_CORP':
                      userName = record.subChannelAdminNickName;
                      break;
                    default :
                      userName = record.principalName;
                      break;
                  }
                  return (
                    <span>{userName}</span>
                  )
                }}
              />
              {distributorEnhancedVO.subCorpId && (
                <>
                  <ProDescriptions.Item
                    label="子组织ID"
                    dataIndex="subCorpId"
                  />
                  <ProDescriptions.Item
                    label="子组织名称"
                    dataIndex="subCorpName"
                  />
                </>
              )}
              <ProDescriptions.Item
                label="组织内角色"
                dataIndex="customRole"
                render={(_, record) => (
                  <span
                    style={{ display: 'inline-flex', alignItems: 'center' }}
                  >
                    {record.customRole === 'CHANNEL_ADMIN' &&
                      <ColorPoint color="red" />
                    }
                    {CustomRoleMap[record.customRole]}
                  </span>
                )}
              />
            </ProDescriptions>
          </div>

          {/* 考核计划信息展示层 */}
          <div>
            <Typography.Title level={5}>考核计划信息</Typography.Title>
            <ProDescriptions 
              column={2} 
              bordered 
              dataSource={distributorEnhancedVO?.currentPlan}
              labelStyle={{ width: '100px' }}
              contentStyle={{ width: '150px' }}
            >
              <ProDescriptions.Item 
                label="考核主体类型"
                dataIndex="principalType"
                render={(_, record) => {
                  return principalTypeMap[record.principalType];
                }}
              />  
              <ProDescriptions.Item 
                label={distributorEnhancedVO.principalType  === 'CORP' ? '组织ID' : '用户ID'}
                dataIndex="principalId"
              />
              <ProDescriptions.Item 
                label={distributorEnhancedVO.principalType  === 'CORP' ? '组织名称' : '用户名称'}
                dataIndex="principalName"
                render={(_, record) => {
                  return distributorEnhancedVO?.principalName;
                }}
              />
              <ProDescriptions.Item 
                label="考核类型" 
                dataIndex="type"
                render={(_, record) => {
                  if (!record?.type) return <span style={{ color: 'red' }}>未设置</span>;
                  return salesAssessTypeMap[record.type] || record.type;
                }}
              />
              <ProDescriptions.Item 
                label="考核周期" 
                render={(_, record) => {
                  if (!record?.planFromDate) return <span style={{ color: 'red' }}>未设置</span>;
                  const startDate = record.planFromDate;
                  const endDate = record.planEndDate;
                  return endDate ? `${startDate} ~ ${endDate}` : startDate;
                }}
              />
              <ProDescriptions.Item
                label="考核状态"
                dataIndex="status"
                render={(_, record) => {
                  if (!record?.status) return <span style={{ color: 'red' }}>未设置</span>;
                  const statusInfo = assessStatusMap[record.status];
                  return (
                    <span style={{ display: 'inline-flex', alignItems: 'center' }}>
                      <ColorPoint color={statusInfo?.color} />
                      {statusInfo?.desc || record.status}
                    </span>
                  );
                }}
              />
              <ProDescriptions.Item 
                label="目标金额" 
                render={(_, record) => {
                  const target = record?.kpiTarget;
                  if (!target?.totalSalesAmount) return <span style={{ color: 'red' }}>未设置</span>;
                  return `${convertToWan(target.totalSalesAmount).toFixed(2)} 万元`;
                }}
              />
              <ProDescriptions.Item 
                label="目标订单数" 
                render={(_, record) => {
                  const target = record?.kpiTarget;
                  if (!target?.totalSalesCount) return <span style={{ color: 'red' }}>未设置</span>;
                  return `${target.totalSalesCount} 单`;
                }}
              />
              <ProDescriptions.Item 
                label="实际金额" 
                render={(_, record) => {
                  const actual = record?.kpiActual;
                  if (!actual?.totalSalesAmount) return <span style={{ color: '#999' }}>0</span>;
                  return `${convertToWan(actual.totalSalesAmount).toFixed(2)} 万元`;
                }}
              />
              <ProDescriptions.Item 
                label="实际订单数" 
                render={(_, record) => {
                  const actual = record?.kpiActual;
                  if (!actual?.totalSalesCount) return <span style={{ color: '#999' }}>0</span>;
                  return `${actual.totalSalesCount} 单`;
                }}
              />
              <ProDescriptions.Item 
                label="金额完成率" 
                render={(_, record) => {
                  const target = record?.kpiTarget;
                  const actual = record?.kpiActual;
                  if (!target?.totalSalesAmount || !actual?.totalSalesAmount) {
                    return <span style={{ color: '#999' }}>0%</span>;
                  }
                  const rate = ((actual.totalSalesAmount / target.totalSalesAmount) * 100).toFixed(1);
                  return (
                    <span style={{ color: Number(rate) >= 100 ? '#52c41a' : '#ff4d4f' }}>
                      {rate}%
                    </span>
                  );
                }}
              />
              <ProDescriptions.Item 
                label="订单完成率" 
                render={(_, record) => {
                  const target = record?.kpiTarget;
                  const actual = record?.kpiActual;
                  if (!target?.totalSalesCount || !actual?.totalSalesCount) {
                    return <span style={{ color: '#999' }}>0%</span>;
                  }
                  const rate = ((actual.totalSalesCount / target.totalSalesCount) * 100).toFixed(1);
                  return (
                    <span style={{ color: Number(rate) >= 100 ? '#52c41a' : '#ff4d4f' }}>
                      {rate}%
                    </span>
                  );
                }}
              />
              
            </ProDescriptions>
          </div>

          {/* 考核状态提示层 */}
          {distributorEnhancedVO?.currentPlan?.status && (
            <div>
              {distributorEnhancedVO.currentPlan.status === 'ASSESS_FAILED' && (
                <Alert
                  message="考核结果提示"
                  description="当前考核未通过，可能需要调整结算费率"
                  type="warning"
                  showIcon
                  style={{ marginBottom: 0 }}
                />
              )}
              {distributorEnhancedVO.currentPlan.status === 'ASSESS_PASSED' && (
                <Alert
                  message="考核结果提示"
                  description="当前考核已通过，直接确认考核结果"
                  type="success"
                  showIcon
                  style={{ marginBottom: 0 }}
                />
              )}
            </div>
          )}

          {/* 用户选择层 */}
          <div style={{ 
            padding: '16px', 
            backgroundColor: '#f0f7ff', 
            borderRadius: '6px',
            border: '1px solid #91d5ff'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>
                <Typography.Text strong style={{ color: '#1890ff' }}>
                  是否需要同时调整结算配置？
                </Typography.Text>
                <div style={{ marginTop: 4 }}>
                  <Typography.Text type="secondary" style={{ fontSize: 12 }}>
                    {needEditConfig ? '将显示编辑表单，可调整结算费率等配置' : '将直接确认考核结果，不修改当前结算配置'}
                  </Typography.Text>
                </div>
              </div>
              <Switch
                checked={needEditConfig}
                onChange={setNeedEditConfig}
                checkedChildren="调整费率"
                unCheckedChildren="不调整"
              />
            </div>
          </div>

          {/* 结算配置层 */}
          <div>
            <Typography.Title level={5}>结算配置</Typography.Title>
            <div style={{ display: 'flex', gap: '24px' }}>
              {/* 左侧：当前结算配置 - 始终显示 */}
              <div style={{ flex: 1 }}>
                <Typography.Title level={5} style={{ color: '#666', marginBottom: 16 }}>
                  当前配置
                </Typography.Title>
                <ProForm
                  form={formRef}
                  submitter={false}
                  layout="vertical"
                  readonly={true}
                  style={{ 
                    backgroundColor: '#fafafa', 
                    padding: '16px', 
                    borderRadius: '6px',
                    border: '1px solid #d9d9d9'
                  }}
                >
                  <ProFormSelect
                    name="settleType"
                    label="结算类型"
                    width={'md'}
                    options={Object.entries(SettleTypeMap).map(([key, value]) => ({label: value, value: key}))}
                  />
                  <ProFormDigit
                    name="calculationDate"
                    label="结算时间"
                    min={0}
                    max={27}
                    addonAfter="日"
                    fieldProps={{ precision: 0 }}
                    width={'md'}
                  />
                  <ProFormDigit
                    name="newOrderCommRate"
                    label="新订单费率(%)"
                    min={0}
                    max={100}
                    fieldProps={{ precision: 2 }}
                    addonAfter="%"
                    width={'md'}
                  />
                  <ProFormDigit
                    name="renewOrderCommRate"
                    label="续费订单费率(%)"
                    min={0}
                    max={100}
                    fieldProps={{ precision: 2 }}
                    addonAfter="%"
                    width={'md'}
                  />
                  <ProFormSwitch
                    name="hasBeneficiary"
                    label="是否被主账号抽成"
                    checkedChildren="是"
                    unCheckedChildren="否"
                  />
                  {distributorEnhancedVO?.settleConfig?.hasBeneficiary && (
                    <>
                      <ProFormSelect
                        name="beneficiaryTargetId"
                        label="抽成对象"
                        width={'md'}
                        options={allOrganizations.map(org => ({label: org.name, value: org.id}))}
                      />
                      <ProFormDigit
                        name="initYrRate"
                        label="新订单抽成费率(%)"
                        min={0}
                        max={100}
                        fieldProps={{ precision: 2 }}
                        addonAfter="%"
                        width={'md'}
                      />
                      <ProFormDigit
                        name="subseqYrsRate"
                        label="续费订单抽成费率(%)"
                        min={0}
                        max={100}
                        fieldProps={{ precision: 2 }}
                        addonAfter="%"
                        width={'md'}
                      />
                    </>
                  )}
                </ProForm>
              </div>

              {/* 右侧：新结算配置 - 条件显示 */}
              {needEditConfig && (
                <div style={{ flex: 1 }}>
                  <Typography.Title level={5} style={{ color: '#1890ff', marginBottom: 16 }}>
                    新配置（编辑）
                  </Typography.Title>
                  <ProForm
                    disabled
                    className="readonly-form"
                    form={newConfigFormRef}
                    onFinish={handleSubmit}
                    submitter={false}
                    layout="vertical"
                    style={{ 
                      backgroundColor: '#fff', 
                      padding: '16px', 
                      borderRadius: '6px',
                      border: '1px solid #1890ff'
                    }}
                  >
                    <ProFormSelect
                      name="settleType"
                      label="结算类型"
                      rules={[{ required: true, message: '请选择结算类型' }]}
                      placeholder="请选择结算类型"
                      width={'md'}
                      options={Object.entries(SettleTypeMap).map(([key, value]) => ({label: value, value: key}))}
                    />
                    <ProFormDigit
                      name="calculationDate"
                      label="结算时间"
                      rules={[{ required: true, message: '请输入结算时间' }]}
                      min={0}
                      max={27}
                      addonAfter="日"
                      fieldProps={{
                        precision: 0,
                        onFocus: () => setShowCalculationDateHelp(true),
                        onBlur: () => setShowCalculationDateHelp(false)
                      }}
                      tooltip={"例: 27 代表结算周期为上月28号(包含)到本月27号(包含), 0 代表结算周期为本月1号到本月月底"}
                      help={
                        <div style={{ minHeight: '24px', lineHeight: '24px' }}>
                          {showCalculationDateHelp && (
                            <span style={{ color: '#ff7a00', padding: '4px 8px'}}>
                              可选范围 0 ~ 27，0 代表每月月底结算
                            </span>
                          )}
                        </div>
                      }
                      width={'md'}
                    />
                    <ProFormDigit
                      name="newOrderCommRate"
                      label="新订单费率(%)"
                      rules={[{ required: true, message: '请输入新订单费率' }]}
                      min={0}
                      max={100}
                      fieldProps={{ precision: 2 }}
                      addonAfter="%"
                      width={'md'}
                    />
                    <ProFormDigit
                      name="renewOrderCommRate"
                      label="续费订单费率(%)"
                      rules={[{ required: true, message: '请输入续费订单费率' }]}
                      min={0}
                      max={100}
                      fieldProps={{ precision: 2 }}
                      addonAfter="%"
                      width={'md'}
                    />
                    <ProFormSwitch
                      name="hasBeneficiary"
                      label="是否被主账号抽成"
                      checkedChildren="是"
                      unCheckedChildren="否"
                    />
                    <ProForm.Item noStyle shouldUpdate>
                      {({ getFieldValue }) => {
                        const hasBeneficiary = getFieldValue('hasBeneficiary');
                        return hasBeneficiary ? (
                          <>
                            <ProFormSelect
                              name="beneficiaryTargetId"
                              label="抽成对象"
                              rules={[
                                { required: true, message: '请选择抽成对象' },
                              ]}
                              placeholder="请选择抽成对象"
                              width={'md'}
                              options={allOrganizations.map(org => ({label: org.name, value: org.id}))}
                            />
                            <ProFormDigit
                              name="initYrRate"
                              label="新订单抽成费率(%)"
                              rules={[
                                { required: true, message: '请输入新订单抽成费率' },
                              ]}
                              min={0}
                              max={100}
                              fieldProps={{ precision: 2 }}
                              addonAfter="%"
                              width={'md'}
                            />
                            <ProFormDigit
                              name="subseqYrsRate"
                              label="续费订单抽成费率(%)"
                              rules={[
                                { required: true, message: '请输入续费订单抽成费率' },
                              ]}
                              min={0}
                              max={100}
                              fieldProps={{ precision: 2 }}
                              addonAfter="%"
                              width={'md'}
                            />
                          </>
                        ) : null;
                      }}
                    </ProForm.Item>
                  </ProForm>
                </div>
              )}
            </div>
          </div>

          {/* 预设方案层（底部区块） - 条件显示 */}
          {needEditConfig ? (
            <div>
              <Typography.Title level={5}>预设方案</Typography.Title>
              <Typography.Text type="secondary" style={{ marginBottom: 16, display: 'block' }}>
                点击任一预设方案，将自动填入右侧编辑表单
              </Typography.Text>
              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
                  gap: '16px',
                }}
              >
                {defaultSettleConfigs.map((preset, index) => (
                  <Tooltip key={index} title={'点击应用到编辑表单'}>
                    <ProCard
                      title={<span style={{color: '#28a2ed'}} >{preset.memo}</span>}
                      bordered
                      hoverable
                      onClick={() => applyPresetConfig(preset)}
                    >
                      <div>
                        费率: 新单{preset.newOrderCommRate}% / 续费
                        {preset.renewOrderCommRate}%
                      </div>
                      {preset.hasBeneficiary && (
                        <div>
                          抽成: 新单{preset.initYrRate}% / 续费
                          {preset.subseqYrsRate}%
                        </div>
                      )}
                      <div>直营: {preset.salesType ? '是' : '否'}</div>
                    </ProCard>
                  </Tooltip>
                ))}
              </div>
            </div>
          ) : (
            <div style={{height: '350px'}}>&nbsp;</div>
          )
          }
        </Flex>
      ) : (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Typography.Text type="secondary">数据缺失</Typography.Text>
        </div>
      )}
    </Drawer>
  );
}

export default AssessmentReviewDrawer;