import React, { useState, useEffect } from 'react';
import { <PERSON>ton, Typography, Spin, Flex, Drawer, Tag, Tooltip, Divider } from 'antd';
import { ProDescriptions, ProForm, ProFormDigit, ProFormSwitch, ProFormSelect } from '@ant-design/pro-components';
import { ProCard } from '@ant-design/pro-components';
import {
  DistributorSettleConfigVO,
  SettleConfigModel,
  modifyDistributorSettlementConfig,
  percentToDecimal, SettleTypeMap, decimalToPercent,
} from '@/services/SettlementController';
import { OrganizationVO, salesTypeMap } from '@/services/OrganizationController';
import { message } from 'antd';
import { CustomRoleMap } from '@/services/UserController';
import '@/pages/Operate/Settle/components/index.less'

interface SettlementConfigDrawerProps {
  visible: boolean;
  onClose: () => void;
  distributorSettleConfigVO: DistributorSettleConfigVO | null;
  defaultSettleConfigs: SettleConfigModel[];
  allOrganizations: OrganizationVO[];
  drawerLoading: boolean;
  onSuccess?: () => void;
}

const SettlementConfigDrawer: React.FC<SettlementConfigDrawerProps> = ({
  visible,
  onClose,
  distributorSettleConfigVO,
  defaultSettleConfigs,
  allOrganizations,
  drawerLoading,
  onSuccess
}) => {
  const [formRef] = ProForm.useForm();
  const [formLoading, setFormLoading] = useState(false);
  const [showCalculationDateHelp, setShowCalculationDateHelp] = useState(false);

  // 应用预设方案
  const applyPresetConfig = (preset: SettleConfigModel) => {
    formRef.setFieldsValue(preset);
  };
  
  useEffect(() => {
    if (visible && distributorSettleConfigVO && distributorSettleConfigVO.settleConfig) {
      const value = distributorSettleConfigVO.settleConfig;
      formRef.setFieldsValue({
        ...value,
        // 小数转百分比
        newOrderCommRate: decimalToPercent(value.newOrderCommRate),
        renewOrderCommRate: decimalToPercent(value.renewOrderCommRate),
        initYrRate: decimalToPercent(value.initYrRate),
        subseqYrsRate: decimalToPercent(value.subseqYrsRate),
      });
    } else if (visible && distributorSettleConfigVO &&
      (distributorSettleConfigVO.salesType === salesTypeMap.DIRECT.code) &&
      (distributorSettleConfigVO.customRole !== 'CHANNEL_ADMIN')) {
      formRef.setFieldsValue({
        hasBeneficiary: true,
        beneficiaryTargetId: distributorSettleConfigVO.channelAdminId,
      })
    } else {
      formRef.resetFields();
    }

    return () => {
      formRef.resetFields();
    }
    
  }, [visible, formRef, distributorSettleConfigVO])

  // 提交表单
  const handleSubmitSettleConfig = async (values: SettleConfigModel) => {

    if (!distributorSettleConfigVO) return;
    
    setFormLoading(true);
    try {
      const params = {
        principalType: distributorSettleConfigVO.principalType,
        principalId: distributorSettleConfigVO.principalId,
        settleConfig: {
          ...values,
          // 百分比转小数
          newOrderCommRate: percentToDecimal(values.newOrderCommRate),
          renewOrderCommRate: percentToDecimal(values.renewOrderCommRate),
          initYrRate: percentToDecimal(values.initYrRate),
          subseqYrsRate: percentToDecimal(values.subseqYrsRate),
        },
      } as any;
      
      const res = await modifyDistributorSettlementConfig(params);
      if (res) {
        message.success('结算方案保存成功');
        onClose();
        onSuccess?.();
      } else {
        message.error('结算方案保存失败');
      }
    } catch (error) {
      console.error('保存结算方案出错:', error);
      message.error('结算方案保存失败');
    } finally {
      setFormLoading(false);
    }
  };

  return (
    <Drawer
      title={`结算方案 - ${distributorSettleConfigVO?.principalName || '未知用户'}`}
      placement="right"
      onClose={onClose}
      open={visible}
      width={'50%'}
      footer={
        <div style={{ textAlign: 'right' }}>
          <Button onClick={onClose} style={{ marginRight: 8 }}>
            取消
          </Button>
          <Button
            type="primary"
            onClick={() => formRef.submit()}
            loading={formLoading}
          >
            保存
          </Button>
        </div>
      }
    >
      {drawerLoading ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin />
        </div>
      ) : distributorSettleConfigVO ? (
        <Flex vertical gap={16}>
          {/* 数据展示层（顶部区块） */}
          <div>
            <Typography.Title level={5}>基本信息</Typography.Title>
            <ProDescriptions column={2} bordered dataSource={distributorSettleConfigVO}>
              <ProDescriptions.Item label="主组织ID" dataIndex="masterCorpId" />
              <ProDescriptions.Item label="主组织名称" dataIndex="masterCorpName" />
              <ProDescriptions.Item
                label="类型"
                dataIndex="salesType"
                render={(_, record) => (
                  <span
                    style={{ display: 'inline-flex', alignItems: 'center' }}
                  >
                    <ColorPoint color={salesTypeMap[record.salesType].color} />
                    {salesTypeMap[record.salesType].desc}
                  </span>
                )}
              />
              <ProDescriptions.Item label="主账号ID" dataIndex="channelAdminId" />
              <ProDescriptions.Item label="主账号昵称" dataIndex="channelAdminNickName" />
              <ProDescriptions.Item
                label="用户ID"
                render={(_, record) => {
                  let userId;
                  switch (record.principalType) {
                    case 'CORP':
                      userId = record.channelAdminId;
                      break;
                    case 'SUB_CORP':
                      userId = record.subChannelAdminId;
                      break;
                    default :
                      userId = record.principalId;
                      break;
                  }
                  return (
                    <span>{userId}</span>
                  )
                }}
              />
              <ProDescriptions.Item
                label="用户昵称"
                render={(_, record) => {
                  let userName;
                  switch (record.principalType) {
                    case 'CORP':
                      userName = record.channelAdminNickName;
                      break;
                    case 'SUB_CORP':
                      userName = record.subChannelAdminNickName;
                      break;
                    default :
                      userName = record.principalName;
                      break;
                  }
                  return (
                    <span>{userName}</span>
                  )
                }}
              />
              {distributorSettleConfigVO.subCorpId && (
                <>
                  <ProDescriptions.Item
                    label="子组织ID"
                    dataIndex="subCorpId"
                  />
                  <ProDescriptions.Item
                    label="子组织名称"
                    dataIndex="subCorpName"
                  />
                </>
              )}
              <ProDescriptions.Item
                label="组织内角色"
                dataIndex="customRole"
                render={(_, record) => (
                  <span
                    style={{ display: 'inline-flex', alignItems: 'center' }}
                  >
                    {record.customRole === 'CHANNEL_ADMIN' &&
                      <ColorPoint color="red" />
                    }
                    {CustomRoleMap[record.customRole]}
                  </span>
                )}
              />
            </ProDescriptions>
          </div>

          {/* 表单交互层（中间区块） */}
          <div>
            <Typography.Title level={5}>结算方案配置</Typography.Title>
            <ProForm
              disabled
              className="readonly-form"
              form={formRef}
              onFinish={handleSubmitSettleConfig}
              submitter={false}
              layout="horizontal"
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 16 }}
              initialValues={distributorSettleConfigVO?.settleConfig}
            >
              <ProFormSelect
                name="settleType"
                label="结算类型"
                rules={[{ required: true, message: '请选择结算类型' }]}
                placeholder="请选择结算类型"
                width={'sm'}
                options={Object.entries(SettleTypeMap).map(([key, value]) => ({label: value, value: key}))}
              />
              <ProFormDigit
                name="calculationDate"
                label="结算时间"
                rules={[{ required: true, message: '请输入结算时间' }]}
                min={0}
                max={27}
                addonAfter="日"
                fieldProps={{
                  precision: 0,
                  onFocus: () => setShowCalculationDateHelp(true),
                  onBlur: () => setShowCalculationDateHelp(false)
                }}
                tooltip={"例: 27 代表结算周期为上月28号(包含)到本月27号(包含), 0 代表结算周期为本月1号到本月月底"}
                help={
                  <div style={{ minHeight: '24px', lineHeight: '24px' }}>
                    {showCalculationDateHelp && (
                      <span style={{ color: '#ff7a00', padding: '4px 8px'}}>
                        可选范围 0 ~ 27，0 代表每月月底结算
                      </span>
                    )}
                  </div>
                }
              />
              <ProFormDigit
                name="newOrderCommRate"
                label="新订单费率(%)"
                rules={[{ required: true, message: '请输入新订单费率' }]}
                min={0}
                max={100}
                fieldProps={{ precision: 2 }}
                addonAfter="%"
              />
              <ProFormDigit
                name="renewOrderCommRate"
                label="续费订单费率(%)"
                rules={[{ required: true, message: '请输入续费订单费率' }]}
                min={0}
                max={100}
                fieldProps={{ precision: 2 }}
                addonAfter="%"
              />
              <ProFormSwitch
                name="hasBeneficiary"
                label="是否被主账号抽成"
                checkedChildren="是"
                unCheckedChildren="否"
              />
              <ProForm.Item noStyle shouldUpdate>
                {({ getFieldValue }) => {
                  const hasBeneficiary = getFieldValue('hasBeneficiary');
                  return hasBeneficiary ? (
                    <>
                      <ProFormSelect
                        name="beneficiaryTargetId"
                        label="抽成对象"
                        rules={[
                          { required: true, message: '请选择抽成对象' },
                        ]}
                        // disabled={distributorSettleConfigVO.customRole !== 'CHANNEL_ADMIN'}
                        placeholder="请选择抽成对象"
                        width={'sm'}
                        options={
                        allOrganizations.filter(org => org.salesType === 'DIRECT')
                          .map(org => ({label: org.channelAdminNickName, value: org.channelAdminId}))
                      }
                      />
                      <ProFormDigit
                        name="initYrRate"
                        label="首年抽成费率(%)"
                        rules={[
                          { required: true, message: '请输入新订单抽成费率' },
                        ]}
                        min={0}
                        max={100}
                        fieldProps={{ precision: 2 }}
                        addonAfter="%"
                      />
                      <ProFormDigit
                        name="subseqYrsRate"
                        label="非首年抽成费率(%)"
                        rules={[
                          { required: true, message: '请输入续费订单抽成费率' },
                        ]}
                        min={0}
                        max={100}
                        fieldProps={{ precision: 2 }}
                        addonAfter="%"
                      />
                    </>
                  ) : null;
                }}
              </ProForm.Item>
            </ProForm>
          </div>

          {/* 预设方案层（底部区块） */}
          <div>
            <Typography.Title level={5}>预设方案</Typography.Title>
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
                gap: '16px',
              }}
            >
              {defaultSettleConfigs.map((preset, index) => (
                <Tooltip title={'点击应用'}>
                  <ProCard
                    key={index}
                    title={<span style={{color: '#28a2ed'}} >{preset.memo}</span>}
                    bordered
                    hoverable
                    onClick={() => applyPresetConfig(preset)}
                  >
                    <div>
                      费率: 新单{preset.newOrderCommRate}% / 续费
                      {preset.renewOrderCommRate}%
                    </div>
                    {preset.hasBeneficiary && (
                      <div>
                        抽成: 首年{preset.initYrRate}% / 非首年
                        {preset.subseqYrsRate}%
                      </div>
                    )}
                    <div>直营: {preset.salesType ? '是' : '否'}</div>
                  </ProCard>
                </Tooltip>
              ))}
            </div>
          </div>

          <Divider />
          
        </Flex>
      ) : (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Typography.Text type="secondary">暂无结算方案数据</Typography.Text>
        </div>
      )}
    </Drawer>
  );
};

export default SettlementConfigDrawer;


export const ColorPoint = ({ color }) => (
  <span
    style={{
      display: 'inline-block',
      width: '6px',
      height: '6px',
      borderRadius: '50%',
      backgroundColor: color || 'transparent',
      marginRight: '8px',
      verticalAlign: 'middle',
    }}
  />
);