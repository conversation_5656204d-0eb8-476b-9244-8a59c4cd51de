import {
  ProDescriptions,
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { Button, Modal, Space, message } from 'antd';
import { useRef, useState } from 'react';
import styles from './index.less';
import { manualSettle } from '@/services/FinanceController';

// 手动结算
export default (props) => {
  //渠道商结算 settleData: DistributorSettlementVO
  const { onSuccess, settleData, isButton = true } = props;

  const [isModalOpen, setIsModalOpen] = useState(false);
  const formRef = useRef<ProFormInstance>();
  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    // @ts-ignore
    formRef.current.submit();
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      {isButton ? (
        <Button type="primary" onClick={showModal}>
          确定结算
        </Button>
      ) : (
        <a onClick={showModal}>手动结算</a>
      )}

      <Modal
        title={'手动结算'}
        open={isModalOpen}
        width={'500px'}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnClose={true}
        className={styles.modal}
      >
        <ProForm
          submitter={false}
          preserve={false}
          layout="horizontal"
          labelCol={{ span: 5 }}
          formRef={formRef}
          onFinish={async (value) => {
            manualSettle(settleData.id, value.outBizNo).then((result) => {
              if (result) {
                message.success('手工结算成功');
                onSuccess();
              }
            });
          }}
        >
          <ProFormText
            width="lg"
            label="销售/渠道商"
            name="distributorCorpName"
            initialValue={settleData.distributorCorpName}
            readonly
          />
          <ProFormText
            width="lg"
            label="总金额"
            name="totalAmount"
            initialValue={`${settleData.totalAmount}元`}
            readonly
          />

          <ProFormText
            width="lg"
            label="结算金额"
            name="settleAmount"
            initialValue={`${settleData.settleAmount}元`}
            readonly
          />
          <ProFormText
            width="lg"
            label="流水单号"
            name="outBizNo"
            placeholder="请输入银行卡流水单号"
            rules={[
              { required: true },
              {
                pattern: /^[a-zA-Z0-9]{6,32}$/,
                message: '请输入6-32位流水单号',
              },
            ]}
          />
        </ProForm>
      </Modal>
    </>
  );
};
