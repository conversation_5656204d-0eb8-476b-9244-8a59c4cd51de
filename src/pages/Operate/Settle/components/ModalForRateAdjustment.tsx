import type { ProFormInstance } from '@ant-design/pro-components';
import {
  ProForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormText,
} from '@ant-design/pro-components';
import { Button, Modal, Space, message } from 'antd';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import { updateServiceRate } from '@/services/FinanceController';

export default (props) => {
  const { onSuccess } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const formRef = useRef<ProFormInstance>();

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    // @ts-ignore
    formRef.current.submit();
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const disabledDate = (current) => {
    return current < dayjs().subtract(0, 'days');
  };

  return (
    <>
      <Button onClick={showModal}>调整费率</Button>
      <Modal
        title="调整费率"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnClose={true}
      >
        <ProForm
          submitter={false}
          preserve={false}
          layout="horizontal"
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 16 }}
          formRef={formRef}
          onFinish={async (value) => {
            console.log('onfinish:', props);

            updateServiceRate({
              distributorCorpId: props.distributorCorpId,
              serviceRate: value.newRate / 100,
              effectTime: dayjs(value.date).format('YYYYMMDD'), //value.date,
            }).then((result) => {
              if (result) {
                message.success('费率调整成功');
                setIsModalOpen(false);
                onSuccess();
              }
            });
          }}
        >
          <ProFormText
            width="lg"
            label="当前费率"
            name="currentRate"
            initialValue={ props.rateValue ? `${props.rateValue}%` : '未设置'}
            readonly
          />
          <ProFormText
            width="lg"
            label="调整为"
            name="newRate"
            placeholder={'请输入0.00-100.00之间的数字'}
            fieldProps={{
              addonAfter: '%',
            }}
            rules={[
              { required: true, message: '请输入费率' },
              {
                pattern: /^100$|^(?:\d|[1-9]\d)(\.(\d){0,2})?$/,
                message: '请输入0.00-100.00之间的数字',
              },
            ]}
          />
          <ProFormDatePicker
            name="date"
            width="lg"
            label="日期"
            hidden={true}
            extra="在此时间之前的订单不受影响"
            fieldProps={{
              format: (value) => value.format('YYYYMMDD'),
              disabledDate: disabledDate,
              showToday: false,
            }}
            rules={[{ required: false, message: '请选择日期' }]}
          />
        </ProForm>
      </Modal>
    </>
  );
};
