import React, { useState, useEffect } from 'react';
import { Drawer, Button, Form, message, Space, Spin, Typography, Flex, Tooltip, Divider } from 'antd';
import { ProDescriptions, ProCard, ProForm, ProFormSelect, ProFormDatePicker, ProFormDigit, ProFormSwitch } from '@ant-design/pro-components';
import {
  modifyAssessmentPlan,
  AssessmentPlanVO,
  salesAssessTypeMap,
  DistributorEnhancedVO, assessStatusMap,
} from '@/services/AssessmentController';
import { convertToWan, convertToYuan } from '@/services/SettlementController';
import moment from 'moment';
import { salesTypeMap } from '@/services/OrganizationController';
import { CustomRoleMap } from '@/services/UserController';
import { ColorPoint } from './SettlementConfigDrawer';
import '@/pages/Operate/Settle/components/index.less'

interface AssessmentModifyDrawerProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  distributorEnhancedVO: DistributorEnhancedVO | null;
  defaultAssessmentConfigs: AssessmentPlanVO[];
}

const AssessmentConfigDrawer: React.FC<AssessmentModifyDrawerProps> = ({
  visible,
  onClose,
  onSuccess,
  distributorEnhancedVO,
  defaultAssessmentConfigs,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);

  // 当抽屉打开且有考核计划时，初始化表单数据
  useEffect(() => {
    // 设置表单初始值
    if (visible && distributorEnhancedVO && distributorEnhancedVO.currentPlan) {
      form.setFieldsValue({
        id: distributorEnhancedVO?.currentPlan?.id || undefined,
        principalType: distributorEnhancedVO.principalType,
        principalId: distributorEnhancedVO.principalId,
        type: distributorEnhancedVO?.currentPlan?.type || undefined,
        planFromDate: distributorEnhancedVO.currentPlan?.planFromDate ? moment(distributorEnhancedVO.currentPlan.planFromDate) : null,
        planEndDate: distributorEnhancedVO.currentPlan?.planEndDate ? moment(distributorEnhancedVO.currentPlan.planEndDate) : null,
        totalSalesAmount: convertToWan(distributorEnhancedVO.currentPlan.kpiTarget?.totalSalesAmount) || 0,
        totalSalesCount: distributorEnhancedVO.currentPlan.kpiTarget?.totalSalesCount || 0,
        enable: distributorEnhancedVO?.currentPlan?.status ? distributorEnhancedVO.currentPlan.status !== assessStatusMap.ASSESS_INIT.code : false,
      });
    } else {
      form.setFieldsValue({
        enable: true,
      });
    }
  }, [visible, distributorEnhancedVO, form]);

  // 应用预设方案
  const applyPresetConfig = (preset: AssessmentPlanVO) => {
    form.setFieldsValue({
      type: preset.type,
      totalSalesAmount: preset.kpiTarget?.totalSalesAmount || 0,
      totalSalesCount: preset.kpiTarget?.totalSalesCount || 0,
      enable: true,
    });
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);
      
      // 构建请求参数
      const params = {
        id: distributorEnhancedVO?.currentPlan?.id,
        principalType: distributorEnhancedVO?.principalType,
        principalId: distributorEnhancedVO?.principalId,
        type: values.type,
        status: values.enable ? assessStatusMap.ASSESS_WAITING.code  : assessStatusMap.ASSESS_INIT.code,
        planFromDate: values.planFromDate ? values.planFromDate.format('YYYY-MM-DD') : null,
        planEndDate: values.planEndDate ? values.planEndDate.format('YYYY-MM-DD') : null,
        kpiTarget: {
          totalSalesAmount: convertToYuan(values.totalSalesAmount),
          totalSalesCount: values.totalSalesCount,
        }
      };
      
      // 调用接口
      const res = await modifyAssessmentPlan(params);
      
      if (res) {
        message.success('保存考核计划成功');
        onSuccess();
        onClose();
      } else {
        message.error('保存考核计划失败');
      }
    } catch (error) {
      console.error('提交考核计划出错:', error);
      message.error('提交表单出错，请检查表单数据');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Drawer
      title={`考核计划 - ${distributorEnhancedVO?.principalName || '未知用户'}`}
      placement="right"
      onClose={onClose}
      open={visible}
      width={"50%"}
      footer={
        <div style={{ textAlign: 'right' }}>
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" loading={submitting} onClick={handleSubmit}>
              保存
            </Button>
          </Space>
        </div>
      }
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin />
        </div>
      ) : distributorEnhancedVO ? (
        <Flex vertical gap={16}>
          {/* 数据展示层（顶部区块） */}
          <div>
            <Typography.Title level={5}>基本信息</Typography.Title>
            <ProDescriptions column={2} bordered dataSource={distributorEnhancedVO}>
              <ProDescriptions.Item label="主组织ID" dataIndex="masterCorpId" />
              <ProDescriptions.Item
                label="主组织名称"
                dataIndex="masterCorpName"
              />
              <ProDescriptions.Item
                label="类型"
                dataIndex="salesType"
                render={(_, record) => (
                  <span
                    style={{ display: 'inline-flex', alignItems: 'center' }}
                  >
                    <ColorPoint color={salesTypeMap[record.salesType].color} />
                    {salesTypeMap[record.salesType].desc}
                  </span>
                )}
              />
              <ProDescriptions.Item label="主账号ID" dataIndex="channelAdminId" />
              <ProDescriptions.Item
                label="主账号昵称"
                dataIndex="channelAdminNickName"
              />
              <ProDescriptions.Item
                label="用户ID"
                render={(_, record) => {
                  let userId;
                  switch (record.principalType) {
                    case 'CORP':
                      userId = record.channelAdminId;
                      break;
                    case 'SUB_CORP':
                      userId = record.subChannelAdminId;
                      break;
                    default :
                      userId = record.principalId;
                      break;
                  }
                  return (
                    <span>{userId}</span>
                  )
                }}
              />
              <ProDescriptions.Item
                label="用户昵称"
                render={(_, record) => {
                  let userName;
                  switch (record.principalType) {
                    case 'CORP':
                      userName = record.channelAdminNickName;
                      break;
                    case 'SUB_CORP':
                      userName = record.subChannelAdminNickName;
                      break;
                    default :
                      userName = record.principalName;
                      break;
                  }
                  return (
                    <span>{userName}</span>
                  )
                }}
              />
              {distributorEnhancedVO.subCorpId && (
                <>
                  <ProDescriptions.Item
                    label="子组织ID"
                    dataIndex="subCorpId"
                  />
                  <ProDescriptions.Item
                    label="子组织名称"
                    dataIndex="subCorpName"
                  />
                </>
              )}
              <ProDescriptions.Item
                label="组织内角色"
                dataIndex="customRole"
                render={(_, record) => (
                  <span
                    style={{ display: 'inline-flex', alignItems: 'center' }}
                  >
                    {record.customRole === 'CHANNEL_ADMIN' &&
                      <ColorPoint color="red" />
                    }
                    {CustomRoleMap[record.customRole]}
                  </span>
                )}
              />
            </ProDescriptions>
          </div>

          {/* 表单交互层（中间区块） */}
          <div>
            <Typography.Title level={5}>考核方案配置</Typography.Title>
            <ProForm
              disabled
              className='readonly-form'
              form={form}
              submitter={false}
              layout="horizontal"
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 16 }}
            >

              <ProFormSelect
                name="type"
                label="考核类型"
                rules={[{ required: true, message: '请选择考核类型' }]}
                options={Object.entries(salesAssessTypeMap).map(([key, value]) => ({
                  label: value,
                  value: key,
                }))}
                placeholder="请选择考核类型"
                width={'sm'}
              />
              
              <ProFormDatePicker
                disabled={false}
                name="planFromDate"
                label="考核开始日期"
                rules={[{ required: true, message: '请选择考核开始日期' }]}
                fieldProps={{
                  format: 'YYYY-MM-DD',
                }}
                width={'sm'}
              />
              
              <ProFormDatePicker
                disabled={false}
                name="planEndDate"
                label="考核结束日期"
                rules={[{ required: true, message: '请选择考核结束日期' }]}
                fieldProps={{
                  format: 'YYYY-MM-DD',
                }}
                width={'sm'}
              />
              
              <ProFormDigit
                name="totalSalesAmount"
                label="销售金额目标(万)"
                rules={[{ required: true, message: '请输入销售金额目标' }]}
                fieldProps={{
                  precision: 2,
                  step: 1,
                }}
                min={0}
                width={'sm'}
              />
              
              <ProFormDigit
                name="totalSalesCount"
                label="销售订单数目标"
                rules={[{ required: true, message: '请输入销售订单数目标' }]}
                min={0}
                fieldProps={{
                  precision: 0,
                  step: 1,
                }}
                width={'sm'}
              />

              <ProFormSwitch
                name="enable"
                label="启用考核"
                checkedChildren="启用"
                unCheckedChildren="禁用"
                disabled={false}
              />
            </ProForm>
          </div>

          {/* 预设方案层（底部区块） */}
          <div>
            <Typography.Title level={5}>预设方案</Typography.Title>
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
                gap: '16px',
              }}
            >
              {defaultAssessmentConfigs.map((preset, index) => (
                <Tooltip title={'点击应用'} key={index}>
                  <ProCard
                    title={<span style={{color: '#28a2ed'}} >{preset.memo || '默认方案'}</span>}
                    bordered
                    hoverable
                    onClick={() => applyPresetConfig(preset)}
                  >
                    <div>
                      销售金额目标: {preset.kpiTarget?.totalSalesAmount || 0}万
                    </div>
                    <div>
                      销售订单数目标: {preset.kpiTarget?.totalSalesCount || 0}
                    </div>
                  </ProCard>
                </Tooltip>
              ))}
            </div>
          </div>

          <Divider />

        </Flex>
      ) : (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Typography.Text type="secondary">暂无考核计划数据</Typography.Text>
        </div>
      )}
    </Drawer>
  );
};

export default AssessmentConfigDrawer;
