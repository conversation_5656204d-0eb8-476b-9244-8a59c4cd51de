import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Spin, Table, Empty, Space, Tag, Typography } from 'antd';
import {
  queryAssessmentPlanByPage,
  AssessmentPlanVO,
  assessStatusMap,
  PrincipalModel, salesAssessTypeMap,
} from '@/services/AssessmentController';
import mainStyles from '@/pages/Operate/Settle/index.less';
import { ColorPoint } from '@/pages/Operate/Settle/components/SettlementConfigDrawer';
import { convertToWan } from '@/services/SettlementController';

interface AssessmentDetailDrawerProps {
  visible: boolean;
  onClose: () => void;
  principal: PrincipalModel | null;
}

const AssessmentDetailDrawer: React.FC<AssessmentDetailDrawerProps> = ({
  visible,
  onClose,
  principal
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [assessmentPlans, setAssessmentPlans] = useState<AssessmentPlanVO[]>([]);

  // 分页参数
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取考核记录数据
  const fetchAssessmentPlans = async () => {
    if (!principal) return;
    
    setLoading(true);
    try {
      const res = await queryAssessmentPlanByPage({
        principalType: principal.type,
        principalId: principal.id,
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        orderBy: 'create_time desc'
      });
      
      if (res) {
        // 适配接口返回数据
        // 如果res是数组，则直接使用，否则尝试获取res.list
        const planList = Array.isArray(res) ? res : (res as any).list || [];
        const total = Array.isArray(res) ? planList.length : (res as any).total || 0;
        
        setAssessmentPlans(planList);
        setPagination(prev => ({
          ...prev,
          total: total,
        }));
      }
    } catch (error) {
      console.error('获取考核记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 当抽屉打开或userId变化时获取数据
  useEffect(() => {
    if (visible && principal) {
      fetchAssessmentPlans();
    }
  }, [visible, principal, pagination.current, pagination.pageSize]);

  // 表格列定义
  const columns = [
    {
      title: '考核ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '考核类型',
      dataIndex: 'type',
      key: 'type',
      render: (_, record) => {
        return salesAssessTypeMap[record.type]
      }
    },
    {
      title: '目标业绩',
      key: 'kpiTarget',
      render: (_, record) => {
        const totalSalesAmount =
          record.kpiTarget?.totalSalesAmount;
        const totalSalesCount =
          record.kpiTarget?.totalSalesCount || 0;
        return (
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <span style={{ lineHeight: '23px' }}>
              {convertToWan(totalSalesAmount).toFixed(2)} 万元
            </span>
            <span style={{ lineHeight: '23px' }}>{totalSalesCount} 单</span>
          </div>
        );
      },
    },
    {
      title: '实际业绩',
      key: 'kpiActual',
      render: (_, record) => {
        const totalSalesAmount =
          record.kpiActual?.totalSalesAmount;
        const totalSalesCount =
          record.kpiActual?.totalSalesCount || 0;
        return (
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <span style={{ lineHeight: '23px' }}>
              {convertToWan(totalSalesAmount).toFixed(2)} 万元
            </span>
            <span style={{ lineHeight: '23px' }}>{totalSalesCount} 单</span>
          </div>
        );
      },
    },
    {
      title: '考核周期',
      key: 'assessPeriod',
      render: (_, record) => {
        if (record.customRole === 'OPS_MEMBER') {
          return <>-</>;
        }
        if (!record.planFromDate) {
          return <span style={{ color: 'red' }}>数据异常</span>;
        }

        const startDate = record.planFromDate;
        const endDate = record.planEndDate;

        if (!endDate) return startDate;
        return (
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <span style={{ lineHeight: '23px' }}>{startDate}/</span>
            <span style={{ lineHeight: '23px' }}>{endDate}</span>
          </div>
        );
      },
    },
    {
      title: '考核状态',
      key: 'assessStatus',
      render: (_, record) => {
        if (!record.type) {
          return <span style={{ color: 'red' }}>数据异常</span>;
        }
        const statusInfo = assessStatusMap[record.status];
        return (
          <span style={{ display: 'inline-flex', alignItems: 'center' }}>
            <ColorPoint color={statusInfo?.color} />
            {statusInfo?.desc || record.status}
          </span>
        );
      },
    },
  ];

  // 表格分页变化处理
  const handleTableChange = (newPagination) => {
    setPagination({
      current: newPagination.current,
      pageSize: newPagination.pageSize,
      total: pagination.total,
    });
  };

  return (
    <Drawer
      title={`考核记录 - ${principal?.name || '未知用户'}`}
      placement="right"
      onClose={onClose}
      open={visible}
      width={'80%'}
      footer={
        <div style={{ textAlign: 'right' }}>
          <Button onClick={onClose}>
            关闭
          </Button>
        </div>
      }
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin />
        </div>
      ) : assessmentPlans.length > 0 ? (
        <Table
          className={mainStyles.table}
          rowKey="id"
          columns={columns}
          dataSource={assessmentPlans}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          onChange={handleTableChange}
        />
      ) : (
        <Empty 
          description="暂无考核记录"
          image={Empty.PRESENTED_IMAGE_SIMPLE} 
          style={{ margin: '100px 0' }} 
        />
      )}
    </Drawer>
  );
};

export default AssessmentDetailDrawer;
