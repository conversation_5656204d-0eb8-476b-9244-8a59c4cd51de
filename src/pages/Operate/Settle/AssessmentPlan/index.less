.container {
    padding: 16px;
}

.pageHeader {
  margin-bottom: 16px;
}

.searchCard {
  margin-bottom: 16px;
}

.filterContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.filterItem {
  max-width: 400px;
  min-width: 200px;
  flex: 1;
}

.mainContent {
  display: flex;
  margin-bottom: 16px;
}

.treeCard {
  width: 260px;
  margin-right: 16px;
}

.treeContainer {
  max-height: 600px;
  overflow-y: auto;
}

@media screen and (max-width: 768px) {
  .filterItem {
    min-width: 100%;
  }

  .mainContent {
    flex-direction: column;
  }

  .treeCard {
    width: 100%;
    margin-right: 0;
    margin-bottom: 16px;
  }
}


// 操作按钮样式
.actionButton {
  font-size: 13px !important;
  font-family: "PingFang SC", serif !important;
  font-weight: lighter !important;
  height: 24px !important;
  line-height: 24px !important;
  padding: 0 8px !important;
}