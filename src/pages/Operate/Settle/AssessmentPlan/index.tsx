import React, { useEffect, useState, useMemo } from 'react';
import { Card, Table, Input, Button, Space, message, Select, Progress, Flex, Tooltip, Cascader } from 'antd';
import {
  queryRootCorpByPage,
  OrganizationVO,
  salesTypeMap,
} from '@/services/OrganizationController';
import styles from '@/pages/Operate/Settle/AssessmentPlan/index.less';
import mainStyles from '@/pages/Operate/Settle/index.less';
import {
  getAllDistributorAssessment,
  DistributorEnhancedVO,
  salesAssessTypeMap,
  assessStatusMap,
  AssessmentPlanVO,
  DEFAULT_ASSESSMENT_CONFIGS, PrincipalModel, manualRefresh,
} from '@/services/AssessmentController';
import { CustomRoleMap } from '@/services/UserController';
import AssessmentDetailDrawer from '../components/AssessmentDetailDrawer';
import AssessmentConfigDrawer from '../components/AssessmentConfigDrawer';
import SettlementConfigDrawer, { ColorPoint } from '../components/SettlementConfigDrawer';
import {
  convertToWan,
  decimalToPercent,
  DEFAULT_SETTLEMENT_CONFIGS,
  DistributorOrgTree, getDistributorOrgTrees,
  SettleConfigModel,
  truncateToDecimalsString,
} from '@/services/SettlementController';
import { queryConfigByKeys } from '@/services/SystemController';
import AssessmentReviewDrawer from '@/pages/Operate/Settle/components/AssessmentReviewDrawer';

// 表格分页参数
interface TablePagination {
  current: number;
  pageSize: number;
  total: number;
  showSizeChanger: boolean;
  showQuickJumper: boolean;
}

const AssessmentPlan: React.FC = () => {
  const [allOrganizations, setAllOrganizations] = useState<OrganizationVO[]>(
    [],
  );

  // 考核数据
  const [allAssessments, setAllAssessments] = useState<DistributorEnhancedVO[]>(
    [],
  );
  const [dataLoading, setDataLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  // 前端分页状态
  const [pagination, setPagination] = useState<TablePagination>({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
  });
  // 过滤条件
  const [filterState, setFilterState] = useState({
    salesType: '',
    orgId: 0,
    userId: 0,
    assessStatus: '',
  });
  // 组织树
  const [orgTrees, setOrgTrees] = useState<DistributorOrgTree[]>([]);
  // 多级筛选状态
  const [selectedOrg, setSelectedOrg] = useState<number[]>([]);
  const [filteredOrgTrees, setFilteredOrgTrees] = useState<
    DistributorOrgTree[]
  >([]);

  // 抽屉相关状态
  const [drawerVisible, setDrawerVisible] = useState<boolean>(false);
  const [selectPrincipal, setSelectPrincipal] = useState<PrincipalModel | null>(
    null,
  );

  // 编辑抽屉相关状态
  const [assessmentDrawerVisible, setAssessmentDrawerVisible] =
    useState<boolean>(false);
  const [selectedAssessment, setSelectedAssessment] =
    useState<DistributorEnhancedVO | null>(null);

  // 结算方案抽屉状态
  const [settlementDrawerVisible, setSettlementDrawerVisible] =
    useState<boolean>(false);
  const [selectedSettlement, setSelectedSettlement] =
    useState<DistributorEnhancedVO | null>(null);
  const [defaultSettleConfigs, setDefaultSettleConfigs] = useState<
    SettleConfigModel[]
  >([]);
  const [defaultAssessmentConfigs, setDefaultAssessmentConfigs] = useState<
    AssessmentPlanVO[]
  >([]);
  const [drawerLoading, setDrawerLoading] = useState<boolean>(false);

  // 结算结果 review 抽屉状态
  const [reviewDrawerVisible, setReviewDrawerVisible] =
    useState<boolean>(false);

  // 初始化加载数据
  useEffect(() => {
    fetchOrganizations();
    fetchOrgTree();
    fetchData();
    fetchDefaultSettleConfigs();
  }, []);

  // 获取所有考核数据
  const fetchData = async () => {
    setDataLoading(true);
    try {
      const res = await getAllDistributorAssessment();
      if (res) {
        setAllAssessments(res);
      }
    } catch (error) {
      message.error('获取考核数据失败');
      console.error('Failed to fetch assessments:', error);
    } finally {
      setDataLoading(false);
    }
  };

  // 手动刷新绩效
  const refresh = async () => {
    try {
      setRefreshing(true);
      const result = await manualRefresh();
      if (result) {
        message.success('手动刷新成功');
        fetchData();
      } else {
        message.error('手动刷新失败');
      }
    } catch (error) {
      message.error('手动刷新失败');
    } finally {
      setRefreshing(false);
    }
  };

  /**
   * 解析系统配置的JSON字符串
   * @param jsonString 双重转义的JSON字符串
   * @param processor 可选的数据处理函数
   * @returns 解析后的JavaScript对象，解析失败则返回空数组
   */
  const parseConfigJson = (jsonString: string, processor?: any) => {
    try {
      // 处理双重转义的JSON字符串
      const jsonStr = jsonString.replace(/\\\"/g, '"');
      // 解析JSON字符串为JavaScript对象
      const jsonObj = JSON.parse(jsonStr);

      if (Array.isArray(jsonObj)) {
        return processor ? jsonObj.map(processor) : jsonObj;
      } else {
        console.error('Config is not an array:', jsonObj);
        return [];
      }
    } catch (parseError) {
      console.error('Failed to parse config JSON:', parseError);
      return [];
    }
  };

  // 获取预设配置
  const fetchDefaultSettleConfigs = async () => {
    try {
      const res = await queryConfigByKeys([
        DEFAULT_SETTLEMENT_CONFIGS,
        DEFAULT_ASSESSMENT_CONFIGS,
      ]);

      // 处理结算方案配置
      if (res && res[DEFAULT_SETTLEMENT_CONFIGS]) {
        const settleProcessor = (preset) => ({
          ...preset,
          newOrderCommRate: decimalToPercent(preset.newOrderCommRate),
          renewOrderCommRate: decimalToPercent(preset.renewOrderCommRate),
          initYrRate: decimalToPercent(preset.initYrRate),
          subseqYrsRate: decimalToPercent(preset.subseqYrsRate),
        });

        setDefaultSettleConfigs(
          parseConfigJson(res[DEFAULT_SETTLEMENT_CONFIGS], settleProcessor),
        );
      }

      // 处理考核方案配置
      if (res && res[DEFAULT_ASSESSMENT_CONFIGS]) {
        const assessmentProcessor = (preset) => ({
          ...preset,
          kpiTarget: {
            totalSalesAmount: convertToWan(preset.kpiTarget.totalSalesAmount),
            totalSalesCount: preset.kpiTarget.totalSalesCount,
          },
        });
        setDefaultAssessmentConfigs(
          parseConfigJson(res[DEFAULT_ASSESSMENT_CONFIGS], assessmentProcessor),
        );
      }
    } catch (error) {
      console.error('获取默认配置失败:', error);
      setDefaultSettleConfigs([]);
      setDefaultAssessmentConfigs([]);
    }
  };

  // 获取所有组织数据
  const fetchOrganizations = async () => {
    try {
      const res = await queryRootCorpByPage({
        pageNum: 1,
        pageSize: 1000, // 假设组织总数不会太多
      });

      if (res && res.list) {
        setAllOrganizations(res.list);
      }
    } catch (error) {
      message.error('获取组织列表失败');
      console.error('Failed to fetch organizations:', error);
    } finally {
    }
  };

  const fetchOrgTree = async () => {
    getDistributorOrgTrees().then((res) => {
      if (res && Array.isArray(res)) {
        setOrgTrees(res);
        setFilteredOrgTrees(res); // 初始时显示所有组织
        console.log('orgTrees', res);
      }
    });
  };

  // 根据销售类型过滤组织树
  const filterOrgTreesBySalesType = ( trees: DistributorOrgTree[], salesType: string ): DistributorOrgTree[] => {
    if (!salesType) {
      return trees;
    }
    return trees
      .filter((tree) => {
        // 递归过滤子组织
        const filteredChildren = tree.children
          ? filterOrgTreesBySalesType(tree.children, salesType)
          : [];
        // 如果当前组织匹配销售类型，或者有匹配的子组织，就保留
        return tree.salesType === salesType || filteredChildren.length > 0;
      })
      .map((tree) => ({
        ...tree,
        children: tree.children
          ? filterOrgTreesBySalesType(tree.children, salesType)
          : [],
      }));
  };

  // 监听销售类型变化，更新组织树
  useEffect(() => {
    if (orgTrees.length > 0) {
      const filteredTrees = filterOrgTreesBySalesType(
        orgTrees,
        filterState.salesType,
      );
      setFilteredOrgTrees(filteredTrees);
      // 清空已选择的组织（因为可能不在新的过滤结果中）
      setSelectedOrg([]);
    }
  }, [filterState.salesType, orgTrees]);

  // 将组织树转换为Cascader需要的格式
  const convertToCascaderOptions = (trees: DistributorOrgTree[]): any[] => {
    return trees.map((tree) => {
      const options: any[] = [];
      // 首先添加子组织（children）
      if (tree.children && tree.children.length > 0) {
        const childrenOptions = convertToCascaderOptions(tree.children).map(
          (child) => ({
            ...child,
            label: `🏢${child.label}`, // 为子组织添加图标
            isOrg: true,
          }),
        );
        options.push(...childrenOptions);
      }

      // 然后添加成员（memberMap中除了OPS_MEMBER的成员）
      if (tree.memberMap) {
        const memberOptions: any[] = [];
        // 遍历memberMap，过滤掉OPS_MEMBER
        // memberMap可能是Map对象或普通对象
        const memberMapEntries =
          tree.memberMap instanceof Map
            ? Array.from(tree.memberMap.entries())
            : Object.entries(tree.memberMap);

        for (const [role, members] of memberMapEntries) {
          if (
            // role !== 'OPS_MEMBER' &&
            members &&
            Array.isArray(members) &&
            members.length > 0
          ) {
            members.forEach((member) => {
              memberOptions.push({
                value: `user_${member.userId}`, // 用前缀区分用户和组织
                label: `👤${member.nickName} (${CustomRoleMap[role]})`,
                isMember: true,
                userId: member.userId,
                role: role,
              });
            });
          }
        }
        options.push(...memberOptions);
      }

      return {
        value: tree.orgId,
        label: tree.orgName,
        isOrg: true,
        children: options.length > 0 ? options : undefined,
      };
    });
  };

  // 处理多级筛选变化
  const handleOrgCascaderChange = ( value: (number | string)[], selectedOptions?: any[] ) => {
    // 过滤掉分隔符选项
    if (!(value && Array.isArray(value) && value.length > 0)) {
      setSelectedOrg([]);
    }
    const filteredValue = value;
    setSelectedOrg(filteredValue as number[]);

    // 更新过滤状态
    if (filteredValue && filteredValue.length > 0) {
      const lastValue = filteredValue[filteredValue.length - 1];
      // const lastOption = selectedOptions?.[selectedOptions.length - 1];

      // 判断最后选择的是组织还是用户
      if (typeof lastValue === 'string' && lastValue.startsWith('user_')) {
        // 选择的是用户
        const userId = lastValue.replace('user_', '');
        handleFilterChange('userId', Number.parseInt(userId));
        handleFilterChange('orgId', 0);
        // handleFilterChange('nickName', lastOption?.label?.split(' (')[0] || '');
      } else {
        handleFilterChange('orgId', lastValue);
        handleFilterChange('userId', 0);
      }
    } else {
      // 清空所有过滤
      handleFilterChange('orgId', 0);
      handleFilterChange('userId', 0);
    }
  };

  // 筛选考核数据
  const filteredAssessments = useMemo(() => {
    // 应用文本过滤
    let filtered = allAssessments;

    // 根据销售类型筛选
    if (filterState.salesType) {
      filtered = filtered.filter(
        (item) => {
          if (item.subSalesType) {
            return item.subSalesType === filterState.salesType;
          } else {
            return item.salesType === filterState.salesType;
          }
        }
      );
    }

    if (filterState.userId) {
      filtered = filtered.filter(
        (item) =>
          item.principalType === 'USER' &&
          item.principalId === filterState.userId,
      );
    }

    if (filterState.orgId) {
      filtered = filtered.filter((item) =>
        item.subCorpId ? (item.subCorpId === filterState.orgId || item.masterCorpId === filterState.orgId) : (item.masterCorpId === filterState.orgId)
      );
    }

    // 根据考核状态筛选
    if (filterState.assessStatus) {
      filtered = filtered.filter(
        (item) =>
          item.currentPlan &&
          item.currentPlan.status.toString() === filterState.assessStatus,
      );
    }

    return filtered;
  }, [allAssessments, allOrganizations, filterState]);

  // 前端分页数据
  const paginatedData = useMemo(() => {
    const startIndex = (pagination.current - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return filteredAssessments.slice(startIndex, endIndex);
  }, [filteredAssessments, pagination.current, pagination.pageSize]);

  // 过滤器变更处理
  const handleFilterChange = (key, value) => {
    setFilterState((prev) => ({
      ...prev,
      [key]: value,
    }));
    // 重置分页到第一页
    setPagination((prev) => ({
      ...prev,
      current: 1,
    }));

    // 如果修改的是销售类型，清空组织选择
    if (key === 'salesType') {
      setSelectedOrg([]);
    }
  };

  // 表格变更处理
  const handleTableChange = (newPagination, filters, sorter) => {
    setPagination((prev) => ({
      ...prev,
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    }));
  };

  // 编辑成功后刷新数据
  const handleEditSuccess = () => {
    fetchData();
  };

  // 编辑考核计划
  const handleEditAssessment = (record: DistributorEnhancedVO) => {
    if (!record.currentPlan) {
      message.warning('该用户没有考核计划，请创建');
    }
    // 即使没有考核计划也允许编辑，将创建新的考核计划
    setSelectedAssessment(record);
    setAssessmentDrawerVisible(true);
  };

  // 查看考核清单
  const handleViewAssessment = (record: DistributorEnhancedVO) => {
    setSelectPrincipal({
      type: record.principalType,
      id: record.principalId,
      name: record.principalName,
    } as PrincipalModel);
    setDrawerVisible(true);
  };

  // 处理查看结算方案
  const handleViewSettlement = (record: DistributorEnhancedVO) => {
    setSettlementDrawerVisible(true);
    setSelectedSettlement(record);
    if (!record.settleConfig) {
      message.warning('尚未设置结算方案，请及时设置');
    }
  };

  // 处理考核结果确认
  const handleReviewAssessment = (record: DistributorEnhancedVO) => {
    setReviewDrawerVisible(true);
    setSelectedAssessment(record);
  };

  // 关闭考核方案修改抽屉
  const handleAssessmentConfigDrawerClose = () => {
    setAssessmentDrawerVisible(false);
    setSelectedAssessment(null);
    fetchData();
  };

  // 关闭结算方案修改抽屉
  const handleSettlementConfigDrawerClose = () => {
    setSettlementDrawerVisible(false);
    setSelectedSettlement(null);
    fetchData();
  };

  // 关闭考核结果确认抽屉
  const handleAssessmentReviewDrawerClose = () => {
    setReviewDrawerVisible(false);
    setSelectedAssessment(null);
    fetchData();
  };

  // 更新分页总数
  useEffect(() => {
    setPagination((prev) => ({
      ...prev,
      total: filteredAssessments.length,
    }));
  }, [filteredAssessments]);

  // 表格列定义
  const columns = [
    {
      title: '结算主体',
      dataIndex: 'principalName',
      key: 'principalName',
      width: 280,
      render: (_, record: DistributorEnhancedVO) => {
        const orgName = record.subCorpName || record.masterCorpName;
        const orgId = record.subCorpName
          ? record.subCorpId
          : record.masterCorpId;

        return (
          <Flex align="center" style={{ gap: 4 }}>
            <Tooltip title={orgName}>
              <span
                style={{
                  display: 'inline-block',
                  width: '120px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {orgName}
              </span>
            </Tooltip>
            <span>
              (<span style={{ color: '#1890ff' }}>{orgId}</span>)
            </span>

            {record.principalType === 'USER' && (
              <>
                <span style={{ color: '#BFBFBF', margin: '0 4px' }}>|</span>
                <Tooltip title={record.principalName}>
                  <span
                    style={{
                      display: 'inline-block',
                      width: '120px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {record.principalName}
                  </span>
                </Tooltip>
                <span>
                  (
                  <span style={{ color: '#1890ff' }}>{record.principalId}</span>
                  )
                </span>
              </>
            )}
          </Flex>
        );
      },
    },
    {
      title: '销售/渠道商',
      dataIndex: 'masterCorpName',
      key: 'masterCorpName',
      ellipsis: {
        showTitle: false,
      },
      render: (text) => <span title={text}>{text}</span>,
    },
    {
      title: '销售/渠道商类型 | 角色',
      dataIndex: 'salesType',
      key: 'salesType',
      render: (_, record) => {
        const salesType = record.subSalesType || record.salesType;
        const roleType =
          record.principalType === 'CORP'
            ? ''
            : record.principalType === 'SUB_CORP'
              ? '二级渠道'
              : CustomRoleMap[record.customRole];
        const color: string =
          roleType === '渠道管理员'
            ? '#F5222D'
            : roleType === '二级渠道管理员'
              ? '#FAAD14'
              : roleType === '二级渠道'
                ? '#16f2fa'
                : 'gray';
        return (
          <Flex style={{ gap: 16 }}>
            <span
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                width: 66,
              }}
            >
              <ColorPoint color={salesTypeMap[salesType].color} />
              {salesTypeMap[salesType].desc}
            </span>
            <span style={{ color: '#BFBFBF' }}>|</span>
            <span>
              <ColorPoint color={color} />
              {roleType || '-'}
            </span>
          </Flex>
        );
      },
    },
    {
      title: '签约日期',
      key: 'contractDate',
      render: (_, record) => {
        if (record.customRole === 'OPS_MEMBER') {
          return <>-</>;
        }
        if (!record.contractDate) {
          return <span style={{ color: 'red' }}>未设置</span>;
        }
        return record.contractDate;
      },
    },
    {
      title: '考核类型',
      key: 'assessType',
      render: (_, record) => {
        if (record.customRole === 'OPS_MEMBER') {
          return <>-</>;
        }
        if (!record.currentPlan || !record.currentPlan.type) {
          return <span style={{ color: 'red' }}>未设置</span>;
        }
        return (
          salesAssessTypeMap[record.currentPlan.type] || record.currentPlan.type
        );
      },
    },
    {
      title: '考核状态',
      key: 'assessStatus',
      render: (_, record) => {
        if (record.customRole === 'OPS_MEMBER') {
          return <>-</>;
        }
        if (!record.currentPlan || !record.currentPlan.type) {
          return <span style={{ color: 'red' }}>未设置</span>;
        }
        const statusInfo = assessStatusMap[record.currentPlan.status];
        return (
          <span style={{ display: 'inline-flex', alignItems: 'center' }}>
            <ColorPoint color={statusInfo?.color} />
            {statusInfo?.desc || record.status}
          </span>
        );
      },
    },
    {
      title: (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <span>当前业绩</span>
          <Button size={'small'} onClick={refresh} loading={refreshing}>
            刷新
          </Button>
        </div>
      ),
      key: 'newOrderCommRate',
      render: (_, record) => {
        const totalSalesAmount =
          record.currentPlan?.kpiActual?.totalSalesAmount || 0;
        const totalSalesCount =
          record.currentPlan?.kpiActual?.totalSalesCount || 0;
        const targetSalesAmount =
          record.currentPlan?.kpiTarget?.totalSalesAmount || 0;
        const targetSalesCount =
          record.currentPlan?.kpiTarget?.totalSalesCount || 0;

        // 计算完成率
        const amountProgress =
          targetSalesAmount > 0
            ? Math.min((totalSalesAmount / targetSalesAmount) * 100, 100)
            : 0;
        const countProgress =
          targetSalesCount > 0
            ? Math.min((totalSalesCount / targetSalesCount) * 100, 100)
            : 0;

        return (
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '6px',
              minWidth: '80px',
            }}
          >
            {/* 第一行：销售金额 */}
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                height: '30px',
              }}
            >
              <span
                style={{
                  color: '#1890ff',
                  fontWeight: '500',
                  fontSize: '12px',
                }}
              >
                {truncateToDecimalsString(convertToWan(totalSalesAmount), 2)}万/
                {truncateToDecimalsString(convertToWan(targetSalesAmount), 2)}万
              </span>
              <Progress
                type="circle"
                percent={Math.round(amountProgress)}
                strokeColor="#1890ff"
                size={28}
                strokeWidth={4}
                format={(percent) => (
                  <span
                    style={{
                      fontSize: '8px',
                      color: '#1890ff',
                      fontWeight: '600',
                    }}
                  >
                    {percent}%
                  </span>
                )}
              />
            </div>

            {/* 第二行：销售单数 */}
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                height: '30px',
              }}
            >
              <span
                style={{
                  color: '#52c41a',
                  fontWeight: '500',
                  fontSize: '12px',
                }}
              >
                {totalSalesCount}/{targetSalesCount}单
              </span>
              <Progress
                type="circle"
                percent={Math.round(countProgress)}
                strokeColor="#52c41a"
                size={28}
                strokeWidth={4}
                format={(percent) => (
                  <span
                    style={{
                      fontSize: '8px',
                      color: '#52c41a',
                      fontWeight: '600',
                    }}
                  >
                    {percent}%
                  </span>
                )}
              />
            </div>
          </div>
        );
      },
    },
    {
      title: '考核周期',
      key: 'assessPeriod',
      render: (_, record) => {
        if (record.customRole === 'OPS_MEMBER') {
          return <>-</>;
        }
        if (!record.currentPlan || !record.currentPlan.planFromDate) {
          return <span style={{ color: 'red' }}>未设置</span>;
        }

        const startDate = record.currentPlan.planFromDate;
        const endDate = record.currentPlan.planEndDate;

        if (!endDate) return startDate;
        return (
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <span style={{ lineHeight: '23px' }}>{startDate}/</span>
            <span style={{ lineHeight: '23px' }}>{endDate}</span>
          </div>
        );
      },
    },
    {
      title: '费率',
      key: 'OrderCommRate',
      render: (_, record) => {
        if (record.customRole === 'OPS_MEMBER') {
          return <>-</>;
        }
        if (!record.settleConfig || !record.settleConfig.newOrderCommRate) {
          return <span style={{ color: 'red' }}>未设置</span>;
        }
        return `${record.settleConfig.newOrderCommRate * 100}% / ${record.settleConfig.renewOrderCommRate * 100}%`;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record: DistributorEnhancedVO) => {
        if (record.customRole === 'OPS_MEMBER') {
          return <>-</>;
        }

        const status = record.currentPlan?.status;
        const showConfirmButton =
          status === 'ASSESS_PASSED' || status === 'ASSESS_FAILED';

        return (
          <Space size="small" style={{ gap: '0' }}>
            <Button
              type="link"
              onClick={() => handleEditAssessment(record)}
              className={styles.actionButton}
              size="small"
            >
              考核计划
            </Button>
            <Button
              type="link"
              onClick={() => handleViewSettlement(record)}
              className={styles.actionButton}
              size="small"
            >
              结算费率
            </Button>
            <Button
              type="link"
              onClick={() => handleViewAssessment(record)}
              className={styles.actionButton}
              size="small"
            >
              考核记录
            </Button>
            {showConfirmButton && (
              <Button
                type="link"
                onClick={() => handleReviewAssessment(record)}
                className={styles.actionButton}
                size="small"
                style={{
                  color: '#ff4d4f',
                }}
              >
                确认结果
              </Button>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <div className={styles.container}>
      {/* 搜索区域 */}
      <Card className={styles.searchCard}>
        <div className={styles.filterContainer}>
          {/* 销售/渠道商 类型下拉框 */}
          <div className={styles.filterItem}>
            <Select
              placeholder="销售/渠道商类型"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilterChange('salesType', value)}
              value={filterState.salesType || undefined}
            >
              {Object.values(salesTypeMap).map((type) => (
                <Select.Option key={type.code} value={type.code}>
                  {type.desc}
                </Select.Option>
              ))}
            </Select>
          </div>
          {/* 渠道商多级筛选框 */}
          <div className={styles.filterItem} style={{minWidth: 200, maxWidth: 600}}>
            <Cascader
              placeholder="选择销售/渠道商"
              allowClear
              style={{ width: '100%' }}
              showSearch={{
                filter: (inputValue, path) =>
                  path.some((option) =>
                    option.label
                      ?.toString()
                      .toLowerCase()
                      .includes(inputValue.toLowerCase()),
                  ),
              }}
              options={convertToCascaderOptions(filteredOrgTrees)}
              onChange={handleOrgCascaderChange}
              value={selectedOrg}
              changeOnSelect
              expandTrigger="hover"
            />
          </div>
          {/* 考核状态下拉框 */}
          <div className={styles.filterItem}>
            <Select
              placeholder="考核状态"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilterChange('assessStatus', value)}
              value={filterState.assessStatus || undefined}
            >
              {Object.entries(assessStatusMap).map(([key, value]) => (
                <Select.Option key={key} value={value.code}>
                  {value.desc}
                </Select.Option>
              ))}
            </Select>
          </div>
        </div>
      </Card>

      <div className={styles.mainContent}>
        <Card className={mainStyles.tableCard}>
          <Table
            rowKey="userId"
            loading={dataLoading || refreshing}
            dataSource={paginatedData}
            columns={columns}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: pagination.showSizeChanger,
              showQuickJumper: pagination.showQuickJumper,
              onChange: (page, pageSize) => {
                setPagination((prev) => ({
                  ...prev,
                  current: page,
                  pageSize: pageSize || prev.pageSize,
                }));
              },
            }}
            onChange={handleTableChange}
            scroll={{ x: 'max-content' }}
            rowClassName={styles.tableRow}
          />
        </Card>
      </div>

      {/* 编辑考核计划抽屉 */}
      <AssessmentConfigDrawer
        visible={assessmentDrawerVisible}
        onClose={handleAssessmentConfigDrawerClose}
        onSuccess={handleEditSuccess}
        distributorEnhancedVO={selectedAssessment}
        defaultAssessmentConfigs={defaultAssessmentConfigs}
      />

      {/* 结算方案抽屉 */}
      <SettlementConfigDrawer
        visible={settlementDrawerVisible}
        onClose={handleSettlementConfigDrawerClose}
        distributorSettleConfigVO={selectedSettlement}
        defaultSettleConfigs={defaultSettleConfigs}
        allOrganizations={allOrganizations}
        drawerLoading={drawerLoading}
        onSuccess={handleEditSuccess}
      />

      {/* 考核记录抽屉 */}
      <AssessmentDetailDrawer
        visible={drawerVisible}
        onClose={() => setDrawerVisible(false)}
        principal={selectPrincipal}
      />

      {/* 考核结果 review 抽屉 */}
      <AssessmentReviewDrawer
        visible={reviewDrawerVisible}
        onClose={handleAssessmentReviewDrawerClose}
        onSuccess={handleEditSuccess}
        distributorEnhancedVO={selectedAssessment}
        defaultSettleConfigs={defaultSettleConfigs}
        allOrganizations={allOrganizations}
      />
    </div>
  );
};

export default AssessmentPlan;
