import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Alert, Button, Checkbox, DatePicker, Flex, Pagination, Select, Tabs, TabsProps, message as antdMessage } from 'antd';
import '@/pages/UserFavor/index.less';
import {
  FavorCreationModel,
  FavorImageModel, getModels4Favor,
  queryFavorDetail,
  queryFavorImg4ModelByPage, queryFavorImgWithoutModelId,
} from '@/services/UserFavorController';
import ImageCardBlock from '@/components/Favor/ImageCardBlock';
import FavorDetailModal from '@/components/Favor/FavorDetailModal';
import WatermarkImage from '@/components/WatermarkImage';
import ImgPreview from '@/components/ImgPreview';
import UserFavorButton from '@/components/Favor/UserFavorButton';
import { MaterialModel } from '@/services/MaterialModelController';
import WatermarkVideo from '@/components/WatermarkVideo';
import { CreativeVO, queryCreativeById } from '@/services/CreativeController';
import VideoPreview from '@/components/VideoPreview';
import useImageSelector from '@/hooks/useImageSelector';
import IconFont from '@/components/IconFont';
import { useDownloader } from '@/hooks/useDownloader';

const { RangePicker } = DatePicker;
interface UserFavorProps {
  type?: 'IMAGE' | 'VIDEO';
}
const shotLabel = {
  IMAGE: '图片',
  VIDEO: '视频',
};
const UserFavor : React.FC<UserFavorProps> = ({type = 'IMAGE'}) => {

  const [searchType, setSearchType] = useState<'WITH_MODEL_ID' | 'WITH_OUT_MODEL_ID'>('WITH_MODEL_ID');
  const [showListWithModelId, setShowListWithModelId] = useState<Array<FavorCreationModel>>([]);
  const [showListWithOutModelId, setShowListWithOutModelId] = useState<Array<FavorImageModel>>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedItem, setSelectItem] = useState<FavorCreationModel>();
  const items: TabsProps['items'] =  [{ key: 'WITH_MODEL_ID', label: '服装' + shotLabel[type] }, { key: 'WITH_OUT_MODEL_ID', label: '其他'}];

  // 图片预览相关参数
  const [showImgPreview, setShowImagePreview] = useState(false);
  const [selectedModel, setSelectedModel] = useState<FavorImageModel>();
  const [imgPreviewIndex, setImgPreviewIndex] = useState<number | undefined>();
  // 视频预览相关参数
  const [showVideoPreview, setShowVideoPreview] = useState(false);
  const [selectedBatch, setSelectedBatch] = useState<CreativeVO>();
  // 日期选择相关参数
  const [dates, setDates] = useState<any>([]);
  // 按服装名称搜索相关参数
  const [selectedModelId, setSelectedModelId] = useState<number>();
  const [loraModels, setLoraModels] = useState<Array<MaterialModel>>();
  // 按类型搜索相关参数
  // const [type, setType] = useState<'IMAGE' | 'VIDEO'>('IMAGE');
  // Alert 相关参数
  const [message, setMessage] = useState<string>('');

  const [selectedCreation, setSelectedCreation] = useState<CreativeVO>();

  const {
    selectedImages,
    isAllSelected,
    selectedCount,
    handleSelectImage,
    handleSelectAll,
    shouldShowImageSelection,
    setImageSelectorOptions,
    isImageSelected,
  } = useImageSelector({
    images: [],
    shouldShowSelector: () => true,
  });
  const { downloading, simpleDownloadUrls } = useDownloader();

  useEffect(() => {
    init();
  }, []);

  useEffect(() => {
    fetchData();
  }, [pageNum, pageSize, searchType, dates, selectedModelId, type]);

  useEffect(() => {
    switch (searchType) {
      case 'WITH_MODEL_ID':
        setMessage(`此处显示与服装关联的${shotLabel[type]}收藏`);
        break;
      case 'WITH_OUT_MODEL_ID':
        setMessage(`此处显示未关联服装的${shotLabel[type]}收藏`);
        break;
      default:
        break;
    }
  }, [searchType]);

  async function init() {
    const loraModelsRes = await getModels4Favor();
    if (loraModelsRes) {
      setLoraModels(loraModelsRes);
    }
  }

  async function fetchData() {
    let query = {};
    query['type'] = type;

    if (selectedModelId) {
      query['modelId'] = selectedModelId;
    }

    if (dates && dates.length > 1) {
      // @ts-ignore
      query['dateFrom'] = dates[0].format('YYYYMMDD');
      // @ts-ignore
      query['dateTo'] = dates[1].format('YYYYMMDD');
    }

    if (pageNum && pageSize) {
      query['pageNum'] = pageNum;
      query['pageSize'] = pageSize;
    }



    switch (searchType) {
      case 'WITH_MODEL_ID': // 'ALL' 可以分页
        const allRes = await queryFavorImg4ModelByPage(query);
        if (allRes) {
          setShowListWithModelId(allRes.list || []);
          setTotalCount(allRes.totalCount || 0);
        }
        break;
      case 'WITH_OUT_MODEL_ID':
        const otherRes = await queryFavorImgWithoutModelId(query);
        if (otherRes) {
          setShowListWithOutModelId(otherRes);
          setImageSelectorOptions(prev => ({
            ...prev,
            images: otherRes?.map((item) => item.image) || [],
          }));
        }
        break;
      default:
        break;
    }
  }

  const handlePageChange = (page: number, pageSize: number) => {
    setPageNum(page);
    setPageSize(pageSize);
  };
  
  const handleShowDetailModal = (item : FavorCreationModel) => {
    setSelectItem(item);
    setShowDetailModal(true);
  }

  const handleDateChange = (newDates) => {
    console.log('handleDateChange:', newDates);
    setDates(newDates);
  };

  const handlePreview = (item: FavorImageModel, index: number) => {
    if (item.type === 'IMAGE') {
      handleImgPreview(item, index);
    } else {
      handleVideoPreview(item);
    }
  }

  const handleImgPreview = (item: FavorImageModel, index: number) => {
    setSelectedModel(item);
    setImgPreviewIndex(index);

    //走服务端查一下这个创作的最新信息
    queryCreativeById(item?.batchId).then(res => {
      if (res) {
        // 设置创建创作
        // setSelectedCreation(res);

        // 设置图片预览
        setShowImagePreview(true);
      }
    })



  }

  const handleVideoPreview = (item: FavorImageModel) => {
    queryCreativeById(item.batchId).then(res => {
      if (res) {
        setSelectedBatch(res);
        setSelectedModel(item);
        setShowVideoPreview(true);
      }
    });
  }

  return (
    <PageContainer className={'user-favor'}>
      <Flex
        className={
          'user-favor-container ' + 'user-favor-container-fix-' + searchType
        }
      >
        <Tabs
          defaultActiveKey="1"
          items={items}
          onChange={(key) =>
            setSearchType(key as 'WITH_MODEL_ID' | 'WITH_OUT_MODEL_ID')
          }
          size={'large'}
          indicator={{ size: 102 }}
        />
        <Alert type={'info'} showIcon message={message} style={{width: '36%', fontSize: 14, height: 32, borderRadius: 6, marginLeft: 12 }} />
        <div className="user-favor-tool-bar">
          {/*<Select
            value={type}
            placeholder="请选择收藏类型"
            onChange={(value) => setType(value)}
            style={{ width: 200 }}
            options={[
              { label: '图片', value: 'IMAGE' },
              { label: '视频', value: 'VIDEO' },
            ]}
          />*/}
          <RangePicker
            onChange={handleDateChange}
            placeholder={['开始日期', '结束日期']}
            style={{ marginLeft: 12 }}
          />
          {searchType === 'WITH_MODEL_ID' && (
            <Select
              value={selectedModelId}
              placeholder="请选择服装"
              onChange={(value) => setSelectedModelId(value)}
              style={{ width: 200 }}
              allowClear
              showSearch
              optionFilterProp="label"
              options={
                loraModels
                  ? loraModels.map((lm) => ({ label: lm.name, value: lm.id }))
                  : []
              }
            />
          )}
          {searchType === 'WITH_OUT_MODEL_ID' && shouldShowImageSelection() && (
            <Flex justify="flex-start" align="center">
              <Checkbox
                checked={isAllSelected}
                onChange={(e) => handleSelectAll(e.target.checked)}
              >
                全选
              </Checkbox>
              {/* 下载按钮 */}
              {shouldShowImageSelection() &&
                <Button
                  loading={downloading}
                  type={'primary'} 
                  className={'download-btn'} 
                  onClick={() => {
                    simpleDownloadUrls(selectedImages, "favorite_images_" + new Date().getTime());
                  }}
                  icon={<IconFont type={'icon-icon-download'} style={{ fontSize: 16, color: '#FFFFFF' }} />}
                  disabled={selectedCount === 0}
                >
                  下载{
                    isAllSelected ? '全部' : `选中(${selectedCount})`
                  }
                </Button>
              }
            </Flex>
          )}
        </div>
        {searchType === 'WITH_MODEL_ID' && (
          <div className="user-favor-list">
            <div className="user-favor-card-list">
              {showListWithModelId &&
                Array.isArray(showListWithModelId) &&
                showListWithModelId.length > 0 &&
                showListWithModelId.map((item) => (
                  <div key={item.modelId} style={{ position: 'relative' }}>
                    <ImageCardBlock
                      key={item.modelId}
                      item={item}
                      onclick={() => {
                        handleShowDetailModal(item);
                      }}
                    />
                    <Button
                      style={{
                        position: 'absolute',
                        top: 8,
                        right: 8,
                        zIndex: 100,
                        width: 20,
                        height: 20,
                        borderRadius: 5.68,
                        opacity: 1,
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                        padding: 4,
                        background: 'rgba(0, 0, 0, 0.6)',
                      }}
                      disabled={downloading}
                      onClick={() => {
                        queryFavorDetail({
                          type: item?.type || 'IMAGE',
                          modelId: item?.modelId || 0,
                        }).then(res => {
                          if (res) {
                            antdMessage.loading({ content: '下载中...', duration: 0 });
                            simpleDownloadUrls(res.images.map(i => i.image), "favorite_images_" + new Date().getTime())
                              .finally(() => {
                                antdMessage.destroy();
                              });
                          }
                        });
                      }}
                    >
                      <IconFont type={'icon-a-shangchuan1x'} style={{ fontSize: '12px' }} />
                    </Button>
                  </div>
                ))}
            </div>
            <Pagination
              current={pageNum}
              pageSize={pageSize}
              total={totalCount}
              onChange={handlePageChange}
              showTotal={(total) => `共 ${total} 条收藏`}
              showSizeChanger // 允许用户更改每页显示条数
              showQuickJumper // 允许用户快速跳转到某一页
              style={{ marginTop: '16px', textAlign: 'center' }}
            />
          </div>
        )}

        {searchType === 'WITH_OUT_MODEL_ID' && (
          <div
            className="favor-image-list-container"
            style={{ paddingTop: 0, height: '100%' }}
          >
            {showListWithOutModelId &&
              Array.isArray(showListWithOutModelId) &&
              showListWithOutModelId.map((item, index) => (
                <div
                  key={`favor-image-${index}`}
                  className="favor-image-list-item"
                >
                  { item.type === 'IMAGE' &&
                    <div style={{ position: 'relative' }}>
                      <WatermarkImage
                        className="favor-image-list-item-water-mark"
                        src={item.image}
                        loading="lazy"
                        onClick={() => handlePreview(item, index)}
                      />
                      {/* 单独放置复选框，避免事件冒泡问题 */}
                      <div 
                        style={{
                          position: 'absolute',
                          top: 8,
                          right: 8,
                          zIndex: 100,
                        }}
                        onClick={(e) => {
                          handleSelectImage(item.image);
                        }}
                      >
                        <Checkbox
                          checked={isImageSelected(item.image)}
                        />
                      </div>
                    </div>
                  }
                  {item.type === 'VIDEO' &&
                    <div style={{display: 'flex', width: '100%', height: '100%' }}>
                      <WatermarkVideo
                        className="favor-image-list-item-water-mark"
                        src={item.image}
                        onClick={() => handlePreview(item, index)}
                        controls={false}
                      />
                    </div>
                  }
                  <div className="user-favor-button">
                    <UserFavorButton
                      favorType={item.type}
                      itemId={item.batchId}
                      imageIndex={item.index}
                    />
                  </div>
                </div>
              ))}
          </div>
        )}
      </Flex>
      <FavorDetailModal
        modelId={selectedItem?.modelId || 0}
        type={selectedItem?.type || 'IMAGE'}
        open={showDetailModal}
        onCancel={() => {
          setShowDetailModal(false);
          fetchData();
        }}
        centered={true}
      />
      <ImgPreview
        previewVisible={showImgPreview}
        previewImgs={showListWithOutModelId.map((item) => item.image)}
        previewIdx={imgPreviewIndex}
        needSwitch={true}
        // creativeBatch={selectedCreation}
        handleCancelPreview={() => setShowImagePreview(false)}
        previewImage={selectedModel?.image || ''}
        showTools
        previewInfoList={
          showListWithOutModelId.map((item) => ({
            batchId: item.batchId,
            indexInBatch: item.index,
          }))}
        showBatchInfo
      />
      <VideoPreview
        src={selectedModel?.image || ''}
        open={showVideoPreview}
        destroyOnClose={true}
        onClose={() => setShowVideoPreview(false)}
        batch={selectedBatch}
        showDownload={true}
        indexInBatch={selectedModel?.index}
      />

    </PageContainer>
  );
}

export default UserFavor;