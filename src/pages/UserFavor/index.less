@import "@/app";
.user-favor {
  &-container {
    display: flex;
    flex-direction: column;
    padding: 16px 16px 8px 16px;
    margin: 16px 16px;
    gap: 8px;
    border-radius: 8px;
    background: #FFFFFF;

    .ant-tabs-nav-list {
      gap: 50px;
    }
    .ant-tabs-tab {
      width: 102px;
      justify-content: center;
    }
  }
  &-container-fix-WITH_OUT_MODEL_ID {
    height: calc(100vh - @navigation-height - 30px);
  }
  &-container-fix-ALL {
  }
  &-tool-bar {
    display: flex;
    justify-content: flex-start;
    gap: 48px;
    margin-bottom: 12px;
  }
  &-list {
  }
  &-card-list {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(5, 1fr); /* 5 列，每列等宽 */
    grid-auto-rows: minmax(100px, auto); /* 行高度最小为100px，最大根据内容自动调整 */
    place-items: flex-start center;
    row-gap: 24px;
  }
  &-button {
    position: absolute;
    right: 8px;
    bottom: 24px;
    svg {
      width: 20px;
      height: 20px;
    }
  }
}