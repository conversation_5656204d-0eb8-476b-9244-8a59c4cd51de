.history-form-container {
  display: flex;
  flex-direction: column;
  padding: 16px 16px 0 16px;
  gap: 8px;
}

.history-form-body-NORMAL {
  padding: 0 16px;
  border-radius: 8px;
  background: #FFFFFF;

  .ant-tabs-tab {
    width: 102px;
    justify-content: center;
  }
}

.history-form-body-LOOK {
  padding: 16px 16px;
  border-radius: 8px;
  background: #FFFFFF;

  .ant-tabs-tab {
    width: 102px;
    justify-content: center;
  }
}

.history-filter-row {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  gap: 8px;
  z-index: 0;
  //height: 32px;
}

.history-card-list {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(5, 1fr); /* 5 列，每列等宽 */
  grid-auto-rows: minmax(100px, auto); /* 行高度最小为100px，最大根据内容自动调整 */
  place-items: flex-start center;
  row-gap: 24px;
}

.history-image-card {
  width: 248px;
  opacity: 1;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  padding: 0;
  z-index: 1;
  gap: 8px;
}

.history-img-cover {
  position: relative;
  width: 248px;
  height: 248px;
  border-radius: 8px;
  opacity: 1;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  overflow: hidden;

  background: #F5F6F9;

  z-index: 0;
}

.history-img-cover img:not(.no-inherit) {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.history-img-count {
  position: absolute;
  right: 12px;
  bottom: 12px;

  height: 29px;
  border-radius: 4px;
  opacity: 1;

  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 6px 12px;
  gap: 8px;

  background: rgba(0, 0, 0, 0.6);

  font-family: "PingFang SC";
  font-size: 12px;
  font-weight: 500;
  line-height: normal;
  text-align: right;
  letter-spacing: 0em;

  font-variation-settings: "opsz" auto;
  color: #FFFFFF;

  z-index: 0;
}

.history-image-biz-tag {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px 0 0 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1;

  span {
    color: #FFFFFF;
    font-size: 12px;
  }
}

.history-image-card-info {
  width: 100%;
  opacity: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0;
  gap: 4px;

  z-index: 1;
}

.history-image-card-button {
  width: 120px;
  height: 38px;
  border-radius: 8px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px 32px;
  gap: 8px;

  background: linear-gradient(90deg, #0060FF 0%, #9478EA 100%);
  transition: background 0.3s;

  z-index: 4;

  font-family: "PingFang SC";
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0em;

  font-variation-settings: "opsz" auto;
  color: #FFFFFF;
}

.history-traing-mask {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  opacity: 1;

  background: rgba(41, 41, 41, 0.8);

  display: flex;
  flex-direction: column;
  align-items: center;
}

.history-train-icon {
  margin-top: 56px;
}

.history-training-title {
  margin-top: 12px;

  font-family: "PingFang SC";
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0em;

  font-variation-settings: "opsz" auto;
  color: #FFFFFF;
}

.history-training-finish-time {
  margin-top: 8px;

  font-family: "PingFang SC";
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0em;

  font-variation-settings: "opsz" auto;
  color: rgba(255, 255, 255, 0.8);
}

.history-detail-content {
  display: flex;
  flex-direction: row;
  padding: 0;
  border-radius: 24px;
}

.history-detail-left {
  width: 264px;
  margin-left: -24px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 24px;
  gap: 16px;
  align-self: stretch;

  z-index: 0;

  img:not(.no-inherit) {
    width: auto;
    height: 160px;
    object-fit: contain;
  }
}

.history-detail-left-inner {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0;
  gap: 12px;
  z-index: 0;
  width: 100%;
}

.history-left-inner-top {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0;
  gap: 8px;
  width: 100%;
  z-index: 0;
  
  /* 标题元素靠左对齐 */
  > div.font-pf.text14.weight {
    align-self: flex-start;
  }

  img:not(.no-inherit) {
    width: 160px;
    height: 160px;
    object-fit: contain;
    border-radius: 5.16px;
    box-sizing: border-box;
    border: 0.65px solid #E0E0E0;
    margin: 0 auto;
  }
}

.history-detail-pair {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0;
  gap: 8px;
  z-index: 1;
  color: #1A1B1D;
}

.history-detail-pair-row {
  flex-direction: row;
  justify-content: flex-start;
}

.history-detail-info-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 3px 0;
  border-bottom: 1px dashed #E0E0E0;
}

.history-detail-info-row:last-child {
  border-bottom: none;
}

.history-detail-info-label {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  flex: 0 0 auto;
}

.history-detail-info-value {
  font-size: 12px;
  color: #666;
  text-align: right;
  flex: 1;
  word-break: break-word;
  padding-left: 8px;
}

.history-detail-info-row-compact {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0;
  margin: 0;
  line-height: 1.2;
}

.history-detail-section-compact {
  margin: 0;
  padding: 0;
}

.history-detail-section {
  margin-bottom: 8px;
  padding-bottom: 3px;
}

// 固定姿势创作的场景信息行样式
.scene-info-row {
  align-items: flex-start !important;
  
  .history-detail-info-label {
    align-self: flex-start;
    margin-top: 2px;
    flex: 0 0 30px; // 固定标签宽度，给右侧更多空间
  }
  
  .history-detail-info-value {
    flex: 1;
    text-align: left;
    padding-left: 8px;
  }
}

// 固定姿势创作的场景样式
.fixed-posture-scenes {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.scene-item {
  font-size: 12px;
  color: #666;
  padding: 2px 0;
  position: relative;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.scene-item:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px dashed #E0E0E0;
}

.history-detail-right {
  width: 920px;
  border-radius: 0 24px 24px 0;
  opacity: 1;
  background: #F5F6F9;
  z-index: 1;

  padding-left: 30px;



  overflow-y: auto;
  will-change: transform;
}

.history-detail-image-item {
  opacity: 1;
  display: flex;
  flex-direction: column;
  padding: 0px;
  gap: 8px;
  margin-right: 12px;
  margin-top: 16px;
  z-index: 0;
}

.history-detail-img-desc {
  font-family: 'PingFang SC';
  font-size: 12px;
  font-weight: normal;
  line-height: normal;
  letter-spacing: 0em;

  font-variation-settings: "opsz" auto;
  color: #1A1B1D;

  z-index: 1;
}

.history-detail-img-container {
  position: relative;
  opacity: 1;
  display: flex;
  z-index: 0;
  cursor: pointer;
}

.history-detail-img-icon {
  position: absolute;
  right: 8px;
  bottom: 8px;
  width: 20px;
  height: 20px;
  border-radius: 5.68px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px;

  background: rgba(0, 0, 0, 0.6);

  z-index: 0;
}

.history-detail-img-icon-box {
  position: absolute;
  right: 8px;
  bottom: 8px;
  width: auto;
  height: auto;
  border-radius: 5.68px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 4px;

  z-index: 0;
}

.history-image-card-info-block {
  display: flex;
  width: 100%;
}

.history-image-card-info-inner-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.history-image-card-info-inner-right {
  display: flex;
  width: 10%;
  align-items: flex-start;
  margin-left: auto;
}
.history-detail-img-favor-icon {
  position: absolute;
  right: 36px;
  bottom: 8px;
  width: 20px;
  height: 20px;
  border-radius: 5.68px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px;
  background: rgba(0, 0, 0, 0.6);
  z-index: 0;
}
