import React, { useCallback, useEffect, useState, useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Badge,
  Button,
  Checkbox,
  Flex,
  Input,
  message,
  Modal,
  Pagination,
  Popconfirm,
  Radio,
  Segmented,
  Select, Space,
  Tabs,
  TabsProps,
  Tooltip,
} from 'antd';
import { useLocation } from 'react-router-dom';
import './index.less';
import '@/app.less';
import { MaterialModel, queryModelDetailShowImage } from '@/services/MaterialModelController';
import IconFont from '@/components/IconFont';
import {
  ALLCreativeStatus,
  ALLCreativeType,
  applyRefineExample,
  BizType,
  completeRefineExample,
  CreativeStatus,
  CreativeTaskVO,
  CreativeVO,
  downloadAllImages,
  getCreativeBatchByIdWithTask,
  isExampleImage,
  isImageLike,
  isProcessing,
  isRefineCompleted,
  isRefineInit,
  monitorImgDownload,
  queryCreativeById,
  queryCreativesByPage,
  queryModels4HistoryTasks,
  queryNeedProcessCreativeCnt,
  startCreateVideo,
} from '@/services/CreativeController';
import { download, formatText, getUserInfo } from '@/utils/utils';
import ImgPreview, { ImgPreviewInfo } from '@/components/ImgPreview';
import {
  queryAllDistributorOperators,
  queryAllStaffs,
  queryDistributorCreativeRecordsByPage,
} from '@/services/DistributorController';
import WatermarkImage from '@/components/WatermarkImage';
import ImageCardBlock from '@/components/History/ImageCardBlock';
import VideoCard from '@/components/Creative/VideoCard';
import { queryAllOperators } from '@/services/SystemController';
import { predict, PredictVO, updatePoint } from '@/services/PointController';
import { useNavigate } from '@@/exports';
import { allByRoleTypes, isVipUser, UserVO } from '@/services/UserController';
import TopupModal from '@/pages/Topup/Topup';
import WatermarkVideo from '@/components/WatermarkVideo';
import { QuestionCircleOutlined, RedoOutlined } from '@ant-design/icons';
import AssignModal from '@/components/Operate/AssignModal';
import { queryGarmentList } from '@/services/PromptDictController';
import _ from 'lodash';
import debounce from 'lodash/debounce';
import { DEBOUNCE_DELAY, IS_TRIAL_ACCOUNT } from '@/constants';
import UserFavorButton from '@/components/Favor/UserFavorButton';
import MaskOverlay from '@/components/imageOperate/MaskOverlay';
import useImageSelector from '@/hooks/useImageSelector';
import { sortFpImages } from '@/components/Lora/FacePinchingImageBlock';

const { Option } = Select;

interface HistoryPageProps {
  isOperate?: boolean;
  isDistributor?: boolean;
  type?: string;
  bizType?: BizType;
}

const HistoryPage: React.FC<HistoryPageProps> = ({
                                                   isOperate = false,
                                                   isDistributor = false,
                                                   type = 'IMAGE',
                                                   bizType = 'ALL',
                                                 }) => {

  const [selectedModelId, setSelectedModelId] = useState<number>();
  const [selectedStatus, setSelectedStatus] = useState<CreativeStatus | null>(null);
  const [searchKey, setSearchKey] = useState<string>();
  const [searchKeyValue, setSearchKeyValue] = useState<string>(''); // 用于商户输入框显示的值
  const [searchId, setSearchId] = useState<string>();
  const [searchIdValue, setSearchIdValue] = useState<string>(''); // 用于ID输入框显示的值

  const [creations, setCreations] = useState<Array<CreativeVO>>([]);
  const [loraModels, setLoraModels] = useState<Array<MaterialModel>>([]);

  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedCreation, setSelectedCreation] = useState<CreativeVO>();
  const [needWatermark, setNeedWatermark] = useState(false);

  const location = useLocation();
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const isTrialAccount = sessionStorage.getItem(IS_TRIAL_ACCOUNT) === 'Y';

  // @ts-ignore
  isDistributor = isDistributor ? isDistributor : userInfo?.roleType === 'DISTRIBUTOR';

  //翻页查询展示历史创作
  const [searchType, setSearchType] = useState<string | null>(null);
  const [ownerType, setOwnerType] = useState<string>(isDistributor ? 'customer' : 'mine');
  const [totalCreations, setTotalCreations] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const [predictVO, setPredictVO] = useState<PredictVO | null>(null);
  const [showTopupModal, setShowTopupModal] = useState(false);
  const [showCreateVideoModal, setShowCreateVideoModal] = useState(false);

  //只看精选图
  const [onlyShowExampleImg, setOnlyShowExampleImg] = useState(false);
  const [onlyShowDislike, setOnlyShowDislike] = useState(false);
  const [onlyShowLike, setOnlyShowLike] = useState(false);
  const [searchCustomerType, setSearchCustomerType] = useState<string | null>(null);
  const [onlyShowRefine, setOnlyShowRefine] = useState(false);
  const [onlyShowDemo, setOnlyShowDemo] = useState(false);

  const [relatedOperator, setRelatedOperator] = useState('all');
  const [relatedOperatorItems, setRelatedOperatorItems] = useState<any[]>([]);

  const [selectedGarment, setSelectedGarment] = useState<string[] | null>(null);
  const [searchTitle, setSearchTitle] = useState<string | null>(null);
  const [searchTitleValue, setSearchTitleValue] = useState<string>(''); // 用于标题输入框显示的值
  const [garmentItems, setGarmentItems] = useState<any[]>([]);

  const [needProcessCreative, setNeedProcessCreative] = useState(0);
  const [vipUser, setVipUser] = useState(false);
  const [assignBatch, setAssignBatch] = useState<CreativeVO | null>(null);
  const [operatorList, setOperatorList] = useState<UserVO[]>([]);
  //渠道运营
  const [selectOperatorId, setSelectedOperatorId] = useState<number>();
  const [isParamsParsed, setIsParamsParsed] = useState(false);

  //只查询有下载的创作：后台管理员
  const [onlyDownloaded, setOnlyDownloaded] = useState(false);

  const navigate = useNavigate();
  const isDistAdminOrOpera = userInfo && (userInfo.customRole === 'CHANNEL_ADMIN' || userInfo.customRole === 'OPS_MEMBER' || userInfo.customRole === 'SECOND_CHANNEL_ADMIN');

  const {
    selectedImages,
    handleSelectImage,
    handleSelectAll,
    shouldShowImageSelection,
    setImageSelectorOptions,
    selectedCount,
    indeterminate,
    isAllSelected,
    isImageSelected,
  } = useImageSelector({
    images: selectedCreation?.resultImages || [],
    shouldShowSelector: (selectedCreation) => {
      return selectedCreation && !isProcessing(selectedCreation) && (['CREATE_IMAGE', 'FIXED_POSTURE_CREATION', 'FACE_PINCHING'].includes(selectedCreation.type));
    },
  });

  const [previewImage, setPreviewImage] = useState('');
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewIdx, setPreviewIdx] = useState();
  const [previewImgs, setPreviewImgs] = useState<string[]>([]);
  const [previewInfoList, setPreviewInfoList] = useState<ImgPreviewInfo[]>([]);
  const [likeObj, setLikeObj] = useState<JSON | null | undefined>(null);
  const [showTools, setShowTools] = useState(true);
  const [mask, setMask] = useState('');

  //当前渠道商的所有员工（当前为渠道商管理员或二级管理员时才有值）
  const [allDistributorStaff, setAllDistributorStaff] = useState<UserVO[]>([]);
  //渠道管理-创作记录-我的创作，选择图片创建者
  const [selectedDistributorImageCreatorStaff, setSelectedDistributorImageCreatorStaff] = useState<number>();

  //当前用户是否是渠道管理员或渠道二级管理员
  const isDistAdminOrSecondAdmin = userInfo && (userInfo.customRole === 'CHANNEL_ADMIN' || userInfo.customRole === 'SECOND_CHANNEL_ADMIN');

  //管理后台-创作记录-图生视频，只看20小时未交付的客户创作
  const [onlyNearingDelivery, setOnlyNearingDelivery] = useState(false);
  const [onlyShowProcessing, setOnlyShowProcessing] = useState(false);
  const [onlyShowUserCreative, setOnlyShowUserCreative] = useState(false);

  //管理后台-创作记录，操作人下拉菜单列表
  const [allUserOptions, setAllUserOptions] = useState<any[]>([]);
  //管理后台-创作记录，操作人下拉选项
  const [seletedCreativeBatchOperatorId, setSeletedCreativeBatchOperatorId] = useState<number>();

  async function fetchData() {
    let query = {
      orderBy: 'id desc',
    };

    if (searchKey) {
      query['nickLike'] = searchKey?.trim();
    }

    //只看精选图
    if (onlyShowExampleImg) {
      query['bizTag'] = 'exampleImages';
    }

    if (onlyShowDislike) {
      query['onlyShowDislike'] = true;
    }

    if (onlyShowLike) {
      query['onlyShowLike'] = true;
    }

    if (searchCustomerType && searchCustomerType !== 'all') {
      query['customerType'] = searchCustomerType;
    }

    if (onlyShowProcessing) {
      query['onlyShowProcessing'] = true;
    }

    if (onlyShowUserCreative) {
      query['onlyShowUserCreative'] = true;
    }

    if (onlyShowRefine) {
      query['onlyShowRefine'] = true;
    }

    if (onlyShowDemo) {
      query['onlyShowDemo'] = true;
    }

    if (relatedOperator && relatedOperator !== 'all') {
      query['relatedOperator'] = relatedOperator;
    }

    if (isOperate) {
      if (selectedStatus) {
        query['status'] = selectedStatus;
      } else {
        query['status'] = null;
      }
    }

    if (selectedModelId) {
      query['modelId'] = selectedModelId;
    }

    if (page && pageSize) {
      query['pageNum'] = page;
      query['pageSize'] = pageSize;
    }

    if (searchType) {
      query['type'] = searchType === 'ALL' ? null : searchType;
    }

    if (type && !isOperate) {
      query['typeList'] = ALLCreativeType.filter(e => e.type === type).map(e => e.key);
    }

    if (!isOperate && (userInfo.roleType !== 'OPERATOR' || userInfo.roleType !== 'ADMIN')) {
      query['typeNotIn'] = ['FIX_VIDEO_FACE'];
    }

    if (searchId) query['id'] = searchId;

    if (selectOperatorId) {
      query['relateDistOpUserId'] = selectOperatorId;
    }

    if (selectedGarment) {
      let list: string[] = [];
      selectedGarment.forEach(item => {
        list = [...list, ...(item.split(','))];
      });
      query['garmentTypeList'] = list;
    }

    if (searchTitle) {
      query['title'] = searchTitle?.trim();
    }

    if (bizType && bizType !== 'ALL') {
      query['bizType'] = bizType;
    }

    if (isDistAdminOrSecondAdmin && ownerType === 'mine' && selectedDistributorImageCreatorStaff) {
      query['operatorId'] = selectedDistributorImageCreatorStaff;
    }

    if (isOperate && seletedCreativeBatchOperatorId){
      query['operatorId'] = seletedCreativeBatchOperatorId;
    }

    if (isOperate && onlyNearingDelivery) {
      query['onlyNearingDelivery'] = onlyNearingDelivery;
    }

    if (onlyDownloaded) {
      query['onlyDownloaded'] = onlyDownloaded;
    }

    const method = ownerType === 'customer' ? queryDistributorCreativeRecordsByPage : queryCreativesByPage;
    let ret = await method(query);
    if (ret) {
      setCreations(ret?.list || []);
      if (ret?.totalCount !== undefined) {
        setTotalCreations(ret.totalCount);
      }
    }
  }

  const handleModelChange = (value) => {
    setSelectedModelId(value);
  };

  const handleOperatorChange = (value) => {
    setSelectedOperatorId(value);
  };

  async function handleClickCreationCard(id, type) {
    //走服务端查一下这个创作的最新信息
    let creative;
    if (type === 'BASIC_CHANGING_CLOTHES' || type === 'FIXED_POSTURE_CREATION' || type === 'CLOTHING_SWAP' ) {
      creative = await getCreativeBatchByIdWithTask(id);
    } else {
      creative = await queryCreativeById(id);
    }
    creative && fillCreativeExt(creative);

    // @ts-ignore
    setSelectedCreation(creative);
    setImageSelectorOptions(prev => ({
      ...prev,
      images: creative?.resultImages || [],
    }));
    setShowDetailModal(true);
  }

  function fillCreativeExt(creative: CreativeVO) {
    fillMask(creative);
  }

  const fillMask = (creative: CreativeVO) => {
    let json;
    if (creative && creative.extInfo) {
      json = creative.extInfo['originMaskModel'];
    }
    if (!json) {
      return;
    }
    const obj = JSON.parse(json);
    console.log(obj);
    if (Array.isArray(obj)) {
      obj.forEach((item: any) => {
        creative.extInfo[item['key'] + 'Mask'] = item['mask'];
      });
    }
  };

  async function init() {
    const newModels = await queryModels4HistoryTasks();
    // @ts-ignore
    setLoraModels(newModels);

    if (isOperate) {
      queryAllOperators().then(res => {
        if (res && res.length > 0) {
          let items = [
            { label: '全部', value: 'all', style: { width: 40, padding: 0, textAlign: 'center' } },
            { label: '未设', value: 'unset', style: { width: 40, padding: 0, textAlign: 'center' } },
          ];
          res.forEach(operator => {
            items.push({
              label: operator.nickName,
              value: operator.mobile,
              style: { width: 40, padding: 0, textAlign: 'center' },
            });
          });

          setRelatedOperatorItems(items);
        }
      });

      //查询平台所有用户作为操作人下拉菜单列表
      allByRoleTypes(['MERCHANT','ADMIN','OPERATOR','DISTRIBUTOR','DEMO_ACCOUNT']).then(res => {
        if (res && Array.isArray(res)){
          setAllUserOptions(res.map(item => ({
            label: item.nickName,
            value: item.id,
          })));
        }
      })

    }

    if (isOperate || isDistributor) {
      queryGarmentList().then(res => {
        if (res && res.length > 0) {
          const map = new Map();
          res.forEach(item => {
            let value = [];
            if (map.has(item.word)) {
              value = map.get(item.word);
            }
            map.set(item.word, [...value, item.prompt]);
          });

          const items = Array.from(map.keys()).map(key => ({
            label: key,
            // value是map.get(key)转换成以逗号分割的字符串
            value: map.get(key).join(','),
          })).sort((a, b) => a.label > b.label ? 1 : -1);

          setGarmentItems(items);
        }
      });
    }

  }

  // 防抖搜索函数 - 商户名称
  const debouncedSearchKey = useCallback(
    debounce((value: string) => {
      setSearchKey(value || undefined);
    }, DEBOUNCE_DELAY),
    []
  );

  // 防抖搜索函数 - ID
  const debouncedSearchId = useCallback(
    debounce((value: string) => {
      setSearchId(value || undefined);
    }, DEBOUNCE_DELAY),
    []
  );

  // 防抖搜索函数 - 标题
  const debouncedSearchTitle = useCallback(
    debounce((value: string) => {
      setSearchTitle(value || null);
    }, DEBOUNCE_DELAY),
    []
  );

  //加载后处理
  // @ts-ignore
  useEffect(() => {

    init();

    // 解析查询字符串
    const modelId = new URLSearchParams(location.search).get('modelId');
    if (modelId) {
      setSelectedModelId(Number(modelId));
    }

    // 解析查询字符串
    const id = new URLSearchParams(location.search).get('id');
    if (id) {
      handleClickCreationCard(id, '');
    }

    if (isOperate) {
      queryNeedProcessCreativeCnt().then(res => {
        if (res) {
          setNeedProcessCreative(res);
        }
      });
    }

    if (isDistributor) {
      queryAllDistributorOperators({}).then(res => {
        if (res) {
          setOperatorList(res);
        }
      });

      if (isDistAdminOrSecondAdmin) {
        queryAllStaffs({}).then(res => {
          if (res && Array.isArray(res)) {
            setAllDistributorStaff(res);
          }
        });
      }
    }

    setIsParamsParsed(true);

    return () => {
      setIsParamsParsed(false);
      // 清理防抖函数
      debouncedSearchKey.cancel();
      debouncedSearchId.cancel();
      debouncedSearchTitle.cancel();
    };

  }, []);

  useEffect(() => {
    if (isParamsParsed) {
      fetchData();
    }
  }, [isParamsParsed]);

  useEffect(() => {
    if (isParamsParsed) {
      fetchData();
    }
  }, [page, pageSize]);

  useEffect(() => {
    if (isParamsParsed) {
      setPage(1);
      fetchData();
    }
  }, [selectOperatorId, selectedModelId, searchKey,
    selectedStatus, searchType, onlyShowExampleImg, ownerType, onlyShowDislike,
    onlyShowLike, searchId, searchCustomerType, onlyShowProcessing, onlyShowDemo, onlyShowRefine, relatedOperator,
    selectedGarment, searchTitle, selectedDistributorImageCreatorStaff, onlyNearingDelivery, onlyDownloaded,
    onlyShowUserCreative, seletedCreativeBatchOperatorId
  ]);

  // 详情图片列表的参数
  function getImgSize(imgRatio: string) {
    if (!['THREE_FOUR', 'THREE_FOUR_LG_N'].includes(imgRatio)) {
      const height = 150;
      return { width: height + 'px', height: height + 'px' };
    } else {
      const height = 200;
      return { width: height * 0.75 + 'px', height: height + 'px' };
    }
  }

  useEffect(() => {
    if (ownerType === 'customer') {
      setSelectedDistributorImageCreatorStaff(undefined);
    }
  }, [ownerType]);

  useEffect(() => {

    // 视频创作的提醒-只在生产环境下启用
    if (searchType === 'CREATE_VIDEO') {
      queryCreativesByPage({
        pageNum: 1,
        pageSize: 10,
        onlyNearingDelivery: true,
        type: 'CREATE_VIDEO',
        status: 'PROCESSING',
        customerType: 'customer',

      }).then(res => {
        if (res && res?.totalCount && res?.totalCount > 0) {
          Modal.confirm({
            title: '交付超时提醒',
            content: `有${res?.totalCount}个客户视频任务交付即将/已经超时，是否查看？`,
            okText: '查看',
            cancelText: '先忽略',
            centered: true,
            onOk: () => {
              setOnlyNearingDelivery(true);
              setSearchCustomerType('customer');
              setSelectedStatus('PROCESSING');
            },
          });
        }
      });
    }
  }, [searchType]);

  const queryDetailImage = (modelId, idx = -1, imgList: string[] | [] = [], extInfo: [] | null = null, showTools = true, needWatermark = true) => {
    queryModelDetailShowImage(modelId).then(res => {
      if (res) {
        handlePreviewUrl(res, idx, imgList, extInfo, showTools, needWatermark);
      } else {
        handlePreviewUrl(selectedCreation?.modelShowImg, -1, [], [], false, false);
      }
    });
  };

  const handlePreviewUrl = (url, idx = -1, imgList: string[] | [] = [], extInfo: [] | null = null, showTools = true, needWatermark = true, mask = '') => {
    setPreviewImage(url);
    setMask(mask);

    if (idx >= 0) {
      //@ts-ignore
      setPreviewIdx(idx);
      //@ts-ignore
      setPreviewImgs(imgList);
      setPreviewInfoList(
        selectedCreation?.resultImages?.map((item, index) => (
          { batchId: selectedCreation.id, indexInBatch: index }
        )) || []);
    } else {
      setPreviewIdx(undefined);
      setPreviewImgs([]);
    }

    setPreviewVisible(true);
    if (extInfo) {
      // @ts-ignore
      setLikeObj(extInfo['like']);
    } else {
      setLikeObj(null);
    }

    setShowTools(showTools);
    setNeedWatermark(needWatermark);
  };

  const handleCancelPreview = () => {
    setPreviewVisible(false);
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setPage(page);
    if (pageSize) {
      setPageSize(pageSize);
    }
  };

  function imgsCountMoreThan10() {
    return selectedCreation && selectedCreation.resultImages && selectedCreation.resultImages.length > 10;
  }

  function is_downloaded(imgUrl: string, creativeBatchVO: CreativeVO) {
    return imgUrl && creativeBatchVO?.extInfo['downloadedImgs']?.includes(imgUrl);
  }

  const doPredict = async (isCommit = false) => {
    const res = await predict('CREATE_VIDEO', 1);
    if (res) {
      setPredictVO(res);
      if (isCommit && res.needTopup) {
        setShowTopupModal(true);
        return false;
      }
    }
    return true;
  };

  const handleCreateVideo = async () => {
    setPredictVO(null);
    await doPredict();
    isVipUser().then(res => {
      setVipUser(!!res);
      setShowCreateVideoModal(true);
    });
  };

  const handleRefine = () => {
    if (!selectedCreation) return;

    applyRefineExample(selectedCreation?.id).then(res => {
      if (res) {
        message.success('已设置为精修待处理状态');
        //走服务端查一下这个创作的最新信息
        queryCreativeById(selectedCreation?.id).then(res => {
          if (res) {
            setSelectedCreation(res);
            setImageSelectorOptions(prev => ({
              ...prev,
              images: res.resultImages || [],
            }));
            fetchData();
          }
        });

      }
    });
  };

  const handleCompleteRefine = () => {
    if (!selectedCreation) return;

    completeRefineExample(selectedCreation?.id).then(res => {
      if (res) {
        message.success('精修状态已置为完成');
        //走服务端查一下这个创作的最新信息
        queryCreativeById(selectedCreation?.id).then(res => {
          if (res) {
            setSelectedCreation(res);
            setImageSelectorOptions(prev => ({
              ...prev,
              images: res.resultImages || [],
            }));
            fetchData();
          }
        });
      }
    });
  };

  const commitCreateVideo = async () => {
    if (!selectedCreation || !selectedCreation?.resultImages) return;
    const canRun = await doPredict(true);
    if (!canRun) return;

    const selectedImages = [];
    const target = [...selectedCreation?.resultImages];
    const max = Math.min(target.length, 4);
    for (let i = 0; i < max; i++) {
      const randomIndex = Math.floor(Math.random() * target.length);
      // @ts-ignore
      selectedImages.push(target[randomIndex]);
      target.splice(randomIndex, 1);
    }

    startCreateVideo({ images: selectedImages }).then(res => {
      if (res) {
        navigate('/create-video');
        updatePoint();
      }
    });
  };

  const [downloading, setDownloading] = useState(false);

  // 下载选中的图片
  const handleDownloadSelected = () => {
    if (downloading) return;
    setDownloading(true);

    if (selectedImages.length === 0) {
      message.warning('请先选择要下载的图片');
      setDownloading(false);
      return;
    }

    downloadAllImages(selectedCreation?.id || 0, selectedImages).then(res => {
      setDownloading(false);
      if (res) {
        download(res);
      }
    });
  };

  const items: TabsProps['items'] = [{ key: 'ALL', label: '全部' }];

  // 控制不同角色看到的历史记录里的类型Tab
  (bizType === 'ALL') && ALLCreativeType.filter(item => ['OPERATOR', 'ADMIN'].includes(userInfo.roleType) || !(['LOGO_COMBINE', 'TRYON', 'FACE_PINCHING'].includes(item.key)))
    .forEach(item => {
      if (isOperate || (item.type === type && item.key !== 'FIX_VIDEO_FACE')) {

        const label = item.key !== 'CREATE_VIDEO' ? item.label :
          <Badge count={needProcessCreative} style={{ position: 'absolute', right: -12 }}>{item.label}</Badge>;

        // TODO 开放后不再进行该判断 过滤掉BASIC_CHANGING_CLOTHES
        if (item.key === 'BASIC_CHANGING_CLOTHES') {
          if (userInfo.roleType === 'ADMIN' || userInfo.roleType === 'OPERATOR') {
            items.push({ key: item.value, label });
          }
          return;
        }

        // TODO 仅运营可以查看CLOTH_RECOLOR
        if (item.key === 'CLOTH_RECOLOR') {
          if (['OPERATOR', 'ADMIN'].includes(userInfo.roleType)) {
            items.push({ key: item.value, label });
          }
          return;
        }

        // 仅管理员身份可以查看POSE_SAMPLE_DIAGRAM
        if (item.key === 'POSE_SAMPLE_DIAGRAM') {
          if (userInfo.roleType === 'ADMIN') {
            items.push({ key: item.value, label });
          }
          return;
        }

        // TODO 单图换衣待开放
        if (item.key === 'CLOTHING_SWAP') {
          if (userInfo.roleType === 'ADMIN' || userInfo.roleType === 'OPERATOR') {
            items.push({ key: item.value, label });
          }
          return;
        }

        // 其余的部分不做过滤
        items.push({ key: item.value, label });
      }
    });

  // 获取人脸和蒙版图片数据
  const getFaceAndMaskData = () => {
    if (selectedCreation?.type !== 'BASIC_CHANGING_CLOTHES' ||
      !selectedCreation?.creativeTasksList ||
      previewIdx === undefined ||
      !previewImgs) {
      return undefined;
    }

    const faceAndMaskData: { resultFaceImage: string, resultMaskImage: string }[] = [];
    // 获取当前预览的图片URL
    const currentImageUrl = previewImgs[previewIdx];

    // 通过图片URL直接找到对应的任务，而不是通过索引计算
    const currentTask = selectedCreation.creativeTasksList.find(task =>
      task.resultImages && task.resultImages.some(img => img === currentImageUrl),
    );

    if (currentTask && currentTask.extInfo && currentTask.extInfo.resultFaceImage && currentTask.extInfo.resultMaskImage) {
      faceAndMaskData.push({
        resultFaceImage: currentTask.extInfo.resultFaceImage,
        resultMaskImage: currentTask.extInfo.resultMaskImage,
      });
    }

    return faceAndMaskData.length > 0 ? faceAndMaskData : undefined;
  };

  // 解析固定姿势创作的场景信息
  const parseFixedPostureScenes = (creativeTasksList: any[]) => {
    if (!creativeTasksList || creativeTasksList.length === 0) {
      return [];
    }

    const scenes = creativeTasksList.map(task => {
      if (task.extInfo && task.extInfo.loraPath) {
        const loraPath = task.extInfo.loraPath;

        // 使用正则表达式解析路径中的场景信息
        // 1. 首先匹配文件名: "风格-熟女-咖啡馆裹身裙街拍_5489_20250106_152832-flux.safetensors"
        const fileNameMatch = loraPath.match(/([^/]+\.safetensors)$/);
        if (fileNameMatch) {
          const fileName = fileNameMatch[1];

          // 2. 去掉"-flux.safetensors"后缀，获取主要部分
          const sceneMatch = fileName.replace('-flux.safetensors', '');

          // 3. 按照下划线分割，获取主要场景名称部分
          const sceneParts = sceneMatch.split('_');
          if (sceneParts.length > 0) {
            const sceneName = sceneParts[0]; // 取第一部分: "风格-熟女-咖啡馆裹身裙街拍"
            return sceneName;
          }
        }
      }
      return null;
    }).filter(scene => scene !== null);

    // 去重
    const uniqueScenes = [...new Set(scenes)];
    console.log('Unique scenes:', uniqueScenes);
    return uniqueScenes;
  };

  const sortNormalImages = (resultImages) => {
    if (selectedCreation?.type === 'FACE_PINCHING') {
      return resultImages?.sort(sortFpImages);
    }

    return resultImages;
  };

  return (
    <PageContainer>
      <div className="history-form-container">
        <Flex vertical className={'history-form-body' + '-' + bizType}>
          {items.length > 2 &&
            <Tabs defaultActiveKey="1" items={items} onChange={(key) => setSearchType(key)} size={'large'}
                  indicator={{ size: 102 }} />
          }

          <Flex justify={isDistributor ? 'space-between' : 'flex-end'} align={'center'}>
            {isDistributor &&
              <Segmented
                style={{ backgroundColor: '#E1E3EB', margin: '12px 0 0 12px' }}
                onChange={(value) => {
                  setOwnerType(value);
                  setSearchKey(undefined);
                }} value={ownerType}
                options={[{ label: '客户创作', value: 'customer' }, { label: '我的创作', value: 'mine' }]}
              />
            }

            <Flex align={'center'} justify={'flex-end'} gap={8}>
              <Flex align={'center'} gap={8}>
                {isOperate &&
                  <Tooltip title={'刷新页面数据'}>
                    <Button icon={<RedoOutlined />} onClick={() => {
                      fetchData();
                      message.success('刷新成功');
                    }} />
                  </Tooltip>
                }

                {isOperate &&
                  <Flex gap={16} className={'img-detail-repair-block img-detail-btn'}>
                    <div className={''} onClick={() => setOnlyShowLike(!onlyShowLike)}>
                      <IconFont type={onlyShowLike ? 'icon-rongqi' : 'icon-xihuan_moren'}
                                className={'img-icon-like color-1a'} />
                    </div>
                    <div className={''} onClick={() => setOnlyShowDislike(!onlyShowDislike)}>
                      <IconFont type={onlyShowDislike ? 'icon-buxihuan_dianji' : 'icon-buxihuan_moren'}
                                className={'img-icon-like color-1a'} />
                    </div>
                  </Flex>
                }

                {isOperate &&
                  <Checkbox onChange={e => setOnlyShowDemo(e.target.checked)}>演示</Checkbox>
                }

                {(isOperate || (isDistributor && ownerType === 'customer')) && type !== 'VIDEO' &&
                  <Checkbox onChange={e => setOnlyShowRefine(e.target.checked)}>待精修</Checkbox>
                }

                {type === 'IMAGE' &&
                  <Checkbox onChange={e => setOnlyShowExampleImg(e.target.checked)}>精选图</Checkbox>
                }

                {isOperate && searchType === 'CREATE_VIDEO' &&
                  <Checkbox checked={onlyNearingDelivery} onChange={e => {
                    setOnlyNearingDelivery(e.target.checked);
                    setSearchCustomerType('customer');
                  }}>20小时未交付</Checkbox>
                }

                {(isOperate || isDistributor) &&
                  <Checkbox checked={onlyDownloaded}
                            onChange={e => {
                              setOnlyDownloaded(e.target.checked);
                            }}>客户下载过</Checkbox>
                }

                {/*{isOperate && searchType === 'CREATE_VIDEO' &&*/}
                {/*  <Radio.Group options={relatedOperatorItems}*/}
                {/*               defaultValue={'all'} optionType="button" size={'small'}*/}
                {/*               onChange={e => setRelatedOperator(e.target.value)} />*/}
                {/*}*/}

                {isOperate &&
                  <Select value={selectedStatus} placeholder="所有状态" onChange={e => setSelectedStatus(e)}
                          style={{ width: 100 }} allowClear>
                    {ALLCreativeStatus.filter(item => item.value !== 'INIT').map((item, index) => (
                      <Option key={index} value={item.value}>{item.label}</Option>
                    ))}
                  </Select>
                }
              </Flex>

              {(isOperate || (isDistributor && ownerType === 'customer')) &&
                <>
                  {isOperate &&
                    <Input 
                      placeholder={'id'} 
                      style={{ width: 100 }} 
                      allowClear
                      value={searchIdValue}
                      onChange={(e) => {
                        const value = e.target.value;
                        setSearchIdValue(value); // 立即更新输入框显示
                        
                        // 如果清空了输入框，立即触发搜索
                        if (!value) {
                          debouncedSearchId.cancel(); // 取消之前的防抖
                          setSearchId(undefined);
                        } else {
                          // 否则使用防抖搜索
                          debouncedSearchId(value);
                        }
                      }} 
                    />
                  }

                  <Input 
                    placeholder={'商户'} 
                    style={{ width: 100 }} 
                    allowClear
                    value={searchKeyValue}
                    onChange={(e) => {
                      const value = e.target.value;
                      setSearchKeyValue(value); // 立即更新输入框显示
                      
                      // 如果清空了输入框，立即触发搜索
                      if (!value) {
                        debouncedSearchKey.cancel(); // 取消之前的防抖
                        setSearchKey(undefined);
                      } else {
                        // 否则使用防抖搜索
                        debouncedSearchKey(value);
                      }
                    }} 
                  />
                </>
              }

              {/*渠道管理-创作记录-客户创作*/}
              {isDistAdminOrOpera && ownerType === 'customer' &&
                <Select value={selectOperatorId} placeholder="请选择运营" onChange={handleOperatorChange}
                        style={{ width: 200 }} allowClear showSearch optionFilterProp="label"
                        options={operatorList.map(operator => ({
                          value: operator.id,
                          label: operator.nickName,
                        }))} />
              }

              {/*渠道管理-创作记录-我的创作*/}
              {isDistAdminOrSecondAdmin && ownerType === 'mine' &&
                <Select value={selectedDistributorImageCreatorStaff} placeholder="请选择操作人"
                        onChange={val => setSelectedDistributorImageCreatorStaff(val)}
                        style={{ width: 200 }} allowClear showSearch optionFilterProp="label"
                        options={operatorList.map(operator => ({
                          value: operator.id,
                          label: operator.nickName,
                        }))} />
              }

              {isDistributor && ownerType === 'customer' && searchType !== 'CREATE_VIDEO' && type !== 'VIDEO' &&
                <Select value={selectedGarment} placeholder="选择服装类型(可多选)"
                        onChange={(value) => setSelectedGarment(value)} mode={'multiple'}
                        style={{ width: 180 }} allowClear showSearch optionFilterProp="label"
                        options={garmentItems} />
              }

              {isOperate &&
                <Select value={seletedCreativeBatchOperatorId} placeholder="操作人"
                        onChange={(value) => setSeletedCreativeBatchOperatorId(value)}
                        style={{ width: 120 }} allowClear showSearch optionFilterProp="label"
                        options={allUserOptions} />
              }

              {type === 'IMAGE' &&
                <Select value={selectedModelId} placeholder="请选择服装" onChange={handleModelChange}
                        style={{ width: 200 }} allowClear showSearch optionFilterProp="label"
                        options={loraModels ? loraModels.map(lm => ({ label: lm.name, value: lm.id })) : []} />
              }

              {(type === 'VIDEO' || searchType === 'CREATE_VIDEO') &&
                <Input 
                  maxLength={64} 
                  placeholder={'标题'} 
                  style={{ width: 100 }}
                  allowClear
                  value={searchTitleValue}
                  onChange={(e) => {
                    const value = e.target.value;
                    setSearchTitleValue(value); // 立即更新输入框显示
                    
                    // 如果清空了输入框，立即触发搜索
                    if (!value) {
                      debouncedSearchTitle.cancel(); // 取消之前的防抖
                      setSearchTitle(null);
                    } else {
                      // 否则使用防抖搜索
                      debouncedSearchTitle(value);
                    }
                  }} 
                />
              }

            </Flex>
          </Flex>

          <Flex justify={isDistributor ? 'space-between' : 'flex-end'} align={'center'} gap={8}>
            {isOperate &&
              <>
                <Checkbox checked={onlyShowUserCreative} onChange={e => setOnlyShowUserCreative(e.target.checked)}>
                  不看自动创作
                </Checkbox>

                <Flex gap={8} align={'center'}>
                  <Radio.Group options={[{ value: 'all', label: '全部' },
                    { value: 'autoGen', label: '自动' },
                    { value: 'operator', label: '运营' },
                    { value: 'customer', label: '全部客户' },
                    { value: 'normal', label: '未付费客户' },
                    { value: 'vip', label: '付费客户' },
                  ]}
                               defaultValue={'all'} optionType="button" size={'small'} value={searchCustomerType}
                               onChange={e => setSearchCustomerType(e.target.value)} />
                </Flex>

                <Checkbox checked={onlyShowProcessing} onChange={e => setOnlyShowProcessing(e.target.checked)}>
                  处理中+时长排序
                </Checkbox>
              </>
            }
          </Flex>

          <div className={'history-card-list margin-top-16'}>
            {creations.map((item, index) => (
              <ImageCardBlock key={index} item={item} isOperate={isOperate} isDistributor={isDistributor}
                              ownerType={ownerType} onClick={(e) => handleClickCreationCard(e.id, e.type)}
                              failCallback={fetchData} />
            ))}
          </div>
          <div className={'stick-bottom-pagination'} style={{
            marginTop: '48px',
          }}>
            <Pagination
              current={page}
              pageSize={pageSize}
              total={totalCreations}
              onChange={handlePageChange}
              showTotal={(total) => `共 ${total} 条创作`}
              showSizeChanger // 允许用户更改每页显示条数
              showQuickJumper // 允许用户快速跳转到某一页
              style={{ textAlign: 'center' }}
            />
          </div>
        </Flex>
      </div>

      {/*任务详情浮层弹窗页面*/}
      {showDetailModal &&
        <Modal
          open={showDetailModal}
          onCancel={() => setShowDetailModal(false)}
          footer={null}
          width={selectedCreation?.type === 'CREATE_VIDEO' ? 'auto' : 1160}
          centered={true}
          closable={true}
          styles={{
            mask: {
              backgroundColor: 'rgba(0,0,0,0.4)',
            },
          }}
        >
          <div className="history-detail-content"
               style={{
                 height: selectedCreation?.type === 'CREATE_VIDEO' ? isOperate ? 'auto' : 780 : (imgsCountMoreThan10() ? 830 : 686),
                 minHeight: 780,
               }}>
            <div className={'history-detail-left'}>
              <div className={'history-detail-left-inner'}>
                {(selectedCreation?.type === 'REPAIR_HANDS' || selectedCreation?.type === 'IMAGE_UPSCALE' || selectedCreation?.type === 'CLOTH_RECOLOR') &&
                  <>
                    <div className={'history-left-inner-top'}>
                      <div className="font-pf text14 weight" style={{ textAlign: 'left', width: '100%' }}>原图</div>
                      {selectedCreation?.type === 'REPAIR_HANDS' && isOperate &&
                        <WatermarkImage style={{ cursor: 'pointer' }}
                                        onClick={() => handlePreviewUrl(selectedCreation?.extInfo['originImage'], 0, [selectedCreation?.extInfo['originImage']], [], isOperate)}
                                        src={selectedCreation?.extInfo['originImage']} />
                      }
                      {!(selectedCreation?.type === 'REPAIR_HANDS' && isOperate) &&
                        <WatermarkImage style={{ cursor: 'pointer' }}
                                        onClick={() => handlePreviewUrl(selectedCreation?.extInfo['originImage'], -1, [], [], false)}
                                        src={selectedCreation?.extInfo['originImage']} />
                      }
                      {selectedCreation?.type === 'CLOTH_RECOLOR' && selectedCreation?.extInfo['originMaskImage'] &&
                        <WatermarkImage style={{ cursor: 'pointer' }}
                                        onClick={() => handlePreviewUrl(selectedCreation?.extInfo['originMaskImage'], -1, [], [], false)}
                                        src={selectedCreation?.extInfo['originMaskImage']} />
                      }
                    </div>

                    <div className="history-detail-section">
                      {selectedCreation?.type === 'CLOTH_RECOLOR' &&
                        <div className="history-detail-info-row">
                          <div className="history-detail-info-label">目标颜色</div>
                          <div className="history-detail-info-value">
                            <span style={{ width: 14, height: 14, display: 'inline-block', backgroundColor: selectedCreation?.extInfo['targetHexColor'], verticalAlign: 'middle', marginRight: 4 }}></span>
                            {selectedCreation?.extInfo['targetHexColor']}
                          </div>
                        </div>
                      }
                      {selectedCreation?.type !== 'IMAGE_UPSCALE' &&
                        <div className="history-detail-info-row">
                          <div className="history-detail-info-label">创建时间</div>
                          <div className="history-detail-info-value">{selectedCreation?.createTime}</div>
                        </div>
                      }

                      <div className="history-detail-info-row">
                        <div className="history-detail-info-label">任务ID</div>
                        <div className="history-detail-info-value">{selectedCreation?.id}</div>
                      </div>
                    </div>
                  </>
                }
                {(selectedCreation?.type === 'REPAIR_DETAIL') &&
                  (<>
                    <div className={'history-left-inner-top'}>
                      <div className="font-pf text14 weight" style={{ textAlign: 'left', width: '100%' }}>原图</div>
                      <div
                        style={{
                          cursor: 'pointer',
                          position: 'relative',
                          display: 'flex',
                          justifyContent: 'center',
                          width: 'auto',
                        }}
                        onClick={() => handlePreviewUrl(selectedCreation?.extInfo['originImage'], -1, [],
                          [], false, false, selectedCreation?.extInfo['originMask'])}
                      >
                        <img alt="image"
                             src={selectedCreation?.extInfo['originImage']} />
                        {selectedCreation?.extInfo['originMask'] &&
                          <MaskOverlay
                            maskUrl={selectedCreation?.extInfo['originMask']}
                            maskColor="#61B3FF"
                          />
                        }
                      </div>
                    </div>
                    <div className={'history-left-inner-top'}>
                      <div className="font-pf text14 weight" style={{ textAlign: 'left', width: '100%' }}>参考</div>
                      <div
                        style={{
                          cursor: 'pointer',
                          position: 'relative',
                          display: 'flex',
                          justifyContent: 'center',
                          width: 'auto',
                        }}
                        onClick={() => handlePreviewUrl(selectedCreation?.extInfo['clothOriginImage'], -1, [],
                          [], false, false, selectedCreation?.extInfo['clothMask'])}
                      >
                        <img alt="clothImage"
                             src={selectedCreation?.extInfo['clothOriginImage']} />
                        {selectedCreation?.extInfo['clothMask'] &&
                          <MaskOverlay
                            maskUrl={selectedCreation?.extInfo['clothMask']}
                            maskColor="#61B3FF"
                          />
                        }
                      </div>
                    </div>

                    <div className="history-detail-section">
                      <div className="history-detail-info-row">
                        <div className="history-detail-info-label">任务ID</div>
                        <div className="history-detail-info-value">{selectedCreation?.id}</div>
                      </div>

                      <div className="history-detail-info-row">
                        <div className="history-detail-info-label">创建时间</div>
                        <div className="history-detail-info-value">{selectedCreation?.createTime}</div>
                      </div>
                    </div>
                  </>)
                }

                {selectedCreation?.type !== 'REPAIR_HANDS' &&
                  <>
                    {(selectedCreation?.type === 'CREATE_IMAGE' || selectedCreation?.type === 'FIXED_POSTURE_CREATION') &&
                      <div className={'history-left-inner-top'}>
                        <div className="font-pf text14 weight" style={{ textAlign: 'left', width: '100%' }}>服装</div>
                        <div style={{ textAlign: 'left', width: '100%' }}>
                          <Tooltip title={selectedCreation?.modelName}>
                            <span className="font-pf text14">{formatText(selectedCreation?.modelName, 12)}</span>
                          </Tooltip>
                        </div>
                        <img alt="cloth model"
                             style={{ cursor: 'pointer' }}
                             onClick={() => queryDetailImage(selectedCreation?.modelId, -1, [], [], false, false)}
                             src={selectedCreation?.modelShowImg} />
                      </div>
                    }

                    {selectedCreation?.type === 'LOGO_COMBINE' &&
                      <div className={'history-left-inner-top'}>
                        <div className="font-pf text14 weight" style={{ textAlign: 'left', width: '100%' }}>印花</div>
                        <img alt="cloth model"
                             style={{ cursor: 'pointer' }}
                             onClick={() => handlePreviewUrl(selectedCreation?.extInfo['logoImageOss'], -1, [], [], false)}
                             src={selectedCreation?.extInfo['logoImageOss']} />
                      </div>
                    }

                    {selectedCreation?.type === 'CREATE_VIDEO' &&
                      <div className={'history-left-inner-top'}>
                        <div className="font-pf text14 weight" style={{ textAlign: 'left', width: '100%' }}>原图</div>
                        <img alt="cloth model"
                             style={{ cursor: 'pointer' }}
                             onClick={() => handlePreviewUrl(selectedCreation?.showImage, -1, [], [], false, false)}
                             src={selectedCreation?.showImage} />
                      </div>
                    }

                    {(selectedCreation?.type === 'FACE_SCENE_SWITCH' || selectedCreation?.type === 'REMOVE_WRINKLE' || selectedCreation?.type === 'ERASE_BRUSH') &&
                      selectedCreation?.extInfo['originImage'] &&
                      <div className={'history-left-inner-top'}>
                        <div className="font-pf text14 weight" style={{ textAlign: 'left', width: '100%' }}>原图</div>
                        <img alt="cloth model"
                             style={{ cursor: 'pointer' }}
                             onClick={() => handlePreviewUrl(selectedCreation?.extInfo['originImage'], -1, [], [], false, false)}
                             src={selectedCreation?.extInfo['originImage']} />
                      </div>
                    }
                    {selectedCreation?.extInfo['originCustomSceneImg'] &&
                      <div className={'history-left-inner-top'}>
                        <div className="font-pf text14 weight" style={{ textAlign: 'left', width: '100%' }}>背景参考
                        </div>
                        <img alt="cloth model"
                             style={{ cursor: 'pointer' }}
                             onClick={() => handlePreviewUrl(selectedCreation?.extInfo['originCustomSceneImg'], -1, [], [], false, false)}
                             src={selectedCreation?.extInfo['originCustomSceneImg']} />
                      </div>
                    }

                    {selectedCreation?.type === 'PARTIAL_REDRAW' &&
                      <div className={'history-left-inner-top'}>
                        <div className="font-pf text14 weight" style={{ textAlign: 'left', width: '100%' }}>原图</div>
                        <img alt="cloth model"
                             style={{ cursor: 'pointer' }}
                             onClick={() => handlePreviewUrl(selectedCreation?.extInfo['originImage'], -1, [], [], false, false)}
                             src={selectedCreation?.extInfo['originImage']} />
                      </div>
                    }

                    {selectedCreation?.type === 'BASIC_CHANGING_CLOTHES' &&
                      <div className={'history-left-inner-top'}>
                        <div className="font-pf text14 weight" style={{ textAlign: 'left', width: '100%' }}>原图</div>
                        <img alt="cloth model"
                             style={{ cursor: 'pointer' }}
                             onClick={() => handlePreviewUrl(selectedCreation?.extInfo['originImage'], -1, [], [], false, false)}
                             src={selectedCreation?.extInfo['originImage']} />
                      </div>
                    }

                    {selectedCreation?.type === 'CLOTHING_SWAP' &&
                      <div className={'history-left-inner-top'}>
                        <div className="font-pf text14 weight" style={{ textAlign: 'left', width: '100%' }}>服装图</div>
                        <img alt="cloth model"
                             style={{ cursor: 'pointer' }}
                             onClick={() => handlePreviewUrl(selectedCreation?.extInfo['originImage'], -1, [], [], false, false)}
                             src={selectedCreation?.extInfo['originImage']} />
                      </div>
                    }


                    <div className="history-detail-section">
                      {selectedCreation?.faceName &&
                        <div className="history-detail-info-row">
                          <div className="history-detail-info-label">模特</div>
                          <div className="history-detail-info-value">{selectedCreation?.faceName}</div>
                        </div>
                      }

                      {(selectedCreation?.type === 'CREATE_IMAGE' || selectedCreation?.type === 'POSE_SAMPLE_DIAGRAM') &&
                        <div className="history-detail-info-row">
                          <div className="history-detail-info-label">场景</div>
                          {selectedCreation?.sceneName ? (
                            <div className="history-detail-info-value">{selectedCreation?.sceneName}</div>
                          ) : selectedCreation?.extInfo['originCustomScene'] ? (
                            <div className="history-detail-info-value">
                              {'自定义场景'}
                              <Tooltip title={selectedCreation?.extInfo['originCustomScene']}>
                                <QuestionCircleOutlined style={{ fontSize: 14, marginLeft: 4 }}
                                                        className={'color-96'} />
                              </Tooltip>
                            </div>
                          ) : (<></>)
                          }
                        </div>
                      }


                      {selectedCreation?.type === 'FIXED_POSTURE_CREATION'  &&
                        <>
                          {/* 场景信息展示 */}
                          {selectedCreation?.creativeTasksList && selectedCreation.creativeTasksList.length > 0 && (
                            <div className="history-detail-section">
                              <div className="history-detail-info-row scene-info-row">
                                <div className="history-detail-info-label">场景</div>
                                <div className="history-detail-info-value">
                                  <div className="fixed-posture-scenes">
                                    {parseFixedPostureScenes(selectedCreation.creativeTasksList).map((scene, index) => (
                                      <div key={index} className="scene-item">
                                        {scene}
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </>
                      }

                      {selectedCreation?.type === 'PARTIAL_REDRAW' &&
                        <div className="history-detail-info-row">
                          <div className="history-detail-info-label">局部重绘提示词</div>
                          <div className="history-detail-info-value">
                            <Tooltip title={selectedCreation?.extInfo['originRedrawDesc']}>
                              <QuestionCircleOutlined style={{ fontSize: 14 }} className={'color-96'} />
                            </Tooltip>
                          </div>
                        </div>
                      }

                      {selectedCreation?.imageProportionName && selectedCreation?.type !== 'CREATE_VIDEO' &&
                        <div className="history-detail-info-row">
                          <div className="history-detail-info-label">图片比例</div>
                          <div className="history-detail-info-value">{selectedCreation?.imageProportionName}</div>
                        </div>
                      }

                      {selectedCreation?.type === 'CREATE_VIDEO' &&
                        <div className="history-detail-info-row">
                          <div className="history-detail-info-label">视频时长</div>
                          <div className="history-detail-info-value">{selectedCreation?.timeSecs4Video || 5}秒</div>
                        </div>
                      }

                      {selectedCreation && selectedCreation.extInfo && (selectedCreation.extInfo['colorIndex'] || selectedCreation.extInfo['colorIndex'] === '0') &&
                        <div className="history-detail-info-row">
                          <div className="history-detail-info-label">服装颜色</div>
                          <div className="history-detail-info-value">
                            {selectedCreation.extInfo['colorIndex'] === 0 ? '随机颜色' : `颜色${selectedCreation.extInfo['colorIndex']}`}
                          </div>
                        </div>
                      }
                    </div>

                    {selectedCreation && selectedCreation.extInfo && selectedCreation.extInfo['originClothCollocation'] &&
                      <div className="history-detail-section">
                        <div className="history-detail-info-label" style={{ marginBottom: '6px' }}>服装搭配</div>
                        {selectedCreation.extInfo['originClothCollocation']['shoe'] &&
                          <div className="history-detail-info-row">
                            <div className="history-detail-info-label color-96">鞋子</div>
                            <div className="history-detail-info-value">
                              {selectedCreation.extInfo['originClothCollocation']['shoe']}
                            </div>
                          </div>
                        }
                        {selectedCreation.extInfo['originClothCollocation']['tops'] &&
                          <div className="history-detail-info-row">
                            <div className="history-detail-info-label color-96">上装</div>
                            <div className="history-detail-info-value">
                              {selectedCreation.extInfo['originClothCollocation']['tops']}
                            </div>
                          </div>
                        }
                        {selectedCreation.extInfo['originClothCollocation']['bottoms'] &&
                          <div className="history-detail-info-row">
                            <div className="history-detail-info-label color-96">下装</div>
                            <div className="history-detail-info-value">
                              {selectedCreation.extInfo['originClothCollocation']['bottoms']}
                            </div>
                          </div>
                        }
                        {selectedCreation.extInfo['originClothCollocation']['others'] &&
                          <div className="history-detail-info-row">
                            <div className="history-detail-info-label color-96">配饰</div>
                            <div className="history-detail-info-value">
                              {selectedCreation.extInfo['originClothCollocation']['others']}
                            </div>
                          </div>
                        }
                        {selectedCreation.extInfo['originClothCollocation']['props'] &&
                          <div className="history-detail-info-row">
                            <div className="history-detail-info-label color-96">道具</div>
                            <div className="history-detail-info-value">
                              {selectedCreation.extInfo['originClothCollocation']['props']}
                            </div>
                          </div>
                        }
                      </div>
                    }

                    {selectedCreation && selectedCreation.extInfo && (selectedCreation.extInfo['cameraAngle']) &&
                      <div className="history-detail-info-row">
                        <div className="history-detail-info-label">视角</div>
                        <div className="history-detail-info-value">
                          {selectedCreation.extInfo['cameraAngle'].includes('back view') ? '背面' : '正面'} /
                          {selectedCreation.extInfo['bodyTypeUnlimited'] === 'Y' ? '不限' : (selectedCreation.extInfo['cameraAngle'].includes('upper body') ? '上半身' : (selectedCreation.extInfo['cameraAngle'].includes('lower body') ? '下半身' : '全身'))}
                          {!isTrialAccount && !selectedCreation.extInfo['enableAntiBlurLora'] && ' / 背景虚化'}
                          {userInfo.roleType !== 'MERCHANT' && selectedCreation.extInfo['enableNewModel'] && ' / 新模型'}
                        </div>
                      </div>
                    }

                    <div className="history-detail-section">
                      <div className="history-detail-info-row">
                        <div className="history-detail-info-label">操作人</div>
                        <div className="history-detail-info-value">{selectedCreation?.operatorNick}</div>
                      </div>

                      <div className="history-detail-info-row">
                        <div className="history-detail-info-label">任务ID</div>
                        <div className="history-detail-info-value">{selectedCreation?.id}</div>
                      </div>

                      <div className="history-detail-info-row">
                        <div className="history-detail-info-label">创建时间</div>
                        <div className="history-detail-info-value">{selectedCreation?.createTime}</div>
                      </div>
                    </div>
                  </>
                }
                {isOperate &&
                  <div className="history-detail-section-compact" style={{ marginTop: -8 }}>
                    <div className="history-detail-info-row-compact">
                      <div className="history-detail-info-value"
                           style={{ fontSize: 10, color: '#a0a0a0', textAlign: 'left', padding: 0 }}>
                        {selectedCreation?.extInfo && selectedCreation?.extInfo['version'] === 'v_2' ? 'MG2.0' : 'MG1.0'}
                        &nbsp;&nbsp;-&gt;&nbsp;&nbsp;
                        {selectedCreation?.extInfo ? selectedCreation?.extInfo['serverUrl'] : ''}
                      </div>
                    </div>
                    <div className="history-detail-info-row-compact">
                      <div className="history-detail-info-value"
                           style={{ fontSize: 10, color: '#a0a0a0', textAlign: 'left', padding: 0 }}>
                        开始时间：{selectedCreation?.extInfo && selectedCreation?.extInfo['startTime'] ? selectedCreation?.extInfo['startTime'] : (!isProcessing(selectedCreation) ? '历史数据' : '未开始')}
                      </div>
                    </div>
                    <div className="history-detail-info-row-compact">
                      <div className="history-detail-info-value"
                           style={{ fontSize: 10, color: '#a0a0a0', textAlign: 'left', padding: 0 }}>
                        结束时间：{selectedCreation?.extInfo ? selectedCreation?.extInfo['endTime'] : ''}
                      </div>
                    </div>
                  </div>
                }
              </div>
            </div>
            <div className="history-detail-right"
                 style={{
                   paddingTop: (imgsCountMoreThan10() || selectedCreation?.type === 'CREATE_VIDEO') ? 0 : 50,
                   padding: (selectedCreation?.type === 'CREATE_VIDEO') ? '6px 16px' : '',
                   height: selectedCreation?.type === 'CREATE_VIDEO' ? 'auto' : (imgsCountMoreThan10() ? 800 : 686),
                   width: selectedCreation?.type === 'CREATE_VIDEO' ? 'auto' : 920,
                 }}>

              {/* 全选功能 - 只在满足条件时显示 */}
              {shouldShowImageSelection(selectedCreation) && (
                <Flex justify="flex-start" align="center" style={{ marginBottom: 10 }}>
                  <Checkbox
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    checked={isAllSelected}
                    indeterminate={indeterminate}
                  >
                    全选
                  </Checkbox>
                </Flex>
              )}

              {/* 普通图片创作 */}
              {selectedCreation &&
                selectedCreation.type !== 'CREATE_VIDEO' &&
                selectedCreation.type !== 'FIX_VIDEO_FACE' &&
                selectedCreation.type !== 'BASIC_CHANGING_CLOTHES' &&
                selectedCreation.type !== 'FIXED_POSTURE_CREATION' &&
                selectedCreation.type != 'CLOTHING_SWAP' &&
                selectedCreation.resultImages && (
                  <Flex wrap="wrap" gap={8}
                        style={{ width: '100%', justifyContent: 'flex-start', alignItems: 'flex-start' }}>
                    {sortNormalImages(selectedCreation.resultImages).map((imgUrl: string, index: number) => (
                      <div key={index}
                           className="history-detail-image-item"
                           style={{
                             width: 150,
                             height: 'auto',
                             margin: '0 8px 8px 0',
                           }}>
                        <div className={'history-detail-img-container'} style={{ position: 'relative' }}>
                          {shouldShowImageSelection(selectedCreation) &&
                            <Checkbox checked={isImageSelected(imgUrl)} onChange={() => handleSelectImage(imgUrl)}
                                      style={{ position: 'absolute', top: 8, right: 8, zIndex: 10 }} />}
                          <WatermarkImage loading={'lazy'} src={imgUrl} className={'image-gallery width-100'}
                                          onClick={() => handlePreviewUrl(imgUrl, index, selectedCreation.resultImages, selectedCreation.extInfo)} />

                          {isOperate && (isImageLike(selectedCreation, true, imgUrl, index) || isImageLike(selectedCreation, false, imgUrl, index)) &&
                            <Flex gap={8} style={{ position: 'absolute', top: 8, left: 8, border: '1px solid red' }}>
                              {isImageLike(selectedCreation, true, imgUrl, index) &&
                                <IconFont type={'icon-rongqi'} className={'img-icon-like color-1a'} />
                              }

                              {isImageLike(selectedCreation, false, imgUrl, index) &&
                                <IconFont type={'icon-buxihuan_dianji'} className={'img-icon-like color-1a'} />
                              }
                            </Flex>
                          }

                          <div className={'history-detail-img-icon'} onClick={() => {
                            download(imgUrl);
                            monitorImgDownload(selectedCreation?.id, imgUrl).then(res => {
                              if (res) {
                                if (isOperate) {
                                  //走服务端查一下这个创作的最新信息
                                  queryCreativeById(selectedCreation?.id).then(c => {
                                    if (c) {
                                      setSelectedCreation(c);
                                      setImageSelectorOptions(prev => ({
                                        ...prev,
                                        images: c.resultImages || [],
                                      }));
                                    }
                                  });
                                }
                              }
                            });

                          }}
                               style={{ backgroundColor: (isOperate || isDistributor) && is_downloaded(imgUrl, selectedCreation) ? 'green' : '' }}>
                            <Tooltip
                              title={(isOperate || isDistributor) && is_downloaded(imgUrl, selectedCreation) ? '图片曾被下载' : null}>
                              <IconFont type={'icon-a-shangchuan1x'} style={{ fontSize: '12px' }} />
                            </Tooltip>
                          </div>
                          {getUserInfo()?.roleType !== 'ADMIN' &&
                            <div className={'history-detail-img-favor-icon'}>
                              <UserFavorButton favorType={'IMAGE'} itemId={selectedCreation.id} images={[index]}
                                               imageIndex={index} />
                            </div>
                          }
                        </div>
                      </div>
                    ))}
                  </Flex>
                )}


              {/* 基础款换衣 */}
              {selectedCreation && (selectedCreation.type === 'BASIC_CHANGING_CLOTHES' || selectedCreation.type === 'CLOTHING_SWAP') &&
                selectedCreation.creativeTasksList &&
                selectedCreation.creativeTasksList.map((taskItem: CreativeTaskVO, index: number) => (
                  <Flex key={index} wrap="wrap" gap={8} style={{
                    marginBottom: 16,
                    width: '100%',
                    justifyContent: 'flex-start',
                    alignItems: 'flex-start',
                  }}>
                    {/* 参考图 */}
                    {taskItem.extInfo && taskItem.extInfo.referenceOriginalImage && (
                      <div
                        className="history-detail-image-item"
                        style={{
                          width: getImgSize(selectedCreation.imageProportion)['width'],
                          height: getImgSize(selectedCreation.imageProportion)['height'],
                          margin: '0 8px 8px 0',
                        }}>
                        <div className={'history-detail-img-container'} style={{ width: '100%', height: '100%' }}>
                          <WatermarkImage loading={'lazy'} src={taskItem.extInfo.referenceOriginalImage}
                                          className={'image-gallery width-100'}
                                          onClick={() => handlePreviewUrl(taskItem.extInfo.referenceOriginalImage, -1, [], null)}
                                          style={{ objectFit: 'cover', width: '100%', height: '100%' }} />

                          <div style={{
                            position: 'absolute',
                            top: 8,
                            left: 8,
                            backgroundColor: 'rgba(0, 0, 0, 0.6)',
                            padding: '2px 6px',
                            borderRadius: '4px',
                          }}>
                            <div className="text12 font-pf color-w">参考图</div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* 根据状态显示结果图或状态提示 */}
                    {taskItem.status === 'INIT' ? (
                      // 初始化状态
                      Array(4).fill(0).map((_, imgIndex) => (
                        <div key={imgIndex}
                             className="history-detail-image-item"
                             style={{
                               width: getImgSize(selectedCreation.imageProportion)['width'],
                               height: getImgSize(selectedCreation.imageProportion)['height'],
                               margin: '0 8px 8px 0',
                             }}>
                          <div className={'history-detail-img-container'} style={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: '#f0f2f5',
                          }}>
                            <div className="text16 font-pf color-96">初始化</div>
                          </div>
                        </div>
                      ))
                    ) : taskItem.status === 'QUEUE' ? (
                      // 排队中状态
                      Array(4).fill(0).map((_, imgIndex) => (
                        <div key={imgIndex}
                             className="history-detail-image-item"
                             style={{
                               width: getImgSize(selectedCreation.imageProportion)['width'],
                               height: getImgSize(selectedCreation.imageProportion)['height'],
                               margin: '0 8px 8px 0',
                             }}>
                          <div className={'history-detail-img-container'} style={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: '#f0f2f5',
                          }}>
                            <div className="text16 font-pf color-96">排队中</div>
                          </div>
                        </div>
                      ))
                    ) : taskItem.status === 'PROCESSING' ? (
                      // 处理中状态
                      Array(4).fill(0).map((_, imgIndex) => (
                        <div key={imgIndex}
                             className="history-detail-image-item"
                             style={{
                               width: getImgSize(selectedCreation.imageProportion)['width'],
                               height: getImgSize(selectedCreation.imageProportion)['height'],
                               margin: '0 8px 8px 0',
                             }}>
                          <div className={'history-detail-img-container'} style={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: '#f0f2f5',
                          }}>
                            <div className="text16 font-pf color-96">出图中</div>
                          </div>
                        </div>
                      ))
                    ) : taskItem.status === 'FAILED' ? (
                      // 失败状态
                      Array(4).fill(0).map((_, imgIndex) => (
                        <div key={imgIndex}
                             className="history-detail-image-item"
                             style={{
                               width: getImgSize(selectedCreation.imageProportion)['width'],
                               height: getImgSize(selectedCreation.imageProportion)['height'],
                               margin: '0 8px 8px 0',
                             }}>
                          <div className={'history-detail-img-container'} style={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: '#f0f2f5',
                          }}>
                            <div className="text16 font-pf color-96">生成失败</div>
                          </div>
                        </div>
                      ))
                    ) : (
                      taskItem.resultImages && taskItem.resultImages.map((imgUrl: string, imgIndex: number) => (
                        <div key={imgIndex}
                             className="history-detail-image-item"
                             style={{
                               width: getImgSize(selectedCreation.imageProportion)['width'],
                               height: getImgSize(selectedCreation.imageProportion)['height'],
                               margin: '0 8px 8px 0',
                             }}>
                          <div className={'history-detail-img-container'} style={{ width: '100%', height: '100%' }}>
                            <WatermarkImage loading={'lazy'} src={imgUrl} className={'image-gallery width-100'}
                                            onClick={() => handlePreviewUrl(imgUrl, imgIndex, taskItem.resultImages, null)}
                                            style={{ objectFit: 'cover', width: '100%', height: '100%' }} />

                            {isOperate && (isImageLike(selectedCreation, true, imgUrl, imgIndex) || isImageLike(selectedCreation, false, imgUrl, imgIndex)) &&
                              <Flex gap={8} style={{ position: 'absolute', top: 8, left: 8, border: '1px solid red' }}>
                                {isImageLike(selectedCreation, true, imgUrl, imgIndex) &&
                                  <IconFont type={'icon-rongqi'} className={'img-icon-like color-1a'} />
                                }

                                {isImageLike(selectedCreation, false, imgUrl, imgIndex) &&
                                  <IconFont type={'icon-buxihuan_dianji'} className={'img-icon-like color-1a'} />
                                }
                              </Flex>
                            }

                            <div className={'history-detail-img-icon'} onClick={() => {
                              download(imgUrl);
                              monitorImgDownload(selectedCreation?.id, imgUrl).then(res => {
                                if (res) {
                                  if (isOperate) {
                                    //走服务端查一下这个创作的最新信息
                                    getCreativeBatchByIdWithTask(selectedCreation?.id).then(c => {
                                      if (c) {
                                        setSelectedCreation(c);
                                        setImageSelectorOptions(prev => ({
                                          ...prev,
                                          images: c.resultImages || [],
                                        }));
                                      }
                                    });
                                  }
                                }
                              });
                            }}
                                 style={{ backgroundColor: isOperate && is_downloaded(imgUrl, selectedCreation) ? 'green' : '' }}>
                              <Tooltip
                                title={isOperate && is_downloaded(imgUrl, selectedCreation) ? '图片曾被下载' : null}>
                                <IconFont type={'icon-a-shangchuan1x'} style={{ fontSize: '12px' }} />
                              </Tooltip>
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </Flex>
                ))
              }


              {/* 固定姿势创作 */}
              {selectedCreation &&
                (selectedCreation.type === 'FIXED_POSTURE_CREATION') &&
                selectedCreation.resultImages && (
                  <Flex wrap="wrap" gap={8}
                        style={{ width: '100%', justifyContent: 'flex-start', alignItems: 'flex-start' }}>
                    {selectedCreation.resultImages.map((imgUrl: string, index: number) => (
                      <div key={index}
                           className="history-detail-image-item"
                           style={{
                             width: 150,
                             height: 'auto',
                             margin: '0 8px 8px 0',
                           }}>
                        <div className={'history-detail-img-container'} style={{ position: 'relative' }}>
                          {shouldShowImageSelection(selectedCreation) &&
                            <Checkbox checked={isImageSelected(imgUrl)} onChange={() => handleSelectImage(imgUrl)}
                                      style={{ position: 'absolute', top: 8, right: 8, zIndex: 10 }} />}
                          <WatermarkImage loading={'lazy'} src={imgUrl} className={'image-gallery width-100'}
                                          onClick={() => handlePreviewUrl(imgUrl, index, selectedCreation.resultImages, selectedCreation.extInfo)} />

                          {isOperate && (isImageLike(selectedCreation, true, imgUrl, index) || isImageLike(selectedCreation, false, imgUrl, index)) &&
                            <Flex gap={8} style={{ position: 'absolute', top: 8, left: 8, border: '1px solid red' }}>
                              {isImageLike(selectedCreation, true, imgUrl, index) &&
                                <IconFont type={'icon-rongqi'} className={'img-icon-like color-1a'} />
                              }

                              {isImageLike(selectedCreation, false, imgUrl, index) &&
                                <IconFont type={'icon-buxihuan_dianji'} className={'img-icon-like color-1a'} />
                              }
                            </Flex>
                          }

                          <div className={'history-detail-img-icon'} onClick={() => {
                            download(imgUrl);
                            monitorImgDownload(selectedCreation?.id, imgUrl).then(res => {
                              if (res) {
                                if (isOperate) {
                                  //走服务端查一下这个创作的最新信息
                                  queryCreativeById(selectedCreation?.id).then(c => {
                                    if (c) {
                                      setSelectedCreation(c);
                                      setImageSelectorOptions(prev => ({
                                        ...prev,
                                        images: c.resultImages || [],
                                      }));
                                    }
                                  });
                                }
                              }
                            });

                          }}
                               style={{ backgroundColor: (isOperate || isDistributor) && is_downloaded(imgUrl, selectedCreation) ? 'green' : '' }}>
                            <Tooltip
                              title={(isOperate || isDistributor) && is_downloaded(imgUrl, selectedCreation) ? '图片曾被下载' : null}>
                              <IconFont type={'icon-a-shangchuan1x'} style={{ fontSize: '12px' }} />
                            </Tooltip>
                          </div>
                          {getUserInfo()?.roleType !== 'ADMIN' &&
                            <div className={'history-detail-img-favor-icon'}>
                              <UserFavorButton favorType={'IMAGE'} itemId={selectedCreation.id} images={[index]}
                                               imageIndex={index} />
                            </div>
                          }
                        </div>
                      </div>
                    ))}
                  </Flex>
                )}

              {/* 视频创作 */}
              {selectedCreation?.type === 'CREATE_VIDEO' &&
                <Flex gap={16} wrap={'wrap'} className={'width-100'}>
                  <VideoCard item={selectedCreation} isOperate={isOperate} isDistributor={isDistributor}
                             updateCallback={() => {
                               fetchData();
                               setShowDetailModal(false);
                             }} />
                </Flex>
              }

              {/* 视频换脸 */}
              {selectedCreation?.type === 'FIX_VIDEO_FACE' &&
                <Flex gap={16} wrap={'wrap'} className={'task-image-video'}>
                  {isProcessing(selectedCreation) &&
                    <div>生成中...</div>
                  }

                  {!isProcessing(selectedCreation) &&
                    <WatermarkVideo src={selectedCreation.resultImages ? selectedCreation.resultImages[0] : ''}
                                    needWatermark={false} controls={true} />
                  }
                </Flex>
              }
            </div>

            {/* 转交客户 */}
            <Flex gap={16} justify={'flex-end'} align={'center'} className={'width-100'}
                  style={{ position: 'absolute', bottom: 8, right: 24 }}>
              {isOperate && selectedCreation && !isProcessing(selectedCreation) && ['CREATE_IMAGE', 'CREATE_VIDEO'].includes(selectedCreation.type) &&
                <Button onClick={() => setAssignBatch(selectedCreation)} style={{ zIndex: 3 }}>
                  转交客户
                </Button>
              }

              {/* 下载按钮 */}
              {shouldShowImageSelection(selectedCreation) &&
                <Button
                  loading={downloading}
                  type={'primary'}
                  className={'download-btn'}
                  onClick={() => {
                    handleDownloadSelected();
                  }}
                  icon={<IconFont type={'icon-icon-download'} style={{ fontSize: 16, color: '#FFFFFF' }} />}
                  disabled={selectedCount === 0}
                >
                  下载{
                  // 如果全选了，显示"下载全部"，否则显示"下载选中"
                  isAllSelected ? '全部' : `选中(${selectedCount})`
                }
                </Button>
              }

              {/* 需要精修 */}
              {isDistributor && selectedCreation && isExampleImage(selectedCreation) &&
                <>
                  {(!isRefineInit(selectedCreation) && !isRefineCompleted(selectedCreation)) &&
                    <Popconfirm title={'确认后将发起精修请求'} onConfirm={handleRefine}>
                      <Button type={'primary'}>需要精修</Button>
                    </Popconfirm>
                  }
                  {isRefineInit(selectedCreation) &&
                    <Button type={'primary'} disabled>精修处理中</Button>
                  }
                  {isRefineCompleted(selectedCreation) &&
                    <Button type={'primary'} disabled>精修已完成</Button>
                  }
                </>
              }

              {isOperate && selectedCreation && isExampleImage(selectedCreation) &&
                <>
                  {isRefineInit(selectedCreation) &&
                    <Popconfirm title={'确认后将精修状态设置为已完成'} onConfirm={handleCompleteRefine}>
                      <Button type={'primary'}>完成精修</Button>
                    </Popconfirm>
                  }
                  {isRefineCompleted(selectedCreation) &&
                    <Button type={'primary'} disabled>精修已完成</Button>
                  }
                </>
              }

              {/* 一键生成视频 */}
              {selectedCreation && isExampleImage(selectedCreation) && !isDistributor && !isOperate &&
                <Button type={'primary'} onClick={handleCreateVideo}>一键生成视频</Button>
              }
            </Flex>
          </div>
        </Modal>
      }

      {/* 图片预览 */}
      {previewVisible &&
        <ImgPreview
          previewVisible={previewVisible}
          handleCancelPreview={handleCancelPreview}
          previewImage={previewImage}
          needSwitch={!!previewImgs}
          previewIdx={previewIdx}
          previewImgs={previewImgs}
          likeObj={likeObj}
          creativeBatch={selectedCreation}
          likeCallback={() => {
            handleClickCreationCard(selectedCreation?.id, selectedCreation?.type);
          }}
          showTools={showTools}
          needWatermark={needWatermark}
          modelId={selectedCreation?.modelId}
          previewInfoList={previewInfoList}
          mask={mask}
          originImg={selectedCreation?.extInfo['originImage']}
          faceAndMaskData={getFaceAndMaskData()}
        />
      }

      {showTopupModal &&
        <TopupModal visible={showTopupModal} onClose={() => setShowTopupModal(false)}
                    onPaySuccess={() => {
                      setShowTopupModal(false);
                      doPredict();
                    }} />
      }

      {showCreateVideoModal && (
        <Modal
          open={true}
          onCancel={() => setShowCreateVideoModal(false)}
          centered={true}
          width={460}
          footer={null}
          closable={false}
        >
          <Flex vertical gap={24}>
            <Flex align={'center'} justify={'center'} className={'text16 text-center'} vertical>
              <div>MuseGate会挑选制作视频最佳的四张的图片素材</div>
              <div>消耗
                <IconFont type={'icon-icon_mousidian'} style={{ fontSize: '24px' }} />
                {predictVO?.musePoint} 缪斯点
              </div>
              {!vipUser &&
                <div style={{ color: 'red' }}>该功能仅对付费客户开放，请先充值</div>
              }
            </Flex>

            <Flex justify={'center'} align={'center'} gap={16}>
              <Button onClick={() => setShowCreateVideoModal(false)} style={{ width: 140 }}>取消</Button>
              {vipUser &&
                <Button type={'primary'} onClick={commitCreateVideo} style={{ width: 140 }}>确认</Button>
              }
              {!vipUser &&
                <Button type={'primary'} onClick={() => setShowTopupModal(true)} style={{ width: 140 }}>充值</Button>
              }
            </Flex>
          </Flex>
        </Modal>
      )}

      {assignBatch &&
        <AssignModal batch={assignBatch} onChange={fetchData} onCancel={() => setAssignBatch(null)} />
      }

    </PageContainer>
  );
};

export default HistoryPage;
