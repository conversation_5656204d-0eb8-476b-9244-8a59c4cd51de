import React, { useEffect, useRef, useState } from 'react';
import { Flex, message, Modal, notification, Radio, Segmented, Space, Switch, Tooltip } from 'antd';
import PaymentQRCode from '@/components/PaymentQRCode';
import {
  createAlipayPayQRCode,
  createWeChatPayQRCode,
  queryAlipayPayResult,
  queryWeChatPayResult,
} from '@/services/PayController';
import bg1 from '@/assets/icon/充值/bg_旗舰包@1x.png';
import bg2 from '@/assets/icon/充值/bg_高级包@1x.png';
import bg3 from '@/assets/icon/充值/bg_基础包@1x.png';
import bg4 from '@/assets/icon/充值/bg_新手体验包@1x.png';
import musePointIcon from '@/assets/icon/充值/icon_缪斯点@1x.png';
import giftIcon from '@/assets/icon/充值/icon_礼物@1x.png';
import DengDeng from '@/assets/kehu/dengdeng.png';
import './topup.less';
import { HIDE_TOPUP_MODAL, IS_TRIAL_ACCOUNT, LOGO_BRAND_LARGE, USER_INFO } from '@/constants';
import IconFont from '@/components/IconFont';
import { checkIfCanShowNewbiePlan, getPricePlan, PricePlan } from '@/services/OrderInfoController';
import { maskPhoneNumber } from '@/utils/utils';

interface TopupModalProps {
  visible: boolean;
  onClose?: () => void;
  onPaySuccess?: () => void;
  onPaySuccessWithOrderNo?: (orderNo: string) => void;
  backroleAgentOrder?: boolean;
  payMasterId?: number;
  customOrderPlan?: PricePlan | any;
  payMasterLoginId?: string;
}

const TopupModal: React.FC<TopupModalProps> = ({
                                                 visible,
                                                 onClose,
                                                 onPaySuccess,
                                                 onPaySuccessWithOrderNo,
                                                 backroleAgentOrder = false,
                                                 payMasterId,
                                                 customOrderPlan,
                                                 payMasterLoginId,
                                               }) => {
  const [orderId, setOrderId] = useState<string>('');
  const [payStatus, setPayStatus] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);

  const [selectedPlan, setSelectedPlan] = useState<PricePlan>();
  const [plans, setPlans] = useState<PricePlan[]>();

  const [show, setShow] = useState(visible);

  const [seconds, setSeconds] = useState(3);
  const [masterLoginId, setMasterLoginId] = useState<string>();
  const [masterUser, setMasterUser] = useState<boolean>(true);

  const [canShowNewbiePlan, setCanShowNewbiePlan] = useState<boolean>(false);
  const isTrialAccount = sessionStorage.getItem(IS_TRIAL_ACCOUNT) === 'Y';
  const hideTopupModal = sessionStorage.getItem(HIDE_TOPUP_MODAL) === 'Y';

  if (isTrialAccount || hideTopupModal) {
    return null;
  }

  useEffect(() => {
    if (showSuccess) {
      const countdown = setInterval(() => {
        setSeconds(prev => prev - 1);
        console.log('倒计时:', seconds);
      }, 1000);

      if (seconds === 0) {
        console.log('倒计时结束');
        clearInterval(countdown);

        if (onPaySuccessWithOrderNo) {
          onPaySuccessWithOrderNo(orderId);
        }
        if (onPaySuccess) {
          onPaySuccess();
        }
        if (onClose) {
          onClose();
        }
      }

      return () => clearInterval(countdown);
    }
  }, [showSuccess, seconds, onClose, onPaySuccess]);

  // 处理支付成功回调
  const handlePaySuccess = (orderNo: string) => {
    setOrderId(orderNo);
    setPayStatus('SUCCESS');
    setShow(false);

    //后台自定义收款
    if (backroleAgentOrder) {
      if (onPaySuccessWithOrderNo) {
        onPaySuccessWithOrderNo(orderNo);
      }
    } else {
      setShowSuccess(true);
    }
  };

  useEffect(() => {

    if (backroleAgentOrder) {
      if (!payMasterId || !customOrderPlan || !customOrderPlan.amount) {
        console.log('后台管理自定义收款，数据缺失', payMasterId, customOrderPlan);
        message.error('后台管理自定义收款，数据缺失');
      } else {
        setPlans([customOrderPlan]);
        setSelectedPlan(customOrderPlan);
        setMasterLoginId(payMasterLoginId);
        setMasterUser(true);
      }

    } else {

      getPricePlan().then(res => {
        if (res) {
          setPlans(res);
          setSelectedPlan(res[0]);

        } else {
          console.log('查询支付配置为空');
        }
      });

      // @ts-ignore
      let user: UserVO = JSON.parse(localStorage.getItem(USER_INFO));

      if (user) {
        if (user.masterLoginId) {
          setMasterLoginId(user.masterLoginId);
        } else {
          console.log('当前用户主账号login id获取失败');
        }

        setMasterUser(user.userType === 'MASTER');
      }

      checkIfCanShowNewbiePlan().then(can => {
        if (can) {
          setCanShowNewbiePlan(true);
        } else {
          setCanShowNewbiePlan(false);
        }
        console.log('canShowNewbiePlan:', can);
      });
    }

  }, []);

  function getBG(planCode: string) {
    if (planCode === 'PLAN_FLAGSHIP') {
      return bg1;
    }
    if (planCode === 'PLAN_PREMIUM') {
      return bg2;
    }
    if (planCode === 'PLAN_BASIC') {
      return bg3;
    }
    if (planCode === 'PLAN_NEWBIE') {
      return bg4;
    }

    console.log('不支持的类型：' + planCode);
    return bg2;
  }

  if (payStatus === 'SUCCESS') {
    return (showSuccess &&
      <Modal
        open={showSuccess}
        width={550}
        onCancel={() => {
          if (onClose) {
            onClose();
          }

          if (onPaySuccess) {
            onPaySuccess();
          }

          if (onPaySuccessWithOrderNo) {
            onPaySuccessWithOrderNo(orderId);
          }
        }}
        footer={null}
        closable={false}
        keyboard={false}
        maskClosable={false}
        centered
        className={'topup-success-modal'}
        styles={{
          mask: {
            backgroundColor: 'rgba(0, 0, 0, 0.4)',
          },
        }}
      >
        <div style={{ position: 'relative', display: 'flex' }}>
          <div className="completion-modal-content">
            <span className={'topup-success-finish'}>您已完成充值</span>
            <IconFont type={'icon-icon_duihao'} style={{ fontSize: '64px', color: '#6A54D7' }} />
            <span className={'secs-counter'}>{seconds}s 后自动跳转至首页</span>
          </div>
          <div className="topup-success-modal-close" onClick={onClose}>
            <IconFont type={'icon-lujing'} style={{ fontSize: 24, color: 'white' }} />
          </div>
        </div>
      </Modal>
    );
  }

  return (show &&
    <Modal
      open={show}
      onCancel={onClose}
      footer={null}
      width={'1000px'}
      centered={true}
      className={'topup-modal'}
      closable={true}
      keyboard={false}
      maskClosable={false}
      style={{ minWidth: '50px', maxWidth: '100%' }}
      styles={{
        mask: backroleAgentOrder ? {} : {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          backdropFilter: 'blur(10px)',
        },
      }}
    >
      <div className={'topup-modal-body'}>

        {backroleAgentOrder &&
          <div style={{
            color: 'red',
            fontSize: 24,
            fontWeight: 500,
            margin: 36,
          }}>将二维码截图发给商家，请商家在2小时内完成支付，客户充值完成将自动相应加点</div>
        }

        {!backroleAgentOrder &&
          <div className={'topup-modal-content'}>
            <div className={'logo-row'}>
              <img src={LOGO_BRAND_LARGE} alt="MuseGate" style={{ width: 223, height: 50 }} />
              <div className={'logo-sep'}></div>
              <div className={'logo-text'}>充值中心</div>
            </div>
            <div className={'topup-account-row'}>
              <div className={'icon-container'}>
                <IconFont type={'icon-icon_wode'} style={{ fontSize: 32, color: '#F0EDF4' }} />
              </div>
              <div>
                <span className={'topup-account-desc'}>充值账号：</span><span
                className={'topup-account-mobile'}>{maskPhoneNumber(masterLoginId)}</span>
                {!masterUser && (
                  <>
                    <span style={{ color: '#969799', fontSize: 20 }}>（主账号）</span>
                    <Tooltip
                      title="仅支持为主账号充值，充值后缪斯点数和赠图数团队共享"
                      placement="top"
                    >
                      <IconFont type={'icon-yiwen1'} style={{ fontSize: 24 }} />
                    </Tooltip>
                  </>
                )}
              </div>
            </div>
            <div className={'topup-plan-row'}>

              {/*这里需要判断是否显示新手专享套餐*/}
              {plans && plans.filter((item) => item.code !== 'PLAN_NEWBIE').map((item, index) => (

                  <div key={item.code}
                       className={selectedPlan?.code === item.code ? 'topup-item topup-item-checked' : 'topup-item'}
                       style={{ position: item.code === 'PLAN_FLAGSHIP' ? 'relative' : 'static' }}
                       onClick={() => setSelectedPlan(item)}>

                    {item.code === 'PLAN_FLAGSHIP' && (
                      <div className={'topup-item-recommend'}>推荐</div>
                    )}

                    <div className={'topup-item-bg-title'}>
                      <img src={getBG(item.code)} alt={item.name}
                           style={{ position: 'absolute', left: '0px', top: '0px', width: '100%', height: '100%' }} />
                      <div
                        style={{
                          display: 'flex',
                          position: 'absolute',
                          top: '18px',
                          left: '14px',
                          alignItems: 'center',
                        }}>
                        <div className={'topup-item-title'}>{item.name}</div>
                        {item.code === 'PLAN_NEWBIE' && (
                          <div className={'newbie-only-once'}>账号仅可购买一次</div>
                        )}
                      </div>

                    </div>

                    <Flex vertical gap={16} style={{ padding: 12 }}>
                      <div className={'topup-item-content-up'}>
                        <div className={'topup-item-amount'}>
                        <span
                          className={item.code === 'PLAN_FLAGSHIP' ? 'topup-item-amount-number-qijian' : 'topup-item-amount-number'}>{item.amount}</span>
                          <span className={'topup-item-amount-yuan'}>元</span>
                          <div className={'topup-item-point'}>
                            <img src={musePointIcon} style={{ width: 22, height: 22 }} />
                            <span style={{ width: 'auto', fontSize: 18 }}>{item.musePoint}缪斯点</span>
                          </div>
                        </div>
                      </div>

                      <Flex vertical gap={8}>
                        {item.clothCount !== null && item.clothCount > 0 && (
                          <div className="topup-item-content-down-line">预计<span
                            className={'topup-item-content-down-number'}>{item.clothCount}</span>套服装
                          </div>
                        )}

                        {item.creativeImgCountPerCloth !== null && item.creativeImgCountPerCloth > 0 && (
                          <div className="topup-item-content-down-line">每套服装包含
                            <span className={'topup-item-content-down-number'}>{item.creativeImgCountPerCloth}</span>张创作图片套餐
                          </div>
                        )}

                      </Flex>

                      <Flex vertical gap={8}>
                        {item.code === 'PLAN_CUSTOM' && item.customModelCountGave !== null && item.customModelCountGave > 0 && (
                          <div className="topup-item-content-down-line">
                            <span className={'topup-item-content-down-number'}>{item.customModelCountGave}</span>个定制模特
                          </div>
                        )}
                      </Flex>

                      <Flex vertical gap={8}>
                        {item.code === 'PLAN_NEWBIE' && (
                          <div className="topup-item-content-down-line">
                            可体验<span className={'topup-item-content-down-number'}>1</span>套衣服和<span
                            className={'topup-item-content-down-number'}>1</span>组视频创作
                          </div>
                        )}
                      </Flex>

                      {item.totalGaveAmount && (
                        <div className={'topup-item-content-down'}>
                          <div className={'topup-item-giveaway-summary'}>
                            <div><img src={giftIcon} style={{ width: 24, height: 24 }} />赠送礼包</div>
                            <div>总价值{item.totalGaveAmount}元</div>
                          </div>
                          {item.creativeImgCountGave != null && item.creativeImgCountGave > 0 && (
                            <div className="giveaway-item">
                              <div className="giveaway-description">
                                <span className="plus-sign">+</span>赠{item.creativeImgCountGave}张创作图
                              </div>
                              <div className="giveaway-value">
                                价值{item.creativeImgAmountGave}元
                              </div>
                            </div>
                          )}
                          {item.customModelCountGave != null && item.customModelCountGave > 0 && (
                            <div className="giveaway-item">
                              <div className="giveaway-description">
                                <span className="plus-sign">+</span>赠{item.customModelCountGave}个定制模特
                              </div>
                              <div className="giveaway-value">
                                价值{item.customModelAmountGave}元
                              </div>
                            </div>
                          )}
                          {item.customSceneCountGave != null && item.customSceneCountGave > 0 && (
                            <div className="giveaway-item">
                              <div className="giveaway-description">
                                <span className="plus-sign">+</span>赠{item.customSceneCountGave}个定制场景
                              </div>
                              <div className="giveaway-value">
                                价值{item.customSceneAmountGave}元
                              </div>
                            </div>
                          )}
                          {item.shortVideoCountGave != null && item.shortVideoCountGave > 0 && (
                            <div className="giveaway-item">
                              <div className="giveaway-description">
                                <span className="plus-sign">+</span>赠{item.shortVideoCountGave}个短视频
                              </div>
                              <div className="giveaway-value">
                                价值{item.shortVideoAmountGave}元
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                    </Flex>

                  </div>
                ),
              )
              }

              {/* 添加固定的体验包卡片 */}
              <div
                className={'topup-item'}
                style={{ position: 'static' }}>
                <div className={'topup-item-bg-title'}>
                  <img src={bg4} alt="体验包"
                       style={{ position: 'absolute', left: '0px', top: '0px', width: '100%', height: '100%' }} />
                  <div
                    style={{
                      display: 'flex',
                      position: 'absolute',
                      top: '18px',
                      left: '14px',
                      alignItems: 'center',
                    }}>
                    <div className={'topup-item-title'}>体验包</div>
                    <div className={'newbie-only-once'}>价值 99 元</div>
                  </div>
                </div>

                <Flex vertical gap={16} style={{ padding: 12 }}>
                  <div className={'topup-item-content-up'}>
                    <div className={'topup-item-amount'}>
                      <span className={'topup-item-amount-number'}
                            style={{ fontSize: '24px', fontWeight: 'bold', marginRight: 12 }}>限时免费</span>
                      <div className={'topup-item-point'}>
                        <img src={musePointIcon} style={{ width: 22, height: 22 }} />
                        <span style={{ width: 'auto', fontSize: 18 }}>100缪斯点</span>
                      </div>
                    </div>
                  </div>

                  <Flex vertical gap={8}>
                    <div className="topup-item-content-down-line">
                      可体验<span className={'topup-item-content-down-number'}>1</span>套衣服和<span
                      className={'topup-item-content-down-number'}>1</span>组视频创作
                    </div>
                    <div className="qr-code-container">
                      <div className="qr-code-wrapper">
                        <div className="qr-code-decoration top"></div>
                        <div className="qr-code-decoration right"></div>
                        <div className="qr-code-decoration bottom"></div>
                        <div className="qr-code-decoration left"></div>
                        <img src={DengDeng} alt="微信二维码" className="qr-code-image" />
                      </div>
                      <div className="qr-code-text">
                        <IconFont type="icon-weixin1" style={{ fontSize: 16, marginRight: 4 }} />
                        微信扫码添加客户经理领取
                      </div>
                    </div>
                  </Flex>
                </Flex>
              </div>

            </div>

            <div className={'topup-tips-card'}>
              <div className={'topup-tips-content'}>
                <div className={'topup-tips-item'}>
                  <IconFont type="icon-yiwen1" style={{ fontSize: 16, color: '#969799' }} />
                  <span>创作图片扣除顺序：</span>
                  <span className={'topup-tips-highlight'}>服装套餐图片张数 → 赠送创作图 → 缪斯点</span>
                </div>
                <div className={'topup-tips-item'}>
                  <IconFont type="icon-yiwen1" style={{ fontSize: 16, color: '#969799' }} />
                  <span>套餐外每张图 = </span>
                  <span className={'topup-tips-highlight'}>
                    <img src={musePointIcon} style={{ width: 16, height: 16, verticalAlign: 'middle', marginRight: 4 }}
                         alt={''} />
                    0.4缪斯点
                  </span>
                </div>
                <div className={'topup-tips-item'}>
                  <IconFont type="icon-yiwen1" style={{ fontSize: 16, color: '#969799' }} />
                  <span>缪斯点&创作图</span>
                  <span className={'topup-tips-highlight'}>有效期1年</span>
                  <span className={'topup-tips-contact'}>
                    <IconFont type="icon-lianjie" style={{ fontSize: 14, marginRight: 4 }} />
                    更多套餐优惠，请联系客户经理
                  </span>
                </div>
              </div>
            </div>
          </div>
        }

        <div className={'pay-container'}>
          {selectedPlan && (
            <>
              <PaymentQRCode
                payType="wx"
                selectedPlan={selectedPlan}
                payMasterId={payMasterId}
                onPaySuccess={handlePaySuccess}
                className="pay-inner"
              />
              <PaymentQRCode
                payType="alipay"
                selectedPlan={selectedPlan}
                payMasterId={payMasterId}
                onPaySuccess={handlePaySuccess}
                className="pay-inner"
              />
            </>
          )}
        </div>
      </div>

    </Modal>
  );
};

export default TopupModal;