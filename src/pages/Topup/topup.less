.topup-modal {

  .ant-modal-content {
    padding: 0;
    border-radius: 24px !important;
  }

  //右上角关闭
  .ant-modal-close-x {
    font-size: 24px;
  }
}

.topup-modal-body {
  position: relative;
  display: flex;
  flex-direction: column;
  border-radius: 24px;
}

.topup-modal-content {
  background: #f5f6f9;
  padding: 12px 24px 12px 24px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  border-radius: 24px 24px 0 0;
}

.logo-row {
  height: 50px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  gap: 16px;

  z-index: 0;
}

.logo-sep {
  width: 28px;
  height: 0px;
  transform: rotate(90deg);
  opacity: 1;

  border-top: 1px solid #D8D8D8;

  z-index: 1;
}

.logo-text {
  font-family: PingFang SC;
  font-size: 24px;
  font-weight: 500;
  line-height: 32px;
  letter-spacing: 0px;
  color: #C8C9CC;
}

.topup-account-row {
  height: 32px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  gap: 8px;
  z-index: 1;
}

.topup-plan-row {
  display: flex;
  flex-direction: row;
  gap: 8px;
  margin-top: 16px;
}

.topup-item-bg-title {
  position: relative;
  height: 68px;
  opacity: 1;

  border: 0.5px solid ;
  border-image: linear-gradient(243deg, #DBDDFF 4%, #747BE7 27%, #747BE7 55%) ;
  display: flex;
  flex-direction: row;
  gap: 8px;
}

.topup-item-title {
  height: 32px;
  opacity: 1;
  font-family: PingFang SC;
  font-size: 24px;
  font-weight: 500;
  line-height: 32px;
  letter-spacing: 0px;
  color: #FFFFFF;
}

.newbie-only-once {
  margin-left: 8px;
  height: 26px;
  border-radius: 382px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  padding: 4px 8px;

  background: rgba(255, 255, 255, 0.2);

  box-sizing: border-box;
  border: 1px solid rgba(255, 255, 255, 0.8);

  z-index: 1;

  font-family: PingFang SC;
  font-size: 12px;
  font-weight: normal;
  line-height: 18px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  color: #FFFFFF;
}

.icon-container {
  width: 32px;
  height: 32px;
  border-radius: 148px;
  opacity: 1;
  background: #C7C8E4;
  display: flex;
  justify-content: center;
  align-items: center;
}

.topup-item {
  width: 100%;
  height: 422px;
  border-radius: 16px;
  background: #FFFFFF;
}

.topup-item-recommend {
  position: absolute;
  left: -2px;
  top: -10px;
  width: 82px;
  height: 30px;
  opacity: 1;
  background: #FF5C64;
  z-index: 100;
  border-radius: 100px;
  display: flex;
  justify-content: center;
  align-items: center;

  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  color: #F5F7FC;
}

.topup-item-checked, .topup-item:hover {
  background: url("@/assets/icon/充值/充值选中背景.png") no-repeat;
  background-size: 100% 100% !important;
}

.topup-item-content-up {
  opacity: 1;
  display: flex;
  flex-direction: column;
  padding: 0px;
  gap: 4px;
}

.topup-item-amount {
  height: 60px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  align-items: end;
  padding: 0;
  z-index: 0;
}

.topup-item-amount-number-qijian {
  opacity: 1;

  font-family: "Alibaba Sans";
  font-size: 32px;

  font-variation-settings: "opsz" auto;
  background: radial-gradient(95% 95% at 84% 77%, #603F19 0%, #1E1C1D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;

  z-index: 0;
}

.topup-item-amount-number {
  opacity: 1;

  font-family: "Alibaba Sans";
  font-size: 32px;

  font-variation-settings: "opsz" auto;
  background: radial-gradient(95% 95% at 84% 77%, #185FED 0%, #4035BE 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;

  z-index: 0;
}

.topup-account-desc{
  font-family: PingFang SC;
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: 0px;

  /* 中性色/N8-主文字 */
  color: #1A1B1D;
}

.topup-account-mobile{
  font-family: PingFang SC;
  font-size: 20px;
  font-weight: normal;
  line-height: 28px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 中性色/N8-主文字 */
  color: #1A1B1D;
}

.topup-item-amount-yuan {
  font-family: PingFang SC;
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: 0px;
  color: #1A1B1D;
  margin-right: 8px;
}

.topup-item-point{
  min-width: 150px;
  height: 24px;
  border-radius: 8px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  gap: 2px;
  z-index: 0;

  font-family: Alibaba Sans;
  font-size: 24px;
  font-weight: bold;
  line-height: 32px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  color: #443626;
}

.topup-item-giveaway-container {
  height: 32px;
  border-radius: 8px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 4px 0px;
  gap: 4px;
  background: linear-gradient(90deg, #FFECEC 0%, #FFF5DD 100%);
  z-index: 1;
}

.topup-item-content-down {
  border-radius: 8px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  gap: 8px;

  background: linear-gradient(90deg, #FFF5DD 0%, #FFECEC 100%);
  box-sizing: border-box;
  border: 1px solid #FFFFFF;
}

.topup-item-giveaway-summary{
  border-radius: 4px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  align-self: stretch;

  background: linear-gradient(90deg, rgba(255, 176, 102, 0.3) 0%, rgba(255, 187, 156, 0.5) 100%, rgba(255, 151, 53, 0.3) 100%);

  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  color: #DC212A;
}

.giveaway-item {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding-right: 8px;
}

.giveaway-description {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  color: #642D2F;

  display: flex;
  align-items: center;
}

.plus-sign {
  color: #FF4964; /* 加号颜色 */
  margin-left: 9px;
  margin-right: 9px; /* 加号和文字之间的间距 */
}

.giveaway-value {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  color: #DC212A;
}

.topup-item-content-down-line {
  position: relative;
  padding-left: 16px; /* 调整此值来控制文本与圆点的距离 */

  display: flex;
  flex-direction: row;
  align-items: baseline;

  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 中性色/N8-主文字 */
  color: #1A1B1D;
}

.topup-item-content-down-line::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 8px; /* 圆点的直径 */
  height: 8px; /* 圆点的直径 */
  background-color: #000; /* 圆点的颜色 */
  border-radius: 50%; /* 使其成为圆形 */
}

.topup-item-content-down-number {
  font-family: Alibaba Sans;
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 0px;
  font-variation-settings: "opsz" auto;
  color: #0B2966;
}

.topup-notes {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;
  color: #969799;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
}

.topup-tips-card {
  background: #F8F9FA;
  border-radius: 12px;
  padding: 12px 16px;
  margin-top: 16px;
}

.topup-tips-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.topup-tips-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  flex-wrap: wrap;
}

.topup-tips-highlight {
  color: #0052D9;
  font-weight: 500;
}

.topup-tips-contact {
  margin-left: auto;
  font-size: 12px;
  color: #969799;
  display: flex;
  align-items: center;
  gap: 4px;
}

.pay-container {
  width: 100%;
  height: 156px;
  opacity: 1;
  background: #EBEFFA;
  display: flex;
  justify-content: space-evenly;
  border-radius: 0 0 24px 24px;
}

.pay-inner {
  margin-top: 16px;
  height: 116px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  gap: 16px;
}

// 确保PaymentQRCode组件继承pay-inner样式
.payment-qr-code.pay-inner {
  margin-top: 16px;
  height: 116px;
}

.scan-pay-text{
  font-family: PingFang SC;
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: 0px;

  color: #3D3D3D;
}

.pay-amount-number{
  font-family: PingFang SC;
  font-size: 38px;
  font-weight: 600;
  line-height: 46px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  color: #3D3D3D;
}


.topup-modal-close {
  position: absolute;
  top: 0;
  right: -40px;
  z-index: 1000;
  cursor: pointer;
}

.completion-modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;

  width: 550px;
  height: 378px;
  border-radius: 24px;
  opacity: 1;
  background: #F5F7F8;
}

.topup-success-modal-close {
  position: absolute;
  top: 0;
  right: -40px;
  z-index: 1000;
  cursor: pointer;
}

.topup-success-modal{
  .ant-modal-content{
    padding: 0;
    border-radius: 24px !important;
  }

  .topup-success-finish {
    margin-top: 80px;
    margin-bottom: 28px;
    width: 144px;
    height: 32px;
    opacity: 1;

    /* large title/medium */
    font-family: PingFang SC;
    font-size: 24px;
    font-weight: 500;
    line-height: 32px;
    letter-spacing: 0px;

    color: #3D3D3D;
  }

  .secs-counter {
    margin-top: 48px;
    height: 28px;
    opacity: 1;
    display: flex;
    flex-direction: row;
    padding: 0px;
    gap: 4px;

    font-family: PingFang SC;
    font-size: 20px;
    font-weight: normal;
    line-height: 28px;
    letter-spacing: 0px;

    font-variation-settings: "opsz" auto;
    color: #3D3D3D;
  }
}

.qr-code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 12px;
  gap: 8px;
}

.qr-code-wrapper {
  position: relative;
  width: 160px;
  height: 160px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 8px;
}

.qr-code-decoration {
  position: absolute;
  background: linear-gradient(135deg, #747BE7 0%, #DBDDFF 100%);
  border-radius: 2px;
  
  &.top {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 32px;
    height: 3px;
  }
  
  &.right {
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 32px;
  }
  
  &.bottom {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 32px;
    height: 3px;
  }
  
  &.left {
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 32px;
  }
}

.qr-code-image {
  width: 144px;
  height: 144px;
  border-radius: 6px;
}

.qr-code-text {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 13px;
  font-weight: 500;
  padding: 6px 12px;
  background: rgba(116, 123, 231, 0.1);
  border-radius: 16px;

  .icon-weixin {
    color: #07C160 !important;
    font-size: 16px;
    margin-right: 4px;
  }
}