import userIcon from '@/assets/icon/user-icon.png';
import logo from '@/assets/icon/logo.png';
import brand from '@/assets/icon/brand.png';
import logoBrand from '@/assets/icon/logo-brand.png';
import logoBrandLarge from '@/assets/icon/logo-brand-large.png';
import downloadIcon from '@/assets/icon/download.png';
import creativeLightIcon from '@/assets/icon/creative-light-icon.png';
import queueIcon from '@/assets/icon/queue-icon.png';
import miniLoadingIcon from '@/assets/icon/min-loading-icon.png';
import systemLoraDemo from '@/assets/images/system-lora-demo.png';
import freeIcon from '@/assets/icon/icon-free.png';
import allLoras from '@/assets/images/all-loras.png';
import allLorasNew from '@/assets/images/all-loras.jpg';
import allLoras11 from '@/assets/images/all-loras-11.png';
import imageLoading from '@/assets/images/image-loading.png';
import imageLoading_1_1 from '@/assets/images/image-loading-1x1.png';
import imageLoading_2_1 from '@/assets/images/image-loading-2x1.jpg';
import imageQueue from '@/assets/images/image-queue.jpg';
import imageQueue_1_1 from '@/assets/images/image-queue_1x1.jpg';
import imageQueue_2_1 from '@/assets/images/image-queue-2x1.jpg';
import imageFailed from '@/assets/images/image-failed.jpg';
import imageFailed_1_1 from '@/assets/images/image-failed_1x1.jpg';
import imageFailed_2_1 from '@/assets/images/image-failed-2x1.jpg';
import moreLoraIcon from '@/assets/icon/icon-more-lora.png';
import iconExpand from '@/assets/images/icon-expand.png';
import iconGift from '@/assets/icon/icon_gift.png';
import { getFullUrl } from '@/services/request';
import iconWholeBody from '@/assets/icon/icon-whole-body.jpg';
import iconLowerBody from '@/assets/icon/icon-lower-body.jpg';
import iconUpperBody from '@/assets/icon/icon-upper-body.jpg';
import bgAllModels from '@/assets/images/bg_all_models.jpg';
import bgAllScenes from '@/assets/images/bg_all_scenes.jpg';
import randomColor from '@/assets/images/image-random-color.jpg';
import backDemo from '@/assets/images/image-back-demo.png';
import frontDemo from '@/assets/images/image-front-demo.png';
import bgIndex1 from '@/assets/images/index/bg-index-1.jpg';
import iconHelper from '@/assets/icon/icon-helper.png';
import bgIndexMainBtn from '@/assets/images/index/bg-index-main-btn.png';
import sellingPoints11 from '@/assets/images/index/selling-points-1-1.jpg';
import sellingPoints12 from '@/assets/images/index/selling-points-1-2.png';
import sellingPoints13 from '@/assets/images/index/selling-points-1-3.jpg';
import sellingPoints31 from '@/assets/images/index/selling-points-3-1.jpg';
import sellingPoints32 from '@/assets/images/index/selling-points-3-2.jpg';
import sellingPoints33 from '@/assets/images/index/selling-points-3-3.jpg';
import subSellingPoints1 from '@/assets/images/index/sub-selling-points-1.jpg';
import subSellingPoints2 from '@/assets/images/index/sub-selling-points-2.jpg';
import subSellingPoints3 from '@/assets/images/index/sub-selling-points-3.jpg';
import subSellingPoints4 from '@/assets/images/index/sub-selling-points-4.jpg';
import subSellingPoints5 from '@/assets/images/index/sub-selling-points-5.jpg';
import wxVideoQCode from '@/assets/images/wx-video-qcode.png';
import wxCSQCode from '@/assets/images/wx-cs-qcode.png';
import wxQCode from '@/assets/images/index/img-weixin-qcode.png';
import newBackground from '@/assets/icon/new/newBackground.png';
import newLabel from '@/assets/icon/new/new.png';

//如果是本地需要加/service，如果是生产环境分两种情况，如果是一起部署则不需要加域名，如果是分开部署时需要加上域名
export const SERVICE_URL = process.env.UMI_ENV === 'prod' ? '/' : '/service/';//'http://localhost:8080/';
export const UPLOAD_URL = getFullUrl('/file/upload');
export const UPLOAD_PRO_URL = getFullUrl('/file/uploadPro');
export const UPLOAD_PRO_RESCALE_URL = getFullUrl('/file/uploadProWithRescale');
export const UPLOAD_LORA_URL = process.env.UMI_ENV === 'prod' ? 'https://aigc-http-abcd.conrain.cn:11874/upload/lora' : 'http://localhost:20310/upload/lora';

// constants
export const DEFAULT_NAME = 'conrAIn';
export const TEMP_USER_UUID = 'tempUserUuid';
export const USER_INFO = 'userInfo';
export const IS_TRIAL_ACCOUNT = 'isTrialAccount';
export const IMAGE_POINT = 'imagePoint';
export const EXPERIENCE_POINT = 'experiencePoint';
export const IMAGE_POINT_CHANGE_EVENT = 'IMAGE_POINT_CHANGE_EVENT';
export const NEED_GUIDE = 'SHOW_GUIDE';
export const STORE_TOGGLE_FLOW_STEPS = 'STORE_TOGGLE_FLOW_STEPS';

export const EVENT_TOGGLE_FLOW_STEPS = 'EVENT_TOGGLE_FLOW_STEPS';
export const NAVI_TYPE_CHANGE_EVENT = 'NAVI_TYPE_CHANGE_EVENT';
export const NAVI_TYPE = "S_NAVI_TYPE";
export const IMAGE_CASE_SYNC_CONFIG = "IMAGE_CASE_SYNC_CONFIG";
export const HOME_LOGIN_REGISTER_CONFIG = "HOME_LOGIN_REGISTER_CONFIG";

export const HIDE_TOPUP_MODAL = "HIDE_TOPUP_MODAL";

// Analysis Type
export const TEST_PLAN = "TEST_PLAN";


// 图片处理服务类型
export const IMAGE_OPERATE_CUSTOM = "custom";
export const IMAGE_OPERATE_ALIYUN = "aliyun";

export const ERASE_TASK_ID = "erase_task_id";
export const ERASE_IMAGE = 'erase_image';

// 用户偏好
export const KEY_CUSTOM_SCENE_DESC = 'customSceneDesc';


// image & icon
export const USER_ICON = userIcon;
export const LOGO = logo;
export const BRAND = brand;
export const LOGO_BRAND = logoBrand;
export const LOGO_BRAND_LARGE = logoBrandLarge;
export const DOWNLOAD = downloadIcon;
export const CREATIVE_LIGHT_ICON = creativeLightIcon;
export const QUEUE_ICON = queueIcon;
export const MINI_LOADING_ICON = miniLoadingIcon;
export const SYSTEM_LORA_DEMO = systemLoraDemo;
export const FREE_ICON = freeIcon;
export const ALL_LORAS = allLoras;
export const ALL_LORAS_NEW = allLorasNew;
export const ALL_LORAS_11 = allLoras11;
export const IMAGE_LOADING = imageLoading;
export const IMAGE_LOADING_11 = imageLoading_1_1;
export const IMAGE_LOADING_21 = imageLoading_2_1;
export const IMAGE_QUEUE = imageQueue;
export const IMAGE_QUEUE_11 = imageQueue_1_1;
export const IMAGE_QUEUE_21 = imageQueue_2_1;
export const IMAGE_FAILED = imageFailed;
export const IMAGE_FAILED_11 = imageFailed_1_1;
export const IMAGE_FAILED_21 = imageFailed_2_1;
export const MORE_LORA_ICON = moreLoraIcon;
export const EXPAND_ICON = iconExpand;
export const GIFT_ICON = iconGift;
export const WHOLE_BODY_ICON = iconWholeBody;
export const LOWER_BODY_ICON = iconLowerBody;
export const UPPER_BODY_ICON = iconUpperBody;
export const BG_ALL_MODELS = bgAllModels;
export const BG_ALL_SCENES = bgAllScenes;
export const RANDOM_COLOR = randomColor;
export const FRONT_DEMO = frontDemo;
export const BACK_DEMO = backDemo;
export const BG_INDEX_1 = bgIndex1;
export const BG_INDEX_MAIN_BTN = bgIndexMainBtn;
export const ICON_HELPER = iconHelper;
export const IMG_SELLING_POINTS_11 = sellingPoints11;
export const IMG_SELLING_POINTS_12 = sellingPoints12;
export const IMG_SELLING_POINTS_13 = sellingPoints13;
export const IMG_SELLING_POINTS_31 = sellingPoints31;
export const IMG_SELLING_POINTS_32 = sellingPoints32;
export const IMG_SELLING_POINTS_33 = sellingPoints33;
export const IMG_SUB_SELLING_POINTS_1 = subSellingPoints1;
export const IMG_SUB_SELLING_POINTS_2 = subSellingPoints2;
export const IMG_SUB_SELLING_POINTS_3 = subSellingPoints3;
export const IMG_SUB_SELLING_POINTS_4 = subSellingPoints4;
export const IMG_SUB_SELLING_POINTS_5 = subSellingPoints5;
export const Q_CODE_WX_VIDEO = wxVideoQCode;
export const Q_CODE_WX_CS = wxCSQCode;
export const Q_CODE_WX = wxQCode;
export const NEW_BACKGROUND = newBackground;
export const NEW_LABEL = newLabel;

// REGEX，普通手机号（1[3-9] 开头）或测试手机号（288 开头）的 11 位数字
export const PHONE_REGEX = /^(1[3-9]\d{9}|288\d{8})$/;
export const PASSWORD_REGEX = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{8,16}$/;

export const FILLED_IMAGE_MAP = {
  'ONE_ONE': {
    'PROCESSING': IMAGE_LOADING_11,
    'QUEUE': IMAGE_QUEUE_11,
    'FAILED': IMAGE_FAILED_11,
    isLarge: false,
  },
  'ONE_ONE_LG': {
    'PROCESSING': IMAGE_LOADING_11,
    'QUEUE': IMAGE_QUEUE_11,
    'FAILED': IMAGE_FAILED_11,
    isLarge: true,
  },
  'THREE_FOUR': {
    'PROCESSING': IMAGE_LOADING,
    'QUEUE': IMAGE_QUEUE,
    'FAILED': IMAGE_FAILED,
    isLarge: false,
  },
  'THREE_FOUR_LG': {
    'PROCESSING': IMAGE_LOADING,
    'QUEUE': IMAGE_QUEUE,
    'FAILED': IMAGE_FAILED,
    isLarge: true,
  },
  'THREE_FOUR_LG_N': {
    'PROCESSING': IMAGE_LOADING,
    'QUEUE': IMAGE_QUEUE,
    'FAILED': IMAGE_FAILED,
    isLarge: true,
  },
  'NINE_SIXTEEN_2K': {
    'PROCESSING': IMAGE_LOADING,
    'QUEUE': IMAGE_QUEUE,
    'FAILED': IMAGE_FAILED,
    isLarge: true,
  },
  'P_1152_1536': {
    'PROCESSING': IMAGE_LOADING,
    'QUEUE': IMAGE_QUEUE,
    'FAILED': IMAGE_FAILED,
    isLarge: true,
  },
  'P_1620_2100': {
    'PROCESSING': IMAGE_LOADING,
    'QUEUE': IMAGE_QUEUE,
    'FAILED': IMAGE_FAILED,
    isLarge: true,
  },
  'P_1200_600': {
    'PROCESSING': IMAGE_LOADING_21,
    'QUEUE': IMAGE_QUEUE_21,
    'FAILED': IMAGE_FAILED_21,
    isLarge: true,
  },
  'NONE': {
    'PROCESSING': IMAGE_LOADING,
    'QUEUE': IMAGE_QUEUE,
    'FAILED': IMAGE_FAILED,
    isLarge: true,
  },
};

export const isFilledImage = (imageUrl: string) => {
// 遍历外层对象
  for (const outerKey in FILLED_IMAGE_MAP) {
    if (FILLED_IMAGE_MAP.hasOwnProperty(outerKey)) {
      const innerObj = FILLED_IMAGE_MAP[outerKey];

      // 遍历内层对象
      for (const innerKey in innerObj) {
        if (innerObj.hasOwnProperty(innerKey)) {
          const value = innerObj[innerKey];

          // 检查值是否匹配
          if (value === imageUrl) {
            return true;
          }
        }
      }
    }
  }
  // 如果没有找到匹配的值，则返回false
  return false;
};


// 配置key
export const CONDIFS_KEYS = {
  'SCENE': 'SCENE',
  'FACE': 'FACE',
};

export const REGISTER_INVITE_CODE = 'register.invite.code';

// 防抖配置
export const DEBOUNCE_DELAY = 500; // 搜索输入框防抖延迟时间（毫秒）