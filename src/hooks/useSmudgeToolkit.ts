import { useState, useCallback, useEffect } from 'react';

export interface Point {
  x: number;
  y: number;
}

export interface LineConfig {
  points: Point[];
  tool: string;
  strokeWidth: number;
  color: string;
}

export interface EditorProps {
  mode: 'draw' | 'move';  // 当前模式：绘制或移动
  isErasing: boolean;     // 是否在擦除
  strokeWidth: number;    // 笔刷大小
  scale: number;         // 缩放比例
  position: {x: number, y: number}; // 画布位置
  lines: LineConfig[];         // 绘制历史
  redoStack: LineConfig[];     // 重做栈
  size: {width: number, height: number}
}

const DEFAULT_EDITOR_STATE: EditorProps = {
  mode: 'draw',
  isErasing: false,
  strokeWidth: 32,
  scale: 1,
  position: {x: 0, y: 0},
  lines: [],
  redoStack: [],
  size: {width: 0, height: 0}
};

export const useSmudgeToolkit = (initialState: Partial<EditorProps> = {}) => {
  const [editorState, setEditorState] = useState<EditorProps>({
    ...DEFAULT_EDITOR_STATE,
    ...initialState,
  });

  // 切换模式
  const setMode = useCallback((mode: 'draw' | 'move') => {
    setEditorState(prev => ({ ...prev, mode }));
  }, []);

  // 切换擦除状态
  const toggleErasing = useCallback(() => {
    setEditorState(prev => ({ ...prev, isErasing: !prev.isErasing }));
  }, []);

  // 设置笔刷大小
  const setStrokeWidth = useCallback((width: number) => {
    setEditorState(prev => ({ ...prev, strokeWidth: width }));
  }, []);

  // 设置缩放比例
  const setScale = useCallback((scale: number) => {
    setEditorState(prev => ({ ...prev, scale }));
  }, []);

  // 设置位置
  const setPosition = useCallback((position: {x: number, y: number}) => {
    setEditorState(prev => ({ ...prev, position }));
  }, []);

  const handleLinesChange = useCallback((value) => {
    setEditorState(prev => ({ ...prev, lines: value }));
  }, []);

  // 添加线条
  const addLine = useCallback((line: LineConfig) => {
    setEditorState(prev => ({
      ...prev,
      lines: [...prev.lines, line],
      redoStack: [], // 添加新线条时清空重做栈
    }));
  }, []);

  // 清空所有线条
  const clearLines = useCallback(() => {
    setEditorState(prev => ({
      ...prev,
      lines: [],
      redoStack: [], // 清空时同时清空重做栈
    }));
  }, []);

  // 撤销操作
  const undo = useCallback(() => {
    setEditorState(prev => {
      if (prev.lines.length === 0) return prev;
      const lastLine = prev.lines[prev.lines.length - 1];
      return {
        ...prev,
        lines: prev.lines.slice(0, -1),
        redoStack: [...prev.redoStack, lastLine],
      };
    });
  }, []);

  // 重做操作
  const redo = useCallback(() => {
    setEditorState(prev => {
      if (prev.redoStack.length === 0) return prev;
      const lastRedoLine = prev.redoStack[prev.redoStack.length - 1];
      return {
        ...prev,
        lines: [...prev.lines, lastRedoLine],
        redoStack: prev.redoStack.slice(0, -1),
      };
    });
  }, []);

  // 重置所有状态
  const reset = useCallback(() => {
    setEditorState(DEFAULT_EDITOR_STATE);
  }, []);

  // 缩放回调函数 - 直接设置缩放值
  const handleScaleChange = (scale) => {
    setEditorState(prev => ({
      ...prev,
      scale: Math.max(0.3, Math.min(3, scale))
    }));
  };

  // 位置回调函数
  const handlePositionChange = (position) => {
    setEditorState(prev => ({
      ...prev,
      position
    }));
  };

  const handleSizeChange = (width: number, height: number) => {
    setEditorState(prev => ({
      ...prev,
      size: {width: width, height: height}
    }))
  }

  return {
    editorState,
    setEditorState,
    setMode,
    toggleErasing,
    setStrokeWidth,
    setScale,
    setPosition,
    handleLinesChange,
    handleSizeChange,
    addLine,
    clearLines,
    undo,
    redo,
    reset,
    onScaleChange: handleScaleChange,
    onPositionChange: handlePositionChange
  };
};

export default useSmudgeToolkit; 