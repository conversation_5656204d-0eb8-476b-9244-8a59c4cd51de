import { useEffect, useRef } from 'react';
import { getUserInfo, isProdEnv } from '@/utils/utils';
import { Modal } from 'antd';

interface UseUpdateCheckerOptions {
  checkInterval?: number;
}

export const useUpdateChecker = (options: UseUpdateCheckerOptions = {}) => {
  const { checkInterval = 5000 } = options;
  const lastScriptsRef = useRef<string[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const checkForUpdates = async () => {
    try {
      const response = await fetch('/',  {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache'
        }
      });
      const html = await response.text();
      const scriptReg = /<script.*src=["'](?<src>[^"']+)/gm;
      const newScripts: string[] = [];
      let match;
      
      while ((match = scriptReg.exec(html))) {
        newScripts.push(match.groups?.src || '');
      }

      // 首次检查，保存脚本列表
      if (lastScriptsRef.current.length === 0) {
        lastScriptsRef.current = newScripts;
        return;
      }

      // 检查是否有更新
      const hasUpdate = 
        lastScriptsRef.current.length !== newScripts.length ||
        lastScriptsRef.current.some((script, index) => script !== newScripts[index]);

      if (hasUpdate) {
        Modal.confirm({
          title: '检测到页面更新',
          centered: true,
          content: '是否刷新查看？',
          type: 'confirm',
          onOk: () => {
            window.location.reload();
          },
          onCancel: () => {
            stopChecking();
          }
        });
      }

      lastScriptsRef.current = newScripts;
    } catch (error) {
      // 静默处理错误
    }
  };

  const stopChecking = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  useEffect(() => {
    // 只在生产环境下启用
    if (!isProdEnv()) {
      return;
    }

    if(!(['ADMIN', 'OPERATOR', 'DISTRIBUTOR', 'DEMO_ACCOUNT'].includes(getUserInfo()?.roleType || 'NONE'))){
      return;
    }
    
    // 立即执行一次检查
    checkForUpdates();
    
    // 设置定时器
    timerRef.current = setInterval(checkForUpdates, checkInterval);

    // 页面可见性变化时控制检测
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        checkForUpdates();
        timerRef.current = setInterval(checkForUpdates, checkInterval);
      } else {
        stopChecking();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 清理函数
    return () => {
      stopChecking();
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [checkInterval]);
}; 