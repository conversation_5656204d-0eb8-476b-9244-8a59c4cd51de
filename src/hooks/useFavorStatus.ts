import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { isCreationType, UserFavorType } from '@/services/UserFavorController';
import { favorManager } from '@/utils/favorManager';
import { ensureFavorsLoaded } from '@/utils/favorLoader';

/**
 * 自定义 Hook，用于管理收藏状态
 * @param favorType 收藏类型
 * @param itemId 项目ID
 * @param imageIndex 图片索引（可选）
 * @param images 图片列表（可选）
 */
export const useFavorStatus = (
  favorType: UserFavorType,
  itemId: number,
  imageIndex?: number,
  images: number[] = []
) => {
  const [isFavored, setIsFavored] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  // 更新收藏状态
  const updateFavorStatus = useCallback(() => {
    if ( isCreationType(favorType) && imageIndex !== undefined) {
      // 查找当前图片是否在收藏列表中
      const favorItem = favorManager.getFavorList().find(
        item => item.type === favorType && item.itemId === itemId
      );
      
      if (favorItem?.extInfo && favorItem.extInfo["imageIndexes"]?.includes(imageIndex)) {
        setIsFavored(true);
      } else {
        setIsFavored(false);
      }
    } else {
      // 使用 favorManager 的 isFavorited 方法判断
      setIsFavored(favorManager.isFavorited(favorType, itemId));
    }
  }, [favorType, itemId, imageIndex]);

  // 监听收藏列表变化
  const handleFavorListChange = useCallback((favorList: any[]) => {
    // 不管怎样都更新
    updateFavorStatus();
  }, [favorType, itemId, updateFavorStatus]);

  // 添加收藏
  const addFavor = useCallback(async () => {
    if (loading) return;
    
    setLoading(true);
    const payload = { 
      type: favorType, 
      itemId: itemId,
      images: imageIndex !== undefined ? [imageIndex] : images 
    };
    
    try {
      const success = await favorManager.addUserFavor(payload);
      if (success) {
        setIsFavored(true);
        message.success('收藏成功');
      } else {
        message.error('收藏失败');
      }
    } catch (error) {
      console.error('Failed to add favorite:', error);
      message.error('收藏失败');
    } finally {
      setLoading(false);
    }
  }, [favorType, itemId, imageIndex, images, loading]);

  // 移除收藏
  const removeFavor = useCallback(async () => {
    if (loading) return;
    
    setLoading(true);
    const payload = { 
      type: favorType, 
      itemId: itemId,
      images: imageIndex !== undefined ? [imageIndex] : images 
    };
    
    try {
      const success = await favorManager.removeUserFavor(payload);
      if (success) {
        setIsFavored(false);
        message.success('取消收藏成功');
      } else {
        // message.error('取消失败');
      }
    } catch (error) {
      setIsFavored(false);
    } finally {
      setIsFavored(false);
      setLoading(false);
    }
  }, [favorType, itemId, imageIndex, images, loading]);

  // 初始化
  useEffect(() => {
    // 使用集中式加载器确保收藏数据已加载
    ensureFavorsLoaded(updateFavorStatus);
    
    // 添加监听器
    favorManager.addListener(handleFavorListChange);
    
    // 组件卸载时移除监听器
    return () => {
      favorManager.removeListener(handleFavorListChange);
    };
  }, [updateFavorStatus, handleFavorListChange]);

  return {
    isFavored,
    loading,
    addFavor,
    removeFavor
  };
}; 