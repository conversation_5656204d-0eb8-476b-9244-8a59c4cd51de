
import { useState, useMemo, useCallback, useEffect } from 'react';

interface ImageSelectorOptions<T> {
  // 图片列表
  images?: string[];
  // 可选：判断是否显示图片选择的函数
  shouldShowSelector?: (item?: T) => boolean;
  // 可选：初始选中的图片
  initialSelected?: Record<string, boolean>;
}

interface ImageSelectorResult<T> {
  // 设置图片列表
  setImageSelectorOptions: React.Dispatch<React.SetStateAction<ImageSelectorOptions<T>>>;
  // 选中的图片数组
  selectedImages: string[];
  // 选中的图片数量
  selectedCount: number;
  // 是否半选
  indeterminate: boolean;
  // 是否全部选中
  isAllSelected: boolean;
  // 是否有选中的图片
  hasSelected: boolean;
  // 判断图片是否被选中
  isImageSelected: (imgUrl: string) => boolean;
  // 选择/取消选择单个图片
  handleSelectImage: (imgUrl: string) => void;
  // 全选/取消全选
  handleSelectAll: (checked: boolean) => void;
  // 重置选择状态
  resetSelection: () => void;
  // 判断是否应该显示选择器
  shouldShowImageSelection: (item?: any) => boolean;
}

/**
 * 图片选择器 Hook
 * @param imageSelectorOptions 配置选项
 * @returns 图片选择器状态和操作方法
 */
export function useImageSelector<T = any>(imageSelectorOptions: ImageSelectorOptions<T> = {}): ImageSelectorResult<T> {
  const [options, setOptions] = useState<ImageSelectorOptions<T>>(imageSelectorOptions);
  
  // 选中的图片状态 - 直接使用数组存储URL
  const [selectedImages, setSelectedImages] = useState<string[]>([]);

  // 初始化选中状态
  useEffect(() => {
    if (options.initialSelected) {
      // 如果提供了initialSelected对象，将其转换为数组
      const initialSelectedArray = Object.entries(options.initialSelected)
        .filter(([_, selected]) => selected)
        .map(([url]) => url);
      setSelectedImages(initialSelectedArray);
    } else {
      setSelectedImages([]);
    }
  }, [options]);

  // 选中的图片数量
  const selectedCount = useMemo(() => {
    return selectedImages.length;
  }, [selectedImages]);

  // 是否半选状态
  const indeterminate = useMemo(() => {
    return selectedCount > 0 && selectedCount < (options.images?.length ?? 0);
  }, [selectedCount, options]);
  
  // 是否全部选中
  const isAllSelected = useMemo(() => {
    if (!options.images?.length) return false;
    return options.images.length > 0 && selectedCount === options.images.length;
  }, [options.images, selectedCount]);
  
  // 是否有选中的图片
  const hasSelected = useMemo(() => {
    return selectedCount > 0;
  }, [selectedCount]);
  
  // 判断图片是否被选中
  const isImageSelected = useCallback((imgUrl: string) => {
    return selectedImages.includes(imgUrl);
  }, [selectedImages]);
  
  // 判断是否应该显示图片选择功能
  const shouldShowImageSelection = useCallback((item?: T) => {
    if (options.shouldShowSelector) {
      return options.shouldShowSelector(item);
    }
    // 默认行为：如果有图片列表则显示
    return (options.images?.length ?? 0) > 0;
  }, [options]);

  // 处理选择单个图片
  const handleSelectImage = useCallback((imgUrl: string) => {
    setSelectedImages(prev => {
      // 如果已经选中，则移除
      if (prev.includes(imgUrl)) {
        return prev.filter(url => url !== imgUrl);
      } 
      // 如果未选中，则添加
      return [...prev, imgUrl];
    });
  }, []);

  // 处理全选/取消全选
  const handleSelectAll = useCallback((checked: boolean) => {
    const currentImages = options.images || [];
    if (!currentImages.length) return;

    if (checked) {
      // 全选
      setSelectedImages([...currentImages]);
    } else {
      // 取消全选
      setSelectedImages([]);
    }
  }, [options]);
  
  // 重置选择状态
  const resetSelection = useCallback(() => {
    setSelectedImages([]);
  }, []);

  return {
    setImageSelectorOptions: setOptions,
    selectedImages,
    selectedCount,
    indeterminate,
    isAllSelected,
    hasSelected,
    isImageSelected,
    handleSelectImage,
    handleSelectAll,
    resetSelection,
    shouldShowImageSelection,
  };
}

// 导出默认组件
export default useImageSelector;
