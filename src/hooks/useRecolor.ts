import { useCallback, useEffect, useRef, useState } from 'react';
import { processColorTransfer } from '../utils/colorTransfer';

interface UseRecolorResult {
  processImage: (
    originalImage: HTMLImageElement,
    maskImage: HTMLImageElement,
    targetColor: string,
    smartColorAdjust: boolean,
  ) => void;
  processedCanvas: HTMLCanvasElement | null;
  isProcessing: boolean;
  error: string | null;
}

/**
 * React hook for image color transfer functionality
 * This hook provides an interface to the color transfer utility in colorTransfer.ts
 * with optimizations for performance including debouncing and caching
 */
export function useRecolor(): UseRecolorResult {
  const [processedCanvas, setProcessedCanvas] =
    useState<HTMLCanvasElement | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 使用 ref 存储当前处理的请求 ID，用于取消旧的请求
  const currentRequestIdRef = useRef<number>(0);

  // 使用 ref 存储防抖定时器
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 清理函数，在组件卸载时调用
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  const processImageInternal = useCallback(
    async (
      originalImage: HTMLImageElement,
      maskImage: HTMLImageElement,
      targetColor: string,
      smartColorAdjust: boolean,
      requestId: number,
    ) => {
      try {
        // 检查请求是否已被取消
        if (requestId !== currentRequestIdRef.current) {
          return;
        }

        // 检查图像是否已加载
        if (!originalImage.complete || !maskImage.complete) {
          throw new Error('Images are not fully loaded');
        }

        console.log('Processing with smartColorAdjust:', smartColorAdjust);
        console.log('Original image:', originalImage.width, 'x', originalImage.height);
        console.log('Mask image:', maskImage.width, 'x', maskImage.height);

        // 处理图像
        const resultCanvas = await processColorTransfer(
          originalImage,
          maskImage,
          targetColor,
          { texturePreserve: 0.7, smartColorAdjust }
        );

        // 检查请求是否已被取消
        if (requestId !== currentRequestIdRef.current) {
          return;
        }

        setProcessedCanvas(resultCanvas);
      } catch (err) {
        // 检查请求是否已被取消
        if (requestId !== currentRequestIdRef.current) {
          return;
        }

        const errorMessage =
          err instanceof Error
            ? err.message
            : 'An unknown error occurred during image processing';
        console.error(`Image processing error: ${errorMessage}`);
        setError(errorMessage);
      } finally {
        // 检查请求是否已被取消
        if (requestId !== currentRequestIdRef.current) {
          return;
        }

        setIsProcessing(false);
      }
    },
    [],
  );

  const processImage = useCallback(
    (
      originalImage: HTMLImageElement,
      maskImage: HTMLImageElement,
      targetColor: string,
      smartColorAdjust: boolean,
    ) => {
      // 取消之前的防抖定时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      // 增加请求 ID
      const requestId = currentRequestIdRef.current + 1;
      currentRequestIdRef.current = requestId;

      // 设置处理状态
      setIsProcessing(true);
      setError(null);
      
      console.log('Processing request with:', {
        originalImage: originalImage ? 'loaded' : 'missing',
        maskImage: maskImage ? 'loaded' : 'missing',
        targetColor,
        smartColorAdjust
      });

      // 使用防抖，延迟处理
      debounceTimerRef.current = setTimeout(() => {
        processImageInternal(
          originalImage,
          maskImage,
          targetColor,
          smartColorAdjust,
          requestId,
        );
      }, 100); // 100ms 防抖延迟
    },
    [processImageInternal],
  );

  return {
    processImage,
    processedCanvas,
    isProcessing,
    error,
  };
}

export type { UseRecolorResult };
