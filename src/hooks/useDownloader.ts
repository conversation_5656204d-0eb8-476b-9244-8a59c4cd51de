import { batchDownload } from '@/services/FileController';
import { useState, useCallback } from 'react';

/**
 * 简单的文件下载工具函数
 * @param url 要下载的URL
 * @param fileName
 */
const download = (url: string, fileName: string | null = null) => {
  const link = document.createElement('a');
  link.href = url;
  // 文件名称
  link.download = fileName ? fileName : url.substr(url.lastIndexOf('/') + 1);
  document.body.appendChild(link);
  link.click();  // 模拟点击
  document.body.removeChild(link);  // 下载后从文档中移除
};

/**
 * 下载器 Hook 参数
 */
interface DownloaderOptions {
  // 初始的下载记录，格式为 {[url]: boolean}
  initialDownloaded?: Record<string, boolean>;
  // 下载前的回调
  onBeforeDownload?: (url: string) => boolean | Promise<boolean>;
  // 下载后的回调
  onAfterDownload?: (url: string, success: boolean) => void;
  // 是否显示下载成功提示
  showSuccessMessage?: boolean;
}

/**
 * 下载器 Hook 返回值
 */
interface DownloaderResult {
  // 批量下载
  simpleDownloadUrls: (urls: string[], fileName: string | undefined) => Promise<void>;
  // 已下载的URL记录
  downloadedUrls: Record<string, boolean>;
  // 判断 URL 是否已被下载
  isDownloaded: (url: string) => boolean;
  // 下载单个 URL
  downloadUrl: (url: string) => Promise<void>;
  // 下载多个 URL
  downloadUrls: (urls: string[]) => Promise<void>;
  // 清除下载记录
  clearDownloaded: () => void;
  // 设置 URL 为已下载状态
  markAsDownloaded: (url: string) => void;
  // 当前是否有正在进行的下载
  downloading: boolean;
  // 设置下载状态
  setDownloading: React.Dispatch<React.SetStateAction<boolean>>;
}

/**
 * 下载器 Hook
 * @param options 配置选项
 * @returns 下载相关的状态和方法
 */
export function useDownloader(options: DownloaderOptions = {}): DownloaderResult {
  const {
    initialDownloaded = {},
    onBeforeDownload,
    onAfterDownload,
    showSuccessMessage = true,
  } = options;

  // 已下载的 URL 记录
  const [downloadedUrls, setDownloadedUrls] = useState<Record<string, boolean>>(initialDownloaded);
  // 当前下载中的状态
  const [downloading, setDownloading] = useState(false);

  // 批量下载
  const simpleDownloadUrls = useCallback((urls: string[], fileName: string | undefined) => {
    setDownloading(true);
    return batchDownload(urls, fileName).then((res) => {
      if (res) {
        download(res);
      }
    }).finally(() => {
      setDownloading(false);
    });
  }, []);

  // 判断 URL 是否已被下载
  const isDownloaded = useCallback((url: string) => {
    return downloadedUrls[url];
  }, [downloadedUrls]);

  // 清除下载记录
  const clearDownloaded = useCallback(() => {
    setDownloadedUrls({});
  }, []);

  // 标记 URL 为已下载
  const markAsDownloaded = useCallback((url: string) => {
    setDownloadedUrls(prev => ({ ...prev, [url]: true }));
  }, []);

  // 下载单个 URL
  const downloadUrl = useCallback(async (url: string) => {
    // 如果有下载前回调，先执行并检查结果
    if (onBeforeDownload) {
      const canProceed = await onBeforeDownload(url);
      if (!canProceed) return;
    }

    // not implemented yet
  }, [onBeforeDownload, onAfterDownload, showSuccessMessage]);

  // 下载多个 URL
  const downloadUrls = useCallback(async (urls: string[]) => {
    if (!urls.length) return;
    // not implemented yet
  }, [downloadedUrls, onBeforeDownload, onAfterDownload, showSuccessMessage]);

  return {
    simpleDownloadUrls,
    downloadedUrls,
    isDownloaded,
    downloadUrl,
    downloadUrls,
    clearDownloaded,
    markAsDownloaded,
    downloading,
    setDownloading,
  };
}
