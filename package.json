{"private": true, "author": "半泉 <<EMAIL>>", "scripts": {"build": "max build", "build:prod": "cross-env UMI_ENV=prod max build", "dev": "max dev", "format": "prettier --cache --write .", "postinstall": "max setup", "prepare": "husky", "setup": "max setup", "start": "npm run dev", "test": "cross-env TS_NODE_TRANSPILE_ONLY=yes jest --passWithNoTests"}, "dependencies": {"@ant-design/charts": "^2.2.6", "@ant-design/icons": "^5.0.1", "@ant-design/plots": "^2.3.3", "@ant-design/pro-components": "^2.4.4", "@ant-design/pro-form": "^2.25.1", "@ant-design/pro-layout": "^7.19.0", "@techstark/opencv-js": "4.11.0-release.1", "@umijs/max": "^4.1.10", "antd": "^5.4.0", "compressorjs": "^1.2.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.12", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "file-saver": "^2.0.5", "heic-convert": "^2.1.0", "jszip": "^3.10.1", "konva": "^9.3.12", "ldrs": "^1.0.2", "lodash": "^4.17.21", "lottie-react": "^2.4.0", "lottie-web": "^5.12.2", "moment": "^2.30.1", "qrcode": "^1.5.4", "qrcode.react": "^3.1.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-device-detect": "^2.2.3", "react-json-view": "^1.21.3", "react-konva": "^18.2.10", "react-resizable": "^3.0.5", "react-router-dom": "^6.23.0", "use-image": "^1.1.1"}, "devDependencies": {"@testing-library/jest-dom": "^5", "@testing-library/react": "^14", "@types/jest": "^29", "@types/react": "^18.3.12", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.0.11", "@types/react-lazyload": "^3.2.3", "@types/react-resizable": "^3.0.8", "@types/testing-library__jest-dom": "^5.14.5", "cross-env": "^7", "husky": "^9", "jest": "^29", "jest-environment-jsdom": "^29", "lint-staged": "^13.2.0", "prettier": "link:@umijs/utils/compiled/prettier", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "react-lazyload": "^3.2.1", "ts-node": "^10", "typescript": "^5.6.2"}}